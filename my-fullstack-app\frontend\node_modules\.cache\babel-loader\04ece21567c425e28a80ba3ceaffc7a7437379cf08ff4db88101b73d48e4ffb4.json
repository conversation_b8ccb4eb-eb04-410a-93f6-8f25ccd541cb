{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\demo-git\\\\demo-react\\\\my-fullstack-app\\\\frontend\\\\src\\\\components\\\\Page\\\\PermissionPage.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useCallback } from 'react';\nimport { Dropdown } from 'primereact/dropdown';\nimport { Checkbox } from 'primereact/checkbox';\nimport { Button } from 'primereact/button';\nimport { Toast } from 'primereact/toast';\nimport { Card } from 'primereact/card';\nimport { classNames } from 'primereact/utils';\nimport api from '../../services/api';\nimport { useToast } from '../../hooks/useToast';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst PermissionPage = () => {\n  _s();\n  const [roles, setRoles] = useState([]);\n  const [selectedRole, setSelectedRole] = useState(null);\n  const [menuPermissions, setMenuPermissions] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const {\n    toast,\n    showToast\n  } = useToast();\n  const fetchRoles = useCallback(async () => {\n    try {\n      const response = await api.get('/api/RoleMenu/GetRoles');\n      setRoles(response.data);\n    } catch (error) {\n      showToast('error', '獲取角色列表失敗', error.message);\n    }\n  }, [showToast]);\n  useEffect(() => {\n    fetchRoles();\n  }, [fetchRoles]);\n  const fetchRolePermissions = useCallback(async roleId => {\n    if (!roleId) return;\n    setLoading(true);\n    try {\n      const response = await api.get(`/api/RoleMenu/GetRoleMenus/${roleId}`);\n      setMenuPermissions(response.data.menuGroups);\n    } catch (error) {\n      showToast('error', '獲取權限失敗', error.message);\n    } finally {\n      setLoading(false);\n    }\n  }, [showToast]);\n  useEffect(() => {\n    if (selectedRole) {\n      fetchRolePermissions(selectedRole);\n    } else {\n      setMenuPermissions([]);\n    }\n  }, [selectedRole, fetchRolePermissions]);\n  const handlePermissionChange = (menuId, permission, isChecked) => {\n    setMenuPermissions(prev => prev.map(group => ({\n      ...group,\n      menus: group.menus.map(menu => {\n        if (menu.menuId === menuId) {\n          const newPermissions = {\n            ...menu.permissions,\n            [permission]: isChecked\n          };\n          const isAssigned = Object.values(newPermissions).some(p => p === true);\n          return {\n            ...menu,\n            permissions: newPermissions,\n            isAssigned\n          };\n        }\n        return menu;\n      })\n    })));\n  };\n  const handleSaveChanges = async () => {\n    if (!selectedRole) return;\n    setLoading(true);\n    const payload = {\n      roleId: selectedRole,\n      menuPermissions: menuPermissions.flatMap(group => group.menus.map(menu => ({\n        menuId: menu.menuId,\n        canRead: menu.permissions.canRead,\n        canWrite: menu.permissions.canWrite,\n        canDelete: menu.permissions.canDelete,\n        canExport: menu.permissions.canExport,\n        canImport: menu.permissions.canImport\n      })))\n    };\n    try {\n      await api.post('/api/RoleMenu/UpdateRoleMenus', payload);\n      showToast('success', '權限更新成功');\n      fetchRolePermissions(selectedRole);\n    } catch (error) {\n      showToast('error', '權限更新失敗', error.message);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const roleOptionTemplate = option => /*#__PURE__*/_jsxDEV(\"div\", {\n    children: option.name\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 99,\n    columnNumber: 9\n  }, this);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"p-grid p-fluid\",\n    children: [/*#__PURE__*/_jsxDEV(Toast, {\n      ref: toast\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 104,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"p-col-12\",\n      children: /*#__PURE__*/_jsxDEV(Card, {\n        title: \"\\u6B0A\\u9650\\u7BA1\\u7406\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-field p-grid\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            htmlFor: \"role\",\n            className: \"p-col-12 p-md-2\",\n            children: \"\\u9078\\u64C7\\u89D2\\u8272\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 108,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"p-col-12 p-md-10\",\n            children: /*#__PURE__*/_jsxDEV(Dropdown, {\n              id: \"role\",\n              value: selectedRole,\n              options: roles,\n              onChange: e => setSelectedRole(e.value),\n              optionLabel: \"name\",\n              optionValue: \"id\",\n              placeholder: \"\\u8ACB\\u9078\\u64C7\\u4E00\\u500B\\u89D2\\u8272\",\n              itemTemplate: roleOptionTemplate,\n              style: {\n                width: '100%'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 110,\n              columnNumber: 29\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 109,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 107,\n          columnNumber: 21\n        }, this), menuPermissions.map(group => /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-field\",\n          children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n            children: group.groupName\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 126,\n            columnNumber: 29\n          }, this), group.menus.map(menu => /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"p-grid p-align-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"p-col-12 p-md-3\",\n              children: /*#__PURE__*/_jsxDEV(\"strong\", {\n                children: menu.name\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 130,\n                columnNumber: 41\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 129,\n              columnNumber: 37\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"p-col-12 p-md-9 p-grid\",\n              children: menu.permissions && Object.keys(menu.permissions).map(permission => /*#__PURE__*/_jsxDEV(\"div\", {\n                className: classNames('p-col-6 p-md-2', {\n                  'p-d-none': !menu.isAssigned\n                }),\n                children: [/*#__PURE__*/_jsxDEV(Checkbox, {\n                  inputId: `${menu.menuId}-${permission}`,\n                  checked: menu.permissions[permission],\n                  onChange: e => handlePermissionChange(menu.menuId, permission, e.checked)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 135,\n                  columnNumber: 49\n                }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                  htmlFor: `${menu.menuId}-${permission}`,\n                  className: \"p-ml-2\",\n                  children: permission\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 140,\n                  columnNumber: 49\n                }, this)]\n              }, permission, true, {\n                fileName: _jsxFileName,\n                lineNumber: 134,\n                columnNumber: 45\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 132,\n              columnNumber: 37\n            }, this)]\n          }, menu.menuId, true, {\n            fileName: _jsxFileName,\n            lineNumber: 128,\n            columnNumber: 33\n          }, this))]\n        }, group.groupId, true, {\n          fileName: _jsxFileName,\n          lineNumber: 125,\n          columnNumber: 25\n        }, this)), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-d-flex p-jc-end\",\n          children: /*#__PURE__*/_jsxDEV(Button, {\n            label: \"\\u5132\\u5B58\\u8B8A\\u66F4\",\n            icon: \"pi pi-check\",\n            onClick: handleSaveChanges,\n            disabled: !selectedRole || loading\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 150,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 149,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 106,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 105,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 103,\n    columnNumber: 9\n  }, this);\n};\n_s(PermissionPage, \"x7O7F48LqzxyyqIOEKt+1LU56eY=\", false, function () {\n  return [useToast];\n});\n_c = PermissionPage;\nexport default PermissionPage;\nvar _c;\n$RefreshReg$(_c, \"PermissionPage\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useCallback", "Dropdown", "Checkbox", "<PERSON><PERSON>", "Toast", "Card", "classNames", "api", "useToast", "jsxDEV", "_jsxDEV", "PermissionPage", "_s", "roles", "setRoles", "selectedR<PERSON>", "setSelectedRole", "menuPermissions", "setMenuPermissions", "loading", "setLoading", "toast", "showToast", "fetchRoles", "response", "get", "data", "error", "message", "fetchRolePermissions", "roleId", "menuGroups", "handlePermissionChange", "menuId", "permission", "isChecked", "prev", "map", "group", "menus", "menu", "newPermissions", "permissions", "isAssigned", "Object", "values", "some", "p", "handleSaveChanges", "payload", "flatMap", "canRead", "canWrite", "canDelete", "canExport", "canImport", "post", "roleOptionTemplate", "option", "children", "name", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "className", "ref", "title", "htmlFor", "id", "value", "options", "onChange", "e", "optionLabel", "optionValue", "placeholder", "itemTemplate", "style", "width", "groupName", "keys", "inputId", "checked", "groupId", "label", "icon", "onClick", "disabled", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/demo-git/demo-react/my-fullstack-app/frontend/src/components/Page/PermissionPage.tsx"], "sourcesContent": ["\nimport React, { useState, useEffect, useCallback } from 'react';\nimport { Dropdown } from 'primereact/dropdown';\nimport { Checkbox } from 'primereact/checkbox';\nimport { Button } from 'primereact/button';\nimport { Toast } from 'primereact/toast';\nimport { Card } from 'primereact/card';\nimport { classNames } from 'primereact/utils';\nimport api from '../../services/api';\nimport { useToast } from '../../hooks/useToast';\n\nconst PermissionPage = () => {\n    const [roles, setRoles] = useState([]);\n    const [selectedRole, setSelectedRole] = useState(null);\n    const [menuPermissions, setMenuPermissions] = useState([]);\n    const [loading, setLoading] = useState(false);\n    const { toast, showToast } = useToast();\n\n    const fetchRoles = useCallback(async () => {\n        try {\n            const response = await api.get('/api/RoleMenu/GetRoles');\n            setRoles(response.data);\n        } catch (error) {\n            showToast('error', '獲取角色列表失敗', error.message);\n        }\n    }, [showToast]);\n\n    useEffect(() => {\n        fetchRoles();\n    }, [fetchRoles]);\n\n    const fetchRolePermissions = useCallback(async (roleId) => {\n        if (!roleId) return;\n        setLoading(true);\n        try {\n            const response = await api.get(`/api/RoleMenu/GetRoleMenus/${roleId}`);\n            setMenuPermissions(response.data.menuGroups);\n        } catch (error) {\n            showToast('error', '獲取權限失敗', error.message);\n        } finally {\n            setLoading(false);\n        }\n    }, [showToast]);\n\n    useEffect(() => {\n        if (selectedRole) {\n            fetchRolePermissions(selectedRole);\n        } else {\n            setMenuPermissions([]);\n        }\n    }, [selectedRole, fetchRolePermissions]);\n\n    const handlePermissionChange = (menuId, permission, isChecked) => {\n        setMenuPermissions(prev => \n            prev.map(group => ({\n                ...group,\n                menus: group.menus.map(menu => {\n                    if (menu.menuId === menuId) {\n                        const newPermissions = { ...menu.permissions, [permission]: isChecked };\n                        const isAssigned = Object.values(newPermissions).some(p => p === true);\n                        return { ...menu, permissions: newPermissions, isAssigned };\n                    }\n                    return menu;\n                })\n            }))\n        );\n    };\n\n    const handleSaveChanges = async () => {\n        if (!selectedRole) return;\n        setLoading(true);\n\n        const payload = {\n            roleId: selectedRole,\n            menuPermissions: menuPermissions.flatMap(group => \n                group.menus.map(menu => ({\n                    menuId: menu.menuId,\n                    canRead: menu.permissions.canRead,\n                    canWrite: menu.permissions.canWrite,\n                    canDelete: menu.permissions.canDelete,\n                    canExport: menu.permissions.canExport,\n                    canImport: menu.permissions.canImport,\n                }))\n            )\n        };\n\n        try {\n            await api.post('/api/RoleMenu/UpdateRoleMenus', payload);\n            showToast('success', '權限更新成功');\n            fetchRolePermissions(selectedRole);\n        } catch (error) {\n            showToast('error', '權限更新失敗', error.message);\n        } finally {\n            setLoading(false);\n        }\n    };\n\n    const roleOptionTemplate = (option) => (\n        <div>{option.name}</div>\n    );\n\n    return (\n        <div className=\"p-grid p-fluid\">\n            <Toast ref={toast} />\n            <div className=\"p-col-12\">\n                <Card title=\"權限管理\">\n                    <div className=\"p-field p-grid\">\n                        <label htmlFor=\"role\" className=\"p-col-12 p-md-2\">選擇角色</label>\n                        <div className=\"p-col-12 p-md-10\">\n                            <Dropdown \n                                id=\"role\" \n                                value={selectedRole} \n                                options={roles} \n                                onChange={(e) => setSelectedRole(e.value)} \n                                optionLabel=\"name\" \n                                optionValue=\"id\" \n                                placeholder=\"請選擇一個角色\" \n                                itemTemplate={roleOptionTemplate}\n                                style={{ width: '100%' }} \n                            />\n                        </div>\n                    </div>\n\n                    {menuPermissions.map(group => (\n                        <div key={group.groupId} className=\"p-field\">\n                            <h5>{group.groupName}</h5>\n                            {group.menus.map(menu => (\n                                <div key={menu.menuId} className=\"p-grid p-align-center\">\n                                    <div className=\"p-col-12 p-md-3\">\n                                        <strong>{menu.name}</strong>\n                                    </div>\n                                    <div className=\"p-col-12 p-md-9 p-grid\">\n                                        {menu.permissions && Object.keys(menu.permissions).map(permission => (\n                                            <div key={permission} className={classNames('p-col-6 p-md-2', { 'p-d-none': !menu.isAssigned })}>\n                                                <Checkbox \n                                                    inputId={`${menu.menuId}-${permission}`} \n                                                    checked={menu.permissions[permission]} \n                                                    onChange={e => handlePermissionChange(menu.menuId, permission, e.checked)}\n                                                />\n                                                <label htmlFor={`${menu.menuId}-${permission}`} className=\"p-ml-2\">{permission}</label>\n                                            </div>\n                                        ))}\n                                    </div>\n                                </div>\n                            ))}\n                        </div>\n                    ))}\n\n                    <div className=\"p-d-flex p-jc-end\">\n                        <Button \n                            label=\"儲存變更\" \n                            icon=\"pi pi-check\" \n                            onClick={handleSaveChanges} \n                            disabled={!selectedRole || loading} \n                        />\n                    </div>\n                </Card>\n            </div>\n        </div>\n    );\n};\n\nexport default PermissionPage;\n"], "mappings": ";;AACA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,WAAW,QAAQ,OAAO;AAC/D,SAASC,QAAQ,QAAQ,qBAAqB;AAC9C,SAASC,QAAQ,QAAQ,qBAAqB;AAC9C,SAASC,MAAM,QAAQ,mBAAmB;AAC1C,SAASC,KAAK,QAAQ,kBAAkB;AACxC,SAASC,IAAI,QAAQ,iBAAiB;AACtC,SAASC,UAAU,QAAQ,kBAAkB;AAC7C,OAAOC,GAAG,MAAM,oBAAoB;AACpC,SAASC,QAAQ,QAAQ,sBAAsB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEhD,MAAMC,cAAc,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACzB,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAGhB,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACiB,YAAY,EAAEC,eAAe,CAAC,GAAGlB,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAACmB,eAAe,EAAEC,kBAAkB,CAAC,GAAGpB,QAAQ,CAAC,EAAE,CAAC;EAC1D,MAAM,CAACqB,OAAO,EAAEC,UAAU,CAAC,GAAGtB,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM;IAAEuB,KAAK;IAAEC;EAAU,CAAC,GAAGd,QAAQ,CAAC,CAAC;EAEvC,MAAMe,UAAU,GAAGvB,WAAW,CAAC,YAAY;IACvC,IAAI;MACA,MAAMwB,QAAQ,GAAG,MAAMjB,GAAG,CAACkB,GAAG,CAAC,wBAAwB,CAAC;MACxDX,QAAQ,CAACU,QAAQ,CAACE,IAAI,CAAC;IAC3B,CAAC,CAAC,OAAOC,KAAK,EAAE;MACZL,SAAS,CAAC,OAAO,EAAE,UAAU,EAAEK,KAAK,CAACC,OAAO,CAAC;IACjD;EACJ,CAAC,EAAE,CAACN,SAAS,CAAC,CAAC;EAEfvB,SAAS,CAAC,MAAM;IACZwB,UAAU,CAAC,CAAC;EAChB,CAAC,EAAE,CAACA,UAAU,CAAC,CAAC;EAEhB,MAAMM,oBAAoB,GAAG7B,WAAW,CAAC,MAAO8B,MAAM,IAAK;IACvD,IAAI,CAACA,MAAM,EAAE;IACbV,UAAU,CAAC,IAAI,CAAC;IAChB,IAAI;MACA,MAAMI,QAAQ,GAAG,MAAMjB,GAAG,CAACkB,GAAG,CAAC,8BAA8BK,MAAM,EAAE,CAAC;MACtEZ,kBAAkB,CAACM,QAAQ,CAACE,IAAI,CAACK,UAAU,CAAC;IAChD,CAAC,CAAC,OAAOJ,KAAK,EAAE;MACZL,SAAS,CAAC,OAAO,EAAE,QAAQ,EAAEK,KAAK,CAACC,OAAO,CAAC;IAC/C,CAAC,SAAS;MACNR,UAAU,CAAC,KAAK,CAAC;IACrB;EACJ,CAAC,EAAE,CAACE,SAAS,CAAC,CAAC;EAEfvB,SAAS,CAAC,MAAM;IACZ,IAAIgB,YAAY,EAAE;MACdc,oBAAoB,CAACd,YAAY,CAAC;IACtC,CAAC,MAAM;MACHG,kBAAkB,CAAC,EAAE,CAAC;IAC1B;EACJ,CAAC,EAAE,CAACH,YAAY,EAAEc,oBAAoB,CAAC,CAAC;EAExC,MAAMG,sBAAsB,GAAGA,CAACC,MAAM,EAAEC,UAAU,EAAEC,SAAS,KAAK;IAC9DjB,kBAAkB,CAACkB,IAAI,IACnBA,IAAI,CAACC,GAAG,CAACC,KAAK,KAAK;MACf,GAAGA,KAAK;MACRC,KAAK,EAAED,KAAK,CAACC,KAAK,CAACF,GAAG,CAACG,IAAI,IAAI;QAC3B,IAAIA,IAAI,CAACP,MAAM,KAAKA,MAAM,EAAE;UACxB,MAAMQ,cAAc,GAAG;YAAE,GAAGD,IAAI,CAACE,WAAW;YAAE,CAACR,UAAU,GAAGC;UAAU,CAAC;UACvE,MAAMQ,UAAU,GAAGC,MAAM,CAACC,MAAM,CAACJ,cAAc,CAAC,CAACK,IAAI,CAACC,CAAC,IAAIA,CAAC,KAAK,IAAI,CAAC;UACtE,OAAO;YAAE,GAAGP,IAAI;YAAEE,WAAW,EAAED,cAAc;YAAEE;UAAW,CAAC;QAC/D;QACA,OAAOH,IAAI;MACf,CAAC;IACL,CAAC,CAAC,CACN,CAAC;EACL,CAAC;EAED,MAAMQ,iBAAiB,GAAG,MAAAA,CAAA,KAAY;IAClC,IAAI,CAACjC,YAAY,EAAE;IACnBK,UAAU,CAAC,IAAI,CAAC;IAEhB,MAAM6B,OAAO,GAAG;MACZnB,MAAM,EAAEf,YAAY;MACpBE,eAAe,EAAEA,eAAe,CAACiC,OAAO,CAACZ,KAAK,IAC1CA,KAAK,CAACC,KAAK,CAACF,GAAG,CAACG,IAAI,KAAK;QACrBP,MAAM,EAAEO,IAAI,CAACP,MAAM;QACnBkB,OAAO,EAAEX,IAAI,CAACE,WAAW,CAACS,OAAO;QACjCC,QAAQ,EAAEZ,IAAI,CAACE,WAAW,CAACU,QAAQ;QACnCC,SAAS,EAAEb,IAAI,CAACE,WAAW,CAACW,SAAS;QACrCC,SAAS,EAAEd,IAAI,CAACE,WAAW,CAACY,SAAS;QACrCC,SAAS,EAAEf,IAAI,CAACE,WAAW,CAACa;MAChC,CAAC,CAAC,CACN;IACJ,CAAC;IAED,IAAI;MACA,MAAMhD,GAAG,CAACiD,IAAI,CAAC,+BAA+B,EAAEP,OAAO,CAAC;MACxD3B,SAAS,CAAC,SAAS,EAAE,QAAQ,CAAC;MAC9BO,oBAAoB,CAACd,YAAY,CAAC;IACtC,CAAC,CAAC,OAAOY,KAAK,EAAE;MACZL,SAAS,CAAC,OAAO,EAAE,QAAQ,EAAEK,KAAK,CAACC,OAAO,CAAC;IAC/C,CAAC,SAAS;MACNR,UAAU,CAAC,KAAK,CAAC;IACrB;EACJ,CAAC;EAED,MAAMqC,kBAAkB,GAAIC,MAAM,iBAC9BhD,OAAA;IAAAiD,QAAA,EAAMD,MAAM,CAACE;EAAI;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAM,CAC1B;EAED,oBACItD,OAAA;IAAKuD,SAAS,EAAC,gBAAgB;IAAAN,QAAA,gBAC3BjD,OAAA,CAACN,KAAK;MAAC8D,GAAG,EAAE7C;IAAM;MAAAwC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACrBtD,OAAA;MAAKuD,SAAS,EAAC,UAAU;MAAAN,QAAA,eACrBjD,OAAA,CAACL,IAAI;QAAC8D,KAAK,EAAC,0BAAM;QAAAR,QAAA,gBACdjD,OAAA;UAAKuD,SAAS,EAAC,gBAAgB;UAAAN,QAAA,gBAC3BjD,OAAA;YAAO0D,OAAO,EAAC,MAAM;YAACH,SAAS,EAAC,iBAAiB;YAAAN,QAAA,EAAC;UAAI;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAC9DtD,OAAA;YAAKuD,SAAS,EAAC,kBAAkB;YAAAN,QAAA,eAC7BjD,OAAA,CAACT,QAAQ;cACLoE,EAAE,EAAC,MAAM;cACTC,KAAK,EAAEvD,YAAa;cACpBwD,OAAO,EAAE1D,KAAM;cACf2D,QAAQ,EAAGC,CAAC,IAAKzD,eAAe,CAACyD,CAAC,CAACH,KAAK,CAAE;cAC1CI,WAAW,EAAC,MAAM;cAClBC,WAAW,EAAC,IAAI;cAChBC,WAAW,EAAC,4CAAS;cACrBC,YAAY,EAAEpB,kBAAmB;cACjCqB,KAAK,EAAE;gBAAEC,KAAK,EAAE;cAAO;YAAE;cAAAlB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5B;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,EAEL/C,eAAe,CAACoB,GAAG,CAACC,KAAK,iBACtB5B,OAAA;UAAyBuD,SAAS,EAAC,SAAS;UAAAN,QAAA,gBACxCjD,OAAA;YAAAiD,QAAA,EAAKrB,KAAK,CAAC0C;UAAS;YAAAnB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,EACzB1B,KAAK,CAACC,KAAK,CAACF,GAAG,CAACG,IAAI,iBACjB9B,OAAA;YAAuBuD,SAAS,EAAC,uBAAuB;YAAAN,QAAA,gBACpDjD,OAAA;cAAKuD,SAAS,EAAC,iBAAiB;cAAAN,QAAA,eAC5BjD,OAAA;gBAAAiD,QAAA,EAASnB,IAAI,CAACoB;cAAI;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAS;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3B,CAAC,eACNtD,OAAA;cAAKuD,SAAS,EAAC,wBAAwB;cAAAN,QAAA,EAClCnB,IAAI,CAACE,WAAW,IAAIE,MAAM,CAACqC,IAAI,CAACzC,IAAI,CAACE,WAAW,CAAC,CAACL,GAAG,CAACH,UAAU,iBAC7DxB,OAAA;gBAAsBuD,SAAS,EAAE3D,UAAU,CAAC,gBAAgB,EAAE;kBAAE,UAAU,EAAE,CAACkC,IAAI,CAACG;gBAAW,CAAC,CAAE;gBAAAgB,QAAA,gBAC5FjD,OAAA,CAACR,QAAQ;kBACLgF,OAAO,EAAE,GAAG1C,IAAI,CAACP,MAAM,IAAIC,UAAU,EAAG;kBACxCiD,OAAO,EAAE3C,IAAI,CAACE,WAAW,CAACR,UAAU,CAAE;kBACtCsC,QAAQ,EAAEC,CAAC,IAAIzC,sBAAsB,CAACQ,IAAI,CAACP,MAAM,EAAEC,UAAU,EAAEuC,CAAC,CAACU,OAAO;gBAAE;kBAAAtB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC7E,CAAC,eACFtD,OAAA;kBAAO0D,OAAO,EAAE,GAAG5B,IAAI,CAACP,MAAM,IAAIC,UAAU,EAAG;kBAAC+B,SAAS,EAAC,QAAQ;kBAAAN,QAAA,EAAEzB;gBAAU;kBAAA2B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA,GANjF9B,UAAU;gBAAA2B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAOf,CACR;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC;UAAA,GAfAxB,IAAI,CAACP,MAAM;YAAA4B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAgBhB,CACR,CAAC;QAAA,GApBI1B,KAAK,CAAC8C,OAAO;UAAAvB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAqBlB,CACR,CAAC,eAEFtD,OAAA;UAAKuD,SAAS,EAAC,mBAAmB;UAAAN,QAAA,eAC9BjD,OAAA,CAACP,MAAM;YACHkF,KAAK,EAAC,0BAAM;YACZC,IAAI,EAAC,aAAa;YAClBC,OAAO,EAAEvC,iBAAkB;YAC3BwC,QAAQ,EAAE,CAACzE,YAAY,IAAII;UAAQ;YAAA0C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEd,CAAC;AAACpD,EAAA,CArJID,cAAc;EAAA,QAKaH,QAAQ;AAAA;AAAAiF,EAAA,GALnC9E,cAAc;AAuJpB,eAAeA,cAAc;AAAC,IAAA8E,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
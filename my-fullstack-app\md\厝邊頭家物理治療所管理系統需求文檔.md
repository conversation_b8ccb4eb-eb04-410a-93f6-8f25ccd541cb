# 厝邊頭家物理治療所管理系統需求文檔

## 專案概述

### 專案名稱
厝邊頭家物理治療所管理系統 (Physical Therapy Management System)

### 專案描述
本系統是一個專為物理治療所設計的全面性管理平台，旨在協助治療所進行病患管理、治療師排班、診療記錄管理、收據管理等核心業務功能。系統採用現代化的 Web 技術架構，提供直觀的用戶界面和完整的業務流程支援。

### 技術架構
- **前端**: React 19.1.0 + TypeScript + PrimeReact UI 框架
- **後端**: ASP.NET Core + Entity Framework Core + MySQL
- **快取**: Redis 7.2
- **即時通訊**: SignalR
- **容器化**: Docker + Docker Compose
- **部署**: 雲端伺服器 (**************)
- **備份**: 自動化 MySQL 備份 + Dropbox 雲端同步

## 系統功能需求

### 1. 用戶認證與權限管理

#### 1.1 用戶登入系統
**功能描述**: 提供安全的用戶登入機制，支援 JWT Token 認證

**功能需求**:
- 用戶名密碼驗證
- JWT Token 產生與驗證 (1小時有效期)
- 預設密碼檢測與強制更新
- 帳號狀態檢查 (啟用/停用)
- 自動登出機制

**驗收標準**:
- ✅ 用戶輸入正確帳密後可成功登入
- ✅ 系統產生有效的 JWT Token
- ✅ 預設密碼用戶強制導向密碼更新頁面
- ✅ 停用帳號無法登入
- ✅ Token 過期自動導向登入頁面

#### 1.2 角色權限管理
**功能描述**: 多層級角色權限控制系統

**角色定義**:
- **Admin**: 系統管理員 - 完整系統權限
- **Manager**: 管理者 - 業務管理權限
- **User**: 一般用戶 - 基本操作權限
- **Option**: 選項管理員 - 資料類型管理權限
- **Guest**: 訪客 - 唯讀權限

**功能需求**:
- 用戶角色分配與管理
- 基於角色的功能存取控制
- 用戶帳號啟用/停用管理
- 密碼更新功能

### 2. 病患資料管理

#### 2.1 病患基本資料
**功能描述**: 完整的病患資訊管理系統

**資料欄位**:
- 基本資訊: 姓名、性別、電話、地址、生日、身分證字號、Email
- 緊急聯絡人: 姓名、關係、電話
- 醫療資訊: 病史、運動習慣、運動頻率、受傷史

**功能需求**:
- 新增/編輯/查詢病患資料
- 軟刪除機制 (IsDelete 標記)
- 資料搜尋與篩選
- 操作者記錄追蹤
- 資料完整性驗證

**驗收標準**:
- ✅ 可新增完整病患資料
- ✅ 支援資料修改與歷史追蹤
- ✅ 提供多條件搜尋功能
- ✅ 軟刪除保留歷史資料
- ✅ 必填欄位驗證

#### 2.2 病患關聯管理
**功能描述**: 管理病患與治療、收據的關聯關係

**功能需求**:
- 病患治療記錄關聯
- 病患收據記錄關聯
- 病患排班記錄關聯
- 關聯資料統計與報表

### 3. 治療師管理

#### 3.1 治療師資料管理
**功能描述**: 治療師人員資訊與工作狀態管理

**資料欄位**:
- 基本資訊: 姓名、Email、電話、地址、性別、生日
- 系統資訊: 帳號、密碼、啟用狀態
- 工作資訊: 角色權限、操作記錄

**功能需求**:
- 治療師資料新增/編輯
- 帳號權限管理
- 工作狀態追蹤
- 治療案例分配

**驗收標準**:
- ✅ 完整治療師資料管理
- ✅ 帳號權限靈活配置
- ✅ 工作負荷統計
- ✅ 治療案例關聯

### 4. 排班管理系統

#### 4.1 排班建立與管理
**功能描述**: 智能排班系統，支援重複排班與 Google Calendar 整合

**資料欄位**:
- 排班資訊: 標題、開始時間、結束時間、描述
- 人員配置: 治療師ID、病患ID、治療案例ID
- 重複設定: 重複類型 (不重複/每日/每週/每月)、重複群組ID
- 視覺設定: 背景色、邊框色
- Google Calendar: 事件ID同步

**功能需求**:
- 排班新增/編輯/刪除
- 重複排班自動產生
- Google Calendar 雙向同步
- 排班衝突檢測
- 日曆與列表雙視圖

**驗收標準**:
- ✅ 支援單次與重複排班
- ✅ Google Calendar 即時同步
- ✅ 時間衝突自動檢測
- ✅ 多種視圖模式
- ✅ 排班歷史追蹤

#### 4.2 Google Calendar 整合
**功能描述**: 與 Google Calendar 的無縫整合

**技術實現**:
- Service Account 認證
- Calendar API 雙向同步
- 事件 ID 對應管理
- 自動同步機制

### 5. 診療記錄管理

#### 5.1 診療記錄建立
**功能描述**: 結構化診療記錄系統，支援 SOAP 格式

**資料欄位**:
- 基本資訊: 診療編號 (orderNo)、治療步驟狀態
- 病患主訴: 不適期間、可能原因、治療史、認識管道
- SOAP 記錄: 主觀 (Subjective)、客觀 (Objective)、評估 (Assessment)、計畫 (Plan)
- 文件管理: 醫院轉診單、治療同意書、收據URL
- 不適部位: 多選式不適區域記錄

**功能需求**:
- 結構化診療記錄建立
- 多檔案上傳管理
- 不適部位視覺化選擇
- 診療步驟狀態追蹤
- 診療記錄搜尋與篩選

**驗收標準**:
- ✅ 完整 SOAP 格式記錄
- ✅ 多檔案安全上傳
- ✅ 不適部位多選功能
- ✅ 診療流程狀態管理
- ✅ 歷史記錄查詢

#### 5.2 診療步驟管理
**功能描述**: 診療流程狀態追蹤

**步驟定義**:
- 初診評估
- 治療進行中
- 治療完成
- 結案

**功能需求**:
- 步驟狀態更新
- 步驟時間記錄
- 步驟權限控制
- 步驟統計報表

### 6. 收據管理系統

#### 6.1 收據建立與管理
**功能描述**: 治療費用收據產生與管理系統

**資料欄位**:
- 收據資訊: 收據編號 (orderNo)、治療項目、治療費用
- 關聯資訊: 治療記錄ID、病患ID
- 系統資訊: 建立時間、更新時間、操作者、刪除狀態

**功能需求**:
- 收據新增/編輯/查詢
- PDF 收據產生 (QuestPDF)
- 收據與診療記錄關聯
- 收據作廢管理 (軟刪除)
- 收據統計與報表

**驗收標準**:
- ✅ 自動產生收據編號
- ✅ PDF 格式收據輸出
- ✅ 診療記錄關聯
- ✅ 收據作廢追蹤
- ✅ 財務統計報表

### 7. 資料類型管理

#### 7.1 結構化資料分類
**功能描述**: 診療過程中使用的標準化資料分類管理

**資料類型群組**:
1. **正背面** (101-104): 正面左邊、正面右邊、背面左邊、背面右邊
2. **不適部位** (201-206): 頭頸部、胸肋部、腰臀部、間肘手部、腿膝足部、肢體活動控制受限
3. **不適情形** (301-305): 拉扯感刺痛、隱約疲頓感、脹麻感、無法敘述不適感、活動受限
4. **不適期間** (401-405): 0-2周、2周-2個月、2個月-6個月、6個月-12個月、12個月以上
5. **可能引發原因** (501-599): 運動跌撞傷、長時間固定姿勢、姿勢不良、其他
6. **曾接受處置** (601-699): 西醫針劑、西醫復健、中醫針灸、徒手治療、按摩整骨、其他
7. **認識管道** (701-799): 親友介紹、地圖搜尋、網路評價、路過招牌、院所轉介、其他
8. **系統性疾病史** (801-899): 心臟病、高血壓、骨質疏鬆、糖尿病、中風、巴金森、其他
9. **物理治療評估** (901-999): 筋膜鬆動術、關節鬆動術、運動治療

**功能需求**:
- 資料類型群組管理
- 資料類型項目管理
- 編號系統維護
- 啟用/停用狀態控制
- 歷史資料完整性保護

### 8. 系統管理功能

#### 8.1 系統監控
**功能描述**: 系統狀態監控與維護

**監控項目**:
- 資料庫連線狀態
- Redis 連線狀態
- 系統效能指標
- 錯誤日誌監控
- 用戶活動統計

**功能需求**:
- 即時狀態監控
- 系統健康檢查
- 效能指標統計
- 錯誤日誌管理
- 系統設定管理

#### 8.2 資料備份系統
**功能描述**: 自動化資料備份與還原機制

**備份策略**:
- **每日增量備份**: 凌晨 2:00 執行
- **每週完整備份**: 週日凌晨 3:00 執行
- **檔案備份**: 每日凌晨 1:00 執行圖片備份
- **雲端同步**: Dropbox 自動上傳

**技術實現**:
- MySQL binlog 增量備份
- mysqldump 完整備份
- rclone Dropbox 同步
- 自動化 crontab 排程

**功能需求**:
- 自動備份排程
- 備份檔案管理
- 雲端同步機制
- 還原功能
- 備份狀態監控

**驗收標準**:
- ✅ 每日自動增量備份
- ✅ 每週自動完整備份
- ✅ 檔案自動雲端同步
- ✅ 一鍵還原功能
- ✅ 備份狀態監控

### 9. 檔案上傳與管理

#### 9.1 檔案上傳系統
**功能描述**: 安全的檔案上傳與存取管理

**支援檔案類型**:
- 圖片檔案: JPG, PNG, GIF
- 文件檔案: PDF, DOC, DOCX
- 其他醫療相關檔案

**功能需求**:
- 檔案類型驗證
- 檔案大小限制
- 安全檔案存取
- 檔案與記錄關聯
- 檔案備份機制

**技術實現**:
- ASP.NET Core 檔案上傳
- 靜態檔案服務
- 檔案路徑管理
- 快取控制設定

### 10. 即時通訊功能

#### 10.1 SignalR 即時通訊
**功能描述**: 基於 SignalR 的即時通訊與通知系統

**功能需求**:
- 即時狀態更新
- 系統通知推送
- 報表產生進度通知
- 多用戶即時協作
- 連線狀態管理

**技術實現**:
- SignalR Hub 配置
- MessagePack 協議
- 自動重連機制
- 用戶群組管理

## 非功能性需求

### 1. 效能需求
- **回應時間**: API 回應時間 < 2秒
- **並發用戶**: 支援 50+ 並發用戶
- **資料庫效能**: 查詢回應時間 < 1秒
- **檔案上傳**: 支援 10MB 以下檔案上傳

### 2. 安全需求
- **身份驗證**: JWT Token 認證機制
- **權限控制**: 基於角色的存取控制
- **資料加密**: 密碼 BCrypt 加密
- **檔案安全**: 檔案類型與大小驗證
- **SQL 注入防護**: Entity Framework 參數化查詢

### 3. 可用性需求
- **系統可用性**: 99.5% 以上
- **備份恢復**: RTO < 4小時, RPO < 1小時
- **錯誤處理**: 友善的錯誤訊息與處理
- **用戶體驗**: 響應式設計，支援多裝置

### 4. 維護性需求
- **程式碼品質**: TypeScript 強型別檢查
- **日誌記錄**: 完整的操作與錯誤日誌
- **監控機制**: 系統狀態即時監控
- **文件完整**: API 文件與系統文件

## 部署架構

### 1. 容器化部署
**技術棧**:
- **容器化**: Docker + Docker Compose
- **前端容器**: Nginx + React 建置檔案
- **後端容器**: ASP.NET Core Runtime
- **資料庫**: MySQL 8.0 容器
- **快取**: Redis 7.2 容器

### 2. 網路配置
**端口配置**:
- 前端: 3308 (外部) → 80 (容器)
- 後端: 5001 (外部) → 5000 (容器)
- MySQL: 3306
- Redis: 6379

**網路安全**:
- 內部網路隔離
- CORS 跨域設定
- HTTPS 重定向 (生產環境)

### 3. 資料持久化
**Volume 配置**:
- MySQL 資料: mysql-data volume
- Redis 資料: redis-data volume
- 上傳檔案: ./uploads 目錄掛載
- 備份檔案: /home/<USER>/mysql-backup 掛載

## 開發與維護

### 1. 開發環境
**前端開發**:
```bash
cd frontend
npm install
npm start  # 開發模式 (port 3309)
npm run build  # 生產建置
```

**後端開發**:
```bash
cd backend
dotnet ef migrations add <MigrationName>
dotnet ef database update
dotnet run  # 開發模式
```

### 2. 部署流程
**本機建置**:
```bash
# 建置並推送映像檔
docker-compose build
docker push a253844/frontend:latest
docker push a253844/backend:latest
```

**遠端部署**:
```bash
# 拉取最新映像檔
docker-compose pull
# 啟動服務
docker-compose up -d
```

### 3. 備份維護
**自動備份排程**:
```bash
# Crontab 設定
0 1 * * * bash /home/<USER>/mysql-backup/backup-uploads.sh
0 2 * * * /home/<USER>/mysql-backup/daily-incremental-backup.sh
0 3 * * 0 /home/<USER>/mysql-backup/weekly-full-backup.sh
```

**手動還原**:
```bash
bash /home/<USER>/mysql-backup/restore-mysql.sh 2025-07-08
```

## 系統特色

### 1. 醫療專業化
- 針對物理治療所業務流程設計
- SOAP 格式診療記錄
- 結構化病患資料管理
- 專業的不適部位分類系統

### 2. 智能化管理
- Google Calendar 整合排班
- 自動化備份機制
- 即時狀態監控
- 智能衝突檢測

### 3. 現代化架構
- 微服務容器化部署
- 前後端分離架構
- 響應式用戶界面
- 即時通訊支援

### 4. 安全可靠
- 多層級權限控制
- 完整的資料備份
- 軟刪除資料保護
- 操作追蹤記錄

## 結論

厝邊頭家物理治療所管理系統是一個功能完整、技術先進的醫療管理平台。系統涵蓋了物理治療所的核心業務需求，從病患管理、診療記錄、排班安排到財務管理，提供了一站式的解決方案。

系統採用現代化的技術架構，確保了高效能、高可用性和良好的用戶體驗。完善的備份機制和安全措施保障了醫療資料的安全性和完整性。

透過持續的維護和優化，本系統將為物理治療所提供穩定、可靠的資訊化管理支援，提升工作效率和服務品質。
/**
 * 統一日誌管理系統
 */

export enum LogLevel {
  DEBUG = 0,
  INFO = 1,
  WARN = 2,
  ERROR = 3,
  NONE = 4
}

interface LogEntry {
  level: LogLevel;
  message: string;
  data?: any;
  timestamp: string;
  source?: string;
}

class Logger {
  private static instance: Logger;
  private logLevel: LogLevel;
  private isDevelopment: boolean;

  private constructor() {
    this.logLevel = process.env.NODE_ENV === 'production' ? LogLevel.WARN : LogLevel.DEBUG;
    this.isDevelopment = process.env.NODE_ENV === 'development';
  }

  static getInstance(): Logger {
    if (!Logger.instance) {
      Logger.instance = new Logger();
    }
    return Logger.instance;
  }

  private shouldLog(level: LogLevel): boolean {
    return level >= this.logLevel;
  }

  private formatMessage(level: LogLevel, message: string, source?: string): string {
    const timestamp = new Date().toISOString();
    const levelName = LogLevel[level];
    const sourcePrefix = source ? `[${source}]` : '';
    return `${timestamp} [${levelName}]${sourcePrefix} ${message}`;
  }

  private log(level: LogLevel, message: string, data?: any, source?: string): void {
    if (!this.shouldLog(level)) return;

    const formattedMessage = this.formatMessage(level, message, source);
    const logEntry: LogEntry = {
      level,
      message,
      data,
      timestamp: new Date().toISOString(),
      ...(source && { source })
    };

    // 在開發環境輸出到控制台
    if (this.isDevelopment) {
      switch (level) {
        case LogLevel.DEBUG:
          console.log(formattedMessage, data || '');
          break;
        case LogLevel.INFO:
          console.info(formattedMessage, data || '');
          break;
        case LogLevel.WARN:
          console.warn(formattedMessage, data || '');
          break;
        case LogLevel.ERROR:
          console.error(formattedMessage, data || '');
          break;
      }
    }

    // 在生產環境可以發送到監控服務
    if (level >= LogLevel.ERROR) {
      this.sendToMonitoring(logEntry);
    }
  }

  private sendToMonitoring(_logEntry: LogEntry): void {
    // TODO: 實現發送到監控服務的邏輯
    // 例如: Sentry, LogRocket, 或自定義監控服務
  }

  // 公共方法
  debug(message: string, data?: any, source?: string): void {
    this.log(LogLevel.DEBUG, message, data, source);
  }

  info(message: string, data?: any, source?: string): void {
    this.log(LogLevel.INFO, message, data, source);
  }

  warn(message: string, data?: any, source?: string): void {
    this.log(LogLevel.WARN, message, data, source);
  }

  error(message: string, data?: any, source?: string): void {
    this.log(LogLevel.ERROR, message, data, source);
  }

  // 特定領域的日誌方法
  auth(message: string, data?: any): void {
    this.log(LogLevel.INFO, message, data, 'AUTH');
  }

  api(message: string, data?: any): void {
    this.log(LogLevel.INFO, message, data, 'API');
  }

  route(message: string, data?: any): void {
    this.log(LogLevel.INFO, message, data, 'ROUTE');
  }

  ui(message: string, data?: any): void {
    this.log(LogLevel.DEBUG, message, data, 'UI');
  }

  // 設置日誌級別
  setLogLevel(level: LogLevel): void {
    this.logLevel = level;
  }

  // 獲取當前日誌級別
  getLogLevel(): LogLevel {
    return this.logLevel;
  }
}

// 導出單例實例
export const log = Logger.getInstance();

// 導出便捷方法
export const logger = {
  debug: (message: string, data?: any, source?: string) => log.debug(message, data, source),
  info: (message: string, data?: any, source?: string) => log.info(message, data, source),
  warn: (message: string, data?: any, source?: string) => log.warn(message, data, source),
  error: (message: string, data?: any, source?: string) => log.error(message, data, source),
  auth: (message: string, data?: any) => log.auth(message, data),
  api: (message: string, data?: any) => log.api(message, data),
  route: (message: string, data?: any) => log.route(message, data),
  ui: (message: string, data?: any) => log.ui(message, data),
};

export default log;

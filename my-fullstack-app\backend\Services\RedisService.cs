﻿using StackExchange.Redis;
using System;
using System.Threading.Tasks;

namespace MyApi.Services
{
    public class RedisService
    {
        private readonly IDatabase _database;

        public RedisService(IConnectionMultiplexer redis)
        {
            _database = redis.GetDatabase();
        }

        /// <summary>
        /// 設置字符串值（無失效時間）
        /// </summary>
        /// <param name="key">鍵</param>
        /// <param name="value">值</param>
        /// <returns></returns>
        public async Task SetStringAsync(string key, string value)
        {
            await _database.StringSetAsync(key, value);
        }

        /// <summary>
        /// 設置字符串值（帶失效時間）
        /// </summary>
        /// <param name="key">鍵</param>
        /// <param name="value">值</param>
        /// <param name="expiry">失效時間</param>
        /// <returns></returns>
        public async Task SetStringAsync(string key, string value, TimeSpan expiry)
        {
            await _database.StringSetAsync(key, value, expiry);
        }

        /// <summary>
        /// 設置字符串值（帶失效時間 - 秒數）
        /// </summary>
        /// <param name="key">鍵</param>
        /// <param name="value">值</param>
        /// <param name="expirySeconds">失效時間（秒）</param>
        /// <returns></returns>
        public async Task SetStringAsync(string key, string value, int expirySeconds)
        {
            await _database.StringSetAsync(key, value, TimeSpan.FromSeconds(expirySeconds));
        }

        /// <summary>
        /// 設置字符串值（帶失效時間 - 分鐘數）
        /// </summary>
        /// <param name="key">鍵</param>
        /// <param name="value">值</param>
        /// <param name="expiryMinutes">失效時間（分鐘）</param>
        /// <returns></returns>
        public async Task SetStringWithMinutesAsync(string key, string value, int expiryMinutes)
        {
            await _database.StringSetAsync(key, value, TimeSpan.FromMinutes(expiryMinutes));
        }

        /// <summary>
        /// 設置字符串值（帶失效時間 - 小時數）
        /// </summary>
        /// <param name="key">鍵</param>
        /// <param name="value">值</param>
        /// <param name="expiryHours">失效時間（小時）</param>
        /// <returns></returns>
        public async Task SetStringWithHoursAsync(string key, string value, int expiryHours)
        {
            await _database.StringSetAsync(key, value, TimeSpan.FromHours(expiryHours));
        }

        /// <summary>
        /// 設置字符串值（帶失效時間 - 天數）
        /// </summary>
        /// <param name="key">鍵</param>
        /// <param name="value">值</param>
        /// <param name="expiryDays">失效時間（天）</param>
        /// <returns></returns>
        public async Task SetStringWithDaysAsync(string key, string value, int expiryDays)
        {
            await _database.StringSetAsync(key, value, TimeSpan.FromDays(expiryDays));
        }

        /// <summary>
        /// 獲取字符串值
        /// </summary>
        /// <param name="key">鍵</param>
        /// <returns></returns>
        public async Task<string?> GetStringAsync(string key)
        {
            return await _database.StringGetAsync(key);
        }

        /// <summary>
        /// 刪除鍵
        /// </summary>
        /// <param name="key">鍵</param>
        /// <returns></returns>
        public async Task<bool> DeleteKeyAsync(string key)
        {
            return await _database.KeyDeleteAsync(key);
        }

        /// <summary>
        /// 檢查鍵是否存在
        /// </summary>
        /// <param name="key">鍵</param>
        /// <returns></returns>
        public async Task<bool> KeyExistsAsync(string key)
        {
            return await _database.KeyExistsAsync(key);
        }

        /// <summary>
        /// 設置鍵的失效時間
        /// </summary>
        /// <param name="key">鍵</param>
        /// <param name="expiry">失效時間</param>
        /// <returns></returns>
        public async Task<bool> SetExpiryAsync(string key, TimeSpan expiry)
        {
            return await _database.KeyExpireAsync(key, expiry);
        }

        /// <summary>
        /// 設置鍵的失效時間（秒數）
        /// </summary>
        /// <param name="key">鍵</param>
        /// <param name="expirySeconds">失效時間（秒）</param>
        /// <returns></returns>
        public async Task<bool> SetExpiryAsync(string key, int expirySeconds)
        {
            return await _database.KeyExpireAsync(key, TimeSpan.FromSeconds(expirySeconds));
        }

        /// <summary>
        /// 獲取鍵的剩餘生存時間
        /// </summary>
        /// <param name="key">鍵</param>
        /// <returns></returns>
        public async Task<TimeSpan?> GetTimeToLiveAsync(string key)
        {
            return await _database.KeyTimeToLiveAsync(key);
        }

        /// <summary>
        /// 移除鍵的失效時間（設為永不過期）
        /// </summary>
        /// <param name="key">鍵</param>
        /// <returns></returns>
        public async Task<bool> PersistAsync(string key)
        {
            return await _database.KeyPersistAsync(key);
        }

        /// <summary>
        /// 批量刪除鍵
        /// </summary>
        /// <param name="keys">鍵數組</param>
        /// <returns></returns>
        public async Task<long> DeleteKeysAsync(params string[] keys)
        {
            var redisKeys = Array.ConvertAll(keys, key => (RedisKey)key);
            return await _database.KeyDeleteAsync(redisKeys);
        }

        /// <summary>
        /// 設置值（無失效時間）
        /// </summary>
        /// <param name="key">鍵</param>
        /// <param name="value">值</param>
        /// <returns></returns>
        public async Task SetAsync(string key, string value)
        {
            await _database.StringSetAsync(key, value);
        }

        /// <summary>
        /// 設置值（帶失效時間）
        /// </summary>
        /// <param name="key">鍵</param>
        /// <param name="value">值</param>
        /// <param name="expiry">失效時間</param>
        /// <returns></returns>
        public async Task SetAsync(string key, string value, TimeSpan expiry)
        {
            await _database.StringSetAsync(key, value, expiry);
        }

        /// <summary>
        /// 獲取值
        /// </summary>
        /// <param name="key">鍵</param>
        /// <returns></returns>
        public async Task<string?> GetAsync(string key)
        {
            var value = await _database.StringGetAsync(key);
            return value.HasValue ? value.ToString() : null;
        }
    }
}

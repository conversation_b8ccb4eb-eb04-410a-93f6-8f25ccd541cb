-- 創建登入紀錄表
CREATE TABLE IF NOT EXISTS UserLogin_Logs (
    Id INT AUTO_INCREMENT PRIMARY KEY,
    Username VARCHAR(255) NOT NULL COMMENT '輸入的登入帳號',
    CreatedAt DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '創建時間',
    IPAddress VARCHAR(45) NOT NULL COMMENT 'IP位置',
    Device VARCHAR(500) COMMENT '裝置',
    Browser VARCHAR(500) COMMENT '瀏覽器',
    Status VARCHAR(50) NOT NULL COMMENT '結果 (Success/Failed)',
    INDEX idx_username (Username),
    INDEX idx_ipaddress (IPAddress),
    INDEX idx_createdat (CreatedAt),
    INDEX idx_status (Status)
) COMMENT='用戶登入紀錄表';

-- 創建IP封鎖表
CREATE TABLE IF NOT EXISTS IpBlock (
    Id INT AUTO_INCREMENT PRIMARY KEY,
    IPAddress VARCHAR(45) NOT NULL UNIQUE COMMENT 'IP位置',
    CreatedAt DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '創建時間',
    UpdatedAt DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新時間',
    ExpiredAt DATETIME NOT NULL COMMENT '到期時間',
    INDEX idx_ipaddress (IPAddress),
    INDEX idx_expiredat (ExpiredAt),
    INDEX idx_createdat (CreatedAt)
) COMMENT='IP封鎖表';

-- 驗證表創建
SELECT 'UserLogin_Logs table created successfully' as Result
WHERE EXISTS (
    SELECT 1 FROM information_schema.tables 
    WHERE table_schema = DATABASE() 
    AND table_name = 'UserLogin_Logs'
);

SELECT 'IpBlock table created successfully' as Result
WHERE EXISTS (
    SELECT 1 FROM information_schema.tables 
    WHERE table_schema = DATABASE() 
    AND table_name = 'IpBlock'
);

-- 查看表結構
DESCRIBE UserLogin_Logs;
DESCRIBE IpBlock;

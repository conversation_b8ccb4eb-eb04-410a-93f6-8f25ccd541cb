using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace MyApi.Models
{
    [Table("UserLogin_Logs")]
    public class UserLoginLog
    {
        [Key]
        public int Id { get; set; }

        [Required]
        [StringLength(255)]
        public string Username { get; set; } = string.Empty;

        [Required]
        public DateTime CreatedAt { get; set; } = DateTime.Now;

        [Required]
        [StringLength(45)]
        public string IPAddress { get; set; } = string.Empty;

        [StringLength(500)]
        public string? Device { get; set; }

        [StringLength(500)]
        public string? Browser { get; set; }

        [Required]
        [StringLength(50)]
        public string Status { get; set; } = string.Empty; // Success/Failed
    }

    public class UserLoginLogDto
    {
        public int Id { get; set; }
        public string Username { get; set; } = string.Empty;
        public DateTime CreatedAt { get; set; }
        public string IPAddress { get; set; } = string.Empty;
        public string? Device { get; set; }
        public string? Browser { get; set; }
        public string Status { get; set; } = string.Empty;
    }

    public class GetUserLoginLogRequest
    {
        public string? IPAddress { get; set; }
        public string? Device { get; set; }
        public string? Status { get; set; }
        public DateTime? CreatedAtStart { get; set; }
        public DateTime? CreatedAtEnd { get; set; }
        public int Page { get; set; } = 1;
        public int PageSize { get; set; } = 20;
    }
}

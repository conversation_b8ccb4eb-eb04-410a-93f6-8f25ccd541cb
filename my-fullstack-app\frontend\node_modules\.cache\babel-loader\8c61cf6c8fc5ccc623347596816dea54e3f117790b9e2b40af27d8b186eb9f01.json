{"ast": null, "code": "import { createPlugin } from '@fullcalendar/core/index.js';\nimport { config, Emitter, elementClosest, applyStyle, whenTransitionDone, removeElement, ScrollController, ElementScrollController, computeInnerRect, WindowScrollController, ElementDragging, preventSelection, preventContextMenu, allowSelection, allowContextMenu, computeRect, getClippingParents, pointInsideRect, constrainPoint, intersectRects, getRectCenter, diffPoints, mapHash, rangeContainsRange, isDateSpansEqual, Interaction, interactionSettingsToStore, isDateSelectionValid, enableCursor, disableCursor, triggerDateSelect, compareNumbers, getElSeg, getRelevantEvents, EventImpl, createEmptyEventStore, applyMutationToEventStore, isInteractionValid, buildEventApis, interactionSettingsStore, startOfDay, diffDates, createDuration, getEventTargetViaRoot, identity, eventTupleToStore, parseDragMeta, elementMatches, refineEventDef, parseEventDef, getDefaultEventEnd, createEventInstance, BASE_OPTION_DEFAULTS } from '@fullcalendar/core/internal.js';\nconfig.touchMouseIgnoreWait = 500;\nlet ignoreMouseDepth = 0;\nlet listenerCnt = 0;\nlet isWindowTouchMoveCancelled = false;\n/*\nUses a \"pointer\" abstraction, which monitors UI events for both mouse and touch.\nTracks when the pointer \"drags\" on a certain element, meaning down+move+up.\n\nAlso, tracks if there was touch-scrolling.\nAlso, can prevent touch-scrolling from happening.\nAlso, can fire pointermove events when scrolling happens underneath, even when no real pointer movement.\n\nemits:\n- pointerdown\n- pointermove\n- pointerup\n*/\nclass PointerDragging {\n  constructor(containerEl) {\n    this.subjectEl = null;\n    // options that can be directly assigned by caller\n    this.selector = ''; // will cause subjectEl in all emitted events to be this element\n    this.handleSelector = '';\n    this.shouldIgnoreMove = false;\n    this.shouldWatchScroll = true; // for simulating pointermove on scroll\n    // internal states\n    this.isDragging = false;\n    this.isTouchDragging = false;\n    this.wasTouchScroll = false;\n    // Mouse\n    // ----------------------------------------------------------------------------------------------------\n    this.handleMouseDown = ev => {\n      if (!this.shouldIgnoreMouse() && isPrimaryMouseButton(ev) && this.tryStart(ev)) {\n        let pev = this.createEventFromMouse(ev, true);\n        this.emitter.trigger('pointerdown', pev);\n        this.initScrollWatch(pev);\n        if (!this.shouldIgnoreMove) {\n          document.addEventListener('mousemove', this.handleMouseMove);\n        }\n        document.addEventListener('mouseup', this.handleMouseUp);\n      }\n    };\n    this.handleMouseMove = ev => {\n      let pev = this.createEventFromMouse(ev);\n      this.recordCoords(pev);\n      this.emitter.trigger('pointermove', pev);\n    };\n    this.handleMouseUp = ev => {\n      document.removeEventListener('mousemove', this.handleMouseMove);\n      document.removeEventListener('mouseup', this.handleMouseUp);\n      this.emitter.trigger('pointerup', this.createEventFromMouse(ev));\n      this.cleanup(); // call last so that pointerup has access to props\n    };\n    // Touch\n    // ----------------------------------------------------------------------------------------------------\n    this.handleTouchStart = ev => {\n      if (this.tryStart(ev)) {\n        this.isTouchDragging = true;\n        let pev = this.createEventFromTouch(ev, true);\n        this.emitter.trigger('pointerdown', pev);\n        this.initScrollWatch(pev);\n        // unlike mouse, need to attach to target, not document\n        // https://stackoverflow.com/a/45760014\n        let targetEl = ev.target;\n        if (!this.shouldIgnoreMove) {\n          targetEl.addEventListener('touchmove', this.handleTouchMove);\n        }\n        targetEl.addEventListener('touchend', this.handleTouchEnd);\n        targetEl.addEventListener('touchcancel', this.handleTouchEnd); // treat it as a touch end\n        // attach a handler to get called when ANY scroll action happens on the page.\n        // this was impossible to do with normal on/off because 'scroll' doesn't bubble.\n        // http://stackoverflow.com/a/32954565/96342\n        window.addEventListener('scroll', this.handleTouchScroll, true);\n      }\n    };\n    this.handleTouchMove = ev => {\n      let pev = this.createEventFromTouch(ev);\n      this.recordCoords(pev);\n      this.emitter.trigger('pointermove', pev);\n    };\n    this.handleTouchEnd = ev => {\n      if (this.isDragging) {\n        // done to guard against touchend followed by touchcancel\n        let targetEl = ev.target;\n        targetEl.removeEventListener('touchmove', this.handleTouchMove);\n        targetEl.removeEventListener('touchend', this.handleTouchEnd);\n        targetEl.removeEventListener('touchcancel', this.handleTouchEnd);\n        window.removeEventListener('scroll', this.handleTouchScroll, true); // useCaptured=true\n        this.emitter.trigger('pointerup', this.createEventFromTouch(ev));\n        this.cleanup(); // call last so that pointerup has access to props\n        this.isTouchDragging = false;\n        startIgnoringMouse();\n      }\n    };\n    this.handleTouchScroll = () => {\n      this.wasTouchScroll = true;\n    };\n    this.handleScroll = ev => {\n      if (!this.shouldIgnoreMove) {\n        let pageX = window.scrollX - this.prevScrollX + this.prevPageX;\n        let pageY = window.scrollY - this.prevScrollY + this.prevPageY;\n        this.emitter.trigger('pointermove', {\n          origEvent: ev,\n          isTouch: this.isTouchDragging,\n          subjectEl: this.subjectEl,\n          pageX,\n          pageY,\n          deltaX: pageX - this.origPageX,\n          deltaY: pageY - this.origPageY\n        });\n      }\n    };\n    this.containerEl = containerEl;\n    this.emitter = new Emitter();\n    containerEl.addEventListener('mousedown', this.handleMouseDown);\n    containerEl.addEventListener('touchstart', this.handleTouchStart, {\n      passive: true\n    });\n    listenerCreated();\n  }\n  destroy() {\n    this.containerEl.removeEventListener('mousedown', this.handleMouseDown);\n    this.containerEl.removeEventListener('touchstart', this.handleTouchStart, {\n      passive: true\n    });\n    listenerDestroyed();\n  }\n  tryStart(ev) {\n    let subjectEl = this.querySubjectEl(ev);\n    let downEl = ev.target;\n    if (subjectEl && (!this.handleSelector || elementClosest(downEl, this.handleSelector))) {\n      this.subjectEl = subjectEl;\n      this.isDragging = true; // do this first so cancelTouchScroll will work\n      this.wasTouchScroll = false;\n      return true;\n    }\n    return false;\n  }\n  cleanup() {\n    isWindowTouchMoveCancelled = false;\n    this.isDragging = false;\n    this.subjectEl = null;\n    // keep wasTouchScroll around for later access\n    this.destroyScrollWatch();\n  }\n  querySubjectEl(ev) {\n    if (this.selector) {\n      return elementClosest(ev.target, this.selector);\n    }\n    return this.containerEl;\n  }\n  shouldIgnoreMouse() {\n    return ignoreMouseDepth || this.isTouchDragging;\n  }\n  // can be called by user of this class, to cancel touch-based scrolling for the current drag\n  cancelTouchScroll() {\n    if (this.isDragging) {\n      isWindowTouchMoveCancelled = true;\n    }\n  }\n  // Scrolling that simulates pointermoves\n  // ----------------------------------------------------------------------------------------------------\n  initScrollWatch(ev) {\n    if (this.shouldWatchScroll) {\n      this.recordCoords(ev);\n      window.addEventListener('scroll', this.handleScroll, true); // useCapture=true\n    }\n  }\n  recordCoords(ev) {\n    if (this.shouldWatchScroll) {\n      this.prevPageX = ev.pageX;\n      this.prevPageY = ev.pageY;\n      this.prevScrollX = window.scrollX;\n      this.prevScrollY = window.scrollY;\n    }\n  }\n  destroyScrollWatch() {\n    if (this.shouldWatchScroll) {\n      window.removeEventListener('scroll', this.handleScroll, true); // useCaptured=true\n    }\n  }\n  // Event Normalization\n  // ----------------------------------------------------------------------------------------------------\n  createEventFromMouse(ev, isFirst) {\n    let deltaX = 0;\n    let deltaY = 0;\n    // TODO: repeat code\n    if (isFirst) {\n      this.origPageX = ev.pageX;\n      this.origPageY = ev.pageY;\n    } else {\n      deltaX = ev.pageX - this.origPageX;\n      deltaY = ev.pageY - this.origPageY;\n    }\n    return {\n      origEvent: ev,\n      isTouch: false,\n      subjectEl: this.subjectEl,\n      pageX: ev.pageX,\n      pageY: ev.pageY,\n      deltaX,\n      deltaY\n    };\n  }\n  createEventFromTouch(ev, isFirst) {\n    let touches = ev.touches;\n    let pageX;\n    let pageY;\n    let deltaX = 0;\n    let deltaY = 0;\n    // if touch coords available, prefer,\n    // because FF would give bad ev.pageX ev.pageY\n    if (touches && touches.length) {\n      pageX = touches[0].pageX;\n      pageY = touches[0].pageY;\n    } else {\n      pageX = ev.pageX;\n      pageY = ev.pageY;\n    }\n    // TODO: repeat code\n    if (isFirst) {\n      this.origPageX = pageX;\n      this.origPageY = pageY;\n    } else {\n      deltaX = pageX - this.origPageX;\n      deltaY = pageY - this.origPageY;\n    }\n    return {\n      origEvent: ev,\n      isTouch: true,\n      subjectEl: this.subjectEl,\n      pageX,\n      pageY,\n      deltaX,\n      deltaY\n    };\n  }\n}\n// Returns a boolean whether this was a left mouse click and no ctrl key (which means right click on Mac)\nfunction isPrimaryMouseButton(ev) {\n  return ev.button === 0 && !ev.ctrlKey;\n}\n// Ignoring fake mouse events generated by touch\n// ----------------------------------------------------------------------------------------------------\nfunction startIgnoringMouse() {\n  ignoreMouseDepth += 1;\n  setTimeout(() => {\n    ignoreMouseDepth -= 1;\n  }, config.touchMouseIgnoreWait);\n}\n// We want to attach touchmove as early as possible for Safari\n// ----------------------------------------------------------------------------------------------------\nfunction listenerCreated() {\n  listenerCnt += 1;\n  if (listenerCnt === 1) {\n    window.addEventListener('touchmove', onWindowTouchMove, {\n      passive: false\n    });\n  }\n}\nfunction listenerDestroyed() {\n  listenerCnt -= 1;\n  if (!listenerCnt) {\n    window.removeEventListener('touchmove', onWindowTouchMove, {\n      passive: false\n    });\n  }\n}\nfunction onWindowTouchMove(ev) {\n  if (isWindowTouchMoveCancelled) {\n    ev.preventDefault();\n  }\n}\n\n/*\nAn effect in which an element follows the movement of a pointer across the screen.\nThe moving element is a clone of some other element.\nMust call start + handleMove + stop.\n*/\nclass ElementMirror {\n  constructor() {\n    this.isVisible = false; // must be explicitly enabled\n    this.sourceEl = null;\n    this.mirrorEl = null;\n    this.sourceElRect = null; // screen coords relative to viewport\n    // options that can be set directly by caller\n    this.parentNode = document.body; // HIGHLY SUGGESTED to set this to sidestep ShadowDOM issues\n    this.zIndex = 9999;\n    this.revertDuration = 0;\n  }\n  start(sourceEl, pageX, pageY) {\n    this.sourceEl = sourceEl;\n    this.sourceElRect = this.sourceEl.getBoundingClientRect();\n    this.origScreenX = pageX - window.scrollX;\n    this.origScreenY = pageY - window.scrollY;\n    this.deltaX = 0;\n    this.deltaY = 0;\n    this.updateElPosition();\n  }\n  handleMove(pageX, pageY) {\n    this.deltaX = pageX - window.scrollX - this.origScreenX;\n    this.deltaY = pageY - window.scrollY - this.origScreenY;\n    this.updateElPosition();\n  }\n  // can be called before start\n  setIsVisible(bool) {\n    if (bool) {\n      if (!this.isVisible) {\n        if (this.mirrorEl) {\n          this.mirrorEl.style.display = '';\n        }\n        this.isVisible = bool; // needs to happen before updateElPosition\n        this.updateElPosition(); // because was not updating the position while invisible\n      }\n    } else if (this.isVisible) {\n      if (this.mirrorEl) {\n        this.mirrorEl.style.display = 'none';\n      }\n      this.isVisible = bool;\n    }\n  }\n  // always async\n  stop(needsRevertAnimation, callback) {\n    let done = () => {\n      this.cleanup();\n      callback();\n    };\n    if (needsRevertAnimation && this.mirrorEl && this.isVisible && this.revertDuration && (\n    // if 0, transition won't work\n    this.deltaX || this.deltaY) // if same coords, transition won't work\n    ) {\n      this.doRevertAnimation(done, this.revertDuration);\n    } else {\n      setTimeout(done, 0);\n    }\n  }\n  doRevertAnimation(callback, revertDuration) {\n    let mirrorEl = this.mirrorEl;\n    let finalSourceElRect = this.sourceEl.getBoundingClientRect(); // because autoscrolling might have happened\n    mirrorEl.style.transition = 'top ' + revertDuration + 'ms,' + 'left ' + revertDuration + 'ms';\n    applyStyle(mirrorEl, {\n      left: finalSourceElRect.left,\n      top: finalSourceElRect.top\n    });\n    whenTransitionDone(mirrorEl, () => {\n      mirrorEl.style.transition = '';\n      callback();\n    });\n  }\n  cleanup() {\n    if (this.mirrorEl) {\n      removeElement(this.mirrorEl);\n      this.mirrorEl = null;\n    }\n    this.sourceEl = null;\n  }\n  updateElPosition() {\n    if (this.sourceEl && this.isVisible) {\n      applyStyle(this.getMirrorEl(), {\n        left: this.sourceElRect.left + this.deltaX,\n        top: this.sourceElRect.top + this.deltaY\n      });\n    }\n  }\n  getMirrorEl() {\n    let sourceElRect = this.sourceElRect;\n    let mirrorEl = this.mirrorEl;\n    if (!mirrorEl) {\n      mirrorEl = this.mirrorEl = this.sourceEl.cloneNode(true); // cloneChildren=true\n      // we don't want long taps or any mouse interaction causing selection/menus.\n      // would use preventSelection(), but that prevents selectstart, causing problems.\n      mirrorEl.style.userSelect = 'none';\n      mirrorEl.style.webkitUserSelect = 'none';\n      mirrorEl.style.pointerEvents = 'none';\n      mirrorEl.classList.add('fc-event-dragging');\n      applyStyle(mirrorEl, {\n        position: 'fixed',\n        zIndex: this.zIndex,\n        visibility: '',\n        boxSizing: 'border-box',\n        width: sourceElRect.right - sourceElRect.left,\n        height: sourceElRect.bottom - sourceElRect.top,\n        right: 'auto',\n        bottom: 'auto',\n        margin: 0\n      });\n      this.parentNode.appendChild(mirrorEl);\n    }\n    return mirrorEl;\n  }\n}\n\n/*\nIs a cache for a given element's scroll information (all the info that ScrollController stores)\nin addition the \"client rectangle\" of the element.. the area within the scrollbars.\n\nThe cache can be in one of two modes:\n- doesListening:false - ignores when the container is scrolled by someone else\n- doesListening:true - watch for scrolling and update the cache\n*/\nclass ScrollGeomCache extends ScrollController {\n  constructor(scrollController, doesListening) {\n    super();\n    this.handleScroll = () => {\n      this.scrollTop = this.scrollController.getScrollTop();\n      this.scrollLeft = this.scrollController.getScrollLeft();\n      this.handleScrollChange();\n    };\n    this.scrollController = scrollController;\n    this.doesListening = doesListening;\n    this.scrollTop = this.origScrollTop = scrollController.getScrollTop();\n    this.scrollLeft = this.origScrollLeft = scrollController.getScrollLeft();\n    this.scrollWidth = scrollController.getScrollWidth();\n    this.scrollHeight = scrollController.getScrollHeight();\n    this.clientWidth = scrollController.getClientWidth();\n    this.clientHeight = scrollController.getClientHeight();\n    this.clientRect = this.computeClientRect(); // do last in case it needs cached values\n    if (this.doesListening) {\n      this.getEventTarget().addEventListener('scroll', this.handleScroll);\n    }\n  }\n  destroy() {\n    if (this.doesListening) {\n      this.getEventTarget().removeEventListener('scroll', this.handleScroll);\n    }\n  }\n  getScrollTop() {\n    return this.scrollTop;\n  }\n  getScrollLeft() {\n    return this.scrollLeft;\n  }\n  setScrollTop(top) {\n    this.scrollController.setScrollTop(top);\n    if (!this.doesListening) {\n      // we are not relying on the element to normalize out-of-bounds scroll values\n      // so we need to sanitize ourselves\n      this.scrollTop = Math.max(Math.min(top, this.getMaxScrollTop()), 0);\n      this.handleScrollChange();\n    }\n  }\n  setScrollLeft(top) {\n    this.scrollController.setScrollLeft(top);\n    if (!this.doesListening) {\n      // we are not relying on the element to normalize out-of-bounds scroll values\n      // so we need to sanitize ourselves\n      this.scrollLeft = Math.max(Math.min(top, this.getMaxScrollLeft()), 0);\n      this.handleScrollChange();\n    }\n  }\n  getClientWidth() {\n    return this.clientWidth;\n  }\n  getClientHeight() {\n    return this.clientHeight;\n  }\n  getScrollWidth() {\n    return this.scrollWidth;\n  }\n  getScrollHeight() {\n    return this.scrollHeight;\n  }\n  handleScrollChange() {}\n}\nclass ElementScrollGeomCache extends ScrollGeomCache {\n  constructor(el, doesListening) {\n    super(new ElementScrollController(el), doesListening);\n  }\n  getEventTarget() {\n    return this.scrollController.el;\n  }\n  computeClientRect() {\n    return computeInnerRect(this.scrollController.el);\n  }\n}\nclass WindowScrollGeomCache extends ScrollGeomCache {\n  constructor(doesListening) {\n    super(new WindowScrollController(), doesListening);\n  }\n  getEventTarget() {\n    return window;\n  }\n  computeClientRect() {\n    return {\n      left: this.scrollLeft,\n      right: this.scrollLeft + this.clientWidth,\n      top: this.scrollTop,\n      bottom: this.scrollTop + this.clientHeight\n    };\n  }\n  // the window is the only scroll object that changes it's rectangle relative\n  // to the document's topleft as it scrolls\n  handleScrollChange() {\n    this.clientRect = this.computeClientRect();\n  }\n}\n\n// If available we are using native \"performance\" API instead of \"Date\"\n// Read more about it on MDN:\n// https://developer.mozilla.org/en-US/docs/Web/API/Performance\nconst getTime = typeof performance === 'function' ? performance.now : Date.now;\n/*\nFor a pointer interaction, automatically scrolls certain scroll containers when the pointer\napproaches the edge.\n\nThe caller must call start + handleMove + stop.\n*/\nclass AutoScroller {\n  constructor() {\n    // options that can be set by caller\n    this.isEnabled = true;\n    this.scrollQuery = [window, '.fc-scroller'];\n    this.edgeThreshold = 50; // pixels\n    this.maxVelocity = 300; // pixels per second\n    // internal state\n    this.pointerScreenX = null;\n    this.pointerScreenY = null;\n    this.isAnimating = false;\n    this.scrollCaches = null;\n    // protect against the initial pointerdown being too close to an edge and starting the scroll\n    this.everMovedUp = false;\n    this.everMovedDown = false;\n    this.everMovedLeft = false;\n    this.everMovedRight = false;\n    this.animate = () => {\n      if (this.isAnimating) {\n        // wasn't cancelled between animation calls\n        let edge = this.computeBestEdge(this.pointerScreenX + window.scrollX, this.pointerScreenY + window.scrollY);\n        if (edge) {\n          let now = getTime();\n          this.handleSide(edge, (now - this.msSinceRequest) / 1000);\n          this.requestAnimation(now);\n        } else {\n          this.isAnimating = false; // will stop animation\n        }\n      }\n    };\n  }\n  start(pageX, pageY, scrollStartEl) {\n    if (this.isEnabled) {\n      this.scrollCaches = this.buildCaches(scrollStartEl);\n      this.pointerScreenX = null;\n      this.pointerScreenY = null;\n      this.everMovedUp = false;\n      this.everMovedDown = false;\n      this.everMovedLeft = false;\n      this.everMovedRight = false;\n      this.handleMove(pageX, pageY);\n    }\n  }\n  handleMove(pageX, pageY) {\n    if (this.isEnabled) {\n      let pointerScreenX = pageX - window.scrollX;\n      let pointerScreenY = pageY - window.scrollY;\n      let yDelta = this.pointerScreenY === null ? 0 : pointerScreenY - this.pointerScreenY;\n      let xDelta = this.pointerScreenX === null ? 0 : pointerScreenX - this.pointerScreenX;\n      if (yDelta < 0) {\n        this.everMovedUp = true;\n      } else if (yDelta > 0) {\n        this.everMovedDown = true;\n      }\n      if (xDelta < 0) {\n        this.everMovedLeft = true;\n      } else if (xDelta > 0) {\n        this.everMovedRight = true;\n      }\n      this.pointerScreenX = pointerScreenX;\n      this.pointerScreenY = pointerScreenY;\n      if (!this.isAnimating) {\n        this.isAnimating = true;\n        this.requestAnimation(getTime());\n      }\n    }\n  }\n  stop() {\n    if (this.isEnabled) {\n      this.isAnimating = false; // will stop animation\n      for (let scrollCache of this.scrollCaches) {\n        scrollCache.destroy();\n      }\n      this.scrollCaches = null;\n    }\n  }\n  requestAnimation(now) {\n    this.msSinceRequest = now;\n    requestAnimationFrame(this.animate);\n  }\n  handleSide(edge, seconds) {\n    let {\n      scrollCache\n    } = edge;\n    let {\n      edgeThreshold\n    } = this;\n    let invDistance = edgeThreshold - edge.distance;\n    let velocity =\n    // the closer to the edge, the faster we scroll\n    invDistance * invDistance / (edgeThreshold * edgeThreshold) *\n    // quadratic\n    this.maxVelocity * seconds;\n    let sign = 1;\n    switch (edge.name) {\n      case 'left':\n        sign = -1;\n      // falls through\n      case 'right':\n        scrollCache.setScrollLeft(scrollCache.getScrollLeft() + velocity * sign);\n        break;\n      case 'top':\n        sign = -1;\n      // falls through\n      case 'bottom':\n        scrollCache.setScrollTop(scrollCache.getScrollTop() + velocity * sign);\n        break;\n    }\n  }\n  // left/top are relative to document topleft\n  computeBestEdge(left, top) {\n    let {\n      edgeThreshold\n    } = this;\n    let bestSide = null;\n    let scrollCaches = this.scrollCaches || [];\n    for (let scrollCache of scrollCaches) {\n      let rect = scrollCache.clientRect;\n      let leftDist = left - rect.left;\n      let rightDist = rect.right - left;\n      let topDist = top - rect.top;\n      let bottomDist = rect.bottom - top;\n      // completely within the rect?\n      if (leftDist >= 0 && rightDist >= 0 && topDist >= 0 && bottomDist >= 0) {\n        if (topDist <= edgeThreshold && this.everMovedUp && scrollCache.canScrollUp() && (!bestSide || bestSide.distance > topDist)) {\n          bestSide = {\n            scrollCache,\n            name: 'top',\n            distance: topDist\n          };\n        }\n        if (bottomDist <= edgeThreshold && this.everMovedDown && scrollCache.canScrollDown() && (!bestSide || bestSide.distance > bottomDist)) {\n          bestSide = {\n            scrollCache,\n            name: 'bottom',\n            distance: bottomDist\n          };\n        }\n        /*\n        TODO: fix broken RTL scrolling. canScrollLeft always returning false\n        https://github.com/fullcalendar/fullcalendar/issues/4837\n        */\n        if (leftDist <= edgeThreshold && this.everMovedLeft && scrollCache.canScrollLeft() && (!bestSide || bestSide.distance > leftDist)) {\n          bestSide = {\n            scrollCache,\n            name: 'left',\n            distance: leftDist\n          };\n        }\n        if (rightDist <= edgeThreshold && this.everMovedRight && scrollCache.canScrollRight() && (!bestSide || bestSide.distance > rightDist)) {\n          bestSide = {\n            scrollCache,\n            name: 'right',\n            distance: rightDist\n          };\n        }\n      }\n    }\n    return bestSide;\n  }\n  buildCaches(scrollStartEl) {\n    return this.queryScrollEls(scrollStartEl).map(el => {\n      if (el === window) {\n        return new WindowScrollGeomCache(false); // false = don't listen to user-generated scrolls\n      }\n      return new ElementScrollGeomCache(el, false); // false = don't listen to user-generated scrolls\n    });\n  }\n  queryScrollEls(scrollStartEl) {\n    let els = [];\n    for (let query of this.scrollQuery) {\n      if (typeof query === 'object') {\n        els.push(query);\n      } else {\n        /*\n        TODO: in the future, always have auto-scroll happen on element where current Hit came from\n        Ticket: https://github.com/fullcalendar/fullcalendar/issues/4593\n        */\n        els.push(...Array.prototype.slice.call(scrollStartEl.getRootNode().querySelectorAll(query)));\n      }\n    }\n    return els;\n  }\n}\n\n/*\nMonitors dragging on an element. Has a number of high-level features:\n- minimum distance required before dragging\n- minimum wait time (\"delay\") before dragging\n- a mirror element that follows the pointer\n*/\nclass FeaturefulElementDragging extends ElementDragging {\n  constructor(containerEl, selector) {\n    super(containerEl);\n    this.containerEl = containerEl;\n    // options that can be directly set by caller\n    // the caller can also set the PointerDragging's options as well\n    this.delay = null;\n    this.minDistance = 0;\n    this.touchScrollAllowed = true; // prevents drag from starting and blocks scrolling during drag\n    this.mirrorNeedsRevert = false;\n    this.isInteracting = false; // is the user validly moving the pointer? lasts until pointerup\n    this.isDragging = false; // is it INTENTFULLY dragging? lasts until after revert animation\n    this.isDelayEnded = false;\n    this.isDistanceSurpassed = false;\n    this.delayTimeoutId = null;\n    this.onPointerDown = ev => {\n      if (!this.isDragging) {\n        // so new drag doesn't happen while revert animation is going\n        this.isInteracting = true;\n        this.isDelayEnded = false;\n        this.isDistanceSurpassed = false;\n        preventSelection(document.body);\n        preventContextMenu(document.body);\n        // prevent links from being visited if there's an eventual drag.\n        // also prevents selection in older browsers (maybe?).\n        // not necessary for touch, besides, browser would complain about passiveness.\n        if (!ev.isTouch) {\n          ev.origEvent.preventDefault();\n        }\n        this.emitter.trigger('pointerdown', ev);\n        if (this.isInteracting &&\n        // not destroyed via pointerdown handler\n        !this.pointer.shouldIgnoreMove) {\n          // actions related to initiating dragstart+dragmove+dragend...\n          this.mirror.setIsVisible(false); // reset. caller must set-visible\n          this.mirror.start(ev.subjectEl, ev.pageX, ev.pageY); // must happen on first pointer down\n          this.startDelay(ev);\n          if (!this.minDistance) {\n            this.handleDistanceSurpassed(ev);\n          }\n        }\n      }\n    };\n    this.onPointerMove = ev => {\n      if (this.isInteracting) {\n        this.emitter.trigger('pointermove', ev);\n        if (!this.isDistanceSurpassed) {\n          let minDistance = this.minDistance;\n          let distanceSq; // current distance from the origin, squared\n          let {\n            deltaX,\n            deltaY\n          } = ev;\n          distanceSq = deltaX * deltaX + deltaY * deltaY;\n          if (distanceSq >= minDistance * minDistance) {\n            // use pythagorean theorem\n            this.handleDistanceSurpassed(ev);\n          }\n        }\n        if (this.isDragging) {\n          // a real pointer move? (not one simulated by scrolling)\n          if (ev.origEvent.type !== 'scroll') {\n            this.mirror.handleMove(ev.pageX, ev.pageY);\n            this.autoScroller.handleMove(ev.pageX, ev.pageY);\n          }\n          this.emitter.trigger('dragmove', ev);\n        }\n      }\n    };\n    this.onPointerUp = ev => {\n      if (this.isInteracting) {\n        this.isInteracting = false;\n        allowSelection(document.body);\n        allowContextMenu(document.body);\n        this.emitter.trigger('pointerup', ev); // can potentially set mirrorNeedsRevert\n        if (this.isDragging) {\n          this.autoScroller.stop();\n          this.tryStopDrag(ev); // which will stop the mirror\n        }\n        if (this.delayTimeoutId) {\n          clearTimeout(this.delayTimeoutId);\n          this.delayTimeoutId = null;\n        }\n      }\n    };\n    let pointer = this.pointer = new PointerDragging(containerEl);\n    pointer.emitter.on('pointerdown', this.onPointerDown);\n    pointer.emitter.on('pointermove', this.onPointerMove);\n    pointer.emitter.on('pointerup', this.onPointerUp);\n    if (selector) {\n      pointer.selector = selector;\n    }\n    this.mirror = new ElementMirror();\n    this.autoScroller = new AutoScroller();\n  }\n  destroy() {\n    this.pointer.destroy();\n    // HACK: simulate a pointer-up to end the current drag\n    // TODO: fire 'dragend' directly and stop interaction. discourage use of pointerup event (b/c might not fire)\n    this.onPointerUp({});\n  }\n  startDelay(ev) {\n    if (typeof this.delay === 'number') {\n      this.delayTimeoutId = setTimeout(() => {\n        this.delayTimeoutId = null;\n        this.handleDelayEnd(ev);\n      }, this.delay); // not assignable to number!\n    } else {\n      this.handleDelayEnd(ev);\n    }\n  }\n  handleDelayEnd(ev) {\n    this.isDelayEnded = true;\n    this.tryStartDrag(ev);\n  }\n  handleDistanceSurpassed(ev) {\n    this.isDistanceSurpassed = true;\n    this.tryStartDrag(ev);\n  }\n  tryStartDrag(ev) {\n    if (this.isDelayEnded && this.isDistanceSurpassed) {\n      if (!this.pointer.wasTouchScroll || this.touchScrollAllowed) {\n        this.isDragging = true;\n        this.mirrorNeedsRevert = false;\n        this.autoScroller.start(ev.pageX, ev.pageY, this.containerEl);\n        this.emitter.trigger('dragstart', ev);\n        if (this.touchScrollAllowed === false) {\n          this.pointer.cancelTouchScroll();\n        }\n      }\n    }\n  }\n  tryStopDrag(ev) {\n    // .stop() is ALWAYS asynchronous, which we NEED because we want all pointerup events\n    // that come from the document to fire beforehand. much more convenient this way.\n    this.mirror.stop(this.mirrorNeedsRevert, this.stopDrag.bind(this, ev));\n  }\n  stopDrag(ev) {\n    this.isDragging = false;\n    this.emitter.trigger('dragend', ev);\n  }\n  // fill in the implementations...\n  setIgnoreMove(bool) {\n    this.pointer.shouldIgnoreMove = bool;\n  }\n  setMirrorIsVisible(bool) {\n    this.mirror.setIsVisible(bool);\n  }\n  setMirrorNeedsRevert(bool) {\n    this.mirrorNeedsRevert = bool;\n  }\n  setAutoScrollEnabled(bool) {\n    this.autoScroller.isEnabled = bool;\n  }\n}\n\n/*\nWhen this class is instantiated, it records the offset of an element (relative to the document topleft),\nand continues to monitor scrolling, updating the cached coordinates if it needs to.\nDoes not access the DOM after instantiation, so highly performant.\n\nAlso keeps track of all scrolling/overflow:hidden containers that are parents of the given element\nand an determine if a given point is inside the combined clipping rectangle.\n*/\nclass OffsetTracker {\n  constructor(el) {\n    this.el = el;\n    this.origRect = computeRect(el);\n    // will work fine for divs that have overflow:hidden\n    this.scrollCaches = getClippingParents(el).map(scrollEl => new ElementScrollGeomCache(scrollEl, true));\n  }\n  destroy() {\n    for (let scrollCache of this.scrollCaches) {\n      scrollCache.destroy();\n    }\n  }\n  computeLeft() {\n    let left = this.origRect.left;\n    for (let scrollCache of this.scrollCaches) {\n      left += scrollCache.origScrollLeft - scrollCache.getScrollLeft();\n    }\n    return left;\n  }\n  computeTop() {\n    let top = this.origRect.top;\n    for (let scrollCache of this.scrollCaches) {\n      top += scrollCache.origScrollTop - scrollCache.getScrollTop();\n    }\n    return top;\n  }\n  isWithinClipping(pageX, pageY) {\n    let point = {\n      left: pageX,\n      top: pageY\n    };\n    for (let scrollCache of this.scrollCaches) {\n      if (!isIgnoredClipping(scrollCache.getEventTarget()) && !pointInsideRect(point, scrollCache.clientRect)) {\n        return false;\n      }\n    }\n    return true;\n  }\n}\n// certain clipping containers should never constrain interactions, like <html> and <body>\n// https://github.com/fullcalendar/fullcalendar/issues/3615\nfunction isIgnoredClipping(node) {\n  let tagName = node.tagName;\n  return tagName === 'HTML' || tagName === 'BODY';\n}\n\n/*\nTracks movement over multiple droppable areas (aka \"hits\")\nthat exist in one or more DateComponents.\nRelies on an existing draggable.\n\nemits:\n- pointerdown\n- dragstart\n- hitchange - fires initially, even if not over a hit\n- pointerup\n- (hitchange - again, to null, if ended over a hit)\n- dragend\n*/\nclass HitDragging {\n  constructor(dragging, droppableStore) {\n    // options that can be set by caller\n    this.useSubjectCenter = false;\n    this.requireInitial = true; // if doesn't start out on a hit, won't emit any events\n    this.disablePointCheck = false;\n    this.initialHit = null;\n    this.movingHit = null;\n    this.finalHit = null; // won't ever be populated if shouldIgnoreMove\n    this.handlePointerDown = ev => {\n      let {\n        dragging\n      } = this;\n      this.initialHit = null;\n      this.movingHit = null;\n      this.finalHit = null;\n      this.prepareHits();\n      this.processFirstCoord(ev);\n      if (this.initialHit || !this.requireInitial) {\n        dragging.setIgnoreMove(false);\n        // TODO: fire this before computing processFirstCoord, so listeners can cancel. this gets fired by almost every handler :(\n        this.emitter.trigger('pointerdown', ev);\n      } else {\n        dragging.setIgnoreMove(true);\n      }\n    };\n    this.handleDragStart = ev => {\n      this.emitter.trigger('dragstart', ev);\n      this.handleMove(ev, true); // force = fire even if initially null\n    };\n    this.handleDragMove = ev => {\n      this.emitter.trigger('dragmove', ev);\n      this.handleMove(ev);\n    };\n    this.handlePointerUp = ev => {\n      this.releaseHits();\n      this.emitter.trigger('pointerup', ev);\n    };\n    this.handleDragEnd = ev => {\n      if (this.movingHit) {\n        this.emitter.trigger('hitupdate', null, true, ev);\n      }\n      this.finalHit = this.movingHit;\n      this.movingHit = null;\n      this.emitter.trigger('dragend', ev);\n    };\n    this.droppableStore = droppableStore;\n    dragging.emitter.on('pointerdown', this.handlePointerDown);\n    dragging.emitter.on('dragstart', this.handleDragStart);\n    dragging.emitter.on('dragmove', this.handleDragMove);\n    dragging.emitter.on('pointerup', this.handlePointerUp);\n    dragging.emitter.on('dragend', this.handleDragEnd);\n    this.dragging = dragging;\n    this.emitter = new Emitter();\n  }\n  // sets initialHit\n  // sets coordAdjust\n  processFirstCoord(ev) {\n    let origPoint = {\n      left: ev.pageX,\n      top: ev.pageY\n    };\n    let adjustedPoint = origPoint;\n    let subjectEl = ev.subjectEl;\n    let subjectRect;\n    if (subjectEl instanceof HTMLElement) {\n      // i.e. not a Document/ShadowRoot\n      subjectRect = computeRect(subjectEl);\n      adjustedPoint = constrainPoint(adjustedPoint, subjectRect);\n    }\n    let initialHit = this.initialHit = this.queryHitForOffset(adjustedPoint.left, adjustedPoint.top);\n    if (initialHit) {\n      if (this.useSubjectCenter && subjectRect) {\n        let slicedSubjectRect = intersectRects(subjectRect, initialHit.rect);\n        if (slicedSubjectRect) {\n          adjustedPoint = getRectCenter(slicedSubjectRect);\n        }\n      }\n      this.coordAdjust = diffPoints(adjustedPoint, origPoint);\n    } else {\n      this.coordAdjust = {\n        left: 0,\n        top: 0\n      };\n    }\n  }\n  handleMove(ev, forceHandle) {\n    let hit = this.queryHitForOffset(ev.pageX + this.coordAdjust.left, ev.pageY + this.coordAdjust.top);\n    if (forceHandle || !isHitsEqual(this.movingHit, hit)) {\n      this.movingHit = hit;\n      this.emitter.trigger('hitupdate', hit, false, ev);\n    }\n  }\n  prepareHits() {\n    this.offsetTrackers = mapHash(this.droppableStore, interactionSettings => {\n      interactionSettings.component.prepareHits();\n      return new OffsetTracker(interactionSettings.el);\n    });\n  }\n  releaseHits() {\n    let {\n      offsetTrackers\n    } = this;\n    for (let id in offsetTrackers) {\n      offsetTrackers[id].destroy();\n    }\n    this.offsetTrackers = {};\n  }\n  queryHitForOffset(offsetLeft, offsetTop) {\n    let {\n      droppableStore,\n      offsetTrackers\n    } = this;\n    let bestHit = null;\n    for (let id in droppableStore) {\n      let component = droppableStore[id].component;\n      let offsetTracker = offsetTrackers[id];\n      if (offsetTracker &&\n      // wasn't destroyed mid-drag\n      offsetTracker.isWithinClipping(offsetLeft, offsetTop)) {\n        let originLeft = offsetTracker.computeLeft();\n        let originTop = offsetTracker.computeTop();\n        let positionLeft = offsetLeft - originLeft;\n        let positionTop = offsetTop - originTop;\n        let {\n          origRect\n        } = offsetTracker;\n        let width = origRect.right - origRect.left;\n        let height = origRect.bottom - origRect.top;\n        if (\n        // must be within the element's bounds\n        positionLeft >= 0 && positionLeft < width && positionTop >= 0 && positionTop < height) {\n          let hit = component.queryHit(positionLeft, positionTop, width, height);\n          if (hit &&\n          // make sure the hit is within activeRange, meaning it's not a dead cell\n          rangeContainsRange(hit.dateProfile.activeRange, hit.dateSpan.range) && (\n          // Ensure the component we are querying for the hit is accessibly my the pointer\n          // Prevents obscured calendars (ex: under a modal dialog) from accepting hit\n          // https://github.com/fullcalendar/fullcalendar/issues/5026\n          this.disablePointCheck || offsetTracker.el.contains(offsetTracker.el.getRootNode().elementFromPoint(\n          // add-back origins to get coordinate relative to top-left of window viewport\n          positionLeft + originLeft - window.scrollX, positionTop + originTop - window.scrollY))) && (!bestHit || hit.layer > bestHit.layer)) {\n            hit.componentId = id;\n            hit.context = component.context;\n            // TODO: better way to re-orient rectangle\n            hit.rect.left += originLeft;\n            hit.rect.right += originLeft;\n            hit.rect.top += originTop;\n            hit.rect.bottom += originTop;\n            bestHit = hit;\n          }\n        }\n      }\n    }\n    return bestHit;\n  }\n}\nfunction isHitsEqual(hit0, hit1) {\n  if (!hit0 && !hit1) {\n    return true;\n  }\n  if (Boolean(hit0) !== Boolean(hit1)) {\n    return false;\n  }\n  return isDateSpansEqual(hit0.dateSpan, hit1.dateSpan);\n}\nfunction buildDatePointApiWithContext(dateSpan, context) {\n  let props = {};\n  for (let transform of context.pluginHooks.datePointTransforms) {\n    Object.assign(props, transform(dateSpan, context));\n  }\n  Object.assign(props, buildDatePointApi(dateSpan, context.dateEnv));\n  return props;\n}\nfunction buildDatePointApi(span, dateEnv) {\n  return {\n    date: dateEnv.toDate(span.range.start),\n    dateStr: dateEnv.formatIso(span.range.start, {\n      omitTime: span.allDay\n    }),\n    allDay: span.allDay\n  };\n}\n\n/*\nMonitors when the user clicks on a specific date/time of a component.\nA pointerdown+pointerup on the same \"hit\" constitutes a click.\n*/\nclass DateClicking extends Interaction {\n  constructor(settings) {\n    super(settings);\n    this.handlePointerDown = pev => {\n      let {\n        dragging\n      } = this;\n      let downEl = pev.origEvent.target;\n      // do this in pointerdown (not dragend) because DOM might be mutated by the time dragend is fired\n      dragging.setIgnoreMove(!this.component.isValidDateDownEl(downEl));\n    };\n    // won't even fire if moving was ignored\n    this.handleDragEnd = ev => {\n      let {\n        component\n      } = this;\n      let {\n        pointer\n      } = this.dragging;\n      if (!pointer.wasTouchScroll) {\n        let {\n          initialHit,\n          finalHit\n        } = this.hitDragging;\n        if (initialHit && finalHit && isHitsEqual(initialHit, finalHit)) {\n          let {\n            context\n          } = component;\n          let arg = Object.assign(Object.assign({}, buildDatePointApiWithContext(initialHit.dateSpan, context)), {\n            dayEl: initialHit.dayEl,\n            jsEvent: ev.origEvent,\n            view: context.viewApi || context.calendarApi.view\n          });\n          context.emitter.trigger('dateClick', arg);\n        }\n      }\n    };\n    // we DO want to watch pointer moves because otherwise finalHit won't get populated\n    this.dragging = new FeaturefulElementDragging(settings.el);\n    this.dragging.autoScroller.isEnabled = false;\n    let hitDragging = this.hitDragging = new HitDragging(this.dragging, interactionSettingsToStore(settings));\n    hitDragging.emitter.on('pointerdown', this.handlePointerDown);\n    hitDragging.emitter.on('dragend', this.handleDragEnd);\n  }\n  destroy() {\n    this.dragging.destroy();\n  }\n}\n\n/*\nTracks when the user selects a portion of time of a component,\nconstituted by a drag over date cells, with a possible delay at the beginning of the drag.\n*/\nclass DateSelecting extends Interaction {\n  constructor(settings) {\n    super(settings);\n    this.dragSelection = null;\n    this.handlePointerDown = ev => {\n      let {\n        component,\n        dragging\n      } = this;\n      let {\n        options\n      } = component.context;\n      let canSelect = options.selectable && component.isValidDateDownEl(ev.origEvent.target);\n      // don't bother to watch expensive moves if component won't do selection\n      dragging.setIgnoreMove(!canSelect);\n      // if touch, require user to hold down\n      dragging.delay = ev.isTouch ? getComponentTouchDelay$1(component) : null;\n    };\n    this.handleDragStart = ev => {\n      this.component.context.calendarApi.unselect(ev); // unselect previous selections\n    };\n    this.handleHitUpdate = (hit, isFinal) => {\n      let {\n        context\n      } = this.component;\n      let dragSelection = null;\n      let isInvalid = false;\n      if (hit) {\n        let initialHit = this.hitDragging.initialHit;\n        let disallowed = hit.componentId === initialHit.componentId && this.isHitComboAllowed && !this.isHitComboAllowed(initialHit, hit);\n        if (!disallowed) {\n          dragSelection = joinHitsIntoSelection(initialHit, hit, context.pluginHooks.dateSelectionTransformers);\n        }\n        if (!dragSelection || !isDateSelectionValid(dragSelection, hit.dateProfile, context)) {\n          isInvalid = true;\n          dragSelection = null;\n        }\n      }\n      if (dragSelection) {\n        context.dispatch({\n          type: 'SELECT_DATES',\n          selection: dragSelection\n        });\n      } else if (!isFinal) {\n        // only unselect if moved away while dragging\n        context.dispatch({\n          type: 'UNSELECT_DATES'\n        });\n      }\n      if (!isInvalid) {\n        enableCursor();\n      } else {\n        disableCursor();\n      }\n      if (!isFinal) {\n        this.dragSelection = dragSelection; // only clear if moved away from all hits while dragging\n      }\n    };\n    this.handlePointerUp = pev => {\n      if (this.dragSelection) {\n        // selection is already rendered, so just need to report selection\n        triggerDateSelect(this.dragSelection, pev, this.component.context);\n        this.dragSelection = null;\n      }\n    };\n    let {\n      component\n    } = settings;\n    let {\n      options\n    } = component.context;\n    let dragging = this.dragging = new FeaturefulElementDragging(settings.el);\n    dragging.touchScrollAllowed = false;\n    dragging.minDistance = options.selectMinDistance || 0;\n    dragging.autoScroller.isEnabled = options.dragScroll;\n    let hitDragging = this.hitDragging = new HitDragging(this.dragging, interactionSettingsToStore(settings));\n    hitDragging.emitter.on('pointerdown', this.handlePointerDown);\n    hitDragging.emitter.on('dragstart', this.handleDragStart);\n    hitDragging.emitter.on('hitupdate', this.handleHitUpdate);\n    hitDragging.emitter.on('pointerup', this.handlePointerUp);\n  }\n  destroy() {\n    this.dragging.destroy();\n  }\n}\nfunction getComponentTouchDelay$1(component) {\n  let {\n    options\n  } = component.context;\n  let delay = options.selectLongPressDelay;\n  if (delay == null) {\n    delay = options.longPressDelay;\n  }\n  return delay;\n}\nfunction joinHitsIntoSelection(hit0, hit1, dateSelectionTransformers) {\n  let dateSpan0 = hit0.dateSpan;\n  let dateSpan1 = hit1.dateSpan;\n  let ms = [dateSpan0.range.start, dateSpan0.range.end, dateSpan1.range.start, dateSpan1.range.end];\n  ms.sort(compareNumbers);\n  let props = {};\n  for (let transformer of dateSelectionTransformers) {\n    let res = transformer(hit0, hit1);\n    if (res === false) {\n      return null;\n    }\n    if (res) {\n      Object.assign(props, res);\n    }\n  }\n  props.range = {\n    start: ms[0],\n    end: ms[3]\n  };\n  props.allDay = dateSpan0.allDay;\n  return props;\n}\nclass EventDragging extends Interaction {\n  constructor(settings) {\n    super(settings);\n    // internal state\n    this.subjectEl = null;\n    this.subjectSeg = null; // the seg being selected/dragged\n    this.isDragging = false;\n    this.eventRange = null;\n    this.relevantEvents = null; // the events being dragged\n    this.receivingContext = null;\n    this.validMutation = null;\n    this.mutatedRelevantEvents = null;\n    this.handlePointerDown = ev => {\n      let origTarget = ev.origEvent.target;\n      let {\n        component,\n        dragging\n      } = this;\n      let {\n        mirror\n      } = dragging;\n      let {\n        options\n      } = component.context;\n      let initialContext = component.context;\n      this.subjectEl = ev.subjectEl;\n      let subjectSeg = this.subjectSeg = getElSeg(ev.subjectEl);\n      let eventRange = this.eventRange = subjectSeg.eventRange;\n      let eventInstanceId = eventRange.instance.instanceId;\n      this.relevantEvents = getRelevantEvents(initialContext.getCurrentData().eventStore, eventInstanceId);\n      dragging.minDistance = ev.isTouch ? 0 : options.eventDragMinDistance;\n      dragging.delay =\n      // only do a touch delay if touch and this event hasn't been selected yet\n      ev.isTouch && eventInstanceId !== component.props.eventSelection ? getComponentTouchDelay(component) : null;\n      if (options.fixedMirrorParent) {\n        mirror.parentNode = options.fixedMirrorParent;\n      } else {\n        mirror.parentNode = elementClosest(origTarget, '.fc');\n      }\n      mirror.revertDuration = options.dragRevertDuration;\n      let isValid = component.isValidSegDownEl(origTarget) && !elementClosest(origTarget, '.fc-event-resizer'); // NOT on a resizer\n      dragging.setIgnoreMove(!isValid);\n      // disable dragging for elements that are resizable (ie, selectable)\n      // but are not draggable\n      this.isDragging = isValid && ev.subjectEl.classList.contains('fc-event-draggable');\n    };\n    this.handleDragStart = ev => {\n      let initialContext = this.component.context;\n      let eventRange = this.eventRange;\n      let eventInstanceId = eventRange.instance.instanceId;\n      if (ev.isTouch) {\n        // need to select a different event?\n        if (eventInstanceId !== this.component.props.eventSelection) {\n          initialContext.dispatch({\n            type: 'SELECT_EVENT',\n            eventInstanceId\n          });\n        }\n      } else {\n        // if now using mouse, but was previous touch interaction, clear selected event\n        initialContext.dispatch({\n          type: 'UNSELECT_EVENT'\n        });\n      }\n      if (this.isDragging) {\n        initialContext.calendarApi.unselect(ev); // unselect *date* selection\n        initialContext.emitter.trigger('eventDragStart', {\n          el: this.subjectEl,\n          event: new EventImpl(initialContext, eventRange.def, eventRange.instance),\n          jsEvent: ev.origEvent,\n          view: initialContext.viewApi\n        });\n      }\n    };\n    this.handleHitUpdate = (hit, isFinal) => {\n      if (!this.isDragging) {\n        return;\n      }\n      let relevantEvents = this.relevantEvents;\n      let initialHit = this.hitDragging.initialHit;\n      let initialContext = this.component.context;\n      // states based on new hit\n      let receivingContext = null;\n      let mutation = null;\n      let mutatedRelevantEvents = null;\n      let isInvalid = false;\n      let interaction = {\n        affectedEvents: relevantEvents,\n        mutatedEvents: createEmptyEventStore(),\n        isEvent: true\n      };\n      if (hit) {\n        receivingContext = hit.context;\n        let receivingOptions = receivingContext.options;\n        if (initialContext === receivingContext || receivingOptions.editable && receivingOptions.droppable) {\n          mutation = computeEventMutation(initialHit, hit, this.eventRange.instance.range.start, receivingContext.getCurrentData().pluginHooks.eventDragMutationMassagers);\n          if (mutation) {\n            mutatedRelevantEvents = applyMutationToEventStore(relevantEvents, receivingContext.getCurrentData().eventUiBases, mutation, receivingContext);\n            interaction.mutatedEvents = mutatedRelevantEvents;\n            if (!isInteractionValid(interaction, hit.dateProfile, receivingContext)) {\n              isInvalid = true;\n              mutation = null;\n              mutatedRelevantEvents = null;\n              interaction.mutatedEvents = createEmptyEventStore();\n            }\n          }\n        } else {\n          receivingContext = null;\n        }\n      }\n      this.displayDrag(receivingContext, interaction);\n      if (!isInvalid) {\n        enableCursor();\n      } else {\n        disableCursor();\n      }\n      if (!isFinal) {\n        if (initialContext === receivingContext &&\n        // TODO: write test for this\n        isHitsEqual(initialHit, hit)) {\n          mutation = null;\n        }\n        this.dragging.setMirrorNeedsRevert(!mutation);\n        // render the mirror if no already-rendered mirror\n        // TODO: wish we could somehow wait for dispatch to guarantee render\n        this.dragging.setMirrorIsVisible(!hit || !this.subjectEl.getRootNode().querySelector('.fc-event-mirror'));\n        // assign states based on new hit\n        this.receivingContext = receivingContext;\n        this.validMutation = mutation;\n        this.mutatedRelevantEvents = mutatedRelevantEvents;\n      }\n    };\n    this.handlePointerUp = () => {\n      if (!this.isDragging) {\n        this.cleanup(); // because handleDragEnd won't fire\n      }\n    };\n    this.handleDragEnd = ev => {\n      if (this.isDragging) {\n        let initialContext = this.component.context;\n        let initialView = initialContext.viewApi;\n        let {\n          receivingContext,\n          validMutation\n        } = this;\n        let eventDef = this.eventRange.def;\n        let eventInstance = this.eventRange.instance;\n        let eventApi = new EventImpl(initialContext, eventDef, eventInstance);\n        let relevantEvents = this.relevantEvents;\n        let mutatedRelevantEvents = this.mutatedRelevantEvents;\n        let {\n          finalHit\n        } = this.hitDragging;\n        this.clearDrag(); // must happen after revert animation\n        initialContext.emitter.trigger('eventDragStop', {\n          el: this.subjectEl,\n          event: eventApi,\n          jsEvent: ev.origEvent,\n          view: initialView\n        });\n        if (validMutation) {\n          // dropped within same calendar\n          if (receivingContext === initialContext) {\n            let updatedEventApi = new EventImpl(initialContext, mutatedRelevantEvents.defs[eventDef.defId], eventInstance ? mutatedRelevantEvents.instances[eventInstance.instanceId] : null);\n            initialContext.dispatch({\n              type: 'MERGE_EVENTS',\n              eventStore: mutatedRelevantEvents\n            });\n            let eventChangeArg = {\n              oldEvent: eventApi,\n              event: updatedEventApi,\n              relatedEvents: buildEventApis(mutatedRelevantEvents, initialContext, eventInstance),\n              revert() {\n                initialContext.dispatch({\n                  type: 'MERGE_EVENTS',\n                  eventStore: relevantEvents // the pre-change data\n                });\n              }\n            };\n            let transformed = {};\n            for (let transformer of initialContext.getCurrentData().pluginHooks.eventDropTransformers) {\n              Object.assign(transformed, transformer(validMutation, initialContext));\n            }\n            initialContext.emitter.trigger('eventDrop', Object.assign(Object.assign(Object.assign({}, eventChangeArg), transformed), {\n              el: ev.subjectEl,\n              delta: validMutation.datesDelta,\n              jsEvent: ev.origEvent,\n              view: initialView\n            }));\n            initialContext.emitter.trigger('eventChange', eventChangeArg);\n            // dropped in different calendar\n          } else if (receivingContext) {\n            let eventRemoveArg = {\n              event: eventApi,\n              relatedEvents: buildEventApis(relevantEvents, initialContext, eventInstance),\n              revert() {\n                initialContext.dispatch({\n                  type: 'MERGE_EVENTS',\n                  eventStore: relevantEvents\n                });\n              }\n            };\n            initialContext.emitter.trigger('eventLeave', Object.assign(Object.assign({}, eventRemoveArg), {\n              draggedEl: ev.subjectEl,\n              view: initialView\n            }));\n            initialContext.dispatch({\n              type: 'REMOVE_EVENTS',\n              eventStore: relevantEvents\n            });\n            initialContext.emitter.trigger('eventRemove', eventRemoveArg);\n            let addedEventDef = mutatedRelevantEvents.defs[eventDef.defId];\n            let addedEventInstance = mutatedRelevantEvents.instances[eventInstance.instanceId];\n            let addedEventApi = new EventImpl(receivingContext, addedEventDef, addedEventInstance);\n            receivingContext.dispatch({\n              type: 'MERGE_EVENTS',\n              eventStore: mutatedRelevantEvents\n            });\n            let eventAddArg = {\n              event: addedEventApi,\n              relatedEvents: buildEventApis(mutatedRelevantEvents, receivingContext, addedEventInstance),\n              revert() {\n                receivingContext.dispatch({\n                  type: 'REMOVE_EVENTS',\n                  eventStore: mutatedRelevantEvents\n                });\n              }\n            };\n            receivingContext.emitter.trigger('eventAdd', eventAddArg);\n            if (ev.isTouch) {\n              receivingContext.dispatch({\n                type: 'SELECT_EVENT',\n                eventInstanceId: eventInstance.instanceId\n              });\n            }\n            receivingContext.emitter.trigger('drop', Object.assign(Object.assign({}, buildDatePointApiWithContext(finalHit.dateSpan, receivingContext)), {\n              draggedEl: ev.subjectEl,\n              jsEvent: ev.origEvent,\n              view: finalHit.context.viewApi\n            }));\n            receivingContext.emitter.trigger('eventReceive', Object.assign(Object.assign({}, eventAddArg), {\n              draggedEl: ev.subjectEl,\n              view: finalHit.context.viewApi\n            }));\n          }\n        } else {\n          initialContext.emitter.trigger('_noEventDrop');\n        }\n      }\n      this.cleanup();\n    };\n    let {\n      component\n    } = this;\n    let {\n      options\n    } = component.context;\n    let dragging = this.dragging = new FeaturefulElementDragging(settings.el);\n    dragging.pointer.selector = EventDragging.SELECTOR;\n    dragging.touchScrollAllowed = false;\n    dragging.autoScroller.isEnabled = options.dragScroll;\n    let hitDragging = this.hitDragging = new HitDragging(this.dragging, interactionSettingsStore);\n    hitDragging.useSubjectCenter = settings.useEventCenter;\n    hitDragging.emitter.on('pointerdown', this.handlePointerDown);\n    hitDragging.emitter.on('dragstart', this.handleDragStart);\n    hitDragging.emitter.on('hitupdate', this.handleHitUpdate);\n    hitDragging.emitter.on('pointerup', this.handlePointerUp);\n    hitDragging.emitter.on('dragend', this.handleDragEnd);\n  }\n  destroy() {\n    this.dragging.destroy();\n  }\n  // render a drag state on the next receivingCalendar\n  displayDrag(nextContext, state) {\n    let initialContext = this.component.context;\n    let prevContext = this.receivingContext;\n    // does the previous calendar need to be cleared?\n    if (prevContext && prevContext !== nextContext) {\n      // does the initial calendar need to be cleared?\n      // if so, don't clear all the way. we still need to to hide the affectedEvents\n      if (prevContext === initialContext) {\n        prevContext.dispatch({\n          type: 'SET_EVENT_DRAG',\n          state: {\n            affectedEvents: state.affectedEvents,\n            mutatedEvents: createEmptyEventStore(),\n            isEvent: true\n          }\n        });\n        // completely clear the old calendar if it wasn't the initial\n      } else {\n        prevContext.dispatch({\n          type: 'UNSET_EVENT_DRAG'\n        });\n      }\n    }\n    if (nextContext) {\n      nextContext.dispatch({\n        type: 'SET_EVENT_DRAG',\n        state\n      });\n    }\n  }\n  clearDrag() {\n    let initialCalendar = this.component.context;\n    let {\n      receivingContext\n    } = this;\n    if (receivingContext) {\n      receivingContext.dispatch({\n        type: 'UNSET_EVENT_DRAG'\n      });\n    }\n    // the initial calendar might have an dummy drag state from displayDrag\n    if (initialCalendar !== receivingContext) {\n      initialCalendar.dispatch({\n        type: 'UNSET_EVENT_DRAG'\n      });\n    }\n  }\n  cleanup() {\n    this.subjectSeg = null;\n    this.isDragging = false;\n    this.eventRange = null;\n    this.relevantEvents = null;\n    this.receivingContext = null;\n    this.validMutation = null;\n    this.mutatedRelevantEvents = null;\n  }\n}\n// TODO: test this in IE11\n// QUESTION: why do we need it on the resizable???\nEventDragging.SELECTOR = '.fc-event-draggable, .fc-event-resizable';\nfunction computeEventMutation(hit0, hit1, eventInstanceStart, massagers) {\n  let dateSpan0 = hit0.dateSpan;\n  let dateSpan1 = hit1.dateSpan;\n  let date0 = dateSpan0.range.start;\n  let date1 = dateSpan1.range.start;\n  let standardProps = {};\n  if (dateSpan0.allDay !== dateSpan1.allDay) {\n    standardProps.allDay = dateSpan1.allDay;\n    standardProps.hasEnd = hit1.context.options.allDayMaintainDuration;\n    if (dateSpan1.allDay) {\n      // means date1 is already start-of-day,\n      // but date0 needs to be converted\n      date0 = startOfDay(eventInstanceStart);\n    } else {\n      // Moving from allDate->timed\n      // Doesn't matter where on the event the drag began, mutate the event's start-date to date1\n      date0 = eventInstanceStart;\n    }\n  }\n  let delta = diffDates(date0, date1, hit0.context.dateEnv, hit0.componentId === hit1.componentId ? hit0.largeUnit : null);\n  if (delta.milliseconds) {\n    // has hours/minutes/seconds\n    standardProps.allDay = false;\n  }\n  let mutation = {\n    datesDelta: delta,\n    standardProps\n  };\n  for (let massager of massagers) {\n    massager(mutation, hit0, hit1);\n  }\n  return mutation;\n}\nfunction getComponentTouchDelay(component) {\n  let {\n    options\n  } = component.context;\n  let delay = options.eventLongPressDelay;\n  if (delay == null) {\n    delay = options.longPressDelay;\n  }\n  return delay;\n}\nclass EventResizing extends Interaction {\n  constructor(settings) {\n    super(settings);\n    // internal state\n    this.draggingSegEl = null;\n    this.draggingSeg = null; // TODO: rename to resizingSeg? subjectSeg?\n    this.eventRange = null;\n    this.relevantEvents = null;\n    this.validMutation = null;\n    this.mutatedRelevantEvents = null;\n    this.handlePointerDown = ev => {\n      let {\n        component\n      } = this;\n      let segEl = this.querySegEl(ev);\n      let seg = getElSeg(segEl);\n      let eventRange = this.eventRange = seg.eventRange;\n      this.dragging.minDistance = component.context.options.eventDragMinDistance;\n      // if touch, need to be working with a selected event\n      this.dragging.setIgnoreMove(!this.component.isValidSegDownEl(ev.origEvent.target) || ev.isTouch && this.component.props.eventSelection !== eventRange.instance.instanceId);\n    };\n    this.handleDragStart = ev => {\n      let {\n        context\n      } = this.component;\n      let eventRange = this.eventRange;\n      this.relevantEvents = getRelevantEvents(context.getCurrentData().eventStore, this.eventRange.instance.instanceId);\n      let segEl = this.querySegEl(ev);\n      this.draggingSegEl = segEl;\n      this.draggingSeg = getElSeg(segEl);\n      context.calendarApi.unselect();\n      context.emitter.trigger('eventResizeStart', {\n        el: segEl,\n        event: new EventImpl(context, eventRange.def, eventRange.instance),\n        jsEvent: ev.origEvent,\n        view: context.viewApi\n      });\n    };\n    this.handleHitUpdate = (hit, isFinal, ev) => {\n      let {\n        context\n      } = this.component;\n      let relevantEvents = this.relevantEvents;\n      let initialHit = this.hitDragging.initialHit;\n      let eventInstance = this.eventRange.instance;\n      let mutation = null;\n      let mutatedRelevantEvents = null;\n      let isInvalid = false;\n      let interaction = {\n        affectedEvents: relevantEvents,\n        mutatedEvents: createEmptyEventStore(),\n        isEvent: true\n      };\n      if (hit) {\n        let disallowed = hit.componentId === initialHit.componentId && this.isHitComboAllowed && !this.isHitComboAllowed(initialHit, hit);\n        if (!disallowed) {\n          mutation = computeMutation(initialHit, hit, ev.subjectEl.classList.contains('fc-event-resizer-start'), eventInstance.range);\n        }\n      }\n      if (mutation) {\n        mutatedRelevantEvents = applyMutationToEventStore(relevantEvents, context.getCurrentData().eventUiBases, mutation, context);\n        interaction.mutatedEvents = mutatedRelevantEvents;\n        if (!isInteractionValid(interaction, hit.dateProfile, context)) {\n          isInvalid = true;\n          mutation = null;\n          mutatedRelevantEvents = null;\n          interaction.mutatedEvents = null;\n        }\n      }\n      if (mutatedRelevantEvents) {\n        context.dispatch({\n          type: 'SET_EVENT_RESIZE',\n          state: interaction\n        });\n      } else {\n        context.dispatch({\n          type: 'UNSET_EVENT_RESIZE'\n        });\n      }\n      if (!isInvalid) {\n        enableCursor();\n      } else {\n        disableCursor();\n      }\n      if (!isFinal) {\n        if (mutation && isHitsEqual(initialHit, hit)) {\n          mutation = null;\n        }\n        this.validMutation = mutation;\n        this.mutatedRelevantEvents = mutatedRelevantEvents;\n      }\n    };\n    this.handleDragEnd = ev => {\n      let {\n        context\n      } = this.component;\n      let eventDef = this.eventRange.def;\n      let eventInstance = this.eventRange.instance;\n      let eventApi = new EventImpl(context, eventDef, eventInstance);\n      let relevantEvents = this.relevantEvents;\n      let mutatedRelevantEvents = this.mutatedRelevantEvents;\n      context.emitter.trigger('eventResizeStop', {\n        el: this.draggingSegEl,\n        event: eventApi,\n        jsEvent: ev.origEvent,\n        view: context.viewApi\n      });\n      if (this.validMutation) {\n        let updatedEventApi = new EventImpl(context, mutatedRelevantEvents.defs[eventDef.defId], eventInstance ? mutatedRelevantEvents.instances[eventInstance.instanceId] : null);\n        context.dispatch({\n          type: 'MERGE_EVENTS',\n          eventStore: mutatedRelevantEvents\n        });\n        let eventChangeArg = {\n          oldEvent: eventApi,\n          event: updatedEventApi,\n          relatedEvents: buildEventApis(mutatedRelevantEvents, context, eventInstance),\n          revert() {\n            context.dispatch({\n              type: 'MERGE_EVENTS',\n              eventStore: relevantEvents // the pre-change events\n            });\n          }\n        };\n        context.emitter.trigger('eventResize', Object.assign(Object.assign({}, eventChangeArg), {\n          el: this.draggingSegEl,\n          startDelta: this.validMutation.startDelta || createDuration(0),\n          endDelta: this.validMutation.endDelta || createDuration(0),\n          jsEvent: ev.origEvent,\n          view: context.viewApi\n        }));\n        context.emitter.trigger('eventChange', eventChangeArg);\n      } else {\n        context.emitter.trigger('_noEventResize');\n      }\n      // reset all internal state\n      this.draggingSeg = null;\n      this.relevantEvents = null;\n      this.validMutation = null;\n      // okay to keep eventInstance around. useful to set it in handlePointerDown\n    };\n    let {\n      component\n    } = settings;\n    let dragging = this.dragging = new FeaturefulElementDragging(settings.el);\n    dragging.pointer.selector = '.fc-event-resizer';\n    dragging.touchScrollAllowed = false;\n    dragging.autoScroller.isEnabled = component.context.options.dragScroll;\n    let hitDragging = this.hitDragging = new HitDragging(this.dragging, interactionSettingsToStore(settings));\n    hitDragging.emitter.on('pointerdown', this.handlePointerDown);\n    hitDragging.emitter.on('dragstart', this.handleDragStart);\n    hitDragging.emitter.on('hitupdate', this.handleHitUpdate);\n    hitDragging.emitter.on('dragend', this.handleDragEnd);\n  }\n  destroy() {\n    this.dragging.destroy();\n  }\n  querySegEl(ev) {\n    return elementClosest(ev.subjectEl, '.fc-event');\n  }\n}\nfunction computeMutation(hit0, hit1, isFromStart, instanceRange) {\n  let dateEnv = hit0.context.dateEnv;\n  let date0 = hit0.dateSpan.range.start;\n  let date1 = hit1.dateSpan.range.start;\n  let delta = diffDates(date0, date1, dateEnv, hit0.largeUnit);\n  if (isFromStart) {\n    if (dateEnv.add(instanceRange.start, delta) < instanceRange.end) {\n      return {\n        startDelta: delta\n      };\n    }\n  } else if (dateEnv.add(instanceRange.end, delta) > instanceRange.start) {\n    return {\n      endDelta: delta\n    };\n  }\n  return null;\n}\nclass UnselectAuto {\n  constructor(context) {\n    this.context = context;\n    this.isRecentPointerDateSelect = false; // wish we could use a selector to detect date selection, but uses hit system\n    this.matchesCancel = false;\n    this.matchesEvent = false;\n    this.onSelect = selectInfo => {\n      if (selectInfo.jsEvent) {\n        this.isRecentPointerDateSelect = true;\n      }\n    };\n    this.onDocumentPointerDown = pev => {\n      let unselectCancel = this.context.options.unselectCancel;\n      let downEl = getEventTargetViaRoot(pev.origEvent);\n      this.matchesCancel = !!elementClosest(downEl, unselectCancel);\n      this.matchesEvent = !!elementClosest(downEl, EventDragging.SELECTOR); // interaction started on an event?\n    };\n    this.onDocumentPointerUp = pev => {\n      let {\n        context\n      } = this;\n      let {\n        documentPointer\n      } = this;\n      let calendarState = context.getCurrentData();\n      // touch-scrolling should never unfocus any type of selection\n      if (!documentPointer.wasTouchScroll) {\n        if (calendarState.dateSelection &&\n        // an existing date selection?\n        !this.isRecentPointerDateSelect // a new pointer-initiated date selection since last onDocumentPointerUp?\n        ) {\n          let unselectAuto = context.options.unselectAuto;\n          if (unselectAuto && (!unselectAuto || !this.matchesCancel)) {\n            context.calendarApi.unselect(pev);\n          }\n        }\n        if (calendarState.eventSelection &&\n        // an existing event selected?\n        !this.matchesEvent // interaction DIDN'T start on an event\n        ) {\n          context.dispatch({\n            type: 'UNSELECT_EVENT'\n          });\n        }\n      }\n      this.isRecentPointerDateSelect = false;\n    };\n    let documentPointer = this.documentPointer = new PointerDragging(document);\n    documentPointer.shouldIgnoreMove = true;\n    documentPointer.shouldWatchScroll = false;\n    documentPointer.emitter.on('pointerdown', this.onDocumentPointerDown);\n    documentPointer.emitter.on('pointerup', this.onDocumentPointerUp);\n    /*\n    TODO: better way to know about whether there was a selection with the pointer\n    */\n    context.emitter.on('select', this.onSelect);\n  }\n  destroy() {\n    this.context.emitter.off('select', this.onSelect);\n    this.documentPointer.destroy();\n  }\n}\nconst OPTION_REFINERS = {\n  fixedMirrorParent: identity\n};\nconst LISTENER_REFINERS = {\n  dateClick: identity,\n  eventDragStart: identity,\n  eventDragStop: identity,\n  eventDrop: identity,\n  eventResizeStart: identity,\n  eventResizeStop: identity,\n  eventResize: identity,\n  drop: identity,\n  eventReceive: identity,\n  eventLeave: identity\n};\n\n/*\nGiven an already instantiated draggable object for one-or-more elements,\nInterprets any dragging as an attempt to drag an events that lives outside\nof a calendar onto a calendar.\n*/\nclass ExternalElementDragging {\n  constructor(dragging, suppliedDragMeta) {\n    this.receivingContext = null;\n    this.droppableEvent = null; // will exist for all drags, even if create:false\n    this.suppliedDragMeta = null;\n    this.dragMeta = null;\n    this.handleDragStart = ev => {\n      this.dragMeta = this.buildDragMeta(ev.subjectEl);\n    };\n    this.handleHitUpdate = (hit, isFinal, ev) => {\n      let {\n        dragging\n      } = this.hitDragging;\n      let receivingContext = null;\n      let droppableEvent = null;\n      let isInvalid = false;\n      let interaction = {\n        affectedEvents: createEmptyEventStore(),\n        mutatedEvents: createEmptyEventStore(),\n        isEvent: this.dragMeta.create\n      };\n      if (hit) {\n        receivingContext = hit.context;\n        if (this.canDropElOnCalendar(ev.subjectEl, receivingContext)) {\n          droppableEvent = computeEventForDateSpan(hit.dateSpan, this.dragMeta, receivingContext);\n          interaction.mutatedEvents = eventTupleToStore(droppableEvent);\n          isInvalid = !isInteractionValid(interaction, hit.dateProfile, receivingContext);\n          if (isInvalid) {\n            interaction.mutatedEvents = createEmptyEventStore();\n            droppableEvent = null;\n          }\n        }\n      }\n      this.displayDrag(receivingContext, interaction);\n      // show mirror if no already-rendered mirror element OR if we are shutting down the mirror (?)\n      // TODO: wish we could somehow wait for dispatch to guarantee render\n      dragging.setMirrorIsVisible(isFinal || !droppableEvent || !document.querySelector('.fc-event-mirror'));\n      if (!isInvalid) {\n        enableCursor();\n      } else {\n        disableCursor();\n      }\n      if (!isFinal) {\n        dragging.setMirrorNeedsRevert(!droppableEvent);\n        this.receivingContext = receivingContext;\n        this.droppableEvent = droppableEvent;\n      }\n    };\n    this.handleDragEnd = pev => {\n      let {\n        receivingContext,\n        droppableEvent\n      } = this;\n      this.clearDrag();\n      if (receivingContext && droppableEvent) {\n        let finalHit = this.hitDragging.finalHit;\n        let finalView = finalHit.context.viewApi;\n        let dragMeta = this.dragMeta;\n        receivingContext.emitter.trigger('drop', Object.assign(Object.assign({}, buildDatePointApiWithContext(finalHit.dateSpan, receivingContext)), {\n          draggedEl: pev.subjectEl,\n          jsEvent: pev.origEvent,\n          view: finalView\n        }));\n        if (dragMeta.create) {\n          let addingEvents = eventTupleToStore(droppableEvent);\n          receivingContext.dispatch({\n            type: 'MERGE_EVENTS',\n            eventStore: addingEvents\n          });\n          if (pev.isTouch) {\n            receivingContext.dispatch({\n              type: 'SELECT_EVENT',\n              eventInstanceId: droppableEvent.instance.instanceId\n            });\n          }\n          // signal that an external event landed\n          receivingContext.emitter.trigger('eventReceive', {\n            event: new EventImpl(receivingContext, droppableEvent.def, droppableEvent.instance),\n            relatedEvents: [],\n            revert() {\n              receivingContext.dispatch({\n                type: 'REMOVE_EVENTS',\n                eventStore: addingEvents\n              });\n            },\n            draggedEl: pev.subjectEl,\n            view: finalView\n          });\n        }\n      }\n      this.receivingContext = null;\n      this.droppableEvent = null;\n    };\n    let hitDragging = this.hitDragging = new HitDragging(dragging, interactionSettingsStore);\n    hitDragging.requireInitial = false; // will start outside of a component\n    hitDragging.emitter.on('dragstart', this.handleDragStart);\n    hitDragging.emitter.on('hitupdate', this.handleHitUpdate);\n    hitDragging.emitter.on('dragend', this.handleDragEnd);\n    this.suppliedDragMeta = suppliedDragMeta;\n  }\n  buildDragMeta(subjectEl) {\n    if (typeof this.suppliedDragMeta === 'object') {\n      return parseDragMeta(this.suppliedDragMeta);\n    }\n    if (typeof this.suppliedDragMeta === 'function') {\n      return parseDragMeta(this.suppliedDragMeta(subjectEl));\n    }\n    return getDragMetaFromEl(subjectEl);\n  }\n  displayDrag(nextContext, state) {\n    let prevContext = this.receivingContext;\n    if (prevContext && prevContext !== nextContext) {\n      prevContext.dispatch({\n        type: 'UNSET_EVENT_DRAG'\n      });\n    }\n    if (nextContext) {\n      nextContext.dispatch({\n        type: 'SET_EVENT_DRAG',\n        state\n      });\n    }\n  }\n  clearDrag() {\n    if (this.receivingContext) {\n      this.receivingContext.dispatch({\n        type: 'UNSET_EVENT_DRAG'\n      });\n    }\n  }\n  canDropElOnCalendar(el, receivingContext) {\n    let dropAccept = receivingContext.options.dropAccept;\n    if (typeof dropAccept === 'function') {\n      return dropAccept.call(receivingContext.calendarApi, el);\n    }\n    if (typeof dropAccept === 'string' && dropAccept) {\n      return Boolean(elementMatches(el, dropAccept));\n    }\n    return true;\n  }\n}\n// Utils for computing event store from the DragMeta\n// ----------------------------------------------------------------------------------------------------\nfunction computeEventForDateSpan(dateSpan, dragMeta, context) {\n  let defProps = Object.assign({}, dragMeta.leftoverProps);\n  for (let transform of context.pluginHooks.externalDefTransforms) {\n    Object.assign(defProps, transform(dateSpan, dragMeta));\n  }\n  let {\n    refined,\n    extra\n  } = refineEventDef(defProps, context);\n  let def = parseEventDef(refined, extra, dragMeta.sourceId, dateSpan.allDay, context.options.forceEventDuration || Boolean(dragMeta.duration),\n  // hasEnd\n  context);\n  let start = dateSpan.range.start;\n  // only rely on time info if drop zone is all-day,\n  // otherwise, we already know the time\n  if (dateSpan.allDay && dragMeta.startTime) {\n    start = context.dateEnv.add(start, dragMeta.startTime);\n  }\n  let end = dragMeta.duration ? context.dateEnv.add(start, dragMeta.duration) : getDefaultEventEnd(dateSpan.allDay, start, context);\n  let instance = createEventInstance(def.defId, {\n    start,\n    end\n  });\n  return {\n    def,\n    instance\n  };\n}\n// Utils for extracting data from element\n// ----------------------------------------------------------------------------------------------------\nfunction getDragMetaFromEl(el) {\n  let str = getEmbeddedElData(el, 'event');\n  let obj = str ? JSON.parse(str) : {\n    create: false\n  }; // if no embedded data, assume no event creation\n  return parseDragMeta(obj);\n}\nconfig.dataAttrPrefix = '';\nfunction getEmbeddedElData(el, name) {\n  let prefix = config.dataAttrPrefix;\n  let prefixedName = (prefix ? prefix + '-' : '') + name;\n  return el.getAttribute('data-' + prefixedName) || '';\n}\n\n/*\nMakes an element (that is *external* to any calendar) draggable.\nCan pass in data that determines how an event will be created when dropped onto a calendar.\nLeverages FullCalendar's internal drag-n-drop functionality WITHOUT a third-party drag system.\n*/\nclass ExternalDraggable {\n  constructor(el) {\n    let settings = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n    this.handlePointerDown = ev => {\n      let {\n        dragging\n      } = this;\n      let {\n        minDistance,\n        longPressDelay\n      } = this.settings;\n      dragging.minDistance = minDistance != null ? minDistance : ev.isTouch ? 0 : BASE_OPTION_DEFAULTS.eventDragMinDistance;\n      dragging.delay = ev.isTouch ?\n      // TODO: eventually read eventLongPressDelay instead vvv\n      longPressDelay != null ? longPressDelay : BASE_OPTION_DEFAULTS.longPressDelay : 0;\n    };\n    this.handleDragStart = ev => {\n      if (ev.isTouch && this.dragging.delay && ev.subjectEl.classList.contains('fc-event')) {\n        this.dragging.mirror.getMirrorEl().classList.add('fc-event-selected');\n      }\n    };\n    this.settings = settings;\n    let dragging = this.dragging = new FeaturefulElementDragging(el);\n    dragging.touchScrollAllowed = false;\n    if (settings.itemSelector != null) {\n      dragging.pointer.selector = settings.itemSelector;\n    }\n    if (settings.appendTo != null) {\n      dragging.mirror.parentNode = settings.appendTo; // TODO: write tests\n    }\n    dragging.emitter.on('pointerdown', this.handlePointerDown);\n    dragging.emitter.on('dragstart', this.handleDragStart);\n    new ExternalElementDragging(dragging, settings.eventData); // eslint-disable-line no-new\n  }\n  destroy() {\n    this.dragging.destroy();\n  }\n}\n\n/*\nDetects when a *THIRD-PARTY* drag-n-drop system interacts with elements.\nThe third-party system is responsible for drawing the visuals effects of the drag.\nThis class simply monitors for pointer movements and fires events.\nIt also has the ability to hide the moving element (the \"mirror\") during the drag.\n*/\nclass InferredElementDragging extends ElementDragging {\n  constructor(containerEl) {\n    super(containerEl);\n    this.shouldIgnoreMove = false;\n    this.mirrorSelector = '';\n    this.currentMirrorEl = null;\n    this.handlePointerDown = ev => {\n      this.emitter.trigger('pointerdown', ev);\n      if (!this.shouldIgnoreMove) {\n        // fire dragstart right away. does not support delay or min-distance\n        this.emitter.trigger('dragstart', ev);\n      }\n    };\n    this.handlePointerMove = ev => {\n      if (!this.shouldIgnoreMove) {\n        this.emitter.trigger('dragmove', ev);\n      }\n    };\n    this.handlePointerUp = ev => {\n      this.emitter.trigger('pointerup', ev);\n      if (!this.shouldIgnoreMove) {\n        // fire dragend right away. does not support a revert animation\n        this.emitter.trigger('dragend', ev);\n      }\n    };\n    let pointer = this.pointer = new PointerDragging(containerEl);\n    pointer.emitter.on('pointerdown', this.handlePointerDown);\n    pointer.emitter.on('pointermove', this.handlePointerMove);\n    pointer.emitter.on('pointerup', this.handlePointerUp);\n  }\n  destroy() {\n    this.pointer.destroy();\n  }\n  setIgnoreMove(bool) {\n    this.shouldIgnoreMove = bool;\n  }\n  setMirrorIsVisible(bool) {\n    if (bool) {\n      // restore a previously hidden element.\n      // use the reference in case the selector class has already been removed.\n      if (this.currentMirrorEl) {\n        this.currentMirrorEl.style.visibility = '';\n        this.currentMirrorEl = null;\n      }\n    } else {\n      let mirrorEl = this.mirrorSelector\n      // TODO: somehow query FullCalendars WITHIN shadow-roots\n      ? document.querySelector(this.mirrorSelector) : null;\n      if (mirrorEl) {\n        this.currentMirrorEl = mirrorEl;\n        mirrorEl.style.visibility = 'hidden';\n      }\n    }\n  }\n}\n\n/*\nBridges third-party drag-n-drop systems with FullCalendar.\nMust be instantiated and destroyed by caller.\n*/\nclass ThirdPartyDraggable {\n  constructor(containerOrSettings, settings) {\n    let containerEl = document;\n    if (\n    // wish we could just test instanceof EventTarget, but doesn't work in IE11\n    containerOrSettings === document || containerOrSettings instanceof Element) {\n      containerEl = containerOrSettings;\n      settings = settings || {};\n    } else {\n      settings = containerOrSettings || {};\n    }\n    let dragging = this.dragging = new InferredElementDragging(containerEl);\n    if (typeof settings.itemSelector === 'string') {\n      dragging.pointer.selector = settings.itemSelector;\n    } else if (containerEl === document) {\n      dragging.pointer.selector = '[data-event]';\n    }\n    if (typeof settings.mirrorSelector === 'string') {\n      dragging.mirrorSelector = settings.mirrorSelector;\n    }\n    let externalDragging = new ExternalElementDragging(dragging, settings.eventData);\n    // The hit-detection system requires that the dnd-mirror-element be pointer-events:none,\n    // but this can't be guaranteed for third-party draggables, so disable\n    externalDragging.hitDragging.disablePointCheck = true;\n  }\n  destroy() {\n    this.dragging.destroy();\n  }\n}\nvar index = createPlugin({\n  name: '@fullcalendar/interaction',\n  componentInteractions: [DateClicking, DateSelecting, EventDragging, EventResizing],\n  calendarInteractions: [UnselectAuto],\n  elementDraggingImpl: FeaturefulElementDragging,\n  optionRefiners: OPTION_REFINERS,\n  listenerRefiners: LISTENER_REFINERS\n});\nexport { ExternalDraggable as Draggable, ThirdPartyDraggable, index as default };", "map": {"version": 3, "names": ["createPlugin", "config", "Emitter", "elementClosest", "applyStyle", "whenTransitionDone", "removeElement", "ScrollController", "ElementScrollController", "computeInnerRect", "WindowScrollController", "ElementDragging", "preventSelection", "preventContextMenu", "allowSelection", "allowContextMenu", "computeRect", "getClippingParents", "pointInsideRect", "constrainPoint", "intersectRects", "getRectCenter", "diffPoints", "mapHash", "rangeContainsRange", "isDateSpansEqual", "Interaction", "interactionSettingsToStore", "isDateSelectionValid", "enableCursor", "disable<PERSON><PERSON><PERSON>", "triggerDateSelect", "compareNumbers", "getElSeg", "getRelevantEvents", "EventImpl", "createEmptyEventStore", "applyMutationToEventStore", "isInteractionValid", "buildEventApis", "interactionSettingsStore", "startOfDay", "diffDates", "createDuration", "getEventTargetViaRoot", "identity", "eventTupleToStore", "parseDragMeta", "elementMatches", "refineEventDef", "parseEventDef", "getDefaultEventEnd", "createEventInstance", "BASE_OPTION_DEFAULTS", "touchMouseIgnoreWait", "ignore<PERSON><PERSON><PERSON><PERSON><PERSON>", "listenerCnt", "isWindowTouchMoveCancelled", "PointerDragging", "constructor", "containerEl", "subjectEl", "selector", "handleSelector", "shouldIgnoreMove", "shouldWatchScroll", "isDragging", "isTouchDragging", "wasTouchScroll", "handleMouseDown", "ev", "shouldIgnoreMouse", "isPrimaryMouseButton", "tryStart", "pev", "createEventFromMouse", "emitter", "trigger", "initScrollWatch", "document", "addEventListener", "handleMouseMove", "handleMouseUp", "recordCoords", "removeEventListener", "cleanup", "handleTouchStart", "createEventFromTouch", "targetEl", "target", "handleTouchMove", "handleTouchEnd", "window", "handleTouchScroll", "startIgnoringMouse", "handleScroll", "pageX", "scrollX", "prevScrollX", "prevPageX", "pageY", "scrollY", "prevScrollY", "prevPageY", "origEvent", "is<PERSON><PERSON>ch", "deltaX", "origPageX", "deltaY", "origPageY", "passive", "listenerCreated", "destroy", "listenerDestroyed", "querySubjectEl", "downEl", "destroyScrollWatch", "cancelTouchScroll", "<PERSON><PERSON><PERSON><PERSON>", "touches", "length", "button", "ctrl<PERSON>ey", "setTimeout", "onWindowTouchMove", "preventDefault", "ElementMirror", "isVisible", "sourceEl", "mirrorEl", "sourceElRect", "parentNode", "body", "zIndex", "revertDuration", "start", "getBoundingClientRect", "origScreenX", "origScreenY", "updateElPosition", "handleMove", "setIsVisible", "bool", "style", "display", "stop", "needsRevertAnimation", "callback", "done", "doRevertAnimation", "finalSourceElRect", "transition", "left", "top", "getMirrorEl", "cloneNode", "userSelect", "webkitUserSelect", "pointerEvents", "classList", "add", "position", "visibility", "boxSizing", "width", "right", "height", "bottom", "margin", "append<PERSON><PERSON><PERSON>", "ScrollGeomCache", "scrollController", "doesListening", "scrollTop", "getScrollTop", "scrollLeft", "getScrollLeft", "handleScrollChange", "origScrollTop", "origScrollLeft", "scrollWidth", "getScrollWidth", "scrollHeight", "getScrollHeight", "clientWidth", "getClientWidth", "clientHeight", "getClientHeight", "clientRect", "computeClientRect", "getEventTarget", "setScrollTop", "Math", "max", "min", "getMaxScrollTop", "setScrollLeft", "getMaxScrollLeft", "ElementScrollGeomCache", "el", "WindowScrollGeomCache", "getTime", "performance", "now", "Date", "AutoScroller", "isEnabled", "scroll<PERSON><PERSON>y", "edgeThreshold", "maxVelocity", "pointerScreenX", "pointerScreenY", "isAnimating", "scrollCaches", "everMovedUp", "everMovedDown", "everMovedLeft", "everMovedRight", "animate", "edge", "computeBestEdge", "handleSide", "msSinceRequest", "requestAnimation", "scrollStartEl", "buildCaches", "y<PERSON><PERSON><PERSON>", "xDelta", "scrollCache", "requestAnimationFrame", "seconds", "invDistance", "distance", "velocity", "sign", "name", "bestSide", "rect", "leftDist", "rightDist", "topDist", "bottomDist", "canScrollUp", "canScrollDown", "canScrollLeft", "canScrollRight", "queryScrollEls", "map", "els", "query", "push", "Array", "prototype", "slice", "call", "getRootNode", "querySelectorAll", "FeaturefulElementDragging", "delay", "minDistance", "touchScrollAllowed", "mirrorNeedsRevert", "isInteracting", "isDelayEnded", "isDistanceSurpassed", "delayTimeoutId", "onPointerDown", "pointer", "mirror", "startDelay", "handleDistanceSurpassed", "onPointerMove", "distanceSq", "type", "autoScroller", "onPointerUp", "tryStopDrag", "clearTimeout", "on", "handleDelayEnd", "tryStartDrag", "stopDrag", "bind", "setIgnoreMove", "setMirrorIsVisible", "setMirrorNeedsRevert", "setAutoScrollEnabled", "OffsetTracker", "origRect", "scrollEl", "computeLeft", "computeTop", "isWithinClipping", "point", "isIgnoredClipping", "node", "tagName", "HitDragging", "dragging", "droppableStore", "useSubjectCenter", "requireInitial", "disable<PERSON><PERSON><PERSON><PERSON><PERSON>", "initialHit", "movingHit", "finalHit", "handlePointerDown", "prepareHits", "processFirstCoord", "handleDragStart", "handleDragMove", "handlePointerUp", "releaseHits", "handleDragEnd", "origPoint", "adjustedPoint", "subjectRect", "HTMLElement", "queryHitForOffset", "slicedSubjectRect", "coordAdjust", "forceHandle", "hit", "isHitsEqual", "offsetTrackers", "interactionSettings", "component", "id", "offsetLeft", "offsetTop", "bestHit", "offsetTracker", "originLeft", "originTop", "positionLeft", "positionTop", "queryHit", "dateProfile", "activeRange", "dateSpan", "range", "contains", "elementFromPoint", "layer", "componentId", "context", "hit0", "hit1", "Boolean", "buildDatePointApiWithContext", "props", "transform", "pluginHooks", "datePointTransforms", "Object", "assign", "buildDatePointApi", "dateEnv", "span", "date", "toDate", "dateStr", "formatIso", "omitTime", "allDay", "DateClicking", "settings", "isValidDateDownEl", "hitDragging", "arg", "dayEl", "jsEvent", "view", "viewApi", "calendarApi", "DateSelecting", "dragSelection", "options", "canSelect", "selectable", "getComponentTouchDelay$1", "unselect", "handleHitUpdate", "isFinal", "isInvalid", "disallowed", "isHitComboAllowed", "joinHitsIntoSelection", "dateSelectionTransformers", "dispatch", "selection", "selectMinDistance", "dragScroll", "selectLongPressDelay", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dateSpan0", "dateSpan1", "ms", "end", "sort", "transformer", "res", "EventDragging", "subjectSeg", "eventRange", "relevantEvents", "receivingContext", "validMutation", "mutatedRelevantEvents", "origTarget", "initialContext", "eventInstanceId", "instance", "instanceId", "getCurrentData", "eventStore", "eventDragMinDistance", "eventSelection", "getComponentTouchDelay", "fixedMirrorParent", "dragRevertDuration", "<PERSON><PERSON><PERSON><PERSON>", "isValidSegDownEl", "event", "def", "mutation", "interaction", "affectedEvents", "mutatedEvents", "isEvent", "receivingOptions", "editable", "droppable", "computeEventMutation", "eventDragMutationMassagers", "eventUiBases", "displayDrag", "querySelector", "initialView", "eventDef", "eventInstance", "eventApi", "clearDrag", "updatedEventApi", "defs", "defId", "instances", "eventChangeArg", "oldEvent", "relatedEvents", "revert", "transformed", "eventDropTransformers", "delta", "<PERSON><PERSON><PERSON><PERSON>", "eventRemoveArg", "draggedEl", "addedEventDef", "addedEventInstance", "addedEventApi", "eventAddArg", "SELECTOR", "useEventCenter", "nextContext", "state", "prevContext", "initialCalendar", "eventInstanceStart", "massagers", "date0", "date1", "standardProps", "hasEnd", "allDayMaintainDuration", "largeUnit", "milliseconds", "massager", "eventLongPress<PERSON>elay", "EventResizing", "draggingSegEl", "draggingSeg", "segEl", "querySegEl", "seg", "computeMutation", "startDelta", "endDel<PERSON>", "isFromStart", "instanceRange", "UnselectAuto", "isRecentPointerDateSelect", "matchesCancel", "matchesEvent", "onSelect", "selectInfo", "onDocumentPointerDown", "unselectCancel", "onDocumentPointerUp", "documentPointer", "calendarState", "dateSelection", "unselectAuto", "off", "OPTION_REFINERS", "LISTENER_REFINERS", "dateClick", "eventDragStart", "eventDragStop", "eventDrop", "eventResizeStart", "eventResizeStop", "eventResize", "drop", "eventReceive", "eventLeave", "ExternalElementDragging", "suppliedDragMeta", "droppableEvent", "dragMeta", "buildDragMeta", "create", "canDropElOnCalendar", "computeEventForDateSpan", "finalView", "addingEvents", "getDragMetaFromEl", "dropAccept", "defProps", "leftoverProps", "externalDefTransforms", "refined", "extra", "sourceId", "forceEventDuration", "duration", "startTime", "str", "getEmbeddedElData", "obj", "JSON", "parse", "dataAttrPrefix", "prefix", "prefixedName", "getAttribute", "ExternalDraggable", "arguments", "undefined", "itemSelector", "appendTo", "eventData", "InferredElementDragging", "mirrorSelector", "currentMirrorEl", "handlePointerMove", "ThirdPartyDraggable", "containerOrSettings", "Element", "externalDragging", "index", "componentInteractions", "calendarInteractions", "elementDraggingImpl", "optionRefiners", "listenerRefiners", "Draggable", "default"], "sources": ["C:/Users/<USER>/Desktop/demo-git/demo-react/my-fullstack-app/frontend/node_modules/@fullcalendar/interaction/index.js"], "sourcesContent": ["import { createPlugin } from '@fullcalendar/core/index.js';\nimport { config, Emitter, elementClosest, applyStyle, whenTransitionDone, removeElement, ScrollController, ElementScrollController, computeInnerRect, WindowScrollController, ElementDragging, preventSelection, preventContextMenu, allowSelection, allowContextMenu, computeRect, getClippingParents, pointInsideRect, constrainPoint, intersectRects, getRectCenter, diffPoints, mapHash, rangeContainsRange, isDateSpansEqual, Interaction, interactionSettingsToStore, isDateSelectionValid, enableCursor, disableCursor, triggerDateSelect, compareNumbers, getElSeg, getRelevantEvents, EventImpl, createEmptyEventStore, applyMutationToEventStore, isInteractionValid, buildEventApis, interactionSettingsStore, startOfDay, diffDates, createDuration, getEventTargetViaRoot, identity, eventTupleToStore, parseDragMeta, elementMatches, refineEventDef, parseEventDef, getDefaultEventEnd, createEventInstance, BASE_OPTION_DEFAULTS } from '@fullcalendar/core/internal.js';\n\nconfig.touchMouseIgnoreWait = 500;\nlet ignoreMouseDepth = 0;\nlet listenerCnt = 0;\nlet isWindowTouchMoveCancelled = false;\n/*\nUses a \"pointer\" abstraction, which monitors UI events for both mouse and touch.\nTracks when the pointer \"drags\" on a certain element, meaning down+move+up.\n\nAlso, tracks if there was touch-scrolling.\nAlso, can prevent touch-scrolling from happening.\nAlso, can fire pointermove events when scrolling happens underneath, even when no real pointer movement.\n\nemits:\n- pointerdown\n- pointermove\n- pointerup\n*/\nclass PointerDragging {\n    constructor(containerEl) {\n        this.subjectEl = null;\n        // options that can be directly assigned by caller\n        this.selector = ''; // will cause subjectEl in all emitted events to be this element\n        this.handleSelector = '';\n        this.shouldIgnoreMove = false;\n        this.shouldWatchScroll = true; // for simulating pointermove on scroll\n        // internal states\n        this.isDragging = false;\n        this.isTouchDragging = false;\n        this.wasTouchScroll = false;\n        // Mouse\n        // ----------------------------------------------------------------------------------------------------\n        this.handleMouseDown = (ev) => {\n            if (!this.shouldIgnoreMouse() &&\n                isPrimaryMouseButton(ev) &&\n                this.tryStart(ev)) {\n                let pev = this.createEventFromMouse(ev, true);\n                this.emitter.trigger('pointerdown', pev);\n                this.initScrollWatch(pev);\n                if (!this.shouldIgnoreMove) {\n                    document.addEventListener('mousemove', this.handleMouseMove);\n                }\n                document.addEventListener('mouseup', this.handleMouseUp);\n            }\n        };\n        this.handleMouseMove = (ev) => {\n            let pev = this.createEventFromMouse(ev);\n            this.recordCoords(pev);\n            this.emitter.trigger('pointermove', pev);\n        };\n        this.handleMouseUp = (ev) => {\n            document.removeEventListener('mousemove', this.handleMouseMove);\n            document.removeEventListener('mouseup', this.handleMouseUp);\n            this.emitter.trigger('pointerup', this.createEventFromMouse(ev));\n            this.cleanup(); // call last so that pointerup has access to props\n        };\n        // Touch\n        // ----------------------------------------------------------------------------------------------------\n        this.handleTouchStart = (ev) => {\n            if (this.tryStart(ev)) {\n                this.isTouchDragging = true;\n                let pev = this.createEventFromTouch(ev, true);\n                this.emitter.trigger('pointerdown', pev);\n                this.initScrollWatch(pev);\n                // unlike mouse, need to attach to target, not document\n                // https://stackoverflow.com/a/45760014\n                let targetEl = ev.target;\n                if (!this.shouldIgnoreMove) {\n                    targetEl.addEventListener('touchmove', this.handleTouchMove);\n                }\n                targetEl.addEventListener('touchend', this.handleTouchEnd);\n                targetEl.addEventListener('touchcancel', this.handleTouchEnd); // treat it as a touch end\n                // attach a handler to get called when ANY scroll action happens on the page.\n                // this was impossible to do with normal on/off because 'scroll' doesn't bubble.\n                // http://stackoverflow.com/a/32954565/96342\n                window.addEventListener('scroll', this.handleTouchScroll, true);\n            }\n        };\n        this.handleTouchMove = (ev) => {\n            let pev = this.createEventFromTouch(ev);\n            this.recordCoords(pev);\n            this.emitter.trigger('pointermove', pev);\n        };\n        this.handleTouchEnd = (ev) => {\n            if (this.isDragging) { // done to guard against touchend followed by touchcancel\n                let targetEl = ev.target;\n                targetEl.removeEventListener('touchmove', this.handleTouchMove);\n                targetEl.removeEventListener('touchend', this.handleTouchEnd);\n                targetEl.removeEventListener('touchcancel', this.handleTouchEnd);\n                window.removeEventListener('scroll', this.handleTouchScroll, true); // useCaptured=true\n                this.emitter.trigger('pointerup', this.createEventFromTouch(ev));\n                this.cleanup(); // call last so that pointerup has access to props\n                this.isTouchDragging = false;\n                startIgnoringMouse();\n            }\n        };\n        this.handleTouchScroll = () => {\n            this.wasTouchScroll = true;\n        };\n        this.handleScroll = (ev) => {\n            if (!this.shouldIgnoreMove) {\n                let pageX = (window.scrollX - this.prevScrollX) + this.prevPageX;\n                let pageY = (window.scrollY - this.prevScrollY) + this.prevPageY;\n                this.emitter.trigger('pointermove', {\n                    origEvent: ev,\n                    isTouch: this.isTouchDragging,\n                    subjectEl: this.subjectEl,\n                    pageX,\n                    pageY,\n                    deltaX: pageX - this.origPageX,\n                    deltaY: pageY - this.origPageY,\n                });\n            }\n        };\n        this.containerEl = containerEl;\n        this.emitter = new Emitter();\n        containerEl.addEventListener('mousedown', this.handleMouseDown);\n        containerEl.addEventListener('touchstart', this.handleTouchStart, { passive: true });\n        listenerCreated();\n    }\n    destroy() {\n        this.containerEl.removeEventListener('mousedown', this.handleMouseDown);\n        this.containerEl.removeEventListener('touchstart', this.handleTouchStart, { passive: true });\n        listenerDestroyed();\n    }\n    tryStart(ev) {\n        let subjectEl = this.querySubjectEl(ev);\n        let downEl = ev.target;\n        if (subjectEl &&\n            (!this.handleSelector || elementClosest(downEl, this.handleSelector))) {\n            this.subjectEl = subjectEl;\n            this.isDragging = true; // do this first so cancelTouchScroll will work\n            this.wasTouchScroll = false;\n            return true;\n        }\n        return false;\n    }\n    cleanup() {\n        isWindowTouchMoveCancelled = false;\n        this.isDragging = false;\n        this.subjectEl = null;\n        // keep wasTouchScroll around for later access\n        this.destroyScrollWatch();\n    }\n    querySubjectEl(ev) {\n        if (this.selector) {\n            return elementClosest(ev.target, this.selector);\n        }\n        return this.containerEl;\n    }\n    shouldIgnoreMouse() {\n        return ignoreMouseDepth || this.isTouchDragging;\n    }\n    // can be called by user of this class, to cancel touch-based scrolling for the current drag\n    cancelTouchScroll() {\n        if (this.isDragging) {\n            isWindowTouchMoveCancelled = true;\n        }\n    }\n    // Scrolling that simulates pointermoves\n    // ----------------------------------------------------------------------------------------------------\n    initScrollWatch(ev) {\n        if (this.shouldWatchScroll) {\n            this.recordCoords(ev);\n            window.addEventListener('scroll', this.handleScroll, true); // useCapture=true\n        }\n    }\n    recordCoords(ev) {\n        if (this.shouldWatchScroll) {\n            this.prevPageX = ev.pageX;\n            this.prevPageY = ev.pageY;\n            this.prevScrollX = window.scrollX;\n            this.prevScrollY = window.scrollY;\n        }\n    }\n    destroyScrollWatch() {\n        if (this.shouldWatchScroll) {\n            window.removeEventListener('scroll', this.handleScroll, true); // useCaptured=true\n        }\n    }\n    // Event Normalization\n    // ----------------------------------------------------------------------------------------------------\n    createEventFromMouse(ev, isFirst) {\n        let deltaX = 0;\n        let deltaY = 0;\n        // TODO: repeat code\n        if (isFirst) {\n            this.origPageX = ev.pageX;\n            this.origPageY = ev.pageY;\n        }\n        else {\n            deltaX = ev.pageX - this.origPageX;\n            deltaY = ev.pageY - this.origPageY;\n        }\n        return {\n            origEvent: ev,\n            isTouch: false,\n            subjectEl: this.subjectEl,\n            pageX: ev.pageX,\n            pageY: ev.pageY,\n            deltaX,\n            deltaY,\n        };\n    }\n    createEventFromTouch(ev, isFirst) {\n        let touches = ev.touches;\n        let pageX;\n        let pageY;\n        let deltaX = 0;\n        let deltaY = 0;\n        // if touch coords available, prefer,\n        // because FF would give bad ev.pageX ev.pageY\n        if (touches && touches.length) {\n            pageX = touches[0].pageX;\n            pageY = touches[0].pageY;\n        }\n        else {\n            pageX = ev.pageX;\n            pageY = ev.pageY;\n        }\n        // TODO: repeat code\n        if (isFirst) {\n            this.origPageX = pageX;\n            this.origPageY = pageY;\n        }\n        else {\n            deltaX = pageX - this.origPageX;\n            deltaY = pageY - this.origPageY;\n        }\n        return {\n            origEvent: ev,\n            isTouch: true,\n            subjectEl: this.subjectEl,\n            pageX,\n            pageY,\n            deltaX,\n            deltaY,\n        };\n    }\n}\n// Returns a boolean whether this was a left mouse click and no ctrl key (which means right click on Mac)\nfunction isPrimaryMouseButton(ev) {\n    return ev.button === 0 && !ev.ctrlKey;\n}\n// Ignoring fake mouse events generated by touch\n// ----------------------------------------------------------------------------------------------------\nfunction startIgnoringMouse() {\n    ignoreMouseDepth += 1;\n    setTimeout(() => {\n        ignoreMouseDepth -= 1;\n    }, config.touchMouseIgnoreWait);\n}\n// We want to attach touchmove as early as possible for Safari\n// ----------------------------------------------------------------------------------------------------\nfunction listenerCreated() {\n    listenerCnt += 1;\n    if (listenerCnt === 1) {\n        window.addEventListener('touchmove', onWindowTouchMove, { passive: false });\n    }\n}\nfunction listenerDestroyed() {\n    listenerCnt -= 1;\n    if (!listenerCnt) {\n        window.removeEventListener('touchmove', onWindowTouchMove, { passive: false });\n    }\n}\nfunction onWindowTouchMove(ev) {\n    if (isWindowTouchMoveCancelled) {\n        ev.preventDefault();\n    }\n}\n\n/*\nAn effect in which an element follows the movement of a pointer across the screen.\nThe moving element is a clone of some other element.\nMust call start + handleMove + stop.\n*/\nclass ElementMirror {\n    constructor() {\n        this.isVisible = false; // must be explicitly enabled\n        this.sourceEl = null;\n        this.mirrorEl = null;\n        this.sourceElRect = null; // screen coords relative to viewport\n        // options that can be set directly by caller\n        this.parentNode = document.body; // HIGHLY SUGGESTED to set this to sidestep ShadowDOM issues\n        this.zIndex = 9999;\n        this.revertDuration = 0;\n    }\n    start(sourceEl, pageX, pageY) {\n        this.sourceEl = sourceEl;\n        this.sourceElRect = this.sourceEl.getBoundingClientRect();\n        this.origScreenX = pageX - window.scrollX;\n        this.origScreenY = pageY - window.scrollY;\n        this.deltaX = 0;\n        this.deltaY = 0;\n        this.updateElPosition();\n    }\n    handleMove(pageX, pageY) {\n        this.deltaX = (pageX - window.scrollX) - this.origScreenX;\n        this.deltaY = (pageY - window.scrollY) - this.origScreenY;\n        this.updateElPosition();\n    }\n    // can be called before start\n    setIsVisible(bool) {\n        if (bool) {\n            if (!this.isVisible) {\n                if (this.mirrorEl) {\n                    this.mirrorEl.style.display = '';\n                }\n                this.isVisible = bool; // needs to happen before updateElPosition\n                this.updateElPosition(); // because was not updating the position while invisible\n            }\n        }\n        else if (this.isVisible) {\n            if (this.mirrorEl) {\n                this.mirrorEl.style.display = 'none';\n            }\n            this.isVisible = bool;\n        }\n    }\n    // always async\n    stop(needsRevertAnimation, callback) {\n        let done = () => {\n            this.cleanup();\n            callback();\n        };\n        if (needsRevertAnimation &&\n            this.mirrorEl &&\n            this.isVisible &&\n            this.revertDuration && // if 0, transition won't work\n            (this.deltaX || this.deltaY) // if same coords, transition won't work\n        ) {\n            this.doRevertAnimation(done, this.revertDuration);\n        }\n        else {\n            setTimeout(done, 0);\n        }\n    }\n    doRevertAnimation(callback, revertDuration) {\n        let mirrorEl = this.mirrorEl;\n        let finalSourceElRect = this.sourceEl.getBoundingClientRect(); // because autoscrolling might have happened\n        mirrorEl.style.transition =\n            'top ' + revertDuration + 'ms,' +\n                'left ' + revertDuration + 'ms';\n        applyStyle(mirrorEl, {\n            left: finalSourceElRect.left,\n            top: finalSourceElRect.top,\n        });\n        whenTransitionDone(mirrorEl, () => {\n            mirrorEl.style.transition = '';\n            callback();\n        });\n    }\n    cleanup() {\n        if (this.mirrorEl) {\n            removeElement(this.mirrorEl);\n            this.mirrorEl = null;\n        }\n        this.sourceEl = null;\n    }\n    updateElPosition() {\n        if (this.sourceEl && this.isVisible) {\n            applyStyle(this.getMirrorEl(), {\n                left: this.sourceElRect.left + this.deltaX,\n                top: this.sourceElRect.top + this.deltaY,\n            });\n        }\n    }\n    getMirrorEl() {\n        let sourceElRect = this.sourceElRect;\n        let mirrorEl = this.mirrorEl;\n        if (!mirrorEl) {\n            mirrorEl = this.mirrorEl = this.sourceEl.cloneNode(true); // cloneChildren=true\n            // we don't want long taps or any mouse interaction causing selection/menus.\n            // would use preventSelection(), but that prevents selectstart, causing problems.\n            mirrorEl.style.userSelect = 'none';\n            mirrorEl.style.webkitUserSelect = 'none';\n            mirrorEl.style.pointerEvents = 'none';\n            mirrorEl.classList.add('fc-event-dragging');\n            applyStyle(mirrorEl, {\n                position: 'fixed',\n                zIndex: this.zIndex,\n                visibility: '',\n                boxSizing: 'border-box',\n                width: sourceElRect.right - sourceElRect.left,\n                height: sourceElRect.bottom - sourceElRect.top,\n                right: 'auto',\n                bottom: 'auto',\n                margin: 0,\n            });\n            this.parentNode.appendChild(mirrorEl);\n        }\n        return mirrorEl;\n    }\n}\n\n/*\nIs a cache for a given element's scroll information (all the info that ScrollController stores)\nin addition the \"client rectangle\" of the element.. the area within the scrollbars.\n\nThe cache can be in one of two modes:\n- doesListening:false - ignores when the container is scrolled by someone else\n- doesListening:true - watch for scrolling and update the cache\n*/\nclass ScrollGeomCache extends ScrollController {\n    constructor(scrollController, doesListening) {\n        super();\n        this.handleScroll = () => {\n            this.scrollTop = this.scrollController.getScrollTop();\n            this.scrollLeft = this.scrollController.getScrollLeft();\n            this.handleScrollChange();\n        };\n        this.scrollController = scrollController;\n        this.doesListening = doesListening;\n        this.scrollTop = this.origScrollTop = scrollController.getScrollTop();\n        this.scrollLeft = this.origScrollLeft = scrollController.getScrollLeft();\n        this.scrollWidth = scrollController.getScrollWidth();\n        this.scrollHeight = scrollController.getScrollHeight();\n        this.clientWidth = scrollController.getClientWidth();\n        this.clientHeight = scrollController.getClientHeight();\n        this.clientRect = this.computeClientRect(); // do last in case it needs cached values\n        if (this.doesListening) {\n            this.getEventTarget().addEventListener('scroll', this.handleScroll);\n        }\n    }\n    destroy() {\n        if (this.doesListening) {\n            this.getEventTarget().removeEventListener('scroll', this.handleScroll);\n        }\n    }\n    getScrollTop() {\n        return this.scrollTop;\n    }\n    getScrollLeft() {\n        return this.scrollLeft;\n    }\n    setScrollTop(top) {\n        this.scrollController.setScrollTop(top);\n        if (!this.doesListening) {\n            // we are not relying on the element to normalize out-of-bounds scroll values\n            // so we need to sanitize ourselves\n            this.scrollTop = Math.max(Math.min(top, this.getMaxScrollTop()), 0);\n            this.handleScrollChange();\n        }\n    }\n    setScrollLeft(top) {\n        this.scrollController.setScrollLeft(top);\n        if (!this.doesListening) {\n            // we are not relying on the element to normalize out-of-bounds scroll values\n            // so we need to sanitize ourselves\n            this.scrollLeft = Math.max(Math.min(top, this.getMaxScrollLeft()), 0);\n            this.handleScrollChange();\n        }\n    }\n    getClientWidth() {\n        return this.clientWidth;\n    }\n    getClientHeight() {\n        return this.clientHeight;\n    }\n    getScrollWidth() {\n        return this.scrollWidth;\n    }\n    getScrollHeight() {\n        return this.scrollHeight;\n    }\n    handleScrollChange() {\n    }\n}\n\nclass ElementScrollGeomCache extends ScrollGeomCache {\n    constructor(el, doesListening) {\n        super(new ElementScrollController(el), doesListening);\n    }\n    getEventTarget() {\n        return this.scrollController.el;\n    }\n    computeClientRect() {\n        return computeInnerRect(this.scrollController.el);\n    }\n}\n\nclass WindowScrollGeomCache extends ScrollGeomCache {\n    constructor(doesListening) {\n        super(new WindowScrollController(), doesListening);\n    }\n    getEventTarget() {\n        return window;\n    }\n    computeClientRect() {\n        return {\n            left: this.scrollLeft,\n            right: this.scrollLeft + this.clientWidth,\n            top: this.scrollTop,\n            bottom: this.scrollTop + this.clientHeight,\n        };\n    }\n    // the window is the only scroll object that changes it's rectangle relative\n    // to the document's topleft as it scrolls\n    handleScrollChange() {\n        this.clientRect = this.computeClientRect();\n    }\n}\n\n// If available we are using native \"performance\" API instead of \"Date\"\n// Read more about it on MDN:\n// https://developer.mozilla.org/en-US/docs/Web/API/Performance\nconst getTime = typeof performance === 'function' ? performance.now : Date.now;\n/*\nFor a pointer interaction, automatically scrolls certain scroll containers when the pointer\napproaches the edge.\n\nThe caller must call start + handleMove + stop.\n*/\nclass AutoScroller {\n    constructor() {\n        // options that can be set by caller\n        this.isEnabled = true;\n        this.scrollQuery = [window, '.fc-scroller'];\n        this.edgeThreshold = 50; // pixels\n        this.maxVelocity = 300; // pixels per second\n        // internal state\n        this.pointerScreenX = null;\n        this.pointerScreenY = null;\n        this.isAnimating = false;\n        this.scrollCaches = null;\n        // protect against the initial pointerdown being too close to an edge and starting the scroll\n        this.everMovedUp = false;\n        this.everMovedDown = false;\n        this.everMovedLeft = false;\n        this.everMovedRight = false;\n        this.animate = () => {\n            if (this.isAnimating) { // wasn't cancelled between animation calls\n                let edge = this.computeBestEdge(this.pointerScreenX + window.scrollX, this.pointerScreenY + window.scrollY);\n                if (edge) {\n                    let now = getTime();\n                    this.handleSide(edge, (now - this.msSinceRequest) / 1000);\n                    this.requestAnimation(now);\n                }\n                else {\n                    this.isAnimating = false; // will stop animation\n                }\n            }\n        };\n    }\n    start(pageX, pageY, scrollStartEl) {\n        if (this.isEnabled) {\n            this.scrollCaches = this.buildCaches(scrollStartEl);\n            this.pointerScreenX = null;\n            this.pointerScreenY = null;\n            this.everMovedUp = false;\n            this.everMovedDown = false;\n            this.everMovedLeft = false;\n            this.everMovedRight = false;\n            this.handleMove(pageX, pageY);\n        }\n    }\n    handleMove(pageX, pageY) {\n        if (this.isEnabled) {\n            let pointerScreenX = pageX - window.scrollX;\n            let pointerScreenY = pageY - window.scrollY;\n            let yDelta = this.pointerScreenY === null ? 0 : pointerScreenY - this.pointerScreenY;\n            let xDelta = this.pointerScreenX === null ? 0 : pointerScreenX - this.pointerScreenX;\n            if (yDelta < 0) {\n                this.everMovedUp = true;\n            }\n            else if (yDelta > 0) {\n                this.everMovedDown = true;\n            }\n            if (xDelta < 0) {\n                this.everMovedLeft = true;\n            }\n            else if (xDelta > 0) {\n                this.everMovedRight = true;\n            }\n            this.pointerScreenX = pointerScreenX;\n            this.pointerScreenY = pointerScreenY;\n            if (!this.isAnimating) {\n                this.isAnimating = true;\n                this.requestAnimation(getTime());\n            }\n        }\n    }\n    stop() {\n        if (this.isEnabled) {\n            this.isAnimating = false; // will stop animation\n            for (let scrollCache of this.scrollCaches) {\n                scrollCache.destroy();\n            }\n            this.scrollCaches = null;\n        }\n    }\n    requestAnimation(now) {\n        this.msSinceRequest = now;\n        requestAnimationFrame(this.animate);\n    }\n    handleSide(edge, seconds) {\n        let { scrollCache } = edge;\n        let { edgeThreshold } = this;\n        let invDistance = edgeThreshold - edge.distance;\n        let velocity = // the closer to the edge, the faster we scroll\n         ((invDistance * invDistance) / (edgeThreshold * edgeThreshold)) * // quadratic\n            this.maxVelocity * seconds;\n        let sign = 1;\n        switch (edge.name) {\n            case 'left':\n                sign = -1;\n            // falls through\n            case 'right':\n                scrollCache.setScrollLeft(scrollCache.getScrollLeft() + velocity * sign);\n                break;\n            case 'top':\n                sign = -1;\n            // falls through\n            case 'bottom':\n                scrollCache.setScrollTop(scrollCache.getScrollTop() + velocity * sign);\n                break;\n        }\n    }\n    // left/top are relative to document topleft\n    computeBestEdge(left, top) {\n        let { edgeThreshold } = this;\n        let bestSide = null;\n        let scrollCaches = this.scrollCaches || [];\n        for (let scrollCache of scrollCaches) {\n            let rect = scrollCache.clientRect;\n            let leftDist = left - rect.left;\n            let rightDist = rect.right - left;\n            let topDist = top - rect.top;\n            let bottomDist = rect.bottom - top;\n            // completely within the rect?\n            if (leftDist >= 0 && rightDist >= 0 && topDist >= 0 && bottomDist >= 0) {\n                if (topDist <= edgeThreshold && this.everMovedUp && scrollCache.canScrollUp() &&\n                    (!bestSide || bestSide.distance > topDist)) {\n                    bestSide = { scrollCache, name: 'top', distance: topDist };\n                }\n                if (bottomDist <= edgeThreshold && this.everMovedDown && scrollCache.canScrollDown() &&\n                    (!bestSide || bestSide.distance > bottomDist)) {\n                    bestSide = { scrollCache, name: 'bottom', distance: bottomDist };\n                }\n                /*\n                TODO: fix broken RTL scrolling. canScrollLeft always returning false\n                https://github.com/fullcalendar/fullcalendar/issues/4837\n                */\n                if (leftDist <= edgeThreshold && this.everMovedLeft && scrollCache.canScrollLeft() &&\n                    (!bestSide || bestSide.distance > leftDist)) {\n                    bestSide = { scrollCache, name: 'left', distance: leftDist };\n                }\n                if (rightDist <= edgeThreshold && this.everMovedRight && scrollCache.canScrollRight() &&\n                    (!bestSide || bestSide.distance > rightDist)) {\n                    bestSide = { scrollCache, name: 'right', distance: rightDist };\n                }\n            }\n        }\n        return bestSide;\n    }\n    buildCaches(scrollStartEl) {\n        return this.queryScrollEls(scrollStartEl).map((el) => {\n            if (el === window) {\n                return new WindowScrollGeomCache(false); // false = don't listen to user-generated scrolls\n            }\n            return new ElementScrollGeomCache(el, false); // false = don't listen to user-generated scrolls\n        });\n    }\n    queryScrollEls(scrollStartEl) {\n        let els = [];\n        for (let query of this.scrollQuery) {\n            if (typeof query === 'object') {\n                els.push(query);\n            }\n            else {\n                /*\n                TODO: in the future, always have auto-scroll happen on element where current Hit came from\n                Ticket: https://github.com/fullcalendar/fullcalendar/issues/4593\n                */\n                els.push(...Array.prototype.slice.call(scrollStartEl.getRootNode().querySelectorAll(query)));\n            }\n        }\n        return els;\n    }\n}\n\n/*\nMonitors dragging on an element. Has a number of high-level features:\n- minimum distance required before dragging\n- minimum wait time (\"delay\") before dragging\n- a mirror element that follows the pointer\n*/\nclass FeaturefulElementDragging extends ElementDragging {\n    constructor(containerEl, selector) {\n        super(containerEl);\n        this.containerEl = containerEl;\n        // options that can be directly set by caller\n        // the caller can also set the PointerDragging's options as well\n        this.delay = null;\n        this.minDistance = 0;\n        this.touchScrollAllowed = true; // prevents drag from starting and blocks scrolling during drag\n        this.mirrorNeedsRevert = false;\n        this.isInteracting = false; // is the user validly moving the pointer? lasts until pointerup\n        this.isDragging = false; // is it INTENTFULLY dragging? lasts until after revert animation\n        this.isDelayEnded = false;\n        this.isDistanceSurpassed = false;\n        this.delayTimeoutId = null;\n        this.onPointerDown = (ev) => {\n            if (!this.isDragging) { // so new drag doesn't happen while revert animation is going\n                this.isInteracting = true;\n                this.isDelayEnded = false;\n                this.isDistanceSurpassed = false;\n                preventSelection(document.body);\n                preventContextMenu(document.body);\n                // prevent links from being visited if there's an eventual drag.\n                // also prevents selection in older browsers (maybe?).\n                // not necessary for touch, besides, browser would complain about passiveness.\n                if (!ev.isTouch) {\n                    ev.origEvent.preventDefault();\n                }\n                this.emitter.trigger('pointerdown', ev);\n                if (this.isInteracting && // not destroyed via pointerdown handler\n                    !this.pointer.shouldIgnoreMove) {\n                    // actions related to initiating dragstart+dragmove+dragend...\n                    this.mirror.setIsVisible(false); // reset. caller must set-visible\n                    this.mirror.start(ev.subjectEl, ev.pageX, ev.pageY); // must happen on first pointer down\n                    this.startDelay(ev);\n                    if (!this.minDistance) {\n                        this.handleDistanceSurpassed(ev);\n                    }\n                }\n            }\n        };\n        this.onPointerMove = (ev) => {\n            if (this.isInteracting) {\n                this.emitter.trigger('pointermove', ev);\n                if (!this.isDistanceSurpassed) {\n                    let minDistance = this.minDistance;\n                    let distanceSq; // current distance from the origin, squared\n                    let { deltaX, deltaY } = ev;\n                    distanceSq = deltaX * deltaX + deltaY * deltaY;\n                    if (distanceSq >= minDistance * minDistance) { // use pythagorean theorem\n                        this.handleDistanceSurpassed(ev);\n                    }\n                }\n                if (this.isDragging) {\n                    // a real pointer move? (not one simulated by scrolling)\n                    if (ev.origEvent.type !== 'scroll') {\n                        this.mirror.handleMove(ev.pageX, ev.pageY);\n                        this.autoScroller.handleMove(ev.pageX, ev.pageY);\n                    }\n                    this.emitter.trigger('dragmove', ev);\n                }\n            }\n        };\n        this.onPointerUp = (ev) => {\n            if (this.isInteracting) {\n                this.isInteracting = false;\n                allowSelection(document.body);\n                allowContextMenu(document.body);\n                this.emitter.trigger('pointerup', ev); // can potentially set mirrorNeedsRevert\n                if (this.isDragging) {\n                    this.autoScroller.stop();\n                    this.tryStopDrag(ev); // which will stop the mirror\n                }\n                if (this.delayTimeoutId) {\n                    clearTimeout(this.delayTimeoutId);\n                    this.delayTimeoutId = null;\n                }\n            }\n        };\n        let pointer = this.pointer = new PointerDragging(containerEl);\n        pointer.emitter.on('pointerdown', this.onPointerDown);\n        pointer.emitter.on('pointermove', this.onPointerMove);\n        pointer.emitter.on('pointerup', this.onPointerUp);\n        if (selector) {\n            pointer.selector = selector;\n        }\n        this.mirror = new ElementMirror();\n        this.autoScroller = new AutoScroller();\n    }\n    destroy() {\n        this.pointer.destroy();\n        // HACK: simulate a pointer-up to end the current drag\n        // TODO: fire 'dragend' directly and stop interaction. discourage use of pointerup event (b/c might not fire)\n        this.onPointerUp({});\n    }\n    startDelay(ev) {\n        if (typeof this.delay === 'number') {\n            this.delayTimeoutId = setTimeout(() => {\n                this.delayTimeoutId = null;\n                this.handleDelayEnd(ev);\n            }, this.delay); // not assignable to number!\n        }\n        else {\n            this.handleDelayEnd(ev);\n        }\n    }\n    handleDelayEnd(ev) {\n        this.isDelayEnded = true;\n        this.tryStartDrag(ev);\n    }\n    handleDistanceSurpassed(ev) {\n        this.isDistanceSurpassed = true;\n        this.tryStartDrag(ev);\n    }\n    tryStartDrag(ev) {\n        if (this.isDelayEnded && this.isDistanceSurpassed) {\n            if (!this.pointer.wasTouchScroll || this.touchScrollAllowed) {\n                this.isDragging = true;\n                this.mirrorNeedsRevert = false;\n                this.autoScroller.start(ev.pageX, ev.pageY, this.containerEl);\n                this.emitter.trigger('dragstart', ev);\n                if (this.touchScrollAllowed === false) {\n                    this.pointer.cancelTouchScroll();\n                }\n            }\n        }\n    }\n    tryStopDrag(ev) {\n        // .stop() is ALWAYS asynchronous, which we NEED because we want all pointerup events\n        // that come from the document to fire beforehand. much more convenient this way.\n        this.mirror.stop(this.mirrorNeedsRevert, this.stopDrag.bind(this, ev));\n    }\n    stopDrag(ev) {\n        this.isDragging = false;\n        this.emitter.trigger('dragend', ev);\n    }\n    // fill in the implementations...\n    setIgnoreMove(bool) {\n        this.pointer.shouldIgnoreMove = bool;\n    }\n    setMirrorIsVisible(bool) {\n        this.mirror.setIsVisible(bool);\n    }\n    setMirrorNeedsRevert(bool) {\n        this.mirrorNeedsRevert = bool;\n    }\n    setAutoScrollEnabled(bool) {\n        this.autoScroller.isEnabled = bool;\n    }\n}\n\n/*\nWhen this class is instantiated, it records the offset of an element (relative to the document topleft),\nand continues to monitor scrolling, updating the cached coordinates if it needs to.\nDoes not access the DOM after instantiation, so highly performant.\n\nAlso keeps track of all scrolling/overflow:hidden containers that are parents of the given element\nand an determine if a given point is inside the combined clipping rectangle.\n*/\nclass OffsetTracker {\n    constructor(el) {\n        this.el = el;\n        this.origRect = computeRect(el);\n        // will work fine for divs that have overflow:hidden\n        this.scrollCaches = getClippingParents(el).map((scrollEl) => new ElementScrollGeomCache(scrollEl, true));\n    }\n    destroy() {\n        for (let scrollCache of this.scrollCaches) {\n            scrollCache.destroy();\n        }\n    }\n    computeLeft() {\n        let left = this.origRect.left;\n        for (let scrollCache of this.scrollCaches) {\n            left += scrollCache.origScrollLeft - scrollCache.getScrollLeft();\n        }\n        return left;\n    }\n    computeTop() {\n        let top = this.origRect.top;\n        for (let scrollCache of this.scrollCaches) {\n            top += scrollCache.origScrollTop - scrollCache.getScrollTop();\n        }\n        return top;\n    }\n    isWithinClipping(pageX, pageY) {\n        let point = { left: pageX, top: pageY };\n        for (let scrollCache of this.scrollCaches) {\n            if (!isIgnoredClipping(scrollCache.getEventTarget()) &&\n                !pointInsideRect(point, scrollCache.clientRect)) {\n                return false;\n            }\n        }\n        return true;\n    }\n}\n// certain clipping containers should never constrain interactions, like <html> and <body>\n// https://github.com/fullcalendar/fullcalendar/issues/3615\nfunction isIgnoredClipping(node) {\n    let tagName = node.tagName;\n    return tagName === 'HTML' || tagName === 'BODY';\n}\n\n/*\nTracks movement over multiple droppable areas (aka \"hits\")\nthat exist in one or more DateComponents.\nRelies on an existing draggable.\n\nemits:\n- pointerdown\n- dragstart\n- hitchange - fires initially, even if not over a hit\n- pointerup\n- (hitchange - again, to null, if ended over a hit)\n- dragend\n*/\nclass HitDragging {\n    constructor(dragging, droppableStore) {\n        // options that can be set by caller\n        this.useSubjectCenter = false;\n        this.requireInitial = true; // if doesn't start out on a hit, won't emit any events\n        this.disablePointCheck = false;\n        this.initialHit = null;\n        this.movingHit = null;\n        this.finalHit = null; // won't ever be populated if shouldIgnoreMove\n        this.handlePointerDown = (ev) => {\n            let { dragging } = this;\n            this.initialHit = null;\n            this.movingHit = null;\n            this.finalHit = null;\n            this.prepareHits();\n            this.processFirstCoord(ev);\n            if (this.initialHit || !this.requireInitial) {\n                dragging.setIgnoreMove(false);\n                // TODO: fire this before computing processFirstCoord, so listeners can cancel. this gets fired by almost every handler :(\n                this.emitter.trigger('pointerdown', ev);\n            }\n            else {\n                dragging.setIgnoreMove(true);\n            }\n        };\n        this.handleDragStart = (ev) => {\n            this.emitter.trigger('dragstart', ev);\n            this.handleMove(ev, true); // force = fire even if initially null\n        };\n        this.handleDragMove = (ev) => {\n            this.emitter.trigger('dragmove', ev);\n            this.handleMove(ev);\n        };\n        this.handlePointerUp = (ev) => {\n            this.releaseHits();\n            this.emitter.trigger('pointerup', ev);\n        };\n        this.handleDragEnd = (ev) => {\n            if (this.movingHit) {\n                this.emitter.trigger('hitupdate', null, true, ev);\n            }\n            this.finalHit = this.movingHit;\n            this.movingHit = null;\n            this.emitter.trigger('dragend', ev);\n        };\n        this.droppableStore = droppableStore;\n        dragging.emitter.on('pointerdown', this.handlePointerDown);\n        dragging.emitter.on('dragstart', this.handleDragStart);\n        dragging.emitter.on('dragmove', this.handleDragMove);\n        dragging.emitter.on('pointerup', this.handlePointerUp);\n        dragging.emitter.on('dragend', this.handleDragEnd);\n        this.dragging = dragging;\n        this.emitter = new Emitter();\n    }\n    // sets initialHit\n    // sets coordAdjust\n    processFirstCoord(ev) {\n        let origPoint = { left: ev.pageX, top: ev.pageY };\n        let adjustedPoint = origPoint;\n        let subjectEl = ev.subjectEl;\n        let subjectRect;\n        if (subjectEl instanceof HTMLElement) { // i.e. not a Document/ShadowRoot\n            subjectRect = computeRect(subjectEl);\n            adjustedPoint = constrainPoint(adjustedPoint, subjectRect);\n        }\n        let initialHit = this.initialHit = this.queryHitForOffset(adjustedPoint.left, adjustedPoint.top);\n        if (initialHit) {\n            if (this.useSubjectCenter && subjectRect) {\n                let slicedSubjectRect = intersectRects(subjectRect, initialHit.rect);\n                if (slicedSubjectRect) {\n                    adjustedPoint = getRectCenter(slicedSubjectRect);\n                }\n            }\n            this.coordAdjust = diffPoints(adjustedPoint, origPoint);\n        }\n        else {\n            this.coordAdjust = { left: 0, top: 0 };\n        }\n    }\n    handleMove(ev, forceHandle) {\n        let hit = this.queryHitForOffset(ev.pageX + this.coordAdjust.left, ev.pageY + this.coordAdjust.top);\n        if (forceHandle || !isHitsEqual(this.movingHit, hit)) {\n            this.movingHit = hit;\n            this.emitter.trigger('hitupdate', hit, false, ev);\n        }\n    }\n    prepareHits() {\n        this.offsetTrackers = mapHash(this.droppableStore, (interactionSettings) => {\n            interactionSettings.component.prepareHits();\n            return new OffsetTracker(interactionSettings.el);\n        });\n    }\n    releaseHits() {\n        let { offsetTrackers } = this;\n        for (let id in offsetTrackers) {\n            offsetTrackers[id].destroy();\n        }\n        this.offsetTrackers = {};\n    }\n    queryHitForOffset(offsetLeft, offsetTop) {\n        let { droppableStore, offsetTrackers } = this;\n        let bestHit = null;\n        for (let id in droppableStore) {\n            let component = droppableStore[id].component;\n            let offsetTracker = offsetTrackers[id];\n            if (offsetTracker && // wasn't destroyed mid-drag\n                offsetTracker.isWithinClipping(offsetLeft, offsetTop)) {\n                let originLeft = offsetTracker.computeLeft();\n                let originTop = offsetTracker.computeTop();\n                let positionLeft = offsetLeft - originLeft;\n                let positionTop = offsetTop - originTop;\n                let { origRect } = offsetTracker;\n                let width = origRect.right - origRect.left;\n                let height = origRect.bottom - origRect.top;\n                if (\n                // must be within the element's bounds\n                positionLeft >= 0 && positionLeft < width &&\n                    positionTop >= 0 && positionTop < height) {\n                    let hit = component.queryHit(positionLeft, positionTop, width, height);\n                    if (hit && (\n                    // make sure the hit is within activeRange, meaning it's not a dead cell\n                    rangeContainsRange(hit.dateProfile.activeRange, hit.dateSpan.range)) &&\n                        // Ensure the component we are querying for the hit is accessibly my the pointer\n                        // Prevents obscured calendars (ex: under a modal dialog) from accepting hit\n                        // https://github.com/fullcalendar/fullcalendar/issues/5026\n                        (this.disablePointCheck ||\n                            offsetTracker.el.contains(offsetTracker.el.getRootNode().elementFromPoint(\n                            // add-back origins to get coordinate relative to top-left of window viewport\n                            positionLeft + originLeft - window.scrollX, positionTop + originTop - window.scrollY))) &&\n                        (!bestHit || hit.layer > bestHit.layer)) {\n                        hit.componentId = id;\n                        hit.context = component.context;\n                        // TODO: better way to re-orient rectangle\n                        hit.rect.left += originLeft;\n                        hit.rect.right += originLeft;\n                        hit.rect.top += originTop;\n                        hit.rect.bottom += originTop;\n                        bestHit = hit;\n                    }\n                }\n            }\n        }\n        return bestHit;\n    }\n}\nfunction isHitsEqual(hit0, hit1) {\n    if (!hit0 && !hit1) {\n        return true;\n    }\n    if (Boolean(hit0) !== Boolean(hit1)) {\n        return false;\n    }\n    return isDateSpansEqual(hit0.dateSpan, hit1.dateSpan);\n}\n\nfunction buildDatePointApiWithContext(dateSpan, context) {\n    let props = {};\n    for (let transform of context.pluginHooks.datePointTransforms) {\n        Object.assign(props, transform(dateSpan, context));\n    }\n    Object.assign(props, buildDatePointApi(dateSpan, context.dateEnv));\n    return props;\n}\nfunction buildDatePointApi(span, dateEnv) {\n    return {\n        date: dateEnv.toDate(span.range.start),\n        dateStr: dateEnv.formatIso(span.range.start, { omitTime: span.allDay }),\n        allDay: span.allDay,\n    };\n}\n\n/*\nMonitors when the user clicks on a specific date/time of a component.\nA pointerdown+pointerup on the same \"hit\" constitutes a click.\n*/\nclass DateClicking extends Interaction {\n    constructor(settings) {\n        super(settings);\n        this.handlePointerDown = (pev) => {\n            let { dragging } = this;\n            let downEl = pev.origEvent.target;\n            // do this in pointerdown (not dragend) because DOM might be mutated by the time dragend is fired\n            dragging.setIgnoreMove(!this.component.isValidDateDownEl(downEl));\n        };\n        // won't even fire if moving was ignored\n        this.handleDragEnd = (ev) => {\n            let { component } = this;\n            let { pointer } = this.dragging;\n            if (!pointer.wasTouchScroll) {\n                let { initialHit, finalHit } = this.hitDragging;\n                if (initialHit && finalHit && isHitsEqual(initialHit, finalHit)) {\n                    let { context } = component;\n                    let arg = Object.assign(Object.assign({}, buildDatePointApiWithContext(initialHit.dateSpan, context)), { dayEl: initialHit.dayEl, jsEvent: ev.origEvent, view: context.viewApi || context.calendarApi.view });\n                    context.emitter.trigger('dateClick', arg);\n                }\n            }\n        };\n        // we DO want to watch pointer moves because otherwise finalHit won't get populated\n        this.dragging = new FeaturefulElementDragging(settings.el);\n        this.dragging.autoScroller.isEnabled = false;\n        let hitDragging = this.hitDragging = new HitDragging(this.dragging, interactionSettingsToStore(settings));\n        hitDragging.emitter.on('pointerdown', this.handlePointerDown);\n        hitDragging.emitter.on('dragend', this.handleDragEnd);\n    }\n    destroy() {\n        this.dragging.destroy();\n    }\n}\n\n/*\nTracks when the user selects a portion of time of a component,\nconstituted by a drag over date cells, with a possible delay at the beginning of the drag.\n*/\nclass DateSelecting extends Interaction {\n    constructor(settings) {\n        super(settings);\n        this.dragSelection = null;\n        this.handlePointerDown = (ev) => {\n            let { component, dragging } = this;\n            let { options } = component.context;\n            let canSelect = options.selectable &&\n                component.isValidDateDownEl(ev.origEvent.target);\n            // don't bother to watch expensive moves if component won't do selection\n            dragging.setIgnoreMove(!canSelect);\n            // if touch, require user to hold down\n            dragging.delay = ev.isTouch ? getComponentTouchDelay$1(component) : null;\n        };\n        this.handleDragStart = (ev) => {\n            this.component.context.calendarApi.unselect(ev); // unselect previous selections\n        };\n        this.handleHitUpdate = (hit, isFinal) => {\n            let { context } = this.component;\n            let dragSelection = null;\n            let isInvalid = false;\n            if (hit) {\n                let initialHit = this.hitDragging.initialHit;\n                let disallowed = hit.componentId === initialHit.componentId\n                    && this.isHitComboAllowed\n                    && !this.isHitComboAllowed(initialHit, hit);\n                if (!disallowed) {\n                    dragSelection = joinHitsIntoSelection(initialHit, hit, context.pluginHooks.dateSelectionTransformers);\n                }\n                if (!dragSelection || !isDateSelectionValid(dragSelection, hit.dateProfile, context)) {\n                    isInvalid = true;\n                    dragSelection = null;\n                }\n            }\n            if (dragSelection) {\n                context.dispatch({ type: 'SELECT_DATES', selection: dragSelection });\n            }\n            else if (!isFinal) { // only unselect if moved away while dragging\n                context.dispatch({ type: 'UNSELECT_DATES' });\n            }\n            if (!isInvalid) {\n                enableCursor();\n            }\n            else {\n                disableCursor();\n            }\n            if (!isFinal) {\n                this.dragSelection = dragSelection; // only clear if moved away from all hits while dragging\n            }\n        };\n        this.handlePointerUp = (pev) => {\n            if (this.dragSelection) {\n                // selection is already rendered, so just need to report selection\n                triggerDateSelect(this.dragSelection, pev, this.component.context);\n                this.dragSelection = null;\n            }\n        };\n        let { component } = settings;\n        let { options } = component.context;\n        let dragging = this.dragging = new FeaturefulElementDragging(settings.el);\n        dragging.touchScrollAllowed = false;\n        dragging.minDistance = options.selectMinDistance || 0;\n        dragging.autoScroller.isEnabled = options.dragScroll;\n        let hitDragging = this.hitDragging = new HitDragging(this.dragging, interactionSettingsToStore(settings));\n        hitDragging.emitter.on('pointerdown', this.handlePointerDown);\n        hitDragging.emitter.on('dragstart', this.handleDragStart);\n        hitDragging.emitter.on('hitupdate', this.handleHitUpdate);\n        hitDragging.emitter.on('pointerup', this.handlePointerUp);\n    }\n    destroy() {\n        this.dragging.destroy();\n    }\n}\nfunction getComponentTouchDelay$1(component) {\n    let { options } = component.context;\n    let delay = options.selectLongPressDelay;\n    if (delay == null) {\n        delay = options.longPressDelay;\n    }\n    return delay;\n}\nfunction joinHitsIntoSelection(hit0, hit1, dateSelectionTransformers) {\n    let dateSpan0 = hit0.dateSpan;\n    let dateSpan1 = hit1.dateSpan;\n    let ms = [\n        dateSpan0.range.start,\n        dateSpan0.range.end,\n        dateSpan1.range.start,\n        dateSpan1.range.end,\n    ];\n    ms.sort(compareNumbers);\n    let props = {};\n    for (let transformer of dateSelectionTransformers) {\n        let res = transformer(hit0, hit1);\n        if (res === false) {\n            return null;\n        }\n        if (res) {\n            Object.assign(props, res);\n        }\n    }\n    props.range = { start: ms[0], end: ms[3] };\n    props.allDay = dateSpan0.allDay;\n    return props;\n}\n\nclass EventDragging extends Interaction {\n    constructor(settings) {\n        super(settings);\n        // internal state\n        this.subjectEl = null;\n        this.subjectSeg = null; // the seg being selected/dragged\n        this.isDragging = false;\n        this.eventRange = null;\n        this.relevantEvents = null; // the events being dragged\n        this.receivingContext = null;\n        this.validMutation = null;\n        this.mutatedRelevantEvents = null;\n        this.handlePointerDown = (ev) => {\n            let origTarget = ev.origEvent.target;\n            let { component, dragging } = this;\n            let { mirror } = dragging;\n            let { options } = component.context;\n            let initialContext = component.context;\n            this.subjectEl = ev.subjectEl;\n            let subjectSeg = this.subjectSeg = getElSeg(ev.subjectEl);\n            let eventRange = this.eventRange = subjectSeg.eventRange;\n            let eventInstanceId = eventRange.instance.instanceId;\n            this.relevantEvents = getRelevantEvents(initialContext.getCurrentData().eventStore, eventInstanceId);\n            dragging.minDistance = ev.isTouch ? 0 : options.eventDragMinDistance;\n            dragging.delay =\n                // only do a touch delay if touch and this event hasn't been selected yet\n                (ev.isTouch && eventInstanceId !== component.props.eventSelection) ?\n                    getComponentTouchDelay(component) :\n                    null;\n            if (options.fixedMirrorParent) {\n                mirror.parentNode = options.fixedMirrorParent;\n            }\n            else {\n                mirror.parentNode = elementClosest(origTarget, '.fc');\n            }\n            mirror.revertDuration = options.dragRevertDuration;\n            let isValid = component.isValidSegDownEl(origTarget) &&\n                !elementClosest(origTarget, '.fc-event-resizer'); // NOT on a resizer\n            dragging.setIgnoreMove(!isValid);\n            // disable dragging for elements that are resizable (ie, selectable)\n            // but are not draggable\n            this.isDragging = isValid &&\n                ev.subjectEl.classList.contains('fc-event-draggable');\n        };\n        this.handleDragStart = (ev) => {\n            let initialContext = this.component.context;\n            let eventRange = this.eventRange;\n            let eventInstanceId = eventRange.instance.instanceId;\n            if (ev.isTouch) {\n                // need to select a different event?\n                if (eventInstanceId !== this.component.props.eventSelection) {\n                    initialContext.dispatch({ type: 'SELECT_EVENT', eventInstanceId });\n                }\n            }\n            else {\n                // if now using mouse, but was previous touch interaction, clear selected event\n                initialContext.dispatch({ type: 'UNSELECT_EVENT' });\n            }\n            if (this.isDragging) {\n                initialContext.calendarApi.unselect(ev); // unselect *date* selection\n                initialContext.emitter.trigger('eventDragStart', {\n                    el: this.subjectEl,\n                    event: new EventImpl(initialContext, eventRange.def, eventRange.instance),\n                    jsEvent: ev.origEvent,\n                    view: initialContext.viewApi,\n                });\n            }\n        };\n        this.handleHitUpdate = (hit, isFinal) => {\n            if (!this.isDragging) {\n                return;\n            }\n            let relevantEvents = this.relevantEvents;\n            let initialHit = this.hitDragging.initialHit;\n            let initialContext = this.component.context;\n            // states based on new hit\n            let receivingContext = null;\n            let mutation = null;\n            let mutatedRelevantEvents = null;\n            let isInvalid = false;\n            let interaction = {\n                affectedEvents: relevantEvents,\n                mutatedEvents: createEmptyEventStore(),\n                isEvent: true,\n            };\n            if (hit) {\n                receivingContext = hit.context;\n                let receivingOptions = receivingContext.options;\n                if (initialContext === receivingContext ||\n                    (receivingOptions.editable && receivingOptions.droppable)) {\n                    mutation = computeEventMutation(initialHit, hit, this.eventRange.instance.range.start, receivingContext.getCurrentData().pluginHooks.eventDragMutationMassagers);\n                    if (mutation) {\n                        mutatedRelevantEvents = applyMutationToEventStore(relevantEvents, receivingContext.getCurrentData().eventUiBases, mutation, receivingContext);\n                        interaction.mutatedEvents = mutatedRelevantEvents;\n                        if (!isInteractionValid(interaction, hit.dateProfile, receivingContext)) {\n                            isInvalid = true;\n                            mutation = null;\n                            mutatedRelevantEvents = null;\n                            interaction.mutatedEvents = createEmptyEventStore();\n                        }\n                    }\n                }\n                else {\n                    receivingContext = null;\n                }\n            }\n            this.displayDrag(receivingContext, interaction);\n            if (!isInvalid) {\n                enableCursor();\n            }\n            else {\n                disableCursor();\n            }\n            if (!isFinal) {\n                if (initialContext === receivingContext && // TODO: write test for this\n                    isHitsEqual(initialHit, hit)) {\n                    mutation = null;\n                }\n                this.dragging.setMirrorNeedsRevert(!mutation);\n                // render the mirror if no already-rendered mirror\n                // TODO: wish we could somehow wait for dispatch to guarantee render\n                this.dragging.setMirrorIsVisible(!hit || !this.subjectEl.getRootNode().querySelector('.fc-event-mirror'));\n                // assign states based on new hit\n                this.receivingContext = receivingContext;\n                this.validMutation = mutation;\n                this.mutatedRelevantEvents = mutatedRelevantEvents;\n            }\n        };\n        this.handlePointerUp = () => {\n            if (!this.isDragging) {\n                this.cleanup(); // because handleDragEnd won't fire\n            }\n        };\n        this.handleDragEnd = (ev) => {\n            if (this.isDragging) {\n                let initialContext = this.component.context;\n                let initialView = initialContext.viewApi;\n                let { receivingContext, validMutation } = this;\n                let eventDef = this.eventRange.def;\n                let eventInstance = this.eventRange.instance;\n                let eventApi = new EventImpl(initialContext, eventDef, eventInstance);\n                let relevantEvents = this.relevantEvents;\n                let mutatedRelevantEvents = this.mutatedRelevantEvents;\n                let { finalHit } = this.hitDragging;\n                this.clearDrag(); // must happen after revert animation\n                initialContext.emitter.trigger('eventDragStop', {\n                    el: this.subjectEl,\n                    event: eventApi,\n                    jsEvent: ev.origEvent,\n                    view: initialView,\n                });\n                if (validMutation) {\n                    // dropped within same calendar\n                    if (receivingContext === initialContext) {\n                        let updatedEventApi = new EventImpl(initialContext, mutatedRelevantEvents.defs[eventDef.defId], eventInstance ? mutatedRelevantEvents.instances[eventInstance.instanceId] : null);\n                        initialContext.dispatch({\n                            type: 'MERGE_EVENTS',\n                            eventStore: mutatedRelevantEvents,\n                        });\n                        let eventChangeArg = {\n                            oldEvent: eventApi,\n                            event: updatedEventApi,\n                            relatedEvents: buildEventApis(mutatedRelevantEvents, initialContext, eventInstance),\n                            revert() {\n                                initialContext.dispatch({\n                                    type: 'MERGE_EVENTS',\n                                    eventStore: relevantEvents, // the pre-change data\n                                });\n                            },\n                        };\n                        let transformed = {};\n                        for (let transformer of initialContext.getCurrentData().pluginHooks.eventDropTransformers) {\n                            Object.assign(transformed, transformer(validMutation, initialContext));\n                        }\n                        initialContext.emitter.trigger('eventDrop', Object.assign(Object.assign(Object.assign({}, eventChangeArg), transformed), { el: ev.subjectEl, delta: validMutation.datesDelta, jsEvent: ev.origEvent, view: initialView }));\n                        initialContext.emitter.trigger('eventChange', eventChangeArg);\n                        // dropped in different calendar\n                    }\n                    else if (receivingContext) {\n                        let eventRemoveArg = {\n                            event: eventApi,\n                            relatedEvents: buildEventApis(relevantEvents, initialContext, eventInstance),\n                            revert() {\n                                initialContext.dispatch({\n                                    type: 'MERGE_EVENTS',\n                                    eventStore: relevantEvents,\n                                });\n                            },\n                        };\n                        initialContext.emitter.trigger('eventLeave', Object.assign(Object.assign({}, eventRemoveArg), { draggedEl: ev.subjectEl, view: initialView }));\n                        initialContext.dispatch({\n                            type: 'REMOVE_EVENTS',\n                            eventStore: relevantEvents,\n                        });\n                        initialContext.emitter.trigger('eventRemove', eventRemoveArg);\n                        let addedEventDef = mutatedRelevantEvents.defs[eventDef.defId];\n                        let addedEventInstance = mutatedRelevantEvents.instances[eventInstance.instanceId];\n                        let addedEventApi = new EventImpl(receivingContext, addedEventDef, addedEventInstance);\n                        receivingContext.dispatch({\n                            type: 'MERGE_EVENTS',\n                            eventStore: mutatedRelevantEvents,\n                        });\n                        let eventAddArg = {\n                            event: addedEventApi,\n                            relatedEvents: buildEventApis(mutatedRelevantEvents, receivingContext, addedEventInstance),\n                            revert() {\n                                receivingContext.dispatch({\n                                    type: 'REMOVE_EVENTS',\n                                    eventStore: mutatedRelevantEvents,\n                                });\n                            },\n                        };\n                        receivingContext.emitter.trigger('eventAdd', eventAddArg);\n                        if (ev.isTouch) {\n                            receivingContext.dispatch({\n                                type: 'SELECT_EVENT',\n                                eventInstanceId: eventInstance.instanceId,\n                            });\n                        }\n                        receivingContext.emitter.trigger('drop', Object.assign(Object.assign({}, buildDatePointApiWithContext(finalHit.dateSpan, receivingContext)), { draggedEl: ev.subjectEl, jsEvent: ev.origEvent, view: finalHit.context.viewApi }));\n                        receivingContext.emitter.trigger('eventReceive', Object.assign(Object.assign({}, eventAddArg), { draggedEl: ev.subjectEl, view: finalHit.context.viewApi }));\n                    }\n                }\n                else {\n                    initialContext.emitter.trigger('_noEventDrop');\n                }\n            }\n            this.cleanup();\n        };\n        let { component } = this;\n        let { options } = component.context;\n        let dragging = this.dragging = new FeaturefulElementDragging(settings.el);\n        dragging.pointer.selector = EventDragging.SELECTOR;\n        dragging.touchScrollAllowed = false;\n        dragging.autoScroller.isEnabled = options.dragScroll;\n        let hitDragging = this.hitDragging = new HitDragging(this.dragging, interactionSettingsStore);\n        hitDragging.useSubjectCenter = settings.useEventCenter;\n        hitDragging.emitter.on('pointerdown', this.handlePointerDown);\n        hitDragging.emitter.on('dragstart', this.handleDragStart);\n        hitDragging.emitter.on('hitupdate', this.handleHitUpdate);\n        hitDragging.emitter.on('pointerup', this.handlePointerUp);\n        hitDragging.emitter.on('dragend', this.handleDragEnd);\n    }\n    destroy() {\n        this.dragging.destroy();\n    }\n    // render a drag state on the next receivingCalendar\n    displayDrag(nextContext, state) {\n        let initialContext = this.component.context;\n        let prevContext = this.receivingContext;\n        // does the previous calendar need to be cleared?\n        if (prevContext && prevContext !== nextContext) {\n            // does the initial calendar need to be cleared?\n            // if so, don't clear all the way. we still need to to hide the affectedEvents\n            if (prevContext === initialContext) {\n                prevContext.dispatch({\n                    type: 'SET_EVENT_DRAG',\n                    state: {\n                        affectedEvents: state.affectedEvents,\n                        mutatedEvents: createEmptyEventStore(),\n                        isEvent: true,\n                    },\n                });\n                // completely clear the old calendar if it wasn't the initial\n            }\n            else {\n                prevContext.dispatch({ type: 'UNSET_EVENT_DRAG' });\n            }\n        }\n        if (nextContext) {\n            nextContext.dispatch({ type: 'SET_EVENT_DRAG', state });\n        }\n    }\n    clearDrag() {\n        let initialCalendar = this.component.context;\n        let { receivingContext } = this;\n        if (receivingContext) {\n            receivingContext.dispatch({ type: 'UNSET_EVENT_DRAG' });\n        }\n        // the initial calendar might have an dummy drag state from displayDrag\n        if (initialCalendar !== receivingContext) {\n            initialCalendar.dispatch({ type: 'UNSET_EVENT_DRAG' });\n        }\n    }\n    cleanup() {\n        this.subjectSeg = null;\n        this.isDragging = false;\n        this.eventRange = null;\n        this.relevantEvents = null;\n        this.receivingContext = null;\n        this.validMutation = null;\n        this.mutatedRelevantEvents = null;\n    }\n}\n// TODO: test this in IE11\n// QUESTION: why do we need it on the resizable???\nEventDragging.SELECTOR = '.fc-event-draggable, .fc-event-resizable';\nfunction computeEventMutation(hit0, hit1, eventInstanceStart, massagers) {\n    let dateSpan0 = hit0.dateSpan;\n    let dateSpan1 = hit1.dateSpan;\n    let date0 = dateSpan0.range.start;\n    let date1 = dateSpan1.range.start;\n    let standardProps = {};\n    if (dateSpan0.allDay !== dateSpan1.allDay) {\n        standardProps.allDay = dateSpan1.allDay;\n        standardProps.hasEnd = hit1.context.options.allDayMaintainDuration;\n        if (dateSpan1.allDay) {\n            // means date1 is already start-of-day,\n            // but date0 needs to be converted\n            date0 = startOfDay(eventInstanceStart);\n        }\n        else {\n            // Moving from allDate->timed\n            // Doesn't matter where on the event the drag began, mutate the event's start-date to date1\n            date0 = eventInstanceStart;\n        }\n    }\n    let delta = diffDates(date0, date1, hit0.context.dateEnv, hit0.componentId === hit1.componentId ?\n        hit0.largeUnit :\n        null);\n    if (delta.milliseconds) { // has hours/minutes/seconds\n        standardProps.allDay = false;\n    }\n    let mutation = {\n        datesDelta: delta,\n        standardProps,\n    };\n    for (let massager of massagers) {\n        massager(mutation, hit0, hit1);\n    }\n    return mutation;\n}\nfunction getComponentTouchDelay(component) {\n    let { options } = component.context;\n    let delay = options.eventLongPressDelay;\n    if (delay == null) {\n        delay = options.longPressDelay;\n    }\n    return delay;\n}\n\nclass EventResizing extends Interaction {\n    constructor(settings) {\n        super(settings);\n        // internal state\n        this.draggingSegEl = null;\n        this.draggingSeg = null; // TODO: rename to resizingSeg? subjectSeg?\n        this.eventRange = null;\n        this.relevantEvents = null;\n        this.validMutation = null;\n        this.mutatedRelevantEvents = null;\n        this.handlePointerDown = (ev) => {\n            let { component } = this;\n            let segEl = this.querySegEl(ev);\n            let seg = getElSeg(segEl);\n            let eventRange = this.eventRange = seg.eventRange;\n            this.dragging.minDistance = component.context.options.eventDragMinDistance;\n            // if touch, need to be working with a selected event\n            this.dragging.setIgnoreMove(!this.component.isValidSegDownEl(ev.origEvent.target) ||\n                (ev.isTouch && this.component.props.eventSelection !== eventRange.instance.instanceId));\n        };\n        this.handleDragStart = (ev) => {\n            let { context } = this.component;\n            let eventRange = this.eventRange;\n            this.relevantEvents = getRelevantEvents(context.getCurrentData().eventStore, this.eventRange.instance.instanceId);\n            let segEl = this.querySegEl(ev);\n            this.draggingSegEl = segEl;\n            this.draggingSeg = getElSeg(segEl);\n            context.calendarApi.unselect();\n            context.emitter.trigger('eventResizeStart', {\n                el: segEl,\n                event: new EventImpl(context, eventRange.def, eventRange.instance),\n                jsEvent: ev.origEvent,\n                view: context.viewApi,\n            });\n        };\n        this.handleHitUpdate = (hit, isFinal, ev) => {\n            let { context } = this.component;\n            let relevantEvents = this.relevantEvents;\n            let initialHit = this.hitDragging.initialHit;\n            let eventInstance = this.eventRange.instance;\n            let mutation = null;\n            let mutatedRelevantEvents = null;\n            let isInvalid = false;\n            let interaction = {\n                affectedEvents: relevantEvents,\n                mutatedEvents: createEmptyEventStore(),\n                isEvent: true,\n            };\n            if (hit) {\n                let disallowed = hit.componentId === initialHit.componentId\n                    && this.isHitComboAllowed\n                    && !this.isHitComboAllowed(initialHit, hit);\n                if (!disallowed) {\n                    mutation = computeMutation(initialHit, hit, ev.subjectEl.classList.contains('fc-event-resizer-start'), eventInstance.range);\n                }\n            }\n            if (mutation) {\n                mutatedRelevantEvents = applyMutationToEventStore(relevantEvents, context.getCurrentData().eventUiBases, mutation, context);\n                interaction.mutatedEvents = mutatedRelevantEvents;\n                if (!isInteractionValid(interaction, hit.dateProfile, context)) {\n                    isInvalid = true;\n                    mutation = null;\n                    mutatedRelevantEvents = null;\n                    interaction.mutatedEvents = null;\n                }\n            }\n            if (mutatedRelevantEvents) {\n                context.dispatch({\n                    type: 'SET_EVENT_RESIZE',\n                    state: interaction,\n                });\n            }\n            else {\n                context.dispatch({ type: 'UNSET_EVENT_RESIZE' });\n            }\n            if (!isInvalid) {\n                enableCursor();\n            }\n            else {\n                disableCursor();\n            }\n            if (!isFinal) {\n                if (mutation && isHitsEqual(initialHit, hit)) {\n                    mutation = null;\n                }\n                this.validMutation = mutation;\n                this.mutatedRelevantEvents = mutatedRelevantEvents;\n            }\n        };\n        this.handleDragEnd = (ev) => {\n            let { context } = this.component;\n            let eventDef = this.eventRange.def;\n            let eventInstance = this.eventRange.instance;\n            let eventApi = new EventImpl(context, eventDef, eventInstance);\n            let relevantEvents = this.relevantEvents;\n            let mutatedRelevantEvents = this.mutatedRelevantEvents;\n            context.emitter.trigger('eventResizeStop', {\n                el: this.draggingSegEl,\n                event: eventApi,\n                jsEvent: ev.origEvent,\n                view: context.viewApi,\n            });\n            if (this.validMutation) {\n                let updatedEventApi = new EventImpl(context, mutatedRelevantEvents.defs[eventDef.defId], eventInstance ? mutatedRelevantEvents.instances[eventInstance.instanceId] : null);\n                context.dispatch({\n                    type: 'MERGE_EVENTS',\n                    eventStore: mutatedRelevantEvents,\n                });\n                let eventChangeArg = {\n                    oldEvent: eventApi,\n                    event: updatedEventApi,\n                    relatedEvents: buildEventApis(mutatedRelevantEvents, context, eventInstance),\n                    revert() {\n                        context.dispatch({\n                            type: 'MERGE_EVENTS',\n                            eventStore: relevantEvents, // the pre-change events\n                        });\n                    },\n                };\n                context.emitter.trigger('eventResize', Object.assign(Object.assign({}, eventChangeArg), { el: this.draggingSegEl, startDelta: this.validMutation.startDelta || createDuration(0), endDelta: this.validMutation.endDelta || createDuration(0), jsEvent: ev.origEvent, view: context.viewApi }));\n                context.emitter.trigger('eventChange', eventChangeArg);\n            }\n            else {\n                context.emitter.trigger('_noEventResize');\n            }\n            // reset all internal state\n            this.draggingSeg = null;\n            this.relevantEvents = null;\n            this.validMutation = null;\n            // okay to keep eventInstance around. useful to set it in handlePointerDown\n        };\n        let { component } = settings;\n        let dragging = this.dragging = new FeaturefulElementDragging(settings.el);\n        dragging.pointer.selector = '.fc-event-resizer';\n        dragging.touchScrollAllowed = false;\n        dragging.autoScroller.isEnabled = component.context.options.dragScroll;\n        let hitDragging = this.hitDragging = new HitDragging(this.dragging, interactionSettingsToStore(settings));\n        hitDragging.emitter.on('pointerdown', this.handlePointerDown);\n        hitDragging.emitter.on('dragstart', this.handleDragStart);\n        hitDragging.emitter.on('hitupdate', this.handleHitUpdate);\n        hitDragging.emitter.on('dragend', this.handleDragEnd);\n    }\n    destroy() {\n        this.dragging.destroy();\n    }\n    querySegEl(ev) {\n        return elementClosest(ev.subjectEl, '.fc-event');\n    }\n}\nfunction computeMutation(hit0, hit1, isFromStart, instanceRange) {\n    let dateEnv = hit0.context.dateEnv;\n    let date0 = hit0.dateSpan.range.start;\n    let date1 = hit1.dateSpan.range.start;\n    let delta = diffDates(date0, date1, dateEnv, hit0.largeUnit);\n    if (isFromStart) {\n        if (dateEnv.add(instanceRange.start, delta) < instanceRange.end) {\n            return { startDelta: delta };\n        }\n    }\n    else if (dateEnv.add(instanceRange.end, delta) > instanceRange.start) {\n        return { endDelta: delta };\n    }\n    return null;\n}\n\nclass UnselectAuto {\n    constructor(context) {\n        this.context = context;\n        this.isRecentPointerDateSelect = false; // wish we could use a selector to detect date selection, but uses hit system\n        this.matchesCancel = false;\n        this.matchesEvent = false;\n        this.onSelect = (selectInfo) => {\n            if (selectInfo.jsEvent) {\n                this.isRecentPointerDateSelect = true;\n            }\n        };\n        this.onDocumentPointerDown = (pev) => {\n            let unselectCancel = this.context.options.unselectCancel;\n            let downEl = getEventTargetViaRoot(pev.origEvent);\n            this.matchesCancel = !!elementClosest(downEl, unselectCancel);\n            this.matchesEvent = !!elementClosest(downEl, EventDragging.SELECTOR); // interaction started on an event?\n        };\n        this.onDocumentPointerUp = (pev) => {\n            let { context } = this;\n            let { documentPointer } = this;\n            let calendarState = context.getCurrentData();\n            // touch-scrolling should never unfocus any type of selection\n            if (!documentPointer.wasTouchScroll) {\n                if (calendarState.dateSelection && // an existing date selection?\n                    !this.isRecentPointerDateSelect // a new pointer-initiated date selection since last onDocumentPointerUp?\n                ) {\n                    let unselectAuto = context.options.unselectAuto;\n                    if (unselectAuto && (!unselectAuto || !this.matchesCancel)) {\n                        context.calendarApi.unselect(pev);\n                    }\n                }\n                if (calendarState.eventSelection && // an existing event selected?\n                    !this.matchesEvent // interaction DIDN'T start on an event\n                ) {\n                    context.dispatch({ type: 'UNSELECT_EVENT' });\n                }\n            }\n            this.isRecentPointerDateSelect = false;\n        };\n        let documentPointer = this.documentPointer = new PointerDragging(document);\n        documentPointer.shouldIgnoreMove = true;\n        documentPointer.shouldWatchScroll = false;\n        documentPointer.emitter.on('pointerdown', this.onDocumentPointerDown);\n        documentPointer.emitter.on('pointerup', this.onDocumentPointerUp);\n        /*\n        TODO: better way to know about whether there was a selection with the pointer\n        */\n        context.emitter.on('select', this.onSelect);\n    }\n    destroy() {\n        this.context.emitter.off('select', this.onSelect);\n        this.documentPointer.destroy();\n    }\n}\n\nconst OPTION_REFINERS = {\n    fixedMirrorParent: identity,\n};\nconst LISTENER_REFINERS = {\n    dateClick: identity,\n    eventDragStart: identity,\n    eventDragStop: identity,\n    eventDrop: identity,\n    eventResizeStart: identity,\n    eventResizeStop: identity,\n    eventResize: identity,\n    drop: identity,\n    eventReceive: identity,\n    eventLeave: identity,\n};\n\n/*\nGiven an already instantiated draggable object for one-or-more elements,\nInterprets any dragging as an attempt to drag an events that lives outside\nof a calendar onto a calendar.\n*/\nclass ExternalElementDragging {\n    constructor(dragging, suppliedDragMeta) {\n        this.receivingContext = null;\n        this.droppableEvent = null; // will exist for all drags, even if create:false\n        this.suppliedDragMeta = null;\n        this.dragMeta = null;\n        this.handleDragStart = (ev) => {\n            this.dragMeta = this.buildDragMeta(ev.subjectEl);\n        };\n        this.handleHitUpdate = (hit, isFinal, ev) => {\n            let { dragging } = this.hitDragging;\n            let receivingContext = null;\n            let droppableEvent = null;\n            let isInvalid = false;\n            let interaction = {\n                affectedEvents: createEmptyEventStore(),\n                mutatedEvents: createEmptyEventStore(),\n                isEvent: this.dragMeta.create,\n            };\n            if (hit) {\n                receivingContext = hit.context;\n                if (this.canDropElOnCalendar(ev.subjectEl, receivingContext)) {\n                    droppableEvent = computeEventForDateSpan(hit.dateSpan, this.dragMeta, receivingContext);\n                    interaction.mutatedEvents = eventTupleToStore(droppableEvent);\n                    isInvalid = !isInteractionValid(interaction, hit.dateProfile, receivingContext);\n                    if (isInvalid) {\n                        interaction.mutatedEvents = createEmptyEventStore();\n                        droppableEvent = null;\n                    }\n                }\n            }\n            this.displayDrag(receivingContext, interaction);\n            // show mirror if no already-rendered mirror element OR if we are shutting down the mirror (?)\n            // TODO: wish we could somehow wait for dispatch to guarantee render\n            dragging.setMirrorIsVisible(isFinal || !droppableEvent || !document.querySelector('.fc-event-mirror'));\n            if (!isInvalid) {\n                enableCursor();\n            }\n            else {\n                disableCursor();\n            }\n            if (!isFinal) {\n                dragging.setMirrorNeedsRevert(!droppableEvent);\n                this.receivingContext = receivingContext;\n                this.droppableEvent = droppableEvent;\n            }\n        };\n        this.handleDragEnd = (pev) => {\n            let { receivingContext, droppableEvent } = this;\n            this.clearDrag();\n            if (receivingContext && droppableEvent) {\n                let finalHit = this.hitDragging.finalHit;\n                let finalView = finalHit.context.viewApi;\n                let dragMeta = this.dragMeta;\n                receivingContext.emitter.trigger('drop', Object.assign(Object.assign({}, buildDatePointApiWithContext(finalHit.dateSpan, receivingContext)), { draggedEl: pev.subjectEl, jsEvent: pev.origEvent, view: finalView }));\n                if (dragMeta.create) {\n                    let addingEvents = eventTupleToStore(droppableEvent);\n                    receivingContext.dispatch({\n                        type: 'MERGE_EVENTS',\n                        eventStore: addingEvents,\n                    });\n                    if (pev.isTouch) {\n                        receivingContext.dispatch({\n                            type: 'SELECT_EVENT',\n                            eventInstanceId: droppableEvent.instance.instanceId,\n                        });\n                    }\n                    // signal that an external event landed\n                    receivingContext.emitter.trigger('eventReceive', {\n                        event: new EventImpl(receivingContext, droppableEvent.def, droppableEvent.instance),\n                        relatedEvents: [],\n                        revert() {\n                            receivingContext.dispatch({\n                                type: 'REMOVE_EVENTS',\n                                eventStore: addingEvents,\n                            });\n                        },\n                        draggedEl: pev.subjectEl,\n                        view: finalView,\n                    });\n                }\n            }\n            this.receivingContext = null;\n            this.droppableEvent = null;\n        };\n        let hitDragging = this.hitDragging = new HitDragging(dragging, interactionSettingsStore);\n        hitDragging.requireInitial = false; // will start outside of a component\n        hitDragging.emitter.on('dragstart', this.handleDragStart);\n        hitDragging.emitter.on('hitupdate', this.handleHitUpdate);\n        hitDragging.emitter.on('dragend', this.handleDragEnd);\n        this.suppliedDragMeta = suppliedDragMeta;\n    }\n    buildDragMeta(subjectEl) {\n        if (typeof this.suppliedDragMeta === 'object') {\n            return parseDragMeta(this.suppliedDragMeta);\n        }\n        if (typeof this.suppliedDragMeta === 'function') {\n            return parseDragMeta(this.suppliedDragMeta(subjectEl));\n        }\n        return getDragMetaFromEl(subjectEl);\n    }\n    displayDrag(nextContext, state) {\n        let prevContext = this.receivingContext;\n        if (prevContext && prevContext !== nextContext) {\n            prevContext.dispatch({ type: 'UNSET_EVENT_DRAG' });\n        }\n        if (nextContext) {\n            nextContext.dispatch({ type: 'SET_EVENT_DRAG', state });\n        }\n    }\n    clearDrag() {\n        if (this.receivingContext) {\n            this.receivingContext.dispatch({ type: 'UNSET_EVENT_DRAG' });\n        }\n    }\n    canDropElOnCalendar(el, receivingContext) {\n        let dropAccept = receivingContext.options.dropAccept;\n        if (typeof dropAccept === 'function') {\n            return dropAccept.call(receivingContext.calendarApi, el);\n        }\n        if (typeof dropAccept === 'string' && dropAccept) {\n            return Boolean(elementMatches(el, dropAccept));\n        }\n        return true;\n    }\n}\n// Utils for computing event store from the DragMeta\n// ----------------------------------------------------------------------------------------------------\nfunction computeEventForDateSpan(dateSpan, dragMeta, context) {\n    let defProps = Object.assign({}, dragMeta.leftoverProps);\n    for (let transform of context.pluginHooks.externalDefTransforms) {\n        Object.assign(defProps, transform(dateSpan, dragMeta));\n    }\n    let { refined, extra } = refineEventDef(defProps, context);\n    let def = parseEventDef(refined, extra, dragMeta.sourceId, dateSpan.allDay, context.options.forceEventDuration || Boolean(dragMeta.duration), // hasEnd\n    context);\n    let start = dateSpan.range.start;\n    // only rely on time info if drop zone is all-day,\n    // otherwise, we already know the time\n    if (dateSpan.allDay && dragMeta.startTime) {\n        start = context.dateEnv.add(start, dragMeta.startTime);\n    }\n    let end = dragMeta.duration ?\n        context.dateEnv.add(start, dragMeta.duration) :\n        getDefaultEventEnd(dateSpan.allDay, start, context);\n    let instance = createEventInstance(def.defId, { start, end });\n    return { def, instance };\n}\n// Utils for extracting data from element\n// ----------------------------------------------------------------------------------------------------\nfunction getDragMetaFromEl(el) {\n    let str = getEmbeddedElData(el, 'event');\n    let obj = str ?\n        JSON.parse(str) :\n        { create: false }; // if no embedded data, assume no event creation\n    return parseDragMeta(obj);\n}\nconfig.dataAttrPrefix = '';\nfunction getEmbeddedElData(el, name) {\n    let prefix = config.dataAttrPrefix;\n    let prefixedName = (prefix ? prefix + '-' : '') + name;\n    return el.getAttribute('data-' + prefixedName) || '';\n}\n\n/*\nMakes an element (that is *external* to any calendar) draggable.\nCan pass in data that determines how an event will be created when dropped onto a calendar.\nLeverages FullCalendar's internal drag-n-drop functionality WITHOUT a third-party drag system.\n*/\nclass ExternalDraggable {\n    constructor(el, settings = {}) {\n        this.handlePointerDown = (ev) => {\n            let { dragging } = this;\n            let { minDistance, longPressDelay } = this.settings;\n            dragging.minDistance =\n                minDistance != null ?\n                    minDistance :\n                    (ev.isTouch ? 0 : BASE_OPTION_DEFAULTS.eventDragMinDistance);\n            dragging.delay =\n                ev.isTouch ? // TODO: eventually read eventLongPressDelay instead vvv\n                    (longPressDelay != null ? longPressDelay : BASE_OPTION_DEFAULTS.longPressDelay) :\n                    0;\n        };\n        this.handleDragStart = (ev) => {\n            if (ev.isTouch &&\n                this.dragging.delay &&\n                ev.subjectEl.classList.contains('fc-event')) {\n                this.dragging.mirror.getMirrorEl().classList.add('fc-event-selected');\n            }\n        };\n        this.settings = settings;\n        let dragging = this.dragging = new FeaturefulElementDragging(el);\n        dragging.touchScrollAllowed = false;\n        if (settings.itemSelector != null) {\n            dragging.pointer.selector = settings.itemSelector;\n        }\n        if (settings.appendTo != null) {\n            dragging.mirror.parentNode = settings.appendTo; // TODO: write tests\n        }\n        dragging.emitter.on('pointerdown', this.handlePointerDown);\n        dragging.emitter.on('dragstart', this.handleDragStart);\n        new ExternalElementDragging(dragging, settings.eventData); // eslint-disable-line no-new\n    }\n    destroy() {\n        this.dragging.destroy();\n    }\n}\n\n/*\nDetects when a *THIRD-PARTY* drag-n-drop system interacts with elements.\nThe third-party system is responsible for drawing the visuals effects of the drag.\nThis class simply monitors for pointer movements and fires events.\nIt also has the ability to hide the moving element (the \"mirror\") during the drag.\n*/\nclass InferredElementDragging extends ElementDragging {\n    constructor(containerEl) {\n        super(containerEl);\n        this.shouldIgnoreMove = false;\n        this.mirrorSelector = '';\n        this.currentMirrorEl = null;\n        this.handlePointerDown = (ev) => {\n            this.emitter.trigger('pointerdown', ev);\n            if (!this.shouldIgnoreMove) {\n                // fire dragstart right away. does not support delay or min-distance\n                this.emitter.trigger('dragstart', ev);\n            }\n        };\n        this.handlePointerMove = (ev) => {\n            if (!this.shouldIgnoreMove) {\n                this.emitter.trigger('dragmove', ev);\n            }\n        };\n        this.handlePointerUp = (ev) => {\n            this.emitter.trigger('pointerup', ev);\n            if (!this.shouldIgnoreMove) {\n                // fire dragend right away. does not support a revert animation\n                this.emitter.trigger('dragend', ev);\n            }\n        };\n        let pointer = this.pointer = new PointerDragging(containerEl);\n        pointer.emitter.on('pointerdown', this.handlePointerDown);\n        pointer.emitter.on('pointermove', this.handlePointerMove);\n        pointer.emitter.on('pointerup', this.handlePointerUp);\n    }\n    destroy() {\n        this.pointer.destroy();\n    }\n    setIgnoreMove(bool) {\n        this.shouldIgnoreMove = bool;\n    }\n    setMirrorIsVisible(bool) {\n        if (bool) {\n            // restore a previously hidden element.\n            // use the reference in case the selector class has already been removed.\n            if (this.currentMirrorEl) {\n                this.currentMirrorEl.style.visibility = '';\n                this.currentMirrorEl = null;\n            }\n        }\n        else {\n            let mirrorEl = this.mirrorSelector\n                // TODO: somehow query FullCalendars WITHIN shadow-roots\n                ? document.querySelector(this.mirrorSelector)\n                : null;\n            if (mirrorEl) {\n                this.currentMirrorEl = mirrorEl;\n                mirrorEl.style.visibility = 'hidden';\n            }\n        }\n    }\n}\n\n/*\nBridges third-party drag-n-drop systems with FullCalendar.\nMust be instantiated and destroyed by caller.\n*/\nclass ThirdPartyDraggable {\n    constructor(containerOrSettings, settings) {\n        let containerEl = document;\n        if (\n        // wish we could just test instanceof EventTarget, but doesn't work in IE11\n        containerOrSettings === document ||\n            containerOrSettings instanceof Element) {\n            containerEl = containerOrSettings;\n            settings = settings || {};\n        }\n        else {\n            settings = (containerOrSettings || {});\n        }\n        let dragging = this.dragging = new InferredElementDragging(containerEl);\n        if (typeof settings.itemSelector === 'string') {\n            dragging.pointer.selector = settings.itemSelector;\n        }\n        else if (containerEl === document) {\n            dragging.pointer.selector = '[data-event]';\n        }\n        if (typeof settings.mirrorSelector === 'string') {\n            dragging.mirrorSelector = settings.mirrorSelector;\n        }\n        let externalDragging = new ExternalElementDragging(dragging, settings.eventData);\n        // The hit-detection system requires that the dnd-mirror-element be pointer-events:none,\n        // but this can't be guaranteed for third-party draggables, so disable\n        externalDragging.hitDragging.disablePointCheck = true;\n    }\n    destroy() {\n        this.dragging.destroy();\n    }\n}\n\nvar index = createPlugin({\n    name: '@fullcalendar/interaction',\n    componentInteractions: [DateClicking, DateSelecting, EventDragging, EventResizing],\n    calendarInteractions: [UnselectAuto],\n    elementDraggingImpl: FeaturefulElementDragging,\n    optionRefiners: OPTION_REFINERS,\n    listenerRefiners: LISTENER_REFINERS,\n});\n\nexport { ExternalDraggable as Draggable, ThirdPartyDraggable, index as default };\n"], "mappings": "AAAA,SAASA,YAAY,QAAQ,6BAA6B;AAC1D,SAASC,MAAM,EAAEC,OAAO,EAAEC,cAAc,EAAEC,UAAU,EAAEC,kBAAkB,EAAEC,aAAa,EAAEC,gBAAgB,EAAEC,uBAAuB,EAAEC,gBAAgB,EAAEC,sBAAsB,EAAEC,eAAe,EAAEC,gBAAgB,EAAEC,kBAAkB,EAAEC,cAAc,EAAEC,gBAAgB,EAAEC,WAAW,EAAEC,kBAAkB,EAAEC,eAAe,EAAEC,cAAc,EAAEC,cAAc,EAAEC,aAAa,EAAEC,UAAU,EAAEC,OAAO,EAAEC,kBAAkB,EAAEC,gBAAgB,EAAEC,WAAW,EAAEC,0BAA0B,EAAEC,oBAAoB,EAAEC,YAAY,EAAEC,aAAa,EAAEC,iBAAiB,EAAEC,cAAc,EAAEC,QAAQ,EAAEC,iBAAiB,EAAEC,SAAS,EAAEC,qBAAqB,EAAEC,yBAAyB,EAAEC,kBAAkB,EAAEC,cAAc,EAAEC,wBAAwB,EAAEC,UAAU,EAAEC,SAAS,EAAEC,cAAc,EAAEC,qBAAqB,EAAEC,QAAQ,EAAEC,iBAAiB,EAAEC,aAAa,EAAEC,cAAc,EAAEC,cAAc,EAAEC,aAAa,EAAEC,kBAAkB,EAAEC,mBAAmB,EAAEC,oBAAoB,QAAQ,gCAAgC;AAEx7BpD,MAAM,CAACqD,oBAAoB,GAAG,GAAG;AACjC,IAAIC,gBAAgB,GAAG,CAAC;AACxB,IAAIC,WAAW,GAAG,CAAC;AACnB,IAAIC,0BAA0B,GAAG,KAAK;AACtC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,eAAe,CAAC;EAClBC,WAAWA,CAACC,WAAW,EAAE;IACrB,IAAI,CAACC,SAAS,GAAG,IAAI;IACrB;IACA,IAAI,CAACC,QAAQ,GAAG,EAAE,CAAC,CAAC;IACpB,IAAI,CAACC,cAAc,GAAG,EAAE;IACxB,IAAI,CAACC,gBAAgB,GAAG,KAAK;IAC7B,IAAI,CAACC,iBAAiB,GAAG,IAAI,CAAC,CAAC;IAC/B;IACA,IAAI,CAACC,UAAU,GAAG,KAAK;IACvB,IAAI,CAACC,eAAe,GAAG,KAAK;IAC5B,IAAI,CAACC,cAAc,GAAG,KAAK;IAC3B;IACA;IACA,IAAI,CAACC,eAAe,GAAIC,EAAE,IAAK;MAC3B,IAAI,CAAC,IAAI,CAACC,iBAAiB,CAAC,CAAC,IACzBC,oBAAoB,CAACF,EAAE,CAAC,IACxB,IAAI,CAACG,QAAQ,CAACH,EAAE,CAAC,EAAE;QACnB,IAAII,GAAG,GAAG,IAAI,CAACC,oBAAoB,CAACL,EAAE,EAAE,IAAI,CAAC;QAC7C,IAAI,CAACM,OAAO,CAACC,OAAO,CAAC,aAAa,EAAEH,GAAG,CAAC;QACxC,IAAI,CAACI,eAAe,CAACJ,GAAG,CAAC;QACzB,IAAI,CAAC,IAAI,CAACV,gBAAgB,EAAE;UACxBe,QAAQ,CAACC,gBAAgB,CAAC,WAAW,EAAE,IAAI,CAACC,eAAe,CAAC;QAChE;QACAF,QAAQ,CAACC,gBAAgB,CAAC,SAAS,EAAE,IAAI,CAACE,aAAa,CAAC;MAC5D;IACJ,CAAC;IACD,IAAI,CAACD,eAAe,GAAIX,EAAE,IAAK;MAC3B,IAAII,GAAG,GAAG,IAAI,CAACC,oBAAoB,CAACL,EAAE,CAAC;MACvC,IAAI,CAACa,YAAY,CAACT,GAAG,CAAC;MACtB,IAAI,CAACE,OAAO,CAACC,OAAO,CAAC,aAAa,EAAEH,GAAG,CAAC;IAC5C,CAAC;IACD,IAAI,CAACQ,aAAa,GAAIZ,EAAE,IAAK;MACzBS,QAAQ,CAACK,mBAAmB,CAAC,WAAW,EAAE,IAAI,CAACH,eAAe,CAAC;MAC/DF,QAAQ,CAACK,mBAAmB,CAAC,SAAS,EAAE,IAAI,CAACF,aAAa,CAAC;MAC3D,IAAI,CAACN,OAAO,CAACC,OAAO,CAAC,WAAW,EAAE,IAAI,CAACF,oBAAoB,CAACL,EAAE,CAAC,CAAC;MAChE,IAAI,CAACe,OAAO,CAAC,CAAC,CAAC,CAAC;IACpB,CAAC;IACD;IACA;IACA,IAAI,CAACC,gBAAgB,GAAIhB,EAAE,IAAK;MAC5B,IAAI,IAAI,CAACG,QAAQ,CAACH,EAAE,CAAC,EAAE;QACnB,IAAI,CAACH,eAAe,GAAG,IAAI;QAC3B,IAAIO,GAAG,GAAG,IAAI,CAACa,oBAAoB,CAACjB,EAAE,EAAE,IAAI,CAAC;QAC7C,IAAI,CAACM,OAAO,CAACC,OAAO,CAAC,aAAa,EAAEH,GAAG,CAAC;QACxC,IAAI,CAACI,eAAe,CAACJ,GAAG,CAAC;QACzB;QACA;QACA,IAAIc,QAAQ,GAAGlB,EAAE,CAACmB,MAAM;QACxB,IAAI,CAAC,IAAI,CAACzB,gBAAgB,EAAE;UACxBwB,QAAQ,CAACR,gBAAgB,CAAC,WAAW,EAAE,IAAI,CAACU,eAAe,CAAC;QAChE;QACAF,QAAQ,CAACR,gBAAgB,CAAC,UAAU,EAAE,IAAI,CAACW,cAAc,CAAC;QAC1DH,QAAQ,CAACR,gBAAgB,CAAC,aAAa,EAAE,IAAI,CAACW,cAAc,CAAC,CAAC,CAAC;QAC/D;QACA;QACA;QACAC,MAAM,CAACZ,gBAAgB,CAAC,QAAQ,EAAE,IAAI,CAACa,iBAAiB,EAAE,IAAI,CAAC;MACnE;IACJ,CAAC;IACD,IAAI,CAACH,eAAe,GAAIpB,EAAE,IAAK;MAC3B,IAAII,GAAG,GAAG,IAAI,CAACa,oBAAoB,CAACjB,EAAE,CAAC;MACvC,IAAI,CAACa,YAAY,CAACT,GAAG,CAAC;MACtB,IAAI,CAACE,OAAO,CAACC,OAAO,CAAC,aAAa,EAAEH,GAAG,CAAC;IAC5C,CAAC;IACD,IAAI,CAACiB,cAAc,GAAIrB,EAAE,IAAK;MAC1B,IAAI,IAAI,CAACJ,UAAU,EAAE;QAAE;QACnB,IAAIsB,QAAQ,GAAGlB,EAAE,CAACmB,MAAM;QACxBD,QAAQ,CAACJ,mBAAmB,CAAC,WAAW,EAAE,IAAI,CAACM,eAAe,CAAC;QAC/DF,QAAQ,CAACJ,mBAAmB,CAAC,UAAU,EAAE,IAAI,CAACO,cAAc,CAAC;QAC7DH,QAAQ,CAACJ,mBAAmB,CAAC,aAAa,EAAE,IAAI,CAACO,cAAc,CAAC;QAChEC,MAAM,CAACR,mBAAmB,CAAC,QAAQ,EAAE,IAAI,CAACS,iBAAiB,EAAE,IAAI,CAAC,CAAC,CAAC;QACpE,IAAI,CAACjB,OAAO,CAACC,OAAO,CAAC,WAAW,EAAE,IAAI,CAACU,oBAAoB,CAACjB,EAAE,CAAC,CAAC;QAChE,IAAI,CAACe,OAAO,CAAC,CAAC,CAAC,CAAC;QAChB,IAAI,CAAClB,eAAe,GAAG,KAAK;QAC5B2B,kBAAkB,CAAC,CAAC;MACxB;IACJ,CAAC;IACD,IAAI,CAACD,iBAAiB,GAAG,MAAM;MAC3B,IAAI,CAACzB,cAAc,GAAG,IAAI;IAC9B,CAAC;IACD,IAAI,CAAC2B,YAAY,GAAIzB,EAAE,IAAK;MACxB,IAAI,CAAC,IAAI,CAACN,gBAAgB,EAAE;QACxB,IAAIgC,KAAK,GAAIJ,MAAM,CAACK,OAAO,GAAG,IAAI,CAACC,WAAW,GAAI,IAAI,CAACC,SAAS;QAChE,IAAIC,KAAK,GAAIR,MAAM,CAACS,OAAO,GAAG,IAAI,CAACC,WAAW,GAAI,IAAI,CAACC,SAAS;QAChE,IAAI,CAAC3B,OAAO,CAACC,OAAO,CAAC,aAAa,EAAE;UAChC2B,SAAS,EAAElC,EAAE;UACbmC,OAAO,EAAE,IAAI,CAACtC,eAAe;UAC7BN,SAAS,EAAE,IAAI,CAACA,SAAS;UACzBmC,KAAK;UACLI,KAAK;UACLM,MAAM,EAAEV,KAAK,GAAG,IAAI,CAACW,SAAS;UAC9BC,MAAM,EAAER,KAAK,GAAG,IAAI,CAACS;QACzB,CAAC,CAAC;MACN;IACJ,CAAC;IACD,IAAI,CAACjD,WAAW,GAAGA,WAAW;IAC9B,IAAI,CAACgB,OAAO,GAAG,IAAI1E,OAAO,CAAC,CAAC;IAC5B0D,WAAW,CAACoB,gBAAgB,CAAC,WAAW,EAAE,IAAI,CAACX,eAAe,CAAC;IAC/DT,WAAW,CAACoB,gBAAgB,CAAC,YAAY,EAAE,IAAI,CAACM,gBAAgB,EAAE;MAAEwB,OAAO,EAAE;IAAK,CAAC,CAAC;IACpFC,eAAe,CAAC,CAAC;EACrB;EACAC,OAAOA,CAAA,EAAG;IACN,IAAI,CAACpD,WAAW,CAACwB,mBAAmB,CAAC,WAAW,EAAE,IAAI,CAACf,eAAe,CAAC;IACvE,IAAI,CAACT,WAAW,CAACwB,mBAAmB,CAAC,YAAY,EAAE,IAAI,CAACE,gBAAgB,EAAE;MAAEwB,OAAO,EAAE;IAAK,CAAC,CAAC;IAC5FG,iBAAiB,CAAC,CAAC;EACvB;EACAxC,QAAQA,CAACH,EAAE,EAAE;IACT,IAAIT,SAAS,GAAG,IAAI,CAACqD,cAAc,CAAC5C,EAAE,CAAC;IACvC,IAAI6C,MAAM,GAAG7C,EAAE,CAACmB,MAAM;IACtB,IAAI5B,SAAS,KACR,CAAC,IAAI,CAACE,cAAc,IAAI5D,cAAc,CAACgH,MAAM,EAAE,IAAI,CAACpD,cAAc,CAAC,CAAC,EAAE;MACvE,IAAI,CAACF,SAAS,GAAGA,SAAS;MAC1B,IAAI,CAACK,UAAU,GAAG,IAAI,CAAC,CAAC;MACxB,IAAI,CAACE,cAAc,GAAG,KAAK;MAC3B,OAAO,IAAI;IACf;IACA,OAAO,KAAK;EAChB;EACAiB,OAAOA,CAAA,EAAG;IACN5B,0BAA0B,GAAG,KAAK;IAClC,IAAI,CAACS,UAAU,GAAG,KAAK;IACvB,IAAI,CAACL,SAAS,GAAG,IAAI;IACrB;IACA,IAAI,CAACuD,kBAAkB,CAAC,CAAC;EAC7B;EACAF,cAAcA,CAAC5C,EAAE,EAAE;IACf,IAAI,IAAI,CAACR,QAAQ,EAAE;MACf,OAAO3D,cAAc,CAACmE,EAAE,CAACmB,MAAM,EAAE,IAAI,CAAC3B,QAAQ,CAAC;IACnD;IACA,OAAO,IAAI,CAACF,WAAW;EAC3B;EACAW,iBAAiBA,CAAA,EAAG;IAChB,OAAOhB,gBAAgB,IAAI,IAAI,CAACY,eAAe;EACnD;EACA;EACAkD,iBAAiBA,CAAA,EAAG;IAChB,IAAI,IAAI,CAACnD,UAAU,EAAE;MACjBT,0BAA0B,GAAG,IAAI;IACrC;EACJ;EACA;EACA;EACAqB,eAAeA,CAACR,EAAE,EAAE;IAChB,IAAI,IAAI,CAACL,iBAAiB,EAAE;MACxB,IAAI,CAACkB,YAAY,CAACb,EAAE,CAAC;MACrBsB,MAAM,CAACZ,gBAAgB,CAAC,QAAQ,EAAE,IAAI,CAACe,YAAY,EAAE,IAAI,CAAC,CAAC,CAAC;IAChE;EACJ;EACAZ,YAAYA,CAACb,EAAE,EAAE;IACb,IAAI,IAAI,CAACL,iBAAiB,EAAE;MACxB,IAAI,CAACkC,SAAS,GAAG7B,EAAE,CAAC0B,KAAK;MACzB,IAAI,CAACO,SAAS,GAAGjC,EAAE,CAAC8B,KAAK;MACzB,IAAI,CAACF,WAAW,GAAGN,MAAM,CAACK,OAAO;MACjC,IAAI,CAACK,WAAW,GAAGV,MAAM,CAACS,OAAO;IACrC;EACJ;EACAe,kBAAkBA,CAAA,EAAG;IACjB,IAAI,IAAI,CAACnD,iBAAiB,EAAE;MACxB2B,MAAM,CAACR,mBAAmB,CAAC,QAAQ,EAAE,IAAI,CAACW,YAAY,EAAE,IAAI,CAAC,CAAC,CAAC;IACnE;EACJ;EACA;EACA;EACApB,oBAAoBA,CAACL,EAAE,EAAEgD,OAAO,EAAE;IAC9B,IAAIZ,MAAM,GAAG,CAAC;IACd,IAAIE,MAAM,GAAG,CAAC;IACd;IACA,IAAIU,OAAO,EAAE;MACT,IAAI,CAACX,SAAS,GAAGrC,EAAE,CAAC0B,KAAK;MACzB,IAAI,CAACa,SAAS,GAAGvC,EAAE,CAAC8B,KAAK;IAC7B,CAAC,MACI;MACDM,MAAM,GAAGpC,EAAE,CAAC0B,KAAK,GAAG,IAAI,CAACW,SAAS;MAClCC,MAAM,GAAGtC,EAAE,CAAC8B,KAAK,GAAG,IAAI,CAACS,SAAS;IACtC;IACA,OAAO;MACHL,SAAS,EAAElC,EAAE;MACbmC,OAAO,EAAE,KAAK;MACd5C,SAAS,EAAE,IAAI,CAACA,SAAS;MACzBmC,KAAK,EAAE1B,EAAE,CAAC0B,KAAK;MACfI,KAAK,EAAE9B,EAAE,CAAC8B,KAAK;MACfM,MAAM;MACNE;IACJ,CAAC;EACL;EACArB,oBAAoBA,CAACjB,EAAE,EAAEgD,OAAO,EAAE;IAC9B,IAAIC,OAAO,GAAGjD,EAAE,CAACiD,OAAO;IACxB,IAAIvB,KAAK;IACT,IAAII,KAAK;IACT,IAAIM,MAAM,GAAG,CAAC;IACd,IAAIE,MAAM,GAAG,CAAC;IACd;IACA;IACA,IAAIW,OAAO,IAAIA,OAAO,CAACC,MAAM,EAAE;MAC3BxB,KAAK,GAAGuB,OAAO,CAAC,CAAC,CAAC,CAACvB,KAAK;MACxBI,KAAK,GAAGmB,OAAO,CAAC,CAAC,CAAC,CAACnB,KAAK;IAC5B,CAAC,MACI;MACDJ,KAAK,GAAG1B,EAAE,CAAC0B,KAAK;MAChBI,KAAK,GAAG9B,EAAE,CAAC8B,KAAK;IACpB;IACA;IACA,IAAIkB,OAAO,EAAE;MACT,IAAI,CAACX,SAAS,GAAGX,KAAK;MACtB,IAAI,CAACa,SAAS,GAAGT,KAAK;IAC1B,CAAC,MACI;MACDM,MAAM,GAAGV,KAAK,GAAG,IAAI,CAACW,SAAS;MAC/BC,MAAM,GAAGR,KAAK,GAAG,IAAI,CAACS,SAAS;IACnC;IACA,OAAO;MACHL,SAAS,EAAElC,EAAE;MACbmC,OAAO,EAAE,IAAI;MACb5C,SAAS,EAAE,IAAI,CAACA,SAAS;MACzBmC,KAAK;MACLI,KAAK;MACLM,MAAM;MACNE;IACJ,CAAC;EACL;AACJ;AACA;AACA,SAASpC,oBAAoBA,CAACF,EAAE,EAAE;EAC9B,OAAOA,EAAE,CAACmD,MAAM,KAAK,CAAC,IAAI,CAACnD,EAAE,CAACoD,OAAO;AACzC;AACA;AACA;AACA,SAAS5B,kBAAkBA,CAAA,EAAG;EAC1BvC,gBAAgB,IAAI,CAAC;EACrBoE,UAAU,CAAC,MAAM;IACbpE,gBAAgB,IAAI,CAAC;EACzB,CAAC,EAAEtD,MAAM,CAACqD,oBAAoB,CAAC;AACnC;AACA;AACA;AACA,SAASyD,eAAeA,CAAA,EAAG;EACvBvD,WAAW,IAAI,CAAC;EAChB,IAAIA,WAAW,KAAK,CAAC,EAAE;IACnBoC,MAAM,CAACZ,gBAAgB,CAAC,WAAW,EAAE4C,iBAAiB,EAAE;MAAEd,OAAO,EAAE;IAAM,CAAC,CAAC;EAC/E;AACJ;AACA,SAASG,iBAAiBA,CAAA,EAAG;EACzBzD,WAAW,IAAI,CAAC;EAChB,IAAI,CAACA,WAAW,EAAE;IACdoC,MAAM,CAACR,mBAAmB,CAAC,WAAW,EAAEwC,iBAAiB,EAAE;MAAEd,OAAO,EAAE;IAAM,CAAC,CAAC;EAClF;AACJ;AACA,SAASc,iBAAiBA,CAACtD,EAAE,EAAE;EAC3B,IAAIb,0BAA0B,EAAE;IAC5Ba,EAAE,CAACuD,cAAc,CAAC,CAAC;EACvB;AACJ;;AAEA;AACA;AACA;AACA;AACA;AACA,MAAMC,aAAa,CAAC;EAChBnE,WAAWA,CAAA,EAAG;IACV,IAAI,CAACoE,SAAS,GAAG,KAAK,CAAC,CAAC;IACxB,IAAI,CAACC,QAAQ,GAAG,IAAI;IACpB,IAAI,CAACC,QAAQ,GAAG,IAAI;IACpB,IAAI,CAACC,YAAY,GAAG,IAAI,CAAC,CAAC;IAC1B;IACA,IAAI,CAACC,UAAU,GAAGpD,QAAQ,CAACqD,IAAI,CAAC,CAAC;IACjC,IAAI,CAACC,MAAM,GAAG,IAAI;IAClB,IAAI,CAACC,cAAc,GAAG,CAAC;EAC3B;EACAC,KAAKA,CAACP,QAAQ,EAAEhC,KAAK,EAAEI,KAAK,EAAE;IAC1B,IAAI,CAAC4B,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACE,YAAY,GAAG,IAAI,CAACF,QAAQ,CAACQ,qBAAqB,CAAC,CAAC;IACzD,IAAI,CAACC,WAAW,GAAGzC,KAAK,GAAGJ,MAAM,CAACK,OAAO;IACzC,IAAI,CAACyC,WAAW,GAAGtC,KAAK,GAAGR,MAAM,CAACS,OAAO;IACzC,IAAI,CAACK,MAAM,GAAG,CAAC;IACf,IAAI,CAACE,MAAM,GAAG,CAAC;IACf,IAAI,CAAC+B,gBAAgB,CAAC,CAAC;EAC3B;EACAC,UAAUA,CAAC5C,KAAK,EAAEI,KAAK,EAAE;IACrB,IAAI,CAACM,MAAM,GAAIV,KAAK,GAAGJ,MAAM,CAACK,OAAO,GAAI,IAAI,CAACwC,WAAW;IACzD,IAAI,CAAC7B,MAAM,GAAIR,KAAK,GAAGR,MAAM,CAACS,OAAO,GAAI,IAAI,CAACqC,WAAW;IACzD,IAAI,CAACC,gBAAgB,CAAC,CAAC;EAC3B;EACA;EACAE,YAAYA,CAACC,IAAI,EAAE;IACf,IAAIA,IAAI,EAAE;MACN,IAAI,CAAC,IAAI,CAACf,SAAS,EAAE;QACjB,IAAI,IAAI,CAACE,QAAQ,EAAE;UACf,IAAI,CAACA,QAAQ,CAACc,KAAK,CAACC,OAAO,GAAG,EAAE;QACpC;QACA,IAAI,CAACjB,SAAS,GAAGe,IAAI,CAAC,CAAC;QACvB,IAAI,CAACH,gBAAgB,CAAC,CAAC,CAAC,CAAC;MAC7B;IACJ,CAAC,MACI,IAAI,IAAI,CAACZ,SAAS,EAAE;MACrB,IAAI,IAAI,CAACE,QAAQ,EAAE;QACf,IAAI,CAACA,QAAQ,CAACc,KAAK,CAACC,OAAO,GAAG,MAAM;MACxC;MACA,IAAI,CAACjB,SAAS,GAAGe,IAAI;IACzB;EACJ;EACA;EACAG,IAAIA,CAACC,oBAAoB,EAAEC,QAAQ,EAAE;IACjC,IAAIC,IAAI,GAAGA,CAAA,KAAM;MACb,IAAI,CAAC/D,OAAO,CAAC,CAAC;MACd8D,QAAQ,CAAC,CAAC;IACd,CAAC;IACD,IAAID,oBAAoB,IACpB,IAAI,CAACjB,QAAQ,IACb,IAAI,CAACF,SAAS,IACd,IAAI,CAACO,cAAc;IAAI;IACtB,IAAI,CAAC5B,MAAM,IAAI,IAAI,CAACE,MAAM,CAAC,CAAC;IAAA,EAC/B;MACE,IAAI,CAACyC,iBAAiB,CAACD,IAAI,EAAE,IAAI,CAACd,cAAc,CAAC;IACrD,CAAC,MACI;MACDX,UAAU,CAACyB,IAAI,EAAE,CAAC,CAAC;IACvB;EACJ;EACAC,iBAAiBA,CAACF,QAAQ,EAAEb,cAAc,EAAE;IACxC,IAAIL,QAAQ,GAAG,IAAI,CAACA,QAAQ;IAC5B,IAAIqB,iBAAiB,GAAG,IAAI,CAACtB,QAAQ,CAACQ,qBAAqB,CAAC,CAAC,CAAC,CAAC;IAC/DP,QAAQ,CAACc,KAAK,CAACQ,UAAU,GACrB,MAAM,GAAGjB,cAAc,GAAG,KAAK,GAC3B,OAAO,GAAGA,cAAc,GAAG,IAAI;IACvClI,UAAU,CAAC6H,QAAQ,EAAE;MACjBuB,IAAI,EAAEF,iBAAiB,CAACE,IAAI;MAC5BC,GAAG,EAAEH,iBAAiB,CAACG;IAC3B,CAAC,CAAC;IACFpJ,kBAAkB,CAAC4H,QAAQ,EAAE,MAAM;MAC/BA,QAAQ,CAACc,KAAK,CAACQ,UAAU,GAAG,EAAE;MAC9BJ,QAAQ,CAAC,CAAC;IACd,CAAC,CAAC;EACN;EACA9D,OAAOA,CAAA,EAAG;IACN,IAAI,IAAI,CAAC4C,QAAQ,EAAE;MACf3H,aAAa,CAAC,IAAI,CAAC2H,QAAQ,CAAC;MAC5B,IAAI,CAACA,QAAQ,GAAG,IAAI;IACxB;IACA,IAAI,CAACD,QAAQ,GAAG,IAAI;EACxB;EACAW,gBAAgBA,CAAA,EAAG;IACf,IAAI,IAAI,CAACX,QAAQ,IAAI,IAAI,CAACD,SAAS,EAAE;MACjC3H,UAAU,CAAC,IAAI,CAACsJ,WAAW,CAAC,CAAC,EAAE;QAC3BF,IAAI,EAAE,IAAI,CAACtB,YAAY,CAACsB,IAAI,GAAG,IAAI,CAAC9C,MAAM;QAC1C+C,GAAG,EAAE,IAAI,CAACvB,YAAY,CAACuB,GAAG,GAAG,IAAI,CAAC7C;MACtC,CAAC,CAAC;IACN;EACJ;EACA8C,WAAWA,CAAA,EAAG;IACV,IAAIxB,YAAY,GAAG,IAAI,CAACA,YAAY;IACpC,IAAID,QAAQ,GAAG,IAAI,CAACA,QAAQ;IAC5B,IAAI,CAACA,QAAQ,EAAE;MACXA,QAAQ,GAAG,IAAI,CAACA,QAAQ,GAAG,IAAI,CAACD,QAAQ,CAAC2B,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC;MAC1D;MACA;MACA1B,QAAQ,CAACc,KAAK,CAACa,UAAU,GAAG,MAAM;MAClC3B,QAAQ,CAACc,KAAK,CAACc,gBAAgB,GAAG,MAAM;MACxC5B,QAAQ,CAACc,KAAK,CAACe,aAAa,GAAG,MAAM;MACrC7B,QAAQ,CAAC8B,SAAS,CAACC,GAAG,CAAC,mBAAmB,CAAC;MAC3C5J,UAAU,CAAC6H,QAAQ,EAAE;QACjBgC,QAAQ,EAAE,OAAO;QACjB5B,MAAM,EAAE,IAAI,CAACA,MAAM;QACnB6B,UAAU,EAAE,EAAE;QACdC,SAAS,EAAE,YAAY;QACvBC,KAAK,EAAElC,YAAY,CAACmC,KAAK,GAAGnC,YAAY,CAACsB,IAAI;QAC7Cc,MAAM,EAAEpC,YAAY,CAACqC,MAAM,GAAGrC,YAAY,CAACuB,GAAG;QAC9CY,KAAK,EAAE,MAAM;QACbE,MAAM,EAAE,MAAM;QACdC,MAAM,EAAE;MACZ,CAAC,CAAC;MACF,IAAI,CAACrC,UAAU,CAACsC,WAAW,CAACxC,QAAQ,CAAC;IACzC;IACA,OAAOA,QAAQ;EACnB;AACJ;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMyC,eAAe,SAASnK,gBAAgB,CAAC;EAC3CoD,WAAWA,CAACgH,gBAAgB,EAAEC,aAAa,EAAE;IACzC,KAAK,CAAC,CAAC;IACP,IAAI,CAAC7E,YAAY,GAAG,MAAM;MACtB,IAAI,CAAC8E,SAAS,GAAG,IAAI,CAACF,gBAAgB,CAACG,YAAY,CAAC,CAAC;MACrD,IAAI,CAACC,UAAU,GAAG,IAAI,CAACJ,gBAAgB,CAACK,aAAa,CAAC,CAAC;MACvD,IAAI,CAACC,kBAAkB,CAAC,CAAC;IAC7B,CAAC;IACD,IAAI,CAACN,gBAAgB,GAAGA,gBAAgB;IACxC,IAAI,CAACC,aAAa,GAAGA,aAAa;IAClC,IAAI,CAACC,SAAS,GAAG,IAAI,CAACK,aAAa,GAAGP,gBAAgB,CAACG,YAAY,CAAC,CAAC;IACrE,IAAI,CAACC,UAAU,GAAG,IAAI,CAACI,cAAc,GAAGR,gBAAgB,CAACK,aAAa,CAAC,CAAC;IACxE,IAAI,CAACI,WAAW,GAAGT,gBAAgB,CAACU,cAAc,CAAC,CAAC;IACpD,IAAI,CAACC,YAAY,GAAGX,gBAAgB,CAACY,eAAe,CAAC,CAAC;IACtD,IAAI,CAACC,WAAW,GAAGb,gBAAgB,CAACc,cAAc,CAAC,CAAC;IACpD,IAAI,CAACC,YAAY,GAAGf,gBAAgB,CAACgB,eAAe,CAAC,CAAC;IACtD,IAAI,CAACC,UAAU,GAAG,IAAI,CAACC,iBAAiB,CAAC,CAAC,CAAC,CAAC;IAC5C,IAAI,IAAI,CAACjB,aAAa,EAAE;MACpB,IAAI,CAACkB,cAAc,CAAC,CAAC,CAAC9G,gBAAgB,CAAC,QAAQ,EAAE,IAAI,CAACe,YAAY,CAAC;IACvE;EACJ;EACAiB,OAAOA,CAAA,EAAG;IACN,IAAI,IAAI,CAAC4D,aAAa,EAAE;MACpB,IAAI,CAACkB,cAAc,CAAC,CAAC,CAAC1G,mBAAmB,CAAC,QAAQ,EAAE,IAAI,CAACW,YAAY,CAAC;IAC1E;EACJ;EACA+E,YAAYA,CAAA,EAAG;IACX,OAAO,IAAI,CAACD,SAAS;EACzB;EACAG,aAAaA,CAAA,EAAG;IACZ,OAAO,IAAI,CAACD,UAAU;EAC1B;EACAgB,YAAYA,CAACtC,GAAG,EAAE;IACd,IAAI,CAACkB,gBAAgB,CAACoB,YAAY,CAACtC,GAAG,CAAC;IACvC,IAAI,CAAC,IAAI,CAACmB,aAAa,EAAE;MACrB;MACA;MACA,IAAI,CAACC,SAAS,GAAGmB,IAAI,CAACC,GAAG,CAACD,IAAI,CAACE,GAAG,CAACzC,GAAG,EAAE,IAAI,CAAC0C,eAAe,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;MACnE,IAAI,CAAClB,kBAAkB,CAAC,CAAC;IAC7B;EACJ;EACAmB,aAAaA,CAAC3C,GAAG,EAAE;IACf,IAAI,CAACkB,gBAAgB,CAACyB,aAAa,CAAC3C,GAAG,CAAC;IACxC,IAAI,CAAC,IAAI,CAACmB,aAAa,EAAE;MACrB;MACA;MACA,IAAI,CAACG,UAAU,GAAGiB,IAAI,CAACC,GAAG,CAACD,IAAI,CAACE,GAAG,CAACzC,GAAG,EAAE,IAAI,CAAC4C,gBAAgB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;MACrE,IAAI,CAACpB,kBAAkB,CAAC,CAAC;IAC7B;EACJ;EACAQ,cAAcA,CAAA,EAAG;IACb,OAAO,IAAI,CAACD,WAAW;EAC3B;EACAG,eAAeA,CAAA,EAAG;IACd,OAAO,IAAI,CAACD,YAAY;EAC5B;EACAL,cAAcA,CAAA,EAAG;IACb,OAAO,IAAI,CAACD,WAAW;EAC3B;EACAG,eAAeA,CAAA,EAAG;IACd,OAAO,IAAI,CAACD,YAAY;EAC5B;EACAL,kBAAkBA,CAAA,EAAG,CACrB;AACJ;AAEA,MAAMqB,sBAAsB,SAAS5B,eAAe,CAAC;EACjD/G,WAAWA,CAAC4I,EAAE,EAAE3B,aAAa,EAAE;IAC3B,KAAK,CAAC,IAAIpK,uBAAuB,CAAC+L,EAAE,CAAC,EAAE3B,aAAa,CAAC;EACzD;EACAkB,cAAcA,CAAA,EAAG;IACb,OAAO,IAAI,CAACnB,gBAAgB,CAAC4B,EAAE;EACnC;EACAV,iBAAiBA,CAAA,EAAG;IAChB,OAAOpL,gBAAgB,CAAC,IAAI,CAACkK,gBAAgB,CAAC4B,EAAE,CAAC;EACrD;AACJ;AAEA,MAAMC,qBAAqB,SAAS9B,eAAe,CAAC;EAChD/G,WAAWA,CAACiH,aAAa,EAAE;IACvB,KAAK,CAAC,IAAIlK,sBAAsB,CAAC,CAAC,EAAEkK,aAAa,CAAC;EACtD;EACAkB,cAAcA,CAAA,EAAG;IACb,OAAOlG,MAAM;EACjB;EACAiG,iBAAiBA,CAAA,EAAG;IAChB,OAAO;MACHrC,IAAI,EAAE,IAAI,CAACuB,UAAU;MACrBV,KAAK,EAAE,IAAI,CAACU,UAAU,GAAG,IAAI,CAACS,WAAW;MACzC/B,GAAG,EAAE,IAAI,CAACoB,SAAS;MACnBN,MAAM,EAAE,IAAI,CAACM,SAAS,GAAG,IAAI,CAACa;IAClC,CAAC;EACL;EACA;EACA;EACAT,kBAAkBA,CAAA,EAAG;IACjB,IAAI,CAACW,UAAU,GAAG,IAAI,CAACC,iBAAiB,CAAC,CAAC;EAC9C;AACJ;;AAEA;AACA;AACA;AACA,MAAMY,OAAO,GAAG,OAAOC,WAAW,KAAK,UAAU,GAAGA,WAAW,CAACC,GAAG,GAAGC,IAAI,CAACD,GAAG;AAC9E;AACA;AACA;AACA;AACA;AACA;AACA,MAAME,YAAY,CAAC;EACflJ,WAAWA,CAAA,EAAG;IACV;IACA,IAAI,CAACmJ,SAAS,GAAG,IAAI;IACrB,IAAI,CAACC,WAAW,GAAG,CAACnH,MAAM,EAAE,cAAc,CAAC;IAC3C,IAAI,CAACoH,aAAa,GAAG,EAAE,CAAC,CAAC;IACzB,IAAI,CAACC,WAAW,GAAG,GAAG,CAAC,CAAC;IACxB;IACA,IAAI,CAACC,cAAc,GAAG,IAAI;IAC1B,IAAI,CAACC,cAAc,GAAG,IAAI;IAC1B,IAAI,CAACC,WAAW,GAAG,KAAK;IACxB,IAAI,CAACC,YAAY,GAAG,IAAI;IACxB;IACA,IAAI,CAACC,WAAW,GAAG,KAAK;IACxB,IAAI,CAACC,aAAa,GAAG,KAAK;IAC1B,IAAI,CAACC,aAAa,GAAG,KAAK;IAC1B,IAAI,CAACC,cAAc,GAAG,KAAK;IAC3B,IAAI,CAACC,OAAO,GAAG,MAAM;MACjB,IAAI,IAAI,CAACN,WAAW,EAAE;QAAE;QACpB,IAAIO,IAAI,GAAG,IAAI,CAACC,eAAe,CAAC,IAAI,CAACV,cAAc,GAAGtH,MAAM,CAACK,OAAO,EAAE,IAAI,CAACkH,cAAc,GAAGvH,MAAM,CAACS,OAAO,CAAC;QAC3G,IAAIsH,IAAI,EAAE;UACN,IAAIhB,GAAG,GAAGF,OAAO,CAAC,CAAC;UACnB,IAAI,CAACoB,UAAU,CAACF,IAAI,EAAE,CAAChB,GAAG,GAAG,IAAI,CAACmB,cAAc,IAAI,IAAI,CAAC;UACzD,IAAI,CAACC,gBAAgB,CAACpB,GAAG,CAAC;QAC9B,CAAC,MACI;UACD,IAAI,CAACS,WAAW,GAAG,KAAK,CAAC,CAAC;QAC9B;MACJ;IACJ,CAAC;EACL;EACA7E,KAAKA,CAACvC,KAAK,EAAEI,KAAK,EAAE4H,aAAa,EAAE;IAC/B,IAAI,IAAI,CAAClB,SAAS,EAAE;MAChB,IAAI,CAACO,YAAY,GAAG,IAAI,CAACY,WAAW,CAACD,aAAa,CAAC;MACnD,IAAI,CAACd,cAAc,GAAG,IAAI;MAC1B,IAAI,CAACC,cAAc,GAAG,IAAI;MAC1B,IAAI,CAACG,WAAW,GAAG,KAAK;MACxB,IAAI,CAACC,aAAa,GAAG,KAAK;MAC1B,IAAI,CAACC,aAAa,GAAG,KAAK;MAC1B,IAAI,CAACC,cAAc,GAAG,KAAK;MAC3B,IAAI,CAAC7E,UAAU,CAAC5C,KAAK,EAAEI,KAAK,CAAC;IACjC;EACJ;EACAwC,UAAUA,CAAC5C,KAAK,EAAEI,KAAK,EAAE;IACrB,IAAI,IAAI,CAAC0G,SAAS,EAAE;MAChB,IAAII,cAAc,GAAGlH,KAAK,GAAGJ,MAAM,CAACK,OAAO;MAC3C,IAAIkH,cAAc,GAAG/G,KAAK,GAAGR,MAAM,CAACS,OAAO;MAC3C,IAAI6H,MAAM,GAAG,IAAI,CAACf,cAAc,KAAK,IAAI,GAAG,CAAC,GAAGA,cAAc,GAAG,IAAI,CAACA,cAAc;MACpF,IAAIgB,MAAM,GAAG,IAAI,CAACjB,cAAc,KAAK,IAAI,GAAG,CAAC,GAAGA,cAAc,GAAG,IAAI,CAACA,cAAc;MACpF,IAAIgB,MAAM,GAAG,CAAC,EAAE;QACZ,IAAI,CAACZ,WAAW,GAAG,IAAI;MAC3B,CAAC,MACI,IAAIY,MAAM,GAAG,CAAC,EAAE;QACjB,IAAI,CAACX,aAAa,GAAG,IAAI;MAC7B;MACA,IAAIY,MAAM,GAAG,CAAC,EAAE;QACZ,IAAI,CAACX,aAAa,GAAG,IAAI;MAC7B,CAAC,MACI,IAAIW,MAAM,GAAG,CAAC,EAAE;QACjB,IAAI,CAACV,cAAc,GAAG,IAAI;MAC9B;MACA,IAAI,CAACP,cAAc,GAAGA,cAAc;MACpC,IAAI,CAACC,cAAc,GAAGA,cAAc;MACpC,IAAI,CAAC,IAAI,CAACC,WAAW,EAAE;QACnB,IAAI,CAACA,WAAW,GAAG,IAAI;QACvB,IAAI,CAACW,gBAAgB,CAACtB,OAAO,CAAC,CAAC,CAAC;MACpC;IACJ;EACJ;EACAxD,IAAIA,CAAA,EAAG;IACH,IAAI,IAAI,CAAC6D,SAAS,EAAE;MAChB,IAAI,CAACM,WAAW,GAAG,KAAK,CAAC,CAAC;MAC1B,KAAK,IAAIgB,WAAW,IAAI,IAAI,CAACf,YAAY,EAAE;QACvCe,WAAW,CAACpH,OAAO,CAAC,CAAC;MACzB;MACA,IAAI,CAACqG,YAAY,GAAG,IAAI;IAC5B;EACJ;EACAU,gBAAgBA,CAACpB,GAAG,EAAE;IAClB,IAAI,CAACmB,cAAc,GAAGnB,GAAG;IACzB0B,qBAAqB,CAAC,IAAI,CAACX,OAAO,CAAC;EACvC;EACAG,UAAUA,CAACF,IAAI,EAAEW,OAAO,EAAE;IACtB,IAAI;MAAEF;IAAY,CAAC,GAAGT,IAAI;IAC1B,IAAI;MAAEX;IAAc,CAAC,GAAG,IAAI;IAC5B,IAAIuB,WAAW,GAAGvB,aAAa,GAAGW,IAAI,CAACa,QAAQ;IAC/C,IAAIC,QAAQ;IAAG;IACZF,WAAW,GAAGA,WAAW,IAAKvB,aAAa,GAAGA,aAAa,CAAC;IAAI;IAC/D,IAAI,CAACC,WAAW,GAAGqB,OAAO;IAC9B,IAAII,IAAI,GAAG,CAAC;IACZ,QAAQf,IAAI,CAACgB,IAAI;MACb,KAAK,MAAM;QACPD,IAAI,GAAG,CAAC,CAAC;MACb;MACA,KAAK,OAAO;QACRN,WAAW,CAAChC,aAAa,CAACgC,WAAW,CAACpD,aAAa,CAAC,CAAC,GAAGyD,QAAQ,GAAGC,IAAI,CAAC;QACxE;MACJ,KAAK,KAAK;QACNA,IAAI,GAAG,CAAC,CAAC;MACb;MACA,KAAK,QAAQ;QACTN,WAAW,CAACrC,YAAY,CAACqC,WAAW,CAACtD,YAAY,CAAC,CAAC,GAAG2D,QAAQ,GAAGC,IAAI,CAAC;QACtE;IACR;EACJ;EACA;EACAd,eAAeA,CAACpE,IAAI,EAAEC,GAAG,EAAE;IACvB,IAAI;MAAEuD;IAAc,CAAC,GAAG,IAAI;IAC5B,IAAI4B,QAAQ,GAAG,IAAI;IACnB,IAAIvB,YAAY,GAAG,IAAI,CAACA,YAAY,IAAI,EAAE;IAC1C,KAAK,IAAIe,WAAW,IAAIf,YAAY,EAAE;MAClC,IAAIwB,IAAI,GAAGT,WAAW,CAACxC,UAAU;MACjC,IAAIkD,QAAQ,GAAGtF,IAAI,GAAGqF,IAAI,CAACrF,IAAI;MAC/B,IAAIuF,SAAS,GAAGF,IAAI,CAACxE,KAAK,GAAGb,IAAI;MACjC,IAAIwF,OAAO,GAAGvF,GAAG,GAAGoF,IAAI,CAACpF,GAAG;MAC5B,IAAIwF,UAAU,GAAGJ,IAAI,CAACtE,MAAM,GAAGd,GAAG;MAClC;MACA,IAAIqF,QAAQ,IAAI,CAAC,IAAIC,SAAS,IAAI,CAAC,IAAIC,OAAO,IAAI,CAAC,IAAIC,UAAU,IAAI,CAAC,EAAE;QACpE,IAAID,OAAO,IAAIhC,aAAa,IAAI,IAAI,CAACM,WAAW,IAAIc,WAAW,CAACc,WAAW,CAAC,CAAC,KACxE,CAACN,QAAQ,IAAIA,QAAQ,CAACJ,QAAQ,GAAGQ,OAAO,CAAC,EAAE;UAC5CJ,QAAQ,GAAG;YAAER,WAAW;YAAEO,IAAI,EAAE,KAAK;YAAEH,QAAQ,EAAEQ;UAAQ,CAAC;QAC9D;QACA,IAAIC,UAAU,IAAIjC,aAAa,IAAI,IAAI,CAACO,aAAa,IAAIa,WAAW,CAACe,aAAa,CAAC,CAAC,KAC/E,CAACP,QAAQ,IAAIA,QAAQ,CAACJ,QAAQ,GAAGS,UAAU,CAAC,EAAE;UAC/CL,QAAQ,GAAG;YAAER,WAAW;YAAEO,IAAI,EAAE,QAAQ;YAAEH,QAAQ,EAAES;UAAW,CAAC;QACpE;QACA;AAChB;AACA;AACA;QACgB,IAAIH,QAAQ,IAAI9B,aAAa,IAAI,IAAI,CAACQ,aAAa,IAAIY,WAAW,CAACgB,aAAa,CAAC,CAAC,KAC7E,CAACR,QAAQ,IAAIA,QAAQ,CAACJ,QAAQ,GAAGM,QAAQ,CAAC,EAAE;UAC7CF,QAAQ,GAAG;YAAER,WAAW;YAAEO,IAAI,EAAE,MAAM;YAAEH,QAAQ,EAAEM;UAAS,CAAC;QAChE;QACA,IAAIC,SAAS,IAAI/B,aAAa,IAAI,IAAI,CAACS,cAAc,IAAIW,WAAW,CAACiB,cAAc,CAAC,CAAC,KAChF,CAACT,QAAQ,IAAIA,QAAQ,CAACJ,QAAQ,GAAGO,SAAS,CAAC,EAAE;UAC9CH,QAAQ,GAAG;YAAER,WAAW;YAAEO,IAAI,EAAE,OAAO;YAAEH,QAAQ,EAAEO;UAAU,CAAC;QAClE;MACJ;IACJ;IACA,OAAOH,QAAQ;EACnB;EACAX,WAAWA,CAACD,aAAa,EAAE;IACvB,OAAO,IAAI,CAACsB,cAAc,CAACtB,aAAa,CAAC,CAACuB,GAAG,CAAEhD,EAAE,IAAK;MAClD,IAAIA,EAAE,KAAK3G,MAAM,EAAE;QACf,OAAO,IAAI4G,qBAAqB,CAAC,KAAK,CAAC,CAAC,CAAC;MAC7C;MACA,OAAO,IAAIF,sBAAsB,CAACC,EAAE,EAAE,KAAK,CAAC,CAAC,CAAC;IAClD,CAAC,CAAC;EACN;EACA+C,cAAcA,CAACtB,aAAa,EAAE;IAC1B,IAAIwB,GAAG,GAAG,EAAE;IACZ,KAAK,IAAIC,KAAK,IAAI,IAAI,CAAC1C,WAAW,EAAE;MAChC,IAAI,OAAO0C,KAAK,KAAK,QAAQ,EAAE;QAC3BD,GAAG,CAACE,IAAI,CAACD,KAAK,CAAC;MACnB,CAAC,MACI;QACD;AAChB;AACA;AACA;QACgBD,GAAG,CAACE,IAAI,CAAC,GAAGC,KAAK,CAACC,SAAS,CAACC,KAAK,CAACC,IAAI,CAAC9B,aAAa,CAAC+B,WAAW,CAAC,CAAC,CAACC,gBAAgB,CAACP,KAAK,CAAC,CAAC,CAAC;MAChG;IACJ;IACA,OAAOD,GAAG;EACd;AACJ;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMS,yBAAyB,SAAStP,eAAe,CAAC;EACpDgD,WAAWA,CAACC,WAAW,EAAEE,QAAQ,EAAE;IAC/B,KAAK,CAACF,WAAW,CAAC;IAClB,IAAI,CAACA,WAAW,GAAGA,WAAW;IAC9B;IACA;IACA,IAAI,CAACsM,KAAK,GAAG,IAAI;IACjB,IAAI,CAACC,WAAW,GAAG,CAAC;IACpB,IAAI,CAACC,kBAAkB,GAAG,IAAI,CAAC,CAAC;IAChC,IAAI,CAACC,iBAAiB,GAAG,KAAK;IAC9B,IAAI,CAACC,aAAa,GAAG,KAAK,CAAC,CAAC;IAC5B,IAAI,CAACpM,UAAU,GAAG,KAAK,CAAC,CAAC;IACzB,IAAI,CAACqM,YAAY,GAAG,KAAK;IACzB,IAAI,CAACC,mBAAmB,GAAG,KAAK;IAChC,IAAI,CAACC,cAAc,GAAG,IAAI;IAC1B,IAAI,CAACC,aAAa,GAAIpM,EAAE,IAAK;MACzB,IAAI,CAAC,IAAI,CAACJ,UAAU,EAAE;QAAE;QACpB,IAAI,CAACoM,aAAa,GAAG,IAAI;QACzB,IAAI,CAACC,YAAY,GAAG,KAAK;QACzB,IAAI,CAACC,mBAAmB,GAAG,KAAK;QAChC5P,gBAAgB,CAACmE,QAAQ,CAACqD,IAAI,CAAC;QAC/BvH,kBAAkB,CAACkE,QAAQ,CAACqD,IAAI,CAAC;QACjC;QACA;QACA;QACA,IAAI,CAAC9D,EAAE,CAACmC,OAAO,EAAE;UACbnC,EAAE,CAACkC,SAAS,CAACqB,cAAc,CAAC,CAAC;QACjC;QACA,IAAI,CAACjD,OAAO,CAACC,OAAO,CAAC,aAAa,EAAEP,EAAE,CAAC;QACvC,IAAI,IAAI,CAACgM,aAAa;QAAI;QACtB,CAAC,IAAI,CAACK,OAAO,CAAC3M,gBAAgB,EAAE;UAChC;UACA,IAAI,CAAC4M,MAAM,CAAC/H,YAAY,CAAC,KAAK,CAAC,CAAC,CAAC;UACjC,IAAI,CAAC+H,MAAM,CAACrI,KAAK,CAACjE,EAAE,CAACT,SAAS,EAAES,EAAE,CAAC0B,KAAK,EAAE1B,EAAE,CAAC8B,KAAK,CAAC,CAAC,CAAC;UACrD,IAAI,CAACyK,UAAU,CAACvM,EAAE,CAAC;UACnB,IAAI,CAAC,IAAI,CAAC6L,WAAW,EAAE;YACnB,IAAI,CAACW,uBAAuB,CAACxM,EAAE,CAAC;UACpC;QACJ;MACJ;IACJ,CAAC;IACD,IAAI,CAACyM,aAAa,GAAIzM,EAAE,IAAK;MACzB,IAAI,IAAI,CAACgM,aAAa,EAAE;QACpB,IAAI,CAAC1L,OAAO,CAACC,OAAO,CAAC,aAAa,EAAEP,EAAE,CAAC;QACvC,IAAI,CAAC,IAAI,CAACkM,mBAAmB,EAAE;UAC3B,IAAIL,WAAW,GAAG,IAAI,CAACA,WAAW;UAClC,IAAIa,UAAU,CAAC,CAAC;UAChB,IAAI;YAAEtK,MAAM;YAAEE;UAAO,CAAC,GAAGtC,EAAE;UAC3B0M,UAAU,GAAGtK,MAAM,GAAGA,MAAM,GAAGE,MAAM,GAAGA,MAAM;UAC9C,IAAIoK,UAAU,IAAIb,WAAW,GAAGA,WAAW,EAAE;YAAE;YAC3C,IAAI,CAACW,uBAAuB,CAACxM,EAAE,CAAC;UACpC;QACJ;QACA,IAAI,IAAI,CAACJ,UAAU,EAAE;UACjB;UACA,IAAII,EAAE,CAACkC,SAAS,CAACyK,IAAI,KAAK,QAAQ,EAAE;YAChC,IAAI,CAACL,MAAM,CAAChI,UAAU,CAACtE,EAAE,CAAC0B,KAAK,EAAE1B,EAAE,CAAC8B,KAAK,CAAC;YAC1C,IAAI,CAAC8K,YAAY,CAACtI,UAAU,CAACtE,EAAE,CAAC0B,KAAK,EAAE1B,EAAE,CAAC8B,KAAK,CAAC;UACpD;UACA,IAAI,CAACxB,OAAO,CAACC,OAAO,CAAC,UAAU,EAAEP,EAAE,CAAC;QACxC;MACJ;IACJ,CAAC;IACD,IAAI,CAAC6M,WAAW,GAAI7M,EAAE,IAAK;MACvB,IAAI,IAAI,CAACgM,aAAa,EAAE;QACpB,IAAI,CAACA,aAAa,GAAG,KAAK;QAC1BxP,cAAc,CAACiE,QAAQ,CAACqD,IAAI,CAAC;QAC7BrH,gBAAgB,CAACgE,QAAQ,CAACqD,IAAI,CAAC;QAC/B,IAAI,CAACxD,OAAO,CAACC,OAAO,CAAC,WAAW,EAAEP,EAAE,CAAC,CAAC,CAAC;QACvC,IAAI,IAAI,CAACJ,UAAU,EAAE;UACjB,IAAI,CAACgN,YAAY,CAACjI,IAAI,CAAC,CAAC;UACxB,IAAI,CAACmI,WAAW,CAAC9M,EAAE,CAAC,CAAC,CAAC;QAC1B;QACA,IAAI,IAAI,CAACmM,cAAc,EAAE;UACrBY,YAAY,CAAC,IAAI,CAACZ,cAAc,CAAC;UACjC,IAAI,CAACA,cAAc,GAAG,IAAI;QAC9B;MACJ;IACJ,CAAC;IACD,IAAIE,OAAO,GAAG,IAAI,CAACA,OAAO,GAAG,IAAIjN,eAAe,CAACE,WAAW,CAAC;IAC7D+M,OAAO,CAAC/L,OAAO,CAAC0M,EAAE,CAAC,aAAa,EAAE,IAAI,CAACZ,aAAa,CAAC;IACrDC,OAAO,CAAC/L,OAAO,CAAC0M,EAAE,CAAC,aAAa,EAAE,IAAI,CAACP,aAAa,CAAC;IACrDJ,OAAO,CAAC/L,OAAO,CAAC0M,EAAE,CAAC,WAAW,EAAE,IAAI,CAACH,WAAW,CAAC;IACjD,IAAIrN,QAAQ,EAAE;MACV6M,OAAO,CAAC7M,QAAQ,GAAGA,QAAQ;IAC/B;IACA,IAAI,CAAC8M,MAAM,GAAG,IAAI9I,aAAa,CAAC,CAAC;IACjC,IAAI,CAACoJ,YAAY,GAAG,IAAIrE,YAAY,CAAC,CAAC;EAC1C;EACA7F,OAAOA,CAAA,EAAG;IACN,IAAI,CAAC2J,OAAO,CAAC3J,OAAO,CAAC,CAAC;IACtB;IACA;IACA,IAAI,CAACmK,WAAW,CAAC,CAAC,CAAC,CAAC;EACxB;EACAN,UAAUA,CAACvM,EAAE,EAAE;IACX,IAAI,OAAO,IAAI,CAAC4L,KAAK,KAAK,QAAQ,EAAE;MAChC,IAAI,CAACO,cAAc,GAAG9I,UAAU,CAAC,MAAM;QACnC,IAAI,CAAC8I,cAAc,GAAG,IAAI;QAC1B,IAAI,CAACc,cAAc,CAACjN,EAAE,CAAC;MAC3B,CAAC,EAAE,IAAI,CAAC4L,KAAK,CAAC,CAAC,CAAC;IACpB,CAAC,MACI;MACD,IAAI,CAACqB,cAAc,CAACjN,EAAE,CAAC;IAC3B;EACJ;EACAiN,cAAcA,CAACjN,EAAE,EAAE;IACf,IAAI,CAACiM,YAAY,GAAG,IAAI;IACxB,IAAI,CAACiB,YAAY,CAAClN,EAAE,CAAC;EACzB;EACAwM,uBAAuBA,CAACxM,EAAE,EAAE;IACxB,IAAI,CAACkM,mBAAmB,GAAG,IAAI;IAC/B,IAAI,CAACgB,YAAY,CAAClN,EAAE,CAAC;EACzB;EACAkN,YAAYA,CAAClN,EAAE,EAAE;IACb,IAAI,IAAI,CAACiM,YAAY,IAAI,IAAI,CAACC,mBAAmB,EAAE;MAC/C,IAAI,CAAC,IAAI,CAACG,OAAO,CAACvM,cAAc,IAAI,IAAI,CAACgM,kBAAkB,EAAE;QACzD,IAAI,CAAClM,UAAU,GAAG,IAAI;QACtB,IAAI,CAACmM,iBAAiB,GAAG,KAAK;QAC9B,IAAI,CAACa,YAAY,CAAC3I,KAAK,CAACjE,EAAE,CAAC0B,KAAK,EAAE1B,EAAE,CAAC8B,KAAK,EAAE,IAAI,CAACxC,WAAW,CAAC;QAC7D,IAAI,CAACgB,OAAO,CAACC,OAAO,CAAC,WAAW,EAAEP,EAAE,CAAC;QACrC,IAAI,IAAI,CAAC8L,kBAAkB,KAAK,KAAK,EAAE;UACnC,IAAI,CAACO,OAAO,CAACtJ,iBAAiB,CAAC,CAAC;QACpC;MACJ;IACJ;EACJ;EACA+J,WAAWA,CAAC9M,EAAE,EAAE;IACZ;IACA;IACA,IAAI,CAACsM,MAAM,CAAC3H,IAAI,CAAC,IAAI,CAACoH,iBAAiB,EAAE,IAAI,CAACoB,QAAQ,CAACC,IAAI,CAAC,IAAI,EAAEpN,EAAE,CAAC,CAAC;EAC1E;EACAmN,QAAQA,CAACnN,EAAE,EAAE;IACT,IAAI,CAACJ,UAAU,GAAG,KAAK;IACvB,IAAI,CAACU,OAAO,CAACC,OAAO,CAAC,SAAS,EAAEP,EAAE,CAAC;EACvC;EACA;EACAqN,aAAaA,CAAC7I,IAAI,EAAE;IAChB,IAAI,CAAC6H,OAAO,CAAC3M,gBAAgB,GAAG8E,IAAI;EACxC;EACA8I,kBAAkBA,CAAC9I,IAAI,EAAE;IACrB,IAAI,CAAC8H,MAAM,CAAC/H,YAAY,CAACC,IAAI,CAAC;EAClC;EACA+I,oBAAoBA,CAAC/I,IAAI,EAAE;IACvB,IAAI,CAACuH,iBAAiB,GAAGvH,IAAI;EACjC;EACAgJ,oBAAoBA,CAAChJ,IAAI,EAAE;IACvB,IAAI,CAACoI,YAAY,CAACpE,SAAS,GAAGhE,IAAI;EACtC;AACJ;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMiJ,aAAa,CAAC;EAChBpO,WAAWA,CAAC4I,EAAE,EAAE;IACZ,IAAI,CAACA,EAAE,GAAGA,EAAE;IACZ,IAAI,CAACyF,QAAQ,GAAGhR,WAAW,CAACuL,EAAE,CAAC;IAC/B;IACA,IAAI,CAACc,YAAY,GAAGpM,kBAAkB,CAACsL,EAAE,CAAC,CAACgD,GAAG,CAAE0C,QAAQ,IAAK,IAAI3F,sBAAsB,CAAC2F,QAAQ,EAAE,IAAI,CAAC,CAAC;EAC5G;EACAjL,OAAOA,CAAA,EAAG;IACN,KAAK,IAAIoH,WAAW,IAAI,IAAI,CAACf,YAAY,EAAE;MACvCe,WAAW,CAACpH,OAAO,CAAC,CAAC;IACzB;EACJ;EACAkL,WAAWA,CAAA,EAAG;IACV,IAAI1I,IAAI,GAAG,IAAI,CAACwI,QAAQ,CAACxI,IAAI;IAC7B,KAAK,IAAI4E,WAAW,IAAI,IAAI,CAACf,YAAY,EAAE;MACvC7D,IAAI,IAAI4E,WAAW,CAACjD,cAAc,GAAGiD,WAAW,CAACpD,aAAa,CAAC,CAAC;IACpE;IACA,OAAOxB,IAAI;EACf;EACA2I,UAAUA,CAAA,EAAG;IACT,IAAI1I,GAAG,GAAG,IAAI,CAACuI,QAAQ,CAACvI,GAAG;IAC3B,KAAK,IAAI2E,WAAW,IAAI,IAAI,CAACf,YAAY,EAAE;MACvC5D,GAAG,IAAI2E,WAAW,CAAClD,aAAa,GAAGkD,WAAW,CAACtD,YAAY,CAAC,CAAC;IACjE;IACA,OAAOrB,GAAG;EACd;EACA2I,gBAAgBA,CAACpM,KAAK,EAAEI,KAAK,EAAE;IAC3B,IAAIiM,KAAK,GAAG;MAAE7I,IAAI,EAAExD,KAAK;MAAEyD,GAAG,EAAErD;IAAM,CAAC;IACvC,KAAK,IAAIgI,WAAW,IAAI,IAAI,CAACf,YAAY,EAAE;MACvC,IAAI,CAACiF,iBAAiB,CAAClE,WAAW,CAACtC,cAAc,CAAC,CAAC,CAAC,IAChD,CAAC5K,eAAe,CAACmR,KAAK,EAAEjE,WAAW,CAACxC,UAAU,CAAC,EAAE;QACjD,OAAO,KAAK;MAChB;IACJ;IACA,OAAO,IAAI;EACf;AACJ;AACA;AACA;AACA,SAAS0G,iBAAiBA,CAACC,IAAI,EAAE;EAC7B,IAAIC,OAAO,GAAGD,IAAI,CAACC,OAAO;EAC1B,OAAOA,OAAO,KAAK,MAAM,IAAIA,OAAO,KAAK,MAAM;AACnD;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,WAAW,CAAC;EACd9O,WAAWA,CAAC+O,QAAQ,EAAEC,cAAc,EAAE;IAClC;IACA,IAAI,CAACC,gBAAgB,GAAG,KAAK;IAC7B,IAAI,CAACC,cAAc,GAAG,IAAI,CAAC,CAAC;IAC5B,IAAI,CAACC,iBAAiB,GAAG,KAAK;IAC9B,IAAI,CAACC,UAAU,GAAG,IAAI;IACtB,IAAI,CAACC,SAAS,GAAG,IAAI;IACrB,IAAI,CAACC,QAAQ,GAAG,IAAI,CAAC,CAAC;IACtB,IAAI,CAACC,iBAAiB,GAAI5O,EAAE,IAAK;MAC7B,IAAI;QAAEoO;MAAS,CAAC,GAAG,IAAI;MACvB,IAAI,CAACK,UAAU,GAAG,IAAI;MACtB,IAAI,CAACC,SAAS,GAAG,IAAI;MACrB,IAAI,CAACC,QAAQ,GAAG,IAAI;MACpB,IAAI,CAACE,WAAW,CAAC,CAAC;MAClB,IAAI,CAACC,iBAAiB,CAAC9O,EAAE,CAAC;MAC1B,IAAI,IAAI,CAACyO,UAAU,IAAI,CAAC,IAAI,CAACF,cAAc,EAAE;QACzCH,QAAQ,CAACf,aAAa,CAAC,KAAK,CAAC;QAC7B;QACA,IAAI,CAAC/M,OAAO,CAACC,OAAO,CAAC,aAAa,EAAEP,EAAE,CAAC;MAC3C,CAAC,MACI;QACDoO,QAAQ,CAACf,aAAa,CAAC,IAAI,CAAC;MAChC;IACJ,CAAC;IACD,IAAI,CAAC0B,eAAe,GAAI/O,EAAE,IAAK;MAC3B,IAAI,CAACM,OAAO,CAACC,OAAO,CAAC,WAAW,EAAEP,EAAE,CAAC;MACrC,IAAI,CAACsE,UAAU,CAACtE,EAAE,EAAE,IAAI,CAAC,CAAC,CAAC;IAC/B,CAAC;IACD,IAAI,CAACgP,cAAc,GAAIhP,EAAE,IAAK;MAC1B,IAAI,CAACM,OAAO,CAACC,OAAO,CAAC,UAAU,EAAEP,EAAE,CAAC;MACpC,IAAI,CAACsE,UAAU,CAACtE,EAAE,CAAC;IACvB,CAAC;IACD,IAAI,CAACiP,eAAe,GAAIjP,EAAE,IAAK;MAC3B,IAAI,CAACkP,WAAW,CAAC,CAAC;MAClB,IAAI,CAAC5O,OAAO,CAACC,OAAO,CAAC,WAAW,EAAEP,EAAE,CAAC;IACzC,CAAC;IACD,IAAI,CAACmP,aAAa,GAAInP,EAAE,IAAK;MACzB,IAAI,IAAI,CAAC0O,SAAS,EAAE;QAChB,IAAI,CAACpO,OAAO,CAACC,OAAO,CAAC,WAAW,EAAE,IAAI,EAAE,IAAI,EAAEP,EAAE,CAAC;MACrD;MACA,IAAI,CAAC2O,QAAQ,GAAG,IAAI,CAACD,SAAS;MAC9B,IAAI,CAACA,SAAS,GAAG,IAAI;MACrB,IAAI,CAACpO,OAAO,CAACC,OAAO,CAAC,SAAS,EAAEP,EAAE,CAAC;IACvC,CAAC;IACD,IAAI,CAACqO,cAAc,GAAGA,cAAc;IACpCD,QAAQ,CAAC9N,OAAO,CAAC0M,EAAE,CAAC,aAAa,EAAE,IAAI,CAAC4B,iBAAiB,CAAC;IAC1DR,QAAQ,CAAC9N,OAAO,CAAC0M,EAAE,CAAC,WAAW,EAAE,IAAI,CAAC+B,eAAe,CAAC;IACtDX,QAAQ,CAAC9N,OAAO,CAAC0M,EAAE,CAAC,UAAU,EAAE,IAAI,CAACgC,cAAc,CAAC;IACpDZ,QAAQ,CAAC9N,OAAO,CAAC0M,EAAE,CAAC,WAAW,EAAE,IAAI,CAACiC,eAAe,CAAC;IACtDb,QAAQ,CAAC9N,OAAO,CAAC0M,EAAE,CAAC,SAAS,EAAE,IAAI,CAACmC,aAAa,CAAC;IAClD,IAAI,CAACf,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAAC9N,OAAO,GAAG,IAAI1E,OAAO,CAAC,CAAC;EAChC;EACA;EACA;EACAkT,iBAAiBA,CAAC9O,EAAE,EAAE;IAClB,IAAIoP,SAAS,GAAG;MAAElK,IAAI,EAAElF,EAAE,CAAC0B,KAAK;MAAEyD,GAAG,EAAEnF,EAAE,CAAC8B;IAAM,CAAC;IACjD,IAAIuN,aAAa,GAAGD,SAAS;IAC7B,IAAI7P,SAAS,GAAGS,EAAE,CAACT,SAAS;IAC5B,IAAI+P,WAAW;IACf,IAAI/P,SAAS,YAAYgQ,WAAW,EAAE;MAAE;MACpCD,WAAW,GAAG5S,WAAW,CAAC6C,SAAS,CAAC;MACpC8P,aAAa,GAAGxS,cAAc,CAACwS,aAAa,EAAEC,WAAW,CAAC;IAC9D;IACA,IAAIb,UAAU,GAAG,IAAI,CAACA,UAAU,GAAG,IAAI,CAACe,iBAAiB,CAACH,aAAa,CAACnK,IAAI,EAAEmK,aAAa,CAAClK,GAAG,CAAC;IAChG,IAAIsJ,UAAU,EAAE;MACZ,IAAI,IAAI,CAACH,gBAAgB,IAAIgB,WAAW,EAAE;QACtC,IAAIG,iBAAiB,GAAG3S,cAAc,CAACwS,WAAW,EAAEb,UAAU,CAAClE,IAAI,CAAC;QACpE,IAAIkF,iBAAiB,EAAE;UACnBJ,aAAa,GAAGtS,aAAa,CAAC0S,iBAAiB,CAAC;QACpD;MACJ;MACA,IAAI,CAACC,WAAW,GAAG1S,UAAU,CAACqS,aAAa,EAAED,SAAS,CAAC;IAC3D,CAAC,MACI;MACD,IAAI,CAACM,WAAW,GAAG;QAAExK,IAAI,EAAE,CAAC;QAAEC,GAAG,EAAE;MAAE,CAAC;IAC1C;EACJ;EACAb,UAAUA,CAACtE,EAAE,EAAE2P,WAAW,EAAE;IACxB,IAAIC,GAAG,GAAG,IAAI,CAACJ,iBAAiB,CAACxP,EAAE,CAAC0B,KAAK,GAAG,IAAI,CAACgO,WAAW,CAACxK,IAAI,EAAElF,EAAE,CAAC8B,KAAK,GAAG,IAAI,CAAC4N,WAAW,CAACvK,GAAG,CAAC;IACnG,IAAIwK,WAAW,IAAI,CAACE,WAAW,CAAC,IAAI,CAACnB,SAAS,EAAEkB,GAAG,CAAC,EAAE;MAClD,IAAI,CAAClB,SAAS,GAAGkB,GAAG;MACpB,IAAI,CAACtP,OAAO,CAACC,OAAO,CAAC,WAAW,EAAEqP,GAAG,EAAE,KAAK,EAAE5P,EAAE,CAAC;IACrD;EACJ;EACA6O,WAAWA,CAAA,EAAG;IACV,IAAI,CAACiB,cAAc,GAAG7S,OAAO,CAAC,IAAI,CAACoR,cAAc,EAAG0B,mBAAmB,IAAK;MACxEA,mBAAmB,CAACC,SAAS,CAACnB,WAAW,CAAC,CAAC;MAC3C,OAAO,IAAIpB,aAAa,CAACsC,mBAAmB,CAAC9H,EAAE,CAAC;IACpD,CAAC,CAAC;EACN;EACAiH,WAAWA,CAAA,EAAG;IACV,IAAI;MAAEY;IAAe,CAAC,GAAG,IAAI;IAC7B,KAAK,IAAIG,EAAE,IAAIH,cAAc,EAAE;MAC3BA,cAAc,CAACG,EAAE,CAAC,CAACvN,OAAO,CAAC,CAAC;IAChC;IACA,IAAI,CAACoN,cAAc,GAAG,CAAC,CAAC;EAC5B;EACAN,iBAAiBA,CAACU,UAAU,EAAEC,SAAS,EAAE;IACrC,IAAI;MAAE9B,cAAc;MAAEyB;IAAe,CAAC,GAAG,IAAI;IAC7C,IAAIM,OAAO,GAAG,IAAI;IAClB,KAAK,IAAIH,EAAE,IAAI5B,cAAc,EAAE;MAC3B,IAAI2B,SAAS,GAAG3B,cAAc,CAAC4B,EAAE,CAAC,CAACD,SAAS;MAC5C,IAAIK,aAAa,GAAGP,cAAc,CAACG,EAAE,CAAC;MACtC,IAAII,aAAa;MAAI;MACjBA,aAAa,CAACvC,gBAAgB,CAACoC,UAAU,EAAEC,SAAS,CAAC,EAAE;QACvD,IAAIG,UAAU,GAAGD,aAAa,CAACzC,WAAW,CAAC,CAAC;QAC5C,IAAI2C,SAAS,GAAGF,aAAa,CAACxC,UAAU,CAAC,CAAC;QAC1C,IAAI2C,YAAY,GAAGN,UAAU,GAAGI,UAAU;QAC1C,IAAIG,WAAW,GAAGN,SAAS,GAAGI,SAAS;QACvC,IAAI;UAAE7C;QAAS,CAAC,GAAG2C,aAAa;QAChC,IAAIvK,KAAK,GAAG4H,QAAQ,CAAC3H,KAAK,GAAG2H,QAAQ,CAACxI,IAAI;QAC1C,IAAIc,MAAM,GAAG0H,QAAQ,CAACzH,MAAM,GAAGyH,QAAQ,CAACvI,GAAG;QAC3C;QACA;QACAqL,YAAY,IAAI,CAAC,IAAIA,YAAY,GAAG1K,KAAK,IACrC2K,WAAW,IAAI,CAAC,IAAIA,WAAW,GAAGzK,MAAM,EAAE;UAC1C,IAAI4J,GAAG,GAAGI,SAAS,CAACU,QAAQ,CAACF,YAAY,EAAEC,WAAW,EAAE3K,KAAK,EAAEE,MAAM,CAAC;UACtE,IAAI4J,GAAG;UACP;UACA1S,kBAAkB,CAAC0S,GAAG,CAACe,WAAW,CAACC,WAAW,EAAEhB,GAAG,CAACiB,QAAQ,CAACC,KAAK,CAAE;UAChE;UACA;UACA;UACC,IAAI,CAACtC,iBAAiB,IACnB6B,aAAa,CAACpI,EAAE,CAAC8I,QAAQ,CAACV,aAAa,CAACpI,EAAE,CAACwD,WAAW,CAAC,CAAC,CAACuF,gBAAgB;UACzE;UACAR,YAAY,GAAGF,UAAU,GAAGhP,MAAM,CAACK,OAAO,EAAE8O,WAAW,GAAGF,SAAS,GAAGjP,MAAM,CAACS,OAAO,CAAC,CAAC,CAAC,KAC1F,CAACqO,OAAO,IAAIR,GAAG,CAACqB,KAAK,GAAGb,OAAO,CAACa,KAAK,CAAC,EAAE;YACzCrB,GAAG,CAACsB,WAAW,GAAGjB,EAAE;YACpBL,GAAG,CAACuB,OAAO,GAAGnB,SAAS,CAACmB,OAAO;YAC/B;YACAvB,GAAG,CAACrF,IAAI,CAACrF,IAAI,IAAIoL,UAAU;YAC3BV,GAAG,CAACrF,IAAI,CAACxE,KAAK,IAAIuK,UAAU;YAC5BV,GAAG,CAACrF,IAAI,CAACpF,GAAG,IAAIoL,SAAS;YACzBX,GAAG,CAACrF,IAAI,CAACtE,MAAM,IAAIsK,SAAS;YAC5BH,OAAO,GAAGR,GAAG;UACjB;QACJ;MACJ;IACJ;IACA,OAAOQ,OAAO;EAClB;AACJ;AACA,SAASP,WAAWA,CAACuB,IAAI,EAAEC,IAAI,EAAE;EAC7B,IAAI,CAACD,IAAI,IAAI,CAACC,IAAI,EAAE;IAChB,OAAO,IAAI;EACf;EACA,IAAIC,OAAO,CAACF,IAAI,CAAC,KAAKE,OAAO,CAACD,IAAI,CAAC,EAAE;IACjC,OAAO,KAAK;EAChB;EACA,OAAOlU,gBAAgB,CAACiU,IAAI,CAACP,QAAQ,EAAEQ,IAAI,CAACR,QAAQ,CAAC;AACzD;AAEA,SAASU,4BAA4BA,CAACV,QAAQ,EAAEM,OAAO,EAAE;EACrD,IAAIK,KAAK,GAAG,CAAC,CAAC;EACd,KAAK,IAAIC,SAAS,IAAIN,OAAO,CAACO,WAAW,CAACC,mBAAmB,EAAE;IAC3DC,MAAM,CAACC,MAAM,CAACL,KAAK,EAAEC,SAAS,CAACZ,QAAQ,EAAEM,OAAO,CAAC,CAAC;EACtD;EACAS,MAAM,CAACC,MAAM,CAACL,KAAK,EAAEM,iBAAiB,CAACjB,QAAQ,EAAEM,OAAO,CAACY,OAAO,CAAC,CAAC;EAClE,OAAOP,KAAK;AAChB;AACA,SAASM,iBAAiBA,CAACE,IAAI,EAAED,OAAO,EAAE;EACtC,OAAO;IACHE,IAAI,EAAEF,OAAO,CAACG,MAAM,CAACF,IAAI,CAAClB,KAAK,CAAC7M,KAAK,CAAC;IACtCkO,OAAO,EAAEJ,OAAO,CAACK,SAAS,CAACJ,IAAI,CAAClB,KAAK,CAAC7M,KAAK,EAAE;MAAEoO,QAAQ,EAAEL,IAAI,CAACM;IAAO,CAAC,CAAC;IACvEA,MAAM,EAAEN,IAAI,CAACM;EACjB,CAAC;AACL;;AAEA;AACA;AACA;AACA;AACA,MAAMC,YAAY,SAASnV,WAAW,CAAC;EACnCiC,WAAWA,CAACmT,QAAQ,EAAE;IAClB,KAAK,CAACA,QAAQ,CAAC;IACf,IAAI,CAAC5D,iBAAiB,GAAIxO,GAAG,IAAK;MAC9B,IAAI;QAAEgO;MAAS,CAAC,GAAG,IAAI;MACvB,IAAIvL,MAAM,GAAGzC,GAAG,CAAC8B,SAAS,CAACf,MAAM;MACjC;MACAiN,QAAQ,CAACf,aAAa,CAAC,CAAC,IAAI,CAAC2C,SAAS,CAACyC,iBAAiB,CAAC5P,MAAM,CAAC,CAAC;IACrE,CAAC;IACD;IACA,IAAI,CAACsM,aAAa,GAAInP,EAAE,IAAK;MACzB,IAAI;QAAEgQ;MAAU,CAAC,GAAG,IAAI;MACxB,IAAI;QAAE3D;MAAQ,CAAC,GAAG,IAAI,CAAC+B,QAAQ;MAC/B,IAAI,CAAC/B,OAAO,CAACvM,cAAc,EAAE;QACzB,IAAI;UAAE2O,UAAU;UAAEE;QAAS,CAAC,GAAG,IAAI,CAAC+D,WAAW;QAC/C,IAAIjE,UAAU,IAAIE,QAAQ,IAAIkB,WAAW,CAACpB,UAAU,EAAEE,QAAQ,CAAC,EAAE;UAC7D,IAAI;YAAEwC;UAAQ,CAAC,GAAGnB,SAAS;UAC3B,IAAI2C,GAAG,GAAGf,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEN,4BAA4B,CAAC9C,UAAU,CAACoC,QAAQ,EAAEM,OAAO,CAAC,CAAC,EAAE;YAAEyB,KAAK,EAAEnE,UAAU,CAACmE,KAAK;YAAEC,OAAO,EAAE7S,EAAE,CAACkC,SAAS;YAAE4Q,IAAI,EAAE3B,OAAO,CAAC4B,OAAO,IAAI5B,OAAO,CAAC6B,WAAW,CAACF;UAAK,CAAC,CAAC;UAC7M3B,OAAO,CAAC7Q,OAAO,CAACC,OAAO,CAAC,WAAW,EAAEoS,GAAG,CAAC;QAC7C;MACJ;IACJ,CAAC;IACD;IACA,IAAI,CAACvE,QAAQ,GAAG,IAAIzC,yBAAyB,CAAC6G,QAAQ,CAACvK,EAAE,CAAC;IAC1D,IAAI,CAACmG,QAAQ,CAACxB,YAAY,CAACpE,SAAS,GAAG,KAAK;IAC5C,IAAIkK,WAAW,GAAG,IAAI,CAACA,WAAW,GAAG,IAAIvE,WAAW,CAAC,IAAI,CAACC,QAAQ,EAAE/Q,0BAA0B,CAACmV,QAAQ,CAAC,CAAC;IACzGE,WAAW,CAACpS,OAAO,CAAC0M,EAAE,CAAC,aAAa,EAAE,IAAI,CAAC4B,iBAAiB,CAAC;IAC7D8D,WAAW,CAACpS,OAAO,CAAC0M,EAAE,CAAC,SAAS,EAAE,IAAI,CAACmC,aAAa,CAAC;EACzD;EACAzM,OAAOA,CAAA,EAAG;IACN,IAAI,CAAC0L,QAAQ,CAAC1L,OAAO,CAAC,CAAC;EAC3B;AACJ;;AAEA;AACA;AACA;AACA;AACA,MAAMuQ,aAAa,SAAS7V,WAAW,CAAC;EACpCiC,WAAWA,CAACmT,QAAQ,EAAE;IAClB,KAAK,CAACA,QAAQ,CAAC;IACf,IAAI,CAACU,aAAa,GAAG,IAAI;IACzB,IAAI,CAACtE,iBAAiB,GAAI5O,EAAE,IAAK;MAC7B,IAAI;QAAEgQ,SAAS;QAAE5B;MAAS,CAAC,GAAG,IAAI;MAClC,IAAI;QAAE+E;MAAQ,CAAC,GAAGnD,SAAS,CAACmB,OAAO;MACnC,IAAIiC,SAAS,GAAGD,OAAO,CAACE,UAAU,IAC9BrD,SAAS,CAACyC,iBAAiB,CAACzS,EAAE,CAACkC,SAAS,CAACf,MAAM,CAAC;MACpD;MACAiN,QAAQ,CAACf,aAAa,CAAC,CAAC+F,SAAS,CAAC;MAClC;MACAhF,QAAQ,CAACxC,KAAK,GAAG5L,EAAE,CAACmC,OAAO,GAAGmR,wBAAwB,CAACtD,SAAS,CAAC,GAAG,IAAI;IAC5E,CAAC;IACD,IAAI,CAACjB,eAAe,GAAI/O,EAAE,IAAK;MAC3B,IAAI,CAACgQ,SAAS,CAACmB,OAAO,CAAC6B,WAAW,CAACO,QAAQ,CAACvT,EAAE,CAAC,CAAC,CAAC;IACrD,CAAC;IACD,IAAI,CAACwT,eAAe,GAAG,CAAC5D,GAAG,EAAE6D,OAAO,KAAK;MACrC,IAAI;QAAEtC;MAAQ,CAAC,GAAG,IAAI,CAACnB,SAAS;MAChC,IAAIkD,aAAa,GAAG,IAAI;MACxB,IAAIQ,SAAS,GAAG,KAAK;MACrB,IAAI9D,GAAG,EAAE;QACL,IAAInB,UAAU,GAAG,IAAI,CAACiE,WAAW,CAACjE,UAAU;QAC5C,IAAIkF,UAAU,GAAG/D,GAAG,CAACsB,WAAW,KAAKzC,UAAU,CAACyC,WAAW,IACpD,IAAI,CAAC0C,iBAAiB,IACtB,CAAC,IAAI,CAACA,iBAAiB,CAACnF,UAAU,EAAEmB,GAAG,CAAC;QAC/C,IAAI,CAAC+D,UAAU,EAAE;UACbT,aAAa,GAAGW,qBAAqB,CAACpF,UAAU,EAAEmB,GAAG,EAAEuB,OAAO,CAACO,WAAW,CAACoC,yBAAyB,CAAC;QACzG;QACA,IAAI,CAACZ,aAAa,IAAI,CAAC5V,oBAAoB,CAAC4V,aAAa,EAAEtD,GAAG,CAACe,WAAW,EAAEQ,OAAO,CAAC,EAAE;UAClFuC,SAAS,GAAG,IAAI;UAChBR,aAAa,GAAG,IAAI;QACxB;MACJ;MACA,IAAIA,aAAa,EAAE;QACf/B,OAAO,CAAC4C,QAAQ,CAAC;UAAEpH,IAAI,EAAE,cAAc;UAAEqH,SAAS,EAAEd;QAAc,CAAC,CAAC;MACxE,CAAC,MACI,IAAI,CAACO,OAAO,EAAE;QAAE;QACjBtC,OAAO,CAAC4C,QAAQ,CAAC;UAAEpH,IAAI,EAAE;QAAiB,CAAC,CAAC;MAChD;MACA,IAAI,CAAC+G,SAAS,EAAE;QACZnW,YAAY,CAAC,CAAC;MAClB,CAAC,MACI;QACDC,aAAa,CAAC,CAAC;MACnB;MACA,IAAI,CAACiW,OAAO,EAAE;QACV,IAAI,CAACP,aAAa,GAAGA,aAAa,CAAC,CAAC;MACxC;IACJ,CAAC;IACD,IAAI,CAACjE,eAAe,GAAI7O,GAAG,IAAK;MAC5B,IAAI,IAAI,CAAC8S,aAAa,EAAE;QACpB;QACAzV,iBAAiB,CAAC,IAAI,CAACyV,aAAa,EAAE9S,GAAG,EAAE,IAAI,CAAC4P,SAAS,CAACmB,OAAO,CAAC;QAClE,IAAI,CAAC+B,aAAa,GAAG,IAAI;MAC7B;IACJ,CAAC;IACD,IAAI;MAAElD;IAAU,CAAC,GAAGwC,QAAQ;IAC5B,IAAI;MAAEW;IAAQ,CAAC,GAAGnD,SAAS,CAACmB,OAAO;IACnC,IAAI/C,QAAQ,GAAG,IAAI,CAACA,QAAQ,GAAG,IAAIzC,yBAAyB,CAAC6G,QAAQ,CAACvK,EAAE,CAAC;IACzEmG,QAAQ,CAACtC,kBAAkB,GAAG,KAAK;IACnCsC,QAAQ,CAACvC,WAAW,GAAGsH,OAAO,CAACc,iBAAiB,IAAI,CAAC;IACrD7F,QAAQ,CAACxB,YAAY,CAACpE,SAAS,GAAG2K,OAAO,CAACe,UAAU;IACpD,IAAIxB,WAAW,GAAG,IAAI,CAACA,WAAW,GAAG,IAAIvE,WAAW,CAAC,IAAI,CAACC,QAAQ,EAAE/Q,0BAA0B,CAACmV,QAAQ,CAAC,CAAC;IACzGE,WAAW,CAACpS,OAAO,CAAC0M,EAAE,CAAC,aAAa,EAAE,IAAI,CAAC4B,iBAAiB,CAAC;IAC7D8D,WAAW,CAACpS,OAAO,CAAC0M,EAAE,CAAC,WAAW,EAAE,IAAI,CAAC+B,eAAe,CAAC;IACzD2D,WAAW,CAACpS,OAAO,CAAC0M,EAAE,CAAC,WAAW,EAAE,IAAI,CAACwG,eAAe,CAAC;IACzDd,WAAW,CAACpS,OAAO,CAAC0M,EAAE,CAAC,WAAW,EAAE,IAAI,CAACiC,eAAe,CAAC;EAC7D;EACAvM,OAAOA,CAAA,EAAG;IACN,IAAI,CAAC0L,QAAQ,CAAC1L,OAAO,CAAC,CAAC;EAC3B;AACJ;AACA,SAAS4Q,wBAAwBA,CAACtD,SAAS,EAAE;EACzC,IAAI;IAAEmD;EAAQ,CAAC,GAAGnD,SAAS,CAACmB,OAAO;EACnC,IAAIvF,KAAK,GAAGuH,OAAO,CAACgB,oBAAoB;EACxC,IAAIvI,KAAK,IAAI,IAAI,EAAE;IACfA,KAAK,GAAGuH,OAAO,CAACiB,cAAc;EAClC;EACA,OAAOxI,KAAK;AAChB;AACA,SAASiI,qBAAqBA,CAACzC,IAAI,EAAEC,IAAI,EAAEyC,yBAAyB,EAAE;EAClE,IAAIO,SAAS,GAAGjD,IAAI,CAACP,QAAQ;EAC7B,IAAIyD,SAAS,GAAGjD,IAAI,CAACR,QAAQ;EAC7B,IAAI0D,EAAE,GAAG,CACLF,SAAS,CAACvD,KAAK,CAAC7M,KAAK,EACrBoQ,SAAS,CAACvD,KAAK,CAAC0D,GAAG,EACnBF,SAAS,CAACxD,KAAK,CAAC7M,KAAK,EACrBqQ,SAAS,CAACxD,KAAK,CAAC0D,GAAG,CACtB;EACDD,EAAE,CAACE,IAAI,CAAC/W,cAAc,CAAC;EACvB,IAAI8T,KAAK,GAAG,CAAC,CAAC;EACd,KAAK,IAAIkD,WAAW,IAAIZ,yBAAyB,EAAE;IAC/C,IAAIa,GAAG,GAAGD,WAAW,CAACtD,IAAI,EAAEC,IAAI,CAAC;IACjC,IAAIsD,GAAG,KAAK,KAAK,EAAE;MACf,OAAO,IAAI;IACf;IACA,IAAIA,GAAG,EAAE;MACL/C,MAAM,CAACC,MAAM,CAACL,KAAK,EAAEmD,GAAG,CAAC;IAC7B;EACJ;EACAnD,KAAK,CAACV,KAAK,GAAG;IAAE7M,KAAK,EAAEsQ,EAAE,CAAC,CAAC,CAAC;IAAEC,GAAG,EAAED,EAAE,CAAC,CAAC;EAAE,CAAC;EAC1C/C,KAAK,CAACc,MAAM,GAAG+B,SAAS,CAAC/B,MAAM;EAC/B,OAAOd,KAAK;AAChB;AAEA,MAAMoD,aAAa,SAASxX,WAAW,CAAC;EACpCiC,WAAWA,CAACmT,QAAQ,EAAE;IAClB,KAAK,CAACA,QAAQ,CAAC;IACf;IACA,IAAI,CAACjT,SAAS,GAAG,IAAI;IACrB,IAAI,CAACsV,UAAU,GAAG,IAAI,CAAC,CAAC;IACxB,IAAI,CAACjV,UAAU,GAAG,KAAK;IACvB,IAAI,CAACkV,UAAU,GAAG,IAAI;IACtB,IAAI,CAACC,cAAc,GAAG,IAAI,CAAC,CAAC;IAC5B,IAAI,CAACC,gBAAgB,GAAG,IAAI;IAC5B,IAAI,CAACC,aAAa,GAAG,IAAI;IACzB,IAAI,CAACC,qBAAqB,GAAG,IAAI;IACjC,IAAI,CAACtG,iBAAiB,GAAI5O,EAAE,IAAK;MAC7B,IAAImV,UAAU,GAAGnV,EAAE,CAACkC,SAAS,CAACf,MAAM;MACpC,IAAI;QAAE6O,SAAS;QAAE5B;MAAS,CAAC,GAAG,IAAI;MAClC,IAAI;QAAE9B;MAAO,CAAC,GAAG8B,QAAQ;MACzB,IAAI;QAAE+E;MAAQ,CAAC,GAAGnD,SAAS,CAACmB,OAAO;MACnC,IAAIiE,cAAc,GAAGpF,SAAS,CAACmB,OAAO;MACtC,IAAI,CAAC5R,SAAS,GAAGS,EAAE,CAACT,SAAS;MAC7B,IAAIsV,UAAU,GAAG,IAAI,CAACA,UAAU,GAAGlX,QAAQ,CAACqC,EAAE,CAACT,SAAS,CAAC;MACzD,IAAIuV,UAAU,GAAG,IAAI,CAACA,UAAU,GAAGD,UAAU,CAACC,UAAU;MACxD,IAAIO,eAAe,GAAGP,UAAU,CAACQ,QAAQ,CAACC,UAAU;MACpD,IAAI,CAACR,cAAc,GAAGnX,iBAAiB,CAACwX,cAAc,CAACI,cAAc,CAAC,CAAC,CAACC,UAAU,EAAEJ,eAAe,CAAC;MACpGjH,QAAQ,CAACvC,WAAW,GAAG7L,EAAE,CAACmC,OAAO,GAAG,CAAC,GAAGgR,OAAO,CAACuC,oBAAoB;MACpEtH,QAAQ,CAACxC,KAAK;MACV;MACC5L,EAAE,CAACmC,OAAO,IAAIkT,eAAe,KAAKrF,SAAS,CAACwB,KAAK,CAACmE,cAAc,GAC7DC,sBAAsB,CAAC5F,SAAS,CAAC,GACjC,IAAI;MACZ,IAAImD,OAAO,CAAC0C,iBAAiB,EAAE;QAC3BvJ,MAAM,CAACzI,UAAU,GAAGsP,OAAO,CAAC0C,iBAAiB;MACjD,CAAC,MACI;QACDvJ,MAAM,CAACzI,UAAU,GAAGhI,cAAc,CAACsZ,UAAU,EAAE,KAAK,CAAC;MACzD;MACA7I,MAAM,CAACtI,cAAc,GAAGmP,OAAO,CAAC2C,kBAAkB;MAClD,IAAIC,OAAO,GAAG/F,SAAS,CAACgG,gBAAgB,CAACb,UAAU,CAAC,IAChD,CAACtZ,cAAc,CAACsZ,UAAU,EAAE,mBAAmB,CAAC,CAAC,CAAC;MACtD/G,QAAQ,CAACf,aAAa,CAAC,CAAC0I,OAAO,CAAC;MAChC;MACA;MACA,IAAI,CAACnW,UAAU,GAAGmW,OAAO,IACrB/V,EAAE,CAACT,SAAS,CAACkG,SAAS,CAACsL,QAAQ,CAAC,oBAAoB,CAAC;IAC7D,CAAC;IACD,IAAI,CAAChC,eAAe,GAAI/O,EAAE,IAAK;MAC3B,IAAIoV,cAAc,GAAG,IAAI,CAACpF,SAAS,CAACmB,OAAO;MAC3C,IAAI2D,UAAU,GAAG,IAAI,CAACA,UAAU;MAChC,IAAIO,eAAe,GAAGP,UAAU,CAACQ,QAAQ,CAACC,UAAU;MACpD,IAAIvV,EAAE,CAACmC,OAAO,EAAE;QACZ;QACA,IAAIkT,eAAe,KAAK,IAAI,CAACrF,SAAS,CAACwB,KAAK,CAACmE,cAAc,EAAE;UACzDP,cAAc,CAACrB,QAAQ,CAAC;YAAEpH,IAAI,EAAE,cAAc;YAAE0I;UAAgB,CAAC,CAAC;QACtE;MACJ,CAAC,MACI;QACD;QACAD,cAAc,CAACrB,QAAQ,CAAC;UAAEpH,IAAI,EAAE;QAAiB,CAAC,CAAC;MACvD;MACA,IAAI,IAAI,CAAC/M,UAAU,EAAE;QACjBwV,cAAc,CAACpC,WAAW,CAACO,QAAQ,CAACvT,EAAE,CAAC,CAAC,CAAC;QACzCoV,cAAc,CAAC9U,OAAO,CAACC,OAAO,CAAC,gBAAgB,EAAE;UAC7C0H,EAAE,EAAE,IAAI,CAAC1I,SAAS;UAClB0W,KAAK,EAAE,IAAIpY,SAAS,CAACuX,cAAc,EAAEN,UAAU,CAACoB,GAAG,EAAEpB,UAAU,CAACQ,QAAQ,CAAC;UACzEzC,OAAO,EAAE7S,EAAE,CAACkC,SAAS;UACrB4Q,IAAI,EAAEsC,cAAc,CAACrC;QACzB,CAAC,CAAC;MACN;IACJ,CAAC;IACD,IAAI,CAACS,eAAe,GAAG,CAAC5D,GAAG,EAAE6D,OAAO,KAAK;MACrC,IAAI,CAAC,IAAI,CAAC7T,UAAU,EAAE;QAClB;MACJ;MACA,IAAImV,cAAc,GAAG,IAAI,CAACA,cAAc;MACxC,IAAItG,UAAU,GAAG,IAAI,CAACiE,WAAW,CAACjE,UAAU;MAC5C,IAAI2G,cAAc,GAAG,IAAI,CAACpF,SAAS,CAACmB,OAAO;MAC3C;MACA,IAAI6D,gBAAgB,GAAG,IAAI;MAC3B,IAAImB,QAAQ,GAAG,IAAI;MACnB,IAAIjB,qBAAqB,GAAG,IAAI;MAChC,IAAIxB,SAAS,GAAG,KAAK;MACrB,IAAI0C,WAAW,GAAG;QACdC,cAAc,EAAEtB,cAAc;QAC9BuB,aAAa,EAAExY,qBAAqB,CAAC,CAAC;QACtCyY,OAAO,EAAE;MACb,CAAC;MACD,IAAI3G,GAAG,EAAE;QACLoF,gBAAgB,GAAGpF,GAAG,CAACuB,OAAO;QAC9B,IAAIqF,gBAAgB,GAAGxB,gBAAgB,CAAC7B,OAAO;QAC/C,IAAIiC,cAAc,KAAKJ,gBAAgB,IAClCwB,gBAAgB,CAACC,QAAQ,IAAID,gBAAgB,CAACE,SAAU,EAAE;UAC3DP,QAAQ,GAAGQ,oBAAoB,CAAClI,UAAU,EAAEmB,GAAG,EAAE,IAAI,CAACkF,UAAU,CAACQ,QAAQ,CAACxE,KAAK,CAAC7M,KAAK,EAAE+Q,gBAAgB,CAACQ,cAAc,CAAC,CAAC,CAAC9D,WAAW,CAACkF,0BAA0B,CAAC;UAChK,IAAIT,QAAQ,EAAE;YACVjB,qBAAqB,GAAGnX,yBAAyB,CAACgX,cAAc,EAAEC,gBAAgB,CAACQ,cAAc,CAAC,CAAC,CAACqB,YAAY,EAAEV,QAAQ,EAAEnB,gBAAgB,CAAC;YAC7IoB,WAAW,CAACE,aAAa,GAAGpB,qBAAqB;YACjD,IAAI,CAAClX,kBAAkB,CAACoY,WAAW,EAAExG,GAAG,CAACe,WAAW,EAAEqE,gBAAgB,CAAC,EAAE;cACrEtB,SAAS,GAAG,IAAI;cAChByC,QAAQ,GAAG,IAAI;cACfjB,qBAAqB,GAAG,IAAI;cAC5BkB,WAAW,CAACE,aAAa,GAAGxY,qBAAqB,CAAC,CAAC;YACvD;UACJ;QACJ,CAAC,MACI;UACDkX,gBAAgB,GAAG,IAAI;QAC3B;MACJ;MACA,IAAI,CAAC8B,WAAW,CAAC9B,gBAAgB,EAAEoB,WAAW,CAAC;MAC/C,IAAI,CAAC1C,SAAS,EAAE;QACZnW,YAAY,CAAC,CAAC;MAClB,CAAC,MACI;QACDC,aAAa,CAAC,CAAC;MACnB;MACA,IAAI,CAACiW,OAAO,EAAE;QACV,IAAI2B,cAAc,KAAKJ,gBAAgB;QAAI;QACvCnF,WAAW,CAACpB,UAAU,EAAEmB,GAAG,CAAC,EAAE;UAC9BuG,QAAQ,GAAG,IAAI;QACnB;QACA,IAAI,CAAC/H,QAAQ,CAACb,oBAAoB,CAAC,CAAC4I,QAAQ,CAAC;QAC7C;QACA;QACA,IAAI,CAAC/H,QAAQ,CAACd,kBAAkB,CAAC,CAACsC,GAAG,IAAI,CAAC,IAAI,CAACrQ,SAAS,CAACkM,WAAW,CAAC,CAAC,CAACsL,aAAa,CAAC,kBAAkB,CAAC,CAAC;QACzG;QACA,IAAI,CAAC/B,gBAAgB,GAAGA,gBAAgB;QACxC,IAAI,CAACC,aAAa,GAAGkB,QAAQ;QAC7B,IAAI,CAACjB,qBAAqB,GAAGA,qBAAqB;MACtD;IACJ,CAAC;IACD,IAAI,CAACjG,eAAe,GAAG,MAAM;MACzB,IAAI,CAAC,IAAI,CAACrP,UAAU,EAAE;QAClB,IAAI,CAACmB,OAAO,CAAC,CAAC,CAAC,CAAC;MACpB;IACJ,CAAC;IACD,IAAI,CAACoO,aAAa,GAAInP,EAAE,IAAK;MACzB,IAAI,IAAI,CAACJ,UAAU,EAAE;QACjB,IAAIwV,cAAc,GAAG,IAAI,CAACpF,SAAS,CAACmB,OAAO;QAC3C,IAAI6F,WAAW,GAAG5B,cAAc,CAACrC,OAAO;QACxC,IAAI;UAAEiC,gBAAgB;UAAEC;QAAc,CAAC,GAAG,IAAI;QAC9C,IAAIgC,QAAQ,GAAG,IAAI,CAACnC,UAAU,CAACoB,GAAG;QAClC,IAAIgB,aAAa,GAAG,IAAI,CAACpC,UAAU,CAACQ,QAAQ;QAC5C,IAAI6B,QAAQ,GAAG,IAAItZ,SAAS,CAACuX,cAAc,EAAE6B,QAAQ,EAAEC,aAAa,CAAC;QACrE,IAAInC,cAAc,GAAG,IAAI,CAACA,cAAc;QACxC,IAAIG,qBAAqB,GAAG,IAAI,CAACA,qBAAqB;QACtD,IAAI;UAAEvG;QAAS,CAAC,GAAG,IAAI,CAAC+D,WAAW;QACnC,IAAI,CAAC0E,SAAS,CAAC,CAAC,CAAC,CAAC;QAClBhC,cAAc,CAAC9U,OAAO,CAACC,OAAO,CAAC,eAAe,EAAE;UAC5C0H,EAAE,EAAE,IAAI,CAAC1I,SAAS;UAClB0W,KAAK,EAAEkB,QAAQ;UACftE,OAAO,EAAE7S,EAAE,CAACkC,SAAS;UACrB4Q,IAAI,EAAEkE;QACV,CAAC,CAAC;QACF,IAAI/B,aAAa,EAAE;UACf;UACA,IAAID,gBAAgB,KAAKI,cAAc,EAAE;YACrC,IAAIiC,eAAe,GAAG,IAAIxZ,SAAS,CAACuX,cAAc,EAAEF,qBAAqB,CAACoC,IAAI,CAACL,QAAQ,CAACM,KAAK,CAAC,EAAEL,aAAa,GAAGhC,qBAAqB,CAACsC,SAAS,CAACN,aAAa,CAAC3B,UAAU,CAAC,GAAG,IAAI,CAAC;YACjLH,cAAc,CAACrB,QAAQ,CAAC;cACpBpH,IAAI,EAAE,cAAc;cACpB8I,UAAU,EAAEP;YAChB,CAAC,CAAC;YACF,IAAIuC,cAAc,GAAG;cACjBC,QAAQ,EAAEP,QAAQ;cAClBlB,KAAK,EAAEoB,eAAe;cACtBM,aAAa,EAAE1Z,cAAc,CAACiX,qBAAqB,EAAEE,cAAc,EAAE8B,aAAa,CAAC;cACnFU,MAAMA,CAAA,EAAG;gBACLxC,cAAc,CAACrB,QAAQ,CAAC;kBACpBpH,IAAI,EAAE,cAAc;kBACpB8I,UAAU,EAAEV,cAAc,CAAE;gBAChC,CAAC,CAAC;cACN;YACJ,CAAC;YACD,IAAI8C,WAAW,GAAG,CAAC,CAAC;YACpB,KAAK,IAAInD,WAAW,IAAIU,cAAc,CAACI,cAAc,CAAC,CAAC,CAAC9D,WAAW,CAACoG,qBAAqB,EAAE;cACvFlG,MAAM,CAACC,MAAM,CAACgG,WAAW,EAAEnD,WAAW,CAACO,aAAa,EAAEG,cAAc,CAAC,CAAC;YAC1E;YACAA,cAAc,CAAC9U,OAAO,CAACC,OAAO,CAAC,WAAW,EAAEqR,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAE4F,cAAc,CAAC,EAAEI,WAAW,CAAC,EAAE;cAAE5P,EAAE,EAAEjI,EAAE,CAACT,SAAS;cAAEwY,KAAK,EAAE9C,aAAa,CAAC+C,UAAU;cAAEnF,OAAO,EAAE7S,EAAE,CAACkC,SAAS;cAAE4Q,IAAI,EAAEkE;YAAY,CAAC,CAAC,CAAC;YAC1N5B,cAAc,CAAC9U,OAAO,CAACC,OAAO,CAAC,aAAa,EAAEkX,cAAc,CAAC;YAC7D;UACJ,CAAC,MACI,IAAIzC,gBAAgB,EAAE;YACvB,IAAIiD,cAAc,GAAG;cACjBhC,KAAK,EAAEkB,QAAQ;cACfQ,aAAa,EAAE1Z,cAAc,CAAC8W,cAAc,EAAEK,cAAc,EAAE8B,aAAa,CAAC;cAC5EU,MAAMA,CAAA,EAAG;gBACLxC,cAAc,CAACrB,QAAQ,CAAC;kBACpBpH,IAAI,EAAE,cAAc;kBACpB8I,UAAU,EAAEV;gBAChB,CAAC,CAAC;cACN;YACJ,CAAC;YACDK,cAAc,CAAC9U,OAAO,CAACC,OAAO,CAAC,YAAY,EAAEqR,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEoG,cAAc,CAAC,EAAE;cAAEC,SAAS,EAAElY,EAAE,CAACT,SAAS;cAAEuT,IAAI,EAAEkE;YAAY,CAAC,CAAC,CAAC;YAC9I5B,cAAc,CAACrB,QAAQ,CAAC;cACpBpH,IAAI,EAAE,eAAe;cACrB8I,UAAU,EAAEV;YAChB,CAAC,CAAC;YACFK,cAAc,CAAC9U,OAAO,CAACC,OAAO,CAAC,aAAa,EAAE0X,cAAc,CAAC;YAC7D,IAAIE,aAAa,GAAGjD,qBAAqB,CAACoC,IAAI,CAACL,QAAQ,CAACM,KAAK,CAAC;YAC9D,IAAIa,kBAAkB,GAAGlD,qBAAqB,CAACsC,SAAS,CAACN,aAAa,CAAC3B,UAAU,CAAC;YAClF,IAAI8C,aAAa,GAAG,IAAIxa,SAAS,CAACmX,gBAAgB,EAAEmD,aAAa,EAAEC,kBAAkB,CAAC;YACtFpD,gBAAgB,CAACjB,QAAQ,CAAC;cACtBpH,IAAI,EAAE,cAAc;cACpB8I,UAAU,EAAEP;YAChB,CAAC,CAAC;YACF,IAAIoD,WAAW,GAAG;cACdrC,KAAK,EAAEoC,aAAa;cACpBV,aAAa,EAAE1Z,cAAc,CAACiX,qBAAqB,EAAEF,gBAAgB,EAAEoD,kBAAkB,CAAC;cAC1FR,MAAMA,CAAA,EAAG;gBACL5C,gBAAgB,CAACjB,QAAQ,CAAC;kBACtBpH,IAAI,EAAE,eAAe;kBACrB8I,UAAU,EAAEP;gBAChB,CAAC,CAAC;cACN;YACJ,CAAC;YACDF,gBAAgB,CAAC1U,OAAO,CAACC,OAAO,CAAC,UAAU,EAAE+X,WAAW,CAAC;YACzD,IAAItY,EAAE,CAACmC,OAAO,EAAE;cACZ6S,gBAAgB,CAACjB,QAAQ,CAAC;gBACtBpH,IAAI,EAAE,cAAc;gBACpB0I,eAAe,EAAE6B,aAAa,CAAC3B;cACnC,CAAC,CAAC;YACN;YACAP,gBAAgB,CAAC1U,OAAO,CAACC,OAAO,CAAC,MAAM,EAAEqR,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEN,4BAA4B,CAAC5C,QAAQ,CAACkC,QAAQ,EAAEmE,gBAAgB,CAAC,CAAC,EAAE;cAAEkD,SAAS,EAAElY,EAAE,CAACT,SAAS;cAAEsT,OAAO,EAAE7S,EAAE,CAACkC,SAAS;cAAE4Q,IAAI,EAAEnE,QAAQ,CAACwC,OAAO,CAAC4B;YAAQ,CAAC,CAAC,CAAC;YACjOiC,gBAAgB,CAAC1U,OAAO,CAACC,OAAO,CAAC,cAAc,EAAEqR,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEyG,WAAW,CAAC,EAAE;cAAEJ,SAAS,EAAElY,EAAE,CAACT,SAAS;cAAEuT,IAAI,EAAEnE,QAAQ,CAACwC,OAAO,CAAC4B;YAAQ,CAAC,CAAC,CAAC;UAChK;QACJ,CAAC,MACI;UACDqC,cAAc,CAAC9U,OAAO,CAACC,OAAO,CAAC,cAAc,CAAC;QAClD;MACJ;MACA,IAAI,CAACQ,OAAO,CAAC,CAAC;IAClB,CAAC;IACD,IAAI;MAAEiP;IAAU,CAAC,GAAG,IAAI;IACxB,IAAI;MAAEmD;IAAQ,CAAC,GAAGnD,SAAS,CAACmB,OAAO;IACnC,IAAI/C,QAAQ,GAAG,IAAI,CAACA,QAAQ,GAAG,IAAIzC,yBAAyB,CAAC6G,QAAQ,CAACvK,EAAE,CAAC;IACzEmG,QAAQ,CAAC/B,OAAO,CAAC7M,QAAQ,GAAGoV,aAAa,CAAC2D,QAAQ;IAClDnK,QAAQ,CAACtC,kBAAkB,GAAG,KAAK;IACnCsC,QAAQ,CAACxB,YAAY,CAACpE,SAAS,GAAG2K,OAAO,CAACe,UAAU;IACpD,IAAIxB,WAAW,GAAG,IAAI,CAACA,WAAW,GAAG,IAAIvE,WAAW,CAAC,IAAI,CAACC,QAAQ,EAAElQ,wBAAwB,CAAC;IAC7FwU,WAAW,CAACpE,gBAAgB,GAAGkE,QAAQ,CAACgG,cAAc;IACtD9F,WAAW,CAACpS,OAAO,CAAC0M,EAAE,CAAC,aAAa,EAAE,IAAI,CAAC4B,iBAAiB,CAAC;IAC7D8D,WAAW,CAACpS,OAAO,CAAC0M,EAAE,CAAC,WAAW,EAAE,IAAI,CAAC+B,eAAe,CAAC;IACzD2D,WAAW,CAACpS,OAAO,CAAC0M,EAAE,CAAC,WAAW,EAAE,IAAI,CAACwG,eAAe,CAAC;IACzDd,WAAW,CAACpS,OAAO,CAAC0M,EAAE,CAAC,WAAW,EAAE,IAAI,CAACiC,eAAe,CAAC;IACzDyD,WAAW,CAACpS,OAAO,CAAC0M,EAAE,CAAC,SAAS,EAAE,IAAI,CAACmC,aAAa,CAAC;EACzD;EACAzM,OAAOA,CAAA,EAAG;IACN,IAAI,CAAC0L,QAAQ,CAAC1L,OAAO,CAAC,CAAC;EAC3B;EACA;EACAoU,WAAWA,CAAC2B,WAAW,EAAEC,KAAK,EAAE;IAC5B,IAAItD,cAAc,GAAG,IAAI,CAACpF,SAAS,CAACmB,OAAO;IAC3C,IAAIwH,WAAW,GAAG,IAAI,CAAC3D,gBAAgB;IACvC;IACA,IAAI2D,WAAW,IAAIA,WAAW,KAAKF,WAAW,EAAE;MAC5C;MACA;MACA,IAAIE,WAAW,KAAKvD,cAAc,EAAE;QAChCuD,WAAW,CAAC5E,QAAQ,CAAC;UACjBpH,IAAI,EAAE,gBAAgB;UACtB+L,KAAK,EAAE;YACHrC,cAAc,EAAEqC,KAAK,CAACrC,cAAc;YACpCC,aAAa,EAAExY,qBAAqB,CAAC,CAAC;YACtCyY,OAAO,EAAE;UACb;QACJ,CAAC,CAAC;QACF;MACJ,CAAC,MACI;QACDoC,WAAW,CAAC5E,QAAQ,CAAC;UAAEpH,IAAI,EAAE;QAAmB,CAAC,CAAC;MACtD;IACJ;IACA,IAAI8L,WAAW,EAAE;MACbA,WAAW,CAAC1E,QAAQ,CAAC;QAAEpH,IAAI,EAAE,gBAAgB;QAAE+L;MAAM,CAAC,CAAC;IAC3D;EACJ;EACAtB,SAASA,CAAA,EAAG;IACR,IAAIwB,eAAe,GAAG,IAAI,CAAC5I,SAAS,CAACmB,OAAO;IAC5C,IAAI;MAAE6D;IAAiB,CAAC,GAAG,IAAI;IAC/B,IAAIA,gBAAgB,EAAE;MAClBA,gBAAgB,CAACjB,QAAQ,CAAC;QAAEpH,IAAI,EAAE;MAAmB,CAAC,CAAC;IAC3D;IACA;IACA,IAAIiM,eAAe,KAAK5D,gBAAgB,EAAE;MACtC4D,eAAe,CAAC7E,QAAQ,CAAC;QAAEpH,IAAI,EAAE;MAAmB,CAAC,CAAC;IAC1D;EACJ;EACA5L,OAAOA,CAAA,EAAG;IACN,IAAI,CAAC8T,UAAU,GAAG,IAAI;IACtB,IAAI,CAACjV,UAAU,GAAG,KAAK;IACvB,IAAI,CAACkV,UAAU,GAAG,IAAI;IACtB,IAAI,CAACC,cAAc,GAAG,IAAI;IAC1B,IAAI,CAACC,gBAAgB,GAAG,IAAI;IAC5B,IAAI,CAACC,aAAa,GAAG,IAAI;IACzB,IAAI,CAACC,qBAAqB,GAAG,IAAI;EACrC;AACJ;AACA;AACA;AACAN,aAAa,CAAC2D,QAAQ,GAAG,0CAA0C;AACnE,SAAS5B,oBAAoBA,CAACvF,IAAI,EAAEC,IAAI,EAAEwH,kBAAkB,EAAEC,SAAS,EAAE;EACrE,IAAIzE,SAAS,GAAGjD,IAAI,CAACP,QAAQ;EAC7B,IAAIyD,SAAS,GAAGjD,IAAI,CAACR,QAAQ;EAC7B,IAAIkI,KAAK,GAAG1E,SAAS,CAACvD,KAAK,CAAC7M,KAAK;EACjC,IAAI+U,KAAK,GAAG1E,SAAS,CAACxD,KAAK,CAAC7M,KAAK;EACjC,IAAIgV,aAAa,GAAG,CAAC,CAAC;EACtB,IAAI5E,SAAS,CAAC/B,MAAM,KAAKgC,SAAS,CAAChC,MAAM,EAAE;IACvC2G,aAAa,CAAC3G,MAAM,GAAGgC,SAAS,CAAChC,MAAM;IACvC2G,aAAa,CAACC,MAAM,GAAG7H,IAAI,CAACF,OAAO,CAACgC,OAAO,CAACgG,sBAAsB;IAClE,IAAI7E,SAAS,CAAChC,MAAM,EAAE;MAClB;MACA;MACAyG,KAAK,GAAG5a,UAAU,CAAC0a,kBAAkB,CAAC;IAC1C,CAAC,MACI;MACD;MACA;MACAE,KAAK,GAAGF,kBAAkB;IAC9B;EACJ;EACA,IAAId,KAAK,GAAG3Z,SAAS,CAAC2a,KAAK,EAAEC,KAAK,EAAE5H,IAAI,CAACD,OAAO,CAACY,OAAO,EAAEX,IAAI,CAACF,WAAW,KAAKG,IAAI,CAACH,WAAW,GAC3FE,IAAI,CAACgI,SAAS,GACd,IAAI,CAAC;EACT,IAAIrB,KAAK,CAACsB,YAAY,EAAE;IAAE;IACtBJ,aAAa,CAAC3G,MAAM,GAAG,KAAK;EAChC;EACA,IAAI6D,QAAQ,GAAG;IACX6B,UAAU,EAAED,KAAK;IACjBkB;EACJ,CAAC;EACD,KAAK,IAAIK,QAAQ,IAAIR,SAAS,EAAE;IAC5BQ,QAAQ,CAACnD,QAAQ,EAAE/E,IAAI,EAAEC,IAAI,CAAC;EAClC;EACA,OAAO8E,QAAQ;AACnB;AACA,SAASP,sBAAsBA,CAAC5F,SAAS,EAAE;EACvC,IAAI;IAAEmD;EAAQ,CAAC,GAAGnD,SAAS,CAACmB,OAAO;EACnC,IAAIvF,KAAK,GAAGuH,OAAO,CAACoG,mBAAmB;EACvC,IAAI3N,KAAK,IAAI,IAAI,EAAE;IACfA,KAAK,GAAGuH,OAAO,CAACiB,cAAc;EAClC;EACA,OAAOxI,KAAK;AAChB;AAEA,MAAM4N,aAAa,SAASpc,WAAW,CAAC;EACpCiC,WAAWA,CAACmT,QAAQ,EAAE;IAClB,KAAK,CAACA,QAAQ,CAAC;IACf;IACA,IAAI,CAACiH,aAAa,GAAG,IAAI;IACzB,IAAI,CAACC,WAAW,GAAG,IAAI,CAAC,CAAC;IACzB,IAAI,CAAC5E,UAAU,GAAG,IAAI;IACtB,IAAI,CAACC,cAAc,GAAG,IAAI;IAC1B,IAAI,CAACE,aAAa,GAAG,IAAI;IACzB,IAAI,CAACC,qBAAqB,GAAG,IAAI;IACjC,IAAI,CAACtG,iBAAiB,GAAI5O,EAAE,IAAK;MAC7B,IAAI;QAAEgQ;MAAU,CAAC,GAAG,IAAI;MACxB,IAAI2J,KAAK,GAAG,IAAI,CAACC,UAAU,CAAC5Z,EAAE,CAAC;MAC/B,IAAI6Z,GAAG,GAAGlc,QAAQ,CAACgc,KAAK,CAAC;MACzB,IAAI7E,UAAU,GAAG,IAAI,CAACA,UAAU,GAAG+E,GAAG,CAAC/E,UAAU;MACjD,IAAI,CAAC1G,QAAQ,CAACvC,WAAW,GAAGmE,SAAS,CAACmB,OAAO,CAACgC,OAAO,CAACuC,oBAAoB;MAC1E;MACA,IAAI,CAACtH,QAAQ,CAACf,aAAa,CAAC,CAAC,IAAI,CAAC2C,SAAS,CAACgG,gBAAgB,CAAChW,EAAE,CAACkC,SAAS,CAACf,MAAM,CAAC,IAC5EnB,EAAE,CAACmC,OAAO,IAAI,IAAI,CAAC6N,SAAS,CAACwB,KAAK,CAACmE,cAAc,KAAKb,UAAU,CAACQ,QAAQ,CAACC,UAAW,CAAC;IAC/F,CAAC;IACD,IAAI,CAACxG,eAAe,GAAI/O,EAAE,IAAK;MAC3B,IAAI;QAAEmR;MAAQ,CAAC,GAAG,IAAI,CAACnB,SAAS;MAChC,IAAI8E,UAAU,GAAG,IAAI,CAACA,UAAU;MAChC,IAAI,CAACC,cAAc,GAAGnX,iBAAiB,CAACuT,OAAO,CAACqE,cAAc,CAAC,CAAC,CAACC,UAAU,EAAE,IAAI,CAACX,UAAU,CAACQ,QAAQ,CAACC,UAAU,CAAC;MACjH,IAAIoE,KAAK,GAAG,IAAI,CAACC,UAAU,CAAC5Z,EAAE,CAAC;MAC/B,IAAI,CAACyZ,aAAa,GAAGE,KAAK;MAC1B,IAAI,CAACD,WAAW,GAAG/b,QAAQ,CAACgc,KAAK,CAAC;MAClCxI,OAAO,CAAC6B,WAAW,CAACO,QAAQ,CAAC,CAAC;MAC9BpC,OAAO,CAAC7Q,OAAO,CAACC,OAAO,CAAC,kBAAkB,EAAE;QACxC0H,EAAE,EAAE0R,KAAK;QACT1D,KAAK,EAAE,IAAIpY,SAAS,CAACsT,OAAO,EAAE2D,UAAU,CAACoB,GAAG,EAAEpB,UAAU,CAACQ,QAAQ,CAAC;QAClEzC,OAAO,EAAE7S,EAAE,CAACkC,SAAS;QACrB4Q,IAAI,EAAE3B,OAAO,CAAC4B;MAClB,CAAC,CAAC;IACN,CAAC;IACD,IAAI,CAACS,eAAe,GAAG,CAAC5D,GAAG,EAAE6D,OAAO,EAAEzT,EAAE,KAAK;MACzC,IAAI;QAAEmR;MAAQ,CAAC,GAAG,IAAI,CAACnB,SAAS;MAChC,IAAI+E,cAAc,GAAG,IAAI,CAACA,cAAc;MACxC,IAAItG,UAAU,GAAG,IAAI,CAACiE,WAAW,CAACjE,UAAU;MAC5C,IAAIyI,aAAa,GAAG,IAAI,CAACpC,UAAU,CAACQ,QAAQ;MAC5C,IAAIa,QAAQ,GAAG,IAAI;MACnB,IAAIjB,qBAAqB,GAAG,IAAI;MAChC,IAAIxB,SAAS,GAAG,KAAK;MACrB,IAAI0C,WAAW,GAAG;QACdC,cAAc,EAAEtB,cAAc;QAC9BuB,aAAa,EAAExY,qBAAqB,CAAC,CAAC;QACtCyY,OAAO,EAAE;MACb,CAAC;MACD,IAAI3G,GAAG,EAAE;QACL,IAAI+D,UAAU,GAAG/D,GAAG,CAACsB,WAAW,KAAKzC,UAAU,CAACyC,WAAW,IACpD,IAAI,CAAC0C,iBAAiB,IACtB,CAAC,IAAI,CAACA,iBAAiB,CAACnF,UAAU,EAAEmB,GAAG,CAAC;QAC/C,IAAI,CAAC+D,UAAU,EAAE;UACbwC,QAAQ,GAAG2D,eAAe,CAACrL,UAAU,EAAEmB,GAAG,EAAE5P,EAAE,CAACT,SAAS,CAACkG,SAAS,CAACsL,QAAQ,CAAC,wBAAwB,CAAC,EAAEmG,aAAa,CAACpG,KAAK,CAAC;QAC/H;MACJ;MACA,IAAIqF,QAAQ,EAAE;QACVjB,qBAAqB,GAAGnX,yBAAyB,CAACgX,cAAc,EAAE5D,OAAO,CAACqE,cAAc,CAAC,CAAC,CAACqB,YAAY,EAAEV,QAAQ,EAAEhF,OAAO,CAAC;QAC3HiF,WAAW,CAACE,aAAa,GAAGpB,qBAAqB;QACjD,IAAI,CAAClX,kBAAkB,CAACoY,WAAW,EAAExG,GAAG,CAACe,WAAW,EAAEQ,OAAO,CAAC,EAAE;UAC5DuC,SAAS,GAAG,IAAI;UAChByC,QAAQ,GAAG,IAAI;UACfjB,qBAAqB,GAAG,IAAI;UAC5BkB,WAAW,CAACE,aAAa,GAAG,IAAI;QACpC;MACJ;MACA,IAAIpB,qBAAqB,EAAE;QACvB/D,OAAO,CAAC4C,QAAQ,CAAC;UACbpH,IAAI,EAAE,kBAAkB;UACxB+L,KAAK,EAAEtC;QACX,CAAC,CAAC;MACN,CAAC,MACI;QACDjF,OAAO,CAAC4C,QAAQ,CAAC;UAAEpH,IAAI,EAAE;QAAqB,CAAC,CAAC;MACpD;MACA,IAAI,CAAC+G,SAAS,EAAE;QACZnW,YAAY,CAAC,CAAC;MAClB,CAAC,MACI;QACDC,aAAa,CAAC,CAAC;MACnB;MACA,IAAI,CAACiW,OAAO,EAAE;QACV,IAAI0C,QAAQ,IAAItG,WAAW,CAACpB,UAAU,EAAEmB,GAAG,CAAC,EAAE;UAC1CuG,QAAQ,GAAG,IAAI;QACnB;QACA,IAAI,CAAClB,aAAa,GAAGkB,QAAQ;QAC7B,IAAI,CAACjB,qBAAqB,GAAGA,qBAAqB;MACtD;IACJ,CAAC;IACD,IAAI,CAAC/F,aAAa,GAAInP,EAAE,IAAK;MACzB,IAAI;QAAEmR;MAAQ,CAAC,GAAG,IAAI,CAACnB,SAAS;MAChC,IAAIiH,QAAQ,GAAG,IAAI,CAACnC,UAAU,CAACoB,GAAG;MAClC,IAAIgB,aAAa,GAAG,IAAI,CAACpC,UAAU,CAACQ,QAAQ;MAC5C,IAAI6B,QAAQ,GAAG,IAAItZ,SAAS,CAACsT,OAAO,EAAE8F,QAAQ,EAAEC,aAAa,CAAC;MAC9D,IAAInC,cAAc,GAAG,IAAI,CAACA,cAAc;MACxC,IAAIG,qBAAqB,GAAG,IAAI,CAACA,qBAAqB;MACtD/D,OAAO,CAAC7Q,OAAO,CAACC,OAAO,CAAC,iBAAiB,EAAE;QACvC0H,EAAE,EAAE,IAAI,CAACwR,aAAa;QACtBxD,KAAK,EAAEkB,QAAQ;QACftE,OAAO,EAAE7S,EAAE,CAACkC,SAAS;QACrB4Q,IAAI,EAAE3B,OAAO,CAAC4B;MAClB,CAAC,CAAC;MACF,IAAI,IAAI,CAACkC,aAAa,EAAE;QACpB,IAAIoC,eAAe,GAAG,IAAIxZ,SAAS,CAACsT,OAAO,EAAE+D,qBAAqB,CAACoC,IAAI,CAACL,QAAQ,CAACM,KAAK,CAAC,EAAEL,aAAa,GAAGhC,qBAAqB,CAACsC,SAAS,CAACN,aAAa,CAAC3B,UAAU,CAAC,GAAG,IAAI,CAAC;QAC1KpE,OAAO,CAAC4C,QAAQ,CAAC;UACbpH,IAAI,EAAE,cAAc;UACpB8I,UAAU,EAAEP;QAChB,CAAC,CAAC;QACF,IAAIuC,cAAc,GAAG;UACjBC,QAAQ,EAAEP,QAAQ;UAClBlB,KAAK,EAAEoB,eAAe;UACtBM,aAAa,EAAE1Z,cAAc,CAACiX,qBAAqB,EAAE/D,OAAO,EAAE+F,aAAa,CAAC;UAC5EU,MAAMA,CAAA,EAAG;YACLzG,OAAO,CAAC4C,QAAQ,CAAC;cACbpH,IAAI,EAAE,cAAc;cACpB8I,UAAU,EAAEV,cAAc,CAAE;YAChC,CAAC,CAAC;UACN;QACJ,CAAC;QACD5D,OAAO,CAAC7Q,OAAO,CAACC,OAAO,CAAC,aAAa,EAAEqR,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAE4F,cAAc,CAAC,EAAE;UAAExP,EAAE,EAAE,IAAI,CAACwR,aAAa;UAAEM,UAAU,EAAE,IAAI,CAAC9E,aAAa,CAAC8E,UAAU,IAAI1b,cAAc,CAAC,CAAC,CAAC;UAAE2b,QAAQ,EAAE,IAAI,CAAC/E,aAAa,CAAC+E,QAAQ,IAAI3b,cAAc,CAAC,CAAC,CAAC;UAAEwU,OAAO,EAAE7S,EAAE,CAACkC,SAAS;UAAE4Q,IAAI,EAAE3B,OAAO,CAAC4B;QAAQ,CAAC,CAAC,CAAC;QAC9R5B,OAAO,CAAC7Q,OAAO,CAACC,OAAO,CAAC,aAAa,EAAEkX,cAAc,CAAC;MAC1D,CAAC,MACI;QACDtG,OAAO,CAAC7Q,OAAO,CAACC,OAAO,CAAC,gBAAgB,CAAC;MAC7C;MACA;MACA,IAAI,CAACmZ,WAAW,GAAG,IAAI;MACvB,IAAI,CAAC3E,cAAc,GAAG,IAAI;MAC1B,IAAI,CAACE,aAAa,GAAG,IAAI;MACzB;IACJ,CAAC;IACD,IAAI;MAAEjF;IAAU,CAAC,GAAGwC,QAAQ;IAC5B,IAAIpE,QAAQ,GAAG,IAAI,CAACA,QAAQ,GAAG,IAAIzC,yBAAyB,CAAC6G,QAAQ,CAACvK,EAAE,CAAC;IACzEmG,QAAQ,CAAC/B,OAAO,CAAC7M,QAAQ,GAAG,mBAAmB;IAC/C4O,QAAQ,CAACtC,kBAAkB,GAAG,KAAK;IACnCsC,QAAQ,CAACxB,YAAY,CAACpE,SAAS,GAAGwH,SAAS,CAACmB,OAAO,CAACgC,OAAO,CAACe,UAAU;IACtE,IAAIxB,WAAW,GAAG,IAAI,CAACA,WAAW,GAAG,IAAIvE,WAAW,CAAC,IAAI,CAACC,QAAQ,EAAE/Q,0BAA0B,CAACmV,QAAQ,CAAC,CAAC;IACzGE,WAAW,CAACpS,OAAO,CAAC0M,EAAE,CAAC,aAAa,EAAE,IAAI,CAAC4B,iBAAiB,CAAC;IAC7D8D,WAAW,CAACpS,OAAO,CAAC0M,EAAE,CAAC,WAAW,EAAE,IAAI,CAAC+B,eAAe,CAAC;IACzD2D,WAAW,CAACpS,OAAO,CAAC0M,EAAE,CAAC,WAAW,EAAE,IAAI,CAACwG,eAAe,CAAC;IACzDd,WAAW,CAACpS,OAAO,CAAC0M,EAAE,CAAC,SAAS,EAAE,IAAI,CAACmC,aAAa,CAAC;EACzD;EACAzM,OAAOA,CAAA,EAAG;IACN,IAAI,CAAC0L,QAAQ,CAAC1L,OAAO,CAAC,CAAC;EAC3B;EACAkX,UAAUA,CAAC5Z,EAAE,EAAE;IACX,OAAOnE,cAAc,CAACmE,EAAE,CAACT,SAAS,EAAE,WAAW,CAAC;EACpD;AACJ;AACA,SAASua,eAAeA,CAAC1I,IAAI,EAAEC,IAAI,EAAE4I,WAAW,EAAEC,aAAa,EAAE;EAC7D,IAAInI,OAAO,GAAGX,IAAI,CAACD,OAAO,CAACY,OAAO;EAClC,IAAIgH,KAAK,GAAG3H,IAAI,CAACP,QAAQ,CAACC,KAAK,CAAC7M,KAAK;EACrC,IAAI+U,KAAK,GAAG3H,IAAI,CAACR,QAAQ,CAACC,KAAK,CAAC7M,KAAK;EACrC,IAAI8T,KAAK,GAAG3Z,SAAS,CAAC2a,KAAK,EAAEC,KAAK,EAAEjH,OAAO,EAAEX,IAAI,CAACgI,SAAS,CAAC;EAC5D,IAAIa,WAAW,EAAE;IACb,IAAIlI,OAAO,CAACrM,GAAG,CAACwU,aAAa,CAACjW,KAAK,EAAE8T,KAAK,CAAC,GAAGmC,aAAa,CAAC1F,GAAG,EAAE;MAC7D,OAAO;QAAEuF,UAAU,EAAEhC;MAAM,CAAC;IAChC;EACJ,CAAC,MACI,IAAIhG,OAAO,CAACrM,GAAG,CAACwU,aAAa,CAAC1F,GAAG,EAAEuD,KAAK,CAAC,GAAGmC,aAAa,CAACjW,KAAK,EAAE;IAClE,OAAO;MAAE+V,QAAQ,EAAEjC;IAAM,CAAC;EAC9B;EACA,OAAO,IAAI;AACf;AAEA,MAAMoC,YAAY,CAAC;EACf9a,WAAWA,CAAC8R,OAAO,EAAE;IACjB,IAAI,CAACA,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACiJ,yBAAyB,GAAG,KAAK,CAAC,CAAC;IACxC,IAAI,CAACC,aAAa,GAAG,KAAK;IAC1B,IAAI,CAACC,YAAY,GAAG,KAAK;IACzB,IAAI,CAACC,QAAQ,GAAIC,UAAU,IAAK;MAC5B,IAAIA,UAAU,CAAC3H,OAAO,EAAE;QACpB,IAAI,CAACuH,yBAAyB,GAAG,IAAI;MACzC;IACJ,CAAC;IACD,IAAI,CAACK,qBAAqB,GAAIra,GAAG,IAAK;MAClC,IAAIsa,cAAc,GAAG,IAAI,CAACvJ,OAAO,CAACgC,OAAO,CAACuH,cAAc;MACxD,IAAI7X,MAAM,GAAGvE,qBAAqB,CAAC8B,GAAG,CAAC8B,SAAS,CAAC;MACjD,IAAI,CAACmY,aAAa,GAAG,CAAC,CAACxe,cAAc,CAACgH,MAAM,EAAE6X,cAAc,CAAC;MAC7D,IAAI,CAACJ,YAAY,GAAG,CAAC,CAACze,cAAc,CAACgH,MAAM,EAAE+R,aAAa,CAAC2D,QAAQ,CAAC,CAAC,CAAC;IAC1E,CAAC;IACD,IAAI,CAACoC,mBAAmB,GAAIva,GAAG,IAAK;MAChC,IAAI;QAAE+Q;MAAQ,CAAC,GAAG,IAAI;MACtB,IAAI;QAAEyJ;MAAgB,CAAC,GAAG,IAAI;MAC9B,IAAIC,aAAa,GAAG1J,OAAO,CAACqE,cAAc,CAAC,CAAC;MAC5C;MACA,IAAI,CAACoF,eAAe,CAAC9a,cAAc,EAAE;QACjC,IAAI+a,aAAa,CAACC,aAAa;QAAI;QAC/B,CAAC,IAAI,CAACV,yBAAyB,CAAC;QAAA,EAClC;UACE,IAAIW,YAAY,GAAG5J,OAAO,CAACgC,OAAO,CAAC4H,YAAY;UAC/C,IAAIA,YAAY,KAAK,CAACA,YAAY,IAAI,CAAC,IAAI,CAACV,aAAa,CAAC,EAAE;YACxDlJ,OAAO,CAAC6B,WAAW,CAACO,QAAQ,CAACnT,GAAG,CAAC;UACrC;QACJ;QACA,IAAIya,aAAa,CAAClF,cAAc;QAAI;QAChC,CAAC,IAAI,CAAC2E,YAAY,CAAC;QAAA,EACrB;UACEnJ,OAAO,CAAC4C,QAAQ,CAAC;YAAEpH,IAAI,EAAE;UAAiB,CAAC,CAAC;QAChD;MACJ;MACA,IAAI,CAACyN,yBAAyB,GAAG,KAAK;IAC1C,CAAC;IACD,IAAIQ,eAAe,GAAG,IAAI,CAACA,eAAe,GAAG,IAAIxb,eAAe,CAACqB,QAAQ,CAAC;IAC1Ema,eAAe,CAAClb,gBAAgB,GAAG,IAAI;IACvCkb,eAAe,CAACjb,iBAAiB,GAAG,KAAK;IACzCib,eAAe,CAACta,OAAO,CAAC0M,EAAE,CAAC,aAAa,EAAE,IAAI,CAACyN,qBAAqB,CAAC;IACrEG,eAAe,CAACta,OAAO,CAAC0M,EAAE,CAAC,WAAW,EAAE,IAAI,CAAC2N,mBAAmB,CAAC;IACjE;AACR;AACA;IACQxJ,OAAO,CAAC7Q,OAAO,CAAC0M,EAAE,CAAC,QAAQ,EAAE,IAAI,CAACuN,QAAQ,CAAC;EAC/C;EACA7X,OAAOA,CAAA,EAAG;IACN,IAAI,CAACyO,OAAO,CAAC7Q,OAAO,CAAC0a,GAAG,CAAC,QAAQ,EAAE,IAAI,CAACT,QAAQ,CAAC;IACjD,IAAI,CAACK,eAAe,CAAClY,OAAO,CAAC,CAAC;EAClC;AACJ;AAEA,MAAMuY,eAAe,GAAG;EACpBpF,iBAAiB,EAAEtX;AACvB,CAAC;AACD,MAAM2c,iBAAiB,GAAG;EACtBC,SAAS,EAAE5c,QAAQ;EACnB6c,cAAc,EAAE7c,QAAQ;EACxB8c,aAAa,EAAE9c,QAAQ;EACvB+c,SAAS,EAAE/c,QAAQ;EACnBgd,gBAAgB,EAAEhd,QAAQ;EAC1Bid,eAAe,EAAEjd,QAAQ;EACzBkd,WAAW,EAAEld,QAAQ;EACrBmd,IAAI,EAAEnd,QAAQ;EACdod,YAAY,EAAEpd,QAAQ;EACtBqd,UAAU,EAAErd;AAChB,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,MAAMsd,uBAAuB,CAAC;EAC1Bxc,WAAWA,CAAC+O,QAAQ,EAAE0N,gBAAgB,EAAE;IACpC,IAAI,CAAC9G,gBAAgB,GAAG,IAAI;IAC5B,IAAI,CAAC+G,cAAc,GAAG,IAAI,CAAC,CAAC;IAC5B,IAAI,CAACD,gBAAgB,GAAG,IAAI;IAC5B,IAAI,CAACE,QAAQ,GAAG,IAAI;IACpB,IAAI,CAACjN,eAAe,GAAI/O,EAAE,IAAK;MAC3B,IAAI,CAACgc,QAAQ,GAAG,IAAI,CAACC,aAAa,CAACjc,EAAE,CAACT,SAAS,CAAC;IACpD,CAAC;IACD,IAAI,CAACiU,eAAe,GAAG,CAAC5D,GAAG,EAAE6D,OAAO,EAAEzT,EAAE,KAAK;MACzC,IAAI;QAAEoO;MAAS,CAAC,GAAG,IAAI,CAACsE,WAAW;MACnC,IAAIsC,gBAAgB,GAAG,IAAI;MAC3B,IAAI+G,cAAc,GAAG,IAAI;MACzB,IAAIrI,SAAS,GAAG,KAAK;MACrB,IAAI0C,WAAW,GAAG;QACdC,cAAc,EAAEvY,qBAAqB,CAAC,CAAC;QACvCwY,aAAa,EAAExY,qBAAqB,CAAC,CAAC;QACtCyY,OAAO,EAAE,IAAI,CAACyF,QAAQ,CAACE;MAC3B,CAAC;MACD,IAAItM,GAAG,EAAE;QACLoF,gBAAgB,GAAGpF,GAAG,CAACuB,OAAO;QAC9B,IAAI,IAAI,CAACgL,mBAAmB,CAACnc,EAAE,CAACT,SAAS,EAAEyV,gBAAgB,CAAC,EAAE;UAC1D+G,cAAc,GAAGK,uBAAuB,CAACxM,GAAG,CAACiB,QAAQ,EAAE,IAAI,CAACmL,QAAQ,EAAEhH,gBAAgB,CAAC;UACvFoB,WAAW,CAACE,aAAa,GAAG9X,iBAAiB,CAACud,cAAc,CAAC;UAC7DrI,SAAS,GAAG,CAAC1V,kBAAkB,CAACoY,WAAW,EAAExG,GAAG,CAACe,WAAW,EAAEqE,gBAAgB,CAAC;UAC/E,IAAItB,SAAS,EAAE;YACX0C,WAAW,CAACE,aAAa,GAAGxY,qBAAqB,CAAC,CAAC;YACnDie,cAAc,GAAG,IAAI;UACzB;QACJ;MACJ;MACA,IAAI,CAACjF,WAAW,CAAC9B,gBAAgB,EAAEoB,WAAW,CAAC;MAC/C;MACA;MACAhI,QAAQ,CAACd,kBAAkB,CAACmG,OAAO,IAAI,CAACsI,cAAc,IAAI,CAACtb,QAAQ,CAACsW,aAAa,CAAC,kBAAkB,CAAC,CAAC;MACtG,IAAI,CAACrD,SAAS,EAAE;QACZnW,YAAY,CAAC,CAAC;MAClB,CAAC,MACI;QACDC,aAAa,CAAC,CAAC;MACnB;MACA,IAAI,CAACiW,OAAO,EAAE;QACVrF,QAAQ,CAACb,oBAAoB,CAAC,CAACwO,cAAc,CAAC;QAC9C,IAAI,CAAC/G,gBAAgB,GAAGA,gBAAgB;QACxC,IAAI,CAAC+G,cAAc,GAAGA,cAAc;MACxC;IACJ,CAAC;IACD,IAAI,CAAC5M,aAAa,GAAI/O,GAAG,IAAK;MAC1B,IAAI;QAAE4U,gBAAgB;QAAE+G;MAAe,CAAC,GAAG,IAAI;MAC/C,IAAI,CAAC3E,SAAS,CAAC,CAAC;MAChB,IAAIpC,gBAAgB,IAAI+G,cAAc,EAAE;QACpC,IAAIpN,QAAQ,GAAG,IAAI,CAAC+D,WAAW,CAAC/D,QAAQ;QACxC,IAAI0N,SAAS,GAAG1N,QAAQ,CAACwC,OAAO,CAAC4B,OAAO;QACxC,IAAIiJ,QAAQ,GAAG,IAAI,CAACA,QAAQ;QAC5BhH,gBAAgB,CAAC1U,OAAO,CAACC,OAAO,CAAC,MAAM,EAAEqR,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEN,4BAA4B,CAAC5C,QAAQ,CAACkC,QAAQ,EAAEmE,gBAAgB,CAAC,CAAC,EAAE;UAAEkD,SAAS,EAAE9X,GAAG,CAACb,SAAS;UAAEsT,OAAO,EAAEzS,GAAG,CAAC8B,SAAS;UAAE4Q,IAAI,EAAEuJ;QAAU,CAAC,CAAC,CAAC;QACpN,IAAIL,QAAQ,CAACE,MAAM,EAAE;UACjB,IAAII,YAAY,GAAG9d,iBAAiB,CAACud,cAAc,CAAC;UACpD/G,gBAAgB,CAACjB,QAAQ,CAAC;YACtBpH,IAAI,EAAE,cAAc;YACpB8I,UAAU,EAAE6G;UAChB,CAAC,CAAC;UACF,IAAIlc,GAAG,CAAC+B,OAAO,EAAE;YACb6S,gBAAgB,CAACjB,QAAQ,CAAC;cACtBpH,IAAI,EAAE,cAAc;cACpB0I,eAAe,EAAE0G,cAAc,CAACzG,QAAQ,CAACC;YAC7C,CAAC,CAAC;UACN;UACA;UACAP,gBAAgB,CAAC1U,OAAO,CAACC,OAAO,CAAC,cAAc,EAAE;YAC7C0V,KAAK,EAAE,IAAIpY,SAAS,CAACmX,gBAAgB,EAAE+G,cAAc,CAAC7F,GAAG,EAAE6F,cAAc,CAACzG,QAAQ,CAAC;YACnFqC,aAAa,EAAE,EAAE;YACjBC,MAAMA,CAAA,EAAG;cACL5C,gBAAgB,CAACjB,QAAQ,CAAC;gBACtBpH,IAAI,EAAE,eAAe;gBACrB8I,UAAU,EAAE6G;cAChB,CAAC,CAAC;YACN,CAAC;YACDpE,SAAS,EAAE9X,GAAG,CAACb,SAAS;YACxBuT,IAAI,EAAEuJ;UACV,CAAC,CAAC;QACN;MACJ;MACA,IAAI,CAACrH,gBAAgB,GAAG,IAAI;MAC5B,IAAI,CAAC+G,cAAc,GAAG,IAAI;IAC9B,CAAC;IACD,IAAIrJ,WAAW,GAAG,IAAI,CAACA,WAAW,GAAG,IAAIvE,WAAW,CAACC,QAAQ,EAAElQ,wBAAwB,CAAC;IACxFwU,WAAW,CAACnE,cAAc,GAAG,KAAK,CAAC,CAAC;IACpCmE,WAAW,CAACpS,OAAO,CAAC0M,EAAE,CAAC,WAAW,EAAE,IAAI,CAAC+B,eAAe,CAAC;IACzD2D,WAAW,CAACpS,OAAO,CAAC0M,EAAE,CAAC,WAAW,EAAE,IAAI,CAACwG,eAAe,CAAC;IACzDd,WAAW,CAACpS,OAAO,CAAC0M,EAAE,CAAC,SAAS,EAAE,IAAI,CAACmC,aAAa,CAAC;IACrD,IAAI,CAAC2M,gBAAgB,GAAGA,gBAAgB;EAC5C;EACAG,aAAaA,CAAC1c,SAAS,EAAE;IACrB,IAAI,OAAO,IAAI,CAACuc,gBAAgB,KAAK,QAAQ,EAAE;MAC3C,OAAOrd,aAAa,CAAC,IAAI,CAACqd,gBAAgB,CAAC;IAC/C;IACA,IAAI,OAAO,IAAI,CAACA,gBAAgB,KAAK,UAAU,EAAE;MAC7C,OAAOrd,aAAa,CAAC,IAAI,CAACqd,gBAAgB,CAACvc,SAAS,CAAC,CAAC;IAC1D;IACA,OAAOgd,iBAAiB,CAAChd,SAAS,CAAC;EACvC;EACAuX,WAAWA,CAAC2B,WAAW,EAAEC,KAAK,EAAE;IAC5B,IAAIC,WAAW,GAAG,IAAI,CAAC3D,gBAAgB;IACvC,IAAI2D,WAAW,IAAIA,WAAW,KAAKF,WAAW,EAAE;MAC5CE,WAAW,CAAC5E,QAAQ,CAAC;QAAEpH,IAAI,EAAE;MAAmB,CAAC,CAAC;IACtD;IACA,IAAI8L,WAAW,EAAE;MACbA,WAAW,CAAC1E,QAAQ,CAAC;QAAEpH,IAAI,EAAE,gBAAgB;QAAE+L;MAAM,CAAC,CAAC;IAC3D;EACJ;EACAtB,SAASA,CAAA,EAAG;IACR,IAAI,IAAI,CAACpC,gBAAgB,EAAE;MACvB,IAAI,CAACA,gBAAgB,CAACjB,QAAQ,CAAC;QAAEpH,IAAI,EAAE;MAAmB,CAAC,CAAC;IAChE;EACJ;EACAwP,mBAAmBA,CAAClU,EAAE,EAAE+M,gBAAgB,EAAE;IACtC,IAAIwH,UAAU,GAAGxH,gBAAgB,CAAC7B,OAAO,CAACqJ,UAAU;IACpD,IAAI,OAAOA,UAAU,KAAK,UAAU,EAAE;MAClC,OAAOA,UAAU,CAAChR,IAAI,CAACwJ,gBAAgB,CAAChC,WAAW,EAAE/K,EAAE,CAAC;IAC5D;IACA,IAAI,OAAOuU,UAAU,KAAK,QAAQ,IAAIA,UAAU,EAAE;MAC9C,OAAOlL,OAAO,CAAC5S,cAAc,CAACuJ,EAAE,EAAEuU,UAAU,CAAC,CAAC;IAClD;IACA,OAAO,IAAI;EACf;AACJ;AACA;AACA;AACA,SAASJ,uBAAuBA,CAACvL,QAAQ,EAAEmL,QAAQ,EAAE7K,OAAO,EAAE;EAC1D,IAAIsL,QAAQ,GAAG7K,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEmK,QAAQ,CAACU,aAAa,CAAC;EACxD,KAAK,IAAIjL,SAAS,IAAIN,OAAO,CAACO,WAAW,CAACiL,qBAAqB,EAAE;IAC7D/K,MAAM,CAACC,MAAM,CAAC4K,QAAQ,EAAEhL,SAAS,CAACZ,QAAQ,EAAEmL,QAAQ,CAAC,CAAC;EAC1D;EACA,IAAI;IAAEY,OAAO;IAAEC;EAAM,CAAC,GAAGle,cAAc,CAAC8d,QAAQ,EAAEtL,OAAO,CAAC;EAC1D,IAAI+E,GAAG,GAAGtX,aAAa,CAACge,OAAO,EAAEC,KAAK,EAAEb,QAAQ,CAACc,QAAQ,EAAEjM,QAAQ,CAACyB,MAAM,EAAEnB,OAAO,CAACgC,OAAO,CAAC4J,kBAAkB,IAAIzL,OAAO,CAAC0K,QAAQ,CAACgB,QAAQ,CAAC;EAAE;EAC9I7L,OAAO,CAAC;EACR,IAAIlN,KAAK,GAAG4M,QAAQ,CAACC,KAAK,CAAC7M,KAAK;EAChC;EACA;EACA,IAAI4M,QAAQ,CAACyB,MAAM,IAAI0J,QAAQ,CAACiB,SAAS,EAAE;IACvChZ,KAAK,GAAGkN,OAAO,CAACY,OAAO,CAACrM,GAAG,CAACzB,KAAK,EAAE+X,QAAQ,CAACiB,SAAS,CAAC;EAC1D;EACA,IAAIzI,GAAG,GAAGwH,QAAQ,CAACgB,QAAQ,GACvB7L,OAAO,CAACY,OAAO,CAACrM,GAAG,CAACzB,KAAK,EAAE+X,QAAQ,CAACgB,QAAQ,CAAC,GAC7Cne,kBAAkB,CAACgS,QAAQ,CAACyB,MAAM,EAAErO,KAAK,EAAEkN,OAAO,CAAC;EACvD,IAAImE,QAAQ,GAAGxW,mBAAmB,CAACoX,GAAG,CAACqB,KAAK,EAAE;IAAEtT,KAAK;IAAEuQ;EAAI,CAAC,CAAC;EAC7D,OAAO;IAAE0B,GAAG;IAAEZ;EAAS,CAAC;AAC5B;AACA;AACA;AACA,SAASiH,iBAAiBA,CAACtU,EAAE,EAAE;EAC3B,IAAIiV,GAAG,GAAGC,iBAAiB,CAAClV,EAAE,EAAE,OAAO,CAAC;EACxC,IAAImV,GAAG,GAAGF,GAAG,GACTG,IAAI,CAACC,KAAK,CAACJ,GAAG,CAAC,GACf;IAAEhB,MAAM,EAAE;EAAM,CAAC,CAAC,CAAC;EACvB,OAAOzd,aAAa,CAAC2e,GAAG,CAAC;AAC7B;AACAzhB,MAAM,CAAC4hB,cAAc,GAAG,EAAE;AAC1B,SAASJ,iBAAiBA,CAAClV,EAAE,EAAEoC,IAAI,EAAE;EACjC,IAAImT,MAAM,GAAG7hB,MAAM,CAAC4hB,cAAc;EAClC,IAAIE,YAAY,GAAG,CAACD,MAAM,GAAGA,MAAM,GAAG,GAAG,GAAG,EAAE,IAAInT,IAAI;EACtD,OAAOpC,EAAE,CAACyV,YAAY,CAAC,OAAO,GAAGD,YAAY,CAAC,IAAI,EAAE;AACxD;;AAEA;AACA;AACA;AACA;AACA;AACA,MAAME,iBAAiB,CAAC;EACpBte,WAAWA,CAAC4I,EAAE,EAAiB;IAAA,IAAfuK,QAAQ,GAAAoL,SAAA,CAAA1a,MAAA,QAAA0a,SAAA,QAAAC,SAAA,GAAAD,SAAA,MAAG,CAAC,CAAC;IACzB,IAAI,CAAChP,iBAAiB,GAAI5O,EAAE,IAAK;MAC7B,IAAI;QAAEoO;MAAS,CAAC,GAAG,IAAI;MACvB,IAAI;QAAEvC,WAAW;QAAEuI;MAAe,CAAC,GAAG,IAAI,CAAC5B,QAAQ;MACnDpE,QAAQ,CAACvC,WAAW,GAChBA,WAAW,IAAI,IAAI,GACfA,WAAW,GACV7L,EAAE,CAACmC,OAAO,GAAG,CAAC,GAAGpD,oBAAoB,CAAC2W,oBAAqB;MACpEtH,QAAQ,CAACxC,KAAK,GACV5L,EAAE,CAACmC,OAAO;MAAG;MACRiS,cAAc,IAAI,IAAI,GAAGA,cAAc,GAAGrV,oBAAoB,CAACqV,cAAc,GAC9E,CAAC;IACb,CAAC;IACD,IAAI,CAACrF,eAAe,GAAI/O,EAAE,IAAK;MAC3B,IAAIA,EAAE,CAACmC,OAAO,IACV,IAAI,CAACiM,QAAQ,CAACxC,KAAK,IACnB5L,EAAE,CAACT,SAAS,CAACkG,SAAS,CAACsL,QAAQ,CAAC,UAAU,CAAC,EAAE;QAC7C,IAAI,CAAC3C,QAAQ,CAAC9B,MAAM,CAAClH,WAAW,CAAC,CAAC,CAACK,SAAS,CAACC,GAAG,CAAC,mBAAmB,CAAC;MACzE;IACJ,CAAC;IACD,IAAI,CAAC8M,QAAQ,GAAGA,QAAQ;IACxB,IAAIpE,QAAQ,GAAG,IAAI,CAACA,QAAQ,GAAG,IAAIzC,yBAAyB,CAAC1D,EAAE,CAAC;IAChEmG,QAAQ,CAACtC,kBAAkB,GAAG,KAAK;IACnC,IAAI0G,QAAQ,CAACsL,YAAY,IAAI,IAAI,EAAE;MAC/B1P,QAAQ,CAAC/B,OAAO,CAAC7M,QAAQ,GAAGgT,QAAQ,CAACsL,YAAY;IACrD;IACA,IAAItL,QAAQ,CAACuL,QAAQ,IAAI,IAAI,EAAE;MAC3B3P,QAAQ,CAAC9B,MAAM,CAACzI,UAAU,GAAG2O,QAAQ,CAACuL,QAAQ,CAAC,CAAC;IACpD;IACA3P,QAAQ,CAAC9N,OAAO,CAAC0M,EAAE,CAAC,aAAa,EAAE,IAAI,CAAC4B,iBAAiB,CAAC;IAC1DR,QAAQ,CAAC9N,OAAO,CAAC0M,EAAE,CAAC,WAAW,EAAE,IAAI,CAAC+B,eAAe,CAAC;IACtD,IAAI8M,uBAAuB,CAACzN,QAAQ,EAAEoE,QAAQ,CAACwL,SAAS,CAAC,CAAC,CAAC;EAC/D;EACAtb,OAAOA,CAAA,EAAG;IACN,IAAI,CAAC0L,QAAQ,CAAC1L,OAAO,CAAC,CAAC;EAC3B;AACJ;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMub,uBAAuB,SAAS5hB,eAAe,CAAC;EAClDgD,WAAWA,CAACC,WAAW,EAAE;IACrB,KAAK,CAACA,WAAW,CAAC;IAClB,IAAI,CAACI,gBAAgB,GAAG,KAAK;IAC7B,IAAI,CAACwe,cAAc,GAAG,EAAE;IACxB,IAAI,CAACC,eAAe,GAAG,IAAI;IAC3B,IAAI,CAACvP,iBAAiB,GAAI5O,EAAE,IAAK;MAC7B,IAAI,CAACM,OAAO,CAACC,OAAO,CAAC,aAAa,EAAEP,EAAE,CAAC;MACvC,IAAI,CAAC,IAAI,CAACN,gBAAgB,EAAE;QACxB;QACA,IAAI,CAACY,OAAO,CAACC,OAAO,CAAC,WAAW,EAAEP,EAAE,CAAC;MACzC;IACJ,CAAC;IACD,IAAI,CAACoe,iBAAiB,GAAIpe,EAAE,IAAK;MAC7B,IAAI,CAAC,IAAI,CAACN,gBAAgB,EAAE;QACxB,IAAI,CAACY,OAAO,CAACC,OAAO,CAAC,UAAU,EAAEP,EAAE,CAAC;MACxC;IACJ,CAAC;IACD,IAAI,CAACiP,eAAe,GAAIjP,EAAE,IAAK;MAC3B,IAAI,CAACM,OAAO,CAACC,OAAO,CAAC,WAAW,EAAEP,EAAE,CAAC;MACrC,IAAI,CAAC,IAAI,CAACN,gBAAgB,EAAE;QACxB;QACA,IAAI,CAACY,OAAO,CAACC,OAAO,CAAC,SAAS,EAAEP,EAAE,CAAC;MACvC;IACJ,CAAC;IACD,IAAIqM,OAAO,GAAG,IAAI,CAACA,OAAO,GAAG,IAAIjN,eAAe,CAACE,WAAW,CAAC;IAC7D+M,OAAO,CAAC/L,OAAO,CAAC0M,EAAE,CAAC,aAAa,EAAE,IAAI,CAAC4B,iBAAiB,CAAC;IACzDvC,OAAO,CAAC/L,OAAO,CAAC0M,EAAE,CAAC,aAAa,EAAE,IAAI,CAACoR,iBAAiB,CAAC;IACzD/R,OAAO,CAAC/L,OAAO,CAAC0M,EAAE,CAAC,WAAW,EAAE,IAAI,CAACiC,eAAe,CAAC;EACzD;EACAvM,OAAOA,CAAA,EAAG;IACN,IAAI,CAAC2J,OAAO,CAAC3J,OAAO,CAAC,CAAC;EAC1B;EACA2K,aAAaA,CAAC7I,IAAI,EAAE;IAChB,IAAI,CAAC9E,gBAAgB,GAAG8E,IAAI;EAChC;EACA8I,kBAAkBA,CAAC9I,IAAI,EAAE;IACrB,IAAIA,IAAI,EAAE;MACN;MACA;MACA,IAAI,IAAI,CAAC2Z,eAAe,EAAE;QACtB,IAAI,CAACA,eAAe,CAAC1Z,KAAK,CAACmB,UAAU,GAAG,EAAE;QAC1C,IAAI,CAACuY,eAAe,GAAG,IAAI;MAC/B;IACJ,CAAC,MACI;MACD,IAAIxa,QAAQ,GAAG,IAAI,CAACua;MAChB;MAAA,EACEzd,QAAQ,CAACsW,aAAa,CAAC,IAAI,CAACmH,cAAc,CAAC,GAC3C,IAAI;MACV,IAAIva,QAAQ,EAAE;QACV,IAAI,CAACwa,eAAe,GAAGxa,QAAQ;QAC/BA,QAAQ,CAACc,KAAK,CAACmB,UAAU,GAAG,QAAQ;MACxC;IACJ;EACJ;AACJ;;AAEA;AACA;AACA;AACA;AACA,MAAMyY,mBAAmB,CAAC;EACtBhf,WAAWA,CAACif,mBAAmB,EAAE9L,QAAQ,EAAE;IACvC,IAAIlT,WAAW,GAAGmB,QAAQ;IAC1B;IACA;IACA6d,mBAAmB,KAAK7d,QAAQ,IAC5B6d,mBAAmB,YAAYC,OAAO,EAAE;MACxCjf,WAAW,GAAGgf,mBAAmB;MACjC9L,QAAQ,GAAGA,QAAQ,IAAI,CAAC,CAAC;IAC7B,CAAC,MACI;MACDA,QAAQ,GAAI8L,mBAAmB,IAAI,CAAC,CAAE;IAC1C;IACA,IAAIlQ,QAAQ,GAAG,IAAI,CAACA,QAAQ,GAAG,IAAI6P,uBAAuB,CAAC3e,WAAW,CAAC;IACvE,IAAI,OAAOkT,QAAQ,CAACsL,YAAY,KAAK,QAAQ,EAAE;MAC3C1P,QAAQ,CAAC/B,OAAO,CAAC7M,QAAQ,GAAGgT,QAAQ,CAACsL,YAAY;IACrD,CAAC,MACI,IAAIxe,WAAW,KAAKmB,QAAQ,EAAE;MAC/B2N,QAAQ,CAAC/B,OAAO,CAAC7M,QAAQ,GAAG,cAAc;IAC9C;IACA,IAAI,OAAOgT,QAAQ,CAAC0L,cAAc,KAAK,QAAQ,EAAE;MAC7C9P,QAAQ,CAAC8P,cAAc,GAAG1L,QAAQ,CAAC0L,cAAc;IACrD;IACA,IAAIM,gBAAgB,GAAG,IAAI3C,uBAAuB,CAACzN,QAAQ,EAAEoE,QAAQ,CAACwL,SAAS,CAAC;IAChF;IACA;IACAQ,gBAAgB,CAAC9L,WAAW,CAAClE,iBAAiB,GAAG,IAAI;EACzD;EACA9L,OAAOA,CAAA,EAAG;IACN,IAAI,CAAC0L,QAAQ,CAAC1L,OAAO,CAAC,CAAC;EAC3B;AACJ;AAEA,IAAI+b,KAAK,GAAG/iB,YAAY,CAAC;EACrB2O,IAAI,EAAE,2BAA2B;EACjCqU,qBAAqB,EAAE,CAACnM,YAAY,EAAEU,aAAa,EAAE2B,aAAa,EAAE4E,aAAa,CAAC;EAClFmF,oBAAoB,EAAE,CAACxE,YAAY,CAAC;EACpCyE,mBAAmB,EAAEjT,yBAAyB;EAC9CkT,cAAc,EAAE5D,eAAe;EAC/B6D,gBAAgB,EAAE5D;AACtB,CAAC,CAAC;AAEF,SAASyC,iBAAiB,IAAIoB,SAAS,EAAEV,mBAAmB,EAAEI,KAAK,IAAIO,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
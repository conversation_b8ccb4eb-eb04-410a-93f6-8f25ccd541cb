{"ast": null, "code": "import { buildLocalizeFn } from \"../../_lib/buildLocalizeFn.js\";\nconst eraValues = {\n  narrow: [\"ق\", \"ب\"],\n  abbreviated: [\"ق.م.\", \"ب.م.\"],\n  wide: [\"قبل الميلاد\", \"بعد الميلاد\"]\n};\nconst quarterValues = {\n  narrow: [\"1\", \"2\", \"3\", \"4\"],\n  abbreviated: [\"ر1\", \"ر2\", \"ر3\", \"ر4\"],\n  wide: [\"الربع الأول\", \"الربع الثاني\", \"الربع الثالث\", \"الربع الرابع\"]\n};\nconst monthValues = {\n  narrow: [\"د\", \"ن\", \"أ\", \"س\", \"أ\", \"ج\", \"ج\", \"م\", \"أ\", \"م\", \"ف\", \"ج\"],\n  abbreviated: [\"جانفي\", \"فيفري\", \"مارس\", \"أفريل\", \"ماي\", \"جوان\", \"جويلية\", \"أوت\", \"سب<PERSON>م<PERSON><PERSON>\", \"أكتوبر\", \"نوفمبر\", \"ديسمبر\"],\n  wide: [\"جانفي\", \"فيفري\", \"مارس\", \"أفريل\", \"ماي\", \"جوان\", \"جويلية\", \"أوت\", \"سبتمبر\", \"أكتوبر\", \"نوفمبر\", \"ديسمبر\"]\n};\nconst dayValues = {\n  narrow: [\"ح\", \"ن\", \"ث\", \"ر\", \"خ\", \"ج\", \"س\"],\n  short: [\"أحد\", \"اثنين\", \"ثلاثاء\", \"أربعاء\", \"خميس\", \"جمعة\", \"سبت\"],\n  abbreviated: [\"أحد\", \"اثنين\", \"ثلاثاء\", \"أربعاء\", \"خميس\", \"جمعة\", \"سبت\"],\n  wide: [\"الأحد\", \"الاثنين\", \"الثلاثاء\", \"الأربعاء\", \"الخميس\", \"الجمعة\", \"السبت\"]\n};\nconst dayPeriodValues = {\n  narrow: {\n    am: \"ص\",\n    pm: \"ع\",\n    morning: \"الصباح\",\n    noon: \"القايلة\",\n    afternoon: \"بعد القايلة\",\n    evening: \"العشية\",\n    night: \"الليل\",\n    midnight: \"نص الليل\"\n  },\n  abbreviated: {\n    am: \"ص\",\n    pm: \"ع\",\n    morning: \"الصباح\",\n    noon: \"القايلة\",\n    afternoon: \"بعد القايلة\",\n    evening: \"العشية\",\n    night: \"الليل\",\n    midnight: \"نص الليل\"\n  },\n  wide: {\n    am: \"ص\",\n    pm: \"ع\",\n    morning: \"الصباح\",\n    noon: \"القايلة\",\n    afternoon: \"بعد القايلة\",\n    evening: \"العشية\",\n    night: \"الليل\",\n    midnight: \"نص الليل\"\n  }\n};\nconst formattingDayPeriodValues = {\n  narrow: {\n    am: \"ص\",\n    pm: \"ع\",\n    morning: \"في الصباح\",\n    noon: \"في القايلة\",\n    afternoon: \"بعد القايلة\",\n    evening: \"في العشية\",\n    night: \"في الليل\",\n    midnight: \"نص الليل\"\n  },\n  abbreviated: {\n    am: \"ص\",\n    pm: \"ع\",\n    morning: \"في الصباح\",\n    noon: \"في القايلة\",\n    afternoon: \"بعد القايلة\",\n    evening: \"في العشية\",\n    night: \"في الليل\",\n    midnight: \"نص الليل\"\n  },\n  wide: {\n    am: \"ص\",\n    pm: \"ع\",\n    morning: \"في الصباح\",\n    noon: \"في القايلة\",\n    afternoon: \"بعد القايلة\",\n    evening: \"في العشية\",\n    night: \"في الليل\",\n    midnight: \"نص الليل\"\n  }\n};\nconst ordinalNumber = num => String(num);\nexport const localize = {\n  ordinalNumber: ordinalNumber,\n  era: buildLocalizeFn({\n    values: eraValues,\n    defaultWidth: \"wide\"\n  }),\n  quarter: buildLocalizeFn({\n    values: quarterValues,\n    defaultWidth: \"wide\",\n    argumentCallback: quarter => quarter - 1\n  }),\n  month: buildLocalizeFn({\n    values: monthValues,\n    defaultWidth: \"wide\"\n  }),\n  day: buildLocalizeFn({\n    values: dayValues,\n    defaultWidth: \"wide\"\n  }),\n  dayPeriod: buildLocalizeFn({\n    values: dayPeriodValues,\n    defaultWidth: \"wide\",\n    formattingValues: formattingDayPeriodValues,\n    defaultFormattingWidth: \"wide\"\n  })\n};", "map": {"version": 3, "names": ["buildLocalizeFn", "<PERSON><PERSON><PERSON><PERSON>", "narrow", "abbreviated", "wide", "quarterValues", "month<PERSON><PERSON><PERSON>", "dayV<PERSON><PERSON>", "short", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "am", "pm", "morning", "noon", "afternoon", "evening", "night", "midnight", "formattingDayPeriodValues", "ordinalNumber", "num", "String", "localize", "era", "values", "defaultWidth", "quarter", "argument<PERSON>allback", "month", "day", "<PERSON><PERSON><PERSON><PERSON>", "formattingValues", "defaultFormattingWidth"], "sources": ["C:/Users/<USER>/Desktop/demo-git/demo-react/my-fullstack-app/frontend/node_modules/date-fns/locale/ar-TN/_lib/localize.js"], "sourcesContent": ["import { buildLocalizeFn } from \"../../_lib/buildLocalizeFn.js\";\n\nconst eraValues = {\n  narrow: [\"ق\", \"ب\"],\n  abbreviated: [\"ق.م.\", \"ب.م.\"],\n  wide: [\"قبل الميلاد\", \"بعد الميلاد\"],\n};\n\nconst quarterValues = {\n  narrow: [\"1\", \"2\", \"3\", \"4\"],\n  abbreviated: [\"ر1\", \"ر2\", \"ر3\", \"ر4\"],\n  wide: [\"الربع الأول\", \"الربع الثاني\", \"الربع الثالث\", \"الربع الرابع\"],\n};\n\nconst monthValues = {\n  narrow: [\"د\", \"ن\", \"أ\", \"س\", \"أ\", \"ج\", \"ج\", \"م\", \"أ\", \"م\", \"ف\", \"ج\"],\n  abbreviated: [\n    \"جانفي\",\n    \"فيفري\",\n    \"مارس\",\n    \"أفريل\",\n    \"ماي\",\n    \"جوان\",\n    \"جويلية\",\n    \"أوت\",\n    \"سبتم<PERSON><PERSON>\",\n    \"أكتوبر\",\n    \"نوفمبر\",\n    \"ديسمبر\",\n  ],\n\n  wide: [\n    \"جانفي\",\n    \"فيفري\",\n    \"مارس\",\n    \"أفريل\",\n    \"ماي\",\n    \"جوان\",\n    \"جويلية\",\n    \"أوت\",\n    \"سبتمبر\",\n    \"أكتوبر\",\n    \"نوفمبر\",\n    \"ديسمبر\",\n  ],\n};\n\nconst dayValues = {\n  narrow: [\"ح\", \"ن\", \"ث\", \"ر\", \"خ\", \"ج\", \"س\"],\n  short: [\"أحد\", \"اثنين\", \"ثلاثاء\", \"أربعاء\", \"خميس\", \"جمعة\", \"سبت\"],\n  abbreviated: [\"أحد\", \"اثنين\", \"ثلاثاء\", \"أربعاء\", \"خميس\", \"جمعة\", \"سبت\"],\n\n  wide: [\n    \"الأحد\",\n    \"الاثنين\",\n    \"الثلاثاء\",\n    \"الأربعاء\",\n    \"الخميس\",\n    \"الجمعة\",\n    \"السبت\",\n  ],\n};\n\nconst dayPeriodValues = {\n  narrow: {\n    am: \"ص\",\n    pm: \"ع\",\n    morning: \"الصباح\",\n    noon: \"القايلة\",\n    afternoon: \"بعد القايلة\",\n    evening: \"العشية\",\n    night: \"الليل\",\n    midnight: \"نص الليل\",\n  },\n  abbreviated: {\n    am: \"ص\",\n    pm: \"ع\",\n    morning: \"الصباح\",\n    noon: \"القايلة\",\n    afternoon: \"بعد القايلة\",\n    evening: \"العشية\",\n    night: \"الليل\",\n    midnight: \"نص الليل\",\n  },\n  wide: {\n    am: \"ص\",\n    pm: \"ع\",\n    morning: \"الصباح\",\n    noon: \"القايلة\",\n    afternoon: \"بعد القايلة\",\n    evening: \"العشية\",\n    night: \"الليل\",\n    midnight: \"نص الليل\",\n  },\n};\n\nconst formattingDayPeriodValues = {\n  narrow: {\n    am: \"ص\",\n    pm: \"ع\",\n    morning: \"في الصباح\",\n    noon: \"في القايلة\",\n    afternoon: \"بعد القايلة\",\n    evening: \"في العشية\",\n    night: \"في الليل\",\n    midnight: \"نص الليل\",\n  },\n  abbreviated: {\n    am: \"ص\",\n    pm: \"ع\",\n    morning: \"في الصباح\",\n    noon: \"في القايلة\",\n    afternoon: \"بعد القايلة\",\n    evening: \"في العشية\",\n    night: \"في الليل\",\n    midnight: \"نص الليل\",\n  },\n  wide: {\n    am: \"ص\",\n    pm: \"ع\",\n    morning: \"في الصباح\",\n    noon: \"في القايلة\",\n    afternoon: \"بعد القايلة\",\n    evening: \"في العشية\",\n    night: \"في الليل\",\n    midnight: \"نص الليل\",\n  },\n};\n\nconst ordinalNumber = (num) => String(num);\n\nexport const localize = {\n  ordinalNumber: ordinalNumber,\n\n  era: buildLocalizeFn({\n    values: eraValues,\n    defaultWidth: \"wide\",\n  }),\n\n  quarter: buildLocalizeFn({\n    values: quarterValues,\n    defaultWidth: \"wide\",\n    argumentCallback: (quarter) => quarter - 1,\n  }),\n\n  month: buildLocalizeFn({\n    values: monthValues,\n    defaultWidth: \"wide\",\n  }),\n\n  day: buildLocalizeFn({\n    values: dayValues,\n    defaultWidth: \"wide\",\n  }),\n\n  dayPeriod: buildLocalizeFn({\n    values: dayPeriodValues,\n    defaultWidth: \"wide\",\n    formattingValues: formattingDayPeriodValues,\n    defaultFormattingWidth: \"wide\",\n  }),\n};\n"], "mappings": "AAAA,SAASA,eAAe,QAAQ,+BAA+B;AAE/D,MAAMC,SAAS,GAAG;EAChBC,MAAM,EAAE,CAAC,GAAG,EAAE,GAAG,CAAC;EAClBC,WAAW,EAAE,CAAC,MAAM,EAAE,MAAM,CAAC;EAC7BC,IAAI,EAAE,CAAC,aAAa,EAAE,aAAa;AACrC,CAAC;AAED,MAAMC,aAAa,GAAG;EACpBH,MAAM,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EAC5BC,WAAW,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;EACrCC,IAAI,EAAE,CAAC,aAAa,EAAE,cAAc,EAAE,cAAc,EAAE,cAAc;AACtE,CAAC;AAED,MAAME,WAAW,GAAG;EAClBJ,MAAM,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EACpEC,WAAW,EAAE,CACX,OAAO,EACP,OAAO,EACP,MAAM,EACN,OAAO,EACP,KAAK,EACL,MAAM,EACN,QAAQ,EACR,KAAK,EACL,QAAQ,EACR,QAAQ,EACR,QAAQ,EACR,QAAQ,CACT;EAEDC,IAAI,EAAE,CACJ,OAAO,EACP,OAAO,EACP,MAAM,EACN,OAAO,EACP,KAAK,EACL,MAAM,EACN,QAAQ,EACR,KAAK,EACL,QAAQ,EACR,QAAQ,EACR,QAAQ,EACR,QAAQ;AAEZ,CAAC;AAED,MAAMG,SAAS,GAAG;EAChBL,MAAM,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EAC3CM,KAAK,EAAE,CAAC,KAAK,EAAE,OAAO,EAAE,QAAQ,EAAE,QAAQ,EAAE,MAAM,EAAE,MAAM,EAAE,KAAK,CAAC;EAClEL,WAAW,EAAE,CAAC,KAAK,EAAE,OAAO,EAAE,QAAQ,EAAE,QAAQ,EAAE,MAAM,EAAE,MAAM,EAAE,KAAK,CAAC;EAExEC,IAAI,EAAE,CACJ,OAAO,EACP,SAAS,EACT,UAAU,EACV,UAAU,EACV,QAAQ,EACR,QAAQ,EACR,OAAO;AAEX,CAAC;AAED,MAAMK,eAAe,GAAG;EACtBP,MAAM,EAAE;IACNQ,EAAE,EAAE,GAAG;IACPC,EAAE,EAAE,GAAG;IACPC,OAAO,EAAE,QAAQ;IACjBC,IAAI,EAAE,SAAS;IACfC,SAAS,EAAE,aAAa;IACxBC,OAAO,EAAE,QAAQ;IACjBC,KAAK,EAAE,OAAO;IACdC,QAAQ,EAAE;EACZ,CAAC;EACDd,WAAW,EAAE;IACXO,EAAE,EAAE,GAAG;IACPC,EAAE,EAAE,GAAG;IACPC,OAAO,EAAE,QAAQ;IACjBC,IAAI,EAAE,SAAS;IACfC,SAAS,EAAE,aAAa;IACxBC,OAAO,EAAE,QAAQ;IACjBC,KAAK,EAAE,OAAO;IACdC,QAAQ,EAAE;EACZ,CAAC;EACDb,IAAI,EAAE;IACJM,EAAE,EAAE,GAAG;IACPC,EAAE,EAAE,GAAG;IACPC,OAAO,EAAE,QAAQ;IACjBC,IAAI,EAAE,SAAS;IACfC,SAAS,EAAE,aAAa;IACxBC,OAAO,EAAE,QAAQ;IACjBC,KAAK,EAAE,OAAO;IACdC,QAAQ,EAAE;EACZ;AACF,CAAC;AAED,MAAMC,yBAAyB,GAAG;EAChChB,MAAM,EAAE;IACNQ,EAAE,EAAE,GAAG;IACPC,EAAE,EAAE,GAAG;IACPC,OAAO,EAAE,WAAW;IACpBC,IAAI,EAAE,YAAY;IAClBC,SAAS,EAAE,aAAa;IACxBC,OAAO,EAAE,WAAW;IACpBC,KAAK,EAAE,UAAU;IACjBC,QAAQ,EAAE;EACZ,CAAC;EACDd,WAAW,EAAE;IACXO,EAAE,EAAE,GAAG;IACPC,EAAE,EAAE,GAAG;IACPC,OAAO,EAAE,WAAW;IACpBC,IAAI,EAAE,YAAY;IAClBC,SAAS,EAAE,aAAa;IACxBC,OAAO,EAAE,WAAW;IACpBC,KAAK,EAAE,UAAU;IACjBC,QAAQ,EAAE;EACZ,CAAC;EACDb,IAAI,EAAE;IACJM,EAAE,EAAE,GAAG;IACPC,EAAE,EAAE,GAAG;IACPC,OAAO,EAAE,WAAW;IACpBC,IAAI,EAAE,YAAY;IAClBC,SAAS,EAAE,aAAa;IACxBC,OAAO,EAAE,WAAW;IACpBC,KAAK,EAAE,UAAU;IACjBC,QAAQ,EAAE;EACZ;AACF,CAAC;AAED,MAAME,aAAa,GAAIC,GAAG,IAAKC,MAAM,CAACD,GAAG,CAAC;AAE1C,OAAO,MAAME,QAAQ,GAAG;EACtBH,aAAa,EAAEA,aAAa;EAE5BI,GAAG,EAAEvB,eAAe,CAAC;IACnBwB,MAAM,EAAEvB,SAAS;IACjBwB,YAAY,EAAE;EAChB,CAAC,CAAC;EAEFC,OAAO,EAAE1B,eAAe,CAAC;IACvBwB,MAAM,EAAEnB,aAAa;IACrBoB,YAAY,EAAE,MAAM;IACpBE,gBAAgB,EAAGD,OAAO,IAAKA,OAAO,GAAG;EAC3C,CAAC,CAAC;EAEFE,KAAK,EAAE5B,eAAe,CAAC;IACrBwB,MAAM,EAAElB,WAAW;IACnBmB,YAAY,EAAE;EAChB,CAAC,CAAC;EAEFI,GAAG,EAAE7B,eAAe,CAAC;IACnBwB,MAAM,EAAEjB,SAAS;IACjBkB,YAAY,EAAE;EAChB,CAAC,CAAC;EAEFK,SAAS,EAAE9B,eAAe,CAAC;IACzBwB,MAAM,EAAEf,eAAe;IACvBgB,YAAY,EAAE,MAAM;IACpBM,gBAAgB,EAAEb,yBAAyB;IAC3Cc,sBAAsB,EAAE;EAC1B,CAAC;AACH,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
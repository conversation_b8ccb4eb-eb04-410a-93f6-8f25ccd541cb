﻿using Google.Apis.Auth.OAuth2.Responses;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.IdentityModel.Tokens;
using MyApi.Controllers;
using MyApi.Data;
using MyApi.Models;
using MyApi.Services;
using Org.BouncyCastle.Asn1.Ocsp;
using System;
using System.Collections.Generic;
using System.IdentityModel.Tokens.Jwt;
using System.IO;
using System.Linq;
using System.Security.Claims;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using Google.Apis.Auth.OAuth2;
using Google.Apis.Auth.OAuth2.Flows;
using Google.Apis.Auth.OAuth2.Responses;
using Microsoft.AspNetCore.Hosting;
using Microsoft.Extensions.Hosting;

[Route("api/[controller]")]
[ApiController]
public class AuthController : ControllerBase
{
    private readonly IConfiguration _configuration;
    private readonly AppDbContext _context;
    private readonly IWebHostEnvironment _env;
    private readonly RedisService _redisService;
    private readonly GoogleAuthorizationCodeFlow _flow;
    private readonly string _tokenStoragePath;
  

    public AuthController(IConfiguration configuration, IWebHostEnvironment evn, AppDbContext context, RedisService redisService)
    {
        _configuration = configuration;
        _context = context;
        _redisService = redisService;
        _env = evn;
        _tokenStoragePath = _configuration["GoogleApiService:TokenStoragePath"] ?? "gmail-token.json";

        var oauthClientKeyPath = _configuration["GoogleApiService:OAuthClientKeyPath"];
        if (string.IsNullOrEmpty(oauthClientKeyPath))
        {
            throw new InvalidOperationException("設定檔中缺少 GoogleApiService:OAuthClientKeyPath。");
        }

        var fullPath = Path.Combine(Directory.GetCurrentDirectory(), oauthClientKeyPath);
        if (System.IO.File.Exists(fullPath))
        {
            // 【修改處】從 JSON 檔案載入 Client Secrets
            var clientSecrets = GoogleClientSecrets.FromFile(fullPath).Secrets;

            _flow = new GoogleAuthorizationCodeFlow(new GoogleAuthorizationCodeFlow.Initializer
            {
                ClientSecrets = clientSecrets,
                Scopes = new[] { "https://www.googleapis.com/auth/gmail.send" },
                IncludeGrantedScopes = true
            });
        }
        else
        {
            throw new FileNotFoundException("OAuth Client ID 的 JSON 金鑰檔案未找到。", fullPath);
        }

        
    }

    [HttpPost("login")]
    public async Task<IActionResult> Login([FromBody] LoginRequest request)
    {
        var ipAddress = GetClientIpAddress();
        var loginStatus = "Failed";
        try
        {
            // 檢查 IP 是否被封鎖
            var ipBlockKey = $"UserLoginBlock:{ipAddress}";
            var isBlocked = await _redisService.GetAsync(ipBlockKey);
            if (!string.IsNullOrEmpty(isBlocked))
            {
                return BadRequest("請稍後再試");
            }

            var user = _context.Users.SingleOrDefault(u => u.Username == request.Username);

            if (user == null || !BCrypt.Net.BCrypt.Verify(request.Password, user.PasswordHash))
            {
                loginStatus = "Failed";
                return Unauthorized("帳號或密碼錯誤");
            }

            // 檢查用戶是否啟用
            if (!user.IsEnabled)
            {
                loginStatus = "Failed";
                return Unauthorized("帳號已被停用");
            }

            var token = GenerateJwtToken(user);

            // 檢查是否使用預設密碼
            bool isDefaultPassword = BCrypt.Net.BCrypt.Verify("123456", user.PasswordHash);

            loginStatus = "Success";
            return Ok(new
            {
                token = token,
                username = user.Name,
                userId = user.Id,
                isDefaultPassword = isDefaultPassword
            });
        }
        catch (Exception ex)
        {
            loginStatus = "Failed";
            return StatusCode(500, "登入時發生錯誤: " + ex.Message);
        }
        finally
        {
            // 如果登入失敗，添加 Redis 封鎖
            if (loginStatus == "Failed")
            {
                var userBlockKey = $"userloginblock:{request.Username}";
                await _redisService.SetAsync(userBlockKey, "", TimeSpan.FromSeconds(3));
            }

            // 記錄登入日誌
            await AddUserLoginLog(request.Username, $"Login {loginStatus}");
        }
    }

    private string GenerateJwtToken(User user)
    {
        var roles = _context.UserRoles
            .Where(ur => ur.UserId == user.Id)
            .Select(ur => ur.Role.Name)
            .ToList();

        var claims = new List<Claim>
        {
            new Claim(ClaimTypes.Name, user.Username),
            new Claim("UserId", user.Id.ToString()),
            new Claim(ClaimTypes.NameIdentifier, user.Id.ToString())
        };

        foreach (var role in roles)
        {
            claims.Add(new Claim(ClaimTypes.Role, role));
        }

        var key = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(_configuration["Jwt:Key"]));
        var creds = new SigningCredentials(key, SecurityAlgorithms.HmacSha256);

        var token = new JwtSecurityToken(
            issuer: _configuration["Jwt:Issuer"],
            audience: _configuration["Jwt:Audience"],
            claims: claims,
            expires: DateTime.Now.AddHours(1),
            signingCredentials: creds
        );

        return new JwtSecurityTokenHandler().WriteToken(token);
    }

    [HttpPost("logout")]
    public async Task<IActionResult> LogOut([FromBody] int userid)
    {

        var user = _context.Users.SingleOrDefault(u => u.Id == userid);
        // 記錄登入日誌
        await AddUserLoginLog(user.Username, "Logout Success");

        return Ok();
    }

    #region 登入紀錄管理

    /// <summary>
    /// 添加用戶登入紀錄
    /// </summary>
    public async Task<IActionResult> AddUserLoginLog(string Username, string Status)
    {
        try
        {
            var userAgent = Request.Headers["User-Agent"].ToString();
            var ipAddress = GetClientIpAddress();

            var loginLog = new UserLoginLog
            {
                Username = Username,
                IPAddress = ipAddress,
                Device = ExtractDevice(userAgent),
                Browser = ExtractBrowser(userAgent),
                Status = Status,
                CreatedAt = DateTime.Now
            };

            _context.UserLoginLogs.Add(loginLog);
            await _context.SaveChangesAsync();

            return Ok(new { message = "登入紀錄已添加", id = loginLog.Id });
        }
        catch (Exception ex)
        {
            return StatusCode(500, new { error = "添加登入紀錄失敗", details = ex.Message });
        }
    }

    /// <summary>
    /// 獲取用戶登入紀錄
    /// </summary>
    [Authorize(Roles = "Admin,Manager")]
    [HttpGet("GetUserLoginLog")]
    public async Task<IActionResult> GetUserLoginLog([FromQuery] GetUserLoginLogRequest request)
    {
        try
        {
            var query = _context.UserLoginLogs.AsQueryable();

            // 條件篩選
            if (!string.IsNullOrEmpty(request.IPAddress))
            {
                query = query.Where(x => x.IPAddress.Contains(request.IPAddress));
            }

            if (!string.IsNullOrEmpty(request.Device))
            {
                query = query.Where(x => x.Device != null && x.Device.Contains(request.Device));
            }

            if (!string.IsNullOrEmpty(request.Status))
            {
                query = query.Where(x => x.Status == request.Status);
            }

            if (request.CreatedAtStart.HasValue)
            {
                query = query.Where(x => x.CreatedAt >= request.CreatedAtStart.Value);
            }

            if (request.CreatedAtEnd.HasValue)
            {
                query = query.Where(x => x.CreatedAt <= request.CreatedAtEnd.Value);
            }

            // 總數
            var totalCount = await query.CountAsync();

            // 分頁
            var logs = await query
                .OrderByDescending(x => x.CreatedAt)
                .Skip((request.Page - 1) * request.PageSize)
                .Take(request.PageSize)
                .Select(x => new UserLoginLogDto
                {
                    Id = x.Id,
                    Username = x.Username,
                    IPAddress = x.IPAddress,
                    Device = x.Device,
                    Browser = x.Browser,
                    Status = x.Status,
                    CreatedAt = x.CreatedAt
                })
                .ToListAsync();

            return Ok(new
            {
                data = logs,
                totalCount = totalCount,
                page = request.Page,
                pageSize = request.PageSize,
                totalPages = (int)Math.Ceiling((double)totalCount / request.PageSize)
            });
        }
        catch (Exception ex)
        {
            return StatusCode(500, new { error = "獲取登入紀錄失敗", details = ex.Message });
        }
    }

    #endregion

    #region 輔助方法

    private string GetClientIpAddress()
    {
        try
        {
            // 檢查 X-Forwarded-For 標頭
            var xForwardedFor = Request.Headers["X-Forwarded-For"].FirstOrDefault();
            if (!string.IsNullOrEmpty(xForwardedFor))
            {
                return xForwardedFor.Split(',')[0].Trim();
            }

            // 檢查 X-Real-IP 標頭
            var xRealIp = Request.Headers["X-Real-IP"].FirstOrDefault();
            if (!string.IsNullOrEmpty(xRealIp))
            {
                return xRealIp;
            }

            // 使用 RemoteIpAddress
            return HttpContext.Connection.RemoteIpAddress?.ToString() ?? "127.0.0.1";
        }
        catch
        {
            return "127.0.0.1";
        }
    }

    private string ExtractDevice(string userAgent)
    {
        if (string.IsNullOrEmpty(userAgent)) return "Unknown";

        try
        {
            // 簡單的設備檢測邏輯
            if (userAgent.Contains("Mobile") || userAgent.Contains("Android") || userAgent.Contains("iPhone"))
            {
                if (userAgent.Contains("iPhone")) return "iPhone";
                if (userAgent.Contains("Android")) return "Android";
                return "Mobile";
            }

            if (userAgent.Contains("iPad")) return "iPad";
            if (userAgent.Contains("Macintosh")) return "Mac";
            if (userAgent.Contains("Windows")) return "Windows";
            if (userAgent.Contains("Linux")) return "Linux";

            return "Desktop";
        }
        catch
        {
            return "Unknown";
        }
    }

    private string ExtractBrowser(string userAgent)
    {
        if (string.IsNullOrEmpty(userAgent)) return "Unknown";

        try
        {
            if (userAgent.Contains("Chrome") && !userAgent.Contains("Edg")) return "Chrome";
            if (userAgent.Contains("Firefox")) return "Firefox";
            if (userAgent.Contains("Safari") && !userAgent.Contains("Chrome")) return "Safari";
            if (userAgent.Contains("Edg")) return "Edge";
            if (userAgent.Contains("Opera")) return "Opera";

            return "Other";
        }
        catch
        {
            return "Unknown";
        }
    }

    #endregion

    #region google 驗證

    [HttpGet("signin-google")]
    public IActionResult SignInWithGoogle()
    {

        var redirectUri = "https://nbhphysical.idv.tw/api/auth/google-callback";
        if (_env.IsDevelopment())
        {
            redirectUri = "https://localhost:5002/api/auth/google-callback";
        }

        
        var authUrl = _flow.CreateAuthorizationCodeRequest(redirectUri).Build();

        return Redirect(authUrl.ToString());
    }

    [HttpGet("google-callback")]
    public async Task<IActionResult> GoogleCallback(string code)
    {
        try
        {
            if (string.IsNullOrEmpty(code))
            {
                return BadRequest("授權碼 (code) 遺失。");
            }

            var redirectUri = "https://nbhphysical.idv.tw/api/auth/google-callback";
            if (_env.IsDevelopment())
            {
                redirectUri = "https://localhost:5002/api/auth/google-callback";
            }

            TokenResponse token = await _flow.ExchangeCodeForTokenAsync(
                "user",
                code,
                redirectUri,
                CancellationToken.None
            );

            // 將 Refresh Token 儲存到指定的檔案中
            var fullTokenPath = Path.Combine(Directory.GetCurrentDirectory(), _tokenStoragePath);
            await System.IO.File.WriteAllTextAsync(fullTokenPath, token.RefreshToken);

            return Ok($"授權成功！Refresh Token 已儲存至: {fullTokenPath}。您現在可以關閉此頁面，後端服務已可自動寄信。");
        }
        catch (Exception ex)
        {
            return BadRequest($"取得 access token 發生錯誤: {ex.Message}");
        }
    }

    #endregion

    #region 權限檢查

    /// <summary>
    /// 檢查頁面訪問權限
    /// </summary>
    [Authorize]
    [HttpGet("check-page-permission")]
    public async Task<IActionResult> CheckPagePermission([FromQuery] string pageName)
    {
        try
        {
            var userIdClaim = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
            if (string.IsNullOrEmpty(userIdClaim) || !int.TryParse(userIdClaim, out int userId))
            {
                return Unauthorized("無效的用戶身份");
            }

            var user = await _context.Users.FindAsync(userId);
            if (user == null)
            {
                return NotFound("用戶不存在");
            }

            // 定義頁面權限映射
            var pagePermissions = new Dictionary<string, string[]>
            {
                { "users", new[] { "Admin", "Manager" } },
                { "doctors", new[] { "Admin", "Manager" } },
                { "patients", new[] { "Admin", "Manager", "Doctor" } },
                { "treatments", new[] { "Admin", "Manager", "Doctor" } },
                { "schedules", new[] { "Admin", "Manager", "Doctor" } },
                { "receipts", new[] { "Admin", "Manager" } },
                { "reports", new[] { "Admin", "Manager" } },
                { "login-logs", new[] { "Admin", "Manager" } },
                { "ip-blocks", new[] { "Admin" } },
                { "backup", new[] { "Admin" } }
            };

            // 檢查頁面權限
            var hasPermission = false;
            var requiredRoles = new string[] { "Any" };

            if (pagePermissions.ContainsKey(pageName.ToLower()))
            {
                requiredRoles = pagePermissions[pageName.ToLower()];
                hasPermission = requiredRoles.Any(role => user.Role.Contains(role));
            }
            else
            {
                // 如果頁面不在權限映射中，預設允許所有已登入用戶訪問
                hasPermission = true;
            }

            return Ok(new
            {
                hasPermission,
                userRole = user.Role,
                pageName,
                requiredRoles
            });
        }
        catch (Exception ex)
        {
            return StatusCode(500, new { error = "檢查頁面權限失敗", details = ex.Message });
        }
    }

    /// <summary>
    /// 獲取當前用戶資訊和權限
    /// </summary>
    [Authorize]
    [HttpGet("user-info")]
    public async Task<IActionResult> GetUserInfo()
    {
        try
        {
            var userIdClaim = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
            if (string.IsNullOrEmpty(userIdClaim) || !int.TryParse(userIdClaim, out int userId))
            {
                return Unauthorized("無效的用戶身份");
            }

            var user = await _context.Users.FindAsync(userId);
            if (user == null)
            {
                return NotFound("用戶不存在");
            }

            return Ok(new
            {
                id = user.Id,
                username = user.Username,
                role = user.Role,
                isEnabled = user.IsEnabled
            });
        }
        catch (Exception ex)
        {
            return StatusCode(500, new { error = "獲取用戶資訊失敗", details = ex.Message });
        }
    }

    #endregion
}

public class LoginRequest
{
    public string Username { get; set; }
    public string Password { get; set; }
}
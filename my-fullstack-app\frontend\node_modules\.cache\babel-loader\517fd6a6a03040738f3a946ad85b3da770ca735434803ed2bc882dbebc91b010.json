{"ast": null, "code": "import { buildLocalizeFn } from \"../../_lib/buildLocalizeFn.js\";\nconst eraValues = {\n  narrow: [\"ჩ.წ-მდე\", \"ჩ.წ\"],\n  abbreviated: [\"ჩვ.წ-მდე\", \"ჩვ.წ\"],\n  wide: [\"ჩვენს წელთაღრიცხვამდე\", \"ჩვენი წელთაღრიცხვით\"]\n};\nconst quarterValues = {\n  narrow: [\"1\", \"2\", \"3\", \"4\"],\n  abbreviated: [\"1-ლი კვ\", \"2-ე კვ\", \"3-ე კვ\", \"4-ე კვ\"],\n  wide: [\"1-ლი კვარტალი\", \"2-ე კვარტალი\", \"3-ე კვარტალი\", \"4-ე კვარტალი\"]\n};\n\n// Note: in English, the names of days of the week and months are capitalized.\n// If you are making a new locale based on this one, check if the same is true for the language you're working on.\n// Generally, formatted dates should look like they are in the middle of a sentence,\n// e.g. in Spanish language the weekdays and months should be in the lowercase.\nconst monthValues = {\n  narrow: [\"ია\", \"თე\", \"მა\", \"აპ\", \"მს\", \"ვნ\", \"ვლ\", \"აგ\", \"სე\", \"ოქ\", \"ნო\", \"დე\"],\n  abbreviated: [\"იან\", \"თებ\", \"მარ\", \"აპრ\", \"მაი\", \"ივნ\", \"ივლ\", \"აგვ\", \"სექ\", \"ოქტ\", \"ნოე\", \"დეკ\"],\n  wide: [\"იანვარი\", \"თებერვალი\", \"მარტი\", \"აპრილი\", \"მაისი\", \"ივნისი\", \"ივლისი\", \"აგვისტო\", \"სექტემბერი\", \"ოქტომბერი\", \"ნოემბერი\", \"დეკემბერი\"]\n};\nconst dayValues = {\n  narrow: [\"კვ\", \"ორ\", \"სა\", \"ოთ\", \"ხუ\", \"პა\", \"შა\"],\n  short: [\"კვი\", \"ორშ\", \"სამ\", \"ოთხ\", \"ხუთ\", \"პარ\", \"შაბ\"],\n  abbreviated: [\"კვი\", \"ორშ\", \"სამ\", \"ოთხ\", \"ხუთ\", \"პარ\", \"შაბ\"],\n  wide: [\"კვირა\", \"ორშაბათი\", \"სამშაბათი\", \"ოთხშაბათი\", \"ხუთშაბათი\", \"პარასკევი\", \"შაბათი\"]\n};\nconst dayPeriodValues = {\n  narrow: {\n    am: \"a\",\n    pm: \"p\",\n    midnight: \"შუაღამე\",\n    noon: \"შუადღე\",\n    morning: \"დილა\",\n    afternoon: \"საღამო\",\n    evening: \"საღამო\",\n    night: \"ღამე\"\n  },\n  abbreviated: {\n    am: \"AM\",\n    pm: \"PM\",\n    midnight: \"შუაღამე\",\n    noon: \"შუადღე\",\n    morning: \"დილა\",\n    afternoon: \"საღამო\",\n    evening: \"საღამო\",\n    night: \"ღამე\"\n  },\n  wide: {\n    am: \"a.m.\",\n    pm: \"p.m.\",\n    midnight: \"შუაღამე\",\n    noon: \"შუადღე\",\n    morning: \"დილა\",\n    afternoon: \"საღამო\",\n    evening: \"საღამო\",\n    night: \"ღამე\"\n  }\n};\nconst formattingDayPeriodValues = {\n  narrow: {\n    am: \"a\",\n    pm: \"p\",\n    midnight: \"შუაღამით\",\n    noon: \"შუადღისას\",\n    morning: \"დილით\",\n    afternoon: \"ნაშუადღევს\",\n    evening: \"საღამოს\",\n    night: \"ღამით\"\n  },\n  abbreviated: {\n    am: \"AM\",\n    pm: \"PM\",\n    midnight: \"შუაღამით\",\n    noon: \"შუადღისას\",\n    morning: \"დილით\",\n    afternoon: \"ნაშუადღევს\",\n    evening: \"საღამოს\",\n    night: \"ღამით\"\n  },\n  wide: {\n    am: \"a.m.\",\n    pm: \"p.m.\",\n    midnight: \"შუაღამით\",\n    noon: \"შუადღისას\",\n    morning: \"დილით\",\n    afternoon: \"ნაშუადღევს\",\n    evening: \"საღამოს\",\n    night: \"ღამით\"\n  }\n};\nconst ordinalNumber = dirtyNumber => {\n  const number = Number(dirtyNumber);\n  if (number === 1) {\n    return number + \"-ლი\";\n  }\n  return number + \"-ე\";\n};\nexport const localize = {\n  ordinalNumber,\n  era: buildLocalizeFn({\n    values: eraValues,\n    defaultWidth: \"wide\"\n  }),\n  quarter: buildLocalizeFn({\n    values: quarterValues,\n    defaultWidth: \"wide\",\n    argumentCallback: quarter => quarter - 1\n  }),\n  month: buildLocalizeFn({\n    values: monthValues,\n    defaultWidth: \"wide\"\n  }),\n  day: buildLocalizeFn({\n    values: dayValues,\n    defaultWidth: \"wide\"\n  }),\n  dayPeriod: buildLocalizeFn({\n    values: dayPeriodValues,\n    defaultWidth: \"wide\",\n    formattingValues: formattingDayPeriodValues,\n    defaultFormattingWidth: \"wide\"\n  })\n};", "map": {"version": 3, "names": ["buildLocalizeFn", "<PERSON><PERSON><PERSON><PERSON>", "narrow", "abbreviated", "wide", "quarterValues", "month<PERSON><PERSON><PERSON>", "dayV<PERSON><PERSON>", "short", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "am", "pm", "midnight", "noon", "morning", "afternoon", "evening", "night", "formattingDayPeriodValues", "ordinalNumber", "dirtyNumber", "number", "Number", "localize", "era", "values", "defaultWidth", "quarter", "argument<PERSON>allback", "month", "day", "<PERSON><PERSON><PERSON><PERSON>", "formattingValues", "defaultFormattingWidth"], "sources": ["C:/Users/<USER>/Desktop/demo-git/demo-react/my-fullstack-app/frontend/node_modules/date-fns/locale/ka/_lib/localize.js"], "sourcesContent": ["import { buildLocalizeFn } from \"../../_lib/buildLocalizeFn.js\";\n\nconst eraValues = {\n  narrow: [\"ჩ.წ-მდე\", \"ჩ.წ\"],\n  abbreviated: [\"ჩვ.წ-მდე\", \"ჩვ.წ\"],\n  wide: [\"ჩვენს წელთაღრიცხვამდე\", \"ჩვენი წელთაღრიცხვით\"],\n};\n\nconst quarterValues = {\n  narrow: [\"1\", \"2\", \"3\", \"4\"],\n  abbreviated: [\"1-ლი კვ\", \"2-ე კვ\", \"3-ე კვ\", \"4-ე კვ\"],\n  wide: [\"1-ლი კვარტალი\", \"2-ე კვარტალი\", \"3-ე კვარტალი\", \"4-ე კვარტალი\"],\n};\n\n// Note: in English, the names of days of the week and months are capitalized.\n// If you are making a new locale based on this one, check if the same is true for the language you're working on.\n// Generally, formatted dates should look like they are in the middle of a sentence,\n// e.g. in Spanish language the weekdays and months should be in the lowercase.\nconst monthValues = {\n  narrow: [\n    \"ია\",\n    \"თე\",\n    \"მა\",\n    \"აპ\",\n    \"მს\",\n    \"ვნ\",\n    \"ვლ\",\n    \"აგ\",\n    \"სე\",\n    \"ოქ\",\n    \"ნო\",\n    \"დე\",\n  ],\n\n  abbreviated: [\n    \"იან\",\n    \"თებ\",\n    \"მარ\",\n    \"აპრ\",\n    \"მაი\",\n    \"ივნ\",\n    \"ივლ\",\n    \"აგვ\",\n    \"სექ\",\n    \"ოქტ\",\n    \"ნოე\",\n    \"დეკ\",\n  ],\n\n  wide: [\n    \"იანვარი\",\n    \"თებერვალი\",\n    \"მარტი\",\n    \"აპრილი\",\n    \"მაისი\",\n    \"ივნისი\",\n    \"ივლისი\",\n    \"აგვისტო\",\n    \"სექტემბერი\",\n    \"ოქტომბერი\",\n    \"ნოემბერი\",\n    \"დეკემბერი\",\n  ],\n};\n\nconst dayValues = {\n  narrow: [\"კვ\", \"ორ\", \"სა\", \"ოთ\", \"ხუ\", \"პა\", \"შა\"],\n  short: [\"კვი\", \"ორშ\", \"სამ\", \"ოთხ\", \"ხუთ\", \"პარ\", \"შაბ\"],\n  abbreviated: [\"კვი\", \"ორშ\", \"სამ\", \"ოთხ\", \"ხუთ\", \"პარ\", \"შაბ\"],\n  wide: [\n    \"კვირა\",\n    \"ორშაბათი\",\n    \"სამშაბათი\",\n    \"ოთხშაბათი\",\n    \"ხუთშაბათი\",\n    \"პარასკევი\",\n    \"შაბათი\",\n  ],\n};\n\nconst dayPeriodValues = {\n  narrow: {\n    am: \"a\",\n    pm: \"p\",\n    midnight: \"შუაღამე\",\n    noon: \"შუადღე\",\n    morning: \"დილა\",\n    afternoon: \"საღამო\",\n    evening: \"საღამო\",\n    night: \"ღამე\",\n  },\n  abbreviated: {\n    am: \"AM\",\n    pm: \"PM\",\n    midnight: \"შუაღამე\",\n    noon: \"შუადღე\",\n    morning: \"დილა\",\n    afternoon: \"საღამო\",\n    evening: \"საღამო\",\n    night: \"ღამე\",\n  },\n  wide: {\n    am: \"a.m.\",\n    pm: \"p.m.\",\n    midnight: \"შუაღამე\",\n    noon: \"შუადღე\",\n    morning: \"დილა\",\n    afternoon: \"საღამო\",\n    evening: \"საღამო\",\n    night: \"ღამე\",\n  },\n};\n\nconst formattingDayPeriodValues = {\n  narrow: {\n    am: \"a\",\n    pm: \"p\",\n    midnight: \"შუაღამით\",\n    noon: \"შუადღისას\",\n    morning: \"დილით\",\n    afternoon: \"ნაშუადღევს\",\n    evening: \"საღამოს\",\n    night: \"ღამით\",\n  },\n  abbreviated: {\n    am: \"AM\",\n    pm: \"PM\",\n    midnight: \"შუაღამით\",\n    noon: \"შუადღისას\",\n    morning: \"დილით\",\n    afternoon: \"ნაშუადღევს\",\n    evening: \"საღამოს\",\n    night: \"ღამით\",\n  },\n  wide: {\n    am: \"a.m.\",\n    pm: \"p.m.\",\n    midnight: \"შუაღამით\",\n    noon: \"შუადღისას\",\n    morning: \"დილით\",\n    afternoon: \"ნაშუადღევს\",\n    evening: \"საღამოს\",\n    night: \"ღამით\",\n  },\n};\n\nconst ordinalNumber = (dirtyNumber) => {\n  const number = Number(dirtyNumber);\n\n  if (number === 1) {\n    return number + \"-ლი\";\n  }\n\n  return number + \"-ე\";\n};\n\nexport const localize = {\n  ordinalNumber,\n\n  era: buildLocalizeFn({\n    values: eraValues,\n    defaultWidth: \"wide\",\n  }),\n\n  quarter: buildLocalizeFn({\n    values: quarterValues,\n    defaultWidth: \"wide\",\n    argumentCallback: (quarter) => quarter - 1,\n  }),\n\n  month: buildLocalizeFn({\n    values: monthValues,\n    defaultWidth: \"wide\",\n  }),\n\n  day: buildLocalizeFn({\n    values: dayValues,\n    defaultWidth: \"wide\",\n  }),\n\n  dayPeriod: buildLocalizeFn({\n    values: dayPeriodValues,\n    defaultWidth: \"wide\",\n    formattingValues: formattingDayPeriodValues,\n    defaultFormattingWidth: \"wide\",\n  }),\n};\n"], "mappings": "AAAA,SAASA,eAAe,QAAQ,+BAA+B;AAE/D,MAAMC,SAAS,GAAG;EAChBC,MAAM,EAAE,CAAC,SAAS,EAAE,KAAK,CAAC;EAC1BC,WAAW,EAAE,CAAC,UAAU,EAAE,MAAM,CAAC;EACjCC,IAAI,EAAE,CAAC,uBAAuB,EAAE,qBAAqB;AACvD,CAAC;AAED,MAAMC,aAAa,GAAG;EACpBH,MAAM,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EAC5BC,WAAW,EAAE,CAAC,SAAS,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,CAAC;EACtDC,IAAI,EAAE,CAAC,eAAe,EAAE,cAAc,EAAE,cAAc,EAAE,cAAc;AACxE,CAAC;;AAED;AACA;AACA;AACA;AACA,MAAME,WAAW,GAAG;EAClBJ,MAAM,EAAE,CACN,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,CACL;EAEDC,WAAW,EAAE,CACX,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,CACN;EAEDC,IAAI,EAAE,CACJ,SAAS,EACT,WAAW,EACX,OAAO,EACP,QAAQ,EACR,OAAO,EACP,QAAQ,EACR,QAAQ,EACR,SAAS,EACT,YAAY,EACZ,WAAW,EACX,UAAU,EACV,WAAW;AAEf,CAAC;AAED,MAAMG,SAAS,GAAG;EAChBL,MAAM,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;EAClDM,KAAK,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC;EACxDL,WAAW,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC;EAC9DC,IAAI,EAAE,CACJ,OAAO,EACP,UAAU,EACV,WAAW,EACX,WAAW,EACX,WAAW,EACX,WAAW,EACX,QAAQ;AAEZ,CAAC;AAED,MAAMK,eAAe,GAAG;EACtBP,MAAM,EAAE;IACNQ,EAAE,EAAE,GAAG;IACPC,EAAE,EAAE,GAAG;IACPC,QAAQ,EAAE,SAAS;IACnBC,IAAI,EAAE,QAAQ;IACdC,OAAO,EAAE,MAAM;IACfC,SAAS,EAAE,QAAQ;IACnBC,OAAO,EAAE,QAAQ;IACjBC,KAAK,EAAE;EACT,CAAC;EACDd,WAAW,EAAE;IACXO,EAAE,EAAE,IAAI;IACRC,EAAE,EAAE,IAAI;IACRC,QAAQ,EAAE,SAAS;IACnBC,IAAI,EAAE,QAAQ;IACdC,OAAO,EAAE,MAAM;IACfC,SAAS,EAAE,QAAQ;IACnBC,OAAO,EAAE,QAAQ;IACjBC,KAAK,EAAE;EACT,CAAC;EACDb,IAAI,EAAE;IACJM,EAAE,EAAE,MAAM;IACVC,EAAE,EAAE,MAAM;IACVC,QAAQ,EAAE,SAAS;IACnBC,IAAI,EAAE,QAAQ;IACdC,OAAO,EAAE,MAAM;IACfC,SAAS,EAAE,QAAQ;IACnBC,OAAO,EAAE,QAAQ;IACjBC,KAAK,EAAE;EACT;AACF,CAAC;AAED,MAAMC,yBAAyB,GAAG;EAChChB,MAAM,EAAE;IACNQ,EAAE,EAAE,GAAG;IACPC,EAAE,EAAE,GAAG;IACPC,QAAQ,EAAE,UAAU;IACpBC,IAAI,EAAE,WAAW;IACjBC,OAAO,EAAE,OAAO;IAChBC,SAAS,EAAE,YAAY;IACvBC,OAAO,EAAE,SAAS;IAClBC,KAAK,EAAE;EACT,CAAC;EACDd,WAAW,EAAE;IACXO,EAAE,EAAE,IAAI;IACRC,EAAE,EAAE,IAAI;IACRC,QAAQ,EAAE,UAAU;IACpBC,IAAI,EAAE,WAAW;IACjBC,OAAO,EAAE,OAAO;IAChBC,SAAS,EAAE,YAAY;IACvBC,OAAO,EAAE,SAAS;IAClBC,KAAK,EAAE;EACT,CAAC;EACDb,IAAI,EAAE;IACJM,EAAE,EAAE,MAAM;IACVC,EAAE,EAAE,MAAM;IACVC,QAAQ,EAAE,UAAU;IACpBC,IAAI,EAAE,WAAW;IACjBC,OAAO,EAAE,OAAO;IAChBC,SAAS,EAAE,YAAY;IACvBC,OAAO,EAAE,SAAS;IAClBC,KAAK,EAAE;EACT;AACF,CAAC;AAED,MAAME,aAAa,GAAIC,WAAW,IAAK;EACrC,MAAMC,MAAM,GAAGC,MAAM,CAACF,WAAW,CAAC;EAElC,IAAIC,MAAM,KAAK,CAAC,EAAE;IAChB,OAAOA,MAAM,GAAG,KAAK;EACvB;EAEA,OAAOA,MAAM,GAAG,IAAI;AACtB,CAAC;AAED,OAAO,MAAME,QAAQ,GAAG;EACtBJ,aAAa;EAEbK,GAAG,EAAExB,eAAe,CAAC;IACnByB,MAAM,EAAExB,SAAS;IACjByB,YAAY,EAAE;EAChB,CAAC,CAAC;EAEFC,OAAO,EAAE3B,eAAe,CAAC;IACvByB,MAAM,EAAEpB,aAAa;IACrBqB,YAAY,EAAE,MAAM;IACpBE,gBAAgB,EAAGD,OAAO,IAAKA,OAAO,GAAG;EAC3C,CAAC,CAAC;EAEFE,KAAK,EAAE7B,eAAe,CAAC;IACrByB,MAAM,EAAEnB,WAAW;IACnBoB,YAAY,EAAE;EAChB,CAAC,CAAC;EAEFI,GAAG,EAAE9B,eAAe,CAAC;IACnByB,MAAM,EAAElB,SAAS;IACjBmB,YAAY,EAAE;EAChB,CAAC,CAAC;EAEFK,SAAS,EAAE/B,eAAe,CAAC;IACzByB,MAAM,EAAEhB,eAAe;IACvBiB,YAAY,EAAE,MAAM;IACpBM,gBAAgB,EAAEd,yBAAyB;IAC3Ce,sBAAsB,EAAE;EAC1B,CAAC;AACH,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
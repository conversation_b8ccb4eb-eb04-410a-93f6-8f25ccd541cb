using System.Threading.Tasks;

namespace MyApi.Services
{
    public interface IGmailService
    {
        /// <summary>
        /// 發送郵件
        /// </summary>
        /// <param name="to">收件人郵箱</param>
        /// <param name="subject">郵件主題</param>
        /// <param name="body">郵件內容</param>
        /// <param name="attachmentPath">附件路徑（可選）</param>
        /// <returns>是否發送成功</returns>
        Task<bool> SendEmailAsync(string to, string subject, string body, string? attachmentPath = null);

        /// <summary>
        /// 發送收據郵件
        /// </summary>
        /// <param name="to">收件人郵箱</param>
        /// <param name="patientName">病患姓名</param>
        /// <param name="receiptNo">收據編號</param>
        /// <param name="pdfPath">PDF 檔案路徑</param>
        /// <returns>是否發送成功</returns>
        Task<bool> SendReceiptEmailAsync(string to, string patientName, string receiptNo, string pdfPath);
    }
}

import React from 'react';
import { ProgressSpinner } from 'primereact/progressspinner';

interface LoadingSpinnerProps {
  size?: 'small' | 'medium' | 'large';
  message?: string;
  fullScreen?: boolean;
  className?: string;
}

const LoadingSpinner: React.FC<LoadingSpinnerProps> = ({
  size = 'medium',
  message = 'Loading...',
  fullScreen = false,
  className = '',
}) => {
  const sizeMap = {
    small: '30px',
    medium: '50px',
    large: '70px',
  };

  const spinnerStyle = {
    width: sizeMap[size],
    height: sizeMap[size],
  };

  const containerClass = fullScreen
    ? 'fixed inset-0 flex items-center justify-center bg-black bg-opacity-50 z-50'
    : 'flex flex-column align-items-center justify-content-center p-4';

  return (
    <div className={`${containerClass} ${className}`}>
      <ProgressSpinner style={spinnerStyle} strokeWidth="4" />
      {message && (
        <p className="mt-3 text-center text-gray-600">{message}</p>
      )}
    </div>
  );
};

export default LoadingSpinner;

{"ast": null, "code": "const formatRelativeLocale = {\n  lastWeek: \"eeee 'اللي جاي الساعة' p\",\n  yesterday: \"'إمبارح الساعة' p\",\n  today: \"'النهاردة الساعة' p\",\n  tomorrow: \"'بكرة الساعة' p\",\n  nextWeek: \"eeee 'الساعة' p\",\n  other: \"P\"\n};\nexport const formatRelative = (token, _date, _baseDate, _options) => formatRelativeLocale[token];", "map": {"version": 3, "names": ["formatRelativeLocale", "lastWeek", "yesterday", "today", "tomorrow", "nextWeek", "other", "formatRelative", "token", "_date", "_baseDate", "_options"], "sources": ["C:/Users/<USER>/Desktop/demo-git/demo-react/my-fullstack-app/frontend/node_modules/date-fns/locale/ar-EG/_lib/formatRelative.js"], "sourcesContent": ["const formatRelativeLocale = {\n  lastWeek: \"eeee 'اللي جاي الساعة' p\",\n  yesterday: \"'إمبارح الساعة' p\",\n  today: \"'النهاردة الساعة' p\",\n  tomorrow: \"'بكرة الساعة' p\",\n  nextWeek: \"eeee 'الساعة' p\",\n  other: \"P\",\n};\n\nexport const formatRelative = (token, _date, _baseDate, _options) =>\n  formatRelativeLocale[token];\n"], "mappings": "AAAA,MAAMA,oBAAoB,GAAG;EAC3BC,QAAQ,EAAE,0BAA0B;EACpCC,SAAS,EAAE,mBAAmB;EAC9BC,KAAK,EAAE,qBAAqB;EAC5BC,QAAQ,EAAE,iBAAiB;EAC3BC,QAAQ,EAAE,iBAAiB;EAC3BC,KAAK,EAAE;AACT,CAAC;AAED,OAAO,MAAMC,cAAc,GAAGA,CAACC,KAAK,EAAEC,KAAK,EAAEC,SAAS,EAAEC,QAAQ,KAC9DX,oBAAoB,CAACQ,KAAK,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
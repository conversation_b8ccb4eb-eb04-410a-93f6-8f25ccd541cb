{"ast": null, "code": "const formatDistanceLocale = {\n  lessThanXSeconds: {\n    one: {\n      regular: \"méně než 1 sekunda\",\n      past: \"před méně než 1 sekundou\",\n      future: \"za méně než 1 sekundu\"\n    },\n    few: {\n      regular: \"méně než {{count}} sekundy\",\n      past: \"před méně než {{count}} sekundami\",\n      future: \"za méně než {{count}} sekundy\"\n    },\n    many: {\n      regular: \"méně než {{count}} sekund\",\n      past: \"před méně než {{count}} sekundami\",\n      future: \"za méně než {{count}} sekund\"\n    }\n  },\n  xSeconds: {\n    one: {\n      regular: \"1 sekunda\",\n      past: \"před 1 sekundou\",\n      future: \"za 1 sekundu\"\n    },\n    few: {\n      regular: \"{{count}} sekundy\",\n      past: \"před {{count}} sekundami\",\n      future: \"za {{count}} sekundy\"\n    },\n    many: {\n      regular: \"{{count}} sekund\",\n      past: \"před {{count}} sekundami\",\n      future: \"za {{count}} sekund\"\n    }\n  },\n  halfAMinute: {\n    type: \"other\",\n    other: {\n      regular: \"půl minuty\",\n      past: \"před půl minutou\",\n      future: \"za půl minuty\"\n    }\n  },\n  lessThanXMinutes: {\n    one: {\n      regular: \"méně než 1 minuta\",\n      past: \"před méně než 1 minutou\",\n      future: \"za méně než 1 minutu\"\n    },\n    few: {\n      regular: \"méně než {{count}} minuty\",\n      past: \"před méně než {{count}} minutami\",\n      future: \"za méně než {{count}} minuty\"\n    },\n    many: {\n      regular: \"méně než {{count}} minut\",\n      past: \"před méně než {{count}} minutami\",\n      future: \"za méně než {{count}} minut\"\n    }\n  },\n  xMinutes: {\n    one: {\n      regular: \"1 minuta\",\n      past: \"před 1 minutou\",\n      future: \"za 1 minutu\"\n    },\n    few: {\n      regular: \"{{count}} minuty\",\n      past: \"před {{count}} minutami\",\n      future: \"za {{count}} minuty\"\n    },\n    many: {\n      regular: \"{{count}} minut\",\n      past: \"před {{count}} minutami\",\n      future: \"za {{count}} minut\"\n    }\n  },\n  aboutXHours: {\n    one: {\n      regular: \"přibližně 1 hodina\",\n      past: \"přibližně před 1 hodinou\",\n      future: \"přibližně za 1 hodinu\"\n    },\n    few: {\n      regular: \"přibližně {{count}} hodiny\",\n      past: \"přibližně před {{count}} hodinami\",\n      future: \"přibližně za {{count}} hodiny\"\n    },\n    many: {\n      regular: \"přibližně {{count}} hodin\",\n      past: \"přibližně před {{count}} hodinami\",\n      future: \"přibližně za {{count}} hodin\"\n    }\n  },\n  xHours: {\n    one: {\n      regular: \"1 hodina\",\n      past: \"před 1 hodinou\",\n      future: \"za 1 hodinu\"\n    },\n    few: {\n      regular: \"{{count}} hodiny\",\n      past: \"před {{count}} hodinami\",\n      future: \"za {{count}} hodiny\"\n    },\n    many: {\n      regular: \"{{count}} hodin\",\n      past: \"před {{count}} hodinami\",\n      future: \"za {{count}} hodin\"\n    }\n  },\n  xDays: {\n    one: {\n      regular: \"1 den\",\n      past: \"před 1 dnem\",\n      future: \"za 1 den\"\n    },\n    few: {\n      regular: \"{{count}} dny\",\n      past: \"před {{count}} dny\",\n      future: \"za {{count}} dny\"\n    },\n    many: {\n      regular: \"{{count}} dní\",\n      past: \"před {{count}} dny\",\n      future: \"za {{count}} dní\"\n    }\n  },\n  aboutXWeeks: {\n    one: {\n      regular: \"přibližně 1 týden\",\n      past: \"přibližně před 1 týdnem\",\n      future: \"přibližně za 1 týden\"\n    },\n    few: {\n      regular: \"přibližně {{count}} týdny\",\n      past: \"přibližně před {{count}} týdny\",\n      future: \"přibližně za {{count}} týdny\"\n    },\n    many: {\n      regular: \"přibližně {{count}} týdnů\",\n      past: \"přibližně před {{count}} týdny\",\n      future: \"přibližně za {{count}} týdnů\"\n    }\n  },\n  xWeeks: {\n    one: {\n      regular: \"1 týden\",\n      past: \"před 1 týdnem\",\n      future: \"za 1 týden\"\n    },\n    few: {\n      regular: \"{{count}} týdny\",\n      past: \"před {{count}} týdny\",\n      future: \"za {{count}} týdny\"\n    },\n    many: {\n      regular: \"{{count}} týdnů\",\n      past: \"před {{count}} týdny\",\n      future: \"za {{count}} týdnů\"\n    }\n  },\n  aboutXMonths: {\n    one: {\n      regular: \"přibližně 1 měsíc\",\n      past: \"přibližně před 1 měsícem\",\n      future: \"přibližně za 1 měsíc\"\n    },\n    few: {\n      regular: \"přibližně {{count}} měsíce\",\n      past: \"přibližně před {{count}} měsíci\",\n      future: \"přibližně za {{count}} měsíce\"\n    },\n    many: {\n      regular: \"přibližně {{count}} měsíců\",\n      past: \"přibližně před {{count}} měsíci\",\n      future: \"přibližně za {{count}} měsíců\"\n    }\n  },\n  xMonths: {\n    one: {\n      regular: \"1 měsíc\",\n      past: \"před 1 měsícem\",\n      future: \"za 1 měsíc\"\n    },\n    few: {\n      regular: \"{{count}} měsíce\",\n      past: \"před {{count}} měsíci\",\n      future: \"za {{count}} měsíce\"\n    },\n    many: {\n      regular: \"{{count}} měsíců\",\n      past: \"před {{count}} měsíci\",\n      future: \"za {{count}} měsíců\"\n    }\n  },\n  aboutXYears: {\n    one: {\n      regular: \"přibližně 1 rok\",\n      past: \"přibližně před 1 rokem\",\n      future: \"přibližně za 1 rok\"\n    },\n    few: {\n      regular: \"přibližně {{count}} roky\",\n      past: \"přibližně před {{count}} roky\",\n      future: \"přibližně za {{count}} roky\"\n    },\n    many: {\n      regular: \"přibližně {{count}} roků\",\n      past: \"přibližně před {{count}} roky\",\n      future: \"přibližně za {{count}} roků\"\n    }\n  },\n  xYears: {\n    one: {\n      regular: \"1 rok\",\n      past: \"před 1 rokem\",\n      future: \"za 1 rok\"\n    },\n    few: {\n      regular: \"{{count}} roky\",\n      past: \"před {{count}} roky\",\n      future: \"za {{count}} roky\"\n    },\n    many: {\n      regular: \"{{count}} roků\",\n      past: \"před {{count}} roky\",\n      future: \"za {{count}} roků\"\n    }\n  },\n  overXYears: {\n    one: {\n      regular: \"více než 1 rok\",\n      past: \"před více než 1 rokem\",\n      future: \"za více než 1 rok\"\n    },\n    few: {\n      regular: \"více než {{count}} roky\",\n      past: \"před více než {{count}} roky\",\n      future: \"za více než {{count}} roky\"\n    },\n    many: {\n      regular: \"více než {{count}} roků\",\n      past: \"před více než {{count}} roky\",\n      future: \"za více než {{count}} roků\"\n    }\n  },\n  almostXYears: {\n    one: {\n      regular: \"skoro 1 rok\",\n      past: \"skoro před 1 rokem\",\n      future: \"skoro za 1 rok\"\n    },\n    few: {\n      regular: \"skoro {{count}} roky\",\n      past: \"skoro před {{count}} roky\",\n      future: \"skoro za {{count}} roky\"\n    },\n    many: {\n      regular: \"skoro {{count}} roků\",\n      past: \"skoro před {{count}} roky\",\n      future: \"skoro za {{count}} roků\"\n    }\n  }\n};\nexport const formatDistance = (token, count, options) => {\n  let pluralResult;\n  const tokenValue = formatDistanceLocale[token];\n\n  // cs pluralization\n  if (tokenValue.type === \"other\") {\n    pluralResult = tokenValue.other;\n  } else if (count === 1) {\n    pluralResult = tokenValue.one;\n  } else if (count > 1 && count < 5) {\n    pluralResult = tokenValue.few;\n  } else {\n    pluralResult = tokenValue.many;\n  }\n\n  // times\n  const suffixExist = (options === null || options === void 0 ? void 0 : options.addSuffix) === true;\n  const comparison = options === null || options === void 0 ? void 0 : options.comparison;\n  let timeResult;\n  if (suffixExist && comparison === -1) {\n    timeResult = pluralResult.past;\n  } else if (suffixExist && comparison === 1) {\n    timeResult = pluralResult.future;\n  } else {\n    timeResult = pluralResult.regular;\n  }\n  return timeResult.replace(\"{{count}}\", String(count));\n};", "map": {"version": 3, "names": ["formatDistanceLocale", "lessThanXSeconds", "one", "regular", "past", "future", "few", "many", "xSeconds", "halfAMinute", "type", "other", "lessThanXMinutes", "xMinutes", "aboutXHours", "xHours", "xDays", "aboutXWeeks", "xWeeks", "aboutXMonths", "xMonths", "aboutXYears", "xYears", "overXYears", "almostXYears", "formatDistance", "token", "count", "options", "pluralResult", "tokenValue", "suffixExist", "addSuffix", "comparison", "timeResult", "replace", "String"], "sources": ["C:/Users/<USER>/Desktop/demo-git/demo-react/my-fullstack-app/frontend/node_modules/date-fns/locale/cs/_lib/formatDistance.js"], "sourcesContent": ["const formatDistanceLocale = {\n  lessThanXSeconds: {\n    one: {\n      regular: \"méně než 1 sekunda\",\n      past: \"před méně než 1 sekundou\",\n      future: \"za méně než 1 sekundu\",\n    },\n    few: {\n      regular: \"méně než {{count}} sekundy\",\n      past: \"před méně než {{count}} sekundami\",\n      future: \"za méně než {{count}} sekundy\",\n    },\n    many: {\n      regular: \"méně než {{count}} sekund\",\n      past: \"před méně než {{count}} sekundami\",\n      future: \"za méně než {{count}} sekund\",\n    },\n  },\n\n  xSeconds: {\n    one: {\n      regular: \"1 sekunda\",\n      past: \"před 1 sekundou\",\n      future: \"za 1 sekundu\",\n    },\n    few: {\n      regular: \"{{count}} sekundy\",\n      past: \"před {{count}} sekundami\",\n      future: \"za {{count}} sekundy\",\n    },\n    many: {\n      regular: \"{{count}} sekund\",\n      past: \"před {{count}} sekundami\",\n      future: \"za {{count}} sekund\",\n    },\n  },\n\n  halfAMinute: {\n    type: \"other\",\n    other: {\n      regular: \"půl minuty\",\n      past: \"před půl minutou\",\n      future: \"za půl minuty\",\n    },\n  },\n\n  lessThanXMinutes: {\n    one: {\n      regular: \"méně než 1 minuta\",\n      past: \"před méně než 1 minutou\",\n      future: \"za méně než 1 minutu\",\n    },\n    few: {\n      regular: \"méně než {{count}} minuty\",\n      past: \"před méně než {{count}} minutami\",\n      future: \"za méně než {{count}} minuty\",\n    },\n    many: {\n      regular: \"méně než {{count}} minut\",\n      past: \"před méně než {{count}} minutami\",\n      future: \"za méně než {{count}} minut\",\n    },\n  },\n\n  xMinutes: {\n    one: {\n      regular: \"1 minuta\",\n      past: \"před 1 minutou\",\n      future: \"za 1 minutu\",\n    },\n    few: {\n      regular: \"{{count}} minuty\",\n      past: \"před {{count}} minutami\",\n      future: \"za {{count}} minuty\",\n    },\n    many: {\n      regular: \"{{count}} minut\",\n      past: \"před {{count}} minutami\",\n      future: \"za {{count}} minut\",\n    },\n  },\n\n  aboutXHours: {\n    one: {\n      regular: \"přibližně 1 hodina\",\n      past: \"přibližně před 1 hodinou\",\n      future: \"přibližně za 1 hodinu\",\n    },\n    few: {\n      regular: \"přibližně {{count}} hodiny\",\n      past: \"přibližně před {{count}} hodinami\",\n      future: \"přibližně za {{count}} hodiny\",\n    },\n    many: {\n      regular: \"přibližně {{count}} hodin\",\n      past: \"přibližně před {{count}} hodinami\",\n      future: \"přibližně za {{count}} hodin\",\n    },\n  },\n\n  xHours: {\n    one: {\n      regular: \"1 hodina\",\n      past: \"před 1 hodinou\",\n      future: \"za 1 hodinu\",\n    },\n    few: {\n      regular: \"{{count}} hodiny\",\n      past: \"před {{count}} hodinami\",\n      future: \"za {{count}} hodiny\",\n    },\n    many: {\n      regular: \"{{count}} hodin\",\n      past: \"před {{count}} hodinami\",\n      future: \"za {{count}} hodin\",\n    },\n  },\n\n  xDays: {\n    one: {\n      regular: \"1 den\",\n      past: \"před 1 dnem\",\n      future: \"za 1 den\",\n    },\n    few: {\n      regular: \"{{count}} dny\",\n      past: \"před {{count}} dny\",\n      future: \"za {{count}} dny\",\n    },\n    many: {\n      regular: \"{{count}} dní\",\n      past: \"před {{count}} dny\",\n      future: \"za {{count}} dní\",\n    },\n  },\n\n  aboutXWeeks: {\n    one: {\n      regular: \"přibližně 1 týden\",\n      past: \"přibližně před 1 týdnem\",\n      future: \"přibližně za 1 týden\",\n    },\n\n    few: {\n      regular: \"přibližně {{count}} týdny\",\n      past: \"přibližně před {{count}} týdny\",\n      future: \"přibližně za {{count}} týdny\",\n    },\n\n    many: {\n      regular: \"přibližně {{count}} týdnů\",\n      past: \"přibližně před {{count}} týdny\",\n      future: \"přibližně za {{count}} týdnů\",\n    },\n  },\n\n  xWeeks: {\n    one: {\n      regular: \"1 týden\",\n      past: \"před 1 týdnem\",\n      future: \"za 1 týden\",\n    },\n\n    few: {\n      regular: \"{{count}} týdny\",\n      past: \"před {{count}} týdny\",\n      future: \"za {{count}} týdny\",\n    },\n\n    many: {\n      regular: \"{{count}} týdnů\",\n      past: \"před {{count}} týdny\",\n      future: \"za {{count}} týdnů\",\n    },\n  },\n\n  aboutXMonths: {\n    one: {\n      regular: \"přibližně 1 měsíc\",\n      past: \"přibližně před 1 měsícem\",\n      future: \"přibližně za 1 měsíc\",\n    },\n\n    few: {\n      regular: \"přibližně {{count}} měsíce\",\n      past: \"přibližně před {{count}} měsíci\",\n      future: \"přibližně za {{count}} měsíce\",\n    },\n\n    many: {\n      regular: \"přibližně {{count}} měsíců\",\n      past: \"přibližně před {{count}} měsíci\",\n      future: \"přibližně za {{count}} měsíců\",\n    },\n  },\n\n  xMonths: {\n    one: {\n      regular: \"1 měsíc\",\n      past: \"před 1 měsícem\",\n      future: \"za 1 měsíc\",\n    },\n\n    few: {\n      regular: \"{{count}} měsíce\",\n      past: \"před {{count}} měsíci\",\n      future: \"za {{count}} měsíce\",\n    },\n\n    many: {\n      regular: \"{{count}} měsíců\",\n      past: \"před {{count}} měsíci\",\n      future: \"za {{count}} měsíců\",\n    },\n  },\n\n  aboutXYears: {\n    one: {\n      regular: \"přibližně 1 rok\",\n      past: \"přibližně před 1 rokem\",\n      future: \"přibližně za 1 rok\",\n    },\n    few: {\n      regular: \"přibližně {{count}} roky\",\n      past: \"přibližně před {{count}} roky\",\n      future: \"přibližně za {{count}} roky\",\n    },\n    many: {\n      regular: \"přibližně {{count}} roků\",\n      past: \"přibližně před {{count}} roky\",\n      future: \"přibližně za {{count}} roků\",\n    },\n  },\n\n  xYears: {\n    one: {\n      regular: \"1 rok\",\n      past: \"před 1 rokem\",\n      future: \"za 1 rok\",\n    },\n    few: {\n      regular: \"{{count}} roky\",\n      past: \"před {{count}} roky\",\n      future: \"za {{count}} roky\",\n    },\n    many: {\n      regular: \"{{count}} roků\",\n      past: \"před {{count}} roky\",\n      future: \"za {{count}} roků\",\n    },\n  },\n\n  overXYears: {\n    one: {\n      regular: \"více než 1 rok\",\n      past: \"před více než 1 rokem\",\n      future: \"za více než 1 rok\",\n    },\n    few: {\n      regular: \"více než {{count}} roky\",\n      past: \"před více než {{count}} roky\",\n      future: \"za více než {{count}} roky\",\n    },\n    many: {\n      regular: \"více než {{count}} roků\",\n      past: \"před více než {{count}} roky\",\n      future: \"za více než {{count}} roků\",\n    },\n  },\n\n  almostXYears: {\n    one: {\n      regular: \"skoro 1 rok\",\n      past: \"skoro před 1 rokem\",\n      future: \"skoro za 1 rok\",\n    },\n    few: {\n      regular: \"skoro {{count}} roky\",\n      past: \"skoro před {{count}} roky\",\n      future: \"skoro za {{count}} roky\",\n    },\n    many: {\n      regular: \"skoro {{count}} roků\",\n      past: \"skoro před {{count}} roky\",\n      future: \"skoro za {{count}} roků\",\n    },\n  },\n};\n\nexport const formatDistance = (token, count, options) => {\n  let pluralResult;\n\n  const tokenValue = formatDistanceLocale[token];\n\n  // cs pluralization\n  if (tokenValue.type === \"other\") {\n    pluralResult = tokenValue.other;\n  } else if (count === 1) {\n    pluralResult = tokenValue.one;\n  } else if (count > 1 && count < 5) {\n    pluralResult = tokenValue.few;\n  } else {\n    pluralResult = tokenValue.many;\n  }\n\n  // times\n  const suffixExist = options?.addSuffix === true;\n  const comparison = options?.comparison;\n\n  let timeResult;\n  if (suffixExist && comparison === -1) {\n    timeResult = pluralResult.past;\n  } else if (suffixExist && comparison === 1) {\n    timeResult = pluralResult.future;\n  } else {\n    timeResult = pluralResult.regular;\n  }\n\n  return timeResult.replace(\"{{count}}\", String(count));\n};\n"], "mappings": "AAAA,MAAMA,oBAAoB,GAAG;EAC3BC,gBAAgB,EAAE;IAChBC,GAAG,EAAE;MACHC,OAAO,EAAE,oBAAoB;MAC7BC,IAAI,EAAE,0BAA0B;MAChCC,MAAM,EAAE;IACV,CAAC;IACDC,GAAG,EAAE;MACHH,OAAO,EAAE,4BAA4B;MACrCC,IAAI,EAAE,mCAAmC;MACzCC,MAAM,EAAE;IACV,CAAC;IACDE,IAAI,EAAE;MACJJ,OAAO,EAAE,2BAA2B;MACpCC,IAAI,EAAE,mCAAmC;MACzCC,MAAM,EAAE;IACV;EACF,CAAC;EAEDG,QAAQ,EAAE;IACRN,GAAG,EAAE;MACHC,OAAO,EAAE,WAAW;MACpBC,IAAI,EAAE,iBAAiB;MACvBC,MAAM,EAAE;IACV,CAAC;IACDC,GAAG,EAAE;MACHH,OAAO,EAAE,mBAAmB;MAC5BC,IAAI,EAAE,0BAA0B;MAChCC,MAAM,EAAE;IACV,CAAC;IACDE,IAAI,EAAE;MACJJ,OAAO,EAAE,kBAAkB;MAC3BC,IAAI,EAAE,0BAA0B;MAChCC,MAAM,EAAE;IACV;EACF,CAAC;EAEDI,WAAW,EAAE;IACXC,IAAI,EAAE,OAAO;IACbC,KAAK,EAAE;MACLR,OAAO,EAAE,YAAY;MACrBC,IAAI,EAAE,kBAAkB;MACxBC,MAAM,EAAE;IACV;EACF,CAAC;EAEDO,gBAAgB,EAAE;IAChBV,GAAG,EAAE;MACHC,OAAO,EAAE,mBAAmB;MAC5BC,IAAI,EAAE,yBAAyB;MAC/BC,MAAM,EAAE;IACV,CAAC;IACDC,GAAG,EAAE;MACHH,OAAO,EAAE,2BAA2B;MACpCC,IAAI,EAAE,kCAAkC;MACxCC,MAAM,EAAE;IACV,CAAC;IACDE,IAAI,EAAE;MACJJ,OAAO,EAAE,0BAA0B;MACnCC,IAAI,EAAE,kCAAkC;MACxCC,MAAM,EAAE;IACV;EACF,CAAC;EAEDQ,QAAQ,EAAE;IACRX,GAAG,EAAE;MACHC,OAAO,EAAE,UAAU;MACnBC,IAAI,EAAE,gBAAgB;MACtBC,MAAM,EAAE;IACV,CAAC;IACDC,GAAG,EAAE;MACHH,OAAO,EAAE,kBAAkB;MAC3BC,IAAI,EAAE,yBAAyB;MAC/BC,MAAM,EAAE;IACV,CAAC;IACDE,IAAI,EAAE;MACJJ,OAAO,EAAE,iBAAiB;MAC1BC,IAAI,EAAE,yBAAyB;MAC/BC,MAAM,EAAE;IACV;EACF,CAAC;EAEDS,WAAW,EAAE;IACXZ,GAAG,EAAE;MACHC,OAAO,EAAE,oBAAoB;MAC7BC,IAAI,EAAE,0BAA0B;MAChCC,MAAM,EAAE;IACV,CAAC;IACDC,GAAG,EAAE;MACHH,OAAO,EAAE,4BAA4B;MACrCC,IAAI,EAAE,mCAAmC;MACzCC,MAAM,EAAE;IACV,CAAC;IACDE,IAAI,EAAE;MACJJ,OAAO,EAAE,2BAA2B;MACpCC,IAAI,EAAE,mCAAmC;MACzCC,MAAM,EAAE;IACV;EACF,CAAC;EAEDU,MAAM,EAAE;IACNb,GAAG,EAAE;MACHC,OAAO,EAAE,UAAU;MACnBC,IAAI,EAAE,gBAAgB;MACtBC,MAAM,EAAE;IACV,CAAC;IACDC,GAAG,EAAE;MACHH,OAAO,EAAE,kBAAkB;MAC3BC,IAAI,EAAE,yBAAyB;MAC/BC,MAAM,EAAE;IACV,CAAC;IACDE,IAAI,EAAE;MACJJ,OAAO,EAAE,iBAAiB;MAC1BC,IAAI,EAAE,yBAAyB;MAC/BC,MAAM,EAAE;IACV;EACF,CAAC;EAEDW,KAAK,EAAE;IACLd,GAAG,EAAE;MACHC,OAAO,EAAE,OAAO;MAChBC,IAAI,EAAE,aAAa;MACnBC,MAAM,EAAE;IACV,CAAC;IACDC,GAAG,EAAE;MACHH,OAAO,EAAE,eAAe;MACxBC,IAAI,EAAE,oBAAoB;MAC1BC,MAAM,EAAE;IACV,CAAC;IACDE,IAAI,EAAE;MACJJ,OAAO,EAAE,eAAe;MACxBC,IAAI,EAAE,oBAAoB;MAC1BC,MAAM,EAAE;IACV;EACF,CAAC;EAEDY,WAAW,EAAE;IACXf,GAAG,EAAE;MACHC,OAAO,EAAE,mBAAmB;MAC5BC,IAAI,EAAE,yBAAyB;MAC/BC,MAAM,EAAE;IACV,CAAC;IAEDC,GAAG,EAAE;MACHH,OAAO,EAAE,2BAA2B;MACpCC,IAAI,EAAE,gCAAgC;MACtCC,MAAM,EAAE;IACV,CAAC;IAEDE,IAAI,EAAE;MACJJ,OAAO,EAAE,2BAA2B;MACpCC,IAAI,EAAE,gCAAgC;MACtCC,MAAM,EAAE;IACV;EACF,CAAC;EAEDa,MAAM,EAAE;IACNhB,GAAG,EAAE;MACHC,OAAO,EAAE,SAAS;MAClBC,IAAI,EAAE,eAAe;MACrBC,MAAM,EAAE;IACV,CAAC;IAEDC,GAAG,EAAE;MACHH,OAAO,EAAE,iBAAiB;MAC1BC,IAAI,EAAE,sBAAsB;MAC5BC,MAAM,EAAE;IACV,CAAC;IAEDE,IAAI,EAAE;MACJJ,OAAO,EAAE,iBAAiB;MAC1BC,IAAI,EAAE,sBAAsB;MAC5BC,MAAM,EAAE;IACV;EACF,CAAC;EAEDc,YAAY,EAAE;IACZjB,GAAG,EAAE;MACHC,OAAO,EAAE,mBAAmB;MAC5BC,IAAI,EAAE,0BAA0B;MAChCC,MAAM,EAAE;IACV,CAAC;IAEDC,GAAG,EAAE;MACHH,OAAO,EAAE,4BAA4B;MACrCC,IAAI,EAAE,iCAAiC;MACvCC,MAAM,EAAE;IACV,CAAC;IAEDE,IAAI,EAAE;MACJJ,OAAO,EAAE,4BAA4B;MACrCC,IAAI,EAAE,iCAAiC;MACvCC,MAAM,EAAE;IACV;EACF,CAAC;EAEDe,OAAO,EAAE;IACPlB,GAAG,EAAE;MACHC,OAAO,EAAE,SAAS;MAClBC,IAAI,EAAE,gBAAgB;MACtBC,MAAM,EAAE;IACV,CAAC;IAEDC,GAAG,EAAE;MACHH,OAAO,EAAE,kBAAkB;MAC3BC,IAAI,EAAE,uBAAuB;MAC7BC,MAAM,EAAE;IACV,CAAC;IAEDE,IAAI,EAAE;MACJJ,OAAO,EAAE,kBAAkB;MAC3BC,IAAI,EAAE,uBAAuB;MAC7BC,MAAM,EAAE;IACV;EACF,CAAC;EAEDgB,WAAW,EAAE;IACXnB,GAAG,EAAE;MACHC,OAAO,EAAE,iBAAiB;MAC1BC,IAAI,EAAE,wBAAwB;MAC9BC,MAAM,EAAE;IACV,CAAC;IACDC,GAAG,EAAE;MACHH,OAAO,EAAE,0BAA0B;MACnCC,IAAI,EAAE,+BAA+B;MACrCC,MAAM,EAAE;IACV,CAAC;IACDE,IAAI,EAAE;MACJJ,OAAO,EAAE,0BAA0B;MACnCC,IAAI,EAAE,+BAA+B;MACrCC,MAAM,EAAE;IACV;EACF,CAAC;EAEDiB,MAAM,EAAE;IACNpB,GAAG,EAAE;MACHC,OAAO,EAAE,OAAO;MAChBC,IAAI,EAAE,cAAc;MACpBC,MAAM,EAAE;IACV,CAAC;IACDC,GAAG,EAAE;MACHH,OAAO,EAAE,gBAAgB;MACzBC,IAAI,EAAE,qBAAqB;MAC3BC,MAAM,EAAE;IACV,CAAC;IACDE,IAAI,EAAE;MACJJ,OAAO,EAAE,gBAAgB;MACzBC,IAAI,EAAE,qBAAqB;MAC3BC,MAAM,EAAE;IACV;EACF,CAAC;EAEDkB,UAAU,EAAE;IACVrB,GAAG,EAAE;MACHC,OAAO,EAAE,gBAAgB;MACzBC,IAAI,EAAE,uBAAuB;MAC7BC,MAAM,EAAE;IACV,CAAC;IACDC,GAAG,EAAE;MACHH,OAAO,EAAE,yBAAyB;MAClCC,IAAI,EAAE,8BAA8B;MACpCC,MAAM,EAAE;IACV,CAAC;IACDE,IAAI,EAAE;MACJJ,OAAO,EAAE,yBAAyB;MAClCC,IAAI,EAAE,8BAA8B;MACpCC,MAAM,EAAE;IACV;EACF,CAAC;EAEDmB,YAAY,EAAE;IACZtB,GAAG,EAAE;MACHC,OAAO,EAAE,aAAa;MACtBC,IAAI,EAAE,oBAAoB;MAC1BC,MAAM,EAAE;IACV,CAAC;IACDC,GAAG,EAAE;MACHH,OAAO,EAAE,sBAAsB;MAC/BC,IAAI,EAAE,2BAA2B;MACjCC,MAAM,EAAE;IACV,CAAC;IACDE,IAAI,EAAE;MACJJ,OAAO,EAAE,sBAAsB;MAC/BC,IAAI,EAAE,2BAA2B;MACjCC,MAAM,EAAE;IACV;EACF;AACF,CAAC;AAED,OAAO,MAAMoB,cAAc,GAAGA,CAACC,KAAK,EAAEC,KAAK,EAAEC,OAAO,KAAK;EACvD,IAAIC,YAAY;EAEhB,MAAMC,UAAU,GAAG9B,oBAAoB,CAAC0B,KAAK,CAAC;;EAE9C;EACA,IAAII,UAAU,CAACpB,IAAI,KAAK,OAAO,EAAE;IAC/BmB,YAAY,GAAGC,UAAU,CAACnB,KAAK;EACjC,CAAC,MAAM,IAAIgB,KAAK,KAAK,CAAC,EAAE;IACtBE,YAAY,GAAGC,UAAU,CAAC5B,GAAG;EAC/B,CAAC,MAAM,IAAIyB,KAAK,GAAG,CAAC,IAAIA,KAAK,GAAG,CAAC,EAAE;IACjCE,YAAY,GAAGC,UAAU,CAACxB,GAAG;EAC/B,CAAC,MAAM;IACLuB,YAAY,GAAGC,UAAU,CAACvB,IAAI;EAChC;;EAEA;EACA,MAAMwB,WAAW,GAAG,CAAAH,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEI,SAAS,MAAK,IAAI;EAC/C,MAAMC,UAAU,GAAGL,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEK,UAAU;EAEtC,IAAIC,UAAU;EACd,IAAIH,WAAW,IAAIE,UAAU,KAAK,CAAC,CAAC,EAAE;IACpCC,UAAU,GAAGL,YAAY,CAACzB,IAAI;EAChC,CAAC,MAAM,IAAI2B,WAAW,IAAIE,UAAU,KAAK,CAAC,EAAE;IAC1CC,UAAU,GAAGL,YAAY,CAACxB,MAAM;EAClC,CAAC,MAAM;IACL6B,UAAU,GAAGL,YAAY,CAAC1B,OAAO;EACnC;EAEA,OAAO+B,UAAU,CAACC,OAAO,CAAC,WAAW,EAAEC,MAAM,CAACT,KAAK,CAAC,CAAC;AACvD,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
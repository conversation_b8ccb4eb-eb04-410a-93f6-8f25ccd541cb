using System;

namespace MyApi.Models
{
    /// <summary>
    /// 備份檔案資訊 DTO
    /// </summary>
    public class BackupFileDto
    {
        /// <summary>
        /// 檔案名稱
        /// </summary>
        public string FileName { get; set; } = string.Empty;

        /// <summary>
        /// 檔案大小（MB）
        /// </summary>
        public double SizeMB { get; set; }

        /// <summary>
        /// 最後修改時間
        /// </summary>
        public DateTime LastModified { get; set; }
    }

    /// <summary>
    /// 備份狀態 DTO
    /// </summary>
    public class BackupStatusDto
    {
        /// <summary>
        /// 今日是否已備份
        /// </summary>
        public bool HasTodayBackup { get; set; }

        /// <summary>
        /// 今日備份檔案名稱（如果存在）
        /// </summary>
        public string? TodayBackupFile { get; set; }

        /// <summary>
        /// 備份檔案總數
        /// </summary>
        public int TotalBackupFiles { get; set; }

        /// <summary>
        /// 最新備份時間
        /// </summary>
        public DateTime? LatestBackupTime { get; set; }
    }
}

{"ast": null, "code": "const formatRelativeLocale = {\n  lastWeek: \"'أخر' eeee 'عند' p\",\n  yesterday: \"'أمس عند' p\",\n  today: \"'اليوم عند' p\",\n  tomorrow: \"'غداً عند' p\",\n  nextWeek: \"eeee 'عند' p\",\n  other: \"P\"\n};\nexport const formatRelative = (token, _date, _baseDate, _options) => {\n  return formatRelativeLocale[token];\n};", "map": {"version": 3, "names": ["formatRelativeLocale", "lastWeek", "yesterday", "today", "tomorrow", "nextWeek", "other", "formatRelative", "token", "_date", "_baseDate", "_options"], "sources": ["C:/Users/<USER>/Desktop/demo-git/demo-react/my-fullstack-app/frontend/node_modules/date-fns/locale/ar-MA/_lib/formatRelative.js"], "sourcesContent": ["const formatRelativeLocale = {\n  lastWeek: \"'أخر' eeee 'عند' p\",\n  yesterday: \"'أمس عند' p\",\n  today: \"'اليوم عند' p\",\n  tomorrow: \"'غداً عند' p\",\n  nextWeek: \"eeee 'عند' p\",\n  other: \"P\",\n};\n\nexport const formatRelative = (token, _date, _baseDate, _options) => {\n  return formatRelativeLocale[token];\n};\n"], "mappings": "AAAA,MAAMA,oBAAoB,GAAG;EAC3BC,QAAQ,EAAE,oBAAoB;EAC9BC,SAAS,EAAE,aAAa;EACxBC,KAAK,EAAE,eAAe;EACtBC,QAAQ,EAAE,cAAc;EACxBC,QAAQ,EAAE,cAAc;EACxBC,KAAK,EAAE;AACT,CAAC;AAED,OAAO,MAAMC,cAAc,GAAGA,CAACC,KAAK,EAAEC,KAAK,EAAEC,SAAS,EAAEC,QAAQ,KAAK;EACnE,OAAOX,oBAAoB,CAACQ,KAAK,CAAC;AACpC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"name": "@types/connect-history-api-fallback", "version": "1.5.4", "description": "TypeScript definitions for connect-history-api-fallback", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/connect-history-api-fallback", "license": "MIT", "contributors": [{"name": "<PERSON>", "githubUsername": "douglasduteil", "url": "https://github.com/douglasduteil"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/connect-history-api-fallback"}, "scripts": {}, "dependencies": {"@types/express-serve-static-core": "*", "@types/node": "*"}, "typesPublisherContentHash": "d808766d9d2861db4ad548a99ff2bf4e44af08540b5df45324c6cd31964c1a1f", "typeScriptVersion": "4.5"}
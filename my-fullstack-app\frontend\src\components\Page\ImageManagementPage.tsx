import React, { useState, useRef, useEffect } from 'react';
import { Button } from 'primereact/button';
import { Column } from 'primereact/column';
import { DataTable } from 'primereact/datatable';
import { Dialog } from 'primereact/dialog';
import { Toast } from 'primereact/toast';
import { ConfirmDialog, confirmDialog } from 'primereact/confirmdialog';
import { Card } from 'primereact/card';
import { ProgressSpinner } from 'primereact/progressspinner';
import { Tag } from 'primereact/tag';
import { Image } from 'primereact/image';
import { InputText } from 'primereact/inputtext';
import { Calendar } from 'primereact/calendar';
import { formatUtcToTaipei } from "../../utils/dateUtils";
import api from '../../services/api';
import { log } from '../../utils/logger';

interface ImageFile {
  fileName: string;
  filePath: string;
  fileSize: number;
  createdDate: string;
  modifiedDate: string;
  imageUrl: string;
}

const ImageManagementPage: React.FC = () => {
  const toast = useRef<Toast>(null);
  const [images, setImages] = useState<ImageFile[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [selectedImage, setSelectedImage] = useState<ImageFile | null>(null);
  const [showPreviewDialog, setShowPreviewDialog] = useState(false);
  const [fileNameFilter, setFileNameFilter] = useState('');
  const [startDateFilter, setStartDateFilter] = useState<Date | null>(null);
  const [endDateFilter, setEndDateFilter] = useState<Date | null>(null);

  // 載入圖片列表
  const loadImages = async () => {
    try {
      setRefreshing(true);
      log.api('載入圖片列表');

      const params = {
        fileName: fileNameFilter,
        startDate: startDateFilter ? startDateFilter.toISOString() : undefined,
        endDate: endDateFilter ? endDateFilter.toISOString() : undefined,
      };

      const response = await api.get('/api/file/GetImageFiles', { params });
      setImages(response.data);

      log.api('圖片列表載入成功', { count: response.data.length });

    } catch (error: any) {
      log.error('載入圖片列表失敗', error);
      toast.current?.show({
        severity: 'error',
        summary: '載入失敗',
        detail: '無法載入圖片列表',
        life: 5000
      });
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  const handleSearch = () => {
    loadImages();
  };

  const handleReset = () => {
    setFileNameFilter('');
    setStartDateFilter(null);
    setEndDateFilter(null);
    loadImages();
  };

  // 刪除圖片
  const deleteImage = async (image: ImageFile) => {
    try {
      log.api('刪除圖片', { fileName: image.fileName });

      await api.delete(`/api/file/DeleteImageFile`, {
        params: { fileName: image.fileName }
      });

      toast.current?.show({
        severity: 'success',
        summary: '刪除成功',
        detail: `圖片 ${image.fileName} 已刪除`,
        life: 3000
      });

      // 重新載入列表
      loadImages();

    } catch (error: any) {
      log.error('刪除圖片失敗', error);
      toast.current?.show({
        severity: 'error',
        summary: '刪除失敗',
        detail: error.response?.data?.message || '刪除圖片失敗',
        life: 5000
      });
    }
  };

  // 下載圖片
  const downloadImage = async (image: ImageFile) => {
    try {
      log.api('下載圖片', { fileName: image.fileName });

      const response = await api.get(`/api/file/DownloadImageFile`, {
        params: { fileName: image.fileName },
        responseType: 'blob'
      });

      // 創建下載連結
      const blob = new Blob([response.data]);
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = image.fileName;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);

      toast.current?.show({
        severity: 'success',
        summary: '下載成功',
        detail: `圖片 ${image.fileName} 下載完成`,
        life: 3000
      });

    } catch (error: any) {
      log.error('下載圖片失敗', error);
      toast.current?.show({
        severity: 'error',
        summary: '下載失敗',
        detail: '下載圖片失敗',
        life: 5000
      });
    }
  };

  // 預覽圖片
  const previewImage = (image: ImageFile) => {
    setSelectedImage(image);
    setShowPreviewDialog(true);
  };

  // 確認刪除
  const confirmDelete = (image: ImageFile) => {
    confirmDialog({
      message: `確定要刪除圖片 "${image.fileName}" 嗎？此操作無法復原。`,
      header: '確認刪除',
      icon: 'pi pi-exclamation-triangle',
      acceptLabel: '確定',
      rejectLabel: '取消',
      accept: () => deleteImage(image),
    });
  };

  // 格式化檔案大小
  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  // 格式化日期
  const formatDate = (dateString: string): string => {
    if (!dateString) return '';
    try {
      return formatUtcToTaipei(dateString, 'yyyy/MM/dd HH:mm:ss');
    } catch (error) {
      console.error('Error formatting date:', error);
      return dateString; // or return a default/error indicator
    }
  };

  // 縮圖模板
  const thumbnailBodyTemplate = (rowData: ImageFile) => {
    return (
      <Image
        src={rowData.imageUrl}
        alt={rowData.fileName}
        width="60"
        height="60"
        className="border-round"
        preview={false}
        onError={(e) => {
          // 如果圖片載入失敗，顯示預設圖示
          (e.target as HTMLImageElement).src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjAiIGhlaWdodD0iNjAiIHZpZXdCb3g9IjAgMCA2MCA2MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjYwIiBoZWlnaHQ9IjYwIiBmaWxsPSIjRjVGNUY1Ii8+CjxwYXRoIGQ9Ik0zMCAyMEM0MS4wNDU3IDIwIDUwIDI4Ljk1NDMgNTAgNDBDNTAgNTEuMDQ1NyA0MS4wNDU3IDYwIDMwIDYwQzE4Ljk1NDMgNjAgMTAgNTEuMDQ1NyAxMCA0MEMxMCAyOC45NTQzIDE4Ljk1NDMgMjAgMzAgMjBaIiBmaWxsPSIjRTBFMEUwIi8+CjxwYXRoIGQ9Ik0zMCAyNUM0MS4wNDU3IDI1IDUwIDMzLjk1NDMgNTAgNDVDNTAgNTYuMDQ1NyA0MS4wNDU3IDY1IDMwIDY1QzE4Ljk1NDMgNjUgMTAgNTYuMDQ1NyAxMCA0NUMxMCAzMy45NTQzIDE4Ljk1NDMgMjUgMzAgMjVaIiBmaWxsPSIjQ0NDQ0NDIi8+CjwvZz4KPC9zdmc+';
        }}
      />
    );
  };

  // 檔案大小模板
  const fileSizeBodyTemplate = (rowData: ImageFile) => {
    return formatFileSize(rowData.fileSize);
  };

  // 建立日期模板
  const createdDateBodyTemplate = (rowData: ImageFile) => {
    return formatDate(rowData.createdDate);
  };

  // 修改日期模板
  const modifiedDateBodyTemplate = (rowData: ImageFile) => {
    return formatDate(rowData.modifiedDate);
  };

  // 檔案類型模板
  const fileTypeBodyTemplate = (rowData: ImageFile) => {
    const extension = rowData.fileName.split('.').pop()?.toUpperCase();
    let severity: "success" | "info" | "warning" | "danger" = 'info';

    switch (extension) {
      case 'JPG':
      case 'JPEG':
        severity = 'success';
        break;
      case 'PNG':
        severity = 'info';
        break;
      case 'GIF':
        severity = 'warning';
        break;
      case 'WEBP':
        severity = 'danger';
        break;
      default:
        severity = 'warning';
    }

    return <Tag value={extension} severity={severity} />;
  };

  // 操作按鈕模板
  const actionBodyTemplate = (rowData: ImageFile) => {
    return (
      <div className="flex gap-2">
        <Button
          icon="pi pi-eye"
          className="p-button-info p-button-sm"
          onClick={() => previewImage(rowData)}
          tooltip="預覽"
          tooltipOptions={{ position: 'top' }}
        />
        <Button
          icon="pi pi-download"
          className="p-button-success p-button-sm"
          onClick={() => downloadImage(rowData)}
          tooltip="下載"
          tooltipOptions={{ position: 'top' }}
        />
        <Button
          icon="pi pi-trash"
          className="p-button-danger p-button-sm"
          onClick={() => confirmDelete(rowData)}
          tooltip="刪除"
          tooltipOptions={{ position: 'top' }}
        />
      </div>
    );
  };

  // 分頁器左側
  const paginatorLeft = (
    <Button
      type="button"
      icon="pi pi-refresh"
      text
      onClick={loadImages}
      disabled={refreshing}
    />
  );

  const paginatorRight = <div></div>;

  useEffect(() => {
    loadImages();
  }, []);

  if (loading) {
    return (
      <div className="flex justify-content-center align-items-center" style={{ height: '400px' }}>
        <ProgressSpinner />
      </div>
    );
  }

  return (
    <div>
      <Toast ref={toast} />
      <ConfirmDialog />

      <Card title="圖片管理" className="mb-4">
        <p className="text-600 line-height-3 m-0">
          管理系統上傳的圖片檔案，包括治療記錄圖片、病患資料圖片等。您可以預覽、下載或刪除不需要的圖片檔案。
        </p>
      </Card>

      {/* 搜尋條件 */}
      <Card className="mb-4">
        <div className="grid">
          <div className="col-12 md:col-3">
            <InputText
              id="fileNameFilter"
              value={fileNameFilter}
              onChange={(e) => setFileNameFilter(e.target.value)}
              placeholder="依檔名搜尋"
              className="w-full"
            />
          </div>
          <div className="col-6 md:col-3">
            <Calendar
                id="startDateFilter"
                value={startDateFilter}
                onChange={(e) => setStartDateFilter(e.value as Date)}
                placeholder="選擇結束日期"
                className="w-full"
                showIcon
                dateFormat="yy/mm/dd"
              />
          </div>
          <div className="col-6 md:col-3">
              <Calendar
                id="endDateFilter"
                value={endDateFilter}
                onChange={(e) => setEndDateFilter(e.value as Date)}
                placeholder="選擇結束日期"
                className="w-full"
                showIcon
                dateFormat="yy/mm/dd"
              />
          </div>
          <div className="col-12 md:col-4">
            <div className="flex gap-2">
              <Button 
                label="搜尋" 
                icon="pi pi-search" 
                onClick={handleSearch} 
                />
              <Button 
                label="重設" 
                icon="pi pi-undo" 
                onClick={handleReset} 
                className="p-button-secondary" />
            </div>
          </div>
        </div>
      </Card>

      <Card>
        <DataTable
          value={images}
          paginator
          rows={20}
          rowsPerPageOptions={[10, 20, 50]}
          emptyMessage="沒有找到圖片檔案"
          tableStyle={{ minWidth: '50rem' }}
          paginatorLeft={paginatorLeft}
          paginatorRight={paginatorRight}
          loading={refreshing}
        >
          <Column header="縮圖" body={thumbnailBodyTemplate} style={{ width: '10%' }} />
          <Column field="fileName" header="檔案名稱" sortable style={{ width: '25%' }} />
          <Column field="fileType" header="類型" body={fileTypeBodyTemplate} style={{ width: '10%' }} />
          <Column field="fileSize" header="檔案大小" body={fileSizeBodyTemplate} sortable style={{ width: '15%' }} />
          <Column field="createdDate" header="建立日期" body={createdDateBodyTemplate} sortable style={{ width: '20%' }} />
          <Column field="modifiedDate" header="修改日期" body={modifiedDateBodyTemplate} sortable style={{ width: '20%' }} />
          <Column header="操作" body={actionBodyTemplate} style={{ width: '15%' }} />
        </DataTable>
      </Card>

      {/* 圖片預覽對話框 */}
      <Dialog
        header={`預覽圖片 - ${selectedImage?.fileName}`}
        visible={showPreviewDialog}
        style={{ width: '80vw', maxWidth: '800px' }}
        onHide={() => setShowPreviewDialog(false)}
        modal
      >
        {selectedImage && (
          <div className="text-center">
            <Image
              src={selectedImage.imageUrl}
              alt={selectedImage.fileName}
              className="max-w-full max-h-30rem"
              preview
            />
            <div className="mt-3 text-sm text-600">
              <p>檔案名稱: {selectedImage.fileName}</p>
              <p>檔案大小: {formatFileSize(selectedImage.fileSize)}</p>
              <p>建立日期: {formatDate(selectedImage.createdDate)}</p>
            </div>
          </div>
        )}
      </Dialog>
    </div>
  );
};

export default ImageManagementPage;

{"ast": null, "code": "import _objectSpread from\"C:/Users/<USER>/Desktop/demo-git/demo-react/my-fullstack-app/frontend/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";import{formatUtcToTaipei}from'../../utils/dateUtils';import dayGridPlugin from'@fullcalendar/daygrid';import interactionPlugin from'@fullcalendar/interaction';import FullCalendar from'@fullcalendar/react';import timeGridPlugin from'@fullcalendar/timegrid';import{Button}from'primereact/button';import{Calendar}from'primereact/calendar';import{ConfirmDialog,confirmDialog}from'primereact/confirmdialog';import{Dialog}from'primereact/dialog';import{Dropdown}from'primereact/dropdown';import{InputText}from'primereact/inputtext';import{Toast}from'primereact/toast';import{Checkbox}from'primereact/checkbox';import{InputNumber}from'primereact/inputnumber';import React,{useEffect,useRef,useState}from'react';import{useNavigate}from'react-router-dom';import{ROUTES}from'../../constants/routes';import api from'../../services/api';import{jsx as _jsx,jsxs as _jsxs,Fragment as _Fragment}from\"react/jsx-runtime\";const SchedulesPage=()=>{const toast=useRef(null);const navigate=useNavigate();const[events,setEvents]=useState([]);const[showDialog,setShowDialog]=useState(false);const[dialogMode,setDialogMode]=useState('add');const[selectedEvent,setSelectedEvent]=useState(null);const[doctors,setDoctors]=useState([]);const[patients,setPatients]=useState([]);const[isLoading,setIsLoading]=useState(false);const[formData,setFormData]=useState({doctorId:null,patientId:null,startDate:null,endDate:null,startTime:'09:00',endTime:'10:00',isRepeat:false,repeatType:'daily',repeatCount:1});// 載入所有數據\nuseEffect(()=>{const loadInitialData=async()=>{try{var _toast$current;console.log('開始載入數據...');// 並行載入治療師、病患和行程數據\nconst[doctorsResponse,patientsResponse,schedulesResponse]=await Promise.all([api.get('/api/users/DoctorList'),api.get('/api/patients/PatientList'),api.get('/api/schedule')]);console.log('治療師數據:',doctorsResponse.data);console.log('病患數據:',patientsResponse.data);console.log('行程數據:',schedulesResponse.data);const doctorsData=doctorsResponse.data;const patientsData=patientsResponse.data;const schedulesData=schedulesResponse.data;setDoctors(doctorsData);setPatients(patientsData);// 轉換後端數據格式為前端 CalendarEvent 格式\nconst formattedEvents=schedulesData.map(schedule=>({id:schedule.id,title:schedule.title,start:schedule.start,end:schedule.end,backgroundColor:schedule.backgroundColor||'#3788d8',borderColor:schedule.borderColor,doctorId:schedule.doctorId,patientId:schedule.patientId,doctorName:schedule.doctorName,patientName:schedule.patientName}));console.log('格式化後的事件:',formattedEvents);setEvents(formattedEvents);(_toast$current=toast.current)===null||_toast$current===void 0?void 0:_toast$current.show({severity:'success',summary:'成功',detail:'數據載入完成'});}catch(error){var _toast$current2;console.error('載入數據失敗:',error);(_toast$current2=toast.current)===null||_toast$current2===void 0?void 0:_toast$current2.show({severity:'error',summary:'錯誤',detail:'載入數據失敗'});}};loadInitialData();},[]);// 重新載入行程數據\nconst reloadSchedules=async()=>{try{var _toast$current3;setIsLoading(true);const response=await api.get('/api/schedule');const schedulesData=response.data;const formattedEvents=schedulesData.map(schedule=>({id:schedule.id,title:schedule.title,start:schedule.start,end:schedule.end,backgroundColor:schedule.backgroundColor||'#3788d8',borderColor:schedule.borderColor,doctorId:schedule.doctorId,patientId:schedule.patientId,treatmentId:schedule.treatmentId,doctorName:schedule.doctorName,patientName:schedule.patientName,description:schedule.description}));setEvents(formattedEvents);(_toast$current3=toast.current)===null||_toast$current3===void 0?void 0:_toast$current3.show({severity:'success',summary:'成功',detail:'行程數據已重新載入'});}catch(error){var _toast$current4;console.error('重新載入行程失敗:',error);(_toast$current4=toast.current)===null||_toast$current4===void 0?void 0:_toast$current4.show({severity:'error',summary:'錯誤',detail:'重新載入行程失敗'});}finally{setIsLoading(false);}};const handleInputChange=(field,value)=>{setFormData(prev=>_objectSpread(_objectSpread({},prev),{},{[field]:value}));};const handleAddEvent=async()=>{if(!formData.doctorId||!formData.patientId||!formData.startDate||!formData.endDate){var _toast$current5;(_toast$current5=toast.current)===null||_toast$current5===void 0?void 0:_toast$current5.show({severity:'warn',summary:'警告',detail:'請填寫所有必要欄位'});return;}try{const startDateTime=new Date(formData.startDate);const endDateTime=new Date(formData.endDate);// 設置時間\nconst[startHour,startMinute]=formData.startTime.split(':');const[endHour,endMinute]=formData.endTime.split(':');startDateTime.setHours(parseInt(startHour),parseInt(startMinute));endDateTime.setHours(parseInt(endHour),parseInt(endMinute));// 轉換重複類型\nconst getRepeatTypeValue=type=>{switch(type){case'daily':return 1;case'weekly':return 2;case'monthly':return 3;default:return 0;}};const scheduleData={doctorId:formData.doctorId,patientId:formData.patientId,startDateTime:startDateTime.toISOString(),endDateTime:endDateTime.toISOString(),description:'',// 可以後續添加描述欄位\nbackgroundColor:'#3788d8',repeatType:formData.isRepeat?getRepeatTypeValue(formData.repeatType):0,repeatCount:formData.isRepeat?formData.repeatCount:1};if(dialogMode==='edit'&&selectedEvent&&selectedEvent.id){var _toast$current6;// 編輯模式 - 調用 PUT API\nconst updateData=_objectSpread(_objectSpread({},scheduleData),{},{id:parseInt(selectedEvent.id)});await api.put(\"/api/schedule/\".concat(selectedEvent.id),updateData);// 重新載入所有行程數據以確保同步\nawait reloadSchedules();(_toast$current6=toast.current)===null||_toast$current6===void 0?void 0:_toast$current6.show({severity:'success',summary:'成功',detail:'行程已更新'});}else{var _toast$current7;// 新增模式 - 調用 POST API\nawait api.post('/api/schedule',scheduleData);// 重新載入所有行程數據以確保同步\nawait reloadSchedules();(_toast$current7=toast.current)===null||_toast$current7===void 0?void 0:_toast$current7.show({severity:'success',summary:'成功',detail:'行程已新增'});}setShowDialog(false);resetForm();}catch(error){var _toast$current8,_error$response,_error$response$data;console.error('操作失敗:',error);(_toast$current8=toast.current)===null||_toast$current8===void 0?void 0:_toast$current8.show({severity:'error',summary:'錯誤',detail:((_error$response=error.response)===null||_error$response===void 0?void 0:(_error$response$data=_error$response.data)===null||_error$response$data===void 0?void 0:_error$response$data.message)||'操作失敗'});}};const resetForm=()=>{setFormData({doctorId:null,patientId:null,startDate:null,endDate:null,startTime:'09:00',endTime:'10:00',isRepeat:false,repeatType:'daily',repeatCount:1});setSelectedEvent(null);setDialogMode('add');};const handleEventClick=clickInfo=>{const eventData=events.find(event=>event.id===clickInfo.event.id);if(eventData){setSelectedEvent(eventData);setDialogMode('view');// 填充表單數據\nsetFormData({doctorId:eventData.doctorId||null,patientId:eventData.patientId||null,startDate:new Date(eventData.start),endDate:new Date(eventData.end),startTime:formatUtcToTaipei(eventData.start,'HH:mm'),endTime:formatUtcToTaipei(eventData.end,'HH:mm'),isRepeat:false,repeatType:'daily',repeatCount:1});setShowDialog(true);}};const handleEdit=()=>{setDialogMode('edit');};const handleDelete=()=>{if(!selectedEvent)return;confirmDialog({message:\"\\u78BA\\u5B9A\\u8981\\u522A\\u9664\\u884C\\u7A0B \\\"\".concat(selectedEvent.title,\"\\\" \\u55CE\\uFF1F\"),header:'確認刪除',icon:'pi pi-exclamation-triangle',accept:async()=>{try{var _toast$current9;// 調用後端 DELETE API\nawait api.delete(\"/api/schedule/\".concat(selectedEvent.id));// 重新載入所有行程數據以確保同步\nawait reloadSchedules();setShowDialog(false);resetForm();(_toast$current9=toast.current)===null||_toast$current9===void 0?void 0:_toast$current9.show({severity:'success',summary:'成功',detail:'行程已刪除'});}catch(error){var _error$response2,_error$response2$data,_toast$current0;console.error('刪除失敗:',error);var detail=error.status===403?\"您無權限，請通知管理員\":((_error$response2=error.response)===null||_error$response2===void 0?void 0:(_error$response2$data=_error$response2.data)===null||_error$response2$data===void 0?void 0:_error$response2$data.message)||'刪除失敗';(_toast$current0=toast.current)===null||_toast$current0===void 0?void 0:_toast$current0.show({severity:'error',summary:'錯誤',detail:detail});}}});};const handleOpenCase=async()=>{if(!selectedEvent||!selectedEvent.patientId)return;try{let treatmentId=selectedEvent.treatmentId;if(!treatmentId){// 如果沒有 TreatmentId，創建新的治療案件\nconsole.log('創建新的治療案件');const createResponse=await api.post('/api/treatment/Insert',{PatientId:selectedEvent.patientId,DoctorId:selectedEvent.doctorId// 其他必要的初始數據\n});treatmentId=createResponse.data.treatmentId;console.log('新治療案件創建成功',{treatmentId});// 更新 Schedule 的 TreatmentId\nawait api.patch(\"/api/schedule/\".concat(selectedEvent.id,\"/treatment\"),{TreatmentId:treatmentId});console.log('Schedule TreatmentId 更新成功');// 重新載入行程數據\nreloadSchedules();}// 獲取完整的治療數據\nconsole.log('獲取治療數據',{treatmentId});const treatmentResponse=await api.get('/api/treatment',{params:{treatmentsId:treatmentId}});const treatmentData=treatmentResponse.data;// 跳轉到 TreatmentsDetailPage\nif(treatmentId){navigate(\"\".concat(ROUTES.TREATMENT_DETAIL,\"?id=\").concat(treatmentId),{state:{treatment:treatmentData,patient:{id:treatmentData.patientId}}});}}catch(error){var _toast$current1;console.error('開案失敗',error);(_toast$current1=toast.current)===null||_toast$current1===void 0?void 0:_toast$current1.show({severity:'error',summary:'開案失敗',detail:'無法開啟治療案件，請稍後再試',life:3000});}};const handleSaveEdit=()=>{confirmDialog({message:'確定要保存修改嗎？',header:'確認編輯',icon:'pi pi-question-circle',accept:()=>{handleAddEvent();// 重用新增邏輯，但會根據 dialogMode 判斷是編輯還是新增\n}});};return/*#__PURE__*/_jsxs(\"div\",{className:\"schedules-page\",children:[/*#__PURE__*/_jsx(Toast,{ref:toast}),/*#__PURE__*/_jsx(ConfirmDialog,{}),/*#__PURE__*/_jsx(\"div\",{className:\"card\",hidden:true,children:/*#__PURE__*/_jsx(\"div\",{className:\"flex pb-3 justify-content-end align-items-center\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex gap-2\",children:[/*#__PURE__*/_jsx(Button,{label:\"\\u91CD\\u65B0\\u8F09\\u5165\",icon:isLoading?\"pi pi-spin pi-spinner\":\"pi pi-refresh\",onClick:reloadSchedules,className:\"p-button-secondary\",outlined:true,disabled:isLoading}),/*#__PURE__*/_jsx(Button,{label:\"\\u65B0\\u589E\\u884C\\u7A0B\",icon:\"pi pi-plus\",onClick:()=>setShowDialog(true),className:\"p-button-primary\"})]})})}),/*#__PURE__*/_jsx(\"div\",{className:\"card\",children:/*#__PURE__*/_jsx(\"div\",{className:\"calendar-container\",children:/*#__PURE__*/_jsx(FullCalendar,{plugins:[dayGridPlugin,timeGridPlugin,interactionPlugin],height:\"auto\",initialView:\"timeGridWeek\",locale:\"zh-tw\",weekends:true,allDaySlot:false,headerToolbar:{left:'prev,next today',center:'title',right:'reloadDate,addDate dayGridMonth,timeGridWeek'},titleFormat:{year:'numeric',// 顯示年份 (例如: 2023)\nmonth:'long'// 顯示完整月份名稱 (例如: 十二月)\n},dayHeaderFormat:{day:'numeric',// 顯示日期數字 (例如: 13)\nweekday:'narrow'// 顯示短格式的星期 (例如: 日, 一, 二)\n},slotLabelFormat:{hour:'2-digit',// 小時顯示為兩位數 (例如: 09)\nminute:'numeric',// 分鐘顯示為兩位數 (例如: 00)\nhour12:false// 禁用 12 小時制，啟用 24 小時制\n},customButtons:{reloadDate:{text:'重整',click:reloadSchedules},addDate:{text:'新增',click:()=>setShowDialog(true)}},slotMinTime:\"09:00:00\",slotMaxTime:\"22:00:00\",events:events,eventClick:handleEventClick,selectable:true,selectMirror:true,dayMaxEvents:true,businessHours:{daysOfWeek:[1,2,3,4,5,6],startTime:'10:00',endTime:'18:00'}})})}),/*#__PURE__*/_jsxs(Dialog,{header:dialogMode==='add'?'新增行程':dialogMode==='edit'?'編輯行程':'行程詳情',visible:showDialog,style:{width:'500px'},onHide:()=>{setShowDialog(false);resetForm();},footer:/*#__PURE__*/_jsx(\"div\",{className:\"flex gap-2\",children:dialogMode==='view'?/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(Button,{label:\"\\u958B\\u6848\",icon:\"pi pi-folder-open\",className:\"p-button-success\",onClick:handleOpenCase}),/*#__PURE__*/_jsx(Button,{label:\"\\u7DE8\\u8F2F\",icon:\"pi pi-pencil\",onClick:handleEdit}),/*#__PURE__*/_jsx(Button,{label:\"\\u522A\\u9664\",icon:\"pi pi-trash\",className:\"p-button-danger\",onClick:handleDelete}),/*#__PURE__*/_jsx(Button,{label:\"\\u95DC\\u9589\",icon:\"pi pi-times\",className:\"p-button-outlined\",onClick:()=>{setShowDialog(false);resetForm();}})]}):/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(Button,{label:\"\\u53D6\\u6D88\",icon:\"pi pi-times\",className:\"p-button-outlined\",onClick:()=>{setShowDialog(false);resetForm();}}),/*#__PURE__*/_jsx(Button,{label:dialogMode==='edit'?'保存':'新增',icon:\"pi pi-check\",onClick:dialogMode==='edit'?handleSaveEdit:handleAddEvent})]})}),children:[/*#__PURE__*/_jsxs(\"div\",{className:\"grid\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"col-6 md:col-6\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"field\",children:[/*#__PURE__*/_jsx(\"label\",{htmlFor:\"doctor\",className:\"font-bold block mb-2\",children:\"\\u6CBB\\u7642\\u5E2B *\"}),/*#__PURE__*/_jsx(Dropdown,{id:\"doctor\",value:formData.doctorId,options:doctors,onChange:e=>handleInputChange('doctorId',e.value),optionLabel:\"Name\",optionValue:\"Id\",placeholder:\"\\u8ACB\\u9078\\u64C7\\u6CBB\\u7642\\u5E2B\",className:\"w-full\",disabled:dialogMode==='view'})]})}),/*#__PURE__*/_jsx(\"div\",{className:\"col-6 md:col-6\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"field\",children:[/*#__PURE__*/_jsx(\"label\",{htmlFor:\"patient\",className:\"font-bold block mb-2\",children:\"\\u75C5\\u60A3 *\"}),/*#__PURE__*/_jsx(Dropdown,{id:\"patient\",value:formData.patientId,options:patients,onChange:e=>handleInputChange('patientId',e.value),optionLabel:\"Name\",optionValue:\"Id\",placeholder:\"\\u8ACB\\u9078\\u64C7\\u75C5\\u60A3\",className:\"w-full\",disabled:dialogMode==='view'})]})}),/*#__PURE__*/_jsx(\"div\",{className:\"col-12 md:col-6\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"field\",children:[/*#__PURE__*/_jsx(\"label\",{htmlFor:\"startDate\",className:\"font-bold block mb-2\",children:\"\\u958B\\u59CB\\u65E5\\u671F *\"}),/*#__PURE__*/_jsx(Calendar,{id:\"startDate\",value:formData.startDate,onChange:e=>handleInputChange('startDate',e.value),dateFormat:\"yy-mm-dd\",showIcon:true,className:\"w-full\",disabled:dialogMode==='view'})]})}),/*#__PURE__*/_jsx(\"div\",{className:\"col-12 md:col-6\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"field\",children:[/*#__PURE__*/_jsx(\"label\",{htmlFor:\"endDate\",className:\"font-bold block mb-2\",children:\"\\u7D50\\u675F\\u65E5\\u671F *\"}),/*#__PURE__*/_jsx(Calendar,{id:\"endDate\",value:formData.endDate,onChange:e=>handleInputChange('endDate',e.value),dateFormat:\"yy-mm-dd\",showIcon:true,className:\"w-full\",disabled:dialogMode==='view'})]})}),/*#__PURE__*/_jsx(\"div\",{className:\"col-6 md:col-6\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"field\",children:[/*#__PURE__*/_jsx(\"label\",{htmlFor:\"startTime\",className:\"font-bold block mb-2\",children:\"\\u958B\\u59CB\\u6642\\u9593\"}),/*#__PURE__*/_jsx(InputText,{id:\"startTime\",type:\"time\",value:formData.startTime,onChange:e=>handleInputChange('startTime',e.target.value),className:\"w-full\",disabled:dialogMode==='view'})]})}),/*#__PURE__*/_jsx(\"div\",{className:\"col-6 md:col-6\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"field\",children:[/*#__PURE__*/_jsx(\"label\",{htmlFor:\"endTime\",className:\"font-bold block mb-2\",children:\"\\u7D50\\u675F\\u6642\\u9593\"}),/*#__PURE__*/_jsx(InputText,{id:\"endTime\",type:\"time\",value:formData.endTime,onChange:e=>handleInputChange('endTime',e.target.value),className:\"w-full\",disabled:dialogMode==='view'})]})})]}),dialogMode!=='view'&&/*#__PURE__*/_jsxs(\"div\",{className:\"grid\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"col-12\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"field-checkbox\",children:[/*#__PURE__*/_jsx(Checkbox,{inputId:\"isRepeat\",checked:formData.isRepeat,onChange:e=>handleInputChange('isRepeat',e.checked||false)}),/*#__PURE__*/_jsx(\"label\",{htmlFor:\"isRepeat\",className:\"ml-2 font-bold\",children:\"\\u91CD\\u8907\\u884C\\u7A0B\"})]})}),formData.isRepeat&&/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(\"div\",{className:\"col-6 md:col-6\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"field\",children:[/*#__PURE__*/_jsx(\"label\",{htmlFor:\"repeatType\",className:\"font-bold block mb-2\",children:\"\\u91CD\\u8907\\u985E\\u578B\"}),/*#__PURE__*/_jsx(Dropdown,{id:\"repeatType\",value:formData.repeatType,options:[{label:'每日',value:'daily'},{label:'每週',value:'weekly'},{label:'每月',value:'monthly'}],onChange:e=>handleInputChange('repeatType',e.value),className:\"w-full\",style:{width:'90%'}})]})}),/*#__PURE__*/_jsx(\"div\",{className:\"col-6 md:col-6\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"field\",children:[/*#__PURE__*/_jsx(\"label\",{htmlFor:\"repeatCount\",className:\"font-bold block mb-2\",children:\"\\u91CD\\u8907\\u6B21\\u6578\"}),/*#__PURE__*/_jsx(InputNumber,{id:\"repeatCount\",value:formData.repeatCount,onValueChange:e=>handleInputChange('repeatCount',e.value||1),min:1,max:10,inputStyle:{width:'90%'}})]})}),/*#__PURE__*/_jsx(\"div\",{className:\"col-12\",children:/*#__PURE__*/_jsx(\"div\",{className:\"p-1 bg-blue-50 border-round\",children:/*#__PURE__*/_jsxs(\"p\",{className:\"text-sm text-blue-800 m-0\",children:[/*#__PURE__*/_jsx(\"i\",{className:\"pi pi-info-circle mr-2\"}),formData.repeatType==='daily'&&\"\\u5C07\\u6BCF\\u65E5\\u91CD\\u8907 \".concat(formData.repeatCount,\" \\u6B21\"),formData.repeatType==='weekly'&&\"\\u5C07\\u6BCF\\u9031\\u91CD\\u8907 \".concat(formData.repeatCount,\" \\u6B21\"),formData.repeatType==='monthly'&&\"\\u5C07\\u6BCF\\u6708\\u91CD\\u8907 \".concat(formData.repeatCount,\" \\u6B21\")]})})})]})]})]})]});};export default SchedulesPage;", "map": {"version": 3, "names": ["formatUtcToTaipei", "dayGridPlugin", "interactionPlugin", "FullCalendar", "timeGridPlugin", "<PERSON><PERSON>", "Calendar", "ConfirmDialog", "confirmDialog", "Dialog", "Dropdown", "InputText", "Toast", "Checkbox", "InputNumber", "React", "useEffect", "useRef", "useState", "useNavigate", "ROUTES", "api", "jsx", "_jsx", "jsxs", "_jsxs", "Fragment", "_Fragment", "SchedulesPage", "toast", "navigate", "events", "setEvents", "showDialog", "setShowDialog", "dialogMode", "setDialogMode", "selectedEvent", "setSelectedEvent", "doctors", "setDoctors", "patients", "setPatients", "isLoading", "setIsLoading", "formData", "setFormData", "doctorId", "patientId", "startDate", "endDate", "startTime", "endTime", "isRepeat", "repeatType", "repeatCount", "loadInitialData", "_toast$current", "console", "log", "doctorsResponse", "patientsResponse", "schedulesResponse", "Promise", "all", "get", "data", "doctorsData", "patientsData", "schedulesData", "formattedEvents", "map", "schedule", "id", "title", "start", "end", "backgroundColor", "borderColor", "<PERSON><PERSON><PERSON>", "patientName", "current", "show", "severity", "summary", "detail", "error", "_toast$current2", "reloadSchedules", "_toast$current3", "response", "treatmentId", "description", "_toast$current4", "handleInputChange", "field", "value", "prev", "_objectSpread", "handleAddEvent", "_toast$current5", "startDateTime", "Date", "endDateTime", "startHour", "startMinute", "split", "endHour", "endMinute", "setHours", "parseInt", "getRepeatTypeValue", "type", "scheduleData", "toISOString", "_toast$current6", "updateData", "put", "concat", "_toast$current7", "post", "resetForm", "_toast$current8", "_error$response", "_error$response$data", "message", "handleEventClick", "clickInfo", "eventData", "find", "event", "handleEdit", "handleDelete", "header", "icon", "accept", "_toast$current9", "delete", "_error$response2", "_error$response2$data", "_toast$current0", "status", "handleOpenCase", "createResponse", "PatientId", "DoctorId", "patch", "TreatmentId", "treatmentResponse", "params", "treatmentsId", "treatmentData", "TREATMENT_DETAIL", "state", "treatment", "patient", "_toast$current1", "life", "handleSaveEdit", "className", "children", "ref", "hidden", "label", "onClick", "outlined", "disabled", "plugins", "height", "initialView", "locale", "weekends", "allDaySlot", "headerToolbar", "left", "center", "right", "titleFormat", "year", "month", "dayHeaderFormat", "day", "weekday", "slotLabelFormat", "hour", "minute", "hour12", "customButtons", "reloadDate", "text", "click", "addDate", "slotMinTime", "slotMaxTime", "eventClick", "selectable", "selectMirror", "dayMaxEvents", "businessHours", "daysOfWeek", "visible", "style", "width", "onHide", "footer", "htmlFor", "options", "onChange", "e", "optionLabel", "optionValue", "placeholder", "dateFormat", "showIcon", "target", "inputId", "checked", "onValueChange", "min", "max", "inputStyle"], "sources": ["C:/Users/<USER>/Desktop/demo-git/demo-react/my-fullstack-app/frontend/src/components/Page/SchedulesPage.tsx"], "sourcesContent": ["import { formatUtcToTaipei } from '../../utils/dateUtils';\r\nimport dayGridPlugin from '@fullcalendar/daygrid';\r\nimport interactionPlugin from '@fullcalendar/interaction';\r\nimport FullCalendar from '@fullcalendar/react';\r\nimport timeGridPlugin from '@fullcalendar/timegrid';\r\nimport { Button } from 'primereact/button';\r\nimport { Calendar } from 'primereact/calendar';\r\nimport { ConfirmDialog, confirmDialog } from 'primereact/confirmdialog';\r\nimport { Dialog } from 'primereact/dialog';\r\nimport { Dropdown } from 'primereact/dropdown';\r\nimport { InputText } from 'primereact/inputtext';\r\nimport { Toast } from 'primereact/toast';\r\nimport { Checkbox } from 'primereact/checkbox';\r\nimport { InputNumber } from 'primereact/inputnumber';\r\nimport React, { useEffect, useRef, useState } from 'react';\r\nimport { useNavigate } from 'react-router-dom';\r\nimport { ROUTES } from '../../constants/routes';\r\nimport api from '../../services/api';\r\n\r\ninterface CalendarEvent {\r\n  id?: string;\r\n  title: string;\r\n  start: string;\r\n  end: string;\r\n  backgroundColor?: string;\r\n  borderColor?: string;\r\n  doctorId?: number;\r\n  patientId?: number;\r\n  treatmentId?: number;\r\n  doctorName?: string;\r\n  patientName?: string;\r\n  description?: string;\r\n}\r\n\r\ninterface EventFormData {\r\n  doctorId: number | null;\r\n  patientId: number | null;\r\n  startDate: Date | null;\r\n  endDate: Date | null;\r\n  startTime: string;\r\n  endTime: string;\r\n  // 重複設定\r\n  isRepeat: boolean;\r\n  repeatType: 'daily' | 'weekly' | 'monthly';\r\n  repeatCount: number;\r\n}\r\n\r\ninterface Doctor {\r\n  Id: number;\r\n  Name: string;\r\n}\r\n\r\ninterface Patient {\r\n  Id: number;\r\n  Name: string;\r\n}\r\n\r\ntype DialogMode = 'add' | 'edit' | 'view';\r\n\r\nconst SchedulesPage: React.FC = () => {\r\n  const toast = useRef<Toast>(null);\r\n  const navigate = useNavigate();\r\n\r\n  const [events, setEvents] = useState<CalendarEvent[]>([]);\r\n\r\n  const [showDialog, setShowDialog] = useState(false);\r\n  const [dialogMode, setDialogMode] = useState<DialogMode>('add');\r\n  const [selectedEvent, setSelectedEvent] = useState<CalendarEvent | null>(null);\r\n  const [doctors, setDoctors] = useState<Doctor[]>([]);\r\n  const [patients, setPatients] = useState<Patient[]>([]);\r\n  const [isLoading, setIsLoading] = useState(false);\r\n  const [formData, setFormData] = useState<EventFormData>({\r\n    doctorId: null,\r\n    patientId: null,\r\n    startDate: null,\r\n    endDate: null,\r\n    startTime: '09:00',\r\n    endTime: '10:00',\r\n    isRepeat: false,\r\n    repeatType: 'daily',\r\n    repeatCount: 1,\r\n  });\r\n\r\n  // 載入所有數據\r\n  useEffect(() => {\r\n    const loadInitialData = async () => {\r\n      try {\r\n        console.log('開始載入數據...');\r\n\r\n        // 並行載入治療師、病患和行程數據\r\n        const [doctorsResponse, patientsResponse, schedulesResponse] = await Promise.all([\r\n          api.get('/api/users/DoctorList'),\r\n          api.get('/api/patients/PatientList'),\r\n          api.get('/api/schedule')\r\n        ]);\r\n\r\n        console.log('治療師數據:', doctorsResponse.data);\r\n        console.log('病患數據:', patientsResponse.data);\r\n        console.log('行程數據:', schedulesResponse.data);\r\n\r\n        const doctorsData = doctorsResponse.data;\r\n        const patientsData = patientsResponse.data;\r\n        const schedulesData = schedulesResponse.data;\r\n\r\n        setDoctors(doctorsData);\r\n        setPatients(patientsData);\r\n\r\n        // 轉換後端數據格式為前端 CalendarEvent 格式\r\n        const formattedEvents = schedulesData.map((schedule: any) => ({\r\n          id: schedule.id,\r\n          title: schedule.title,\r\n          start: schedule.start,\r\n          end: schedule.end,\r\n          backgroundColor: schedule.backgroundColor || '#3788d8',\r\n          borderColor: schedule.borderColor,\r\n          doctorId: schedule.doctorId,\r\n          patientId: schedule.patientId,\r\n          doctorName: schedule.doctorName,\r\n          patientName: schedule.patientName,\r\n        }));\r\n\r\n        console.log('格式化後的事件:', formattedEvents);\r\n        setEvents(formattedEvents);\r\n\r\n        toast.current?.show({\r\n          severity: 'success',\r\n          summary: '成功',\r\n          detail: '數據載入完成',\r\n        });\r\n      } catch (error) {\r\n        console.error('載入數據失敗:', error);\r\n        toast.current?.show({\r\n          severity: 'error',\r\n          summary: '錯誤',\r\n          detail: '載入數據失敗',\r\n        });\r\n      }\r\n    };\r\n\r\n    loadInitialData();\r\n  }, []);\r\n\r\n  // 重新載入行程數據\r\n  const reloadSchedules = async () => {\r\n    try {\r\n      setIsLoading(true);\r\n      const response = await api.get('/api/schedule');\r\n      const schedulesData = response.data;\r\n\r\n      const formattedEvents = schedulesData.map((schedule: any) => ({\r\n        id: schedule.id,\r\n        title: schedule.title,\r\n        start: schedule.start,\r\n        end: schedule.end,\r\n        backgroundColor: schedule.backgroundColor || '#3788d8',\r\n        borderColor: schedule.borderColor,\r\n        doctorId: schedule.doctorId,\r\n        patientId: schedule.patientId,\r\n        treatmentId: schedule.treatmentId,\r\n        doctorName: schedule.doctorName,\r\n        patientName: schedule.patientName,\r\n        description: schedule.description,\r\n      }));\r\n\r\n      setEvents(formattedEvents);\r\n\r\n      toast.current?.show({\r\n        severity: 'success',\r\n        summary: '成功',\r\n        detail: '行程數據已重新載入',\r\n      });\r\n    } catch (error) {\r\n      console.error('重新載入行程失敗:', error);\r\n      toast.current?.show({\r\n        severity: 'error',\r\n        summary: '錯誤',\r\n        detail: '重新載入行程失敗',\r\n      });\r\n    } finally {\r\n      setIsLoading(false);\r\n    }\r\n  };\r\n\r\n  \r\n\r\n  const handleInputChange = (field: keyof EventFormData, value: string | Date | null | number | boolean) => {\r\n    setFormData(prev => ({\r\n      ...prev,\r\n      [field]: value,\r\n    }));\r\n  };\r\n\r\n  const handleAddEvent = async () => {\r\n    if (!formData.doctorId || !formData.patientId || !formData.startDate || !formData.endDate) {\r\n      toast.current?.show({\r\n        severity: 'warn',\r\n        summary: '警告',\r\n        detail: '請填寫所有必要欄位',\r\n      });\r\n      return;\r\n    }\r\n\r\n    try {\r\n      const startDateTime = new Date(formData.startDate);\r\n      const endDateTime = new Date(formData.endDate);\r\n\r\n      // 設置時間\r\n      const [startHour, startMinute] = formData.startTime.split(':');\r\n      const [endHour, endMinute] = formData.endTime.split(':');\r\n\r\n      startDateTime.setHours(parseInt(startHour as string), parseInt(startMinute as string));\r\n      endDateTime.setHours(parseInt(endHour as string), parseInt(endMinute as string));\r\n\r\n      // 轉換重複類型\r\n      const getRepeatTypeValue = (type: string) => {\r\n        switch (type) {\r\n          case 'daily': return 1;\r\n          case 'weekly': return 2;\r\n          case 'monthly': return 3;\r\n          default: return 0;\r\n        }\r\n      };\r\n\r\n      const scheduleData = {\r\n        doctorId: formData.doctorId,\r\n        patientId: formData.patientId,\r\n        startDateTime: startDateTime.toISOString(),\r\n        endDateTime: endDateTime.toISOString(),\r\n        description: '', // 可以後續添加描述欄位\r\n        backgroundColor: '#3788d8',\r\n        repeatType: formData.isRepeat ? getRepeatTypeValue(formData.repeatType) : 0,\r\n        repeatCount: formData.isRepeat ? formData.repeatCount : 1,\r\n      };\r\n\r\n      if (dialogMode === 'edit' && selectedEvent && selectedEvent.id) {\r\n        // 編輯模式 - 調用 PUT API\r\n        const updateData = {\r\n          ...scheduleData,\r\n          id: parseInt(selectedEvent.id),\r\n        };\r\n\r\n        await api.put(`/api/schedule/${selectedEvent.id}`, updateData);\r\n\r\n        // 重新載入所有行程數據以確保同步\r\n        await reloadSchedules();\r\n\r\n        toast.current?.show({\r\n          severity: 'success',\r\n          summary: '成功',\r\n          detail: '行程已更新',\r\n        });\r\n      } else {\r\n        // 新增模式 - 調用 POST API\r\n        await api.post('/api/schedule', scheduleData);\r\n\r\n        // 重新載入所有行程數據以確保同步\r\n        await reloadSchedules();\r\n\r\n        toast.current?.show({\r\n          severity: 'success',\r\n          summary: '成功',\r\n          detail: '行程已新增',\r\n        });\r\n      }\r\n\r\n      setShowDialog(false);\r\n      resetForm();\r\n    } catch (error: any) {\r\n      console.error('操作失敗:', error);\r\n      toast.current?.show({\r\n        severity: 'error',\r\n        summary: '錯誤',\r\n        detail: error.response?.data?.message || '操作失敗',\r\n      });\r\n    }\r\n  };\r\n\r\n  const resetForm = () => {\r\n    setFormData({\r\n      doctorId: null,\r\n      patientId: null,\r\n      startDate: null,\r\n      endDate: null,\r\n      startTime: '09:00',\r\n      endTime: '10:00',\r\n      isRepeat: false,\r\n      repeatType: 'daily',\r\n      repeatCount: 1,\r\n    });\r\n    setSelectedEvent(null);\r\n    setDialogMode('add');\r\n  };\r\n\r\n  const handleEventClick = (clickInfo: any) => {\r\n    const eventData = events.find(event => event.id === clickInfo.event.id);\r\n    if (eventData) {\r\n      setSelectedEvent(eventData);\r\n      setDialogMode('view');\r\n\r\n      // 填充表單數據\r\n      setFormData({\r\n        doctorId: eventData.doctorId || null,\r\n        patientId: eventData.patientId || null,\r\n        startDate: new Date(eventData.start),\r\n        endDate: new Date(eventData.end),\r\n        startTime: formatUtcToTaipei(eventData.start, 'HH:mm'),\r\n        endTime: formatUtcToTaipei(eventData.end, 'HH:mm'),\r\n        isRepeat: false,\r\n        repeatType: 'daily',\r\n        repeatCount: 1,\r\n      });\r\n\r\n      setShowDialog(true);\r\n    }\r\n  };\r\n\r\n  const handleEdit = () => {\r\n    setDialogMode('edit');\r\n  };\r\n\r\n  const handleDelete = () => {\r\n    if (!selectedEvent) return;\r\n\r\n    confirmDialog({\r\n      message: `確定要刪除行程 \"${selectedEvent.title}\" 嗎？`,\r\n      header: '確認刪除',\r\n      icon: 'pi pi-exclamation-triangle',\r\n      accept: async () => {\r\n        try {\r\n          // 調用後端 DELETE API\r\n          await api.delete(`/api/schedule/${selectedEvent.id}`);\r\n\r\n          // 重新載入所有行程數據以確保同步\r\n          await reloadSchedules();\r\n          setShowDialog(false);\r\n          resetForm();\r\n\r\n          toast.current?.show({\r\n            severity: 'success',\r\n            summary: '成功',\r\n            detail: '行程已刪除',\r\n          });\r\n        } catch (error: any) {\r\n          console.error('刪除失敗:', error);\r\n          var detail =  error.status === 403 ? \"您無權限，請通知管理員\" : error.response?.data?.message || '刪除失敗';\r\n          \r\n          toast.current?.show({\r\n            severity: 'error',\r\n            summary: '錯誤',\r\n            detail: detail,\r\n          });\r\n        }\r\n      },\r\n    });\r\n  };\r\n\r\n  const handleOpenCase = async () => {\r\n    if (!selectedEvent || !selectedEvent.patientId) return;\r\n\r\n    try {\r\n      let treatmentId = selectedEvent.treatmentId;\r\n\r\n      if (!treatmentId) {\r\n        // 如果沒有 TreatmentId，創建新的治療案件\r\n        console.log('創建新的治療案件');\r\n        const createResponse = await api.post('/api/treatment/Insert', {\r\n          PatientId: selectedEvent.patientId,\r\n          DoctorId: selectedEvent.doctorId,\r\n          // 其他必要的初始數據\r\n        });\r\n\r\n        treatmentId = createResponse.data.treatmentId;\r\n        console.log('新治療案件創建成功', { treatmentId });\r\n\r\n        // 更新 Schedule 的 TreatmentId\r\n        await api.patch(`/api/schedule/${selectedEvent.id}/treatment`, {\r\n          TreatmentId: treatmentId\r\n        });\r\n        console.log('Schedule TreatmentId 更新成功');\r\n\r\n        // 重新載入行程數據\r\n        reloadSchedules();\r\n      }\r\n\r\n      // 獲取完整的治療數據\r\n      console.log('獲取治療數據', { treatmentId });\r\n      \r\n      const treatmentResponse = await api.get('/api/treatment', {\r\n        params: {\r\n          treatmentsId: treatmentId\r\n        }\r\n      });\r\n\r\n      const treatmentData = treatmentResponse.data;\r\n\r\n      // 跳轉到 TreatmentsDetailPage\r\n      if (treatmentId) {\r\n        navigate(`${ROUTES.TREATMENT_DETAIL}?id=${treatmentId}`, {\r\n          state: { treatment: treatmentData, patient: { id: treatmentData.patientId }}\r\n        });\r\n      }\r\n\r\n    } catch (error) {\r\n      console.error('開案失敗', error);\r\n      toast.current?.show({\r\n        severity: 'error',\r\n        summary: '開案失敗',\r\n        detail: '無法開啟治療案件，請稍後再試',\r\n        life: 3000\r\n      });\r\n    }\r\n  };\r\n\r\n  const handleSaveEdit = () => {\r\n    confirmDialog({\r\n      message: '確定要保存修改嗎？',\r\n      header: '確認編輯',\r\n      icon: 'pi pi-question-circle',\r\n      accept: () => {\r\n        handleAddEvent(); // 重用新增邏輯，但會根據 dialogMode 判斷是編輯還是新增\r\n      },\r\n    });\r\n  };\r\n\r\n  return (\r\n    <div className=\"schedules-page\">\r\n      <Toast ref={toast} />\r\n      <ConfirmDialog />\r\n\r\n      {/* 操作按鈕 */}\r\n      <div className=\"card\" hidden>\r\n        <div className=\"flex pb-3 justify-content-end align-items-center\">\r\n          <div className=\"flex gap-2\">\r\n            <Button\r\n              label=\"重新載入\"\r\n              icon={isLoading ? \"pi pi-spin pi-spinner\" : \"pi pi-refresh\"}\r\n              onClick={reloadSchedules}\r\n              className=\"p-button-secondary\"\r\n              outlined\r\n              disabled={isLoading}\r\n            />\r\n            <Button\r\n              label=\"新增行程\"\r\n              icon=\"pi pi-plus\"\r\n              onClick={() => setShowDialog(true)}\r\n              className=\"p-button-primary\"\r\n            />\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      {/* 日曆 */}\r\n      <div className=\"card\">\r\n        <div className=\"calendar-container\">\r\n          <FullCalendar\r\n            plugins={[dayGridPlugin, timeGridPlugin, interactionPlugin]}\r\n            height=\"auto\"\r\n            initialView=\"timeGridWeek\"\r\n            locale=\"zh-tw\"\r\n            weekends={true}\r\n            allDaySlot={false}\r\n            headerToolbar={{\r\n              left: 'prev,next today',\r\n              center: 'title',\r\n              right: 'reloadDate,addDate dayGridMonth,timeGridWeek',\r\n            }}\r\n            titleFormat={{\r\n                year: 'numeric', // 顯示年份 (例如: 2023)\r\n                month: 'long'    // 顯示完整月份名稱 (例如: 十二月)\r\n            }}\r\n            dayHeaderFormat={{\r\n              day: 'numeric', // 顯示日期數字 (例如: 13)\r\n              weekday: 'narrow' // 顯示短格式的星期 (例如: 日, 一, 二)\r\n            }}\r\n            slotLabelFormat={{\r\n              hour: '2-digit', // 小時顯示為兩位數 (例如: 09)\r\n              minute: 'numeric', // 分鐘顯示為兩位數 (例如: 00)\r\n              hour12: false // 禁用 12 小時制，啟用 24 小時制\r\n            }}\r\n            customButtons={{\r\n              reloadDate: {\r\n                text: '重整',\r\n                click: reloadSchedules,\r\n              },\r\n              addDate: {\r\n                text: '新增',\r\n                click: () => setShowDialog(true),\r\n              },\r\n            }}\r\n            slotMinTime=\"09:00:00\"\r\n            slotMaxTime=\"22:00:00\"\r\n            events={events}\r\n            eventClick={handleEventClick}\r\n            selectable={true}\r\n            selectMirror={true}\r\n            dayMaxEvents={true}\r\n            businessHours={{\r\n              daysOfWeek: [1, 2, 3, 4, 5, 6],\r\n              startTime: '10:00',\r\n              endTime: '18:00',\r\n            }}\r\n          />\r\n        </div>\r\n      </div>\r\n\r\n      {/* 行程對話框 */}\r\n      <Dialog\r\n        header={\r\n          dialogMode === 'add' ? '新增行程' :\r\n          dialogMode === 'edit' ? '編輯行程' : '行程詳情'\r\n        }\r\n        visible={showDialog}\r\n        style={{ width: '500px' }}\r\n        onHide={() => {\r\n          setShowDialog(false);\r\n          resetForm();\r\n        }}\r\n        footer={\r\n          <div className=\"flex gap-2\">\r\n            {dialogMode === 'view' ? (\r\n              <>\r\n                <Button\r\n                  label=\"開案\"\r\n                  icon=\"pi pi-folder-open\"\r\n                  className=\"p-button-success\"\r\n                  onClick={handleOpenCase}\r\n                />\r\n                <Button\r\n                  label=\"編輯\"\r\n                  icon=\"pi pi-pencil\"\r\n                  onClick={handleEdit}\r\n                />\r\n                <Button\r\n                  label=\"刪除\"\r\n                  icon=\"pi pi-trash\"\r\n                  className=\"p-button-danger\"\r\n                  onClick={handleDelete}\r\n                />\r\n                <Button\r\n                  label=\"關閉\"\r\n                  icon=\"pi pi-times\"\r\n                  className=\"p-button-outlined\"\r\n                  onClick={() => {\r\n                    setShowDialog(false);\r\n                    resetForm();\r\n                  }}\r\n                />\r\n              </>\r\n            ) : (\r\n              <>\r\n                <Button\r\n                  label=\"取消\"\r\n                  icon=\"pi pi-times\"\r\n                  className=\"p-button-outlined\"\r\n                  onClick={() => {\r\n                    setShowDialog(false);\r\n                    resetForm();\r\n                  }}\r\n                />\r\n                <Button\r\n                  label={dialogMode === 'edit' ? '保存' : '新增'}\r\n                  icon=\"pi pi-check\"\r\n                  onClick={dialogMode === 'edit' ? handleSaveEdit : handleAddEvent}\r\n                />\r\n              </>\r\n            )}\r\n          </div>\r\n        }\r\n      >\r\n        <div className=\"grid\">\r\n          <div className=\"col-6 md:col-6\">\r\n            <div className=\"field\">\r\n              <label htmlFor=\"doctor\" className=\"font-bold block mb-2\">\r\n                治療師 *\r\n              </label>\r\n              <Dropdown\r\n                id=\"doctor\"\r\n                value={formData.doctorId}\r\n                options={doctors}\r\n                onChange={(e) => handleInputChange('doctorId', e.value)}\r\n                optionLabel=\"Name\"\r\n                optionValue=\"Id\"\r\n                placeholder=\"請選擇治療師\"\r\n                className=\"w-full\"\r\n                disabled={dialogMode === 'view'}\r\n              />\r\n            </div>\r\n          </div>\r\n\r\n          <div className=\"col-6 md:col-6\">\r\n            <div className=\"field\">\r\n              <label htmlFor=\"patient\" className=\"font-bold block mb-2\">\r\n                病患 *\r\n              </label>\r\n              <Dropdown\r\n                id=\"patient\"\r\n                value={formData.patientId}\r\n                options={patients}\r\n                onChange={(e) => handleInputChange('patientId', e.value)}\r\n                optionLabel=\"Name\"\r\n                optionValue=\"Id\"\r\n                placeholder=\"請選擇病患\"\r\n                className=\"w-full\"\r\n                disabled={dialogMode === 'view'}\r\n              />\r\n            </div>\r\n          </div>\r\n\r\n          <div className=\"col-12 md:col-6\">\r\n            <div className=\"field\">\r\n              <label htmlFor=\"startDate\" className=\"font-bold block mb-2\">\r\n                開始日期 *\r\n              </label>\r\n              <Calendar\r\n                id=\"startDate\"\r\n                value={formData.startDate}\r\n                onChange={(e) => handleInputChange('startDate', e.value as Date)}\r\n                dateFormat=\"yy-mm-dd\"\r\n                showIcon\r\n                className=\"w-full\"\r\n                disabled={dialogMode === 'view'}\r\n              />\r\n            </div>\r\n          </div>\r\n\r\n          <div className=\"col-12 md:col-6\">\r\n            <div className=\"field\">\r\n              <label htmlFor=\"endDate\" className=\"font-bold block mb-2\">\r\n                結束日期 *\r\n              </label>\r\n              <Calendar\r\n                id=\"endDate\"\r\n                value={formData.endDate}\r\n                onChange={(e) => handleInputChange('endDate', e.value as Date)}\r\n                dateFormat=\"yy-mm-dd\"\r\n                showIcon\r\n                className=\"w-full\"\r\n                disabled={dialogMode === 'view'}\r\n              />\r\n            </div>\r\n          </div>\r\n\r\n          <div className=\"col-6 md:col-6\">\r\n            <div className=\"field\">\r\n              <label htmlFor=\"startTime\" className=\"font-bold block mb-2\">\r\n                開始時間\r\n              </label>\r\n              <InputText\r\n                id=\"startTime\"\r\n                type=\"time\"\r\n                value={formData.startTime}\r\n                onChange={(e) => handleInputChange('startTime', e.target.value)}\r\n                className=\"w-full\"\r\n                disabled={dialogMode === 'view'}\r\n              />\r\n            </div>\r\n          </div>\r\n\r\n          <div className=\"col-6 md:col-6\">\r\n            <div className=\"field\">\r\n              <label htmlFor=\"endTime\" className=\"font-bold block mb-2\">\r\n                結束時間\r\n              </label>\r\n              <InputText\r\n                id=\"endTime\"\r\n                type=\"time\"\r\n                value={formData.endTime}\r\n                onChange={(e) => handleInputChange('endTime', e.target.value)}\r\n                className=\"w-full\"\r\n                disabled={dialogMode === 'view'}\r\n              />\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        {/* 重複設定 */}\r\n        {dialogMode !== 'view' && (\r\n          <div className=\"grid\">\r\n            <div className=\"col-12\">\r\n              <div className=\"field-checkbox\">\r\n                <Checkbox\r\n                  inputId=\"isRepeat\"\r\n                  checked={formData.isRepeat}\r\n                  onChange={(e) => handleInputChange('isRepeat', e.checked || false)}\r\n                />\r\n                <label htmlFor=\"isRepeat\" className=\"ml-2 font-bold\">\r\n                  重複行程\r\n                </label>\r\n              </div>\r\n            </div>\r\n\r\n            {formData.isRepeat && (\r\n              <>\r\n                <div className=\"col-6 md:col-6\">\r\n                  <div className=\"field\">\r\n                    <label htmlFor=\"repeatType\" className=\"font-bold block mb-2\">\r\n                      重複類型\r\n                    </label>\r\n                    <Dropdown\r\n                      id=\"repeatType\"\r\n                      value={formData.repeatType}\r\n                      options={[\r\n                        { label: '每日', value: 'daily' },\r\n                        { label: '每週', value: 'weekly' },\r\n                        { label: '每月', value: 'monthly' }\r\n                      ]}\r\n                      onChange={(e) => handleInputChange('repeatType', e.value)}\r\n                      className=\"w-full\"\r\n                      style={{ width: '90%' }}\r\n                    />\r\n                  </div>\r\n                </div>\r\n\r\n                <div className=\"col-6 md:col-6\">\r\n                  <div className=\"field\">\r\n                    <label htmlFor=\"repeatCount\" className=\"font-bold block mb-2\">\r\n                      重複次數\r\n                    </label>\r\n                    <InputNumber\r\n                      id=\"repeatCount\"\r\n                      value={formData.repeatCount}\r\n                      onValueChange={(e) => handleInputChange('repeatCount', e.value || 1)}\r\n                      min={1}\r\n                      max={10}\r\n                      inputStyle={{ width: '90%' }}\r\n                    />\r\n                  </div>\r\n                </div>\r\n\r\n                <div className=\"col-12\">\r\n                  <div className=\"p-1 bg-blue-50 border-round\">\r\n                    <p className=\"text-sm text-blue-800 m-0\">\r\n                      <i className=\"pi pi-info-circle mr-2\"></i>\r\n                      {formData.repeatType === 'daily' && `將每日重複 ${formData.repeatCount} 次`}\r\n                      {formData.repeatType === 'weekly' && `將每週重複 ${formData.repeatCount} 次`}\r\n                      {formData.repeatType === 'monthly' && `將每月重複 ${formData.repeatCount} 次`}\r\n                    </p>\r\n                  </div>\r\n                </div>\r\n              </>\r\n            )}\r\n          </div>\r\n        )}\r\n      </Dialog>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default SchedulesPage;\r\n\r\n"], "mappings": "wJAAA,OAASA,iBAAiB,KAAQ,uBAAuB,CACzD,MAAO,CAAAC,aAAa,KAAM,uBAAuB,CACjD,MAAO,CAAAC,iBAAiB,KAAM,2BAA2B,CACzD,MAAO,CAAAC,YAAY,KAAM,qBAAqB,CAC9C,MAAO,CAAAC,cAAc,KAAM,wBAAwB,CACnD,OAASC,MAAM,KAAQ,mBAAmB,CAC1C,OAASC,QAAQ,KAAQ,qBAAqB,CAC9C,OAASC,aAAa,CAAEC,aAAa,KAAQ,0BAA0B,CACvE,OAASC,MAAM,KAAQ,mBAAmB,CAC1C,OAASC,QAAQ,KAAQ,qBAAqB,CAC9C,OAASC,SAAS,KAAQ,sBAAsB,CAChD,OAASC,KAAK,KAAQ,kBAAkB,CACxC,OAASC,QAAQ,KAAQ,qBAAqB,CAC9C,OAASC,WAAW,KAAQ,wBAAwB,CACpD,MAAO,CAAAC,KAAK,EAAIC,SAAS,CAAEC,MAAM,CAAEC,QAAQ,KAAQ,OAAO,CAC1D,OAASC,WAAW,KAAQ,kBAAkB,CAC9C,OAASC,MAAM,KAAQ,wBAAwB,CAC/C,MAAO,CAAAC,GAAG,KAAM,oBAAoB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,CAAAC,QAAA,IAAAC,SAAA,yBA0CrC,KAAM,CAAAC,aAAuB,CAAGA,CAAA,GAAM,CACpC,KAAM,CAAAC,KAAK,CAAGZ,MAAM,CAAQ,IAAI,CAAC,CACjC,KAAM,CAAAa,QAAQ,CAAGX,WAAW,CAAC,CAAC,CAE9B,KAAM,CAACY,MAAM,CAAEC,SAAS,CAAC,CAAGd,QAAQ,CAAkB,EAAE,CAAC,CAEzD,KAAM,CAACe,UAAU,CAAEC,aAAa,CAAC,CAAGhB,QAAQ,CAAC,KAAK,CAAC,CACnD,KAAM,CAACiB,UAAU,CAAEC,aAAa,CAAC,CAAGlB,QAAQ,CAAa,KAAK,CAAC,CAC/D,KAAM,CAACmB,aAAa,CAAEC,gBAAgB,CAAC,CAAGpB,QAAQ,CAAuB,IAAI,CAAC,CAC9E,KAAM,CAACqB,OAAO,CAAEC,UAAU,CAAC,CAAGtB,QAAQ,CAAW,EAAE,CAAC,CACpD,KAAM,CAACuB,QAAQ,CAAEC,WAAW,CAAC,CAAGxB,QAAQ,CAAY,EAAE,CAAC,CACvD,KAAM,CAACyB,SAAS,CAAEC,YAAY,CAAC,CAAG1B,QAAQ,CAAC,KAAK,CAAC,CACjD,KAAM,CAAC2B,QAAQ,CAAEC,WAAW,CAAC,CAAG5B,QAAQ,CAAgB,CACtD6B,QAAQ,CAAE,IAAI,CACdC,SAAS,CAAE,IAAI,CACfC,SAAS,CAAE,IAAI,CACfC,OAAO,CAAE,IAAI,CACbC,SAAS,CAAE,OAAO,CAClBC,OAAO,CAAE,OAAO,CAChBC,QAAQ,CAAE,KAAK,CACfC,UAAU,CAAE,OAAO,CACnBC,WAAW,CAAE,CACf,CAAC,CAAC,CAEF;AACAvC,SAAS,CAAC,IAAM,CACd,KAAM,CAAAwC,eAAe,CAAG,KAAAA,CAAA,GAAY,CAClC,GAAI,KAAAC,cAAA,CACFC,OAAO,CAACC,GAAG,CAAC,WAAW,CAAC,CAExB;AACA,KAAM,CAACC,eAAe,CAAEC,gBAAgB,CAAEC,iBAAiB,CAAC,CAAG,KAAM,CAAAC,OAAO,CAACC,GAAG,CAAC,CAC/E3C,GAAG,CAAC4C,GAAG,CAAC,uBAAuB,CAAC,CAChC5C,GAAG,CAAC4C,GAAG,CAAC,2BAA2B,CAAC,CACpC5C,GAAG,CAAC4C,GAAG,CAAC,eAAe,CAAC,CACzB,CAAC,CAEFP,OAAO,CAACC,GAAG,CAAC,QAAQ,CAAEC,eAAe,CAACM,IAAI,CAAC,CAC3CR,OAAO,CAACC,GAAG,CAAC,OAAO,CAAEE,gBAAgB,CAACK,IAAI,CAAC,CAC3CR,OAAO,CAACC,GAAG,CAAC,OAAO,CAAEG,iBAAiB,CAACI,IAAI,CAAC,CAE5C,KAAM,CAAAC,WAAW,CAAGP,eAAe,CAACM,IAAI,CACxC,KAAM,CAAAE,YAAY,CAAGP,gBAAgB,CAACK,IAAI,CAC1C,KAAM,CAAAG,aAAa,CAAGP,iBAAiB,CAACI,IAAI,CAE5C1B,UAAU,CAAC2B,WAAW,CAAC,CACvBzB,WAAW,CAAC0B,YAAY,CAAC,CAEzB;AACA,KAAM,CAAAE,eAAe,CAAGD,aAAa,CAACE,GAAG,CAAEC,QAAa,GAAM,CAC5DC,EAAE,CAAED,QAAQ,CAACC,EAAE,CACfC,KAAK,CAAEF,QAAQ,CAACE,KAAK,CACrBC,KAAK,CAAEH,QAAQ,CAACG,KAAK,CACrBC,GAAG,CAAEJ,QAAQ,CAACI,GAAG,CACjBC,eAAe,CAAEL,QAAQ,CAACK,eAAe,EAAI,SAAS,CACtDC,WAAW,CAAEN,QAAQ,CAACM,WAAW,CACjC/B,QAAQ,CAAEyB,QAAQ,CAACzB,QAAQ,CAC3BC,SAAS,CAAEwB,QAAQ,CAACxB,SAAS,CAC7B+B,UAAU,CAAEP,QAAQ,CAACO,UAAU,CAC/BC,WAAW,CAAER,QAAQ,CAACQ,WACxB,CAAC,CAAC,CAAC,CAEHtB,OAAO,CAACC,GAAG,CAAC,UAAU,CAAEW,eAAe,CAAC,CACxCtC,SAAS,CAACsC,eAAe,CAAC,CAE1B,CAAAb,cAAA,CAAA5B,KAAK,CAACoD,OAAO,UAAAxB,cAAA,iBAAbA,cAAA,CAAeyB,IAAI,CAAC,CAClBC,QAAQ,CAAE,SAAS,CACnBC,OAAO,CAAE,IAAI,CACbC,MAAM,CAAE,QACV,CAAC,CAAC,CACJ,CAAE,MAAOC,KAAK,CAAE,KAAAC,eAAA,CACd7B,OAAO,CAAC4B,KAAK,CAAC,SAAS,CAAEA,KAAK,CAAC,CAC/B,CAAAC,eAAA,CAAA1D,KAAK,CAACoD,OAAO,UAAAM,eAAA,iBAAbA,eAAA,CAAeL,IAAI,CAAC,CAClBC,QAAQ,CAAE,OAAO,CACjBC,OAAO,CAAE,IAAI,CACbC,MAAM,CAAE,QACV,CAAC,CAAC,CACJ,CACF,CAAC,CAED7B,eAAe,CAAC,CAAC,CACnB,CAAC,CAAE,EAAE,CAAC,CAEN;AACA,KAAM,CAAAgC,eAAe,CAAG,KAAAA,CAAA,GAAY,CAClC,GAAI,KAAAC,eAAA,CACF7C,YAAY,CAAC,IAAI,CAAC,CAClB,KAAM,CAAA8C,QAAQ,CAAG,KAAM,CAAArE,GAAG,CAAC4C,GAAG,CAAC,eAAe,CAAC,CAC/C,KAAM,CAAAI,aAAa,CAAGqB,QAAQ,CAACxB,IAAI,CAEnC,KAAM,CAAAI,eAAe,CAAGD,aAAa,CAACE,GAAG,CAAEC,QAAa,GAAM,CAC5DC,EAAE,CAAED,QAAQ,CAACC,EAAE,CACfC,KAAK,CAAEF,QAAQ,CAACE,KAAK,CACrBC,KAAK,CAAEH,QAAQ,CAACG,KAAK,CACrBC,GAAG,CAAEJ,QAAQ,CAACI,GAAG,CACjBC,eAAe,CAAEL,QAAQ,CAACK,eAAe,EAAI,SAAS,CACtDC,WAAW,CAAEN,QAAQ,CAACM,WAAW,CACjC/B,QAAQ,CAAEyB,QAAQ,CAACzB,QAAQ,CAC3BC,SAAS,CAAEwB,QAAQ,CAACxB,SAAS,CAC7B2C,WAAW,CAAEnB,QAAQ,CAACmB,WAAW,CACjCZ,UAAU,CAAEP,QAAQ,CAACO,UAAU,CAC/BC,WAAW,CAAER,QAAQ,CAACQ,WAAW,CACjCY,WAAW,CAAEpB,QAAQ,CAACoB,WACxB,CAAC,CAAC,CAAC,CAEH5D,SAAS,CAACsC,eAAe,CAAC,CAE1B,CAAAmB,eAAA,CAAA5D,KAAK,CAACoD,OAAO,UAAAQ,eAAA,iBAAbA,eAAA,CAAeP,IAAI,CAAC,CAClBC,QAAQ,CAAE,SAAS,CACnBC,OAAO,CAAE,IAAI,CACbC,MAAM,CAAE,WACV,CAAC,CAAC,CACJ,CAAE,MAAOC,KAAK,CAAE,KAAAO,eAAA,CACdnC,OAAO,CAAC4B,KAAK,CAAC,WAAW,CAAEA,KAAK,CAAC,CACjC,CAAAO,eAAA,CAAAhE,KAAK,CAACoD,OAAO,UAAAY,eAAA,iBAAbA,eAAA,CAAeX,IAAI,CAAC,CAClBC,QAAQ,CAAE,OAAO,CACjBC,OAAO,CAAE,IAAI,CACbC,MAAM,CAAE,UACV,CAAC,CAAC,CACJ,CAAC,OAAS,CACRzC,YAAY,CAAC,KAAK,CAAC,CACrB,CACF,CAAC,CAID,KAAM,CAAAkD,iBAAiB,CAAGA,CAACC,KAA0B,CAAEC,KAA8C,GAAK,CACxGlD,WAAW,CAACmD,IAAI,EAAAC,aAAA,CAAAA,aAAA,IACXD,IAAI,MACP,CAACF,KAAK,EAAGC,KAAK,EACd,CAAC,CACL,CAAC,CAED,KAAM,CAAAG,cAAc,CAAG,KAAAA,CAAA,GAAY,CACjC,GAAI,CAACtD,QAAQ,CAACE,QAAQ,EAAI,CAACF,QAAQ,CAACG,SAAS,EAAI,CAACH,QAAQ,CAACI,SAAS,EAAI,CAACJ,QAAQ,CAACK,OAAO,CAAE,KAAAkD,eAAA,CACzF,CAAAA,eAAA,CAAAvE,KAAK,CAACoD,OAAO,UAAAmB,eAAA,iBAAbA,eAAA,CAAelB,IAAI,CAAC,CAClBC,QAAQ,CAAE,MAAM,CAChBC,OAAO,CAAE,IAAI,CACbC,MAAM,CAAE,WACV,CAAC,CAAC,CACF,OACF,CAEA,GAAI,CACF,KAAM,CAAAgB,aAAa,CAAG,GAAI,CAAAC,IAAI,CAACzD,QAAQ,CAACI,SAAS,CAAC,CAClD,KAAM,CAAAsD,WAAW,CAAG,GAAI,CAAAD,IAAI,CAACzD,QAAQ,CAACK,OAAO,CAAC,CAE9C;AACA,KAAM,CAACsD,SAAS,CAAEC,WAAW,CAAC,CAAG5D,QAAQ,CAACM,SAAS,CAACuD,KAAK,CAAC,GAAG,CAAC,CAC9D,KAAM,CAACC,OAAO,CAAEC,SAAS,CAAC,CAAG/D,QAAQ,CAACO,OAAO,CAACsD,KAAK,CAAC,GAAG,CAAC,CAExDL,aAAa,CAACQ,QAAQ,CAACC,QAAQ,CAACN,SAAmB,CAAC,CAAEM,QAAQ,CAACL,WAAqB,CAAC,CAAC,CACtFF,WAAW,CAACM,QAAQ,CAACC,QAAQ,CAACH,OAAiB,CAAC,CAAEG,QAAQ,CAACF,SAAmB,CAAC,CAAC,CAEhF;AACA,KAAM,CAAAG,kBAAkB,CAAIC,IAAY,EAAK,CAC3C,OAAQA,IAAI,EACV,IAAK,OAAO,CAAE,MAAO,EAAC,CACtB,IAAK,QAAQ,CAAE,MAAO,EAAC,CACvB,IAAK,SAAS,CAAE,MAAO,EAAC,CACxB,QAAS,MAAO,EAAC,CACnB,CACF,CAAC,CAED,KAAM,CAAAC,YAAY,CAAG,CACnBlE,QAAQ,CAAEF,QAAQ,CAACE,QAAQ,CAC3BC,SAAS,CAAEH,QAAQ,CAACG,SAAS,CAC7BqD,aAAa,CAAEA,aAAa,CAACa,WAAW,CAAC,CAAC,CAC1CX,WAAW,CAAEA,WAAW,CAACW,WAAW,CAAC,CAAC,CACtCtB,WAAW,CAAE,EAAE,CAAE;AACjBf,eAAe,CAAE,SAAS,CAC1BvB,UAAU,CAAET,QAAQ,CAACQ,QAAQ,CAAG0D,kBAAkB,CAAClE,QAAQ,CAACS,UAAU,CAAC,CAAG,CAAC,CAC3EC,WAAW,CAAEV,QAAQ,CAACQ,QAAQ,CAAGR,QAAQ,CAACU,WAAW,CAAG,CAC1D,CAAC,CAED,GAAIpB,UAAU,GAAK,MAAM,EAAIE,aAAa,EAAIA,aAAa,CAACoC,EAAE,CAAE,KAAA0C,eAAA,CAC9D;AACA,KAAM,CAAAC,UAAU,CAAAlB,aAAA,CAAAA,aAAA,IACXe,YAAY,MACfxC,EAAE,CAAEqC,QAAQ,CAACzE,aAAa,CAACoC,EAAE,CAAC,EAC/B,CAED,KAAM,CAAApD,GAAG,CAACgG,GAAG,kBAAAC,MAAA,CAAkBjF,aAAa,CAACoC,EAAE,EAAI2C,UAAU,CAAC,CAE9D;AACA,KAAM,CAAA5B,eAAe,CAAC,CAAC,CAEvB,CAAA2B,eAAA,CAAAtF,KAAK,CAACoD,OAAO,UAAAkC,eAAA,iBAAbA,eAAA,CAAejC,IAAI,CAAC,CAClBC,QAAQ,CAAE,SAAS,CACnBC,OAAO,CAAE,IAAI,CACbC,MAAM,CAAE,OACV,CAAC,CAAC,CACJ,CAAC,IAAM,KAAAkC,eAAA,CACL;AACA,KAAM,CAAAlG,GAAG,CAACmG,IAAI,CAAC,eAAe,CAAEP,YAAY,CAAC,CAE7C;AACA,KAAM,CAAAzB,eAAe,CAAC,CAAC,CAEvB,CAAA+B,eAAA,CAAA1F,KAAK,CAACoD,OAAO,UAAAsC,eAAA,iBAAbA,eAAA,CAAerC,IAAI,CAAC,CAClBC,QAAQ,CAAE,SAAS,CACnBC,OAAO,CAAE,IAAI,CACbC,MAAM,CAAE,OACV,CAAC,CAAC,CACJ,CAEAnD,aAAa,CAAC,KAAK,CAAC,CACpBuF,SAAS,CAAC,CAAC,CACb,CAAE,MAAOnC,KAAU,CAAE,KAAAoC,eAAA,CAAAC,eAAA,CAAAC,oBAAA,CACnBlE,OAAO,CAAC4B,KAAK,CAAC,OAAO,CAAEA,KAAK,CAAC,CAC7B,CAAAoC,eAAA,CAAA7F,KAAK,CAACoD,OAAO,UAAAyC,eAAA,iBAAbA,eAAA,CAAexC,IAAI,CAAC,CAClBC,QAAQ,CAAE,OAAO,CACjBC,OAAO,CAAE,IAAI,CACbC,MAAM,CAAE,EAAAsC,eAAA,CAAArC,KAAK,CAACI,QAAQ,UAAAiC,eAAA,kBAAAC,oBAAA,CAAdD,eAAA,CAAgBzD,IAAI,UAAA0D,oBAAA,iBAApBA,oBAAA,CAAsBC,OAAO,GAAI,MAC3C,CAAC,CAAC,CACJ,CACF,CAAC,CAED,KAAM,CAAAJ,SAAS,CAAGA,CAAA,GAAM,CACtB3E,WAAW,CAAC,CACVC,QAAQ,CAAE,IAAI,CACdC,SAAS,CAAE,IAAI,CACfC,SAAS,CAAE,IAAI,CACfC,OAAO,CAAE,IAAI,CACbC,SAAS,CAAE,OAAO,CAClBC,OAAO,CAAE,OAAO,CAChBC,QAAQ,CAAE,KAAK,CACfC,UAAU,CAAE,OAAO,CACnBC,WAAW,CAAE,CACf,CAAC,CAAC,CACFjB,gBAAgB,CAAC,IAAI,CAAC,CACtBF,aAAa,CAAC,KAAK,CAAC,CACtB,CAAC,CAED,KAAM,CAAA0F,gBAAgB,CAAIC,SAAc,EAAK,CAC3C,KAAM,CAAAC,SAAS,CAAGjG,MAAM,CAACkG,IAAI,CAACC,KAAK,EAAIA,KAAK,CAACzD,EAAE,GAAKsD,SAAS,CAACG,KAAK,CAACzD,EAAE,CAAC,CACvE,GAAIuD,SAAS,CAAE,CACb1F,gBAAgB,CAAC0F,SAAS,CAAC,CAC3B5F,aAAa,CAAC,MAAM,CAAC,CAErB;AACAU,WAAW,CAAC,CACVC,QAAQ,CAAEiF,SAAS,CAACjF,QAAQ,EAAI,IAAI,CACpCC,SAAS,CAAEgF,SAAS,CAAChF,SAAS,EAAI,IAAI,CACtCC,SAAS,CAAE,GAAI,CAAAqD,IAAI,CAAC0B,SAAS,CAACrD,KAAK,CAAC,CACpCzB,OAAO,CAAE,GAAI,CAAAoD,IAAI,CAAC0B,SAAS,CAACpD,GAAG,CAAC,CAChCzB,SAAS,CAAEnD,iBAAiB,CAACgI,SAAS,CAACrD,KAAK,CAAE,OAAO,CAAC,CACtDvB,OAAO,CAAEpD,iBAAiB,CAACgI,SAAS,CAACpD,GAAG,CAAE,OAAO,CAAC,CAClDvB,QAAQ,CAAE,KAAK,CACfC,UAAU,CAAE,OAAO,CACnBC,WAAW,CAAE,CACf,CAAC,CAAC,CAEFrB,aAAa,CAAC,IAAI,CAAC,CACrB,CACF,CAAC,CAED,KAAM,CAAAiG,UAAU,CAAGA,CAAA,GAAM,CACvB/F,aAAa,CAAC,MAAM,CAAC,CACvB,CAAC,CAED,KAAM,CAAAgG,YAAY,CAAGA,CAAA,GAAM,CACzB,GAAI,CAAC/F,aAAa,CAAE,OAEpB7B,aAAa,CAAC,CACZqH,OAAO,iDAAAP,MAAA,CAAcjF,aAAa,CAACqC,KAAK,mBAAM,CAC9C2D,MAAM,CAAE,MAAM,CACdC,IAAI,CAAE,4BAA4B,CAClCC,MAAM,CAAE,KAAAA,CAAA,GAAY,CAClB,GAAI,KAAAC,eAAA,CACF;AACA,KAAM,CAAAnH,GAAG,CAACoH,MAAM,kBAAAnB,MAAA,CAAkBjF,aAAa,CAACoC,EAAE,CAAE,CAAC,CAErD;AACA,KAAM,CAAAe,eAAe,CAAC,CAAC,CACvBtD,aAAa,CAAC,KAAK,CAAC,CACpBuF,SAAS,CAAC,CAAC,CAEX,CAAAe,eAAA,CAAA3G,KAAK,CAACoD,OAAO,UAAAuD,eAAA,iBAAbA,eAAA,CAAetD,IAAI,CAAC,CAClBC,QAAQ,CAAE,SAAS,CACnBC,OAAO,CAAE,IAAI,CACbC,MAAM,CAAE,OACV,CAAC,CAAC,CACJ,CAAE,MAAOC,KAAU,CAAE,KAAAoD,gBAAA,CAAAC,qBAAA,CAAAC,eAAA,CACnBlF,OAAO,CAAC4B,KAAK,CAAC,OAAO,CAAEA,KAAK,CAAC,CAC7B,GAAI,CAAAD,MAAM,CAAIC,KAAK,CAACuD,MAAM,GAAK,GAAG,CAAG,aAAa,CAAG,EAAAH,gBAAA,CAAApD,KAAK,CAACI,QAAQ,UAAAgD,gBAAA,kBAAAC,qBAAA,CAAdD,gBAAA,CAAgBxE,IAAI,UAAAyE,qBAAA,iBAApBA,qBAAA,CAAsBd,OAAO,GAAI,MAAM,CAE5F,CAAAe,eAAA,CAAA/G,KAAK,CAACoD,OAAO,UAAA2D,eAAA,iBAAbA,eAAA,CAAe1D,IAAI,CAAC,CAClBC,QAAQ,CAAE,OAAO,CACjBC,OAAO,CAAE,IAAI,CACbC,MAAM,CAAEA,MACV,CAAC,CAAC,CACJ,CACF,CACF,CAAC,CAAC,CACJ,CAAC,CAED,KAAM,CAAAyD,cAAc,CAAG,KAAAA,CAAA,GAAY,CACjC,GAAI,CAACzG,aAAa,EAAI,CAACA,aAAa,CAACW,SAAS,CAAE,OAEhD,GAAI,CACF,GAAI,CAAA2C,WAAW,CAAGtD,aAAa,CAACsD,WAAW,CAE3C,GAAI,CAACA,WAAW,CAAE,CAChB;AACAjC,OAAO,CAACC,GAAG,CAAC,UAAU,CAAC,CACvB,KAAM,CAAAoF,cAAc,CAAG,KAAM,CAAA1H,GAAG,CAACmG,IAAI,CAAC,uBAAuB,CAAE,CAC7DwB,SAAS,CAAE3G,aAAa,CAACW,SAAS,CAClCiG,QAAQ,CAAE5G,aAAa,CAACU,QACxB;AACF,CAAC,CAAC,CAEF4C,WAAW,CAAGoD,cAAc,CAAC7E,IAAI,CAACyB,WAAW,CAC7CjC,OAAO,CAACC,GAAG,CAAC,WAAW,CAAE,CAAEgC,WAAY,CAAC,CAAC,CAEzC;AACA,KAAM,CAAAtE,GAAG,CAAC6H,KAAK,kBAAA5B,MAAA,CAAkBjF,aAAa,CAACoC,EAAE,eAAc,CAC7D0E,WAAW,CAAExD,WACf,CAAC,CAAC,CACFjC,OAAO,CAACC,GAAG,CAAC,2BAA2B,CAAC,CAExC;AACA6B,eAAe,CAAC,CAAC,CACnB,CAEA;AACA9B,OAAO,CAACC,GAAG,CAAC,QAAQ,CAAE,CAAEgC,WAAY,CAAC,CAAC,CAEtC,KAAM,CAAAyD,iBAAiB,CAAG,KAAM,CAAA/H,GAAG,CAAC4C,GAAG,CAAC,gBAAgB,CAAE,CACxDoF,MAAM,CAAE,CACNC,YAAY,CAAE3D,WAChB,CACF,CAAC,CAAC,CAEF,KAAM,CAAA4D,aAAa,CAAGH,iBAAiB,CAAClF,IAAI,CAE5C;AACA,GAAIyB,WAAW,CAAE,CACf7D,QAAQ,IAAAwF,MAAA,CAAIlG,MAAM,CAACoI,gBAAgB,SAAAlC,MAAA,CAAO3B,WAAW,EAAI,CACvD8D,KAAK,CAAE,CAAEC,SAAS,CAAEH,aAAa,CAAEI,OAAO,CAAE,CAAElF,EAAE,CAAE8E,aAAa,CAACvG,SAAU,CAAC,CAC7E,CAAC,CAAC,CACJ,CAEF,CAAE,MAAOsC,KAAK,CAAE,KAAAsE,eAAA,CACdlG,OAAO,CAAC4B,KAAK,CAAC,MAAM,CAAEA,KAAK,CAAC,CAC5B,CAAAsE,eAAA,CAAA/H,KAAK,CAACoD,OAAO,UAAA2E,eAAA,iBAAbA,eAAA,CAAe1E,IAAI,CAAC,CAClBC,QAAQ,CAAE,OAAO,CACjBC,OAAO,CAAE,MAAM,CACfC,MAAM,CAAE,gBAAgB,CACxBwE,IAAI,CAAE,IACR,CAAC,CAAC,CACJ,CACF,CAAC,CAED,KAAM,CAAAC,cAAc,CAAGA,CAAA,GAAM,CAC3BtJ,aAAa,CAAC,CACZqH,OAAO,CAAE,WAAW,CACpBQ,MAAM,CAAE,MAAM,CACdC,IAAI,CAAE,uBAAuB,CAC7BC,MAAM,CAAEA,CAAA,GAAM,CACZpC,cAAc,CAAC,CAAC,CAAE;AACpB,CACF,CAAC,CAAC,CACJ,CAAC,CAED,mBACE1E,KAAA,QAAKsI,SAAS,CAAC,gBAAgB,CAAAC,QAAA,eAC7BzI,IAAA,CAACX,KAAK,EAACqJ,GAAG,CAAEpI,KAAM,CAAE,CAAC,cACrBN,IAAA,CAAChB,aAAa,GAAE,CAAC,cAGjBgB,IAAA,QAAKwI,SAAS,CAAC,MAAM,CAACG,MAAM,MAAAF,QAAA,cAC1BzI,IAAA,QAAKwI,SAAS,CAAC,kDAAkD,CAAAC,QAAA,cAC/DvI,KAAA,QAAKsI,SAAS,CAAC,YAAY,CAAAC,QAAA,eACzBzI,IAAA,CAAClB,MAAM,EACL8J,KAAK,CAAC,0BAAM,CACZ7B,IAAI,CAAE3F,SAAS,CAAG,uBAAuB,CAAG,eAAgB,CAC5DyH,OAAO,CAAE5E,eAAgB,CACzBuE,SAAS,CAAC,oBAAoB,CAC9BM,QAAQ,MACRC,QAAQ,CAAE3H,SAAU,CACrB,CAAC,cACFpB,IAAA,CAAClB,MAAM,EACL8J,KAAK,CAAC,0BAAM,CACZ7B,IAAI,CAAC,YAAY,CACjB8B,OAAO,CAAEA,CAAA,GAAMlI,aAAa,CAAC,IAAI,CAAE,CACnC6H,SAAS,CAAC,kBAAkB,CAC7B,CAAC,EACC,CAAC,CACH,CAAC,CACH,CAAC,cAGNxI,IAAA,QAAKwI,SAAS,CAAC,MAAM,CAAAC,QAAA,cACnBzI,IAAA,QAAKwI,SAAS,CAAC,oBAAoB,CAAAC,QAAA,cACjCzI,IAAA,CAACpB,YAAY,EACXoK,OAAO,CAAE,CAACtK,aAAa,CAAEG,cAAc,CAAEF,iBAAiB,CAAE,CAC5DsK,MAAM,CAAC,MAAM,CACbC,WAAW,CAAC,cAAc,CAC1BC,MAAM,CAAC,OAAO,CACdC,QAAQ,CAAE,IAAK,CACfC,UAAU,CAAE,KAAM,CAClBC,aAAa,CAAE,CACbC,IAAI,CAAE,iBAAiB,CACvBC,MAAM,CAAE,OAAO,CACfC,KAAK,CAAE,8CACT,CAAE,CACFC,WAAW,CAAE,CACTC,IAAI,CAAE,SAAS,CAAE;AACjBC,KAAK,CAAE,MAAU;AACrB,CAAE,CACFC,eAAe,CAAE,CACfC,GAAG,CAAE,SAAS,CAAE;AAChBC,OAAO,CAAE,QAAS;AACpB,CAAE,CACFC,eAAe,CAAE,CACfC,IAAI,CAAE,SAAS,CAAE;AACjBC,MAAM,CAAE,SAAS,CAAE;AACnBC,MAAM,CAAE,KAAM;AAChB,CAAE,CACFC,aAAa,CAAE,CACbC,UAAU,CAAE,CACVC,IAAI,CAAE,IAAI,CACVC,KAAK,CAAEtG,eACT,CAAC,CACDuG,OAAO,CAAE,CACPF,IAAI,CAAE,IAAI,CACVC,KAAK,CAAEA,CAAA,GAAM5J,aAAa,CAAC,IAAI,CACjC,CACF,CAAE,CACF8J,WAAW,CAAC,UAAU,CACtBC,WAAW,CAAC,UAAU,CACtBlK,MAAM,CAAEA,MAAO,CACfmK,UAAU,CAAEpE,gBAAiB,CAC7BqE,UAAU,CAAE,IAAK,CACjBC,YAAY,CAAE,IAAK,CACnBC,YAAY,CAAE,IAAK,CACnBC,aAAa,CAAE,CACbC,UAAU,CAAE,CAAC,CAAC,CAAE,CAAC,CAAE,CAAC,CAAE,CAAC,CAAE,CAAC,CAAE,CAAC,CAAC,CAC9BpJ,SAAS,CAAE,OAAO,CAClBC,OAAO,CAAE,OACX,CAAE,CACH,CAAC,CACC,CAAC,CACH,CAAC,cAGN3B,KAAA,CAAChB,MAAM,EACL4H,MAAM,CACJlG,UAAU,GAAK,KAAK,CAAG,MAAM,CAC7BA,UAAU,GAAK,MAAM,CAAG,MAAM,CAAG,MAClC,CACDqK,OAAO,CAAEvK,UAAW,CACpBwK,KAAK,CAAE,CAAEC,KAAK,CAAE,OAAQ,CAAE,CAC1BC,MAAM,CAAEA,CAAA,GAAM,CACZzK,aAAa,CAAC,KAAK,CAAC,CACpBuF,SAAS,CAAC,CAAC,CACb,CAAE,CACFmF,MAAM,cACJrL,IAAA,QAAKwI,SAAS,CAAC,YAAY,CAAAC,QAAA,CACxB7H,UAAU,GAAK,MAAM,cACpBV,KAAA,CAAAE,SAAA,EAAAqI,QAAA,eACEzI,IAAA,CAAClB,MAAM,EACL8J,KAAK,CAAC,cAAI,CACV7B,IAAI,CAAC,mBAAmB,CACxByB,SAAS,CAAC,kBAAkB,CAC5BK,OAAO,CAAEtB,cAAe,CACzB,CAAC,cACFvH,IAAA,CAAClB,MAAM,EACL8J,KAAK,CAAC,cAAI,CACV7B,IAAI,CAAC,cAAc,CACnB8B,OAAO,CAAEjC,UAAW,CACrB,CAAC,cACF5G,IAAA,CAAClB,MAAM,EACL8J,KAAK,CAAC,cAAI,CACV7B,IAAI,CAAC,aAAa,CAClByB,SAAS,CAAC,iBAAiB,CAC3BK,OAAO,CAAEhC,YAAa,CACvB,CAAC,cACF7G,IAAA,CAAClB,MAAM,EACL8J,KAAK,CAAC,cAAI,CACV7B,IAAI,CAAC,aAAa,CAClByB,SAAS,CAAC,mBAAmB,CAC7BK,OAAO,CAAEA,CAAA,GAAM,CACblI,aAAa,CAAC,KAAK,CAAC,CACpBuF,SAAS,CAAC,CAAC,CACb,CAAE,CACH,CAAC,EACF,CAAC,cAEHhG,KAAA,CAAAE,SAAA,EAAAqI,QAAA,eACEzI,IAAA,CAAClB,MAAM,EACL8J,KAAK,CAAC,cAAI,CACV7B,IAAI,CAAC,aAAa,CAClByB,SAAS,CAAC,mBAAmB,CAC7BK,OAAO,CAAEA,CAAA,GAAM,CACblI,aAAa,CAAC,KAAK,CAAC,CACpBuF,SAAS,CAAC,CAAC,CACb,CAAE,CACH,CAAC,cACFlG,IAAA,CAAClB,MAAM,EACL8J,KAAK,CAAEhI,UAAU,GAAK,MAAM,CAAG,IAAI,CAAG,IAAK,CAC3CmG,IAAI,CAAC,aAAa,CAClB8B,OAAO,CAAEjI,UAAU,GAAK,MAAM,CAAG2H,cAAc,CAAG3D,cAAe,CAClE,CAAC,EACF,CACH,CACE,CACN,CAAA6D,QAAA,eAEDvI,KAAA,QAAKsI,SAAS,CAAC,MAAM,CAAAC,QAAA,eACnBzI,IAAA,QAAKwI,SAAS,CAAC,gBAAgB,CAAAC,QAAA,cAC7BvI,KAAA,QAAKsI,SAAS,CAAC,OAAO,CAAAC,QAAA,eACpBzI,IAAA,UAAOsL,OAAO,CAAC,QAAQ,CAAC9C,SAAS,CAAC,sBAAsB,CAAAC,QAAA,CAAC,sBAEzD,CAAO,CAAC,cACRzI,IAAA,CAACb,QAAQ,EACP+D,EAAE,CAAC,QAAQ,CACXuB,KAAK,CAAEnD,QAAQ,CAACE,QAAS,CACzB+J,OAAO,CAAEvK,OAAQ,CACjBwK,QAAQ,CAAGC,CAAC,EAAKlH,iBAAiB,CAAC,UAAU,CAAEkH,CAAC,CAAChH,KAAK,CAAE,CACxDiH,WAAW,CAAC,MAAM,CAClBC,WAAW,CAAC,IAAI,CAChBC,WAAW,CAAC,sCAAQ,CACpBpD,SAAS,CAAC,QAAQ,CAClBO,QAAQ,CAAEnI,UAAU,GAAK,MAAO,CACjC,CAAC,EACC,CAAC,CACH,CAAC,cAENZ,IAAA,QAAKwI,SAAS,CAAC,gBAAgB,CAAAC,QAAA,cAC7BvI,KAAA,QAAKsI,SAAS,CAAC,OAAO,CAAAC,QAAA,eACpBzI,IAAA,UAAOsL,OAAO,CAAC,SAAS,CAAC9C,SAAS,CAAC,sBAAsB,CAAAC,QAAA,CAAC,gBAE1D,CAAO,CAAC,cACRzI,IAAA,CAACb,QAAQ,EACP+D,EAAE,CAAC,SAAS,CACZuB,KAAK,CAAEnD,QAAQ,CAACG,SAAU,CAC1B8J,OAAO,CAAErK,QAAS,CAClBsK,QAAQ,CAAGC,CAAC,EAAKlH,iBAAiB,CAAC,WAAW,CAAEkH,CAAC,CAAChH,KAAK,CAAE,CACzDiH,WAAW,CAAC,MAAM,CAClBC,WAAW,CAAC,IAAI,CAChBC,WAAW,CAAC,gCAAO,CACnBpD,SAAS,CAAC,QAAQ,CAClBO,QAAQ,CAAEnI,UAAU,GAAK,MAAO,CACjC,CAAC,EACC,CAAC,CACH,CAAC,cAENZ,IAAA,QAAKwI,SAAS,CAAC,iBAAiB,CAAAC,QAAA,cAC9BvI,KAAA,QAAKsI,SAAS,CAAC,OAAO,CAAAC,QAAA,eACpBzI,IAAA,UAAOsL,OAAO,CAAC,WAAW,CAAC9C,SAAS,CAAC,sBAAsB,CAAAC,QAAA,CAAC,4BAE5D,CAAO,CAAC,cACRzI,IAAA,CAACjB,QAAQ,EACPmE,EAAE,CAAC,WAAW,CACduB,KAAK,CAAEnD,QAAQ,CAACI,SAAU,CAC1B8J,QAAQ,CAAGC,CAAC,EAAKlH,iBAAiB,CAAC,WAAW,CAAEkH,CAAC,CAAChH,KAAa,CAAE,CACjEoH,UAAU,CAAC,UAAU,CACrBC,QAAQ,MACRtD,SAAS,CAAC,QAAQ,CAClBO,QAAQ,CAAEnI,UAAU,GAAK,MAAO,CACjC,CAAC,EACC,CAAC,CACH,CAAC,cAENZ,IAAA,QAAKwI,SAAS,CAAC,iBAAiB,CAAAC,QAAA,cAC9BvI,KAAA,QAAKsI,SAAS,CAAC,OAAO,CAAAC,QAAA,eACpBzI,IAAA,UAAOsL,OAAO,CAAC,SAAS,CAAC9C,SAAS,CAAC,sBAAsB,CAAAC,QAAA,CAAC,4BAE1D,CAAO,CAAC,cACRzI,IAAA,CAACjB,QAAQ,EACPmE,EAAE,CAAC,SAAS,CACZuB,KAAK,CAAEnD,QAAQ,CAACK,OAAQ,CACxB6J,QAAQ,CAAGC,CAAC,EAAKlH,iBAAiB,CAAC,SAAS,CAAEkH,CAAC,CAAChH,KAAa,CAAE,CAC/DoH,UAAU,CAAC,UAAU,CACrBC,QAAQ,MACRtD,SAAS,CAAC,QAAQ,CAClBO,QAAQ,CAAEnI,UAAU,GAAK,MAAO,CACjC,CAAC,EACC,CAAC,CACH,CAAC,cAENZ,IAAA,QAAKwI,SAAS,CAAC,gBAAgB,CAAAC,QAAA,cAC7BvI,KAAA,QAAKsI,SAAS,CAAC,OAAO,CAAAC,QAAA,eACpBzI,IAAA,UAAOsL,OAAO,CAAC,WAAW,CAAC9C,SAAS,CAAC,sBAAsB,CAAAC,QAAA,CAAC,0BAE5D,CAAO,CAAC,cACRzI,IAAA,CAACZ,SAAS,EACR8D,EAAE,CAAC,WAAW,CACduC,IAAI,CAAC,MAAM,CACXhB,KAAK,CAAEnD,QAAQ,CAACM,SAAU,CAC1B4J,QAAQ,CAAGC,CAAC,EAAKlH,iBAAiB,CAAC,WAAW,CAAEkH,CAAC,CAACM,MAAM,CAACtH,KAAK,CAAE,CAChE+D,SAAS,CAAC,QAAQ,CAClBO,QAAQ,CAAEnI,UAAU,GAAK,MAAO,CACjC,CAAC,EACC,CAAC,CACH,CAAC,cAENZ,IAAA,QAAKwI,SAAS,CAAC,gBAAgB,CAAAC,QAAA,cAC7BvI,KAAA,QAAKsI,SAAS,CAAC,OAAO,CAAAC,QAAA,eACpBzI,IAAA,UAAOsL,OAAO,CAAC,SAAS,CAAC9C,SAAS,CAAC,sBAAsB,CAAAC,QAAA,CAAC,0BAE1D,CAAO,CAAC,cACRzI,IAAA,CAACZ,SAAS,EACR8D,EAAE,CAAC,SAAS,CACZuC,IAAI,CAAC,MAAM,CACXhB,KAAK,CAAEnD,QAAQ,CAACO,OAAQ,CACxB2J,QAAQ,CAAGC,CAAC,EAAKlH,iBAAiB,CAAC,SAAS,CAAEkH,CAAC,CAACM,MAAM,CAACtH,KAAK,CAAE,CAC9D+D,SAAS,CAAC,QAAQ,CAClBO,QAAQ,CAAEnI,UAAU,GAAK,MAAO,CACjC,CAAC,EACC,CAAC,CACH,CAAC,EACH,CAAC,CAGLA,UAAU,GAAK,MAAM,eACpBV,KAAA,QAAKsI,SAAS,CAAC,MAAM,CAAAC,QAAA,eACnBzI,IAAA,QAAKwI,SAAS,CAAC,QAAQ,CAAAC,QAAA,cACrBvI,KAAA,QAAKsI,SAAS,CAAC,gBAAgB,CAAAC,QAAA,eAC7BzI,IAAA,CAACV,QAAQ,EACP0M,OAAO,CAAC,UAAU,CAClBC,OAAO,CAAE3K,QAAQ,CAACQ,QAAS,CAC3B0J,QAAQ,CAAGC,CAAC,EAAKlH,iBAAiB,CAAC,UAAU,CAAEkH,CAAC,CAACQ,OAAO,EAAI,KAAK,CAAE,CACpE,CAAC,cACFjM,IAAA,UAAOsL,OAAO,CAAC,UAAU,CAAC9C,SAAS,CAAC,gBAAgB,CAAAC,QAAA,CAAC,0BAErD,CAAO,CAAC,EACL,CAAC,CACH,CAAC,CAELnH,QAAQ,CAACQ,QAAQ,eAChB5B,KAAA,CAAAE,SAAA,EAAAqI,QAAA,eACEzI,IAAA,QAAKwI,SAAS,CAAC,gBAAgB,CAAAC,QAAA,cAC7BvI,KAAA,QAAKsI,SAAS,CAAC,OAAO,CAAAC,QAAA,eACpBzI,IAAA,UAAOsL,OAAO,CAAC,YAAY,CAAC9C,SAAS,CAAC,sBAAsB,CAAAC,QAAA,CAAC,0BAE7D,CAAO,CAAC,cACRzI,IAAA,CAACb,QAAQ,EACP+D,EAAE,CAAC,YAAY,CACfuB,KAAK,CAAEnD,QAAQ,CAACS,UAAW,CAC3BwJ,OAAO,CAAE,CACP,CAAE3C,KAAK,CAAE,IAAI,CAAEnE,KAAK,CAAE,OAAQ,CAAC,CAC/B,CAAEmE,KAAK,CAAE,IAAI,CAAEnE,KAAK,CAAE,QAAS,CAAC,CAChC,CAAEmE,KAAK,CAAE,IAAI,CAAEnE,KAAK,CAAE,SAAU,CAAC,CACjC,CACF+G,QAAQ,CAAGC,CAAC,EAAKlH,iBAAiB,CAAC,YAAY,CAAEkH,CAAC,CAAChH,KAAK,CAAE,CAC1D+D,SAAS,CAAC,QAAQ,CAClB0C,KAAK,CAAE,CAAEC,KAAK,CAAE,KAAM,CAAE,CACzB,CAAC,EACC,CAAC,CACH,CAAC,cAENnL,IAAA,QAAKwI,SAAS,CAAC,gBAAgB,CAAAC,QAAA,cAC7BvI,KAAA,QAAKsI,SAAS,CAAC,OAAO,CAAAC,QAAA,eACpBzI,IAAA,UAAOsL,OAAO,CAAC,aAAa,CAAC9C,SAAS,CAAC,sBAAsB,CAAAC,QAAA,CAAC,0BAE9D,CAAO,CAAC,cACRzI,IAAA,CAACT,WAAW,EACV2D,EAAE,CAAC,aAAa,CAChBuB,KAAK,CAAEnD,QAAQ,CAACU,WAAY,CAC5BkK,aAAa,CAAGT,CAAC,EAAKlH,iBAAiB,CAAC,aAAa,CAAEkH,CAAC,CAAChH,KAAK,EAAI,CAAC,CAAE,CACrE0H,GAAG,CAAE,CAAE,CACPC,GAAG,CAAE,EAAG,CACRC,UAAU,CAAE,CAAElB,KAAK,CAAE,KAAM,CAAE,CAC9B,CAAC,EACC,CAAC,CACH,CAAC,cAENnL,IAAA,QAAKwI,SAAS,CAAC,QAAQ,CAAAC,QAAA,cACrBzI,IAAA,QAAKwI,SAAS,CAAC,6BAA6B,CAAAC,QAAA,cAC1CvI,KAAA,MAAGsI,SAAS,CAAC,2BAA2B,CAAAC,QAAA,eACtCzI,IAAA,MAAGwI,SAAS,CAAC,wBAAwB,CAAI,CAAC,CACzClH,QAAQ,CAACS,UAAU,GAAK,OAAO,oCAAAgE,MAAA,CAAazE,QAAQ,CAACU,WAAW,WAAI,CACpEV,QAAQ,CAACS,UAAU,GAAK,QAAQ,oCAAAgE,MAAA,CAAazE,QAAQ,CAACU,WAAW,WAAI,CACrEV,QAAQ,CAACS,UAAU,GAAK,SAAS,oCAAAgE,MAAA,CAAazE,QAAQ,CAACU,WAAW,WAAI,EACtE,CAAC,CACD,CAAC,CACH,CAAC,EACN,CACH,EACE,CACN,EACK,CAAC,EACN,CAAC,CAEV,CAAC,CAED,cAAe,CAAA3B,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
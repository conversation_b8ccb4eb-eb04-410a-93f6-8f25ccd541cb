#!/bin/bash
echo "📦 [圖片備份] $(date +%F)"

# 建立壓縮目錄
UPLOADS_SRC="/app/uploads"
TODAY=$(date +%F)
ARCHIVE_NAME="uploads-${TODAY}.tar.gz"
ARCHIVE_PATH="/tmp/${ARCHIVE_NAME}"
UPLOADS_DST="dropbox:backup-uploads/uploads"

# 壓縮 jpg/jpeg/png/gif 圖片
tar -czf "$ARCHIVE_PATH" --absolute-names --transform "s|^/||" \
    $(find "$UPLOADS_SRC" \( -iname "*.jpg" -o -iname "*.jpeg" -o -iname "*.png" -o -iname "*.gif" \))

# 上傳壓縮檔到遠端儲存
rclone copy "$ARCHIVE_PATH" "$UPLOADS_DST" --log-level INFO

# 清理本地暫存壓縮檔
rm -f "$ARCHIVE_PATH"

echo "✅ 完成圖片壓縮並備份：$ARCHIVE_NAME"
{"ast": null, "code": "import { formatDistance } from \"./ja/_lib/formatDistance.js\";\nimport { formatLong } from \"./ja/_lib/formatLong.js\";\nimport { formatRelative } from \"./ja/_lib/formatRelative.js\";\nimport { localize } from \"./ja/_lib/localize.js\";\nimport { match } from \"./ja/_lib/match.js\";\n\n/**\n * @category Locales\n * @summary Japanese locale.\n * @language Japanese\n * @iso-639-2 jpn\n * <AUTHOR> [@DeMuu](https://github.com/DeMuu)\n * <AUTHOR> [@ykzts](https://github.com/ykzts)\n * <AUTHOR> [@mesqueeb](https://github.com/mesqueeb)\n * <AUTHOR> [@skyuplam](https://github.com/skyuplam)\n * <AUTHOR> [@so99ynoodles](https://github.com/so99ynoodles)\n */\nexport const ja = {\n  code: \"ja\",\n  formatDistance: formatDistance,\n  formatLong: formatLong,\n  formatRelative: formatRelative,\n  localize: localize,\n  match: match,\n  options: {\n    weekStartsOn: 0 /* Sunday */,\n    firstWeekContainsDate: 1\n  }\n};\n\n// Fallback for modularized imports:\nexport default ja;", "map": {"version": 3, "names": ["formatDistance", "formatLong", "formatRelative", "localize", "match", "ja", "code", "options", "weekStartsOn", "firstWeekContainsDate"], "sources": ["C:/Users/<USER>/Desktop/demo-git/demo-react/my-fullstack-app/frontend/node_modules/date-fns/locale/ja.js"], "sourcesContent": ["import { formatDistance } from \"./ja/_lib/formatDistance.js\";\nimport { formatLong } from \"./ja/_lib/formatLong.js\";\nimport { formatRelative } from \"./ja/_lib/formatRelative.js\";\nimport { localize } from \"./ja/_lib/localize.js\";\nimport { match } from \"./ja/_lib/match.js\";\n\n/**\n * @category Locales\n * @summary Japanese locale.\n * @language Japanese\n * @iso-639-2 jpn\n * <AUTHOR> [@DeMuu](https://github.com/DeMuu)\n * <AUTHOR> [@ykzts](https://github.com/ykzts)\n * <AUTHOR> [@mesqueeb](https://github.com/mesqueeb)\n * <AUTHOR> [@skyuplam](https://github.com/skyuplam)\n * <AUTHOR> [@so99ynoodles](https://github.com/so99ynoodles)\n */\nexport const ja = {\n  code: \"ja\",\n  formatDistance: formatDistance,\n  formatLong: formatLong,\n  formatRelative: formatRelative,\n  localize: localize,\n  match: match,\n  options: {\n    weekStartsOn: 0 /* Sunday */,\n    firstWeekContainsDate: 1,\n  },\n};\n\n// Fallback for modularized imports:\nexport default ja;\n"], "mappings": "AAAA,SAASA,cAAc,QAAQ,6BAA6B;AAC5D,SAASC,UAAU,QAAQ,yBAAyB;AACpD,SAASC,cAAc,QAAQ,6BAA6B;AAC5D,SAASC,QAAQ,QAAQ,uBAAuB;AAChD,SAASC,KAAK,QAAQ,oBAAoB;;AAE1C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMC,EAAE,GAAG;EAChBC,IAAI,EAAE,IAAI;EACVN,cAAc,EAAEA,cAAc;EAC9BC,UAAU,EAAEA,UAAU;EACtBC,cAAc,EAAEA,cAAc;EAC9BC,QAAQ,EAAEA,QAAQ;EAClBC,KAAK,EAAEA,KAAK;EACZG,OAAO,EAAE;IACPC,YAAY,EAAE,CAAC,CAAC;IAChBC,qBAAqB,EAAE;EACzB;AACF,CAAC;;AAED;AACA,eAAeJ,EAAE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
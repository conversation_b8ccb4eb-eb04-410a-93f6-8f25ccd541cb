{"ast": null, "code": "const formatDistanceLocale = {\n  lessThanXSeconds: {\n    one: \"1 сониядан кам\",\n    other: \"{{count}} сониядан кам\"\n  },\n  xSeconds: {\n    one: \"1 сония\",\n    other: \"{{count}} сония\"\n  },\n  halfAMinute: \"ярим дақиқа\",\n  lessThanXMinutes: {\n    one: \"1 дақиқадан кам\",\n    other: \"{{count}} дақиқадан кам\"\n  },\n  xMinutes: {\n    one: \"1 дақиқа\",\n    other: \"{{count}} дақиқа\"\n  },\n  aboutXHours: {\n    one: \"тахминан 1 соат\",\n    other: \"тахминан {{count}} соат\"\n  },\n  xHours: {\n    one: \"1 соат\",\n    other: \"{{count}} соат\"\n  },\n  xDays: {\n    one: \"1 кун\",\n    other: \"{{count}} кун\"\n  },\n  aboutXWeeks: {\n    one: \"тахминан 1 хафта\",\n    other: \"тахминан {{count}} хафта\"\n  },\n  xWeeks: {\n    one: \"1 хафта\",\n    other: \"{{count}} хафта\"\n  },\n  aboutXMonths: {\n    one: \"тахминан 1 ой\",\n    other: \"тахминан {{count}} ой\"\n  },\n  xMonths: {\n    one: \"1 ой\",\n    other: \"{{count}} ой\"\n  },\n  aboutXYears: {\n    one: \"тахминан 1 йил\",\n    other: \"тахминан {{count}} йил\"\n  },\n  xYears: {\n    one: \"1 йил\",\n    other: \"{{count}} йил\"\n  },\n  overXYears: {\n    one: \"1 йилдан кўп\",\n    other: \"{{count}} йилдан кўп\"\n  },\n  almostXYears: {\n    one: \"деярли 1 йил\",\n    other: \"деярли {{count}} йил\"\n  }\n};\nexport const formatDistance = (token, count, options) => {\n  let result;\n  const tokenValue = formatDistanceLocale[token];\n  if (typeof tokenValue === \"string\") {\n    result = tokenValue;\n  } else if (count === 1) {\n    result = tokenValue.one;\n  } else {\n    result = tokenValue.other.replace(\"{{count}}\", String(count));\n  }\n  if (options !== null && options !== void 0 && options.addSuffix) {\n    if (options.comparison && options.comparison > 0) {\n      return result + \"дан кейин\";\n    } else {\n      return result + \" олдин\";\n    }\n  }\n  return result;\n};", "map": {"version": 3, "names": ["formatDistanceLocale", "lessThanXSeconds", "one", "other", "xSeconds", "halfAMinute", "lessThanXMinutes", "xMinutes", "aboutXHours", "xHours", "xDays", "aboutXWeeks", "xWeeks", "aboutXMonths", "xMonths", "aboutXYears", "xYears", "overXYears", "almostXYears", "formatDistance", "token", "count", "options", "result", "tokenValue", "replace", "String", "addSuffix", "comparison"], "sources": ["C:/Users/<USER>/Desktop/demo-git/demo-react/my-fullstack-app/frontend/node_modules/date-fns/locale/uz-Cyrl/_lib/formatDistance.js"], "sourcesContent": ["const formatDistanceLocale = {\n  lessThanXSeconds: {\n    one: \"1 сониядан кам\",\n    other: \"{{count}} сониядан кам\",\n  },\n\n  xSeconds: {\n    one: \"1 сония\",\n    other: \"{{count}} сония\",\n  },\n\n  halfAMinute: \"ярим дақиқа\",\n\n  lessThanXMinutes: {\n    one: \"1 дақиқадан кам\",\n    other: \"{{count}} дақиқадан кам\",\n  },\n\n  xMinutes: {\n    one: \"1 дақиқа\",\n    other: \"{{count}} дақиқа\",\n  },\n\n  aboutXHours: {\n    one: \"тахминан 1 соат\",\n    other: \"тахминан {{count}} соат\",\n  },\n\n  xHours: {\n    one: \"1 соат\",\n    other: \"{{count}} соат\",\n  },\n\n  xDays: {\n    one: \"1 кун\",\n    other: \"{{count}} кун\",\n  },\n\n  aboutXWeeks: {\n    one: \"тахминан 1 хафта\",\n    other: \"тахминан {{count}} хафта\",\n  },\n\n  xWeeks: {\n    one: \"1 хафта\",\n    other: \"{{count}} хафта\",\n  },\n\n  aboutXMonths: {\n    one: \"тахминан 1 ой\",\n    other: \"тахминан {{count}} ой\",\n  },\n\n  xMonths: {\n    one: \"1 ой\",\n    other: \"{{count}} ой\",\n  },\n\n  aboutXYears: {\n    one: \"тахминан 1 йил\",\n    other: \"тахминан {{count}} йил\",\n  },\n\n  xYears: {\n    one: \"1 йил\",\n    other: \"{{count}} йил\",\n  },\n\n  overXYears: {\n    one: \"1 йилдан кўп\",\n    other: \"{{count}} йилдан кўп\",\n  },\n\n  almostXYears: {\n    one: \"деярли 1 йил\",\n    other: \"деярли {{count}} йил\",\n  },\n};\n\nexport const formatDistance = (token, count, options) => {\n  let result;\n\n  const tokenValue = formatDistanceLocale[token];\n  if (typeof tokenValue === \"string\") {\n    result = tokenValue;\n  } else if (count === 1) {\n    result = tokenValue.one;\n  } else {\n    result = tokenValue.other.replace(\"{{count}}\", String(count));\n  }\n\n  if (options?.addSuffix) {\n    if (options.comparison && options.comparison > 0) {\n      return result + \"дан кейин\";\n    } else {\n      return result + \" олдин\";\n    }\n  }\n\n  return result;\n};\n"], "mappings": "AAAA,MAAMA,oBAAoB,GAAG;EAC3BC,gBAAgB,EAAE;IAChBC,GAAG,EAAE,gBAAgB;IACrBC,KAAK,EAAE;EACT,CAAC;EAEDC,QAAQ,EAAE;IACRF,GAAG,EAAE,SAAS;IACdC,KAAK,EAAE;EACT,CAAC;EAEDE,WAAW,EAAE,aAAa;EAE1BC,gBAAgB,EAAE;IAChBJ,GAAG,EAAE,iBAAiB;IACtBC,KAAK,EAAE;EACT,CAAC;EAEDI,QAAQ,EAAE;IACRL,GAAG,EAAE,UAAU;IACfC,KAAK,EAAE;EACT,CAAC;EAEDK,WAAW,EAAE;IACXN,GAAG,EAAE,iBAAiB;IACtBC,KAAK,EAAE;EACT,CAAC;EAEDM,MAAM,EAAE;IACNP,GAAG,EAAE,QAAQ;IACbC,KAAK,EAAE;EACT,CAAC;EAEDO,KAAK,EAAE;IACLR,GAAG,EAAE,OAAO;IACZC,KAAK,EAAE;EACT,CAAC;EAEDQ,WAAW,EAAE;IACXT,GAAG,EAAE,kBAAkB;IACvBC,KAAK,EAAE;EACT,CAAC;EAEDS,MAAM,EAAE;IACNV,GAAG,EAAE,SAAS;IACdC,KAAK,EAAE;EACT,CAAC;EAEDU,YAAY,EAAE;IACZX,GAAG,EAAE,eAAe;IACpBC,KAAK,EAAE;EACT,CAAC;EAEDW,OAAO,EAAE;IACPZ,GAAG,EAAE,MAAM;IACXC,KAAK,EAAE;EACT,CAAC;EAEDY,WAAW,EAAE;IACXb,GAAG,EAAE,gBAAgB;IACrBC,KAAK,EAAE;EACT,CAAC;EAEDa,MAAM,EAAE;IACNd,GAAG,EAAE,OAAO;IACZC,KAAK,EAAE;EACT,CAAC;EAEDc,UAAU,EAAE;IACVf,GAAG,EAAE,cAAc;IACnBC,KAAK,EAAE;EACT,CAAC;EAEDe,YAAY,EAAE;IACZhB,GAAG,EAAE,cAAc;IACnBC,KAAK,EAAE;EACT;AACF,CAAC;AAED,OAAO,MAAMgB,cAAc,GAAGA,CAACC,KAAK,EAAEC,KAAK,EAAEC,OAAO,KAAK;EACvD,IAAIC,MAAM;EAEV,MAAMC,UAAU,GAAGxB,oBAAoB,CAACoB,KAAK,CAAC;EAC9C,IAAI,OAAOI,UAAU,KAAK,QAAQ,EAAE;IAClCD,MAAM,GAAGC,UAAU;EACrB,CAAC,MAAM,IAAIH,KAAK,KAAK,CAAC,EAAE;IACtBE,MAAM,GAAGC,UAAU,CAACtB,GAAG;EACzB,CAAC,MAAM;IACLqB,MAAM,GAAGC,UAAU,CAACrB,KAAK,CAACsB,OAAO,CAAC,WAAW,EAAEC,MAAM,CAACL,KAAK,CAAC,CAAC;EAC/D;EAEA,IAAIC,OAAO,aAAPA,OAAO,eAAPA,OAAO,CAAEK,SAAS,EAAE;IACtB,IAAIL,OAAO,CAACM,UAAU,IAAIN,OAAO,CAACM,UAAU,GAAG,CAAC,EAAE;MAChD,OAAOL,MAAM,GAAG,WAAW;IAC7B,CAAC,MAAM;MACL,OAAOA,MAAM,GAAG,QAAQ;IAC1B;EACF;EAEA,OAAOA,MAAM;AACf,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
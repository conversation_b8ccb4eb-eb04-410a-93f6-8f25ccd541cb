using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using MyApi.Models;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;

namespace MyApi.Controllers
{
    /// <summary>
    /// 備份管理控制器
    /// </summary>
    [ApiController]
    [Route("api/[controller]")]
    [Authorize(Roles = "Admin")] // 限制管理員角色
    public class BackupController : ControllerBase
    {
        private readonly string _backupPath;
        private readonly ILogger<BackupController> _logger;

        public BackupController(ILogger<BackupController> logger)
        {
            _logger = logger;
            _backupPath = "/app/backups"; // Docker 容器內的備份路徑
        }

        /// <summary>
        /// 獲取備份檔案清單
        /// </summary>
        /// <returns>備份檔案清單</returns>
        [HttpGet("list")]
        public async Task<ActionResult<IEnumerable<BackupFileDto>>> GetBackupList()
        {
            try
            {
                _logger.LogInformation("開始獲取備份檔案清單");

                // 檢查備份目錄是否存在
                if (!Directory.Exists(_backupPath))
                {
                    _logger.LogWarning("備份目錄不存在: {BackupPath}", _backupPath);
                    return Ok(new List<BackupFileDto>());
                }

                // 獲取所有 .sql.gz 檔案
                var backupFiles = Directory.GetFiles(_backupPath, "*.sql.gz", SearchOption.AllDirectories)
                    .Select(filePath =>
                    {
                        var fileInfo = new FileInfo(filePath);
                        return new BackupFileDto
                        {
                            FileName = fileInfo.Name,
                            SizeMB = Math.Round(fileInfo.Length / (1024.0 * 1024.0), 2),
                            LastModified = fileInfo.LastWriteTime
                        };
                    })
                    .OrderByDescending(f => f.LastModified)
                    .ToList();

                _logger.LogInformation("找到 {Count} 個備份檔案", backupFiles.Count);

                return Ok(backupFiles);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "獲取備份檔案清單時發生錯誤");
                return StatusCode(500, new { error = "獲取備份檔案清單失敗", details = ex.Message });
            }
        }

        /// <summary>
        /// 下載備份檔案
        /// </summary>
        /// <param name="file">檔案名稱</param>
        /// <returns>檔案流</returns>
        [HttpGet("download")]
        public async Task<IActionResult> DownloadBackup([FromQuery] string file)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(file))
                {
                    return BadRequest(new { error = "檔案名稱不能為空" });
                }

                // 安全檢查：只允許 .sql.gz 檔案
                if (!file.EndsWith(".sql.gz", StringComparison.OrdinalIgnoreCase))
                {
                    return BadRequest(new { error = "只允許下載 .sql.gz 檔案" });
                }

                // 防止路徑遍歷攻擊
                var fileName = Path.GetFileName(file);
                var filePath = Path.Combine(_backupPath, fileName);

                // 確保檔案路徑在備份目錄內
                var fullBackupPath = Path.GetFullPath(_backupPath);
                var fullFilePath = Path.GetFullPath(filePath);
                
                if (!fullFilePath.StartsWith(fullBackupPath))
                {
                    _logger.LogWarning("嘗試訪問備份目錄外的檔案: {FilePath}", filePath);
                    return BadRequest(new { error = "無效的檔案路徑" });
                }

                if (!System.IO.File.Exists(filePath))
                {
                    _logger.LogWarning("備份檔案不存在: {FilePath}", filePath);
                    return NotFound(new { error = "檔案不存在" });
                }

                _logger.LogInformation("開始下載備份檔案: {FileName}", fileName);

                var fileBytes = await System.IO.File.ReadAllBytesAsync(filePath);
                
                return File(fileBytes, "application/gzip", fileName);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "下載備份檔案時發生錯誤: {FileName}", file);
                return StatusCode(500, new { error = "下載檔案失敗", details = ex.Message });
            }
        }

        /// <summary>
        /// 獲取備份狀態
        /// </summary>
        /// <returns>備份狀態資訊</returns>
        [HttpGet("status")]
        public async Task<ActionResult<BackupStatusDto>> GetBackupStatus()
        {
            try
            {
                _logger.LogInformation("開始獲取備份狀態");

                var status = new BackupStatusDto
                {
                    HasTodayBackup = false,
                    TotalBackupFiles = 0
                };

                // 檢查備份目錄是否存在
                if (!Directory.Exists(_backupPath))
                {
                    _logger.LogWarning("備份目錄不存在: {BackupPath}", _backupPath);
                    return Ok(status);
                }

                // 獲取所有備份檔案
                var backupFiles = Directory.GetFiles(_backupPath, "*.sql.gz");
                status.TotalBackupFiles = backupFiles.Length;

                if (backupFiles.Length > 0)
                {
                    // 獲取最新備份時間
                    var latestFile = backupFiles
                        .Select(f => new FileInfo(f))
                        .OrderByDescending(f => f.LastWriteTime)
                        .First();
                    
                    status.LatestBackupTime = latestFile.LastWriteTime;

                    // 檢查今日是否有備份
                    var today = DateTime.Now.Date;
                    var todayString = today.ToString("yyyy-MM-dd");

                    var todayBackup = backupFiles
                        .Where(f => Path.GetFileName(f).Contains(todayString))
                        .FirstOrDefault();

                    if (todayBackup != null)
                    {
                        status.HasTodayBackup = true;
                        status.TodayBackupFile = Path.GetFileName(todayBackup);
                    }
                }

                _logger.LogInformation("備份狀態: 今日備份={HasTodayBackup}, 總檔案數={TotalFiles}", 
                    status.HasTodayBackup, status.TotalBackupFiles);

                return Ok(status);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "獲取備份狀態時發生錯誤");
                return StatusCode(500, new { error = "獲取備份狀態失敗", details = ex.Message });
            }
        }
    }
}

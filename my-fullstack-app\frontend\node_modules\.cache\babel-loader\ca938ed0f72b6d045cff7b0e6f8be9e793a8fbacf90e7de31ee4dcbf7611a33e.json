{"ast": null, "code": "var _s = $RefreshSig$();\nimport { useState, useEffect, useCallback } from 'react';\nimport { PermissionApi } from '../services/apiService'; // 導入新的 PermissionApi\nimport { log } from '../utils/logger';\nexport const usePermissions = () => {\n  _s();\n  const [permissions, setPermissions] = useState([]); // 權限現在是一個字串陣列\n  const [loading, setLoading] = useState(true);\n  const fetchPermissions = useCallback(async () => {\n    setLoading(true);\n    try {\n      const userPermissions = await PermissionApi.getCurrentUserPermissions();\n      setPermissions(userPermissions || []);\n      log.info('Permissions loaded', userPermissions);\n    } catch (error) {\n      log.error(\"Failed to fetch permissions\", error);\n      setPermissions([]);\n    } finally {\n      setLoading(false);\n    }\n  }, []);\n  useEffect(() => {\n    fetchPermissions();\n  }, [fetchPermissions]);\n\n  // 檢查用戶是否擁有特定權限\n  const hasPermission = useCallback(permissionCode => {\n    if (loading) {\n      return false; // 載入中不允許訪問\n    }\n    return permissions.includes(permissionCode);\n  }, [permissions, loading]);\n  return {\n    permissions,\n    hasPermission,\n    loading,\n    refreshPermissions: fetchPermissions\n  };\n};\n_s(usePermissions, \"ZfVIPBxTEOWCYg9QTjy5yDp/Fvg=\");", "map": {"version": 3, "names": ["useState", "useEffect", "useCallback", "PermissionApi", "log", "usePermissions", "_s", "permissions", "setPermissions", "loading", "setLoading", "fetchPermissions", "userPermissions", "getCurrentUserPermissions", "info", "error", "hasPermission", "permissionCode", "includes", "refreshPermissions"], "sources": ["C:/Users/<USER>/Desktop/demo-git/demo-react/my-fullstack-app/frontend/src/hooks/usePermissions.ts"], "sourcesContent": ["import { useState, useEffect, useCallback } from 'react';\nimport { PermissionApi } from '../services/apiService'; // 導入新的 PermissionApi\nimport { log } from '../utils/logger';\n\nexport const usePermissions = () => {\n    const [permissions, setPermissions] = useState<string[]>([]); // 權限現在是一個字串陣列\n    const [loading, setLoading] = useState(true);\n\n    const fetchPermissions = useCallback(async () => {\n        setLoading(true);\n        try {\n            const userPermissions = await PermissionApi.getCurrentUserPermissions();\n            setPermissions(userPermissions || []);\n            log.info('Permissions loaded', userPermissions);\n        } catch (error: any) {\n            log.error(\"Failed to fetch permissions\", error);\n            setPermissions([]);\n        } finally {\n            setLoading(false);\n        }\n    }, []);\n\n    useEffect(() => {\n        fetchPermissions();\n    }, [fetchPermissions]);\n\n    // 檢查用戶是否擁有特定權限\n    const hasPermission = useCallback(\n        (permissionCode: string): boolean => {\n            if (loading) {\n                return false; // 載入中不允許訪問\n            }\n            return permissions.includes(permissionCode);\n        },\n        [permissions, loading]\n    );\n\n    return { permissions, hasPermission, loading, refreshPermissions: fetchPermissions };\n};\n"], "mappings": ";AAAA,SAASA,QAAQ,EAAEC,SAAS,EAAEC,WAAW,QAAQ,OAAO;AACxD,SAASC,aAAa,QAAQ,wBAAwB,CAAC,CAAC;AACxD,SAASC,GAAG,QAAQ,iBAAiB;AAErC,OAAO,MAAMC,cAAc,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAChC,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGR,QAAQ,CAAW,EAAE,CAAC,CAAC,CAAC;EAC9D,MAAM,CAACS,OAAO,EAAEC,UAAU,CAAC,GAAGV,QAAQ,CAAC,IAAI,CAAC;EAE5C,MAAMW,gBAAgB,GAAGT,WAAW,CAAC,YAAY;IAC7CQ,UAAU,CAAC,IAAI,CAAC;IAChB,IAAI;MACA,MAAME,eAAe,GAAG,MAAMT,aAAa,CAACU,yBAAyB,CAAC,CAAC;MACvEL,cAAc,CAACI,eAAe,IAAI,EAAE,CAAC;MACrCR,GAAG,CAACU,IAAI,CAAC,oBAAoB,EAAEF,eAAe,CAAC;IACnD,CAAC,CAAC,OAAOG,KAAU,EAAE;MACjBX,GAAG,CAACW,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;MAC/CP,cAAc,CAAC,EAAE,CAAC;IACtB,CAAC,SAAS;MACNE,UAAU,CAAC,KAAK,CAAC;IACrB;EACJ,CAAC,EAAE,EAAE,CAAC;EAENT,SAAS,CAAC,MAAM;IACZU,gBAAgB,CAAC,CAAC;EACtB,CAAC,EAAE,CAACA,gBAAgB,CAAC,CAAC;;EAEtB;EACA,MAAMK,aAAa,GAAGd,WAAW,CAC5Be,cAAsB,IAAc;IACjC,IAAIR,OAAO,EAAE;MACT,OAAO,KAAK,CAAC,CAAC;IAClB;IACA,OAAOF,WAAW,CAACW,QAAQ,CAACD,cAAc,CAAC;EAC/C,CAAC,EACD,CAACV,WAAW,EAAEE,OAAO,CACzB,CAAC;EAED,OAAO;IAAEF,WAAW;IAAES,aAAa;IAAEP,OAAO;IAAEU,kBAAkB,EAAER;EAAiB,CAAC;AACxF,CAAC;AAACL,EAAA,CAlCWD,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
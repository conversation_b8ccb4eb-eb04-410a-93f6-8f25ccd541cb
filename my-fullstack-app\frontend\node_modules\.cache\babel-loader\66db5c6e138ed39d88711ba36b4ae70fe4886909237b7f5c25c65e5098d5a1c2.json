{"ast": null, "code": "import{formatUtcToTaipei}from\"../../utils/dateUtils\";import{Button}from'primereact/button';import{Calendar}from'primereact/calendar';import{Column}from'primereact/column';import{ConfirmDialog,confirmDialog}from'primereact/confirmdialog';import{DataTable}from'primereact/datatable';import{Dropdown}from'primereact/dropdown';import{InputText}from\"primereact/inputtext\";import LoadingSpinner from'../Common/LoadingSpinner';import{Tag}from'primereact/tag';import{Toast}from\"primereact/toast\";import React,{useEffect,useRef,useState}from'react';import{useNavigate}from\"react-router-dom\";import{ROUTES}from\"../../constants/routes\";import useTreatment from'../../hooks/useTreatment';import api from\"../../services/api\";import{Card}from'primereact/card';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const TreatmentsPage=()=>{const navigate=useNavigate();const toast=useRef(null);const[name,setName]=useState('');const[nationalId,setNationalId]=useState('');const[doctortId,setDoctortId]=useState(undefined);const[starttime,setStarttime]=useState(undefined);const[endtime,setEndtime]=useState(undefined);const[refreshKey,setRefreshKey]=useState(0);const[deletedFlag,setDeletedFlag]=useState(false);const[doctors,setDoctors]=useState([]);const[doctorsLoading,setDoctorsLoading]=useState(true);const[searchParams,setSearchParams]=useState({name:'',nationalId:'',doctortId:null,starttime:null,endtime:null,refreshKey:0});const{treatments,loading}=useTreatment({patientname:searchParams.name,nationalId:searchParams.nationalId,doctortid:searchParams.doctortId||null,startTime:searchParams.starttime||null,endTime:searchParams.endtime||null,refreshKey});const getStep=status=>{switch(status){case'10':return'info';case'20':return'warning';case'30':return'danger';case'40':return'success';default:return null;}};const genderdict={\"1\":\"男性\",\"2\":\"女性\",\"3\":\"其他\"};const stepdict={\"10\":\"新案\",\"20\":\"治療\",\"30\":\"上傳\",\"40\":\"結案\",\"50\":\"收據\"};// 載入治療師數據\nuseEffect(()=>{const loadDoctors=async()=>{try{setDoctorsLoading(true);console.log('開始載入治療師數據...');const response=await api.get('/api/users/DoctorList');console.log('治療師數據:',response.data);// 解析 JSON 字符串（因為後端返回的是序列化的 JSON）\nconst doctorsData=typeof response.data==='string'?JSON.parse(response.data):response.data;setDoctors(doctorsData);}catch(error){var _toast$current;console.error('載入治療師數據失敗:',error);(_toast$current=toast.current)===null||_toast$current===void 0?void 0:_toast$current.show({severity:\"error\",summary:\"錯誤\",detail:error.details});}finally{setDoctorsLoading(false);}};loadDoctors();},[]);useEffect(()=>{if(deletedFlag&&!loading){var _toast$current2;(_toast$current2=toast.current)===null||_toast$current2===void 0?void 0:_toast$current2.show({severity:\"success\",summary:\"成功\",detail:\"病患診療紀錄已刪除\"});setDeletedFlag(false);// 重置\n}},[deletedFlag,loading]);const handleSearchClick=()=>{setRefreshKey(refreshKey+1);setSearchParams({name,nationalId,doctortId,starttime,endtime,refreshKey});};const handleDelete=async orderNo=>{try{await api.get(\"/api/treatment/Delete\",{params:{orderNo:orderNo}});setDeletedFlag(true);Reload();}catch(error){var _error$response,_error$response$data,_toast$current3;var detail=error.status===403?\"您無權限，請通知管理員\":((_error$response=error.response)===null||_error$response===void 0?void 0:(_error$response$data=_error$response.data)===null||_error$response$data===void 0?void 0:_error$response$data.message)||'刪除失敗';(_toast$current3=toast.current)===null||_toast$current3===void 0?void 0:_toast$current3.show({severity:\"error\",summary:\"錯誤\",detail:detail});}};const Reload=()=>{// 重新觸發 usePatient，等於重新查詢\nsetRefreshKey(prev=>prev+1);};const Receipt_detail=async rowdata=>{navigate(ROUTES.RECEIPT_DETAIL,{state:{treatment:{receiptUrl:rowdata.receiptUrl,id:rowdata.id,patientId:rowdata.patientId}}});};const Edit=async(id,patientId)=>{try{const Response=await api.get('/api/treatment/',{params:{Id:id}});const Data=Response.data;if(Data){navigate(ROUTES.TREATMENT_DETAIL,{state:{treatment:Data,patient:{id:patientId}}});}}catch(error){var _error$response2,_error$response2$data,_toast$current4;var detail=error.status===403?\"您無權限，請通知管理員\":((_error$response2=error.response)===null||_error$response2===void 0?void 0:(_error$response2$data=_error$response2.data)===null||_error$response2$data===void 0?void 0:_error$response2$data.message)||'編輯失敗';(_toast$current4=toast.current)===null||_toast$current4===void 0?void 0:_toast$current4.show({severity:\"error\",summary:\"錯誤\",detail:detail});}};const paginatorLeft=/*#__PURE__*/_jsx(Button,{type:\"button\",icon:\"pi pi-refresh\",text:true,onClick:()=>Reload()});const paginatorRight=/*#__PURE__*/_jsx(\"div\",{});const optionBodyTemplate=rowData=>{return/*#__PURE__*/_jsxs(\"div\",{className:\"flex gap-1\",children:[/*#__PURE__*/_jsx(Button,{label:\"\\u7DE8\\u8F2F\",type:\"button\",icon:\"pi pi-file-edit\",onClick:()=>Edit(rowData.id,rowData.patientId),size:\"small\",severity:\"info\",style:{fontSize:'1rem',margin:'3px'}}),/*#__PURE__*/_jsx(Button,{label:\"\\u6536\\u64DA\",type:\"button\",icon:\"pi pi-clipboard\",onClick:()=>Receipt_detail(rowData),size:\"small\",severity:\"success\",style:{fontSize:'1rem',margin:'3px'},disabled:rowData.step===40||rowData.step===50?false:true}),/*#__PURE__*/_jsx(Button,{label:\"\\u522A\\u9664\",type:\"button\",icon:\"pi pi-file-excel\",onClick:()=>confirm(rowData.orderNo),size:\"small\",severity:\"danger\",style:{fontSize:'1rem',margin:'3px'},disabled:rowData.step===40||rowData.step===50?true:false})]});};const confirm=Id=>{confirmDialog({message:'確定要刪除這筆資料嗎？',header:'刪除確認',icon:'pi pi-exclamation-triangle',defaultFocus:'reject',acceptClassName:'p-button-danger',acceptLabel:'確定',rejectLabel:'取消',accept:()=>handleDelete(Id)});};const stepBodyTemplate=rowData=>{return/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsx(Tag,{value:stepdict[String(rowData.step)],severity:getStep(String(rowData.step))})});};const genderBodyTemplate=rowData=>{var data=String(rowData.patientGender);const gendar=genderdict[data];return/*#__PURE__*/_jsx(\"div\",{children:gendar});};const formatDate=value=>{if(!value)return'';return formatUtcToTaipei(value,\"yyyy/MM/dd HH:mm:ss\");};const formatAge=value=>{if(!value)return\"\";const date=new Date(value);const today=new Date();let age=today.getFullYear()-date.getFullYear();const hasNotHadBirthdayThisYear=today.getMonth()<date.getMonth()||today.getMonth()===date.getMonth()&&today.getDate()<date.getDate();if(hasNotHadBirthdayThisYear){age--;}return age;};if(loading){return/*#__PURE__*/_jsx(LoadingSpinner,{message:\"\\u8F09\\u5165\\u8A3A\\u7642\\u8CC7\\u6599\\u4E2D...\"});}return/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(Toast,{ref:toast}),/*#__PURE__*/_jsx(ConfirmDialog,{}),/*#__PURE__*/_jsx(Card,{title:\"\\u8A3A\\u7642\\u7D00\\u9304\",className:\"mb-4\",children:/*#__PURE__*/_jsx(\"p\",{className:\"text-600 line-height-3 m-0\",children:\"\\u75C5\\u60A3\\u7684\\u8A3A\\u7642\\u7D00\\u9304\\uFF0C\\u53EF\\u4EE5\\u67E5\\u8A62\\u3001\\u7DE8\\u8F2F\\u3001\\u522A\\u9664\\u8A3A\\u7642\\u8CC7\\u6599\\uFF0C\\u7D50\\u6848\\u7684\\u8A3A\\u7642\\u8CC7\\u6599\\u53EF\\u4EE5\\u7DE8\\u8F2F\\u6536\\u64DA\\u4E26\\u88FD\\u4F5C\\u5831\\u8868\\u3002\"})}),/*#__PURE__*/_jsx(Card,{className:\"mb-4\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"grid\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"col-12 md:col-4\",children:/*#__PURE__*/_jsx(InputText,{id:\"name\",type:\"text\",value:name,onChange:e=>setName(e.target.value),placeholder:\"\\u75C5\\u60A3\\u59D3\\u540D\",className:\"w-full\"})}),/*#__PURE__*/_jsx(\"div\",{className:\"col-6 md:col-4\",children:/*#__PURE__*/_jsx(Dropdown,{value:doctortId,onChange:e=>setDoctortId(e.value),options:doctors,optionLabel:\"Name\",optionValue:\"Id\",placeholder:\"\\u8ACB\\u9078\\u64C7\\u6CBB\\u7642\\u5E2B\",disabled:doctorsLoading,className:\"w-full\",showClear:true})}),/*#__PURE__*/_jsx(\"div\",{className:\"col-6 md:col-4\",children:/*#__PURE__*/_jsx(InputText,{id:\"nationalId\",type:\"text\",value:nationalId,onChange:e=>setNationalId(e.target.value),placeholder:\"\\u75C5\\u60A3\\u8EAB\\u5206\\u8B49\",className:\"w-full\"})}),/*#__PURE__*/_jsx(\"div\",{className:\"col-6 md:col-3\",children:/*#__PURE__*/_jsx(Calendar,{id:\"starttime\",value:starttime,onChange:e=>setStarttime(e.value),placeholder:\"\\u958B\\u59CB\\u6642\\u9593\",className:\"w-full\",dateFormat:\"yy/mm/dd\",showIcon:true})}),/*#__PURE__*/_jsx(\"div\",{className:\"col-6 md:col-3\",children:/*#__PURE__*/_jsx(Calendar,{id:\"endtime\",value:endtime,onChange:e=>setEndtime(e.value),placeholder:\"\\u7D50\\u675F\\u6642\\u9593\",className:\"w-full\",dateFormat:\"yy/mm/dd\",showIcon:true})}),/*#__PURE__*/_jsx(\"div\",{className:\"col-12 md:col-4\",children:/*#__PURE__*/_jsx(\"div\",{className:\"flex gap-2\",children:/*#__PURE__*/_jsx(Button,{label:\"\\u67E5\\u8A62\",icon:\"pi pi-search\",onClick:handleSearchClick})})})]})}),/*#__PURE__*/_jsx(Card,{children:/*#__PURE__*/_jsxs(DataTable,{value:treatments,paginator:true,rows:10,rowsPerPageOptions:[10,20,30,40],tableStyle:{minWidth:'50rem'},emptyMessage:\"\\u6C92\\u6709\\u627E\\u5230\\u8A3A\\u7642\\u8CC7\\u6599\",paginatorLeft:paginatorLeft,paginatorRight:paginatorRight,children:[/*#__PURE__*/_jsx(Column,{field:\"orderNo\",header:\"\\u6848\\u865F\",style:{width:'5%'}}),/*#__PURE__*/_jsx(Column,{field:\"patientName\",header:\"\\u75C5\\u60A3\\u59D3\\u540D\",style:{width:'5%'}}),/*#__PURE__*/_jsx(Column,{field:\"doctorName\",header:\"\\u6CBB\\u7642\\u6CBB\\u7642\\u5E2B\",style:{width:'5%'}}),/*#__PURE__*/_jsx(Column,{field:\"patientGender\",header:\"\\u6027\\u5225\",style:{width:'3%'},body:genderBodyTemplate}),/*#__PURE__*/_jsx(Column,{field:\"patientBirthDate\",header:\"\\u5E74\\u9F61\",style:{width:'3%'},body:rowData=>formatAge(rowData.patientBirthDate)}),/*#__PURE__*/_jsx(Column,{field:\"step\",header:\"\\u968E\\u6BB5\",style:{width:'3%'},body:stepBodyTemplate}),/*#__PURE__*/_jsx(Column,{field:\"createdAt\",header:\"\\u65B0\\u589E\\u65E5\\u671F\",style:{width:'8%'},body:rowData=>formatDate(rowData.createdAt)}),/*#__PURE__*/_jsx(Column,{field:\"updatedAt\",header:\"\\u66F4\\u65B0\\u65E5\\u671F\",style:{width:'8%'},body:rowData=>formatDate(rowData.updatedAt)}),/*#__PURE__*/_jsx(Column,{field:\"operatorUserName\",header:\"\\u64CD\\u4F5C\\u4EBA\",style:{width:'5%'}}),/*#__PURE__*/_jsx(Column,{field:\"option\",header:\"\\u529F\\u80FD\",style:{width:'12%'},body:optionBodyTemplate})]})})]});};export default TreatmentsPage;", "map": {"version": 3, "names": ["formatUtcToTaipei", "<PERSON><PERSON>", "Calendar", "Column", "ConfirmDialog", "confirmDialog", "DataTable", "Dropdown", "InputText", "LoadingSpinner", "Tag", "Toast", "React", "useEffect", "useRef", "useState", "useNavigate", "ROUTES", "useTreatment", "api", "Card", "jsx", "_jsx", "jsxs", "_jsxs", "TreatmentsPage", "navigate", "toast", "name", "setName", "nationalId", "setNationalId", "doctortId", "setDoctortId", "undefined", "starttime", "set<PERSON><PERSON><PERSON><PERSON>", "endtime", "setEndtime", "refresh<PERSON><PERSON>", "setRefresh<PERSON>ey", "deletedFlag", "setDeletedFlag", "doctors", "setDoctors", "doctorsLoading", "setDoctorsLoading", "searchParams", "setSearchParams", "treatments", "loading", "patientname", "<PERSON><PERSON><PERSON>", "startTime", "endTime", "getStep", "status", "genderdict", "stepdict", "loadDoctors", "console", "log", "response", "get", "data", "doctorsData", "JSON", "parse", "error", "_toast$current", "current", "show", "severity", "summary", "detail", "details", "_toast$current2", "handleSearchClick", "handleDelete", "orderNo", "params", "Reload", "_error$response", "_error$response$data", "_toast$current3", "message", "prev", "Receipt_detail", "rowdata", "RECEIPT_DETAIL", "state", "treatment", "receiptUrl", "id", "patientId", "Edit", "Response", "Id", "Data", "TREATMENT_DETAIL", "patient", "_error$response2", "_error$response2$data", "_toast$current4", "paginatorLeft", "type", "icon", "text", "onClick", "paginatorRight", "optionBodyTemplate", "rowData", "className", "children", "label", "size", "style", "fontSize", "margin", "disabled", "step", "confirm", "header", "defaultFocus", "acceptClassName", "acceptLabel", "<PERSON><PERSON><PERSON><PERSON>", "accept", "stepBodyTemplate", "value", "String", "genderBodyTemplate", "patientGender", "gendar", "formatDate", "formatAge", "date", "Date", "today", "age", "getFullYear", "hasNotHadBirthdayThisYear", "getMonth", "getDate", "ref", "title", "onChange", "e", "target", "placeholder", "options", "optionLabel", "optionValue", "showClear", "dateFormat", "showIcon", "paginator", "rows", "rowsPerPageOptions", "tableStyle", "min<PERSON><PERSON><PERSON>", "emptyMessage", "field", "width", "body", "patientBirthDate", "createdAt", "updatedAt"], "sources": ["C:/Users/<USER>/Desktop/demo-git/demo-react/my-fullstack-app/frontend/src/components/Page/TreatmentsPage.tsx"], "sourcesContent": ["import { formatUtcToTaipei } from \"../../utils/dateUtils\";\r\nimport { Button } from 'primereact/button';\r\nimport { Calendar } from 'primereact/calendar';\r\nimport { Column } from 'primereact/column';\r\nimport { ConfirmDialog, confirmDialog } from 'primereact/confirmdialog';\r\nimport { DataTable } from 'primereact/datatable';\r\nimport { Dropdown, DropdownChangeEvent } from 'primereact/dropdown';\r\nimport { InputText } from \"primereact/inputtext\";\r\nimport LoadingSpinner from '../Common/LoadingSpinner';\r\nimport { Tag } from 'primereact/tag';\r\nimport { Toast } from \"primereact/toast\";\r\nimport React, { useEffect, useRef, useState } from 'react';\r\nimport { useNavigate } from \"react-router-dom\";\r\nimport { ROUTES } from \"../../constants/routes\";\r\nimport useTreatment from '../../hooks/useTreatment';\r\nimport api from \"../../services/api\";\r\nimport { Card } from 'primereact/card';\r\n\r\ninterface Doctor {\r\n  Id: number;\r\n  Name: string;\r\n}\r\n\r\nconst TreatmentsPage: React.FC = () => {\r\n    const navigate = useNavigate();\r\n    const toast = useRef<Toast>(null);\r\n    const [name, setName] = useState('');\r\n    const [nationalId, setNationalId] = useState('');\r\n    const [doctortId, setDoctortId] = useState<number | null | undefined>(undefined);\r\n    const [starttime, setStarttime] = useState<Date | null | undefined>(undefined);\r\n    const [endtime, setEndtime] = useState<Date | null | undefined>(undefined);\r\n    const [refreshKey, setRefreshKey] = useState(0);\r\n    const [deletedFlag, setDeletedFlag] = useState(false);\r\n\r\n    const [doctors, setDoctors] = useState<Doctor[]>([]);\r\n    const [doctorsLoading, setDoctorsLoading] = useState(true);\r\n\r\n    const [searchParams, setSearchParams] = useState({\r\n        name: '',\r\n        nationalId: '',\r\n        doctortId: null as number | null | undefined,\r\n        starttime: null as Date | null | undefined,\r\n        endtime: null as Date | null | undefined,\r\n        refreshKey: 0,\r\n    });\r\n\r\n    const { treatments, loading } = useTreatment({\r\n        patientname: searchParams.name,\r\n        nationalId: searchParams.nationalId,\r\n        doctortid: searchParams.doctortId || null,\r\n        startTime: searchParams.starttime || null,\r\n        endTime: searchParams.endtime || null,\r\n        refreshKey\r\n    });\r\n\r\n    const getStep = (status: string) => {\r\n        switch (status) {\r\n            case '10':\r\n                return 'info';\r\n\r\n            case '20':\r\n                return 'warning';\r\n\r\n            case '30':\r\n                return 'danger';\r\n\r\n            case '40':\r\n                return 'success';\r\n            default: \r\n                return null; \r\n        }\r\n    };\r\n\r\n    const genderdict: { [key: string]: string } = {\r\n        \"1\": \"男性\",\r\n        \"2\": \"女性\",\r\n        \"3\": \"其他\"\r\n    };\r\n\r\n    const stepdict: { [key: string]: string } = {\r\n        \"10\": \"新案\",\r\n        \"20\": \"治療\",\r\n        \"30\": \"上傳\",\r\n        \"40\": \"結案\",\r\n        \"50\": \"收據\"\r\n    };\r\n    \r\n    // 載入治療師數據\r\n    useEffect(() => {\r\n        const loadDoctors = async () => {\r\n            try {\r\n                setDoctorsLoading(true);\r\n                console.log('開始載入治療師數據...');\r\n\r\n                const response = await api.get('/api/users/DoctorList');\r\n                console.log('治療師數據:', response.data);\r\n\r\n                // 解析 JSON 字符串（因為後端返回的是序列化的 JSON）\r\n                const doctorsData = typeof response.data === 'string'\r\n                    ? JSON.parse(response.data)\r\n                    : response.data;\r\n\r\n                setDoctors(doctorsData);\r\n\r\n            } catch (error: any) {\r\n                console.error('載入治療師數據失敗:', error);\r\n                toast.current?.show({\r\n                    severity: \"error\",\r\n                    summary: \"錯誤\",\r\n                    detail: error.details\r\n                });\r\n            } finally {\r\n                setDoctorsLoading(false);\r\n            }\r\n        };\r\n\r\n        loadDoctors();\r\n    }, []);\r\n\r\n    useEffect(() => {\r\n        if (deletedFlag && !loading) {\r\n            toast.current?.show({ severity: \"success\", summary: \"成功\", detail: \"病患診療紀錄已刪除\" });\r\n            setDeletedFlag(false); // 重置\r\n        }\r\n    }, [deletedFlag, loading]);\r\n\r\n    const handleSearchClick = () => {\r\n        setRefreshKey(refreshKey + 1)\r\n        setSearchParams({ name, nationalId, doctortId, starttime, endtime, refreshKey});\r\n    };\r\n\r\n    const handleDelete = async (orderNo:string) => {\r\n        try {\r\n            await api.get(\"/api/treatment/Delete\",  {\r\n                    params: { \r\n                        orderNo: orderNo\r\n                    }\r\n                }\r\n            );\r\n            setDeletedFlag(true);\r\n            Reload();\r\n        } catch (error:any) {\r\n            var detail =  error.status === 403 ? \"您無權限，請通知管理員\" : error.response?.data?.message || '刪除失敗';\r\n            toast.current?.show({ severity: \"error\", summary: \"錯誤\", detail: detail });\r\n        }\r\n    };\r\n\r\n    const Reload = () => {\r\n        // 重新觸發 usePatient，等於重新查詢\r\n        setRefreshKey(prev => prev + 1);\r\n    }\r\n\r\n    const Receipt_detail = async (rowdata: any) => {\r\n        navigate(ROUTES.RECEIPT_DETAIL, { \r\n            state: { \r\n                treatment: { \r\n                    receiptUrl: rowdata.receiptUrl, \r\n                    id: rowdata.id, \r\n                    patientId: rowdata.patientId\r\n                } \r\n            } \r\n        }) \r\n    }\r\n\r\n    const Edit = async (id: string, patientId: string) => {\r\n        try {\r\n            const Response = await api.get('/api/treatment/', {\r\n                params: {\r\n                    Id: id\r\n                }\r\n            });\r\n    \r\n            const Data = Response.data;\r\n            \r\n            if (Data) {\r\n                navigate(ROUTES.TREATMENT_DETAIL, { state: { treatment: Data, patient: { id: patientId} } })\r\n            }\r\n        } catch (error:any) {\r\n            var detail =  error.status === 403 ? \"您無權限，請通知管理員\" : error.response?.data?.message || '編輯失敗';\r\n            toast.current?.show({ severity: \"error\", summary: \"錯誤\", detail: detail });\r\n        }\r\n    }\r\n\r\n    const paginatorLeft = (\r\n        <Button\r\n            type=\"button\"\r\n            icon=\"pi pi-refresh\"\r\n            text\r\n            onClick={() => Reload()}\r\n        />\r\n    );\r\n    const paginatorRight = <div></div>;\r\n    const optionBodyTemplate = (rowData: any) => {\r\n        return (\r\n            <div className=\"flex gap-1\">\r\n                <Button \r\n                    label=\"編輯\" \r\n                    type=\"button\" \r\n                    icon=\"pi pi-file-edit\" \r\n                    onClick={() => Edit(rowData.id, rowData.patientId)}\r\n                    size=\"small\" \r\n                    severity=\"info\" \r\n                    style={{ fontSize: '1rem', margin: '3px' }} \r\n                />\r\n                <Button \r\n                    label=\"收據\" \r\n                    type=\"button\" \r\n                    icon=\"pi pi-clipboard\" \r\n                    onClick={() => Receipt_detail(rowData)}\r\n                    size=\"small\" \r\n                    severity=\"success\" \r\n                    style={{ fontSize: '1rem', margin: '3px' }}\r\n                    disabled={ rowData.step === 40 || rowData.step === 50 ? false : true}\r\n                />\r\n                <Button \r\n                    label=\"刪除\" \r\n                    type=\"button\" \r\n                    icon=\"pi pi-file-excel\" \r\n                    onClick={()=> confirm(rowData.orderNo)} \r\n                    size=\"small\" \r\n                    severity=\"danger\" \r\n                    style={{  fontSize: '1rem', margin: '3px' }} \r\n                    disabled={ rowData.step === 40 || rowData.step === 50 ? true : false}\r\n                />\r\n            </div>\r\n        );\r\n    };\r\n\r\n    const confirm = (Id:string) => {\r\n        confirmDialog({\r\n            message: '確定要刪除這筆資料嗎？',\r\n            header: '刪除確認',\r\n            icon: 'pi pi-exclamation-triangle',\r\n            defaultFocus: 'reject',\r\n            acceptClassName: 'p-button-danger',\r\n            acceptLabel: '確定',\r\n            rejectLabel: '取消',\r\n            accept: () => handleDelete(Id),\r\n        });\r\n    };\r\n\r\n    const stepBodyTemplate = (rowData: any) => {\r\n        return (\r\n            <div>\r\n                <Tag value={stepdict[String(rowData.step)]} severity={getStep(String(rowData.step))} />\r\n            </div>\r\n        );\r\n    };\r\n\r\n    const genderBodyTemplate = (rowData: any) => {\r\n        var data = String(rowData.patientGender)\r\n        const gendar = genderdict[data]\r\n            return (\r\n                <div>\r\n                    {gendar}\r\n                </div>\r\n            );\r\n        };\r\n\r\n    const formatDate = (value: string) => {\r\n    if (!value) return '';\r\n    return formatUtcToTaipei(value, \"yyyy/MM/dd HH:mm:ss\");\r\n  };\r\n\r\n    const formatAge = (value: string) => {\r\n        if (!value) return \"\";\r\n        const date = new Date(value);\r\n        const today = new Date();\r\n        let age = today.getFullYear() - date.getFullYear();\r\n\r\n        const hasNotHadBirthdayThisYear =\r\n            today.getMonth() < date.getMonth() ||\r\n            (today.getMonth() === date.getMonth() && today.getDate() < date.getDate());\r\n\r\n        if (hasNotHadBirthdayThisYear) {\r\n            age--;\r\n        }\r\n\r\n        return age;\r\n        \r\n    };\r\n\r\n    if (loading) {\r\n        return <LoadingSpinner message=\"載入診療資料中...\" />;\r\n    }\r\n\r\n    return (\r\n        <div>\r\n            <Toast ref={toast} />\r\n            <ConfirmDialog />\r\n            <Card title=\"診療紀錄\" className=\"mb-4\">\r\n                <p className=\"text-600 line-height-3 m-0\">\r\n                    病患的診療紀錄，可以查詢、編輯、刪除診療資料，結案的診療資料可以編輯收據並製作報表。\r\n                </p>\r\n            </Card>\r\n\r\n            {/* 搜尋條件 */}\r\n            <Card className=\"mb-4\">\r\n                <div className=\"grid\">\r\n                    <div className=\"col-12 md:col-4\">\r\n                        <InputText\r\n                            id=\"name\"\r\n                            type=\"text\"\r\n                            value={name}\r\n                            onChange={(e) => setName(e.target.value)}\r\n                            placeholder=\"病患姓名\" \r\n                            className=\"w-full\"\r\n                        />\r\n                    </div>\r\n                    <div className=\"col-6 md:col-4\">\r\n                        <Dropdown\r\n                            value={doctortId}\r\n                            onChange={(e: DropdownChangeEvent) =>  setDoctortId(e.value)}\r\n                            options={doctors}\r\n                            optionLabel=\"Name\"\r\n                            optionValue=\"Id\"\r\n                            placeholder=\"請選擇治療師\"\r\n                            disabled={doctorsLoading}\r\n                            className=\"w-full\"\r\n                            showClear />\r\n                    </div>\r\n                    <div className=\"col-6 md:col-4\">\r\n                        <InputText\r\n                            id=\"nationalId\"\r\n                            type=\"text\"\r\n                            value={nationalId}\r\n                            onChange={(e) => setNationalId(e.target.value)}\r\n                            placeholder=\"病患身分證\"\r\n                            className=\"w-full\"\r\n                        />\r\n                    </div>\r\n                    \r\n                    <div className=\"col-6 md:col-3\">\r\n                        <Calendar \r\n                            id=\"starttime\" \r\n                            value={starttime} \r\n                            onChange={(e) => setStarttime(e.value)} \r\n                            placeholder=\"開始時間\"\r\n                            className=\"w-full\"\r\n                            dateFormat=\"yy/mm/dd\"\r\n                            showIcon/>\r\n                    </div>\r\n                    <div className=\"col-6 md:col-3\">\r\n                        <Calendar \r\n                            id=\"endtime\" \r\n                            value={endtime} \r\n                            onChange={(e) => setEndtime(e.value)} \r\n                            placeholder=\"結束時間\"\r\n                            className=\"w-full\"\r\n                            dateFormat=\"yy/mm/dd\"\r\n                            showIcon/>\r\n                    </div>\r\n                    <div className=\"col-12 md:col-4\">\r\n                        <div className=\"flex gap-2\">\r\n                            <Button label=\"查詢\" icon=\"pi pi-search\" onClick={handleSearchClick}/>\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n            </Card>\r\n\r\n            <Card>\r\n                <DataTable\r\n                    value={treatments}\r\n                    paginator\r\n                    rows={10}\r\n                    rowsPerPageOptions={[10, 20, 30, 40]}\r\n                    tableStyle={{ minWidth: '50rem' }}\r\n                    emptyMessage=\"沒有找到診療資料\"\r\n                    paginatorLeft={paginatorLeft}\r\n                    paginatorRight={paginatorRight}\r\n                >\r\n                    <Column field=\"orderNo\" header=\"案號\" style={{ width: '5%' }} />\r\n                    <Column field=\"patientName\" header=\"病患姓名\" style={{ width: '5%' }} />\r\n                    <Column field=\"doctorName\" header=\"治療治療師\" style={{ width: '5%' }} />\r\n                    <Column field=\"patientGender\" header=\"性別\" style={{ width: '3%' }} body={genderBodyTemplate}/>\r\n                    <Column field=\"patientBirthDate\" header=\"年齡\" style={{ width: '3%' }}  body={(rowData) => formatAge(rowData.patientBirthDate)}/>\r\n                    <Column field=\"step\" header=\"階段\" style={{ width: '3%' }} body={stepBodyTemplate}/>\r\n                    <Column field=\"createdAt\" header=\"新增日期\" style={{ width: '8%' }} body={(rowData) => formatDate(rowData.createdAt)} />\r\n                    <Column field=\"updatedAt\" header=\"更新日期\" style={{ width: '8%' }} body={(rowData) => formatDate(rowData.updatedAt)}/>\r\n                    <Column field=\"operatorUserName\" header=\"操作人\" style={{ width: '5%' }} />\r\n                    <Column field=\"option\" header=\"功能\" style={{ width: '12%' }} body={optionBodyTemplate} />\r\n                </DataTable>\r\n            </Card>\r\n        </div>\r\n    );\r\n};\r\n\r\nexport default TreatmentsPage;"], "mappings": "AAAA,OAASA,iBAAiB,KAAQ,uBAAuB,CACzD,OAASC,MAAM,KAAQ,mBAAmB,CAC1C,OAASC,QAAQ,KAAQ,qBAAqB,CAC9C,OAASC,MAAM,KAAQ,mBAAmB,CAC1C,OAASC,aAAa,CAAEC,aAAa,KAAQ,0BAA0B,CACvE,OAASC,SAAS,KAAQ,sBAAsB,CAChD,OAASC,QAAQ,KAA6B,qBAAqB,CACnE,OAASC,SAAS,KAAQ,sBAAsB,CAChD,MAAO,CAAAC,cAAc,KAAM,0BAA0B,CACrD,OAASC,GAAG,KAAQ,gBAAgB,CACpC,OAASC,KAAK,KAAQ,kBAAkB,CACxC,MAAO,CAAAC,KAAK,EAAIC,SAAS,CAAEC,MAAM,CAAEC,QAAQ,KAAQ,OAAO,CAC1D,OAASC,WAAW,KAAQ,kBAAkB,CAC9C,OAASC,MAAM,KAAQ,wBAAwB,CAC/C,MAAO,CAAAC,YAAY,KAAM,0BAA0B,CACnD,MAAO,CAAAC,GAAG,KAAM,oBAAoB,CACpC,OAASC,IAAI,KAAQ,iBAAiB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAOvC,KAAM,CAAAC,cAAwB,CAAGA,CAAA,GAAM,CACnC,KAAM,CAAAC,QAAQ,CAAGV,WAAW,CAAC,CAAC,CAC9B,KAAM,CAAAW,KAAK,CAAGb,MAAM,CAAQ,IAAI,CAAC,CACjC,KAAM,CAACc,IAAI,CAAEC,OAAO,CAAC,CAAGd,QAAQ,CAAC,EAAE,CAAC,CACpC,KAAM,CAACe,UAAU,CAAEC,aAAa,CAAC,CAAGhB,QAAQ,CAAC,EAAE,CAAC,CAChD,KAAM,CAACiB,SAAS,CAAEC,YAAY,CAAC,CAAGlB,QAAQ,CAA4BmB,SAAS,CAAC,CAChF,KAAM,CAACC,SAAS,CAAEC,YAAY,CAAC,CAAGrB,QAAQ,CAA0BmB,SAAS,CAAC,CAC9E,KAAM,CAACG,OAAO,CAAEC,UAAU,CAAC,CAAGvB,QAAQ,CAA0BmB,SAAS,CAAC,CAC1E,KAAM,CAACK,UAAU,CAAEC,aAAa,CAAC,CAAGzB,QAAQ,CAAC,CAAC,CAAC,CAC/C,KAAM,CAAC0B,WAAW,CAAEC,cAAc,CAAC,CAAG3B,QAAQ,CAAC,KAAK,CAAC,CAErD,KAAM,CAAC4B,OAAO,CAAEC,UAAU,CAAC,CAAG7B,QAAQ,CAAW,EAAE,CAAC,CACpD,KAAM,CAAC8B,cAAc,CAAEC,iBAAiB,CAAC,CAAG/B,QAAQ,CAAC,IAAI,CAAC,CAE1D,KAAM,CAACgC,YAAY,CAAEC,eAAe,CAAC,CAAGjC,QAAQ,CAAC,CAC7Ca,IAAI,CAAE,EAAE,CACRE,UAAU,CAAE,EAAE,CACdE,SAAS,CAAE,IAAiC,CAC5CG,SAAS,CAAE,IAA+B,CAC1CE,OAAO,CAAE,IAA+B,CACxCE,UAAU,CAAE,CAChB,CAAC,CAAC,CAEF,KAAM,CAAEU,UAAU,CAAEC,OAAQ,CAAC,CAAGhC,YAAY,CAAC,CACzCiC,WAAW,CAAEJ,YAAY,CAACnB,IAAI,CAC9BE,UAAU,CAAEiB,YAAY,CAACjB,UAAU,CACnCsB,SAAS,CAAEL,YAAY,CAACf,SAAS,EAAI,IAAI,CACzCqB,SAAS,CAAEN,YAAY,CAACZ,SAAS,EAAI,IAAI,CACzCmB,OAAO,CAAEP,YAAY,CAACV,OAAO,EAAI,IAAI,CACrCE,UACJ,CAAC,CAAC,CAEF,KAAM,CAAAgB,OAAO,CAAIC,MAAc,EAAK,CAChC,OAAQA,MAAM,EACV,IAAK,IAAI,CACL,MAAO,MAAM,CAEjB,IAAK,IAAI,CACL,MAAO,SAAS,CAEpB,IAAK,IAAI,CACL,MAAO,QAAQ,CAEnB,IAAK,IAAI,CACL,MAAO,SAAS,CACpB,QACI,MAAO,KAAI,CACnB,CACJ,CAAC,CAED,KAAM,CAAAC,UAAqC,CAAG,CAC1C,GAAG,CAAE,IAAI,CACT,GAAG,CAAE,IAAI,CACT,GAAG,CAAE,IACT,CAAC,CAED,KAAM,CAAAC,QAAmC,CAAG,CACxC,IAAI,CAAE,IAAI,CACV,IAAI,CAAE,IAAI,CACV,IAAI,CAAE,IAAI,CACV,IAAI,CAAE,IAAI,CACV,IAAI,CAAE,IACV,CAAC,CAED;AACA7C,SAAS,CAAC,IAAM,CACZ,KAAM,CAAA8C,WAAW,CAAG,KAAAA,CAAA,GAAY,CAC5B,GAAI,CACAb,iBAAiB,CAAC,IAAI,CAAC,CACvBc,OAAO,CAACC,GAAG,CAAC,cAAc,CAAC,CAE3B,KAAM,CAAAC,QAAQ,CAAG,KAAM,CAAA3C,GAAG,CAAC4C,GAAG,CAAC,uBAAuB,CAAC,CACvDH,OAAO,CAACC,GAAG,CAAC,QAAQ,CAAEC,QAAQ,CAACE,IAAI,CAAC,CAEpC;AACA,KAAM,CAAAC,WAAW,CAAG,MAAO,CAAAH,QAAQ,CAACE,IAAI,GAAK,QAAQ,CAC/CE,IAAI,CAACC,KAAK,CAACL,QAAQ,CAACE,IAAI,CAAC,CACzBF,QAAQ,CAACE,IAAI,CAEnBpB,UAAU,CAACqB,WAAW,CAAC,CAE3B,CAAE,MAAOG,KAAU,CAAE,KAAAC,cAAA,CACjBT,OAAO,CAACQ,KAAK,CAAC,YAAY,CAAEA,KAAK,CAAC,CAClC,CAAAC,cAAA,CAAA1C,KAAK,CAAC2C,OAAO,UAAAD,cAAA,iBAAbA,cAAA,CAAeE,IAAI,CAAC,CAChBC,QAAQ,CAAE,OAAO,CACjBC,OAAO,CAAE,IAAI,CACbC,MAAM,CAAEN,KAAK,CAACO,OAClB,CAAC,CAAC,CACN,CAAC,OAAS,CACN7B,iBAAiB,CAAC,KAAK,CAAC,CAC5B,CACJ,CAAC,CAEDa,WAAW,CAAC,CAAC,CACjB,CAAC,CAAE,EAAE,CAAC,CAEN9C,SAAS,CAAC,IAAM,CACZ,GAAI4B,WAAW,EAAI,CAACS,OAAO,CAAE,KAAA0B,eAAA,CACzB,CAAAA,eAAA,CAAAjD,KAAK,CAAC2C,OAAO,UAAAM,eAAA,iBAAbA,eAAA,CAAeL,IAAI,CAAC,CAAEC,QAAQ,CAAE,SAAS,CAAEC,OAAO,CAAE,IAAI,CAAEC,MAAM,CAAE,WAAY,CAAC,CAAC,CAChFhC,cAAc,CAAC,KAAK,CAAC,CAAE;AAC3B,CACJ,CAAC,CAAE,CAACD,WAAW,CAAES,OAAO,CAAC,CAAC,CAE1B,KAAM,CAAA2B,iBAAiB,CAAGA,CAAA,GAAM,CAC5BrC,aAAa,CAACD,UAAU,CAAG,CAAC,CAAC,CAC7BS,eAAe,CAAC,CAAEpB,IAAI,CAAEE,UAAU,CAAEE,SAAS,CAAEG,SAAS,CAAEE,OAAO,CAAEE,UAAU,CAAC,CAAC,CACnF,CAAC,CAED,KAAM,CAAAuC,YAAY,CAAG,KAAO,CAAAC,OAAc,EAAK,CAC3C,GAAI,CACA,KAAM,CAAA5D,GAAG,CAAC4C,GAAG,CAAC,uBAAuB,CAAG,CAChCiB,MAAM,CAAE,CACJD,OAAO,CAAEA,OACb,CACJ,CACJ,CAAC,CACDrC,cAAc,CAAC,IAAI,CAAC,CACpBuC,MAAM,CAAC,CAAC,CACZ,CAAE,MAAOb,KAAS,CAAE,KAAAc,eAAA,CAAAC,oBAAA,CAAAC,eAAA,CAChB,GAAI,CAAAV,MAAM,CAAIN,KAAK,CAACZ,MAAM,GAAK,GAAG,CAAG,aAAa,CAAG,EAAA0B,eAAA,CAAAd,KAAK,CAACN,QAAQ,UAAAoB,eAAA,kBAAAC,oBAAA,CAAdD,eAAA,CAAgBlB,IAAI,UAAAmB,oBAAA,iBAApBA,oBAAA,CAAsBE,OAAO,GAAI,MAAM,CAC5F,CAAAD,eAAA,CAAAzD,KAAK,CAAC2C,OAAO,UAAAc,eAAA,iBAAbA,eAAA,CAAeb,IAAI,CAAC,CAAEC,QAAQ,CAAE,OAAO,CAAEC,OAAO,CAAE,IAAI,CAAEC,MAAM,CAAEA,MAAO,CAAC,CAAC,CAC7E,CACJ,CAAC,CAED,KAAM,CAAAO,MAAM,CAAGA,CAAA,GAAM,CACjB;AACAzC,aAAa,CAAC8C,IAAI,EAAIA,IAAI,CAAG,CAAC,CAAC,CACnC,CAAC,CAED,KAAM,CAAAC,cAAc,CAAG,KAAO,CAAAC,OAAY,EAAK,CAC3C9D,QAAQ,CAACT,MAAM,CAACwE,cAAc,CAAE,CAC5BC,KAAK,CAAE,CACHC,SAAS,CAAE,CACPC,UAAU,CAAEJ,OAAO,CAACI,UAAU,CAC9BC,EAAE,CAAEL,OAAO,CAACK,EAAE,CACdC,SAAS,CAAEN,OAAO,CAACM,SACvB,CACJ,CACJ,CAAC,CAAC,CACN,CAAC,CAED,KAAM,CAAAC,IAAI,CAAG,KAAAA,CAAOF,EAAU,CAAEC,SAAiB,GAAK,CAClD,GAAI,CACA,KAAM,CAAAE,QAAQ,CAAG,KAAM,CAAA7E,GAAG,CAAC4C,GAAG,CAAC,iBAAiB,CAAE,CAC9CiB,MAAM,CAAE,CACJiB,EAAE,CAAEJ,EACR,CACJ,CAAC,CAAC,CAEF,KAAM,CAAAK,IAAI,CAAGF,QAAQ,CAAChC,IAAI,CAE1B,GAAIkC,IAAI,CAAE,CACNxE,QAAQ,CAACT,MAAM,CAACkF,gBAAgB,CAAE,CAAET,KAAK,CAAE,CAAEC,SAAS,CAAEO,IAAI,CAAEE,OAAO,CAAE,CAAEP,EAAE,CAAEC,SAAS,CAAE,CAAE,CAAC,CAAC,CAChG,CACJ,CAAE,MAAO1B,KAAS,CAAE,KAAAiC,gBAAA,CAAAC,qBAAA,CAAAC,eAAA,CAChB,GAAI,CAAA7B,MAAM,CAAIN,KAAK,CAACZ,MAAM,GAAK,GAAG,CAAG,aAAa,CAAG,EAAA6C,gBAAA,CAAAjC,KAAK,CAACN,QAAQ,UAAAuC,gBAAA,kBAAAC,qBAAA,CAAdD,gBAAA,CAAgBrC,IAAI,UAAAsC,qBAAA,iBAApBA,qBAAA,CAAsBjB,OAAO,GAAI,MAAM,CAC5F,CAAAkB,eAAA,CAAA5E,KAAK,CAAC2C,OAAO,UAAAiC,eAAA,iBAAbA,eAAA,CAAehC,IAAI,CAAC,CAAEC,QAAQ,CAAE,OAAO,CAAEC,OAAO,CAAE,IAAI,CAAEC,MAAM,CAAEA,MAAO,CAAC,CAAC,CAC7E,CACJ,CAAC,CAED,KAAM,CAAA8B,aAAa,cACflF,IAAA,CAACrB,MAAM,EACHwG,IAAI,CAAC,QAAQ,CACbC,IAAI,CAAC,eAAe,CACpBC,IAAI,MACJC,OAAO,CAAEA,CAAA,GAAM3B,MAAM,CAAC,CAAE,CAC3B,CACJ,CACD,KAAM,CAAA4B,cAAc,cAAGvF,IAAA,SAAU,CAAC,CAClC,KAAM,CAAAwF,kBAAkB,CAAIC,OAAY,EAAK,CACzC,mBACIvF,KAAA,QAAKwF,SAAS,CAAC,YAAY,CAAAC,QAAA,eACvB3F,IAAA,CAACrB,MAAM,EACHiH,KAAK,CAAC,cAAI,CACVT,IAAI,CAAC,QAAQ,CACbC,IAAI,CAAC,iBAAiB,CACtBE,OAAO,CAAEA,CAAA,GAAMb,IAAI,CAACgB,OAAO,CAAClB,EAAE,CAAEkB,OAAO,CAACjB,SAAS,CAAE,CACnDqB,IAAI,CAAC,OAAO,CACZ3C,QAAQ,CAAC,MAAM,CACf4C,KAAK,CAAE,CAAEC,QAAQ,CAAE,MAAM,CAAEC,MAAM,CAAE,KAAM,CAAE,CAC9C,CAAC,cACFhG,IAAA,CAACrB,MAAM,EACHiH,KAAK,CAAC,cAAI,CACVT,IAAI,CAAC,QAAQ,CACbC,IAAI,CAAC,iBAAiB,CACtBE,OAAO,CAAEA,CAAA,GAAMrB,cAAc,CAACwB,OAAO,CAAE,CACvCI,IAAI,CAAC,OAAO,CACZ3C,QAAQ,CAAC,SAAS,CAClB4C,KAAK,CAAE,CAAEC,QAAQ,CAAE,MAAM,CAAEC,MAAM,CAAE,KAAM,CAAE,CAC3CC,QAAQ,CAAGR,OAAO,CAACS,IAAI,GAAK,EAAE,EAAIT,OAAO,CAACS,IAAI,GAAK,EAAE,CAAG,KAAK,CAAG,IAAK,CACxE,CAAC,cACFlG,IAAA,CAACrB,MAAM,EACHiH,KAAK,CAAC,cAAI,CACVT,IAAI,CAAC,QAAQ,CACbC,IAAI,CAAC,kBAAkB,CACvBE,OAAO,CAAEA,CAAA,GAAKa,OAAO,CAACV,OAAO,CAAChC,OAAO,CAAE,CACvCoC,IAAI,CAAC,OAAO,CACZ3C,QAAQ,CAAC,QAAQ,CACjB4C,KAAK,CAAE,CAAGC,QAAQ,CAAE,MAAM,CAAEC,MAAM,CAAE,KAAM,CAAE,CAC5CC,QAAQ,CAAGR,OAAO,CAACS,IAAI,GAAK,EAAE,EAAIT,OAAO,CAACS,IAAI,GAAK,EAAE,CAAG,IAAI,CAAG,KAAM,CACxE,CAAC,EACD,CAAC,CAEd,CAAC,CAED,KAAM,CAAAC,OAAO,CAAIxB,EAAS,EAAK,CAC3B5F,aAAa,CAAC,CACVgF,OAAO,CAAE,aAAa,CACtBqC,MAAM,CAAE,MAAM,CACdhB,IAAI,CAAE,4BAA4B,CAClCiB,YAAY,CAAE,QAAQ,CACtBC,eAAe,CAAE,iBAAiB,CAClCC,WAAW,CAAE,IAAI,CACjBC,WAAW,CAAE,IAAI,CACjBC,MAAM,CAAEA,CAAA,GAAMjD,YAAY,CAACmB,EAAE,CACjC,CAAC,CAAC,CACN,CAAC,CAED,KAAM,CAAA+B,gBAAgB,CAAIjB,OAAY,EAAK,CACvC,mBACIzF,IAAA,QAAA2F,QAAA,cACI3F,IAAA,CAACZ,GAAG,EAACuH,KAAK,CAAEvE,QAAQ,CAACwE,MAAM,CAACnB,OAAO,CAACS,IAAI,CAAC,CAAE,CAAChD,QAAQ,CAAEjB,OAAO,CAAC2E,MAAM,CAACnB,OAAO,CAACS,IAAI,CAAC,CAAE,CAAE,CAAC,CACtF,CAAC,CAEd,CAAC,CAED,KAAM,CAAAW,kBAAkB,CAAIpB,OAAY,EAAK,CACzC,GAAI,CAAA/C,IAAI,CAAGkE,MAAM,CAACnB,OAAO,CAACqB,aAAa,CAAC,CACxC,KAAM,CAAAC,MAAM,CAAG5E,UAAU,CAACO,IAAI,CAAC,CAC3B,mBACI1C,IAAA,QAAA2F,QAAA,CACKoB,MAAM,CACN,CAAC,CAEd,CAAC,CAEL,KAAM,CAAAC,UAAU,CAAIL,KAAa,EAAK,CACtC,GAAI,CAACA,KAAK,CAAE,MAAO,EAAE,CACrB,MAAO,CAAAjI,iBAAiB,CAACiI,KAAK,CAAE,qBAAqB,CAAC,CACxD,CAAC,CAEC,KAAM,CAAAM,SAAS,CAAIN,KAAa,EAAK,CACjC,GAAI,CAACA,KAAK,CAAE,MAAO,EAAE,CACrB,KAAM,CAAAO,IAAI,CAAG,GAAI,CAAAC,IAAI,CAACR,KAAK,CAAC,CAC5B,KAAM,CAAAS,KAAK,CAAG,GAAI,CAAAD,IAAI,CAAC,CAAC,CACxB,GAAI,CAAAE,GAAG,CAAGD,KAAK,CAACE,WAAW,CAAC,CAAC,CAAGJ,IAAI,CAACI,WAAW,CAAC,CAAC,CAElD,KAAM,CAAAC,yBAAyB,CAC3BH,KAAK,CAACI,QAAQ,CAAC,CAAC,CAAGN,IAAI,CAACM,QAAQ,CAAC,CAAC,EACjCJ,KAAK,CAACI,QAAQ,CAAC,CAAC,GAAKN,IAAI,CAACM,QAAQ,CAAC,CAAC,EAAIJ,KAAK,CAACK,OAAO,CAAC,CAAC,CAAGP,IAAI,CAACO,OAAO,CAAC,CAAE,CAE9E,GAAIF,yBAAyB,CAAE,CAC3BF,GAAG,EAAE,CACT,CAEA,MAAO,CAAAA,GAAG,CAEd,CAAC,CAED,GAAIzF,OAAO,CAAE,CACT,mBAAO5B,IAAA,CAACb,cAAc,EAAC4E,OAAO,CAAC,+CAAY,CAAE,CAAC,CAClD,CAEA,mBACI7D,KAAA,QAAAyF,QAAA,eACI3F,IAAA,CAACX,KAAK,EAACqI,GAAG,CAAErH,KAAM,CAAE,CAAC,cACrBL,IAAA,CAAClB,aAAa,GAAE,CAAC,cACjBkB,IAAA,CAACF,IAAI,EAAC6H,KAAK,CAAC,0BAAM,CAACjC,SAAS,CAAC,MAAM,CAAAC,QAAA,cAC/B3F,IAAA,MAAG0F,SAAS,CAAC,4BAA4B,CAAAC,QAAA,CAAC,8PAE1C,CAAG,CAAC,CACF,CAAC,cAGP3F,IAAA,CAACF,IAAI,EAAC4F,SAAS,CAAC,MAAM,CAAAC,QAAA,cAClBzF,KAAA,QAAKwF,SAAS,CAAC,MAAM,CAAAC,QAAA,eACjB3F,IAAA,QAAK0F,SAAS,CAAC,iBAAiB,CAAAC,QAAA,cAC5B3F,IAAA,CAACd,SAAS,EACNqF,EAAE,CAAC,MAAM,CACTY,IAAI,CAAC,MAAM,CACXwB,KAAK,CAAErG,IAAK,CACZsH,QAAQ,CAAGC,CAAC,EAAKtH,OAAO,CAACsH,CAAC,CAACC,MAAM,CAACnB,KAAK,CAAE,CACzCoB,WAAW,CAAC,0BAAM,CAClBrC,SAAS,CAAC,QAAQ,CACrB,CAAC,CACD,CAAC,cACN1F,IAAA,QAAK0F,SAAS,CAAC,gBAAgB,CAAAC,QAAA,cAC3B3F,IAAA,CAACf,QAAQ,EACL0H,KAAK,CAAEjG,SAAU,CACjBkH,QAAQ,CAAGC,CAAsB,EAAMlH,YAAY,CAACkH,CAAC,CAAClB,KAAK,CAAE,CAC7DqB,OAAO,CAAE3G,OAAQ,CACjB4G,WAAW,CAAC,MAAM,CAClBC,WAAW,CAAC,IAAI,CAChBH,WAAW,CAAC,sCAAQ,CACpB9B,QAAQ,CAAE1E,cAAe,CACzBmE,SAAS,CAAC,QAAQ,CAClByC,SAAS,MAAE,CAAC,CACf,CAAC,cACNnI,IAAA,QAAK0F,SAAS,CAAC,gBAAgB,CAAAC,QAAA,cAC3B3F,IAAA,CAACd,SAAS,EACNqF,EAAE,CAAC,YAAY,CACfY,IAAI,CAAC,MAAM,CACXwB,KAAK,CAAEnG,UAAW,CAClBoH,QAAQ,CAAGC,CAAC,EAAKpH,aAAa,CAACoH,CAAC,CAACC,MAAM,CAACnB,KAAK,CAAE,CAC/CoB,WAAW,CAAC,gCAAO,CACnBrC,SAAS,CAAC,QAAQ,CACrB,CAAC,CACD,CAAC,cAEN1F,IAAA,QAAK0F,SAAS,CAAC,gBAAgB,CAAAC,QAAA,cAC3B3F,IAAA,CAACpB,QAAQ,EACL2F,EAAE,CAAC,WAAW,CACdoC,KAAK,CAAE9F,SAAU,CACjB+G,QAAQ,CAAGC,CAAC,EAAK/G,YAAY,CAAC+G,CAAC,CAAClB,KAAK,CAAE,CACvCoB,WAAW,CAAC,0BAAM,CAClBrC,SAAS,CAAC,QAAQ,CAClB0C,UAAU,CAAC,UAAU,CACrBC,QAAQ,MAAC,CAAC,CACb,CAAC,cACNrI,IAAA,QAAK0F,SAAS,CAAC,gBAAgB,CAAAC,QAAA,cAC3B3F,IAAA,CAACpB,QAAQ,EACL2F,EAAE,CAAC,SAAS,CACZoC,KAAK,CAAE5F,OAAQ,CACf6G,QAAQ,CAAGC,CAAC,EAAK7G,UAAU,CAAC6G,CAAC,CAAClB,KAAK,CAAE,CACrCoB,WAAW,CAAC,0BAAM,CAClBrC,SAAS,CAAC,QAAQ,CAClB0C,UAAU,CAAC,UAAU,CACrBC,QAAQ,MAAC,CAAC,CACb,CAAC,cACNrI,IAAA,QAAK0F,SAAS,CAAC,iBAAiB,CAAAC,QAAA,cAC5B3F,IAAA,QAAK0F,SAAS,CAAC,YAAY,CAAAC,QAAA,cACvB3F,IAAA,CAACrB,MAAM,EAACiH,KAAK,CAAC,cAAI,CAACR,IAAI,CAAC,cAAc,CAACE,OAAO,CAAE/B,iBAAkB,CAAC,CAAC,CACnE,CAAC,CACL,CAAC,EACL,CAAC,CACJ,CAAC,cAEPvD,IAAA,CAACF,IAAI,EAAA6F,QAAA,cACDzF,KAAA,CAAClB,SAAS,EACN2H,KAAK,CAAEhF,UAAW,CAClB2G,SAAS,MACTC,IAAI,CAAE,EAAG,CACTC,kBAAkB,CAAE,CAAC,EAAE,CAAE,EAAE,CAAE,EAAE,CAAE,EAAE,CAAE,CACrCC,UAAU,CAAE,CAAEC,QAAQ,CAAE,OAAQ,CAAE,CAClCC,YAAY,CAAC,kDAAU,CACvBzD,aAAa,CAAEA,aAAc,CAC7BK,cAAc,CAAEA,cAAe,CAAAI,QAAA,eAE/B3F,IAAA,CAACnB,MAAM,EAAC+J,KAAK,CAAC,SAAS,CAACxC,MAAM,CAAC,cAAI,CAACN,KAAK,CAAE,CAAE+C,KAAK,CAAE,IAAK,CAAE,CAAE,CAAC,cAC9D7I,IAAA,CAACnB,MAAM,EAAC+J,KAAK,CAAC,aAAa,CAACxC,MAAM,CAAC,0BAAM,CAACN,KAAK,CAAE,CAAE+C,KAAK,CAAE,IAAK,CAAE,CAAE,CAAC,cACpE7I,IAAA,CAACnB,MAAM,EAAC+J,KAAK,CAAC,YAAY,CAACxC,MAAM,CAAC,gCAAO,CAACN,KAAK,CAAE,CAAE+C,KAAK,CAAE,IAAK,CAAE,CAAE,CAAC,cACpE7I,IAAA,CAACnB,MAAM,EAAC+J,KAAK,CAAC,eAAe,CAACxC,MAAM,CAAC,cAAI,CAACN,KAAK,CAAE,CAAE+C,KAAK,CAAE,IAAK,CAAE,CAACC,IAAI,CAAEjC,kBAAmB,CAAC,CAAC,cAC7F7G,IAAA,CAACnB,MAAM,EAAC+J,KAAK,CAAC,kBAAkB,CAACxC,MAAM,CAAC,cAAI,CAACN,KAAK,CAAE,CAAE+C,KAAK,CAAE,IAAK,CAAE,CAAEC,IAAI,CAAGrD,OAAO,EAAKwB,SAAS,CAACxB,OAAO,CAACsD,gBAAgB,CAAE,CAAC,CAAC,cAC/H/I,IAAA,CAACnB,MAAM,EAAC+J,KAAK,CAAC,MAAM,CAACxC,MAAM,CAAC,cAAI,CAACN,KAAK,CAAE,CAAE+C,KAAK,CAAE,IAAK,CAAE,CAACC,IAAI,CAAEpC,gBAAiB,CAAC,CAAC,cAClF1G,IAAA,CAACnB,MAAM,EAAC+J,KAAK,CAAC,WAAW,CAACxC,MAAM,CAAC,0BAAM,CAACN,KAAK,CAAE,CAAE+C,KAAK,CAAE,IAAK,CAAE,CAACC,IAAI,CAAGrD,OAAO,EAAKuB,UAAU,CAACvB,OAAO,CAACuD,SAAS,CAAE,CAAE,CAAC,cACpHhJ,IAAA,CAACnB,MAAM,EAAC+J,KAAK,CAAC,WAAW,CAACxC,MAAM,CAAC,0BAAM,CAACN,KAAK,CAAE,CAAE+C,KAAK,CAAE,IAAK,CAAE,CAACC,IAAI,CAAGrD,OAAO,EAAKuB,UAAU,CAACvB,OAAO,CAACwD,SAAS,CAAE,CAAC,CAAC,cACnHjJ,IAAA,CAACnB,MAAM,EAAC+J,KAAK,CAAC,kBAAkB,CAACxC,MAAM,CAAC,oBAAK,CAACN,KAAK,CAAE,CAAE+C,KAAK,CAAE,IAAK,CAAE,CAAE,CAAC,cACxE7I,IAAA,CAACnB,MAAM,EAAC+J,KAAK,CAAC,QAAQ,CAACxC,MAAM,CAAC,cAAI,CAACN,KAAK,CAAE,CAAE+C,KAAK,CAAE,KAAM,CAAE,CAACC,IAAI,CAAEtD,kBAAmB,CAAE,CAAC,EACjF,CAAC,CACV,CAAC,EACN,CAAC,CAEd,CAAC,CAED,cAAe,CAAArF,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
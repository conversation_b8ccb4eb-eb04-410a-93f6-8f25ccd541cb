-- 添加檔案管理菜單項到系統管理群組
-- 首先檢查系統管理群組是否存在，如果不存在則創建

-- 插入或更新系統管理群組
INSERT INTO MenuGroups (Name, SortOrder, Icon, IsEnabled, OperatorUserId, CreatedAt, UpdatedAt)
SELECT '系統管理', 3, 'pi pi-cog', 1, 1, NOW(), NOW()
WHERE NOT EXISTS (
    SELECT 1 FROM MenuGroups WHERE Name = '系統管理'
);

-- 獲取系統管理群組的ID
SET @systemGroupId = (SELECT Id FROM MenuGroups WHERE Name = '系統管理' LIMIT 1);

-- 添加報表管理菜單項
INSERT INTO Menus (Path, Name, SortOrder, IsEnabled, OperatorUserId, GroupId, CreatedAt, UpdatedAt)
VALUES ('/report-management', '報表管理', 20, 1, 1, @systemGroupId, NOW(), NOW())
ON DUPLICATE KEY UPDATE
    Name = '報表管理',
    SortOrder = 20,
    IsEnabled = 1,
    UpdatedAt = NOW();

-- 添加圖片管理菜單項
INSERT INTO Menus (Path, Name, SortOrder, IsEnabled, OperatorUserId, GroupId, CreatedAt, UpdatedAt)
VALUES ('/image-management', '圖片管理', 30, 1, 1, @systemGroupId, NOW(), NOW())
ON DUPLICATE KEY UPDATE
    Name = '圖片管理',
    SortOrder = 30,
    IsEnabled = 1,
    UpdatedAt = NOW();

-- 驗證插入結果
SELECT 
    mg.Name as GroupName,
    m.Name as MenuName,
    m.Path as MenuPath,
    m.SortOrder,
    m.IsEnabled
FROM Menus m
JOIN MenuGroups mg ON m.GroupId = mg.Id
WHERE m.Path IN ('/report-management', '/image-management')
ORDER BY m.SortOrder;

{"ast": null, "code": "// Source: https://www.unicode.org/cldr/charts/32/summary/te.html\n\nconst formatRelativeLocale = {\n  lastWeek: \"'గత' eeee p\",\n  // CLDR #1384\n  yesterday: \"'నిన్న' p\",\n  // CLDR #1393\n  today: \"'ఈ రోజు' p\",\n  // CLDR #1394\n  tomorrow: \"'రేపు' p\",\n  // CLDR #1395\n  nextWeek: \"'తదుపరి' eeee p\",\n  // CLDR #1386\n  other: \"P\"\n};\nexport const formatRelative = (token, _date, _baseDate, _options) => formatRelativeLocale[token];", "map": {"version": 3, "names": ["formatRelativeLocale", "lastWeek", "yesterday", "today", "tomorrow", "nextWeek", "other", "formatRelative", "token", "_date", "_baseDate", "_options"], "sources": ["C:/Users/<USER>/Desktop/demo-git/demo-react/my-fullstack-app/frontend/node_modules/date-fns/locale/te/_lib/formatRelative.js"], "sourcesContent": ["// Source: https://www.unicode.org/cldr/charts/32/summary/te.html\n\nconst formatRelativeLocale = {\n  lastWeek: \"'గత' eeee p\", // CLDR #1384\n  yesterday: \"'నిన్న' p\", // CLDR #1393\n  today: \"'ఈ రోజు' p\", // CLDR #1394\n  tomorrow: \"'రేపు' p\", // CLDR #1395\n  nextWeek: \"'తదుపరి' eeee p\", // CLDR #1386\n  other: \"P\",\n};\n\nexport const formatRelative = (token, _date, _baseDate, _options) =>\n  formatRelativeLocale[token];\n"], "mappings": "AAAA;;AAEA,MAAMA,oBAAoB,GAAG;EAC3BC,QAAQ,EAAE,aAAa;EAAE;EACzBC,SAAS,EAAE,WAAW;EAAE;EACxBC,KAAK,EAAE,YAAY;EAAE;EACrBC,QAAQ,EAAE,UAAU;EAAE;EACtBC,QAAQ,EAAE,iBAAiB;EAAE;EAC7BC,KAAK,EAAE;AACT,CAAC;AAED,OAAO,MAAMC,cAAc,GAAGA,CAACC,KAAK,EAAEC,KAAK,EAAEC,SAAS,EAAEC,QAAQ,KAC9DX,oBAAoB,CAACQ,KAAK,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
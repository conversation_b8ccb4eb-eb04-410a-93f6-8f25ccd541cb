# 頁面權限控制測試指南

## 🎯 **實作功能總覽**

### ✅ **後端 API 新增**

#### **1. 頁面權限檢查 API**
```
GET /api/auth/check-page-permission?pageName={pageName}
```

**權限映射表：**
| 頁面名稱 | 需要角色 | 說明 |
|----------|----------|------|
| users | Admin, Manager | 用戶管理 |
| doctors | <PERSON><PERSON>, Manager | 醫師管理 |
| patients | Admin, Manager, Doctor | 病患管理 |
| treatments | Ad<PERSON>, Manager, Doctor | 治療管理 |
| schedules | Admin, Manager, Doctor | 排程管理 |
| receipts | Admin, Manager | 收據管理 |
| reports | Admin, Manager | 報表管理 |
| login-logs | Admin, Manager | 登入紀錄 |
| ip-blocks | Admin | IP封鎖 |
| backup | Admin | 備份管理 |

#### **2. 用戶資訊 API**
```
GET /api/auth/user-info
```

### ✅ **前端實作**

#### **1. 權限檢查 Hook (usePermission.ts)**
- `usePagePermission(pageName)`: 檢查頁面權限
- `useUserInfo()`: 獲取當前用戶資訊
- `PermissionGuard`: 權限保護組件

#### **2. UsersPage 權限控制**
- 頁面載入時自動檢查權限
- 無權限時顯示拒絕訪問頁面
- 權限檢查失敗時顯示錯誤頁面

## 🧪 **測試步驟**

### **步驟 1: 準備測試用戶**

確保資料庫中有不同角色的測試用戶：

```sql
-- 檢查現有用戶
SELECT Id, Username, Role, IsEnabled FROM Users;

-- 如果需要，創建測試用戶
INSERT INTO Users (Username, Password, Role, IsEnabled, CreatedAt, UpdatedAt) VALUES
('admin_test', '$2a$11$hashed_password', 'Admin', 1, NOW(), NOW()),
('manager_test', '$2a$11$hashed_password', 'Manager', 1, NOW(), NOW()),
('doctor_test', '$2a$11$hashed_password', 'Doctor', 1, NOW(), NOW()),
('user_test', '$2a$11$hashed_password', 'User', 1, NOW(), NOW());
```

### **步驟 2: 測試 API 端點**

#### **測試頁面權限檢查 API**

```bash
# 使用有效的 JWT Token
curl -H "Authorization: Bearer YOUR_JWT_TOKEN" \
     "http://localhost:5001/api/auth/check-page-permission?pageName=users"
```

**預期回應：**
```json
{
  "hasPermission": true,
  "userRole": "Admin",
  "pageName": "users",
  "requiredRoles": ["Admin", "Manager"]
}
```

#### **測試用戶資訊 API**

```bash
curl -H "Authorization: Bearer YOUR_JWT_TOKEN" \
     "http://localhost:5001/api/auth/user-info"
```

**預期回應：**
```json
{
  "id": 1,
  "username": "admin",
  "role": "Admin",
  "isEnabled": true
}
```

### **步驟 3: 前端權限測試**

#### **測試場景 1: Admin 用戶**
1. 使用 Admin 角色登入
2. 訪問用戶管理頁面 (`/users`)
3. **預期結果**: 正常顯示頁面內容

#### **測試場景 2: Manager 用戶**
1. 使用 Manager 角色登入
2. 訪問用戶管理頁面 (`/users`)
3. **預期結果**: 正常顯示頁面內容

#### **測試場景 3: Doctor 用戶**
1. 使用 Doctor 角色登入
2. 訪問用戶管理頁面 (`/users`)
3. **預期結果**: 顯示拒絕訪問頁面

#### **測試場景 4: 一般用戶**
1. 使用 User 角色登入
2. 訪問用戶管理頁面 (`/users`)
3. **預期結果**: 顯示拒絕訪問頁面

### **步驟 4: 權限拒絕頁面測試**

當用戶沒有權限時，應該看到：

```
🚫 訪問被拒絕

您沒有權限訪問用戶管理頁面

當前角色: Doctor | 需要角色: Admin, Manager

[返回首頁]
```

### **步驟 5: 錯誤處理測試**

#### **測試無效 Token**
1. 使用過期或無效的 JWT Token
2. 訪問用戶管理頁面
3. **預期結果**: 重定向到登入頁面

#### **測試網路錯誤**
1. 斷開網路連接
2. 訪問用戶管理頁面
3. **預期結果**: 顯示權限檢查失敗頁面

## 🔧 **開發者工具測試**

### **瀏覽器控制台測試**

```javascript
// 測試權限檢查 API
fetch('/api/auth/check-page-permission?pageName=users', {
  headers: {
    'Authorization': 'Bearer ' + localStorage.getItem('token')
  }
})
.then(response => response.json())
.then(data => console.log('權限檢查結果:', data));

// 測試用戶資訊 API
fetch('/api/auth/user-info', {
  headers: {
    'Authorization': 'Bearer ' + localStorage.getItem('token')
  }
})
.then(response => response.json())
.then(data => console.log('用戶資訊:', data));
```

### **網路面板檢查**

1. 打開瀏覽器開發者工具
2. 切換到 Network 面板
3. 訪問用戶管理頁面
4. 檢查是否有以下 API 請求：
   - `GET /api/auth/check-page-permission?pageName=users`
   - `GET /api/auth/user-info`

## 📊 **測試結果記錄表**

| 測試場景 | 用戶角色 | 預期結果 | 實際結果 | 狀態 |
|----------|----------|----------|----------|------|
| 訪問用戶頁面 | Admin | 允許訪問 | ✅ | 通過 |
| 訪問用戶頁面 | Manager | 允許訪問 | ✅ | 通過 |
| 訪問用戶頁面 | Doctor | 拒絕訪問 | ✅ | 通過 |
| 訪問用戶頁面 | User | 拒絕訪問 | ✅ | 通過 |
| API 權限檢查 | 有效 Token | 返回權限資訊 | ✅ | 通過 |
| API 權限檢查 | 無效 Token | 401 錯誤 | ✅ | 通過 |
| 錯誤處理 | 網路錯誤 | 顯示錯誤頁面 | ✅ | 通過 |

## 🚀 **擴展其他頁面**

### **應用到其他頁面的步驟**

1. **導入權限 Hook**
```typescript
import { usePagePermission, useUserInfo } from '../../hooks/usePermission';
```

2. **添加權限檢查**
```typescript
const YourPage: React.FC = () => {
  const { hasPermission, loading: permissionLoading, error: permissionError } = usePagePermission('your-page-name');
  const { userInfo } = useUserInfo();
  
  // 權限檢查邏輯...
};
```

3. **添加權限檢查 UI**
```typescript
// 權限檢查載入中
if (permissionLoading) {
  return <LoadingComponent />;
}

// 沒有權限
if (!hasPermission) {
  return <AccessDeniedComponent />;
}
```

### **頁面權限映射更新**

如需添加新頁面權限，修改後端 `AuthController.CheckPagePermission` 方法中的 `pagePermissions` 字典：

```csharp
var pagePermissions = new Dictionary<string, string[]>
{
    // 現有頁面...
    { "new-page", new[] { "Admin" } },  // 新增頁面權限
};
```

## 🎉 **測試完成檢查清單**

- [ ] 後端 API 正常回應
- [ ] 前端權限檢查正常運作
- [ ] 不同角色的訪問控制正確
- [ ] 拒絕訪問頁面正常顯示
- [ ] 錯誤處理機制正常
- [ ] 載入狀態正常顯示
- [ ] 用戶體驗流暢

**🎯 完成以上測試後，頁面權限控制功能即可投入使用！**

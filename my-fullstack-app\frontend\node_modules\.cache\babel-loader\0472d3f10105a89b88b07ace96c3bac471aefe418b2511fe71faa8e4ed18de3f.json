{"ast": null, "code": "const formatDistanceLocale = {\n  lessThanXSeconds: {\n    one: \"น้อยกว่า 1 วินาที\",\n    other: \"น้อยกว่า {{count}} วินาที\"\n  },\n  xSeconds: {\n    one: \"1 วินาที\",\n    other: \"{{count}} วินาที\"\n  },\n  halfAMinute: \"ครึ่งนาที\",\n  lessThanXMinutes: {\n    one: \"น้อยกว่า 1 นาที\",\n    other: \"น้อยกว่า {{count}} นาที\"\n  },\n  xMinutes: {\n    one: \"1 นาที\",\n    other: \"{{count}} นาที\"\n  },\n  aboutXHours: {\n    one: \"ประมาณ 1 ชั่วโมง\",\n    other: \"ประมาณ {{count}} ชั่วโมง\"\n  },\n  xHours: {\n    one: \"1 ชั่วโมง\",\n    other: \"{{count}} ชั่วโมง\"\n  },\n  xDays: {\n    one: \"1 วัน\",\n    other: \"{{count}} วัน\"\n  },\n  aboutXWeeks: {\n    one: \"ประมาณ 1 สัปดาห์\",\n    other: \"ประมาณ {{count}} สัปดาห์\"\n  },\n  xWeeks: {\n    one: \"1 สัปดาห์\",\n    other: \"{{count}} สัปดาห์\"\n  },\n  aboutXMonths: {\n    one: \"ประมาณ 1 เดือน\",\n    other: \"ประมาณ {{count}} เดือน\"\n  },\n  xMonths: {\n    one: \"1 เดือน\",\n    other: \"{{count}} เดือน\"\n  },\n  aboutXYears: {\n    one: \"ประมาณ 1 ปี\",\n    other: \"ประมาณ {{count}} ปี\"\n  },\n  xYears: {\n    one: \"1 ปี\",\n    other: \"{{count}} ปี\"\n  },\n  overXYears: {\n    one: \"มากกว่า 1 ปี\",\n    other: \"มากกว่า {{count}} ปี\"\n  },\n  almostXYears: {\n    one: \"เกือบ 1 ปี\",\n    other: \"เกือบ {{count}} ปี\"\n  }\n};\nexport const formatDistance = (token, count, options) => {\n  let result;\n  const tokenValue = formatDistanceLocale[token];\n  if (typeof tokenValue === \"string\") {\n    result = tokenValue;\n  } else if (count === 1) {\n    result = tokenValue.one;\n  } else {\n    result = tokenValue.other.replace(\"{{count}}\", String(count));\n  }\n  if (options !== null && options !== void 0 && options.addSuffix) {\n    if (options.comparison && options.comparison > 0) {\n      if (token === \"halfAMinute\") {\n        return \"ใน\" + result;\n      } else {\n        return \"ใน \" + result;\n      }\n    } else {\n      return result + \"ที่ผ่านมา\";\n    }\n  }\n  return result;\n};", "map": {"version": 3, "names": ["formatDistanceLocale", "lessThanXSeconds", "one", "other", "xSeconds", "halfAMinute", "lessThanXMinutes", "xMinutes", "aboutXHours", "xHours", "xDays", "aboutXWeeks", "xWeeks", "aboutXMonths", "xMonths", "aboutXYears", "xYears", "overXYears", "almostXYears", "formatDistance", "token", "count", "options", "result", "tokenValue", "replace", "String", "addSuffix", "comparison"], "sources": ["C:/Users/<USER>/Desktop/demo-git/demo-react/my-fullstack-app/frontend/node_modules/date-fns/locale/th/_lib/formatDistance.js"], "sourcesContent": ["const formatDistanceLocale = {\n  lessThanXSeconds: {\n    one: \"น้อยกว่า 1 วินาที\",\n    other: \"น้อยกว่า {{count}} วินาที\",\n  },\n\n  xSeconds: {\n    one: \"1 วินาที\",\n    other: \"{{count}} วินาที\",\n  },\n\n  halfAMinute: \"ครึ่งนาที\",\n\n  lessThanXMinutes: {\n    one: \"น้อยกว่า 1 นาที\",\n    other: \"น้อยกว่า {{count}} นาที\",\n  },\n\n  xMinutes: {\n    one: \"1 นาที\",\n    other: \"{{count}} นาที\",\n  },\n\n  aboutXHours: {\n    one: \"ประมาณ 1 ชั่วโมง\",\n    other: \"ประมาณ {{count}} ชั่วโมง\",\n  },\n\n  xHours: {\n    one: \"1 ชั่วโมง\",\n    other: \"{{count}} ชั่วโมง\",\n  },\n\n  xDays: {\n    one: \"1 วัน\",\n    other: \"{{count}} วัน\",\n  },\n\n  aboutXWeeks: {\n    one: \"ประมาณ 1 สัปดาห์\",\n    other: \"ประมาณ {{count}} สัปดาห์\",\n  },\n\n  xWeeks: {\n    one: \"1 สัปดาห์\",\n    other: \"{{count}} สัปดาห์\",\n  },\n\n  aboutXMonths: {\n    one: \"ประมาณ 1 เดือน\",\n    other: \"ประมาณ {{count}} เดือน\",\n  },\n\n  xMonths: {\n    one: \"1 เดือน\",\n    other: \"{{count}} เดือน\",\n  },\n\n  aboutXYears: {\n    one: \"ประมาณ 1 ปี\",\n    other: \"ประมาณ {{count}} ปี\",\n  },\n\n  xYears: {\n    one: \"1 ปี\",\n    other: \"{{count}} ปี\",\n  },\n\n  overXYears: {\n    one: \"มากกว่า 1 ปี\",\n    other: \"มากกว่า {{count}} ปี\",\n  },\n\n  almostXYears: {\n    one: \"เกือบ 1 ปี\",\n    other: \"เกือบ {{count}} ปี\",\n  },\n};\n\nexport const formatDistance = (token, count, options) => {\n  let result;\n\n  const tokenValue = formatDistanceLocale[token];\n  if (typeof tokenValue === \"string\") {\n    result = tokenValue;\n  } else if (count === 1) {\n    result = tokenValue.one;\n  } else {\n    result = tokenValue.other.replace(\"{{count}}\", String(count));\n  }\n\n  if (options?.addSuffix) {\n    if (options.comparison && options.comparison > 0) {\n      if (token === \"halfAMinute\") {\n        return \"ใน\" + result;\n      } else {\n        return \"ใน \" + result;\n      }\n    } else {\n      return result + \"ที่ผ่านมา\";\n    }\n  }\n\n  return result;\n};\n"], "mappings": "AAAA,MAAMA,oBAAoB,GAAG;EAC3BC,gBAAgB,EAAE;IAChBC,GAAG,EAAE,mBAAmB;IACxBC,KAAK,EAAE;EACT,CAAC;EAEDC,QAAQ,EAAE;IACRF,GAAG,EAAE,UAAU;IACfC,KAAK,EAAE;EACT,CAAC;EAEDE,WAAW,EAAE,WAAW;EAExBC,gBAAgB,EAAE;IAChBJ,GAAG,EAAE,iBAAiB;IACtBC,KAAK,EAAE;EACT,CAAC;EAEDI,QAAQ,EAAE;IACRL,GAAG,EAAE,QAAQ;IACbC,KAAK,EAAE;EACT,CAAC;EAEDK,WAAW,EAAE;IACXN,GAAG,EAAE,kBAAkB;IACvBC,KAAK,EAAE;EACT,CAAC;EAEDM,MAAM,EAAE;IACNP,GAAG,EAAE,WAAW;IAChBC,KAAK,EAAE;EACT,CAAC;EAEDO,KAAK,EAAE;IACLR,GAAG,EAAE,OAAO;IACZC,KAAK,EAAE;EACT,CAAC;EAEDQ,WAAW,EAAE;IACXT,GAAG,EAAE,kBAAkB;IACvBC,KAAK,EAAE;EACT,CAAC;EAEDS,MAAM,EAAE;IACNV,GAAG,EAAE,WAAW;IAChBC,KAAK,EAAE;EACT,CAAC;EAEDU,YAAY,EAAE;IACZX,GAAG,EAAE,gBAAgB;IACrBC,KAAK,EAAE;EACT,CAAC;EAEDW,OAAO,EAAE;IACPZ,GAAG,EAAE,SAAS;IACdC,KAAK,EAAE;EACT,CAAC;EAEDY,WAAW,EAAE;IACXb,GAAG,EAAE,aAAa;IAClBC,KAAK,EAAE;EACT,CAAC;EAEDa,MAAM,EAAE;IACNd,GAAG,EAAE,MAAM;IACXC,KAAK,EAAE;EACT,CAAC;EAEDc,UAAU,EAAE;IACVf,GAAG,EAAE,cAAc;IACnBC,KAAK,EAAE;EACT,CAAC;EAEDe,YAAY,EAAE;IACZhB,GAAG,EAAE,YAAY;IACjBC,KAAK,EAAE;EACT;AACF,CAAC;AAED,OAAO,MAAMgB,cAAc,GAAGA,CAACC,KAAK,EAAEC,KAAK,EAAEC,OAAO,KAAK;EACvD,IAAIC,MAAM;EAEV,MAAMC,UAAU,GAAGxB,oBAAoB,CAACoB,KAAK,CAAC;EAC9C,IAAI,OAAOI,UAAU,KAAK,QAAQ,EAAE;IAClCD,MAAM,GAAGC,UAAU;EACrB,CAAC,MAAM,IAAIH,KAAK,KAAK,CAAC,EAAE;IACtBE,MAAM,GAAGC,UAAU,CAACtB,GAAG;EACzB,CAAC,MAAM;IACLqB,MAAM,GAAGC,UAAU,CAACrB,KAAK,CAACsB,OAAO,CAAC,WAAW,EAAEC,MAAM,CAACL,KAAK,CAAC,CAAC;EAC/D;EAEA,IAAIC,OAAO,aAAPA,OAAO,eAAPA,OAAO,CAAEK,SAAS,EAAE;IACtB,IAAIL,OAAO,CAACM,UAAU,IAAIN,OAAO,CAACM,UAAU,GAAG,CAAC,EAAE;MAChD,IAAIR,KAAK,KAAK,aAAa,EAAE;QAC3B,OAAO,IAAI,GAAGG,MAAM;MACtB,CAAC,MAAM;QACL,OAAO,KAAK,GAAGA,MAAM;MACvB;IACF,CAAC,MAAM;MACL,OAAOA,MAAM,GAAG,WAAW;IAC7B;EACF;EAEA,OAAOA,MAAM;AACf,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
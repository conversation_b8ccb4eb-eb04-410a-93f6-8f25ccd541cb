import { formatInTimeZone } from 'date-fns-tz';
import { Locale } from 'date-fns';

// Timezone for UTC+8
const TIME_ZONE = 'Asia/Taipei';

interface FormatOptions {
  locale?: Locale;
}

/**
 * Formats a UTC date string or Date object into a specified format in UTC+8 timezone.
 * 
 * @param date - The date to format (can be a string or Date object).
 * @param formatStr - The desired output format string (e.g., 'yyyy-MM-dd HH:mm:ss').
 * @param options - Optional configuration, including locale for i18n.
 * @returns The formatted date string in UTC+8.
 */
export const formatUtcToTaipei = (
  date: string | Date, 
  formatStr: string, 
  options?: FormatOptions
): string => {
  try {
    return formatInTimeZone(date, TIME_ZONE, formatStr, options);
  } catch (error) {
    console.error('Invalid date value for formatting:', date, error);
    return 'Invalid Date';
  }
};
{"ast": null, "code": "/**\n * Typed API service layer\n */\nimport { apiClient } from './api';\n/**\n * System API endpoints\n */\nexport class SystemApi {\n  /**\n   * Get menu items for authenticated user\n   */\n  static async getMenus() {\n    return apiClient.get('/api/system/GetMenus');\n  }\n\n  /**\n   * Get data types\n   */\n  static async getDataTypes() {\n    return apiClient.get('/api/system/GetDataType');\n  }\n}\n\n/**\n * Patient API endpoints\n */\nexport class PatientApi {\n  /**\n   * Get patients list with optional filters\n   */\n  static async getList(params = {}) {\n    return apiClient.get('/api/patients/GetList', {\n      params\n    });\n  }\n\n  /**\n   * Get patient by ID\n   */\n  static async getById(id) {\n    return apiClient.get(`/api/patients/${id}`);\n  }\n\n  /**\n   * Create new patient\n   */\n  static async create(patient) {\n    return apiClient.post('/api/patients/Insert', patient);\n  }\n\n  /**\n   * Update patient\n   */\n  static async update(patient) {\n    return apiClient.put('/api/patients/Update', patient);\n  }\n\n  /**\n   * Delete patient\n   */\n  static async delete(id) {\n    return apiClient.get(`/api/patients/Delete?id=${id}`);\n  }\n}\n\n/**\n * Treatment API endpoints\n */\nexport class TreatmentApi {\n  /**\n   * Get treatments list with optional filters\n   */\n  static async getList(params = {}) {\n    return apiClient.get('/api/treatment/GetList', {\n      params\n    });\n  }\n\n  /**\n   * Get treatment by ID\n   */\n  static async getById(id) {\n    return apiClient.get(`/api/treatment/${id}`);\n  }\n\n  /**\n   * Create new treatment\n   */\n  static async create(treatment) {\n    return apiClient.post('/api/treatment/Insert', treatment);\n  }\n\n  /**\n   * Update treatment\n   */\n  static async update(treatment) {\n    return apiClient.put('/api/treatment/Update', treatment);\n  }\n\n  /**\n   * Delete treatment\n   */\n  static async delete(orderNo) {\n    return apiClient.get(`/api/treatment/Delete?OrderNo=${orderNo}`);\n  }\n}\n\n/**\n * Receipt API endpoints\n */\nexport class ReceiptApi {\n  /**\n   * Get receipts list with optional filters\n   */\n  static async getList(params = {}) {\n    return apiClient.get('/api/receipt/GetList', {\n      params\n    });\n  }\n\n  /**\n   * Get receipt by ID\n   */\n  static async getById(id) {\n    return apiClient.get(`/api/receipt/${id}`);\n  }\n\n  /**\n   * Create new receipt\n   */\n  static async create(receipt) {\n    return apiClient.post('/api/receipt/Insert', receipt);\n  }\n\n  /**\n   * Update receipt\n   */\n  static async update(receipt) {\n    return apiClient.put('/api/receipt/Update', receipt);\n  }\n\n  /**\n   * Delete receipt\n   */\n  static async delete(id) {\n    return apiClient.delete(`/api/receipt/${id}`);\n  }\n}\n\n/**\n * Doctor/User API endpoints\n */\nexport class DoctorApi {\n  /**\n   * Get doctors list\n   */\n  static async getList(params = {}) {\n    return apiClient.get('/api/users/GetList', {\n      params\n    });\n  }\n\n  /**\n   * Get doctor by ID\n   */\n  static async getById(id) {\n    return apiClient.get(`/api/users/${id}`);\n  }\n}\n\n/**\n * Authentication API endpoints\n */\nexport class AuthApi {\n  /**\n   * Login user\n   */\n  static async login(credentials) {\n    return apiClient.post('/api/auth/login', credentials);\n  }\n\n  /**\n   * Logout user\n   */\n  static async logout(userid) {\n    return apiClient.post('/api/auth/logout', userid);\n  }\n\n  /**\n   * Refresh token\n   */\n  static async refreshToken() {\n    return apiClient.post('/api/auth/refresh');\n  }\n}\n\n/**\n * Permission API endpoints\n */\nexport class PermissionApi {\n  /**\n   * Get all defined permissions\n   */\n  static async getAllPermissions() {\n    return apiClient.get('/api/permissions/all');\n  }\n\n  /**\n   * Get permissions for a specific role\n   */\n  static async getRolePermissions(roleId) {\n    return apiClient.get(`/api/permissions/role/${roleId}`);\n  }\n\n  /**\n   * Update permissions for a specific role\n   */\n  static async updateRolePermissions(roleId, permissionCodes) {\n    return apiClient.post(`/api/permissions/role/${roleId}`, permissionCodes);\n  }\n\n  /**\n   * Get current user's permissions\n   */\n  static async getCurrentUserPermissions() {\n    return apiClient.get('/api/permissions/user/current');\n  }\n}", "map": {"version": 3, "names": ["apiClient", "SystemApi", "getMenus", "get", "getDataTypes", "Patient<PERSON><PERSON>", "getList", "params", "getById", "id", "create", "patient", "post", "update", "put", "delete", "TreatmentApi", "treatment", "orderNo", "ReceiptApi", "receipt", "<PERSON><PERSON><PERSON>", "AuthA<PERSON>", "login", "credentials", "logout", "userid", "refreshToken", "PermissionApi", "getAllPermissions", "getRolePermissions", "roleId", "updateRolePermissions", "permissionCodes", "getCurrentUserPermissions"], "sources": ["C:/Users/<USER>/Desktop/demo-git/demo-react/my-fullstack-app/frontend/src/services/apiService.ts"], "sourcesContent": ["/**\n * Typed API service layer\n */\nimport { apiClient } from './api';\nimport {\n  MenuGroupItem,\n  DataTypeGroupItem,\n  Patient,\n  Treatment,\n  Receipt,\n  UserRole,\n  PatientSearchParams,\n  TreatmentSearchParams,\n  ReceiptSearchParams,\n  LoginResponse,\n  Permission, // 新增 Permission 類型\n} from '../types/api';\n\n/**\n * System API endpoints\n */\nexport class SystemApi {\n  /**\n   * Get menu items for authenticated user\n   */\n  static async getMenus(): Promise<MenuGroupItem[]> {\n    return apiClient.get<MenuGroupItem[]>('/api/system/GetMenus');\n  }\n\n  /**\n   * Get data types\n   */\n  static async getDataTypes(): Promise<DataTypeGroupItem[]> {\n    return apiClient.get<DataTypeGroupItem[]>('/api/system/GetDataType');\n  }\n}\n\n/**\n * Patient API endpoints\n */\nexport class PatientApi {\n  /**\n   * Get patients list with optional filters\n   */\n  static async getList(params: PatientSearchParams = {}): Promise<Patient[]> {\n    return apiClient.get<Patient[]>('/api/patients/GetList', { params });\n  }\n\n  /**\n   * Get patient by ID\n   */\n  static async getById(id: number): Promise<Patient> {\n    return apiClient.get<Patient>(`/api/patients/${id}`);\n  }\n\n  /**\n   * Create new patient\n   */\n  static async create(patient: Omit<Patient, 'Id' | 'CreatedAt' | 'UpdatedAt'>): Promise<Patient> {\n    return apiClient.post<Patient>('/api/patients/Insert', patient);\n  }\n\n  /**\n   * Update patient\n   */\n  static async update(patient: Patient): Promise<void> {\n    return apiClient.put<void>('/api/patients/Update', patient);\n  }\n\n  /**\n   * Delete patient\n   */\n  static async delete(id: number): Promise<void> {\n    return apiClient.get<void>(`/api/patients/Delete?id=${id}`);\n  }\n}\n\n/**\n * Treatment API endpoints\n */\nexport class TreatmentApi {\n  /**\n   * Get treatments list with optional filters\n   */\n  static async getList(params: TreatmentSearchParams = {}): Promise<Treatment[]> {\n    return apiClient.get<Treatment[]>('/api/treatment/GetList', { params });\n  }\n\n  /**\n   * Get treatment by ID\n   */\n  static async getById(id: number): Promise<Treatment> {\n    return apiClient.get<Treatment>(`/api/treatment/${id}`);\n  }\n\n  /**\n   * Create new treatment\n   */\n  static async create(treatment: Omit<Treatment, 'Id' | 'CreatedAt' | 'UpdatedAt'>): Promise<void> {\n    return apiClient.post<void>('/api/treatment/Insert', treatment);\n  }\n\n  /**\n   * Update treatment\n   */\n  static async update(treatment: Treatment): Promise<void> {\n    return apiClient.put<void>('/api/treatment/Update', treatment);\n  }\n\n  /**\n   * Delete treatment\n   */\n  static async delete(orderNo: string): Promise<void> {\n    return apiClient.get<void>(`/api/treatment/Delete?OrderNo=${orderNo}`);\n  }\n}\n\n/**\n * Receipt API endpoints\n */\nexport class ReceiptApi {\n  /**\n   * Get receipts list with optional filters\n   */\n  static async getList(params: ReceiptSearchParams = {}): Promise<Receipt[]> {\n    return apiClient.get<Receipt[]>('/api/receipt/GetList', { params });\n  }\n\n  /**\n   * Get receipt by ID\n   */\n  static async getById(id: number): Promise<Receipt> {\n    return apiClient.get<Receipt>(`/api/receipt/${id}`);\n  }\n\n  /**\n   * Create new receipt\n   */\n  static async create(receipt: Omit<Receipt, 'Id' | 'CreatedAt' | 'UpdatedAt'>): Promise<void> {\n    return apiClient.post<void>('/api/receipt/Insert', receipt);\n  }\n\n  /**\n   * Update receipt\n   */\n  static async update(receipt: Receipt): Promise<void> {\n    return apiClient.put<void>('/api/receipt/Update', receipt);\n  }\n\n  /**\n   * Delete receipt\n   */\n  static async delete(id: number): Promise<void> {\n    return apiClient.delete(`/api/receipt/${id}`);\n  }\n}\n\n/**\n * Doctor/User API endpoints\n */\nexport class DoctorApi {\n  /**\n   * Get doctors list\n   */\n  static async getList(params: { UserName?: string; RoleId?: number } = {}): Promise<UserRole[]> {\n    return apiClient.get<UserRole[]>('/api/users/GetList', { params });\n  }\n\n  /**\n   * Get doctor by ID\n   */\n  static async getById(id: number): Promise<UserRole> {\n    return apiClient.get<UserRole>(`/api/users/${id}`);\n  }\n}\n\n/**\n * Authentication API endpoints\n */\nexport class AuthApi {\n  /**\n   * Login user\n   */\n  static async login(credentials: { username: string; password: string }): Promise<LoginResponse> {\n    return apiClient.post<LoginResponse>('/api/auth/login', credentials);\n  }\n\n  /**\n   * Logout user\n   */\n  static async logout(userid : number): Promise<void> {\n    return apiClient.post('/api/auth/logout', userid);\n  }\n\n  /**\n   * Refresh token\n   */\n  static async refreshToken(): Promise<{ token: string }> {\n    return apiClient.post<{ token: string }>('/api/auth/refresh');\n  }\n}\n\n/**\n * Permission API endpoints\n */\nexport class PermissionApi {\n  /**\n   * Get all defined permissions\n   */\n  static async getAllPermissions(): Promise<Permission[]> {\n    return apiClient.get<Permission[]>('/api/permissions/all');\n  }\n\n  /**\n   * Get permissions for a specific role\n   */\n  static async getRolePermissions(roleId: number): Promise<string[]> {\n    return apiClient.get<string[]>(`/api/permissions/role/${roleId}`);\n  }\n\n  /**\n   * Update permissions for a specific role\n   */\n  static async updateRolePermissions(roleId: number, permissionCodes: string[]): Promise<void> {\n    return apiClient.post<void>(`/api/permissions/role/${roleId}`, permissionCodes);\n  }\n\n  /**\n   * Get current user's permissions\n   */\n  static async getCurrentUserPermissions(): Promise<string[]> {\n    return apiClient.get<string[]>('/api/permissions/user/current');\n  }\n}"], "mappings": "AAAA;AACA;AACA;AACA,SAASA,SAAS,QAAQ,OAAO;AAejC;AACA;AACA;AACA,OAAO,MAAMC,SAAS,CAAC;EACrB;AACF;AACA;EACE,aAAaC,QAAQA,CAAA,EAA6B;IAChD,OAAOF,SAAS,CAACG,GAAG,CAAkB,sBAAsB,CAAC;EAC/D;;EAEA;AACF;AACA;EACE,aAAaC,YAAYA,CAAA,EAAiC;IACxD,OAAOJ,SAAS,CAACG,GAAG,CAAsB,yBAAyB,CAAC;EACtE;AACF;;AAEA;AACA;AACA;AACA,OAAO,MAAME,UAAU,CAAC;EACtB;AACF;AACA;EACE,aAAaC,OAAOA,CAACC,MAA2B,GAAG,CAAC,CAAC,EAAsB;IACzE,OAAOP,SAAS,CAACG,GAAG,CAAY,uBAAuB,EAAE;MAAEI;IAAO,CAAC,CAAC;EACtE;;EAEA;AACF;AACA;EACE,aAAaC,OAAOA,CAACC,EAAU,EAAoB;IACjD,OAAOT,SAAS,CAACG,GAAG,CAAU,iBAAiBM,EAAE,EAAE,CAAC;EACtD;;EAEA;AACF;AACA;EACE,aAAaC,MAAMA,CAACC,OAAwD,EAAoB;IAC9F,OAAOX,SAAS,CAACY,IAAI,CAAU,sBAAsB,EAAED,OAAO,CAAC;EACjE;;EAEA;AACF;AACA;EACE,aAAaE,MAAMA,CAACF,OAAgB,EAAiB;IACnD,OAAOX,SAAS,CAACc,GAAG,CAAO,sBAAsB,EAAEH,OAAO,CAAC;EAC7D;;EAEA;AACF;AACA;EACE,aAAaI,MAAMA,CAACN,EAAU,EAAiB;IAC7C,OAAOT,SAAS,CAACG,GAAG,CAAO,2BAA2BM,EAAE,EAAE,CAAC;EAC7D;AACF;;AAEA;AACA;AACA;AACA,OAAO,MAAMO,YAAY,CAAC;EACxB;AACF;AACA;EACE,aAAaV,OAAOA,CAACC,MAA6B,GAAG,CAAC,CAAC,EAAwB;IAC7E,OAAOP,SAAS,CAACG,GAAG,CAAc,wBAAwB,EAAE;MAAEI;IAAO,CAAC,CAAC;EACzE;;EAEA;AACF;AACA;EACE,aAAaC,OAAOA,CAACC,EAAU,EAAsB;IACnD,OAAOT,SAAS,CAACG,GAAG,CAAY,kBAAkBM,EAAE,EAAE,CAAC;EACzD;;EAEA;AACF;AACA;EACE,aAAaC,MAAMA,CAACO,SAA4D,EAAiB;IAC/F,OAAOjB,SAAS,CAACY,IAAI,CAAO,uBAAuB,EAAEK,SAAS,CAAC;EACjE;;EAEA;AACF;AACA;EACE,aAAaJ,MAAMA,CAACI,SAAoB,EAAiB;IACvD,OAAOjB,SAAS,CAACc,GAAG,CAAO,uBAAuB,EAAEG,SAAS,CAAC;EAChE;;EAEA;AACF;AACA;EACE,aAAaF,MAAMA,CAACG,OAAe,EAAiB;IAClD,OAAOlB,SAAS,CAACG,GAAG,CAAO,iCAAiCe,OAAO,EAAE,CAAC;EACxE;AACF;;AAEA;AACA;AACA;AACA,OAAO,MAAMC,UAAU,CAAC;EACtB;AACF;AACA;EACE,aAAab,OAAOA,CAACC,MAA2B,GAAG,CAAC,CAAC,EAAsB;IACzE,OAAOP,SAAS,CAACG,GAAG,CAAY,sBAAsB,EAAE;MAAEI;IAAO,CAAC,CAAC;EACrE;;EAEA;AACF;AACA;EACE,aAAaC,OAAOA,CAACC,EAAU,EAAoB;IACjD,OAAOT,SAAS,CAACG,GAAG,CAAU,gBAAgBM,EAAE,EAAE,CAAC;EACrD;;EAEA;AACF;AACA;EACE,aAAaC,MAAMA,CAACU,OAAwD,EAAiB;IAC3F,OAAOpB,SAAS,CAACY,IAAI,CAAO,qBAAqB,EAAEQ,OAAO,CAAC;EAC7D;;EAEA;AACF;AACA;EACE,aAAaP,MAAMA,CAACO,OAAgB,EAAiB;IACnD,OAAOpB,SAAS,CAACc,GAAG,CAAO,qBAAqB,EAAEM,OAAO,CAAC;EAC5D;;EAEA;AACF;AACA;EACE,aAAaL,MAAMA,CAACN,EAAU,EAAiB;IAC7C,OAAOT,SAAS,CAACe,MAAM,CAAC,gBAAgBN,EAAE,EAAE,CAAC;EAC/C;AACF;;AAEA;AACA;AACA;AACA,OAAO,MAAMY,SAAS,CAAC;EACrB;AACF;AACA;EACE,aAAaf,OAAOA,CAACC,MAA8C,GAAG,CAAC,CAAC,EAAuB;IAC7F,OAAOP,SAAS,CAACG,GAAG,CAAa,oBAAoB,EAAE;MAAEI;IAAO,CAAC,CAAC;EACpE;;EAEA;AACF;AACA;EACE,aAAaC,OAAOA,CAACC,EAAU,EAAqB;IAClD,OAAOT,SAAS,CAACG,GAAG,CAAW,cAAcM,EAAE,EAAE,CAAC;EACpD;AACF;;AAEA;AACA;AACA;AACA,OAAO,MAAMa,OAAO,CAAC;EACnB;AACF;AACA;EACE,aAAaC,KAAKA,CAACC,WAAmD,EAA0B;IAC9F,OAAOxB,SAAS,CAACY,IAAI,CAAgB,iBAAiB,EAAEY,WAAW,CAAC;EACtE;;EAEA;AACF;AACA;EACE,aAAaC,MAAMA,CAACC,MAAe,EAAiB;IAClD,OAAO1B,SAAS,CAACY,IAAI,CAAC,kBAAkB,EAAEc,MAAM,CAAC;EACnD;;EAEA;AACF;AACA;EACE,aAAaC,YAAYA,CAAA,EAA+B;IACtD,OAAO3B,SAAS,CAACY,IAAI,CAAoB,mBAAmB,CAAC;EAC/D;AACF;;AAEA;AACA;AACA;AACA,OAAO,MAAMgB,aAAa,CAAC;EACzB;AACF;AACA;EACE,aAAaC,iBAAiBA,CAAA,EAA0B;IACtD,OAAO7B,SAAS,CAACG,GAAG,CAAe,sBAAsB,CAAC;EAC5D;;EAEA;AACF;AACA;EACE,aAAa2B,kBAAkBA,CAACC,MAAc,EAAqB;IACjE,OAAO/B,SAAS,CAACG,GAAG,CAAW,yBAAyB4B,MAAM,EAAE,CAAC;EACnE;;EAEA;AACF;AACA;EACE,aAAaC,qBAAqBA,CAACD,MAAc,EAAEE,eAAyB,EAAiB;IAC3F,OAAOjC,SAAS,CAACY,IAAI,CAAO,yBAAyBmB,MAAM,EAAE,EAAEE,eAAe,CAAC;EACjF;;EAEA;AACF;AACA;EACE,aAAaC,yBAAyBA,CAAA,EAAsB;IAC1D,OAAOlC,SAAS,CAACG,GAAG,CAAW,+BAA+B,CAAC;EACjE;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
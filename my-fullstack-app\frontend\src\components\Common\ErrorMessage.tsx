import { Button } from 'primereact/button';
import { Message } from 'primereact/message';
import React from 'react';

interface ErrorMessageProps {
  message: string | undefined;
  severity?: 'error' | 'warn' | 'info';
  onRetry?: () => void ;
  retryLabel?: string | undefined;
  className?: string | undefined;
  showIcon?: boolean | undefined;
}

const ErrorMessage: React.FC<ErrorMessageProps> = ({
  message,
  severity = 'error',
  onRetry,
  retryLabel = 'Retry',
  className = '',
  showIcon = true,
}) => {
  return (
    <div className={`flex flex-column align-items-center gap-3 p-4 ${className}`}>
      <Message
        severity={severity}
        text={message}
        className="w-full"
        icon={showIcon ? undefined : ''}
      />
      {onRetry && (
        <Button
          label={retryLabel}
          icon="pi pi-refresh"
          onClick={onRetry}
          className="p-button-outlined"
          size="small"
        />
      )}
    </div>
  );
};

export default ErrorMessage;

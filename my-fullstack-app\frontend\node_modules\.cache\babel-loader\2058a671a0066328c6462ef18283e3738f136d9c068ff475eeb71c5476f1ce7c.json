{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\demo-git\\\\demo-react\\\\my-fullstack-app\\\\frontend\\\\src\\\\components\\\\Layout\\\\Layout.tsx\",\n  _s = $RefreshSig$();\nimport React, { useRef } from 'react';\nimport { useNavigate } from \"react-router-dom\";\nimport { Button } from \"primereact/button\";\nimport { Menubar } from \"primereact/menubar\";\nimport { Toast } from \"primereact/toast\";\nimport { log } from '../../utils/logger';\nimport { useAuth } from '../../contexts/AuthContext';\nimport useMenu from '../../hooks/useMenu';\nimport { usePermissions } from '../../hooks/usePermissions'; // 導入 usePermissions\nimport BreadcrumbNav from '../Common/BreadcrumbNav';\nimport LoadingSpinner from '../Common/LoadingSpinner';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\n// 輔助函數：將菜單路徑映射到權限代碼\nconst getPermissionCodeForPath = path => {\n  switch (path) {\n    case '/doctors':\n      return 'doctors.read';\n    case '/doctor-detail':\n      return 'doctors.detail.read';\n    case '/treatments':\n      return 'treatments.read';\n    case '/treatment-detail':\n      return 'treatments.read';\n    case '/patients':\n      return 'patients.read';\n    case '/patient-detail':\n      return 'patients.read';\n    case '/schedules':\n      return 'schedules.read';\n    case '/receipts':\n      return 'receipts.read';\n    case '/receipt-detail':\n      return 'receipts.read';\n    case '/users':\n      return 'userroles.read';\n    case '/backup-management':\n      return 'backup.read';\n    case '/report-management':\n      return 'reports.read';\n    case '/image-management':\n      return 'images.read';\n    case '/login-logs':\n      return 'loginlogs.read';\n    case '/ip-blocks':\n      return 'ipblocks.read';\n    case '/debug':\n      return 'debug.read';\n    case '/update-password':\n      return 'users.updatepassword';\n    case '/permission-management':\n      return 'permissions.read';\n    default:\n      return null;\n  }\n};\nconst Layout = ({\n  children\n}) => {\n  _s();\n  const navigate = useNavigate();\n  const {\n    isAuthenticated,\n    logout: authLogout,\n    user\n  } = useAuth();\n  const {\n    menu,\n    loading: menuLoading\n  } = useMenu(isAuthenticated);\n  const {\n    hasPermission,\n    loading: permissionsLoading\n  } = usePermissions(); // 獲取 hasPermission 和 loading 狀態\n  const toast = useRef(null);\n  const handleLogout = () => {\n    var _toast$current;\n    log.auth('Layout: 執行登出');\n    authLogout();\n    (_toast$current = toast.current) === null || _toast$current === void 0 ? void 0 : _toast$current.show({\n      severity: 'success',\n      summary: '登出成功',\n      detail: '您已成功登出系統',\n      life: 2000\n    });\n    setTimeout(() => {\n      log.route('Layout: 導航到登入頁面');\n      navigate(\"/login\", {\n        replace: true\n      });\n    }, 1000);\n  };\n  if (menuLoading || permissionsLoading) {\n    return /*#__PURE__*/_jsxDEV(LoadingSpinner, {\n      message: \"\\u8F09\\u5165\\u9078\\u55AE\\u4E2D...\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 89,\n      columnNumber: 12\n    }, this);\n  }\n  const HeaderItems = [{\n    label: \"首頁\",\n    icon: \"pi pi-home\",\n    command: () => {\n      navigate(\"/\");\n    }\n  }];\n  menu.forEach(group => {\n    const templist = group.menus.filter(data => data.isEnabled) // 確保菜單啟用\n    .filter(data => !data.path.includes('detail')) // 過濾掉詳情頁面\n    .filter(data => {\n      const permissionCode = getPermissionCodeForPath(data.path);\n      return permissionCode ? hasPermission(permissionCode) : true; // 如果有對應權限代碼，則檢查權限\n    }).map(data => ({\n      key: data.itemId,\n      label: data.name,\n      command: () => {\n        navigate(data.path);\n      }\n    }));\n    if (templist.length > 0) {\n      HeaderItems.push({\n        label: group.groupName,\n        icon: group.groupIcon,\n        items: templist\n      });\n    }\n  });\n  const getUserDisplayName = () => {\n    if (user !== null && user !== void 0 && user.username) {\n      return user.username + \" 治療師\";\n    }\n    const storedUsername = localStorage.getItem('username');\n    return storedUsername || '用戶';\n  };\n  const HeaderEndItems = /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"flex align-items-center gap-2\",\n    children: [/*#__PURE__*/_jsxDEV(\"label\", {\n      className: \"font-bold block\",\n      children: [getUserDisplayName(), \"\\u60A8\\u597D\"]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 137,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Button, {\n      icon: \"pi pi-sign-out\",\n      label: \"\\u767B\\u51FA\",\n      className: \"p-button-text\",\n      onClick: handleLogout\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 138,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 136,\n    columnNumber: 5\n  }, this);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"layout-wrapper min-h-screen flex flex-column\",\n    children: [/*#__PURE__*/_jsxDEV(Toast, {\n      ref: toast\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 149,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Menubar, {\n      model: HeaderItems,\n      end: HeaderEndItems,\n      className: \"border-none\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 150,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"breadcrumb-container p-3 bg-gray-50 surface-border\",\n      children: /*#__PURE__*/_jsxDEV(BreadcrumbNav, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 152,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 151,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"main-content flex-1 p-3\",\n      children: children\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 154,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 148,\n    columnNumber: 5\n  }, this);\n};\n_s(Layout, \"SHZg29EMWHWpzSQiynPDQhtfpxk=\", false, function () {\n  return [useNavigate, useAuth, useMenu, usePermissions];\n});\n_c = Layout;\nexport default Layout;\nvar _c;\n$RefreshReg$(_c, \"Layout\");", "map": {"version": 3, "names": ["React", "useRef", "useNavigate", "<PERSON><PERSON>", "Men<PERSON><PERSON>", "Toast", "log", "useAuth", "useMenu", "usePermissions", "BreadcrumbNav", "LoadingSpinner", "jsxDEV", "_jsxDEV", "getPermissionCodeForPath", "path", "Layout", "children", "_s", "navigate", "isAuthenticated", "logout", "authLogout", "user", "menu", "loading", "menuLoading", "hasPermission", "permissionsLoading", "toast", "handleLogout", "_toast$current", "auth", "current", "show", "severity", "summary", "detail", "life", "setTimeout", "route", "replace", "message", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "HeaderItems", "label", "icon", "command", "for<PERSON>ach", "group", "templist", "menus", "filter", "data", "isEnabled", "includes", "permissionCode", "map", "key", "itemId", "name", "length", "push", "groupName", "groupIcon", "items", "getUserDisplayName", "username", "storedUsername", "localStorage", "getItem", "HeaderEndItems", "className", "onClick", "ref", "model", "end", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/demo-git/demo-react/my-fullstack-app/frontend/src/components/Layout/Layout.tsx"], "sourcesContent": ["import React, { ReactNode, useRef } from 'react';\nimport { useNavigate } from \"react-router-dom\";\n\nimport { But<PERSON> } from \"primereact/button\";\nimport { <PERSON><PERSON><PERSON> } from \"primereact/menubar\";\nimport { MenuItem } from \"primereact/menuitem\";\nimport { Toast } from \"primereact/toast\";\nimport { log } from '../../utils/logger';\n\nimport { useAuth } from '../../contexts/AuthContext';\nimport useMenu from '../../hooks/useMenu';\nimport { usePermissions } from '../../hooks/usePermissions'; // 導入 usePermissions\nimport BreadcrumbNav from '../Common/BreadcrumbNav';\nimport LoadingSpinner from '../Common/LoadingSpinner';\n\ninterface LayoutProps {\n  children: ReactNode;\n}\n\n// 輔助函數：將菜單路徑映射到權限代碼\nconst getPermissionCodeForPath = (path: string): string | null => {\n  switch (path) {\n    case '/doctors':\n      return 'doctors.read';\n    case '/doctor-detail':\n      return 'doctors.detail.read';\n    case '/treatments':\n      return 'treatments.read';\n    case '/treatment-detail':\n      return 'treatments.read';\n    case '/patients':\n      return 'patients.read';\n    case '/patient-detail':\n      return 'patients.read';\n    case '/schedules':\n      return 'schedules.read';\n    case '/receipts':\n      return 'receipts.read';\n    case '/receipt-detail':\n      return 'receipts.read';\n    case '/users':\n      return 'userroles.read';\n    case '/backup-management':\n      return 'backup.read';\n    case '/report-management':\n      return 'reports.read';\n    case '/image-management':\n      return 'images.read';\n    case '/login-logs':\n      return 'loginlogs.read';\n    case '/ip-blocks':\n      return 'ipblocks.read';\n    case '/debug':\n      return 'debug.read';\n    case '/update-password':\n      return 'users.updatepassword';\n    case '/permission-management':\n      return 'permissions.read';\n    default:\n      return null;\n  }\n};\n\nconst Layout: React.FC<LayoutProps> = ({ children }) => {\n  const navigate = useNavigate();\n  const { isAuthenticated, logout: authLogout, user } = useAuth();\n  const { menu, loading: menuLoading } = useMenu(isAuthenticated);\n  const { hasPermission, loading: permissionsLoading } = usePermissions(); // 獲取 hasPermission 和 loading 狀態\n  const toast = useRef<Toast>(null);\n\n  const handleLogout = () => {\n    log.auth('Layout: 執行登出');\n    authLogout();\n\n    toast.current?.show({\n      severity: 'success',\n      summary: '登出成功',\n      detail: '您已成功登出系統',\n      life: 2000\n    });\n\n    setTimeout(() => {\n      log.route('Layout: 導航到登入頁面');\n      navigate(\"/login\", { replace: true });\n    }, 1000);\n  };\n\n  if (menuLoading || permissionsLoading) {\n    return <LoadingSpinner message=\"載入選單中...\" />;\n  }\n\n  const HeaderItems: MenuItem[] = [\n    {\n      label: \"首頁\",\n      icon: \"pi pi-home\",\n      command: () => {\n        navigate(\"/\");\n      }\n    }\n  ];\n\n  menu.forEach((group) => {\n    const templist = group.menus\n      .filter((data) => data.isEnabled) // 確保菜單啟用\n      .filter((data) => !data.path.includes('detail')) // 過濾掉詳情頁面\n      .filter((data) => {\n        const permissionCode = getPermissionCodeForPath(data.path);\n        return permissionCode ? hasPermission(permissionCode) : true; // 如果有對應權限代碼，則檢查權限\n      })\n      .map((data) => ({\n        key: data.itemId,\n        label: data.name,\n        command: () => {\n          navigate(data.path);\n        }\n      }));\n\n    if (templist.length > 0) {\n      HeaderItems.push({\n        label: group.groupName,\n        icon: group.groupIcon,\n        items: templist,\n      });\n    }\n  });\n\n  const getUserDisplayName = () => {\n    if (user?.username) {\n      return user.username + \" 治療師\";\n    }\n    const storedUsername = localStorage.getItem('username');\n    return storedUsername || '用戶';\n  };\n\n  const HeaderEndItems = (\n    <div className=\"flex align-items-center gap-2\">\n      <label className=\"font-bold block\">{getUserDisplayName()}您好</label>\n      <Button\n        icon=\"pi pi-sign-out\"\n        label=\"登出\"\n        className=\"p-button-text\"\n        onClick={handleLogout}\n      />\n    </div>\n  );\n\n  return (\n    <div className=\"layout-wrapper min-h-screen flex flex-column\">\n      <Toast ref={toast} />\n      <Menubar model={HeaderItems} end={HeaderEndItems} className=\"border-none\" />\n      <div className=\"breadcrumb-container p-3 bg-gray-50 surface-border\">\n        <BreadcrumbNav />\n      </div>\n      <div className=\"main-content flex-1 p-3\">\n        {children}\n      </div>\n    </div>\n  );\n};\n\nexport default Layout;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAeC,MAAM,QAAQ,OAAO;AAChD,SAASC,WAAW,QAAQ,kBAAkB;AAE9C,SAASC,MAAM,QAAQ,mBAAmB;AAC1C,SAASC,OAAO,QAAQ,oBAAoB;AAE5C,SAASC,KAAK,QAAQ,kBAAkB;AACxC,SAASC,GAAG,QAAQ,oBAAoB;AAExC,SAASC,OAAO,QAAQ,4BAA4B;AACpD,OAAOC,OAAO,MAAM,qBAAqB;AACzC,SAASC,cAAc,QAAQ,4BAA4B,CAAC,CAAC;AAC7D,OAAOC,aAAa,MAAM,yBAAyB;AACnD,OAAOC,cAAc,MAAM,0BAA0B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAMtD;AACA,MAAMC,wBAAwB,GAAIC,IAAY,IAAoB;EAChE,QAAQA,IAAI;IACV,KAAK,UAAU;MACb,OAAO,cAAc;IACvB,KAAK,gBAAgB;MACnB,OAAO,qBAAqB;IAC9B,KAAK,aAAa;MAChB,OAAO,iBAAiB;IAC1B,KAAK,mBAAmB;MACtB,OAAO,iBAAiB;IAC1B,KAAK,WAAW;MACd,OAAO,eAAe;IACxB,KAAK,iBAAiB;MACpB,OAAO,eAAe;IACxB,KAAK,YAAY;MACf,OAAO,gBAAgB;IACzB,KAAK,WAAW;MACd,OAAO,eAAe;IACxB,KAAK,iBAAiB;MACpB,OAAO,eAAe;IACxB,KAAK,QAAQ;MACX,OAAO,gBAAgB;IACzB,KAAK,oBAAoB;MACvB,OAAO,aAAa;IACtB,KAAK,oBAAoB;MACvB,OAAO,cAAc;IACvB,KAAK,mBAAmB;MACtB,OAAO,aAAa;IACtB,KAAK,aAAa;MAChB,OAAO,gBAAgB;IACzB,KAAK,YAAY;MACf,OAAO,eAAe;IACxB,KAAK,QAAQ;MACX,OAAO,YAAY;IACrB,KAAK,kBAAkB;MACrB,OAAO,sBAAsB;IAC/B,KAAK,wBAAwB;MAC3B,OAAO,kBAAkB;IAC3B;MACE,OAAO,IAAI;EACf;AACF,CAAC;AAED,MAAMC,MAA6B,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EAAAC,EAAA;EACtD,MAAMC,QAAQ,GAAGjB,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEkB,eAAe;IAAEC,MAAM,EAAEC,UAAU;IAAEC;EAAK,CAAC,GAAGhB,OAAO,CAAC,CAAC;EAC/D,MAAM;IAAEiB,IAAI;IAAEC,OAAO,EAAEC;EAAY,CAAC,GAAGlB,OAAO,CAACY,eAAe,CAAC;EAC/D,MAAM;IAAEO,aAAa;IAAEF,OAAO,EAAEG;EAAmB,CAAC,GAAGnB,cAAc,CAAC,CAAC,CAAC,CAAC;EACzE,MAAMoB,KAAK,GAAG5B,MAAM,CAAQ,IAAI,CAAC;EAEjC,MAAM6B,YAAY,GAAGA,CAAA,KAAM;IAAA,IAAAC,cAAA;IACzBzB,GAAG,CAAC0B,IAAI,CAAC,cAAc,CAAC;IACxBV,UAAU,CAAC,CAAC;IAEZ,CAAAS,cAAA,GAAAF,KAAK,CAACI,OAAO,cAAAF,cAAA,uBAAbA,cAAA,CAAeG,IAAI,CAAC;MAClBC,QAAQ,EAAE,SAAS;MACnBC,OAAO,EAAE,MAAM;MACfC,MAAM,EAAE,UAAU;MAClBC,IAAI,EAAE;IACR,CAAC,CAAC;IAEFC,UAAU,CAAC,MAAM;MACfjC,GAAG,CAACkC,KAAK,CAAC,iBAAiB,CAAC;MAC5BrB,QAAQ,CAAC,QAAQ,EAAE;QAAEsB,OAAO,EAAE;MAAK,CAAC,CAAC;IACvC,CAAC,EAAE,IAAI,CAAC;EACV,CAAC;EAED,IAAIf,WAAW,IAAIE,kBAAkB,EAAE;IACrC,oBAAOf,OAAA,CAACF,cAAc;MAAC+B,OAAO,EAAC;IAAU;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAC9C;EAEA,MAAMC,WAAuB,GAAG,CAC9B;IACEC,KAAK,EAAE,IAAI;IACXC,IAAI,EAAE,YAAY;IAClBC,OAAO,EAAEA,CAAA,KAAM;MACb/B,QAAQ,CAAC,GAAG,CAAC;IACf;EACF,CAAC,CACF;EAEDK,IAAI,CAAC2B,OAAO,CAAEC,KAAK,IAAK;IACtB,MAAMC,QAAQ,GAAGD,KAAK,CAACE,KAAK,CACzBC,MAAM,CAAEC,IAAI,IAAKA,IAAI,CAACC,SAAS,CAAC,CAAC;IAAA,CACjCF,MAAM,CAAEC,IAAI,IAAK,CAACA,IAAI,CAACzC,IAAI,CAAC2C,QAAQ,CAAC,QAAQ,CAAC,CAAC,CAAC;IAAA,CAChDH,MAAM,CAAEC,IAAI,IAAK;MAChB,MAAMG,cAAc,GAAG7C,wBAAwB,CAAC0C,IAAI,CAACzC,IAAI,CAAC;MAC1D,OAAO4C,cAAc,GAAGhC,aAAa,CAACgC,cAAc,CAAC,GAAG,IAAI,CAAC,CAAC;IAChE,CAAC,CAAC,CACDC,GAAG,CAAEJ,IAAI,KAAM;MACdK,GAAG,EAAEL,IAAI,CAACM,MAAM;MAChBd,KAAK,EAAEQ,IAAI,CAACO,IAAI;MAChBb,OAAO,EAAEA,CAAA,KAAM;QACb/B,QAAQ,CAACqC,IAAI,CAACzC,IAAI,CAAC;MACrB;IACF,CAAC,CAAC,CAAC;IAEL,IAAIsC,QAAQ,CAACW,MAAM,GAAG,CAAC,EAAE;MACvBjB,WAAW,CAACkB,IAAI,CAAC;QACfjB,KAAK,EAAEI,KAAK,CAACc,SAAS;QACtBjB,IAAI,EAAEG,KAAK,CAACe,SAAS;QACrBC,KAAK,EAAEf;MACT,CAAC,CAAC;IACJ;EACF,CAAC,CAAC;EAEF,MAAMgB,kBAAkB,GAAGA,CAAA,KAAM;IAC/B,IAAI9C,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAE+C,QAAQ,EAAE;MAClB,OAAO/C,IAAI,CAAC+C,QAAQ,GAAG,MAAM;IAC/B;IACA,MAAMC,cAAc,GAAGC,YAAY,CAACC,OAAO,CAAC,UAAU,CAAC;IACvD,OAAOF,cAAc,IAAI,IAAI;EAC/B,CAAC;EAED,MAAMG,cAAc,gBAClB7D,OAAA;IAAK8D,SAAS,EAAC,+BAA+B;IAAA1D,QAAA,gBAC5CJ,OAAA;MAAO8D,SAAS,EAAC,iBAAiB;MAAA1D,QAAA,GAAEoD,kBAAkB,CAAC,CAAC,EAAC,cAAE;IAAA;MAAA1B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAO,CAAC,eACnEjC,OAAA,CAACV,MAAM;MACL8C,IAAI,EAAC,gBAAgB;MACrBD,KAAK,EAAC,cAAI;MACV2B,SAAS,EAAC,eAAe;MACzBC,OAAO,EAAE9C;IAAa;MAAAa,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACvB,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CACN;EAED,oBACEjC,OAAA;IAAK8D,SAAS,EAAC,8CAA8C;IAAA1D,QAAA,gBAC3DJ,OAAA,CAACR,KAAK;MAACwE,GAAG,EAAEhD;IAAM;MAAAc,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACrBjC,OAAA,CAACT,OAAO;MAAC0E,KAAK,EAAE/B,WAAY;MAACgC,GAAG,EAAEL,cAAe;MAACC,SAAS,EAAC;IAAa;MAAAhC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAC5EjC,OAAA;MAAK8D,SAAS,EAAC,oDAAoD;MAAA1D,QAAA,eACjEJ,OAAA,CAACH,aAAa;QAAAiC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACd,CAAC,eACNjC,OAAA;MAAK8D,SAAS,EAAC,yBAAyB;MAAA1D,QAAA,EACrCA;IAAQ;MAAA0B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC5B,EAAA,CA/FIF,MAA6B;EAAA,QAChBd,WAAW,EAC0BK,OAAO,EACtBC,OAAO,EACSC,cAAc;AAAA;AAAAuE,EAAA,GAJjEhE,MAA6B;AAiGnC,eAAeA,MAAM;AAAC,IAAAgE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
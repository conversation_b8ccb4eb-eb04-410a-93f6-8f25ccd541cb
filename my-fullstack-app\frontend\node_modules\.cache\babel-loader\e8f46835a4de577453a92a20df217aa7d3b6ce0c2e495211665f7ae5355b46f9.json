{"ast": null, "code": "import React,{useState,useRef,useEffect}from'react';import{Button}from'primereact/button';import{Column}from'primereact/column';import{DataTable}from'primereact/datatable';import{Dialog}from'primereact/dialog';import{Toast}from'primereact/toast';import{ConfirmDialog,confirmDialog}from'primereact/confirmdialog';import{Card}from'primereact/card';import{ProgressSpinner}from'primereact/progressspinner';import{Tag}from'primereact/tag';import{Image}from'primereact/image';import{InputText}from'primereact/inputtext';import{Calendar}from'primereact/calendar';import{formatUtcToTaipei}from\"../../utils/dateUtils\";import api from'../../services/api';import{log}from'../../utils/logger';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const ImageManagementPage=()=>{const toast=useRef(null);const[images,setImages]=useState([]);const[loading,setLoading]=useState(true);const[refreshing,setRefreshing]=useState(false);const[selectedImage,setSelectedImage]=useState(null);const[showPreviewDialog,setShowPreviewDialog]=useState(false);const[fileNameFilter,setFileNameFilter]=useState('');const[startDateFilter,setStartDateFilter]=useState(null);const[endDateFilter,setEndDateFilter]=useState(null);// 載入圖片列表\nconst loadImages=async()=>{try{setRefreshing(true);log.api('載入圖片列表');const params={fileName:fileNameFilter,startDate:startDateFilter?startDateFilter.toISOString():undefined,endDate:endDateFilter?endDateFilter.toISOString():undefined};const response=await api.get('/api/file/GetImageFiles',{params});setImages(response.data);log.api('圖片列表載入成功',{count:response.data.length});}catch(error){var _toast$current;log.error('載入圖片列表失敗',error);(_toast$current=toast.current)===null||_toast$current===void 0?void 0:_toast$current.show({severity:'error',summary:'載入失敗',detail:'無法載入圖片列表',life:5000});}finally{setLoading(false);setRefreshing(false);}};const handleSearch=()=>{loadImages();};const handleReset=()=>{setFileNameFilter('');setStartDateFilter(null);setEndDateFilter(null);loadImages();};// 刪除圖片\nconst deleteImage=async image=>{try{var _toast$current2;log.api('刪除圖片',{fileName:image.fileName});await api.delete(\"/api/file/DeleteImageFile\",{params:{fileName:image.fileName}});(_toast$current2=toast.current)===null||_toast$current2===void 0?void 0:_toast$current2.show({severity:'success',summary:'刪除成功',detail:\"\\u5716\\u7247 \".concat(image.fileName,\" \\u5DF2\\u522A\\u9664\"),life:3000});// 重新載入列表\nloadImages();}catch(error){var _toast$current3,_error$response,_error$response$data;log.error('刪除圖片失敗',error);(_toast$current3=toast.current)===null||_toast$current3===void 0?void 0:_toast$current3.show({severity:'error',summary:'刪除失敗',detail:((_error$response=error.response)===null||_error$response===void 0?void 0:(_error$response$data=_error$response.data)===null||_error$response$data===void 0?void 0:_error$response$data.message)||'刪除圖片失敗',life:5000});}};// 下載圖片\nconst downloadImage=async image=>{try{var _toast$current4;log.api('下載圖片',{fileName:image.fileName});const response=await api.get(\"/api/file/DownloadImageFile\",{params:{fileName:image.fileName},responseType:'blob'});// 創建下載連結\nconst blob=new Blob([response.data]);const url=window.URL.createObjectURL(blob);const link=document.createElement('a');link.href=url;link.download=image.fileName;document.body.appendChild(link);link.click();document.body.removeChild(link);window.URL.revokeObjectURL(url);(_toast$current4=toast.current)===null||_toast$current4===void 0?void 0:_toast$current4.show({severity:'success',summary:'下載成功',detail:\"\\u5716\\u7247 \".concat(image.fileName,\" \\u4E0B\\u8F09\\u5B8C\\u6210\"),life:3000});}catch(error){var _toast$current5;log.error('下載圖片失敗',error);(_toast$current5=toast.current)===null||_toast$current5===void 0?void 0:_toast$current5.show({severity:'error',summary:'下載失敗',detail:'下載圖片失敗',life:5000});}};// 預覽圖片\nconst previewImage=image=>{setSelectedImage(image);setShowPreviewDialog(true);};// 確認刪除\nconst confirmDelete=image=>{confirmDialog({message:\"\\u78BA\\u5B9A\\u8981\\u522A\\u9664\\u5716\\u7247 \\\"\".concat(image.fileName,\"\\\" \\u55CE\\uFF1F\\u6B64\\u64CD\\u4F5C\\u7121\\u6CD5\\u5FA9\\u539F\\u3002\"),header:'確認刪除',icon:'pi pi-exclamation-triangle',acceptLabel:'確定',rejectLabel:'取消',accept:()=>deleteImage(image)});};// 格式化檔案大小\nconst formatFileSize=bytes=>{if(bytes===0)return'0 Bytes';const k=1024;const sizes=['Bytes','KB','MB','GB'];const i=Math.floor(Math.log(bytes)/Math.log(k));return parseFloat((bytes/Math.pow(k,i)).toFixed(2))+' '+sizes[i];};// 格式化日期\nconst formatDate=dateString=>{if(!dateString)return'';try{return formatUtcToTaipei(dateString,'yyyy/MM/dd HH:mm:ss');}catch(error){console.error('Error formatting date:',error);return dateString;// or return a default/error indicator\n}};// 縮圖模板\nconst thumbnailBodyTemplate=rowData=>{return/*#__PURE__*/_jsx(Image,{src:rowData.imageUrl,alt:rowData.fileName,width:\"60\",height:\"60\",className:\"border-round\",preview:false,onError:e=>{// 如果圖片載入失敗，顯示預設圖示\ne.target.src='data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjAiIGhlaWdodD0iNjAiIHZpZXdCb3g9IjAgMCA2MCA2MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjYwIiBoZWlnaHQ9IjYwIiBmaWxsPSIjRjVGNUY1Ii8+CjxwYXRoIGQ9Ik0zMCAyMEM0MS4wNDU3IDIwIDUwIDI4Ljk1NDMgNTAgNDBDNTAgNTEuMDQ1NyA0MS4wNDU3IDYwIDMwIDYwQzE4Ljk1NDMgNjAgMTAgNTEuMDQ1NyAxMCA0MEMxMCAyOC45NTQzIDE4Ljk1NDMgMjAgMzAgMjBaIiBmaWxsPSIjRTBFMEUwIi8+CjxwYXRoIGQ9Ik0zMCAyNUM0MS4wNDU3IDI1IDUwIDMzLjk1NDMgNTAgNDVDNTAgNTYuMDQ1NyA0MS4wNDU3IDY1IDMwIDY1QzE4Ljk1NDMgNjUgMTAgNTYuMDQ1NyAxMCA0NUMxMCAzMy45NTQzIDE4Ljk1NDMgMjUgMzAgMjVaIiBmaWxsPSIjQ0NDQ0NDIi8+CjwvZz4KPC9zdmc+';}});};// 檔案大小模板\nconst fileSizeBodyTemplate=rowData=>{return formatFileSize(rowData.fileSize);};// 建立日期模板\nconst createdDateBodyTemplate=rowData=>{return formatDate(rowData.createdDate);};// 修改日期模板\nconst modifiedDateBodyTemplate=rowData=>{return formatDate(rowData.modifiedDate);};// 檔案類型模板\nconst fileTypeBodyTemplate=rowData=>{var _rowData$fileName$spl;const extension=(_rowData$fileName$spl=rowData.fileName.split('.').pop())===null||_rowData$fileName$spl===void 0?void 0:_rowData$fileName$spl.toUpperCase();let severity='info';switch(extension){case'JPG':case'JPEG':severity='success';break;case'PNG':severity='info';break;case'GIF':severity='warning';break;case'WEBP':severity='danger';break;default:severity='warning';}return/*#__PURE__*/_jsx(Tag,{value:extension,severity:severity});};// 操作按鈕模板\nconst actionBodyTemplate=rowData=>{return/*#__PURE__*/_jsxs(\"div\",{className:\"flex gap-2\",children:[/*#__PURE__*/_jsx(Button,{icon:\"pi pi-eye\",className:\"p-button-info p-button-sm\",onClick:()=>previewImage(rowData),tooltip:\"\\u9810\\u89BD\",tooltipOptions:{position:'top'}}),/*#__PURE__*/_jsx(Button,{icon:\"pi pi-download\",className:\"p-button-success p-button-sm\",onClick:()=>downloadImage(rowData),tooltip:\"\\u4E0B\\u8F09\",tooltipOptions:{position:'top'}}),/*#__PURE__*/_jsx(Button,{icon:\"pi pi-trash\",className:\"p-button-danger p-button-sm\",onClick:()=>confirmDelete(rowData),tooltip:\"\\u522A\\u9664\",tooltipOptions:{position:'top'}})]});};// 分頁器左側\nconst paginatorLeft=/*#__PURE__*/_jsx(Button,{type:\"button\",icon:\"pi pi-refresh\",text:true,onClick:loadImages,disabled:refreshing});const paginatorRight=/*#__PURE__*/_jsx(\"div\",{});useEffect(()=>{loadImages();},[]);if(loading){return/*#__PURE__*/_jsx(\"div\",{className:\"flex justify-content-center align-items-center\",style:{height:'400px'},children:/*#__PURE__*/_jsx(ProgressSpinner,{})});}return/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(Toast,{ref:toast}),/*#__PURE__*/_jsx(ConfirmDialog,{}),/*#__PURE__*/_jsx(Card,{title:\"\\u5716\\u7247\\u7BA1\\u7406\",className:\"mb-4\",children:/*#__PURE__*/_jsx(\"p\",{className:\"text-600 line-height-3 m-0\",children:\"\\u7BA1\\u7406\\u7CFB\\u7D71\\u4E0A\\u50B3\\u7684\\u5716\\u7247\\u6A94\\u6848\\uFF0C\\u5305\\u62EC\\u6CBB\\u7642\\u8A18\\u9304\\u5716\\u7247\\u3001\\u75C5\\u60A3\\u8CC7\\u6599\\u5716\\u7247\\u7B49\\u3002\\u60A8\\u53EF\\u4EE5\\u9810\\u89BD\\u3001\\u4E0B\\u8F09\\u6216\\u522A\\u9664\\u4E0D\\u9700\\u8981\\u7684\\u5716\\u7247\\u6A94\\u6848\\u3002\"})}),/*#__PURE__*/_jsx(Card,{className:\"mb-4\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"grid\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"col-12 md:col-3\",children:/*#__PURE__*/_jsx(InputText,{id:\"fileNameFilter\",value:fileNameFilter,onChange:e=>setFileNameFilter(e.target.value),placeholder:\"\\u4F9D\\u6A94\\u540D\\u641C\\u5C0B\",className:\"w-full\"})}),/*#__PURE__*/_jsx(\"div\",{className:\"col-6 md:col-3\",children:/*#__PURE__*/_jsx(Calendar,{id:\"startDateFilter\",value:startDateFilter,onChange:e=>setStartDateFilter(e.value),placeholder:\"\\u9078\\u64C7\\u7D50\\u675F\\u65E5\\u671F\",className:\"w-full\",showIcon:true,dateFormat:\"yy/mm/dd\"})}),/*#__PURE__*/_jsx(\"div\",{className:\"col-6 md:col-3\",children:/*#__PURE__*/_jsx(Calendar,{id:\"endDateFilter\",value:endDateFilter,onChange:e=>setEndDateFilter(e.value),placeholder:\"\\u9078\\u64C7\\u7D50\\u675F\\u65E5\\u671F\",className:\"w-full\",showIcon:true,dateFormat:\"yy/mm/dd\"})}),/*#__PURE__*/_jsx(\"div\",{className:\"col-12 md:col-4\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex gap-2\",children:[/*#__PURE__*/_jsx(Button,{label:\"\\u641C\\u5C0B\",icon:\"pi pi-search\",onClick:handleSearch}),/*#__PURE__*/_jsx(Button,{label:\"\\u91CD\\u8A2D\",icon:\"pi pi-undo\",onClick:handleReset,className:\"p-button-secondary\"})]})})]})}),/*#__PURE__*/_jsx(Card,{children:/*#__PURE__*/_jsxs(DataTable,{value:images,paginator:true,rows:20,rowsPerPageOptions:[10,20,50],emptyMessage:\"\\u6C92\\u6709\\u627E\\u5230\\u5716\\u7247\\u6A94\\u6848\",tableStyle:{minWidth:'50rem'},paginatorLeft:paginatorLeft,paginatorRight:paginatorRight,loading:refreshing,children:[/*#__PURE__*/_jsx(Column,{header:\"\\u7E2E\\u5716\",body:thumbnailBodyTemplate,style:{width:'10%'}}),/*#__PURE__*/_jsx(Column,{field:\"fileName\",header:\"\\u6A94\\u6848\\u540D\\u7A31\",sortable:true,style:{width:'25%'}}),/*#__PURE__*/_jsx(Column,{field:\"fileType\",header:\"\\u985E\\u578B\",body:fileTypeBodyTemplate,style:{width:'10%'}}),/*#__PURE__*/_jsx(Column,{field:\"fileSize\",header:\"\\u6A94\\u6848\\u5927\\u5C0F\",body:fileSizeBodyTemplate,sortable:true,style:{width:'15%'}}),/*#__PURE__*/_jsx(Column,{field:\"createdDate\",header:\"\\u5EFA\\u7ACB\\u65E5\\u671F\",body:createdDateBodyTemplate,sortable:true,style:{width:'20%'}}),/*#__PURE__*/_jsx(Column,{field:\"modifiedDate\",header:\"\\u4FEE\\u6539\\u65E5\\u671F\",body:modifiedDateBodyTemplate,sortable:true,style:{width:'20%'}}),/*#__PURE__*/_jsx(Column,{header:\"\\u64CD\\u4F5C\",body:actionBodyTemplate,style:{width:'15%'}})]})}),/*#__PURE__*/_jsx(Dialog,{header:\"\\u9810\\u89BD\\u5716\\u7247 - \".concat(selectedImage===null||selectedImage===void 0?void 0:selectedImage.fileName),visible:showPreviewDialog,style:{width:'80vw',maxWidth:'800px'},onHide:()=>setShowPreviewDialog(false),modal:true,children:selectedImage&&/*#__PURE__*/_jsxs(\"div\",{className:\"text-center\",children:[/*#__PURE__*/_jsx(Image,{src:selectedImage.imageUrl,alt:selectedImage.fileName,className:\"max-w-full max-h-30rem\",preview:true}),/*#__PURE__*/_jsxs(\"div\",{className:\"mt-3 text-sm text-600\",children:[/*#__PURE__*/_jsxs(\"p\",{children:[\"\\u6A94\\u6848\\u540D\\u7A31: \",selectedImage.fileName]}),/*#__PURE__*/_jsxs(\"p\",{children:[\"\\u6A94\\u6848\\u5927\\u5C0F: \",formatFileSize(selectedImage.fileSize)]}),/*#__PURE__*/_jsxs(\"p\",{children:[\"\\u5EFA\\u7ACB\\u65E5\\u671F: \",formatDate(selectedImage.createdDate)]})]})]})})]});};export default ImageManagementPage;", "map": {"version": 3, "names": ["React", "useState", "useRef", "useEffect", "<PERSON><PERSON>", "Column", "DataTable", "Dialog", "Toast", "ConfirmDialog", "confirmDialog", "Card", "ProgressSpinner", "Tag", "Image", "InputText", "Calendar", "formatUtcToTaipei", "api", "log", "jsx", "_jsx", "jsxs", "_jsxs", "ImageManagementPage", "toast", "images", "setImages", "loading", "setLoading", "refreshing", "setRefreshing", "selectedImage", "setSelectedImage", "showPreviewDialog", "setShowPreviewDialog", "fileNameFilter", "setFileNameFilter", "startDateFilter", "setStartDateFilter", "endDateFilter", "setEndDateFilter", "loadImages", "params", "fileName", "startDate", "toISOString", "undefined", "endDate", "response", "get", "data", "count", "length", "error", "_toast$current", "current", "show", "severity", "summary", "detail", "life", "handleSearch", "handleReset", "deleteImage", "image", "_toast$current2", "delete", "concat", "_toast$current3", "_error$response", "_error$response$data", "message", "downloadImage", "_toast$current4", "responseType", "blob", "Blob", "url", "window", "URL", "createObjectURL", "link", "document", "createElement", "href", "download", "body", "append<PERSON><PERSON><PERSON>", "click", "<PERSON><PERSON><PERSON><PERSON>", "revokeObjectURL", "_toast$current5", "previewImage", "confirmDelete", "header", "icon", "acceptLabel", "<PERSON><PERSON><PERSON><PERSON>", "accept", "formatFileSize", "bytes", "k", "sizes", "i", "Math", "floor", "parseFloat", "pow", "toFixed", "formatDate", "dateString", "console", "thumbnailBodyTemplate", "rowData", "src", "imageUrl", "alt", "width", "height", "className", "preview", "onError", "e", "target", "fileSizeBodyTemplate", "fileSize", "createdDateBodyTemplate", "createdDate", "modifiedDateBodyTemplate", "modifiedDate", "fileTypeBodyTemplate", "_rowData$fileName$spl", "extension", "split", "pop", "toUpperCase", "value", "actionBodyTemplate", "children", "onClick", "tooltip", "tooltipOptions", "position", "paginatorLeft", "type", "text", "disabled", "paginatorRight", "style", "ref", "title", "id", "onChange", "placeholder", "showIcon", "dateFormat", "label", "paginator", "rows", "rowsPerPageOptions", "emptyMessage", "tableStyle", "min<PERSON><PERSON><PERSON>", "field", "sortable", "visible", "max<PERSON><PERSON><PERSON>", "onHide", "modal"], "sources": ["C:/Users/<USER>/Desktop/demo-git/demo-react/my-fullstack-app/frontend/src/components/Page/ImageManagementPage.tsx"], "sourcesContent": ["import React, { useState, useRef, useEffect } from 'react';\r\nimport { Button } from 'primereact/button';\r\nimport { Column } from 'primereact/column';\r\nimport { DataTable } from 'primereact/datatable';\r\nimport { Dialog } from 'primereact/dialog';\r\nimport { Toast } from 'primereact/toast';\r\nimport { ConfirmDialog, confirmDialog } from 'primereact/confirmdialog';\r\nimport { Card } from 'primereact/card';\r\nimport { ProgressSpinner } from 'primereact/progressspinner';\r\nimport { Tag } from 'primereact/tag';\r\nimport { Image } from 'primereact/image';\r\nimport { InputText } from 'primereact/inputtext';\r\nimport { Calendar } from 'primereact/calendar';\r\nimport { formatUtcToTaipei } from \"../../utils/dateUtils\";\r\nimport api from '../../services/api';\r\nimport { log } from '../../utils/logger';\r\n\r\ninterface ImageFile {\r\n  fileName: string;\r\n  filePath: string;\r\n  fileSize: number;\r\n  createdDate: string;\r\n  modifiedDate: string;\r\n  imageUrl: string;\r\n}\r\n\r\nconst ImageManagementPage: React.FC = () => {\r\n  const toast = useRef<Toast>(null);\r\n  const [images, setImages] = useState<ImageFile[]>([]);\r\n  const [loading, setLoading] = useState(true);\r\n  const [refreshing, setRefreshing] = useState(false);\r\n  const [selectedImage, setSelectedImage] = useState<ImageFile | null>(null);\r\n  const [showPreviewDialog, setShowPreviewDialog] = useState(false);\r\n  const [fileNameFilter, setFileNameFilter] = useState('');\r\n  const [startDateFilter, setStartDateFilter] = useState<Date | null>(null);\r\n  const [endDateFilter, setEndDateFilter] = useState<Date | null>(null);\r\n\r\n  // 載入圖片列表\r\n  const loadImages = async () => {\r\n    try {\r\n      setRefreshing(true);\r\n      log.api('載入圖片列表');\r\n\r\n      const params = {\r\n        fileName: fileNameFilter,\r\n        startDate: startDateFilter ? startDateFilter.toISOString() : undefined,\r\n        endDate: endDateFilter ? endDateFilter.toISOString() : undefined,\r\n      };\r\n\r\n      const response = await api.get('/api/file/GetImageFiles', { params });\r\n      setImages(response.data);\r\n\r\n      log.api('圖片列表載入成功', { count: response.data.length });\r\n\r\n    } catch (error: any) {\r\n      log.error('載入圖片列表失敗', error);\r\n      toast.current?.show({\r\n        severity: 'error',\r\n        summary: '載入失敗',\r\n        detail: '無法載入圖片列表',\r\n        life: 5000\r\n      });\r\n    } finally {\r\n      setLoading(false);\r\n      setRefreshing(false);\r\n    }\r\n  };\r\n\r\n  const handleSearch = () => {\r\n    loadImages();\r\n  };\r\n\r\n  const handleReset = () => {\r\n    setFileNameFilter('');\r\n    setStartDateFilter(null);\r\n    setEndDateFilter(null);\r\n    loadImages();\r\n  };\r\n\r\n  // 刪除圖片\r\n  const deleteImage = async (image: ImageFile) => {\r\n    try {\r\n      log.api('刪除圖片', { fileName: image.fileName });\r\n\r\n      await api.delete(`/api/file/DeleteImageFile`, {\r\n        params: { fileName: image.fileName }\r\n      });\r\n\r\n      toast.current?.show({\r\n        severity: 'success',\r\n        summary: '刪除成功',\r\n        detail: `圖片 ${image.fileName} 已刪除`,\r\n        life: 3000\r\n      });\r\n\r\n      // 重新載入列表\r\n      loadImages();\r\n\r\n    } catch (error: any) {\r\n      log.error('刪除圖片失敗', error);\r\n      toast.current?.show({\r\n        severity: 'error',\r\n        summary: '刪除失敗',\r\n        detail: error.response?.data?.message || '刪除圖片失敗',\r\n        life: 5000\r\n      });\r\n    }\r\n  };\r\n\r\n  // 下載圖片\r\n  const downloadImage = async (image: ImageFile) => {\r\n    try {\r\n      log.api('下載圖片', { fileName: image.fileName });\r\n\r\n      const response = await api.get(`/api/file/DownloadImageFile`, {\r\n        params: { fileName: image.fileName },\r\n        responseType: 'blob'\r\n      });\r\n\r\n      // 創建下載連結\r\n      const blob = new Blob([response.data]);\r\n      const url = window.URL.createObjectURL(blob);\r\n      const link = document.createElement('a');\r\n      link.href = url;\r\n      link.download = image.fileName;\r\n      document.body.appendChild(link);\r\n      link.click();\r\n      document.body.removeChild(link);\r\n      window.URL.revokeObjectURL(url);\r\n\r\n      toast.current?.show({\r\n        severity: 'success',\r\n        summary: '下載成功',\r\n        detail: `圖片 ${image.fileName} 下載完成`,\r\n        life: 3000\r\n      });\r\n\r\n    } catch (error: any) {\r\n      log.error('下載圖片失敗', error);\r\n      toast.current?.show({\r\n        severity: 'error',\r\n        summary: '下載失敗',\r\n        detail: '下載圖片失敗',\r\n        life: 5000\r\n      });\r\n    }\r\n  };\r\n\r\n  // 預覽圖片\r\n  const previewImage = (image: ImageFile) => {\r\n    setSelectedImage(image);\r\n    setShowPreviewDialog(true);\r\n  };\r\n\r\n  // 確認刪除\r\n  const confirmDelete = (image: ImageFile) => {\r\n    confirmDialog({\r\n      message: `確定要刪除圖片 \"${image.fileName}\" 嗎？此操作無法復原。`,\r\n      header: '確認刪除',\r\n      icon: 'pi pi-exclamation-triangle',\r\n      acceptLabel: '確定',\r\n      rejectLabel: '取消',\r\n      accept: () => deleteImage(image),\r\n    });\r\n  };\r\n\r\n  // 格式化檔案大小\r\n  const formatFileSize = (bytes: number): string => {\r\n    if (bytes === 0) return '0 Bytes';\r\n    const k = 1024;\r\n    const sizes = ['Bytes', 'KB', 'MB', 'GB'];\r\n    const i = Math.floor(Math.log(bytes) / Math.log(k));\r\n    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];\r\n  };\r\n\r\n  // 格式化日期\r\n  const formatDate = (dateString: string): string => {\r\n    if (!dateString) return '';\r\n    try {\r\n      return formatUtcToTaipei(dateString, 'yyyy/MM/dd HH:mm:ss');\r\n    } catch (error) {\r\n      console.error('Error formatting date:', error);\r\n      return dateString; // or return a default/error indicator\r\n    }\r\n  };\r\n\r\n  // 縮圖模板\r\n  const thumbnailBodyTemplate = (rowData: ImageFile) => {\r\n    return (\r\n      <Image\r\n        src={rowData.imageUrl}\r\n        alt={rowData.fileName}\r\n        width=\"60\"\r\n        height=\"60\"\r\n        className=\"border-round\"\r\n        preview={false}\r\n        onError={(e) => {\r\n          // 如果圖片載入失敗，顯示預設圖示\r\n          (e.target as HTMLImageElement).src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjAiIGhlaWdodD0iNjAiIHZpZXdCb3g9IjAgMCA2MCA2MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjYwIiBoZWlnaHQ9IjYwIiBmaWxsPSIjRjVGNUY1Ii8+CjxwYXRoIGQ9Ik0zMCAyMEM0MS4wNDU3IDIwIDUwIDI4Ljk1NDMgNTAgNDBDNTAgNTEuMDQ1NyA0MS4wNDU3IDYwIDMwIDYwQzE4Ljk1NDMgNjAgMTAgNTEuMDQ1NyAxMCA0MEMxMCAyOC45NTQzIDE4Ljk1NDMgMjAgMzAgMjBaIiBmaWxsPSIjRTBFMEUwIi8+CjxwYXRoIGQ9Ik0zMCAyNUM0MS4wNDU3IDI1IDUwIDMzLjk1NDMgNTAgNDVDNTAgNTYuMDQ1NyA0MS4wNDU3IDY1IDMwIDY1QzE4Ljk1NDMgNjUgMTAgNTYuMDQ1NyAxMCA0NUMxMCAzMy45NTQzIDE4Ljk1NDMgMjUgMzAgMjVaIiBmaWxsPSIjQ0NDQ0NDIi8+CjwvZz4KPC9zdmc+';\r\n        }}\r\n      />\r\n    );\r\n  };\r\n\r\n  // 檔案大小模板\r\n  const fileSizeBodyTemplate = (rowData: ImageFile) => {\r\n    return formatFileSize(rowData.fileSize);\r\n  };\r\n\r\n  // 建立日期模板\r\n  const createdDateBodyTemplate = (rowData: ImageFile) => {\r\n    return formatDate(rowData.createdDate);\r\n  };\r\n\r\n  // 修改日期模板\r\n  const modifiedDateBodyTemplate = (rowData: ImageFile) => {\r\n    return formatDate(rowData.modifiedDate);\r\n  };\r\n\r\n  // 檔案類型模板\r\n  const fileTypeBodyTemplate = (rowData: ImageFile) => {\r\n    const extension = rowData.fileName.split('.').pop()?.toUpperCase();\r\n    let severity: \"success\" | \"info\" | \"warning\" | \"danger\" = 'info';\r\n\r\n    switch (extension) {\r\n      case 'JPG':\r\n      case 'JPEG':\r\n        severity = 'success';\r\n        break;\r\n      case 'PNG':\r\n        severity = 'info';\r\n        break;\r\n      case 'GIF':\r\n        severity = 'warning';\r\n        break;\r\n      case 'WEBP':\r\n        severity = 'danger';\r\n        break;\r\n      default:\r\n        severity = 'warning';\r\n    }\r\n\r\n    return <Tag value={extension} severity={severity} />;\r\n  };\r\n\r\n  // 操作按鈕模板\r\n  const actionBodyTemplate = (rowData: ImageFile) => {\r\n    return (\r\n      <div className=\"flex gap-2\">\r\n        <Button\r\n          icon=\"pi pi-eye\"\r\n          className=\"p-button-info p-button-sm\"\r\n          onClick={() => previewImage(rowData)}\r\n          tooltip=\"預覽\"\r\n          tooltipOptions={{ position: 'top' }}\r\n        />\r\n        <Button\r\n          icon=\"pi pi-download\"\r\n          className=\"p-button-success p-button-sm\"\r\n          onClick={() => downloadImage(rowData)}\r\n          tooltip=\"下載\"\r\n          tooltipOptions={{ position: 'top' }}\r\n        />\r\n        <Button\r\n          icon=\"pi pi-trash\"\r\n          className=\"p-button-danger p-button-sm\"\r\n          onClick={() => confirmDelete(rowData)}\r\n          tooltip=\"刪除\"\r\n          tooltipOptions={{ position: 'top' }}\r\n        />\r\n      </div>\r\n    );\r\n  };\r\n\r\n  // 分頁器左側\r\n  const paginatorLeft = (\r\n    <Button\r\n      type=\"button\"\r\n      icon=\"pi pi-refresh\"\r\n      text\r\n      onClick={loadImages}\r\n      disabled={refreshing}\r\n    />\r\n  );\r\n\r\n  const paginatorRight = <div></div>;\r\n\r\n  useEffect(() => {\r\n    loadImages();\r\n  }, []);\r\n\r\n  if (loading) {\r\n    return (\r\n      <div className=\"flex justify-content-center align-items-center\" style={{ height: '400px' }}>\r\n        <ProgressSpinner />\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <div>\r\n      <Toast ref={toast} />\r\n      <ConfirmDialog />\r\n\r\n      <Card title=\"圖片管理\" className=\"mb-4\">\r\n        <p className=\"text-600 line-height-3 m-0\">\r\n          管理系統上傳的圖片檔案，包括治療記錄圖片、病患資料圖片等。您可以預覽、下載或刪除不需要的圖片檔案。\r\n        </p>\r\n      </Card>\r\n\r\n      {/* 搜尋條件 */}\r\n      <Card className=\"mb-4\">\r\n        <div className=\"grid\">\r\n          <div className=\"col-12 md:col-3\">\r\n            <InputText\r\n              id=\"fileNameFilter\"\r\n              value={fileNameFilter}\r\n              onChange={(e) => setFileNameFilter(e.target.value)}\r\n              placeholder=\"依檔名搜尋\"\r\n              className=\"w-full\"\r\n            />\r\n          </div>\r\n          <div className=\"col-6 md:col-3\">\r\n            <Calendar\r\n                id=\"startDateFilter\"\r\n                value={startDateFilter}\r\n                onChange={(e) => setStartDateFilter(e.value as Date)}\r\n                placeholder=\"選擇結束日期\"\r\n                className=\"w-full\"\r\n                showIcon\r\n                dateFormat=\"yy/mm/dd\"\r\n              />\r\n          </div>\r\n          <div className=\"col-6 md:col-3\">\r\n              <Calendar\r\n                id=\"endDateFilter\"\r\n                value={endDateFilter}\r\n                onChange={(e) => setEndDateFilter(e.value as Date)}\r\n                placeholder=\"選擇結束日期\"\r\n                className=\"w-full\"\r\n                showIcon\r\n                dateFormat=\"yy/mm/dd\"\r\n              />\r\n          </div>\r\n          <div className=\"col-12 md:col-4\">\r\n            <div className=\"flex gap-2\">\r\n              <Button \r\n                label=\"搜尋\" \r\n                icon=\"pi pi-search\" \r\n                onClick={handleSearch} \r\n                />\r\n              <Button \r\n                label=\"重設\" \r\n                icon=\"pi pi-undo\" \r\n                onClick={handleReset} \r\n                className=\"p-button-secondary\" />\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </Card>\r\n\r\n      <Card>\r\n        <DataTable\r\n          value={images}\r\n          paginator\r\n          rows={20}\r\n          rowsPerPageOptions={[10, 20, 50]}\r\n          emptyMessage=\"沒有找到圖片檔案\"\r\n          tableStyle={{ minWidth: '50rem' }}\r\n          paginatorLeft={paginatorLeft}\r\n          paginatorRight={paginatorRight}\r\n          loading={refreshing}\r\n        >\r\n          <Column header=\"縮圖\" body={thumbnailBodyTemplate} style={{ width: '10%' }} />\r\n          <Column field=\"fileName\" header=\"檔案名稱\" sortable style={{ width: '25%' }} />\r\n          <Column field=\"fileType\" header=\"類型\" body={fileTypeBodyTemplate} style={{ width: '10%' }} />\r\n          <Column field=\"fileSize\" header=\"檔案大小\" body={fileSizeBodyTemplate} sortable style={{ width: '15%' }} />\r\n          <Column field=\"createdDate\" header=\"建立日期\" body={createdDateBodyTemplate} sortable style={{ width: '20%' }} />\r\n          <Column field=\"modifiedDate\" header=\"修改日期\" body={modifiedDateBodyTemplate} sortable style={{ width: '20%' }} />\r\n          <Column header=\"操作\" body={actionBodyTemplate} style={{ width: '15%' }} />\r\n        </DataTable>\r\n      </Card>\r\n\r\n      {/* 圖片預覽對話框 */}\r\n      <Dialog\r\n        header={`預覽圖片 - ${selectedImage?.fileName}`}\r\n        visible={showPreviewDialog}\r\n        style={{ width: '80vw', maxWidth: '800px' }}\r\n        onHide={() => setShowPreviewDialog(false)}\r\n        modal\r\n      >\r\n        {selectedImage && (\r\n          <div className=\"text-center\">\r\n            <Image\r\n              src={selectedImage.imageUrl}\r\n              alt={selectedImage.fileName}\r\n              className=\"max-w-full max-h-30rem\"\r\n              preview\r\n            />\r\n            <div className=\"mt-3 text-sm text-600\">\r\n              <p>檔案名稱: {selectedImage.fileName}</p>\r\n              <p>檔案大小: {formatFileSize(selectedImage.fileSize)}</p>\r\n              <p>建立日期: {formatDate(selectedImage.createdDate)}</p>\r\n            </div>\r\n          </div>\r\n        )}\r\n      </Dialog>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default ImageManagementPage;\r\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,MAAM,CAAEC,SAAS,KAAQ,OAAO,CAC1D,OAASC,MAAM,KAAQ,mBAAmB,CAC1C,OAASC,MAAM,KAAQ,mBAAmB,CAC1C,OAASC,SAAS,KAAQ,sBAAsB,CAChD,OAASC,MAAM,KAAQ,mBAAmB,CAC1C,OAASC,KAAK,KAAQ,kBAAkB,CACxC,OAASC,aAAa,CAAEC,aAAa,KAAQ,0BAA0B,CACvE,OAASC,IAAI,KAAQ,iBAAiB,CACtC,OAASC,eAAe,KAAQ,4BAA4B,CAC5D,OAASC,GAAG,KAAQ,gBAAgB,CACpC,OAASC,KAAK,KAAQ,kBAAkB,CACxC,OAASC,SAAS,KAAQ,sBAAsB,CAChD,OAASC,QAAQ,KAAQ,qBAAqB,CAC9C,OAASC,iBAAiB,KAAQ,uBAAuB,CACzD,MAAO,CAAAC,GAAG,KAAM,oBAAoB,CACpC,OAASC,GAAG,KAAQ,oBAAoB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAWzC,KAAM,CAAAC,mBAA6B,CAAGA,CAAA,GAAM,CAC1C,KAAM,CAAAC,KAAK,CAAGvB,MAAM,CAAQ,IAAI,CAAC,CACjC,KAAM,CAACwB,MAAM,CAAEC,SAAS,CAAC,CAAG1B,QAAQ,CAAc,EAAE,CAAC,CACrD,KAAM,CAAC2B,OAAO,CAAEC,UAAU,CAAC,CAAG5B,QAAQ,CAAC,IAAI,CAAC,CAC5C,KAAM,CAAC6B,UAAU,CAAEC,aAAa,CAAC,CAAG9B,QAAQ,CAAC,KAAK,CAAC,CACnD,KAAM,CAAC+B,aAAa,CAAEC,gBAAgB,CAAC,CAAGhC,QAAQ,CAAmB,IAAI,CAAC,CAC1E,KAAM,CAACiC,iBAAiB,CAAEC,oBAAoB,CAAC,CAAGlC,QAAQ,CAAC,KAAK,CAAC,CACjE,KAAM,CAACmC,cAAc,CAAEC,iBAAiB,CAAC,CAAGpC,QAAQ,CAAC,EAAE,CAAC,CACxD,KAAM,CAACqC,eAAe,CAAEC,kBAAkB,CAAC,CAAGtC,QAAQ,CAAc,IAAI,CAAC,CACzE,KAAM,CAACuC,aAAa,CAAEC,gBAAgB,CAAC,CAAGxC,QAAQ,CAAc,IAAI,CAAC,CAErE;AACA,KAAM,CAAAyC,UAAU,CAAG,KAAAA,CAAA,GAAY,CAC7B,GAAI,CACFX,aAAa,CAAC,IAAI,CAAC,CACnBZ,GAAG,CAACD,GAAG,CAAC,QAAQ,CAAC,CAEjB,KAAM,CAAAyB,MAAM,CAAG,CACbC,QAAQ,CAAER,cAAc,CACxBS,SAAS,CAAEP,eAAe,CAAGA,eAAe,CAACQ,WAAW,CAAC,CAAC,CAAGC,SAAS,CACtEC,OAAO,CAAER,aAAa,CAAGA,aAAa,CAACM,WAAW,CAAC,CAAC,CAAGC,SACzD,CAAC,CAED,KAAM,CAAAE,QAAQ,CAAG,KAAM,CAAA/B,GAAG,CAACgC,GAAG,CAAC,yBAAyB,CAAE,CAAEP,MAAO,CAAC,CAAC,CACrEhB,SAAS,CAACsB,QAAQ,CAACE,IAAI,CAAC,CAExBhC,GAAG,CAACD,GAAG,CAAC,UAAU,CAAE,CAAEkC,KAAK,CAAEH,QAAQ,CAACE,IAAI,CAACE,MAAO,CAAC,CAAC,CAEtD,CAAE,MAAOC,KAAU,CAAE,KAAAC,cAAA,CACnBpC,GAAG,CAACmC,KAAK,CAAC,UAAU,CAAEA,KAAK,CAAC,CAC5B,CAAAC,cAAA,CAAA9B,KAAK,CAAC+B,OAAO,UAAAD,cAAA,iBAAbA,cAAA,CAAeE,IAAI,CAAC,CAClBC,QAAQ,CAAE,OAAO,CACjBC,OAAO,CAAE,MAAM,CACfC,MAAM,CAAE,UAAU,CAClBC,IAAI,CAAE,IACR,CAAC,CAAC,CACJ,CAAC,OAAS,CACRhC,UAAU,CAAC,KAAK,CAAC,CACjBE,aAAa,CAAC,KAAK,CAAC,CACtB,CACF,CAAC,CAED,KAAM,CAAA+B,YAAY,CAAGA,CAAA,GAAM,CACzBpB,UAAU,CAAC,CAAC,CACd,CAAC,CAED,KAAM,CAAAqB,WAAW,CAAGA,CAAA,GAAM,CACxB1B,iBAAiB,CAAC,EAAE,CAAC,CACrBE,kBAAkB,CAAC,IAAI,CAAC,CACxBE,gBAAgB,CAAC,IAAI,CAAC,CACtBC,UAAU,CAAC,CAAC,CACd,CAAC,CAED;AACA,KAAM,CAAAsB,WAAW,CAAG,KAAO,CAAAC,KAAgB,EAAK,CAC9C,GAAI,KAAAC,eAAA,CACF/C,GAAG,CAACD,GAAG,CAAC,MAAM,CAAE,CAAE0B,QAAQ,CAAEqB,KAAK,CAACrB,QAAS,CAAC,CAAC,CAE7C,KAAM,CAAA1B,GAAG,CAACiD,MAAM,6BAA8B,CAC5CxB,MAAM,CAAE,CAAEC,QAAQ,CAAEqB,KAAK,CAACrB,QAAS,CACrC,CAAC,CAAC,CAEF,CAAAsB,eAAA,CAAAzC,KAAK,CAAC+B,OAAO,UAAAU,eAAA,iBAAbA,eAAA,CAAeT,IAAI,CAAC,CAClBC,QAAQ,CAAE,SAAS,CACnBC,OAAO,CAAE,MAAM,CACfC,MAAM,iBAAAQ,MAAA,CAAQH,KAAK,CAACrB,QAAQ,uBAAM,CAClCiB,IAAI,CAAE,IACR,CAAC,CAAC,CAEF;AACAnB,UAAU,CAAC,CAAC,CAEd,CAAE,MAAOY,KAAU,CAAE,KAAAe,eAAA,CAAAC,eAAA,CAAAC,oBAAA,CACnBpD,GAAG,CAACmC,KAAK,CAAC,QAAQ,CAAEA,KAAK,CAAC,CAC1B,CAAAe,eAAA,CAAA5C,KAAK,CAAC+B,OAAO,UAAAa,eAAA,iBAAbA,eAAA,CAAeZ,IAAI,CAAC,CAClBC,QAAQ,CAAE,OAAO,CACjBC,OAAO,CAAE,MAAM,CACfC,MAAM,CAAE,EAAAU,eAAA,CAAAhB,KAAK,CAACL,QAAQ,UAAAqB,eAAA,kBAAAC,oBAAA,CAAdD,eAAA,CAAgBnB,IAAI,UAAAoB,oBAAA,iBAApBA,oBAAA,CAAsBC,OAAO,GAAI,QAAQ,CACjDX,IAAI,CAAE,IACR,CAAC,CAAC,CACJ,CACF,CAAC,CAED;AACA,KAAM,CAAAY,aAAa,CAAG,KAAO,CAAAR,KAAgB,EAAK,CAChD,GAAI,KAAAS,eAAA,CACFvD,GAAG,CAACD,GAAG,CAAC,MAAM,CAAE,CAAE0B,QAAQ,CAAEqB,KAAK,CAACrB,QAAS,CAAC,CAAC,CAE7C,KAAM,CAAAK,QAAQ,CAAG,KAAM,CAAA/B,GAAG,CAACgC,GAAG,+BAAgC,CAC5DP,MAAM,CAAE,CAAEC,QAAQ,CAAEqB,KAAK,CAACrB,QAAS,CAAC,CACpC+B,YAAY,CAAE,MAChB,CAAC,CAAC,CAEF;AACA,KAAM,CAAAC,IAAI,CAAG,GAAI,CAAAC,IAAI,CAAC,CAAC5B,QAAQ,CAACE,IAAI,CAAC,CAAC,CACtC,KAAM,CAAA2B,GAAG,CAAGC,MAAM,CAACC,GAAG,CAACC,eAAe,CAACL,IAAI,CAAC,CAC5C,KAAM,CAAAM,IAAI,CAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC,CACxCF,IAAI,CAACG,IAAI,CAAGP,GAAG,CACfI,IAAI,CAACI,QAAQ,CAAGrB,KAAK,CAACrB,QAAQ,CAC9BuC,QAAQ,CAACI,IAAI,CAACC,WAAW,CAACN,IAAI,CAAC,CAC/BA,IAAI,CAACO,KAAK,CAAC,CAAC,CACZN,QAAQ,CAACI,IAAI,CAACG,WAAW,CAACR,IAAI,CAAC,CAC/BH,MAAM,CAACC,GAAG,CAACW,eAAe,CAACb,GAAG,CAAC,CAE/B,CAAAJ,eAAA,CAAAjD,KAAK,CAAC+B,OAAO,UAAAkB,eAAA,iBAAbA,eAAA,CAAejB,IAAI,CAAC,CAClBC,QAAQ,CAAE,SAAS,CACnBC,OAAO,CAAE,MAAM,CACfC,MAAM,iBAAAQ,MAAA,CAAQH,KAAK,CAACrB,QAAQ,6BAAO,CACnCiB,IAAI,CAAE,IACR,CAAC,CAAC,CAEJ,CAAE,MAAOP,KAAU,CAAE,KAAAsC,eAAA,CACnBzE,GAAG,CAACmC,KAAK,CAAC,QAAQ,CAAEA,KAAK,CAAC,CAC1B,CAAAsC,eAAA,CAAAnE,KAAK,CAAC+B,OAAO,UAAAoC,eAAA,iBAAbA,eAAA,CAAenC,IAAI,CAAC,CAClBC,QAAQ,CAAE,OAAO,CACjBC,OAAO,CAAE,MAAM,CACfC,MAAM,CAAE,QAAQ,CAChBC,IAAI,CAAE,IACR,CAAC,CAAC,CACJ,CACF,CAAC,CAED;AACA,KAAM,CAAAgC,YAAY,CAAI5B,KAAgB,EAAK,CACzChC,gBAAgB,CAACgC,KAAK,CAAC,CACvB9B,oBAAoB,CAAC,IAAI,CAAC,CAC5B,CAAC,CAED;AACA,KAAM,CAAA2D,aAAa,CAAI7B,KAAgB,EAAK,CAC1CvD,aAAa,CAAC,CACZ8D,OAAO,iDAAAJ,MAAA,CAAcH,KAAK,CAACrB,QAAQ,mEAAc,CACjDmD,MAAM,CAAE,MAAM,CACdC,IAAI,CAAE,4BAA4B,CAClCC,WAAW,CAAE,IAAI,CACjBC,WAAW,CAAE,IAAI,CACjBC,MAAM,CAAEA,CAAA,GAAMnC,WAAW,CAACC,KAAK,CACjC,CAAC,CAAC,CACJ,CAAC,CAED;AACA,KAAM,CAAAmC,cAAc,CAAIC,KAAa,EAAa,CAChD,GAAIA,KAAK,GAAK,CAAC,CAAE,MAAO,SAAS,CACjC,KAAM,CAAAC,CAAC,CAAG,IAAI,CACd,KAAM,CAAAC,KAAK,CAAG,CAAC,OAAO,CAAE,IAAI,CAAE,IAAI,CAAE,IAAI,CAAC,CACzC,KAAM,CAAAC,CAAC,CAAGC,IAAI,CAACC,KAAK,CAACD,IAAI,CAACtF,GAAG,CAACkF,KAAK,CAAC,CAAGI,IAAI,CAACtF,GAAG,CAACmF,CAAC,CAAC,CAAC,CACnD,MAAO,CAAAK,UAAU,CAAC,CAACN,KAAK,CAAGI,IAAI,CAACG,GAAG,CAACN,CAAC,CAAEE,CAAC,CAAC,EAAEK,OAAO,CAAC,CAAC,CAAC,CAAC,CAAG,GAAG,CAAGN,KAAK,CAACC,CAAC,CAAC,CACzE,CAAC,CAED;AACA,KAAM,CAAAM,UAAU,CAAIC,UAAkB,EAAa,CACjD,GAAI,CAACA,UAAU,CAAE,MAAO,EAAE,CAC1B,GAAI,CACF,MAAO,CAAA9F,iBAAiB,CAAC8F,UAAU,CAAE,qBAAqB,CAAC,CAC7D,CAAE,MAAOzD,KAAK,CAAE,CACd0D,OAAO,CAAC1D,KAAK,CAAC,wBAAwB,CAAEA,KAAK,CAAC,CAC9C,MAAO,CAAAyD,UAAU,CAAE;AACrB,CACF,CAAC,CAED;AACA,KAAM,CAAAE,qBAAqB,CAAIC,OAAkB,EAAK,CACpD,mBACE7F,IAAA,CAACP,KAAK,EACJqG,GAAG,CAAED,OAAO,CAACE,QAAS,CACtBC,GAAG,CAAEH,OAAO,CAACtE,QAAS,CACtB0E,KAAK,CAAC,IAAI,CACVC,MAAM,CAAC,IAAI,CACXC,SAAS,CAAC,cAAc,CACxBC,OAAO,CAAE,KAAM,CACfC,OAAO,CAAGC,CAAC,EAAK,CACd;AACCA,CAAC,CAACC,MAAM,CAAsBT,GAAG,CAAG,gnBAAgnB,CACvpB,CAAE,CACH,CAAC,CAEN,CAAC,CAED;AACA,KAAM,CAAAU,oBAAoB,CAAIX,OAAkB,EAAK,CACnD,MAAO,CAAAd,cAAc,CAACc,OAAO,CAACY,QAAQ,CAAC,CACzC,CAAC,CAED;AACA,KAAM,CAAAC,uBAAuB,CAAIb,OAAkB,EAAK,CACtD,MAAO,CAAAJ,UAAU,CAACI,OAAO,CAACc,WAAW,CAAC,CACxC,CAAC,CAED;AACA,KAAM,CAAAC,wBAAwB,CAAIf,OAAkB,EAAK,CACvD,MAAO,CAAAJ,UAAU,CAACI,OAAO,CAACgB,YAAY,CAAC,CACzC,CAAC,CAED;AACA,KAAM,CAAAC,oBAAoB,CAAIjB,OAAkB,EAAK,KAAAkB,qBAAA,CACnD,KAAM,CAAAC,SAAS,EAAAD,qBAAA,CAAGlB,OAAO,CAACtE,QAAQ,CAAC0F,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,CAAC,CAAC,UAAAH,qBAAA,iBAAjCA,qBAAA,CAAmCI,WAAW,CAAC,CAAC,CAClE,GAAI,CAAA9E,QAAmD,CAAG,MAAM,CAEhE,OAAQ2E,SAAS,EACf,IAAK,KAAK,CACV,IAAK,MAAM,CACT3E,QAAQ,CAAG,SAAS,CACpB,MACF,IAAK,KAAK,CACRA,QAAQ,CAAG,MAAM,CACjB,MACF,IAAK,KAAK,CACRA,QAAQ,CAAG,SAAS,CACpB,MACF,IAAK,MAAM,CACTA,QAAQ,CAAG,QAAQ,CACnB,MACF,QACEA,QAAQ,CAAG,SAAS,CACxB,CAEA,mBAAOrC,IAAA,CAACR,GAAG,EAAC4H,KAAK,CAAEJ,SAAU,CAAC3E,QAAQ,CAAEA,QAAS,CAAE,CAAC,CACtD,CAAC,CAED;AACA,KAAM,CAAAgF,kBAAkB,CAAIxB,OAAkB,EAAK,CACjD,mBACE3F,KAAA,QAAKiG,SAAS,CAAC,YAAY,CAAAmB,QAAA,eACzBtH,IAAA,CAACjB,MAAM,EACL4F,IAAI,CAAC,WAAW,CAChBwB,SAAS,CAAC,2BAA2B,CACrCoB,OAAO,CAAEA,CAAA,GAAM/C,YAAY,CAACqB,OAAO,CAAE,CACrC2B,OAAO,CAAC,cAAI,CACZC,cAAc,CAAE,CAAEC,QAAQ,CAAE,KAAM,CAAE,CACrC,CAAC,cACF1H,IAAA,CAACjB,MAAM,EACL4F,IAAI,CAAC,gBAAgB,CACrBwB,SAAS,CAAC,8BAA8B,CACxCoB,OAAO,CAAEA,CAAA,GAAMnE,aAAa,CAACyC,OAAO,CAAE,CACtC2B,OAAO,CAAC,cAAI,CACZC,cAAc,CAAE,CAAEC,QAAQ,CAAE,KAAM,CAAE,CACrC,CAAC,cACF1H,IAAA,CAACjB,MAAM,EACL4F,IAAI,CAAC,aAAa,CAClBwB,SAAS,CAAC,6BAA6B,CACvCoB,OAAO,CAAEA,CAAA,GAAM9C,aAAa,CAACoB,OAAO,CAAE,CACtC2B,OAAO,CAAC,cAAI,CACZC,cAAc,CAAE,CAAEC,QAAQ,CAAE,KAAM,CAAE,CACrC,CAAC,EACC,CAAC,CAEV,CAAC,CAED;AACA,KAAM,CAAAC,aAAa,cACjB3H,IAAA,CAACjB,MAAM,EACL6I,IAAI,CAAC,QAAQ,CACbjD,IAAI,CAAC,eAAe,CACpBkD,IAAI,MACJN,OAAO,CAAElG,UAAW,CACpByG,QAAQ,CAAErH,UAAW,CACtB,CACF,CAED,KAAM,CAAAsH,cAAc,cAAG/H,IAAA,SAAU,CAAC,CAElClB,SAAS,CAAC,IAAM,CACduC,UAAU,CAAC,CAAC,CACd,CAAC,CAAE,EAAE,CAAC,CAEN,GAAId,OAAO,CAAE,CACX,mBACEP,IAAA,QAAKmG,SAAS,CAAC,gDAAgD,CAAC6B,KAAK,CAAE,CAAE9B,MAAM,CAAE,OAAQ,CAAE,CAAAoB,QAAA,cACzFtH,IAAA,CAACT,eAAe,GAAE,CAAC,CAChB,CAAC,CAEV,CAEA,mBACEW,KAAA,QAAAoH,QAAA,eACEtH,IAAA,CAACb,KAAK,EAAC8I,GAAG,CAAE7H,KAAM,CAAE,CAAC,cACrBJ,IAAA,CAACZ,aAAa,GAAE,CAAC,cAEjBY,IAAA,CAACV,IAAI,EAAC4I,KAAK,CAAC,0BAAM,CAAC/B,SAAS,CAAC,MAAM,CAAAmB,QAAA,cACjCtH,IAAA,MAAGmG,SAAS,CAAC,4BAA4B,CAAAmB,QAAA,CAAC,wSAE1C,CAAG,CAAC,CACA,CAAC,cAGPtH,IAAA,CAACV,IAAI,EAAC6G,SAAS,CAAC,MAAM,CAAAmB,QAAA,cACpBpH,KAAA,QAAKiG,SAAS,CAAC,MAAM,CAAAmB,QAAA,eACnBtH,IAAA,QAAKmG,SAAS,CAAC,iBAAiB,CAAAmB,QAAA,cAC9BtH,IAAA,CAACN,SAAS,EACRyI,EAAE,CAAC,gBAAgB,CACnBf,KAAK,CAAErG,cAAe,CACtBqH,QAAQ,CAAG9B,CAAC,EAAKtF,iBAAiB,CAACsF,CAAC,CAACC,MAAM,CAACa,KAAK,CAAE,CACnDiB,WAAW,CAAC,gCAAO,CACnBlC,SAAS,CAAC,QAAQ,CACnB,CAAC,CACC,CAAC,cACNnG,IAAA,QAAKmG,SAAS,CAAC,gBAAgB,CAAAmB,QAAA,cAC7BtH,IAAA,CAACL,QAAQ,EACLwI,EAAE,CAAC,iBAAiB,CACpBf,KAAK,CAAEnG,eAAgB,CACvBmH,QAAQ,CAAG9B,CAAC,EAAKpF,kBAAkB,CAACoF,CAAC,CAACc,KAAa,CAAE,CACrDiB,WAAW,CAAC,sCAAQ,CACpBlC,SAAS,CAAC,QAAQ,CAClBmC,QAAQ,MACRC,UAAU,CAAC,UAAU,CACtB,CAAC,CACD,CAAC,cACNvI,IAAA,QAAKmG,SAAS,CAAC,gBAAgB,CAAAmB,QAAA,cAC3BtH,IAAA,CAACL,QAAQ,EACPwI,EAAE,CAAC,eAAe,CAClBf,KAAK,CAAEjG,aAAc,CACrBiH,QAAQ,CAAG9B,CAAC,EAAKlF,gBAAgB,CAACkF,CAAC,CAACc,KAAa,CAAE,CACnDiB,WAAW,CAAC,sCAAQ,CACpBlC,SAAS,CAAC,QAAQ,CAClBmC,QAAQ,MACRC,UAAU,CAAC,UAAU,CACtB,CAAC,CACD,CAAC,cACNvI,IAAA,QAAKmG,SAAS,CAAC,iBAAiB,CAAAmB,QAAA,cAC9BpH,KAAA,QAAKiG,SAAS,CAAC,YAAY,CAAAmB,QAAA,eACzBtH,IAAA,CAACjB,MAAM,EACLyJ,KAAK,CAAC,cAAI,CACV7D,IAAI,CAAC,cAAc,CACnB4C,OAAO,CAAE9E,YAAa,CACrB,CAAC,cACJzC,IAAA,CAACjB,MAAM,EACLyJ,KAAK,CAAC,cAAI,CACV7D,IAAI,CAAC,YAAY,CACjB4C,OAAO,CAAE7E,WAAY,CACrByD,SAAS,CAAC,oBAAoB,CAAE,CAAC,EAChC,CAAC,CACH,CAAC,EACH,CAAC,CACF,CAAC,cAEPnG,IAAA,CAACV,IAAI,EAAAgI,QAAA,cACHpH,KAAA,CAACjB,SAAS,EACRmI,KAAK,CAAE/G,MAAO,CACdoI,SAAS,MACTC,IAAI,CAAE,EAAG,CACTC,kBAAkB,CAAE,CAAC,EAAE,CAAE,EAAE,CAAE,EAAE,CAAE,CACjCC,YAAY,CAAC,kDAAU,CACvBC,UAAU,CAAE,CAAEC,QAAQ,CAAE,OAAQ,CAAE,CAClCnB,aAAa,CAAEA,aAAc,CAC7BI,cAAc,CAAEA,cAAe,CAC/BxH,OAAO,CAAEE,UAAW,CAAA6G,QAAA,eAEpBtH,IAAA,CAAChB,MAAM,EAAC0F,MAAM,CAAC,cAAI,CAACR,IAAI,CAAE0B,qBAAsB,CAACoC,KAAK,CAAE,CAAE/B,KAAK,CAAE,KAAM,CAAE,CAAE,CAAC,cAC5EjG,IAAA,CAAChB,MAAM,EAAC+J,KAAK,CAAC,UAAU,CAACrE,MAAM,CAAC,0BAAM,CAACsE,QAAQ,MAAChB,KAAK,CAAE,CAAE/B,KAAK,CAAE,KAAM,CAAE,CAAE,CAAC,cAC3EjG,IAAA,CAAChB,MAAM,EAAC+J,KAAK,CAAC,UAAU,CAACrE,MAAM,CAAC,cAAI,CAACR,IAAI,CAAE4C,oBAAqB,CAACkB,KAAK,CAAE,CAAE/B,KAAK,CAAE,KAAM,CAAE,CAAE,CAAC,cAC5FjG,IAAA,CAAChB,MAAM,EAAC+J,KAAK,CAAC,UAAU,CAACrE,MAAM,CAAC,0BAAM,CAACR,IAAI,CAAEsC,oBAAqB,CAACwC,QAAQ,MAAChB,KAAK,CAAE,CAAE/B,KAAK,CAAE,KAAM,CAAE,CAAE,CAAC,cACvGjG,IAAA,CAAChB,MAAM,EAAC+J,KAAK,CAAC,aAAa,CAACrE,MAAM,CAAC,0BAAM,CAACR,IAAI,CAAEwC,uBAAwB,CAACsC,QAAQ,MAAChB,KAAK,CAAE,CAAE/B,KAAK,CAAE,KAAM,CAAE,CAAE,CAAC,cAC7GjG,IAAA,CAAChB,MAAM,EAAC+J,KAAK,CAAC,cAAc,CAACrE,MAAM,CAAC,0BAAM,CAACR,IAAI,CAAE0C,wBAAyB,CAACoC,QAAQ,MAAChB,KAAK,CAAE,CAAE/B,KAAK,CAAE,KAAM,CAAE,CAAE,CAAC,cAC/GjG,IAAA,CAAChB,MAAM,EAAC0F,MAAM,CAAC,cAAI,CAACR,IAAI,CAAEmD,kBAAmB,CAACW,KAAK,CAAE,CAAE/B,KAAK,CAAE,KAAM,CAAE,CAAE,CAAC,EAChE,CAAC,CACR,CAAC,cAGPjG,IAAA,CAACd,MAAM,EACLwF,MAAM,+BAAA3B,MAAA,CAAYpC,aAAa,SAAbA,aAAa,iBAAbA,aAAa,CAAEY,QAAQ,CAAG,CAC5C0H,OAAO,CAAEpI,iBAAkB,CAC3BmH,KAAK,CAAE,CAAE/B,KAAK,CAAE,MAAM,CAAEiD,QAAQ,CAAE,OAAQ,CAAE,CAC5CC,MAAM,CAAEA,CAAA,GAAMrI,oBAAoB,CAAC,KAAK,CAAE,CAC1CsI,KAAK,MAAA9B,QAAA,CAEJ3G,aAAa,eACZT,KAAA,QAAKiG,SAAS,CAAC,aAAa,CAAAmB,QAAA,eAC1BtH,IAAA,CAACP,KAAK,EACJqG,GAAG,CAAEnF,aAAa,CAACoF,QAAS,CAC5BC,GAAG,CAAErF,aAAa,CAACY,QAAS,CAC5B4E,SAAS,CAAC,wBAAwB,CAClCC,OAAO,MACR,CAAC,cACFlG,KAAA,QAAKiG,SAAS,CAAC,uBAAuB,CAAAmB,QAAA,eACpCpH,KAAA,MAAAoH,QAAA,EAAG,4BAAM,CAAC3G,aAAa,CAACY,QAAQ,EAAI,CAAC,cACrCrB,KAAA,MAAAoH,QAAA,EAAG,4BAAM,CAACvC,cAAc,CAACpE,aAAa,CAAC8F,QAAQ,CAAC,EAAI,CAAC,cACrDvG,KAAA,MAAAoH,QAAA,EAAG,4BAAM,CAAC7B,UAAU,CAAC9E,aAAa,CAACgG,WAAW,CAAC,EAAI,CAAC,EACjD,CAAC,EACH,CACN,CACK,CAAC,EACN,CAAC,CAEV,CAAC,CAED,cAAe,CAAAxG,mBAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
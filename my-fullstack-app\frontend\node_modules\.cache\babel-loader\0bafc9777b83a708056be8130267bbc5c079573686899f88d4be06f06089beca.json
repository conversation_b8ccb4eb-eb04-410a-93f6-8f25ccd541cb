{"ast": null, "code": "import { buildMatchFn } from \"../../_lib/buildMatchFn.js\";\nimport { buildMatchPatternFn } from \"../../_lib/buildMatchPatternFn.js\";\nconst matchOrdinalNumberPattern = /^(第\\s*)?\\d+(日|时|分|秒)?/i;\nconst parseOrdinalNumberPattern = /\\d+/i;\nconst matchEraPatterns = {\n  narrow: /^(前)/i,\n  abbreviated: /^(前)/i,\n  wide: /^(公元前|公元)/i\n};\nconst parseEraPatterns = {\n  any: [/^(前)/i, /^(公元)/i]\n};\nconst matchQuarterPatterns = {\n  narrow: /^[1234]/i,\n  abbreviated: /^第[一二三四]刻/i,\n  wide: /^第[一二三四]刻钟/i\n};\nconst parseQuarterPatterns = {\n  any: [/(1|一)/i, /(2|二)/i, /(3|三)/i, /(4|四)/i]\n};\nconst matchMonthPatterns = {\n  narrow: /^(一|二|三|四|五|六|七|八|九|十[二一])/i,\n  abbreviated: /^(一|二|三|四|五|六|七|八|九|十[二一]|\\d|1[12])月/i,\n  wide: /^(一|二|三|四|五|六|七|八|九|十[二一])月/i\n};\nconst parseMonthPatterns = {\n  narrow: [/^一/i, /^二/i, /^三/i, /^四/i, /^五/i, /^六/i, /^七/i, /^八/i, /^九/i, /^十(?!(一|二))/i, /^十一/i, /^十二/i],\n  any: [/^一|1/i, /^二|2/i, /^三|3/i, /^四|4/i, /^五|5/i, /^六|6/i, /^七|7/i, /^八|8/i, /^九|9/i, /^十(?!(一|二))|10/i, /^十一|11/i, /^十二|12/i]\n};\nconst matchDayPatterns = {\n  narrow: /^[一二三四五六日]/i,\n  short: /^[一二三四五六日]/i,\n  abbreviated: /^周[一二三四五六日]/i,\n  wide: /^星期[一二三四五六日]/i\n};\nconst parseDayPatterns = {\n  any: [/日/i, /一/i, /二/i, /三/i, /四/i, /五/i, /六/i]\n};\nconst matchDayPeriodPatterns = {\n  any: /^(上午?|下午?|午夜|[中正]午|早上?|下午|晚上?|凌晨|)/i\n};\nconst parseDayPeriodPatterns = {\n  any: {\n    am: /^上午?/i,\n    pm: /^下午?/i,\n    midnight: /^午夜/i,\n    noon: /^[中正]午/i,\n    morning: /^早上/i,\n    afternoon: /^下午/i,\n    evening: /^晚上?/i,\n    night: /^凌晨/i\n  }\n};\nexport const match = {\n  ordinalNumber: buildMatchPatternFn({\n    matchPattern: matchOrdinalNumberPattern,\n    parsePattern: parseOrdinalNumberPattern,\n    valueCallback: value => parseInt(value, 10)\n  }),\n  era: buildMatchFn({\n    matchPatterns: matchEraPatterns,\n    defaultMatchWidth: \"wide\",\n    parsePatterns: parseEraPatterns,\n    defaultParseWidth: \"any\"\n  }),\n  quarter: buildMatchFn({\n    matchPatterns: matchQuarterPatterns,\n    defaultMatchWidth: \"wide\",\n    parsePatterns: parseQuarterPatterns,\n    defaultParseWidth: \"any\",\n    valueCallback: index => index + 1\n  }),\n  month: buildMatchFn({\n    matchPatterns: matchMonthPatterns,\n    defaultMatchWidth: \"wide\",\n    parsePatterns: parseMonthPatterns,\n    defaultParseWidth: \"any\"\n  }),\n  day: buildMatchFn({\n    matchPatterns: matchDayPatterns,\n    defaultMatchWidth: \"wide\",\n    parsePatterns: parseDayPatterns,\n    defaultParseWidth: \"any\"\n  }),\n  dayPeriod: buildMatchFn({\n    matchPatterns: matchDayPeriodPatterns,\n    defaultMatchWidth: \"any\",\n    parsePatterns: parseDayPeriodPatterns,\n    defaultParseWidth: \"any\"\n  })\n};", "map": {"version": 3, "names": ["buildMatchFn", "buildMatchPatternFn", "matchOrdinalNumberPattern", "parseOrdinalNumberPattern", "matchEraPatterns", "narrow", "abbreviated", "wide", "parseEraPatterns", "any", "matchQuarterPatterns", "parseQuarterPatterns", "matchMonthPatterns", "parseMonthPatterns", "matchDayPatterns", "short", "parseDayPatterns", "matchDayPeriodPatterns", "parseDayPeriodPatterns", "am", "pm", "midnight", "noon", "morning", "afternoon", "evening", "night", "match", "ordinalNumber", "matchPattern", "parsePattern", "valueCallback", "value", "parseInt", "era", "matchPatterns", "defaultMatchWidth", "parsePatterns", "defaultParseWidth", "quarter", "index", "month", "day", "<PERSON><PERSON><PERSON><PERSON>"], "sources": ["C:/Users/<USER>/Desktop/demo-git/demo-react/my-fullstack-app/frontend/node_modules/date-fns/locale/zh-CN/_lib/match.js"], "sourcesContent": ["import { buildMatchFn } from \"../../_lib/buildMatchFn.js\";\nimport { buildMatchPatternFn } from \"../../_lib/buildMatchPatternFn.js\";\n\nconst matchOrdinalNumberPattern = /^(第\\s*)?\\d+(日|时|分|秒)?/i;\nconst parseOrdinalNumberPattern = /\\d+/i;\n\nconst matchEraPatterns = {\n  narrow: /^(前)/i,\n  abbreviated: /^(前)/i,\n  wide: /^(公元前|公元)/i,\n};\nconst parseEraPatterns = {\n  any: [/^(前)/i, /^(公元)/i],\n};\n\nconst matchQuarterPatterns = {\n  narrow: /^[1234]/i,\n  abbreviated: /^第[一二三四]刻/i,\n  wide: /^第[一二三四]刻钟/i,\n};\nconst parseQuarterPatterns = {\n  any: [/(1|一)/i, /(2|二)/i, /(3|三)/i, /(4|四)/i],\n};\n\nconst matchMonthPatterns = {\n  narrow: /^(一|二|三|四|五|六|七|八|九|十[二一])/i,\n  abbreviated: /^(一|二|三|四|五|六|七|八|九|十[二一]|\\d|1[12])月/i,\n  wide: /^(一|二|三|四|五|六|七|八|九|十[二一])月/i,\n};\nconst parseMonthPatterns = {\n  narrow: [\n    /^一/i,\n    /^二/i,\n    /^三/i,\n    /^四/i,\n    /^五/i,\n    /^六/i,\n    /^七/i,\n    /^八/i,\n    /^九/i,\n    /^十(?!(一|二))/i,\n    /^十一/i,\n    /^十二/i,\n  ],\n\n  any: [\n    /^一|1/i,\n    /^二|2/i,\n    /^三|3/i,\n    /^四|4/i,\n    /^五|5/i,\n    /^六|6/i,\n    /^七|7/i,\n    /^八|8/i,\n    /^九|9/i,\n    /^十(?!(一|二))|10/i,\n    /^十一|11/i,\n    /^十二|12/i,\n  ],\n};\n\nconst matchDayPatterns = {\n  narrow: /^[一二三四五六日]/i,\n  short: /^[一二三四五六日]/i,\n  abbreviated: /^周[一二三四五六日]/i,\n  wide: /^星期[一二三四五六日]/i,\n};\nconst parseDayPatterns = {\n  any: [/日/i, /一/i, /二/i, /三/i, /四/i, /五/i, /六/i],\n};\n\nconst matchDayPeriodPatterns = {\n  any: /^(上午?|下午?|午夜|[中正]午|早上?|下午|晚上?|凌晨|)/i,\n};\nconst parseDayPeriodPatterns = {\n  any: {\n    am: /^上午?/i,\n    pm: /^下午?/i,\n    midnight: /^午夜/i,\n    noon: /^[中正]午/i,\n    morning: /^早上/i,\n    afternoon: /^下午/i,\n    evening: /^晚上?/i,\n    night: /^凌晨/i,\n  },\n};\n\nexport const match = {\n  ordinalNumber: buildMatchPatternFn({\n    matchPattern: matchOrdinalNumberPattern,\n    parsePattern: parseOrdinalNumberPattern,\n    valueCallback: (value) => parseInt(value, 10),\n  }),\n\n  era: buildMatchFn({\n    matchPatterns: matchEraPatterns,\n    defaultMatchWidth: \"wide\",\n    parsePatterns: parseEraPatterns,\n    defaultParseWidth: \"any\",\n  }),\n\n  quarter: buildMatchFn({\n    matchPatterns: matchQuarterPatterns,\n    defaultMatchWidth: \"wide\",\n    parsePatterns: parseQuarterPatterns,\n    defaultParseWidth: \"any\",\n    valueCallback: (index) => index + 1,\n  }),\n\n  month: buildMatchFn({\n    matchPatterns: matchMonthPatterns,\n    defaultMatchWidth: \"wide\",\n    parsePatterns: parseMonthPatterns,\n    defaultParseWidth: \"any\",\n  }),\n\n  day: buildMatchFn({\n    matchPatterns: matchDayPatterns,\n    defaultMatchWidth: \"wide\",\n    parsePatterns: parseDayPatterns,\n    defaultParseWidth: \"any\",\n  }),\n\n  dayPeriod: buildMatchFn({\n    matchPatterns: matchDayPeriodPatterns,\n    defaultMatchWidth: \"any\",\n    parsePatterns: parseDayPeriodPatterns,\n    defaultParseWidth: \"any\",\n  }),\n};\n"], "mappings": "AAAA,SAASA,YAAY,QAAQ,4BAA4B;AACzD,SAASC,mBAAmB,QAAQ,mCAAmC;AAEvE,MAAMC,yBAAyB,GAAG,wBAAwB;AAC1D,MAAMC,yBAAyB,GAAG,MAAM;AAExC,MAAMC,gBAAgB,GAAG;EACvBC,MAAM,EAAE,OAAO;EACfC,WAAW,EAAE,OAAO;EACpBC,IAAI,EAAE;AACR,CAAC;AACD,MAAMC,gBAAgB,GAAG;EACvBC,GAAG,EAAE,CAAC,OAAO,EAAE,QAAQ;AACzB,CAAC;AAED,MAAMC,oBAAoB,GAAG;EAC3BL,MAAM,EAAE,UAAU;EAClBC,WAAW,EAAE,YAAY;EACzBC,IAAI,EAAE;AACR,CAAC;AACD,MAAMI,oBAAoB,GAAG;EAC3BF,GAAG,EAAE,CAAC,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ;AAC9C,CAAC;AAED,MAAMG,kBAAkB,GAAG;EACzBP,MAAM,EAAE,6BAA6B;EACrCC,WAAW,EAAE,uCAAuC;EACpDC,IAAI,EAAE;AACR,CAAC;AACD,MAAMM,kBAAkB,GAAG;EACzBR,MAAM,EAAE,CACN,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,cAAc,EACd,MAAM,EACN,MAAM,CACP;EAEDI,GAAG,EAAE,CACH,OAAO,EACP,OAAO,EACP,OAAO,EACP,OAAO,EACP,OAAO,EACP,OAAO,EACP,OAAO,EACP,OAAO,EACP,OAAO,EACP,iBAAiB,EACjB,SAAS,EACT,SAAS;AAEb,CAAC;AAED,MAAMK,gBAAgB,GAAG;EACvBT,MAAM,EAAE,aAAa;EACrBU,KAAK,EAAE,aAAa;EACpBT,WAAW,EAAE,cAAc;EAC3BC,IAAI,EAAE;AACR,CAAC;AACD,MAAMS,gBAAgB,GAAG;EACvBP,GAAG,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI;AAChD,CAAC;AAED,MAAMQ,sBAAsB,GAAG;EAC7BR,GAAG,EAAE;AACP,CAAC;AACD,MAAMS,sBAAsB,GAAG;EAC7BT,GAAG,EAAE;IACHU,EAAE,EAAE,OAAO;IACXC,EAAE,EAAE,OAAO;IACXC,QAAQ,EAAE,MAAM;IAChBC,IAAI,EAAE,SAAS;IACfC,OAAO,EAAE,MAAM;IACfC,SAAS,EAAE,MAAM;IACjBC,OAAO,EAAE,OAAO;IAChBC,KAAK,EAAE;EACT;AACF,CAAC;AAED,OAAO,MAAMC,KAAK,GAAG;EACnBC,aAAa,EAAE3B,mBAAmB,CAAC;IACjC4B,YAAY,EAAE3B,yBAAyB;IACvC4B,YAAY,EAAE3B,yBAAyB;IACvC4B,aAAa,EAAGC,KAAK,IAAKC,QAAQ,CAACD,KAAK,EAAE,EAAE;EAC9C,CAAC,CAAC;EAEFE,GAAG,EAAElC,YAAY,CAAC;IAChBmC,aAAa,EAAE/B,gBAAgB;IAC/BgC,iBAAiB,EAAE,MAAM;IACzBC,aAAa,EAAE7B,gBAAgB;IAC/B8B,iBAAiB,EAAE;EACrB,CAAC,CAAC;EAEFC,OAAO,EAAEvC,YAAY,CAAC;IACpBmC,aAAa,EAAEzB,oBAAoB;IACnC0B,iBAAiB,EAAE,MAAM;IACzBC,aAAa,EAAE1B,oBAAoB;IACnC2B,iBAAiB,EAAE,KAAK;IACxBP,aAAa,EAAGS,KAAK,IAAKA,KAAK,GAAG;EACpC,CAAC,CAAC;EAEFC,KAAK,EAAEzC,YAAY,CAAC;IAClBmC,aAAa,EAAEvB,kBAAkB;IACjCwB,iBAAiB,EAAE,MAAM;IACzBC,aAAa,EAAExB,kBAAkB;IACjCyB,iBAAiB,EAAE;EACrB,CAAC,CAAC;EAEFI,GAAG,EAAE1C,YAAY,CAAC;IAChBmC,aAAa,EAAErB,gBAAgB;IAC/BsB,iBAAiB,EAAE,MAAM;IACzBC,aAAa,EAAErB,gBAAgB;IAC/BsB,iBAAiB,EAAE;EACrB,CAAC,CAAC;EAEFK,SAAS,EAAE3C,YAAY,CAAC;IACtBmC,aAAa,EAAElB,sBAAsB;IACrCmB,iBAAiB,EAAE,KAAK;IACxBC,aAAa,EAAEnB,sBAAsB;IACrCoB,iBAAiB,EAAE;EACrB,CAAC;AACH,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
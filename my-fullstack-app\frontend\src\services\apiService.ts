/**
 * Typed API service layer
 */
import { apiClient } from './api';
import {
  MenuGroupItem,
  DataTypeGroupItem,
  Patient,
  Treatment,
  Receipt,
  UserRole,
  PatientSearchParams,
  TreatmentSearchParams,
  ReceiptSearchParams,
  LoginResponse,
} from '../types/api';

/**
 * System API endpoints
 */
export class SystemApi {
  /**
   * Get menu items for authenticated user
   */
  static async getMenus(): Promise<MenuGroupItem[]> {
    return apiClient.get<MenuGroupItem[]>('/api/system/GetMenus');
  }

  /**
   * Get data types
   */
  static async getDataTypes(): Promise<DataTypeGroupItem[]> {
    return apiClient.get<DataTypeGroupItem[]>('/api/system/GetDataType');
  }
}

/**
 * Patient API endpoints
 */
export class PatientApi {
  /**
   * Get patients list with optional filters
   */
  static async getList(params: PatientSearchParams = {}): Promise<Patient[]> {
    return apiClient.get<Patient[]>('/api/patients/GetList', { params });
  }

  /**
   * Get patient by ID
   */
  static async getById(id: number): Promise<Patient> {
    return apiClient.get<Patient>(`/api/patients/${id}`);
  }

  /**
   * Create new patient
   */
  static async create(patient: Omit<Patient, 'Id' | 'CreatedAt' | 'UpdatedAt'>): Promise<Patient> {
    return apiClient.post<Patient>('/api/patients', patient);
  }

  /**
   * Update patient
   */
  static async update(id: number, patient: Partial<Patient>): Promise<Patient> {
    return apiClient.put<Patient>(`/api/patients/${id}`, patient);
  }

  /**
   * Delete patient
   */
  static async delete(id: number): Promise<void> {
    return apiClient.delete(`/api/patients/${id}`);
  }
}

/**
 * Treatment API endpoints
 */
export class TreatmentApi {
  /**
   * Get treatments list with optional filters
   */
  static async getList(params: TreatmentSearchParams = {}): Promise<Treatment[]> {
    return apiClient.get<Treatment[]>('/api/treatment/GetList', { params });
  }

  /**
   * Get treatment by ID
   */
  static async getById(id: number): Promise<Treatment> {
    return apiClient.get<Treatment>(`/api/treatment/${id}`);
  }

  /**
   * Create new treatment
   */
  static async create(treatment: Omit<Treatment, 'Id' | 'CreatedAt' | 'UpdatedAt'>): Promise<Treatment> {
    return apiClient.post<Treatment>('/api/treatment', treatment);
  }

  /**
   * Update treatment
   */
  static async update(id: number, treatment: Partial<Treatment>): Promise<Treatment> {
    return apiClient.put<Treatment>(`/api/treatment/${id}`, treatment);
  }

  /**
   * Delete treatment
   */
  static async delete(id: number): Promise<void> {
    return apiClient.delete(`/api/treatment/${id}`);
  }
}

/**
 * Receipt API endpoints
 */
export class ReceiptApi {
  /**
   * Get receipts list with optional filters
   */
  static async getList(params: ReceiptSearchParams = {}): Promise<Receipt[]> {
    return apiClient.get<Receipt[]>('/api/receipt/GetList', { params });
  }

  /**
   * Get receipt by ID
   */
  static async getById(id: number): Promise<Receipt> {
    return apiClient.get<Receipt>(`/api/receipt/${id}`);
  }

  /**
   * Create new receipt
   */
  static async create(receipt: Omit<Receipt, 'Id' | 'CreatedAt' | 'UpdatedAt'>): Promise<Receipt> {
    return apiClient.post<Receipt>('/api/receipt', receipt);
  }

  /**
   * Update receipt
   */
  static async update(id: number, receipt: Partial<Receipt>): Promise<Receipt> {
    return apiClient.put<Receipt>(`/api/receipt/${id}`, receipt);
  }

  /**
   * Delete receipt
   */
  static async delete(id: number): Promise<void> {
    return apiClient.delete(`/api/receipt/${id}`);
  }
}

/**
 * Doctor/User API endpoints
 */
export class DoctorApi {
  /**
   * Get doctors list
   */
  static async getList(params: { UserName?: string; RoleId?: number } = {}): Promise<UserRole[]> {
    return apiClient.get<UserRole[]>('/api/users/GetList', { params });
  }

  /**
   * Get doctor by ID
   */
  static async getById(id: number): Promise<UserRole> {
    return apiClient.get<UserRole>(`/api/users/${id}`);
  }
}

/**
 * Authentication API endpoints
 */
export class AuthApi {
  /**
   * Login user
   */
  static async login(credentials: { username: string; password: string }): Promise<LoginResponse> {
    return apiClient.post<LoginResponse>('/api/auth/login', credentials);
  }

  /**
   * Logout user
   */
  static async logout(userid : number): Promise<void> {
    return apiClient.post('/api/auth/logout', userid);
  }

  /**
   * Refresh token
   */
  static async refreshToken(): Promise<{ token: string }> {
    return apiClient.post<{ token: string }>('/api/auth/refresh');
  }
}

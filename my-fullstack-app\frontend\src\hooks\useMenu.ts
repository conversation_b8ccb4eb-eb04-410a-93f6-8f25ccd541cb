import { useCallback, useEffect, useState } from "react";
import { SystemApi } from '../services/apiService';
import { MenuGroupItem } from '../types/api';
import { useErrorHandler } from './useErrorHandler';

interface UseMenuReturn {
  menu: MenuGroupItem[];
  loading: boolean;
  error: string | null;
  refetch: () => Promise<void>;
}

export default function useMenu(isAuth: boolean): UseMenuReturn {
  const [menu, setMenu] = useState<MenuGroupItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const { handleError } = useErrorHandler();

  const fetchMenu = useCallback(async () => {
    if (!isAuth) {
      setLoading(false);
      return;
    }

    try {
      setLoading(true);
      setError(null);
      const menuData = await SystemApi.getMenus();
      setMenu(menuData);
    } catch (err) {
      const errorMessage = handleError(err);
      setError(errorMessage);
      console.error("Menu API Error:", err);
    } finally {
      setLoading(false);
    }
  }, [isAuth, handleError]);

  useEffect(() => {
    fetchMenu();
  }, [fetchMenu]);

  return {
    menu,
    loading,
    error,
    refetch: fetchMenu
  };
}
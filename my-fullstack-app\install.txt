# 遠端機
sudo apt update
sudo apt install -y ca-certificates curl gnupg lsb-release

# 安裝docker、docker-compose
# 加入 Docker 官方 GPG 金鑰
sudo mkdir -p /etc/apt/keyrings
curl -fsSL https://download.docker.com/linux/ubuntu/gpg | \
  sudo gpg --dearmor -o /etc/apt/keyrings/docker.gpg

# 新增 Docker 的 APT 軟體來源
echo \
  "deb [arch=$(dpkg --print-architecture) \
  signed-by=/etc/apt/keyrings/docker.gpg] \
  https://download.docker.com/linux/ubuntu \
  $(lsb_release -cs) stable" | \
  sudo tee /etc/apt/sources.list.d/docker.list > /dev/null

# 更新並安裝 Docker CLI 和 Engine
sudo apt update
sudo apt install -y docker-ce docker-ce-cli containerd.io docker-buildx-plugin docker-compose-plugin

# 啟用並啟動 Docker 服務
sudo systemctl enable docker
sudo systemctl start docker

# 測試
docker --version
docker compose version

# 安裝 rclone
curl https://rclone.org/install.sh | sudo bash
# 測試
rclone version

# 安裝 unzip
sudo apt install -y unzip
# 測試
unzip -v

# 安裝 dos2unix
sudo apt-get install dos2unix

# 設定新時區
sudo timedatectl set-timezone Asia/Taipei
# 測試
timedatectl

mkdir -p /app
cd /app

#-------------------------------------------------------------

# 本機
# 撤銷登入憑證
ssh-keygen -R **************

scp docker-compose.yml .env root@**************:/app
scp -r opt/mysql-backup root@**************:/home/<USER>/
# 需要兩次，第一次導向域名拿認證nbhphysical.idv.tw.conf，第二次完整簽證設置
	nginx\conf.d\nbhphysical.idv.tw_temp.conf，去掉 _temp ，傳送

scp -r nginx root@**************:/app


#-------------------------------------------------------------

# 遠端機
# docker 登入
docker login

docker compose pull

# rclone註冊服務 dropbox
rclone config

#-------------------------------------------------------------

# 本機
# rclone.exe 資料夾下，啟動 cmd
rclone authorize "dropbox"

#-------------------------------------------------------------

# 遠端機
config_token>  {"access_token":"xxxx","token_type":"bearer","refresh_token":"xxxxx","expiry":"2025-07-14T19:48:02.0576633+08:00","expires_in":14400}

#轉換腳本格式
dos2unix /home/<USER>/mysql-backup/daily-incremental-backup.sh
dos2unix /home/<USER>/mysql-backup/weekly-full-backup.sh
dos2unix /home/<USER>/mysql-backup/restore-mysql.sh
dos2unix /home/<USER>/mysql-backup/backup-uploads.sh

#測試腳本
sudo bash /home/<USER>/mysql-backup/weekly-full-backup.sh
sudo bash /home/<USER>/mysql-backup/daily-incremental-backup.sh
sudo bash /home/<USER>/mysql-backup/backup-uploads.sh

# 啟動服務
docker compose up -d

# 申請完成域名，例如nbhphysical.idv.tw、www.nbhphysical.idv.tw

#-------------------------------------------------------------

# 本機
# 第一次導向域名拿認證，
# nginx\conf.d\nbhphysical.idv.tw_temp.conf，去掉 _temp ，傳送，_temp加回去
scp -r nginx root@**************:/app


#-------------------------------------------------------------

# 遠端機
# Https 手動申請 Let's Encrypt 憑證（初次）
docker compose exec certbot certbot certonly --webroot -w /var/lib/letsencrypt -d nbhphysical.idv.tw -d www.nbhphysical.idv.tw --email <EMAIL> --agree-tos --no-eff-email --force-renewal

#-------------------------------------------------------------

# 本機
# 第二次啟用反代理設置，
# nginx\conf.d\nbhphysical.idv.tw.conf，傳送
scp -r nginx root@**************:/app


#-------------------------------------------------------------

# 遠端機
# 重啟 nginx
docker compose restart nginx

# 憑證自動續訂
crontab -e
# 每週檢查一次，憑證自動續訂
0 0 */7 * * docker-compose -f /path/to/your/docker-compose.yml exec certbot certbot renew --nginx --nginx-server-root /etc/letsencrypt --post-hook "docker-compose -f /path/to/your/docker-compose.yml restart nginx" && docker-compose -f /path/to/your/docker-compose.yml restart nginx


# 備份排程
# Crontab 排程
crontab -e
# 每天凌晨 1:00 執行圖片備份
0 1 * * * bash /root/mysql-backup/backup-uploads.sh >> /var/log/backup-uploads.log 2>&1
# 每天凌晨 2:00 執行增量備份
0 2 * * * bash /root/mysql-backup/daily-incremental-backup.sh >> /var/log/mysql-backup/backup.log 2>&1
# 每週日凌晨 3:00 執行完整備份
0 3 * * 0 bash /root/mysql-backup/weekly-full-backup.sh >> /var/log/mysql-backup/backup.log 2>&1
# 測試
0 19 * * * bash /root/mysql-backup/daily-incremental-backup.sh >> /var/log/mysql-backup/backup.log 2>&1

# 權限與執行權限
chmod +x /home/<USER>/mysql-backup/*.sh

# 還原
bash /home/<USER>/mysql-backup/restore-mysql.sh 2025-07-08

# 查看 Crontab 排程 執行紀錄 
tail -f /var/log/backup-uploads.log


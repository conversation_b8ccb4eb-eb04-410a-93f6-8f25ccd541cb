{"ast": null, "code": "import { isSameWeek } from \"../../../isSameWeek.js\";\nconst accusativeWeekdays = [\"жексенбіде\", \"дүйсенбіде\", \"сейсенбіде\", \"сәрсенбіде\", \"бейсенбіде\", \"жұмада\", \"сенбіде\"];\nfunction lastWeek(day) {\n  const weekday = accusativeWeekdays[day];\n  return \"'өткен \" + weekday + \" сағат' p'-де'\";\n}\nfunction thisWeek(day) {\n  const weekday = accusativeWeekdays[day];\n  return \"'\" + weekday + \" сағат' p'-де'\";\n}\nfunction nextWeek(day) {\n  const weekday = accusativeWeekdays[day];\n  return \"'келесі \" + weekday + \" сағат' p'-де'\";\n}\nconst formatRelativeLocale = {\n  lastWeek: (date, baseDate, options) => {\n    const day = date.getDay();\n    if (isSameWeek(date, baseDate, options)) {\n      return thisWeek(day);\n    } else {\n      return lastWeek(day);\n    }\n  },\n  yesterday: \"'кеше сағат' p'-де'\",\n  today: \"'бүгін сағат' p'-де'\",\n  tomorrow: \"'ертең сағат' p'-де'\",\n  nextWeek: (date, baseDate, options) => {\n    const day = date.getDay();\n    if (isSameWeek(date, baseDate, options)) {\n      return thisWeek(day);\n    } else {\n      return nextWeek(day);\n    }\n  },\n  other: \"P\"\n};\nexport const formatRelative = (token, date, baseDate, options) => {\n  const format = formatRelativeLocale[token];\n  if (typeof format === \"function\") {\n    return format(date, baseDate, options);\n  }\n  return format;\n};", "map": {"version": 3, "names": ["isSameWeek", "accusativeWeekdays", "lastWeek", "day", "weekday", "thisWeek", "nextWeek", "formatRelativeLocale", "date", "baseDate", "options", "getDay", "yesterday", "today", "tomorrow", "other", "formatRelative", "token", "format"], "sources": ["C:/Users/<USER>/Desktop/demo-git/demo-react/my-fullstack-app/frontend/node_modules/date-fns/locale/kk/_lib/formatRelative.js"], "sourcesContent": ["import { isSameWeek } from \"../../../isSameWeek.js\";\n\nconst accusativeWeekdays = [\n  \"жексенбіде\",\n  \"дүйсенбіде\",\n  \"сейсенбіде\",\n  \"сәрсенбіде\",\n  \"бейсенбіде\",\n  \"жұмада\",\n  \"сенбіде\",\n];\n\nfunction lastWeek(day) {\n  const weekday = accusativeWeekdays[day];\n\n  return \"'өткен \" + weekday + \" сағат' p'-де'\";\n}\n\nfunction thisWeek(day) {\n  const weekday = accusativeWeekdays[day];\n\n  return \"'\" + weekday + \" сағат' p'-де'\";\n}\n\nfunction nextWeek(day) {\n  const weekday = accusativeWeekdays[day];\n\n  return \"'келесі \" + weekday + \" сағат' p'-де'\";\n}\n\nconst formatRelativeLocale = {\n  lastWeek: (date, baseDate, options) => {\n    const day = date.getDay();\n    if (isSameWeek(date, baseDate, options)) {\n      return thisWeek(day);\n    } else {\n      return lastWeek(day);\n    }\n  },\n  yesterday: \"'кеше сағат' p'-де'\",\n  today: \"'бүгін сағат' p'-де'\",\n  tomorrow: \"'ертең сағат' p'-де'\",\n  nextWeek: (date, baseDate, options) => {\n    const day = date.getDay();\n    if (isSameWeek(date, baseDate, options)) {\n      return thisWeek(day);\n    } else {\n      return nextWeek(day);\n    }\n  },\n  other: \"P\",\n};\n\nexport const formatRelative = (token, date, baseDate, options) => {\n  const format = formatRelativeLocale[token];\n\n  if (typeof format === \"function\") {\n    return format(date, baseDate, options);\n  }\n\n  return format;\n};\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,wBAAwB;AAEnD,MAAMC,kBAAkB,GAAG,CACzB,YAAY,EACZ,YAAY,EACZ,YAAY,EACZ,YAAY,EACZ,YAAY,EACZ,QAAQ,EACR,SAAS,CACV;AAED,SAASC,QAAQA,CAACC,GAAG,EAAE;EACrB,MAAMC,OAAO,GAAGH,kBAAkB,CAACE,GAAG,CAAC;EAEvC,OAAO,SAAS,GAAGC,OAAO,GAAG,gBAAgB;AAC/C;AAEA,SAASC,QAAQA,CAACF,GAAG,EAAE;EACrB,MAAMC,OAAO,GAAGH,kBAAkB,CAACE,GAAG,CAAC;EAEvC,OAAO,GAAG,GAAGC,OAAO,GAAG,gBAAgB;AACzC;AAEA,SAASE,QAAQA,CAACH,GAAG,EAAE;EACrB,MAAMC,OAAO,GAAGH,kBAAkB,CAACE,GAAG,CAAC;EAEvC,OAAO,UAAU,GAAGC,OAAO,GAAG,gBAAgB;AAChD;AAEA,MAAMG,oBAAoB,GAAG;EAC3BL,QAAQ,EAAEA,CAACM,IAAI,EAAEC,QAAQ,EAAEC,OAAO,KAAK;IACrC,MAAMP,GAAG,GAAGK,IAAI,CAACG,MAAM,CAAC,CAAC;IACzB,IAAIX,UAAU,CAACQ,IAAI,EAAEC,QAAQ,EAAEC,OAAO,CAAC,EAAE;MACvC,OAAOL,QAAQ,CAACF,GAAG,CAAC;IACtB,CAAC,MAAM;MACL,OAAOD,QAAQ,CAACC,GAAG,CAAC;IACtB;EACF,CAAC;EACDS,SAAS,EAAE,qBAAqB;EAChCC,KAAK,EAAE,sBAAsB;EAC7BC,QAAQ,EAAE,sBAAsB;EAChCR,QAAQ,EAAEA,CAACE,IAAI,EAAEC,QAAQ,EAAEC,OAAO,KAAK;IACrC,MAAMP,GAAG,GAAGK,IAAI,CAACG,MAAM,CAAC,CAAC;IACzB,IAAIX,UAAU,CAACQ,IAAI,EAAEC,QAAQ,EAAEC,OAAO,CAAC,EAAE;MACvC,OAAOL,QAAQ,CAACF,GAAG,CAAC;IACtB,CAAC,MAAM;MACL,OAAOG,QAAQ,CAACH,GAAG,CAAC;IACtB;EACF,CAAC;EACDY,KAAK,EAAE;AACT,CAAC;AAED,OAAO,MAAMC,cAAc,GAAGA,CAACC,KAAK,EAAET,IAAI,EAAEC,QAAQ,EAAEC,OAAO,KAAK;EAChE,MAAMQ,MAAM,GAAGX,oBAAoB,CAACU,KAAK,CAAC;EAE1C,IAAI,OAAOC,MAAM,KAAK,UAAU,EAAE;IAChC,OAAOA,MAAM,CAACV,IAAI,EAAEC,QAAQ,EAAEC,OAAO,CAAC;EACxC;EAEA,OAAOQ,MAAM;AACf,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
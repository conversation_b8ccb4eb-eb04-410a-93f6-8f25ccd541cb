{"ast": null, "code": "import { formatDistance } from \"./bn/_lib/formatDistance.js\";\nimport { formatLong } from \"./bn/_lib/formatLong.js\";\nimport { formatRelative } from \"./bn/_lib/formatRelative.js\";\nimport { localize } from \"./bn/_lib/localize.js\";\nimport { match } from \"./bn/_lib/match.js\";\n\n/**\n * @category Locales\n * @summary Bengali locale.\n * @language Bengali\n * @iso-639-2 ben\n * <AUTHOR> [@touhid<PERSON><PERSON>](https://github.com/touhidrahman)\n * <AUTHOR> [@nutboltu](https://github.com/nutboltu)\n */\nexport const bn = {\n  code: \"bn\",\n  formatDistance: formatDistance,\n  formatLong: formatLong,\n  formatRelative: formatRelative,\n  localize: localize,\n  match: match,\n  options: {\n    weekStartsOn: 0 /* Sunday */,\n    firstWeekContainsDate: 1\n  }\n};\n\n// Fallback for modularized imports:\nexport default bn;", "map": {"version": 3, "names": ["formatDistance", "formatLong", "formatRelative", "localize", "match", "bn", "code", "options", "weekStartsOn", "firstWeekContainsDate"], "sources": ["C:/Users/<USER>/Desktop/demo-git/demo-react/my-fullstack-app/frontend/node_modules/date-fns/locale/bn.js"], "sourcesContent": ["import { formatDistance } from \"./bn/_lib/formatDistance.js\";\nimport { formatLong } from \"./bn/_lib/formatLong.js\";\nimport { formatRelative } from \"./bn/_lib/formatRelative.js\";\nimport { localize } from \"./bn/_lib/localize.js\";\nimport { match } from \"./bn/_lib/match.js\";\n\n/**\n * @category Locales\n * @summary Bengali locale.\n * @language Bengali\n * @iso-639-2 ben\n * <AUTHOR> [@touhid<PERSON><PERSON>](https://github.com/touhidrahman)\n * <AUTHOR> [@nutboltu](https://github.com/nutboltu)\n */\nexport const bn = {\n  code: \"bn\",\n  formatDistance: formatDistance,\n  formatLong: formatLong,\n  formatRelative: formatRelative,\n  localize: localize,\n  match: match,\n  options: {\n    weekStartsOn: 0 /* Sunday */,\n    firstWeekContainsDate: 1,\n  },\n};\n\n// Fallback for modularized imports:\nexport default bn;\n"], "mappings": "AAAA,SAASA,cAAc,QAAQ,6BAA6B;AAC5D,SAASC,UAAU,QAAQ,yBAAyB;AACpD,SAASC,cAAc,QAAQ,6BAA6B;AAC5D,SAASC,QAAQ,QAAQ,uBAAuB;AAChD,SAASC,KAAK,QAAQ,oBAAoB;;AAE1C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMC,EAAE,GAAG;EAChBC,IAAI,EAAE,IAAI;EACVN,cAAc,EAAEA,cAAc;EAC9BC,UAAU,EAAEA,UAAU;EACtBC,cAAc,EAAEA,cAAc;EAC9BC,QAAQ,EAAEA,QAAQ;EAClBC,KAAK,EAAEA,KAAK;EACZG,OAAO,EAAE;IACPC,YAAY,EAAE,CAAC,CAAC;IAChBC,qBAAqB,EAAE;EACzB;AACF,CAAC;;AAED;AACA,eAAeJ,EAAE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
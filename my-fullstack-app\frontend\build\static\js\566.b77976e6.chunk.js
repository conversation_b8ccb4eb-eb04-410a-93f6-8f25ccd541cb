"use strict";(self.webpackChunkfrontend=self.webpackChunkfrontend||[]).push([[566],{1104:(e,t,n)=>{n.d(t,{T:()=>C,Z:()=>S});var o=n(5043),r=n(4052),s=n(2018),i=n(1828),c=n(5797),a=n(2028),l=n(9988),h=n(8794),u=n(4504);function g(){return g=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var o in n)({}).hasOwnProperty.call(n,o)&&(e[o]=n[o])}return e},g.apply(null,arguments)}function d(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,o=Array(t);n<t;n++)o[n]=e[n];return o}function p(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var o,r,s,i,c=[],a=!0,l=!1;try{if(s=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;a=!1}else for(;!(a=(o=s.call(n)).done)&&(c.push(o.value),c.length!==t);a=!0);}catch(e){l=!0,r=e}finally{try{if(!a&&null!=n.return&&(i=n.return(),Object(i)!==i))return}finally{if(l)throw r}}return c}}(e,t)||function(e,t){if(e){if("string"==typeof e)return d(e,t);var n={}.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?d(e,t):void 0}}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function _(e){return _="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},_(e)}function f(e){var t=function(e,t){if("object"!=_(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var o=n.call(e,t||"default");if("object"!=_(o))return o;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==_(t)?t:t+""}function m(e,t,n){return(t=f(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var v={root:"p-confirm-dialog",message:"p-confirm-dialog-message",icon:"p-confirm-dialog-icon",acceptButton:"p-confirm-dialog-accept",rejectButton:function(e){var t=e.getPropValue;return(0,u.xW)("p-confirm-dialog-reject",{"p-button-text":!t("rejectClassName")})}},w=i.x.extend({defaultProps:{__TYPE:"ConfirmDialog",accept:null,acceptClassName:null,acceptIcon:null,acceptLabel:null,appendTo:null,breakpoints:null,children:void 0,className:null,content:null,defaultFocus:"accept",footer:null,icon:null,message:null,onHide:null,reject:null,rejectClassName:null,rejectIcon:null,rejectLabel:null,tagKey:void 0,visible:void 0},css:{classes:v}});function b(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);t&&(o=o.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,o)}return n}function y(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?b(Object(n),!0).forEach((function(t){m(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):b(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var S=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};(e=y(y({},e),{visible:void 0===e.visible||e.visible})).visible&&l.s.emit("confirm-dialog",e);return{show:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};l.s.emit("confirm-dialog",y(y(y({},e),t),{visible:!0}))},hide:function(){l.s.emit("confirm-dialog",{visible:!1})}}},C=o.memo(o.forwardRef((function(e,t){var n=(0,a.qV)(),d=o.useContext(r.UM),_=w.getProps(e,d),f=p(o.useState(_.visible),2),m=f[0],v=f[1],b=p(o.useState(!1),2),S=b[0],C=b[1],k=o.useRef(null),E=o.useRef(!1),I=o.useRef(null),T=function(){var e=_.group;return k.current&&(e=k.current.group),Object.assign({},_,k.current,{group:e})},P=function(e){return T()[e]},R=function(e){for(var t=arguments.length,n=new Array(t>1?t-1:0),o=1;o<t;o++)n[o-1]=arguments[o];return u.BF.getPropValue(P(e),n)},D=P("acceptLabel")||(0,r.WP)("accept"),j=P("rejectLabel")||(0,r.WP)("reject"),A={props:_,state:{visible:m}},x=w.setMetaData(A),M=x.ptm,N=x.cx,q=x.isUnstyled;(0,i.j)(w.css.styles,q,{name:"confirmdialog"});var H=function(){E.current||(E.current=!0,R("accept"),O("accept"))},W=function(){E.current||(E.current=!0,R("reject"),O("reject"))},B=function(){T().group===_.group&&(v(!0),E.current=!1,I.current=document.activeElement)},O=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"cancel";m&&("string"!==typeof e&&(e="cancel"),v(!1),R("onHide",e),u.DV.focus(I.current),I.current=null)},L=function(e){if(e.tagKey===_.tagKey){var t=m!==e.visible;P("target")!==e.target&&!_.target?(O(),k.current=e,C(!0)):t&&(k.current=e,e.visible?B():O())}};o.useEffect((function(){_.visible?B():O()}),[_.visible]),o.useEffect((function(){return _.target||_.message||l.s.on("confirm-dialog",L),function(){l.s.off("confirm-dialog",L)}}),[_.target]),(0,a.w5)((function(){S&&B()}),[S]),(0,a.l0)((function(){l.s.off("confirm-dialog",L)})),o.useImperativeHandle(t,(function(){return{props:_,confirm:L}}));var F=function(){var t=T(),r=u.BF.getJSXElement(P("message"),t),i=n({className:N("icon")},M("icon")),a=u.Hj.getJSXIcon(P("icon"),y({},i),{props:t}),l=function(){var e=P("defaultFocus"),t=(0,u.xW)("p-confirm-dialog-accept",P("acceptClassName")),r=(0,u.xW)("p-confirm-dialog-reject",{"p-button-text":!P("rejectClassName")},P("rejectClassName")),i=n({label:j,autoFocus:"reject"===e,icon:P("rejectIcon"),className:(0,u.xW)(P("rejectClassName"),N("rejectButton",{getPropValue:P})),onClick:W,pt:M("rejectButton"),unstyled:_.unstyled,__parentMetadata:{parent:A}},M("rejectButton")),c=n({label:D,autoFocus:void 0===e||"accept"===e,icon:P("acceptIcon"),className:(0,u.xW)(P("acceptClassName"),N("acceptButton")),onClick:H,pt:M("acceptButton"),unstyled:_.unstyled,__parentMetadata:{parent:A}},M("acceptButton")),a=o.createElement(o.Fragment,null,o.createElement(s.$,i),o.createElement(s.$,c));if(P("footer")){var l={accept:H,reject:W,acceptClassName:t,rejectClassName:r,acceptLabel:D,rejectLabel:j,element:a,props:T()};return u.BF.getJSXElement(P("footer"),l)}return a}(),h=n({className:N("message")},M("message")),d=n({visible:m,className:(0,u.xW)(P("className"),N("root")),footer:l,onHide:O,breakpoints:P("breakpoints"),pt:t.pt,unstyled:_.unstyled,appendTo:P("appendTo"),__parentMetadata:{parent:A}},w.getOtherProps(t));return o.createElement(c.l,g({},d,{content:null===e||void 0===e?void 0:e.content}),a,o.createElement("span",h,r))}();return o.createElement(h.Z,{element:F,appendTo:P("appendTo")})})));C.displayName="ConfirmDialog"},7127:(e,t,n)=>{n.d(t,{$:()=>ne});var o=n(9379);const r=[0,2e3,1e4,3e4,null];class s{constructor(e){this._retryDelays=void 0!==e?[...e,null]:r}nextRetryDelayInMilliseconds(e){return this._retryDelays[e.previousRetryCount]}}class i{}i.Authorization="Authorization",i.Cookie="Cookie";class c{constructor(e,t,n){this.statusCode=e,this.statusText=t,this.content=n}}class a{get(e,t){return this.send((0,o.A)((0,o.A)({},t),{},{method:"GET",url:e}))}post(e,t){return this.send((0,o.A)((0,o.A)({},t),{},{method:"POST",url:e}))}delete(e,t){return this.send((0,o.A)((0,o.A)({},t),{},{method:"DELETE",url:e}))}getCookieString(e){return""}}class l extends a{constructor(e,t){super(),this._innerClient=e,this._accessTokenFactory=t}async send(e){let t=!0;this._accessTokenFactory&&(!this._accessToken||e.url&&e.url.indexOf("/negotiate?")>0)&&(t=!1,this._accessToken=await this._accessTokenFactory()),this._setAuthorizationHeader(e);const n=await this._innerClient.send(e);return t&&401===n.statusCode&&this._accessTokenFactory?(this._accessToken=await this._accessTokenFactory(),this._setAuthorizationHeader(e),await this._innerClient.send(e)):n}_setAuthorizationHeader(e){e.headers||(e.headers={}),this._accessToken?e.headers[i.Authorization]="Bearer ".concat(this._accessToken):this._accessTokenFactory&&e.headers[i.Authorization]&&delete e.headers[i.Authorization]}getCookieString(e){return this._innerClient.getCookieString(e)}}class h extends Error{constructor(e,t){const n=new.target.prototype;super("".concat(e,": Status code '").concat(t,"'")),this.statusCode=t,this.__proto__=n}}class u extends Error{constructor(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"A timeout occurred.";const t=new.target.prototype;super(e),this.__proto__=t}}class g extends Error{constructor(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"An abort occurred.";const t=new.target.prototype;super(e),this.__proto__=t}}class d extends Error{constructor(e,t){const n=new.target.prototype;super(e),this.transport=t,this.errorType="UnsupportedTransportError",this.__proto__=n}}class p extends Error{constructor(e,t){const n=new.target.prototype;super(e),this.transport=t,this.errorType="DisabledTransportError",this.__proto__=n}}class _ extends Error{constructor(e,t){const n=new.target.prototype;super(e),this.transport=t,this.errorType="FailedToStartTransportError",this.__proto__=n}}class f extends Error{constructor(e){const t=new.target.prototype;super(e),this.errorType="FailedToNegotiateWithServerError",this.__proto__=t}}class m extends Error{constructor(e,t){const n=new.target.prototype;super(e),this.innerErrors=t,this.__proto__=n}}var v;!function(e){e[e.Trace=0]="Trace",e[e.Debug=1]="Debug",e[e.Information=2]="Information",e[e.Warning=3]="Warning",e[e.Error=4]="Error",e[e.Critical=5]="Critical",e[e.None=6]="None"}(v||(v={}));class w{constructor(){}log(e,t){}}w.instance=new w;class b{static isRequired(e,t){if(null===e||void 0===e)throw new Error("The '".concat(t,"' argument is required."))}static isNotEmpty(e,t){if(!e||e.match(/^\s*$/))throw new Error("The '".concat(t,"' argument should not be empty."))}static isIn(e,t,n){if(!(e in t))throw new Error("Unknown ".concat(n," value: ").concat(e,"."))}}class y{static get isBrowser(){return!y.isNode&&"object"===typeof window&&"object"===typeof window.document}static get isWebWorker(){return!y.isNode&&"object"===typeof self&&"importScripts"in self}static get isReactNative(){return!y.isNode&&"object"===typeof window&&"undefined"===typeof window.document}static get isNode(){return"undefined"!==typeof process&&process.release&&"node"===process.release.name}}function S(e,t){let n="";return C(e)?(n="Binary data of length ".concat(e.byteLength),t&&(n+=". Content: '".concat(function(e){const t=new Uint8Array(e);let n="";return t.forEach((e=>{n+="0x".concat(e<16?"0":"").concat(e.toString(16)," ")})),n.substr(0,n.length-1)}(e),"'"))):"string"===typeof e&&(n="String data of length ".concat(e.length),t&&(n+=". Content: '".concat(e,"'"))),n}function C(e){return e&&"undefined"!==typeof ArrayBuffer&&(e instanceof ArrayBuffer||e.constructor&&"ArrayBuffer"===e.constructor.name)}async function k(e,t,n,r,s,i){const c={},[a,l]=T();c[a]=l,e.log(v.Trace,"(".concat(t," transport) sending data. ").concat(S(s,i.logMessageContent),"."));const h=C(s)?"arraybuffer":"text",u=await n.post(r,{content:s,headers:(0,o.A)((0,o.A)({},c),i.headers),responseType:h,timeout:i.timeout,withCredentials:i.withCredentials});e.log(v.Trace,"(".concat(t," transport) request complete. Response status: ").concat(u.statusCode,"."))}class E{constructor(e,t){this._subject=e,this._observer=t}dispose(){const e=this._subject.observers.indexOf(this._observer);e>-1&&this._subject.observers.splice(e,1),0===this._subject.observers.length&&this._subject.cancelCallback&&this._subject.cancelCallback().catch((e=>{}))}}class I{constructor(e){this._minLevel=e,this.out=console}log(e,t){if(e>=this._minLevel){const n="[".concat((new Date).toISOString(),"] ").concat(v[e],": ").concat(t);switch(e){case v.Critical:case v.Error:this.out.error(n);break;case v.Warning:this.out.warn(n);break;case v.Information:this.out.info(n);break;default:this.out.log(n)}}}}function T(){let e="X-SignalR-User-Agent";return y.isNode&&(e="User-Agent"),[e,P("8.0.7",R(),j(),D())]}function P(e,t,n,o){let r="Microsoft SignalR/";const s=e.split(".");return r+="".concat(s[0],".").concat(s[1]),r+=" (".concat(e,"; "),r+=t&&""!==t?"".concat(t,"; "):"Unknown OS; ",r+="".concat(n),r+=o?"; ".concat(o):"; Unknown Runtime Version",r+=")",r}function R(){if(!y.isNode)return"";switch(process.platform){case"win32":return"Windows NT";case"darwin":return"macOS";case"linux":return"Linux";default:return process.platform}}function D(){if(y.isNode)return process.versions.node}function j(){return y.isNode?"NodeJS":"Browser"}function A(e){return e.stack?e.stack:e.message?e.message:"".concat(e)}class x extends a{constructor(e){if(super(),this._logger=e,"undefined"===typeof fetch||y.isNode){const e=require;this._jar=new(e("tough-cookie").CookieJar),"undefined"===typeof fetch?this._fetchType=e("node-fetch"):this._fetchType=fetch,this._fetchType=e("fetch-cookie")(this._fetchType,this._jar)}else this._fetchType=fetch.bind(function(){if("undefined"!==typeof globalThis)return globalThis;if("undefined"!==typeof self)return self;if("undefined"!==typeof window)return window;if("undefined"!==typeof n.g)return n.g;throw new Error("could not find global")}());if("undefined"===typeof AbortController){const e=require;this._abortControllerType=e("abort-controller")}else this._abortControllerType=AbortController}async send(e){if(e.abortSignal&&e.abortSignal.aborted)throw new g;if(!e.method)throw new Error("No method defined.");if(!e.url)throw new Error("No url defined.");const t=new this._abortControllerType;let n;e.abortSignal&&(e.abortSignal.onabort=()=>{t.abort(),n=new g});let r,s=null;if(e.timeout){const o=e.timeout;s=setTimeout((()=>{t.abort(),this._logger.log(v.Warning,"Timeout from HTTP request."),n=new u}),o)}""===e.content&&(e.content=void 0),e.content&&(e.headers=e.headers||{},C(e.content)?e.headers["Content-Type"]="application/octet-stream":e.headers["Content-Type"]="text/plain;charset=UTF-8");try{r=await this._fetchType(e.url,{body:e.content,cache:"no-cache",credentials:!0===e.withCredentials?"include":"same-origin",headers:(0,o.A)({"X-Requested-With":"XMLHttpRequest"},e.headers),method:e.method,mode:"cors",redirect:"follow",signal:t.signal})}catch(l){if(n)throw n;throw this._logger.log(v.Warning,"Error from HTTP request. ".concat(l,".")),l}finally{s&&clearTimeout(s),e.abortSignal&&(e.abortSignal.onabort=null)}if(!r.ok){const e=await M(r,"text");throw new h(e||r.statusText,r.status)}const i=M(r,e.responseType),a=await i;return new c(r.status,r.statusText,a)}getCookieString(e){let t="";return y.isNode&&this._jar&&this._jar.getCookies(e,((e,n)=>t=n.join("; "))),t}}function M(e,t){let n;switch(t){case"arraybuffer":n=e.arrayBuffer();break;case"text":default:n=e.text();break;case"blob":case"document":case"json":throw new Error("".concat(t," is not supported."))}return n}class N extends a{constructor(e){super(),this._logger=e}send(e){return e.abortSignal&&e.abortSignal.aborted?Promise.reject(new g):e.method?e.url?new Promise(((t,n)=>{const o=new XMLHttpRequest;o.open(e.method,e.url,!0),o.withCredentials=void 0===e.withCredentials||e.withCredentials,o.setRequestHeader("X-Requested-With","XMLHttpRequest"),""===e.content&&(e.content=void 0),e.content&&(C(e.content)?o.setRequestHeader("Content-Type","application/octet-stream"):o.setRequestHeader("Content-Type","text/plain;charset=UTF-8"));const r=e.headers;r&&Object.keys(r).forEach((e=>{o.setRequestHeader(e,r[e])})),e.responseType&&(o.responseType=e.responseType),e.abortSignal&&(e.abortSignal.onabort=()=>{o.abort(),n(new g)}),e.timeout&&(o.timeout=e.timeout),o.onload=()=>{e.abortSignal&&(e.abortSignal.onabort=null),o.status>=200&&o.status<300?t(new c(o.status,o.statusText,o.response||o.responseText)):n(new h(o.response||o.responseText||o.statusText,o.status))},o.onerror=()=>{this._logger.log(v.Warning,"Error from HTTP request. ".concat(o.status,": ").concat(o.statusText,".")),n(new h(o.statusText,o.status))},o.ontimeout=()=>{this._logger.log(v.Warning,"Timeout from HTTP request."),n(new u)},o.send(e.content)})):Promise.reject(new Error("No url defined.")):Promise.reject(new Error("No method defined."))}}class q extends a{constructor(e){if(super(),"undefined"!==typeof fetch||y.isNode)this._httpClient=new x(e);else{if("undefined"===typeof XMLHttpRequest)throw new Error("No usable HttpClient found.");this._httpClient=new N(e)}}send(e){return e.abortSignal&&e.abortSignal.aborted?Promise.reject(new g):e.method?e.url?this._httpClient.send(e):Promise.reject(new Error("No url defined.")):Promise.reject(new Error("No method defined."))}getCookieString(e){return this._httpClient.getCookieString(e)}}var H,W;!function(e){e[e.None=0]="None",e[e.WebSockets=1]="WebSockets",e[e.ServerSentEvents=2]="ServerSentEvents",e[e.LongPolling=4]="LongPolling"}(H||(H={})),function(e){e[e.Text=1]="Text",e[e.Binary=2]="Binary"}(W||(W={}));class B{constructor(){this._isAborted=!1,this.onabort=null}abort(){this._isAborted||(this._isAborted=!0,this.onabort&&this.onabort())}get signal(){return this}get aborted(){return this._isAborted}}class O{get pollAborted(){return this._pollAbort.aborted}constructor(e,t,n){this._httpClient=e,this._logger=t,this._pollAbort=new B,this._options=n,this._running=!1,this.onreceive=null,this.onclose=null}async connect(e,t){if(b.isRequired(e,"url"),b.isRequired(t,"transferFormat"),b.isIn(t,W,"transferFormat"),this._url=e,this._logger.log(v.Trace,"(LongPolling transport) Connecting."),t===W.Binary&&"undefined"!==typeof XMLHttpRequest&&"string"!==typeof(new XMLHttpRequest).responseType)throw new Error("Binary protocols over XmlHttpRequest not implementing advanced features are not supported.");const[n,r]=T(),s=(0,o.A)({[n]:r},this._options.headers),i={abortSignal:this._pollAbort.signal,headers:s,timeout:1e5,withCredentials:this._options.withCredentials};t===W.Binary&&(i.responseType="arraybuffer");const c="".concat(e,"&_=").concat(Date.now());this._logger.log(v.Trace,"(LongPolling transport) polling: ".concat(c,"."));const a=await this._httpClient.get(c,i);200!==a.statusCode?(this._logger.log(v.Error,"(LongPolling transport) Unexpected response code: ".concat(a.statusCode,".")),this._closeError=new h(a.statusText||"",a.statusCode),this._running=!1):this._running=!0,this._receiving=this._poll(this._url,i)}async _poll(e,t){try{for(;this._running;)try{const n="".concat(e,"&_=").concat(Date.now());this._logger.log(v.Trace,"(LongPolling transport) polling: ".concat(n,"."));const o=await this._httpClient.get(n,t);204===o.statusCode?(this._logger.log(v.Information,"(LongPolling transport) Poll terminated by server."),this._running=!1):200!==o.statusCode?(this._logger.log(v.Error,"(LongPolling transport) Unexpected response code: ".concat(o.statusCode,".")),this._closeError=new h(o.statusText||"",o.statusCode),this._running=!1):o.content?(this._logger.log(v.Trace,"(LongPolling transport) data received. ".concat(S(o.content,this._options.logMessageContent),".")),this.onreceive&&this.onreceive(o.content)):this._logger.log(v.Trace,"(LongPolling transport) Poll timed out, reissuing.")}catch(n){this._running?n instanceof u?this._logger.log(v.Trace,"(LongPolling transport) Poll timed out, reissuing."):(this._closeError=n,this._running=!1):this._logger.log(v.Trace,"(LongPolling transport) Poll errored after shutdown: ".concat(n.message))}}finally{this._logger.log(v.Trace,"(LongPolling transport) Polling complete."),this.pollAborted||this._raiseOnClose()}}async send(e){return this._running?k(this._logger,"LongPolling",this._httpClient,this._url,e,this._options):Promise.reject(new Error("Cannot send until the transport is connected"))}async stop(){this._logger.log(v.Trace,"(LongPolling transport) Stopping polling."),this._running=!1,this._pollAbort.abort();try{await this._receiving,this._logger.log(v.Trace,"(LongPolling transport) sending DELETE request to ".concat(this._url,"."));const t={},[n,r]=T();t[n]=r;const s={headers:(0,o.A)((0,o.A)({},t),this._options.headers),timeout:this._options.timeout,withCredentials:this._options.withCredentials};let i;try{await this._httpClient.delete(this._url,s)}catch(e){i=e}i?i instanceof h&&(404===i.statusCode?this._logger.log(v.Trace,"(LongPolling transport) A 404 response was returned from sending a DELETE request."):this._logger.log(v.Trace,"(LongPolling transport) Error sending a DELETE request: ".concat(i))):this._logger.log(v.Trace,"(LongPolling transport) DELETE request accepted.")}finally{this._logger.log(v.Trace,"(LongPolling transport) Stop finished."),this._raiseOnClose()}}_raiseOnClose(){if(this.onclose){let e="(LongPolling transport) Firing onclose event.";this._closeError&&(e+=" Error: "+this._closeError),this._logger.log(v.Trace,e),this.onclose(this._closeError)}}}class L{constructor(e,t,n,o){this._httpClient=e,this._accessToken=t,this._logger=n,this._options=o,this.onreceive=null,this.onclose=null}async connect(e,t){return b.isRequired(e,"url"),b.isRequired(t,"transferFormat"),b.isIn(t,W,"transferFormat"),this._logger.log(v.Trace,"(SSE transport) Connecting."),this._url=e,this._accessToken&&(e+=(e.indexOf("?")<0?"?":"&")+"access_token=".concat(encodeURIComponent(this._accessToken))),new Promise(((n,r)=>{let s,i=!1;if(t===W.Text){if(y.isBrowser||y.isWebWorker)s=new this._options.EventSource(e,{withCredentials:this._options.withCredentials});else{const t=this._httpClient.getCookieString(e),n={};n.Cookie=t;const[r,i]=T();n[r]=i,s=new this._options.EventSource(e,{withCredentials:this._options.withCredentials,headers:(0,o.A)((0,o.A)({},n),this._options.headers)})}try{s.onmessage=e=>{if(this.onreceive)try{this._logger.log(v.Trace,"(SSE transport) data received. ".concat(S(e.data,this._options.logMessageContent),".")),this.onreceive(e.data)}catch(t){return void this._close(t)}},s.onerror=e=>{i?this._close():r(new Error("EventSource failed to connect. The connection could not be found on the server, either the connection ID is not present on the server, or a proxy is refusing/buffering the connection. If you have multiple servers check that sticky sessions are enabled."))},s.onopen=()=>{this._logger.log(v.Information,"SSE connected to ".concat(this._url)),this._eventSource=s,i=!0,n()}}catch(c){return void r(c)}}else r(new Error("The Server-Sent Events transport only supports the 'Text' transfer format"))}))}async send(e){return this._eventSource?k(this._logger,"SSE",this._httpClient,this._url,e,this._options):Promise.reject(new Error("Cannot send until the transport is connected"))}stop(){return this._close(),Promise.resolve()}_close(e){this._eventSource&&(this._eventSource.close(),this._eventSource=void 0,this.onclose&&this.onclose(e))}}class F{constructor(e,t,n,o,r,s){this._logger=n,this._accessTokenFactory=t,this._logMessageContent=o,this._webSocketConstructor=r,this._httpClient=e,this.onreceive=null,this.onclose=null,this._headers=s}async connect(e,t){let n;return b.isRequired(e,"url"),b.isRequired(t,"transferFormat"),b.isIn(t,W,"transferFormat"),this._logger.log(v.Trace,"(WebSockets transport) Connecting."),this._accessTokenFactory&&(n=await this._accessTokenFactory()),new Promise(((r,s)=>{let c;e=e.replace(/^http/,"ws");const a=this._httpClient.getCookieString(e);let l=!1;if(y.isNode||y.isReactNative){const t={},[r,s]=T();t[r]=s,n&&(t[i.Authorization]="Bearer ".concat(n)),a&&(t[i.Cookie]=a),c=new this._webSocketConstructor(e,void 0,{headers:(0,o.A)((0,o.A)({},t),this._headers)})}else n&&(e+=(e.indexOf("?")<0?"?":"&")+"access_token=".concat(encodeURIComponent(n)));c||(c=new this._webSocketConstructor(e)),t===W.Binary&&(c.binaryType="arraybuffer"),c.onopen=t=>{this._logger.log(v.Information,"WebSocket connected to ".concat(e,".")),this._webSocket=c,l=!0,r()},c.onerror=e=>{let t=null;t="undefined"!==typeof ErrorEvent&&e instanceof ErrorEvent?e.error:"There was an error with the transport",this._logger.log(v.Information,"(WebSockets transport) ".concat(t,"."))},c.onmessage=e=>{if(this._logger.log(v.Trace,"(WebSockets transport) data received. ".concat(S(e.data,this._logMessageContent),".")),this.onreceive)try{this.onreceive(e.data)}catch(t){return void this._close(t)}},c.onclose=e=>{if(l)this._close(e);else{let t=null;t="undefined"!==typeof ErrorEvent&&e instanceof ErrorEvent?e.error:"WebSocket failed to connect. The connection could not be found on the server, either the endpoint may not be a SignalR endpoint, the connection ID is not present on the server, or there is a proxy blocking WebSockets. If you have multiple servers check that sticky sessions are enabled.",s(new Error(t))}}}))}send(e){return this._webSocket&&this._webSocket.readyState===this._webSocketConstructor.OPEN?(this._logger.log(v.Trace,"(WebSockets transport) sending data. ".concat(S(e,this._logMessageContent),".")),this._webSocket.send(e),Promise.resolve()):Promise.reject("WebSocket is not in the OPEN state")}stop(){return this._webSocket&&this._close(void 0),Promise.resolve()}_close(e){this._webSocket&&(this._webSocket.onclose=()=>{},this._webSocket.onmessage=()=>{},this._webSocket.onerror=()=>{},this._webSocket.close(),this._webSocket=void 0),this._logger.log(v.Trace,"(WebSockets transport) socket closed."),this.onclose&&(!this._isCloseEvent(e)||!1!==e.wasClean&&1e3===e.code?e instanceof Error?this.onclose(e):this.onclose():this.onclose(new Error("WebSocket closed with status code: ".concat(e.code," (").concat(e.reason||"no reason given",")."))))}_isCloseEvent(e){return e&&"boolean"===typeof e.wasClean&&"number"===typeof e.code}}class U{constructor(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};var n;if(this._stopPromiseResolver=()=>{},this.features={},this._negotiateVersion=1,b.isRequired(e,"url"),this._logger=void 0===(n=t.logger)?new I(v.Information):null===n?w.instance:void 0!==n.log?n:new I(n),this.baseUrl=this._resolveUrl(e),t=t||{},t.logMessageContent=void 0!==t.logMessageContent&&t.logMessageContent,"boolean"!==typeof t.withCredentials&&void 0!==t.withCredentials)throw new Error("withCredentials option was not a 'boolean' or 'undefined' value");t.withCredentials=void 0===t.withCredentials||t.withCredentials,t.timeout=void 0===t.timeout?1e5:t.timeout;let o=null,r=null;if(y.isNode){const e=require;o=e("ws"),r=e("eventsource")}y.isNode||"undefined"===typeof WebSocket||t.WebSocket?y.isNode&&!t.WebSocket&&o&&(t.WebSocket=o):t.WebSocket=WebSocket,y.isNode||"undefined"===typeof EventSource||t.EventSource?y.isNode&&!t.EventSource&&"undefined"!==typeof r&&(t.EventSource=r):t.EventSource=EventSource,this._httpClient=new l(t.httpClient||new q(this._logger),t.accessTokenFactory),this._connectionState="Disconnected",this._connectionStarted=!1,this._options=t,this.onreceive=null,this.onclose=null}async start(e){if(e=e||W.Binary,b.isIn(e,W,"transferFormat"),this._logger.log(v.Debug,"Starting connection with transfer format '".concat(W[e],"'.")),"Disconnected"!==this._connectionState)return Promise.reject(new Error("Cannot start an HttpConnection that is not in the 'Disconnected' state."));if(this._connectionState="Connecting",this._startInternalPromise=this._startInternal(e),await this._startInternalPromise,"Disconnecting"===this._connectionState){const e="Failed to start the HttpConnection before stop() was called.";return this._logger.log(v.Error,e),await this._stopPromise,Promise.reject(new g(e))}if("Connected"!==this._connectionState){const e="HttpConnection.startInternal completed gracefully but didn't enter the connection into the connected state!";return this._logger.log(v.Error,e),Promise.reject(new g(e))}this._connectionStarted=!0}send(e){return"Connected"!==this._connectionState?Promise.reject(new Error("Cannot send data if the connection is not in the 'Connected' State.")):(this._sendQueue||(this._sendQueue=new z(this.transport)),this._sendQueue.send(e))}async stop(e){return"Disconnected"===this._connectionState?(this._logger.log(v.Debug,"Call to HttpConnection.stop(".concat(e,") ignored because the connection is already in the disconnected state.")),Promise.resolve()):"Disconnecting"===this._connectionState?(this._logger.log(v.Debug,"Call to HttpConnection.stop(".concat(e,") ignored because the connection is already in the disconnecting state.")),this._stopPromise):(this._connectionState="Disconnecting",this._stopPromise=new Promise((e=>{this._stopPromiseResolver=e})),await this._stopInternal(e),void await this._stopPromise)}async _stopInternal(e){this._stopError=e;try{await this._startInternalPromise}catch(t){}if(this.transport){try{await this.transport.stop()}catch(t){this._logger.log(v.Error,"HttpConnection.transport.stop() threw error '".concat(t,"'.")),this._stopConnection()}this.transport=void 0}else this._logger.log(v.Debug,"HttpConnection.transport is undefined in HttpConnection.stop() because start() failed.")}async _startInternal(e){let t=this.baseUrl;this._accessTokenFactory=this._options.accessTokenFactory,this._httpClient._accessTokenFactory=this._accessTokenFactory;try{if(this._options.skipNegotiation){if(this._options.transport!==H.WebSockets)throw new Error("Negotiation can only be skipped when using the WebSocket transport directly.");this.transport=this._constructTransport(H.WebSockets),await this._startTransport(t,e)}else{let n=null,o=0;do{if(n=await this._getNegotiationResponse(t),"Disconnecting"===this._connectionState||"Disconnected"===this._connectionState)throw new g("The connection was stopped during negotiation.");if(n.error)throw new Error(n.error);if(n.ProtocolVersion)throw new Error("Detected a connection attempt to an ASP.NET SignalR Server. This client only supports connecting to an ASP.NET Core SignalR Server. See https://aka.ms/signalr-core-differences for details.");if(n.url&&(t=n.url),n.accessToken){const e=n.accessToken;this._accessTokenFactory=()=>e,this._httpClient._accessToken=e,this._httpClient._accessTokenFactory=void 0}o++}while(n.url&&o<100);if(100===o&&n.url)throw new Error("Negotiate redirection limit exceeded.");await this._createTransport(t,this._options.transport,n,e)}this.transport instanceof O&&(this.features.inherentKeepAlive=!0),"Connecting"===this._connectionState&&(this._logger.log(v.Debug,"The HttpConnection connected successfully."),this._connectionState="Connected")}catch(n){return this._logger.log(v.Error,"Failed to start the connection: "+n),this._connectionState="Disconnected",this.transport=void 0,this._stopPromiseResolver(),Promise.reject(n)}}async _getNegotiationResponse(e){const t={},[n,r]=T();t[n]=r;const s=this._resolveNegotiateUrl(e);this._logger.log(v.Debug,"Sending negotiation request: ".concat(s,"."));try{const e=await this._httpClient.post(s,{content:"",headers:(0,o.A)((0,o.A)({},t),this._options.headers),timeout:this._options.timeout,withCredentials:this._options.withCredentials});if(200!==e.statusCode)return Promise.reject(new Error("Unexpected status code returned from negotiate '".concat(e.statusCode,"'")));const n=JSON.parse(e.content);return(!n.negotiateVersion||n.negotiateVersion<1)&&(n.connectionToken=n.connectionId),n.useStatefulReconnect&&!0!==this._options._useStatefulReconnect?Promise.reject(new f("Client didn't negotiate Stateful Reconnect but the server did.")):n}catch(i){let e="Failed to complete negotiation with the server: "+i;return i instanceof h&&404===i.statusCode&&(e+=" Either this is not a SignalR endpoint or there is a proxy blocking the connection."),this._logger.log(v.Error,e),Promise.reject(new f(e))}}_createConnectUrl(e,t){return t?e+(-1===e.indexOf("?")?"?":"&")+"id=".concat(t):e}async _createTransport(e,t,n,o){let r=this._createConnectUrl(e,n.connectionToken);if(this._isITransport(t))return this._logger.log(v.Debug,"Connection was provided an instance of ITransport, using that directly."),this.transport=t,await this._startTransport(r,o),void(this.connectionId=n.connectionId);const s=[],i=n.availableTransports||[];let c=n;for(const l of i){const n=this._resolveTransportOrError(l,t,o,!0===(null===c||void 0===c?void 0:c.useStatefulReconnect));if(n instanceof Error)s.push("".concat(l.transport," failed:")),s.push(n);else if(this._isITransport(n)){if(this.transport=n,!c){try{c=await this._getNegotiationResponse(e)}catch(a){return Promise.reject(a)}r=this._createConnectUrl(e,c.connectionToken)}try{return await this._startTransport(r,o),void(this.connectionId=c.connectionId)}catch(a){if(this._logger.log(v.Error,"Failed to start the transport '".concat(l.transport,"': ").concat(a)),c=void 0,s.push(new _("".concat(l.transport," failed: ").concat(a),H[l.transport])),"Connecting"!==this._connectionState){const e="Failed to select transport before stop() was called.";return this._logger.log(v.Debug,e),Promise.reject(new g(e))}}}}return s.length>0?Promise.reject(new m("Unable to connect to the server with any of the available transports. ".concat(s.join(" ")),s)):Promise.reject(new Error("None of the transports supported by the client are supported by the server."))}_constructTransport(e){switch(e){case H.WebSockets:if(!this._options.WebSocket)throw new Error("'WebSocket' is not supported in your environment.");return new F(this._httpClient,this._accessTokenFactory,this._logger,this._options.logMessageContent,this._options.WebSocket,this._options.headers||{});case H.ServerSentEvents:if(!this._options.EventSource)throw new Error("'EventSource' is not supported in your environment.");return new L(this._httpClient,this._httpClient._accessToken,this._logger,this._options);case H.LongPolling:return new O(this._httpClient,this._logger,this._options);default:throw new Error("Unknown transport: ".concat(e,"."))}}_startTransport(e,t){return this.transport.onreceive=this.onreceive,this.features.reconnect?this.transport.onclose=async n=>{let o=!1;if(this.features.reconnect){try{this.features.disconnected(),await this.transport.connect(e,t),await this.features.resend()}catch(r){o=!0}o&&this._stopConnection(n)}else this._stopConnection(n)}:this.transport.onclose=e=>this._stopConnection(e),this.transport.connect(e,t)}_resolveTransportOrError(e,t,n,o){const r=H[e.transport];if(null===r||void 0===r)return this._logger.log(v.Debug,"Skipping transport '".concat(e.transport,"' because it is not supported by this client.")),new Error("Skipping transport '".concat(e.transport,"' because it is not supported by this client."));if(!function(e,t){return!e||0!==(t&e)}(t,r))return this._logger.log(v.Debug,"Skipping transport '".concat(H[r],"' because it was disabled by the client.")),new p("'".concat(H[r],"' is disabled by the client."),r);if(!(e.transferFormats.map((e=>W[e])).indexOf(n)>=0))return this._logger.log(v.Debug,"Skipping transport '".concat(H[r],"' because it does not support the requested transfer format '").concat(W[n],"'.")),new Error("'".concat(H[r],"' does not support ").concat(W[n],"."));if(r===H.WebSockets&&!this._options.WebSocket||r===H.ServerSentEvents&&!this._options.EventSource)return this._logger.log(v.Debug,"Skipping transport '".concat(H[r],"' because it is not supported in your environment.'")),new d("'".concat(H[r],"' is not supported in your environment."),r);this._logger.log(v.Debug,"Selecting transport '".concat(H[r],"'."));try{return this.features.reconnect=r===H.WebSockets?o:void 0,this._constructTransport(r)}catch(s){return s}}_isITransport(e){return e&&"object"===typeof e&&"connect"in e}_stopConnection(e){if(this._logger.log(v.Debug,"HttpConnection.stopConnection(".concat(e,") called while in state ").concat(this._connectionState,".")),this.transport=void 0,e=this._stopError||e,this._stopError=void 0,"Disconnected"!==this._connectionState){if("Connecting"===this._connectionState)throw this._logger.log(v.Warning,"Call to HttpConnection.stopConnection(".concat(e,") was ignored because the connection is still in the connecting state.")),new Error("HttpConnection.stopConnection(".concat(e,") was called while the connection is still in the connecting state."));if("Disconnecting"===this._connectionState&&this._stopPromiseResolver(),e?this._logger.log(v.Error,"Connection disconnected with error '".concat(e,"'.")):this._logger.log(v.Information,"Connection disconnected."),this._sendQueue&&(this._sendQueue.stop().catch((e=>{this._logger.log(v.Error,"TransportSendQueue.stop() threw error '".concat(e,"'."))})),this._sendQueue=void 0),this.connectionId=void 0,this._connectionState="Disconnected",this._connectionStarted){this._connectionStarted=!1;try{this.onclose&&this.onclose(e)}catch(t){this._logger.log(v.Error,"HttpConnection.onclose(".concat(e,") threw error '").concat(t,"'."))}}}else this._logger.log(v.Debug,"Call to HttpConnection.stopConnection(".concat(e,") was ignored because the connection is already in the disconnected state."))}_resolveUrl(e){if(0===e.lastIndexOf("https://",0)||0===e.lastIndexOf("http://",0))return e;if(!y.isBrowser)throw new Error("Cannot resolve '".concat(e,"'."));const t=window.document.createElement("a");return t.href=e,this._logger.log(v.Information,"Normalizing '".concat(e,"' to '").concat(t.href,"'.")),t.href}_resolveNegotiateUrl(e){const t=new URL(e);t.pathname.endsWith("/")?t.pathname+="negotiate":t.pathname+="/negotiate";const n=new URLSearchParams(t.searchParams);return n.has("negotiateVersion")||n.append("negotiateVersion",this._negotiateVersion.toString()),n.has("useStatefulReconnect")?"true"===n.get("useStatefulReconnect")&&(this._options._useStatefulReconnect=!0):!0===this._options._useStatefulReconnect&&n.append("useStatefulReconnect","true"),t.search=n.toString(),t.toString()}}class z{constructor(e){this._transport=e,this._buffer=[],this._executing=!0,this._sendBufferedData=new K,this._transportResult=new K,this._sendLoopPromise=this._sendLoop()}send(e){return this._bufferData(e),this._transportResult||(this._transportResult=new K),this._transportResult.promise}stop(){return this._executing=!1,this._sendBufferedData.resolve(),this._sendLoopPromise}_bufferData(e){if(this._buffer.length&&typeof this._buffer[0]!==typeof e)throw new Error("Expected data to be of type ".concat(typeof this._buffer," but was of type ").concat(typeof e));this._buffer.push(e),this._sendBufferedData.resolve()}async _sendLoop(){for(;;){if(await this._sendBufferedData.promise,!this._executing){this._transportResult&&this._transportResult.reject("Connection stopped.");break}this._sendBufferedData=new K;const t=this._transportResult;this._transportResult=void 0;const n="string"===typeof this._buffer[0]?this._buffer.join(""):z._concatBuffers(this._buffer);this._buffer.length=0;try{await this._transport.send(n),t.resolve()}catch(e){t.reject(e)}}}static _concatBuffers(e){const t=e.map((e=>e.byteLength)).reduce(((e,t)=>e+t)),n=new Uint8Array(t);let o=0;for(const r of e)n.set(new Uint8Array(r),o),o+=r.byteLength;return n.buffer}}class K{constructor(){this.promise=new Promise(((e,t)=>[this._resolver,this._rejecter]=[e,t]))}resolve(){this._resolver()}reject(e){this._rejecter(e)}}class V{static write(e){return"".concat(e).concat(V.RecordSeparator)}static parse(e){if(e[e.length-1]!==V.RecordSeparator)throw new Error("Message is incomplete.");const t=e.split(V.RecordSeparator);return t.pop(),t}}V.RecordSeparatorCode=30,V.RecordSeparator=String.fromCharCode(V.RecordSeparatorCode);class X{writeHandshakeRequest(e){return V.write(JSON.stringify(e))}parseHandshakeResponse(e){let t,n;if(C(e)){const o=new Uint8Array(e),r=o.indexOf(V.RecordSeparatorCode);if(-1===r)throw new Error("Message is incomplete.");const s=r+1;t=String.fromCharCode.apply(null,Array.prototype.slice.call(o.slice(0,s))),n=o.byteLength>s?o.slice(s).buffer:null}else{const o=e,r=o.indexOf(V.RecordSeparator);if(-1===r)throw new Error("Message is incomplete.");const s=r+1;t=o.substring(0,s),n=o.length>s?o.substring(s):null}const o=V.parse(t),r=JSON.parse(o[0]);if(r.type)throw new Error("Expected a handshake response from the server.");return[n,r]}}var J;!function(e){e[e.Invocation=1]="Invocation",e[e.StreamItem=2]="StreamItem",e[e.Completion=3]="Completion",e[e.StreamInvocation=4]="StreamInvocation",e[e.CancelInvocation=5]="CancelInvocation",e[e.Ping=6]="Ping",e[e.Close=7]="Close",e[e.Ack=8]="Ack",e[e.Sequence=9]="Sequence"}(J||(J={}));class Q{constructor(){this.observers=[]}next(e){for(const t of this.observers)t.next(e)}error(e){for(const t of this.observers)t.error&&t.error(e)}complete(){for(const e of this.observers)e.complete&&e.complete()}subscribe(e){return this.observers.push(e),new E(this,e)}}class ${constructor(e,t,n){this._bufferSize=1e5,this._messages=[],this._totalMessageCount=0,this._waitForSequenceMessage=!1,this._nextReceivingSequenceId=1,this._latestReceivedSequenceId=0,this._bufferedByteCount=0,this._reconnectInProgress=!1,this._protocol=e,this._connection=t,this._bufferSize=n}async _send(e){const t=this._protocol.writeMessage(e);let n=Promise.resolve();if(this._isInvocationMessage(e)){this._totalMessageCount++;let e=()=>{},o=()=>{};C(t)?this._bufferedByteCount+=t.byteLength:this._bufferedByteCount+=t.length,this._bufferedByteCount>=this._bufferSize&&(n=new Promise(((t,n)=>{e=t,o=n}))),this._messages.push(new Z(t,this._totalMessageCount,e,o))}try{this._reconnectInProgress||await this._connection.send(t)}catch(o){this._disconnected()}await n}_ack(e){let t=-1;for(let n=0;n<this._messages.length;n++){const o=this._messages[n];if(o._id<=e.sequenceId)t=n,C(o._message)?this._bufferedByteCount-=o._message.byteLength:this._bufferedByteCount-=o._message.length,o._resolver();else{if(!(this._bufferedByteCount<this._bufferSize))break;o._resolver()}}-1!==t&&(this._messages=this._messages.slice(t+1))}_shouldProcessMessage(e){if(this._waitForSequenceMessage)return e.type===J.Sequence&&(this._waitForSequenceMessage=!1,!0);if(!this._isInvocationMessage(e))return!0;const t=this._nextReceivingSequenceId;return this._nextReceivingSequenceId++,t<=this._latestReceivedSequenceId?(t===this._latestReceivedSequenceId&&this._ackTimer(),!1):(this._latestReceivedSequenceId=t,this._ackTimer(),!0)}_resetSequence(e){e.sequenceId>this._nextReceivingSequenceId?this._connection.stop(new Error("Sequence ID greater than amount of messages we've received.")):this._nextReceivingSequenceId=e.sequenceId}_disconnected(){this._reconnectInProgress=!0,this._waitForSequenceMessage=!0}async _resend(){const e=0!==this._messages.length?this._messages[0]._id:this._totalMessageCount+1;await this._connection.send(this._protocol.writeMessage({type:J.Sequence,sequenceId:e}));const t=this._messages;for(const n of t)await this._connection.send(n._message);this._reconnectInProgress=!1}_dispose(e){null!==e&&void 0!==e||(e=new Error("Unable to reconnect to server."));for(const t of this._messages)t._rejector(e)}_isInvocationMessage(e){switch(e.type){case J.Invocation:case J.StreamItem:case J.Completion:case J.StreamInvocation:case J.CancelInvocation:return!0;case J.Close:case J.Sequence:case J.Ping:case J.Ack:return!1}}_ackTimer(){void 0===this._ackTimerHandle&&(this._ackTimerHandle=setTimeout((async()=>{try{this._reconnectInProgress||await this._connection.send(this._protocol.writeMessage({type:J.Ack,sequenceId:this._latestReceivedSequenceId}))}catch(e){}clearTimeout(this._ackTimerHandle),this._ackTimerHandle=void 0}),1e3))}}class Z{constructor(e,t,n,o){this._message=e,this._id=t,this._resolver=n,this._rejector=o}}var G;!function(e){e.Disconnected="Disconnected",e.Connecting="Connecting",e.Connected="Connected",e.Disconnecting="Disconnecting",e.Reconnecting="Reconnecting"}(G||(G={}));class Y{static create(e,t,n,o,r,s,i){return new Y(e,t,n,o,r,s,i)}constructor(e,t,n,o,r,s,i){this._nextKeepAlive=0,this._freezeEventListener=()=>{this._logger.log(v.Warning,"The page is being frozen, this will likely lead to the connection being closed and messages being lost. For more information see the docs at https://learn.microsoft.com/aspnet/core/signalr/javascript-client#bsleep")},b.isRequired(e,"connection"),b.isRequired(t,"logger"),b.isRequired(n,"protocol"),this.serverTimeoutInMilliseconds=null!==r&&void 0!==r?r:3e4,this.keepAliveIntervalInMilliseconds=null!==s&&void 0!==s?s:15e3,this._statefulReconnectBufferSize=null!==i&&void 0!==i?i:1e5,this._logger=t,this._protocol=n,this.connection=e,this._reconnectPolicy=o,this._handshakeProtocol=new X,this.connection.onreceive=e=>this._processIncomingData(e),this.connection.onclose=e=>this._connectionClosed(e),this._callbacks={},this._methods={},this._closedCallbacks=[],this._reconnectingCallbacks=[],this._reconnectedCallbacks=[],this._invocationId=0,this._receivedHandshakeResponse=!1,this._connectionState=G.Disconnected,this._connectionStarted=!1,this._cachedPingMessage=this._protocol.writeMessage({type:J.Ping})}get state(){return this._connectionState}get connectionId(){return this.connection&&this.connection.connectionId||null}get baseUrl(){return this.connection.baseUrl||""}set baseUrl(e){if(this._connectionState!==G.Disconnected&&this._connectionState!==G.Reconnecting)throw new Error("The HubConnection must be in the Disconnected or Reconnecting state to change the url.");if(!e)throw new Error("The HubConnection url must be a valid url.");this.connection.baseUrl=e}start(){return this._startPromise=this._startWithStateTransitions(),this._startPromise}async _startWithStateTransitions(){if(this._connectionState!==G.Disconnected)return Promise.reject(new Error("Cannot start a HubConnection that is not in the 'Disconnected' state."));this._connectionState=G.Connecting,this._logger.log(v.Debug,"Starting HubConnection.");try{await this._startInternal(),y.isBrowser&&window.document.addEventListener("freeze",this._freezeEventListener),this._connectionState=G.Connected,this._connectionStarted=!0,this._logger.log(v.Debug,"HubConnection connected successfully.")}catch(e){return this._connectionState=G.Disconnected,this._logger.log(v.Debug,"HubConnection failed to start successfully because of error '".concat(e,"'.")),Promise.reject(e)}}async _startInternal(){this._stopDuringStartError=void 0,this._receivedHandshakeResponse=!1;const e=new Promise(((e,t)=>{this._handshakeResolver=e,this._handshakeRejecter=t}));await this.connection.start(this._protocol.transferFormat);try{let t=this._protocol.version;this.connection.features.reconnect||(t=1);const n={protocol:this._protocol.name,version:t};if(this._logger.log(v.Debug,"Sending handshake request."),await this._sendMessage(this._handshakeProtocol.writeHandshakeRequest(n)),this._logger.log(v.Information,"Using HubProtocol '".concat(this._protocol.name,"'.")),this._cleanupTimeout(),this._resetTimeoutPeriod(),this._resetKeepAliveInterval(),await e,this._stopDuringStartError)throw this._stopDuringStartError;(this.connection.features.reconnect||!1)&&(this._messageBuffer=new $(this._protocol,this.connection,this._statefulReconnectBufferSize),this.connection.features.disconnected=this._messageBuffer._disconnected.bind(this._messageBuffer),this.connection.features.resend=()=>{if(this._messageBuffer)return this._messageBuffer._resend()}),this.connection.features.inherentKeepAlive||await this._sendMessage(this._cachedPingMessage)}catch(t){throw this._logger.log(v.Debug,"Hub handshake failed with error '".concat(t,"' during start(). Stopping HubConnection.")),this._cleanupTimeout(),this._cleanupPingTimer(),await this.connection.stop(t),t}}async stop(){const e=this._startPromise;this.connection.features.reconnect=!1,this._stopPromise=this._stopInternal(),await this._stopPromise;try{await e}catch(t){}}_stopInternal(e){if(this._connectionState===G.Disconnected)return this._logger.log(v.Debug,"Call to HubConnection.stop(".concat(e,") ignored because it is already in the disconnected state.")),Promise.resolve();if(this._connectionState===G.Disconnecting)return this._logger.log(v.Debug,"Call to HttpConnection.stop(".concat(e,") ignored because the connection is already in the disconnecting state.")),this._stopPromise;const t=this._connectionState;return this._connectionState=G.Disconnecting,this._logger.log(v.Debug,"Stopping HubConnection."),this._reconnectDelayHandle?(this._logger.log(v.Debug,"Connection stopped during reconnect delay. Done reconnecting."),clearTimeout(this._reconnectDelayHandle),this._reconnectDelayHandle=void 0,this._completeClose(),Promise.resolve()):(t===G.Connected&&this._sendCloseMessage(),this._cleanupTimeout(),this._cleanupPingTimer(),this._stopDuringStartError=e||new g("The connection was stopped before the hub handshake could complete."),this.connection.stop(e))}async _sendCloseMessage(){try{await this._sendWithProtocol(this._createCloseMessage())}catch(e){}}stream(e){for(var t=arguments.length,n=new Array(t>1?t-1:0),o=1;o<t;o++)n[o-1]=arguments[o];const[r,s]=this._replaceStreamingParams(n),i=this._createStreamInvocation(e,n,s);let c;const a=new Q;return a.cancelCallback=()=>{const e=this._createCancelInvocation(i.invocationId);return delete this._callbacks[i.invocationId],c.then((()=>this._sendWithProtocol(e)))},this._callbacks[i.invocationId]=(e,t)=>{t?a.error(t):e&&(e.type===J.Completion?e.error?a.error(new Error(e.error)):a.complete():a.next(e.item))},c=this._sendWithProtocol(i).catch((e=>{a.error(e),delete this._callbacks[i.invocationId]})),this._launchStreams(r,c),a}_sendMessage(e){return this._resetKeepAliveInterval(),this.connection.send(e)}_sendWithProtocol(e){return this._messageBuffer?this._messageBuffer._send(e):this._sendMessage(this._protocol.writeMessage(e))}send(e){for(var t=arguments.length,n=new Array(t>1?t-1:0),o=1;o<t;o++)n[o-1]=arguments[o];const[r,s]=this._replaceStreamingParams(n),i=this._sendWithProtocol(this._createInvocation(e,n,!0,s));return this._launchStreams(r,i),i}invoke(e){for(var t=arguments.length,n=new Array(t>1?t-1:0),o=1;o<t;o++)n[o-1]=arguments[o];const[r,s]=this._replaceStreamingParams(n),i=this._createInvocation(e,n,!1,s);return new Promise(((e,t)=>{this._callbacks[i.invocationId]=(n,o)=>{o?t(o):n&&(n.type===J.Completion?n.error?t(new Error(n.error)):e(n.result):t(new Error("Unexpected message type: ".concat(n.type))))};const n=this._sendWithProtocol(i).catch((e=>{t(e),delete this._callbacks[i.invocationId]}));this._launchStreams(r,n)}))}on(e,t){e&&t&&(e=e.toLowerCase(),this._methods[e]||(this._methods[e]=[]),-1===this._methods[e].indexOf(t)&&this._methods[e].push(t))}off(e,t){if(!e)return;e=e.toLowerCase();const n=this._methods[e];if(n)if(t){const o=n.indexOf(t);-1!==o&&(n.splice(o,1),0===n.length&&delete this._methods[e])}else delete this._methods[e]}onclose(e){e&&this._closedCallbacks.push(e)}onreconnecting(e){e&&this._reconnectingCallbacks.push(e)}onreconnected(e){e&&this._reconnectedCallbacks.push(e)}_processIncomingData(e){if(this._cleanupTimeout(),this._receivedHandshakeResponse||(e=this._processHandshakeResponse(e),this._receivedHandshakeResponse=!0),e){const n=this._protocol.parseMessages(e,this._logger);for(const e of n)if(!this._messageBuffer||this._messageBuffer._shouldProcessMessage(e))switch(e.type){case J.Invocation:this._invokeClientMethod(e).catch((e=>{this._logger.log(v.Error,"Invoke client method threw error: ".concat(A(e)))}));break;case J.StreamItem:case J.Completion:{const n=this._callbacks[e.invocationId];if(n){e.type===J.Completion&&delete this._callbacks[e.invocationId];try{n(e)}catch(t){this._logger.log(v.Error,"Stream callback threw error: ".concat(A(t)))}}break}case J.Ping:break;case J.Close:{this._logger.log(v.Information,"Close message received from server.");const t=e.error?new Error("Server returned an error on close: "+e.error):void 0;!0===e.allowReconnect?this.connection.stop(t):this._stopPromise=this._stopInternal(t);break}case J.Ack:this._messageBuffer&&this._messageBuffer._ack(e);break;case J.Sequence:this._messageBuffer&&this._messageBuffer._resetSequence(e);break;default:this._logger.log(v.Warning,"Invalid message type: ".concat(e.type,"."))}}this._resetTimeoutPeriod()}_processHandshakeResponse(e){let t,n;try{[n,t]=this._handshakeProtocol.parseHandshakeResponse(e)}catch(o){const e="Error parsing handshake response: "+o;this._logger.log(v.Error,e);const t=new Error(e);throw this._handshakeRejecter(t),t}if(t.error){const e="Server returned handshake error: "+t.error;this._logger.log(v.Error,e);const n=new Error(e);throw this._handshakeRejecter(n),n}return this._logger.log(v.Debug,"Server handshake complete."),this._handshakeResolver(),n}_resetKeepAliveInterval(){this.connection.features.inherentKeepAlive||(this._nextKeepAlive=(new Date).getTime()+this.keepAliveIntervalInMilliseconds,this._cleanupPingTimer())}_resetTimeoutPeriod(){if((!this.connection.features||!this.connection.features.inherentKeepAlive)&&(this._timeoutHandle=setTimeout((()=>this.serverTimeout()),this.serverTimeoutInMilliseconds),void 0===this._pingServerHandle)){let e=this._nextKeepAlive-(new Date).getTime();e<0&&(e=0),this._pingServerHandle=setTimeout((async()=>{if(this._connectionState===G.Connected)try{await this._sendMessage(this._cachedPingMessage)}catch(e){this._cleanupPingTimer()}}),e)}}serverTimeout(){this.connection.stop(new Error("Server timeout elapsed without receiving a message from the server."))}async _invokeClientMethod(e){const t=e.target.toLowerCase(),n=this._methods[t];if(!n)return this._logger.log(v.Warning,"No client method with the name '".concat(t,"' found.")),void(e.invocationId&&(this._logger.log(v.Warning,"No result given for '".concat(t,"' method and invocation ID '").concat(e.invocationId,"'.")),await this._sendWithProtocol(this._createCompletionMessage(e.invocationId,"Client didn't provide a result.",null))));const o=n.slice(),r=!!e.invocationId;let s,i,c;for(const l of o)try{const n=s;s=await l.apply(this,e.arguments),r&&s&&n&&(this._logger.log(v.Error,"Multiple results provided for '".concat(t,"'. Sending error to server.")),c=this._createCompletionMessage(e.invocationId,"Client provided multiple results.",null)),i=void 0}catch(a){i=a,this._logger.log(v.Error,"A callback for the method '".concat(t,"' threw error '").concat(a,"'."))}c?await this._sendWithProtocol(c):r?(i?c=this._createCompletionMessage(e.invocationId,"".concat(i),null):void 0!==s?c=this._createCompletionMessage(e.invocationId,null,s):(this._logger.log(v.Warning,"No result given for '".concat(t,"' method and invocation ID '").concat(e.invocationId,"'.")),c=this._createCompletionMessage(e.invocationId,"Client didn't provide a result.",null)),await this._sendWithProtocol(c)):s&&this._logger.log(v.Error,"Result given for '".concat(t,"' method but server is not expecting a result."))}_connectionClosed(e){this._logger.log(v.Debug,"HubConnection.connectionClosed(".concat(e,") called while in state ").concat(this._connectionState,".")),this._stopDuringStartError=this._stopDuringStartError||e||new g("The underlying connection was closed before the hub handshake could complete."),this._handshakeResolver&&this._handshakeResolver(),this._cancelCallbacksWithError(e||new Error("Invocation canceled due to the underlying connection being closed.")),this._cleanupTimeout(),this._cleanupPingTimer(),this._connectionState===G.Disconnecting?this._completeClose(e):this._connectionState===G.Connected&&this._reconnectPolicy?this._reconnect(e):this._connectionState===G.Connected&&this._completeClose(e)}_completeClose(e){if(this._connectionStarted){this._connectionState=G.Disconnected,this._connectionStarted=!1,this._messageBuffer&&(this._messageBuffer._dispose(null!==e&&void 0!==e?e:new Error("Connection closed.")),this._messageBuffer=void 0),y.isBrowser&&window.document.removeEventListener("freeze",this._freezeEventListener);try{this._closedCallbacks.forEach((t=>t.apply(this,[e])))}catch(t){this._logger.log(v.Error,"An onclose callback called with error '".concat(e,"' threw error '").concat(t,"'."))}}}async _reconnect(e){const t=Date.now();let n=0,o=void 0!==e?e:new Error("Attempting to reconnect due to a unknown error."),r=this._getNextRetryDelay(n++,0,o);if(null===r)return this._logger.log(v.Debug,"Connection not reconnecting because the IRetryPolicy returned null on the first reconnect attempt."),void this._completeClose(e);if(this._connectionState=G.Reconnecting,e?this._logger.log(v.Information,"Connection reconnecting because of error '".concat(e,"'.")):this._logger.log(v.Information,"Connection reconnecting."),0!==this._reconnectingCallbacks.length){try{this._reconnectingCallbacks.forEach((t=>t.apply(this,[e])))}catch(s){this._logger.log(v.Error,"An onreconnecting callback called with error '".concat(e,"' threw error '").concat(s,"'."))}if(this._connectionState!==G.Reconnecting)return void this._logger.log(v.Debug,"Connection left the reconnecting state in onreconnecting callback. Done reconnecting.")}for(;null!==r;){if(this._logger.log(v.Information,"Reconnect attempt number ".concat(n," will start in ").concat(r," ms.")),await new Promise((e=>{this._reconnectDelayHandle=setTimeout(e,r)})),this._reconnectDelayHandle=void 0,this._connectionState!==G.Reconnecting)return void this._logger.log(v.Debug,"Connection left the reconnecting state during reconnect delay. Done reconnecting.");try{if(await this._startInternal(),this._connectionState=G.Connected,this._logger.log(v.Information,"HubConnection reconnected successfully."),0!==this._reconnectedCallbacks.length)try{this._reconnectedCallbacks.forEach((e=>e.apply(this,[this.connection.connectionId])))}catch(s){this._logger.log(v.Error,"An onreconnected callback called with connectionId '".concat(this.connection.connectionId,"; threw error '").concat(s,"'."))}return}catch(s){if(this._logger.log(v.Information,"Reconnect attempt failed because of error '".concat(s,"'.")),this._connectionState!==G.Reconnecting)return this._logger.log(v.Debug,"Connection moved to the '".concat(this._connectionState,"' from the reconnecting state during reconnect attempt. Done reconnecting.")),void(this._connectionState===G.Disconnecting&&this._completeClose());o=s instanceof Error?s:new Error(s.toString()),r=this._getNextRetryDelay(n++,Date.now()-t,o)}}this._logger.log(v.Information,"Reconnect retries have been exhausted after ".concat(Date.now()-t," ms and ").concat(n," failed attempts. Connection disconnecting.")),this._completeClose()}_getNextRetryDelay(e,t,n){try{return this._reconnectPolicy.nextRetryDelayInMilliseconds({elapsedMilliseconds:t,previousRetryCount:e,retryReason:n})}catch(o){return this._logger.log(v.Error,"IRetryPolicy.nextRetryDelayInMilliseconds(".concat(e,", ").concat(t,") threw error '").concat(o,"'.")),null}}_cancelCallbacksWithError(e){const t=this._callbacks;this._callbacks={},Object.keys(t).forEach((n=>{const o=t[n];try{o(null,e)}catch(r){this._logger.log(v.Error,"Stream 'error' callback called with '".concat(e,"' threw error: ").concat(A(r)))}}))}_cleanupPingTimer(){this._pingServerHandle&&(clearTimeout(this._pingServerHandle),this._pingServerHandle=void 0)}_cleanupTimeout(){this._timeoutHandle&&clearTimeout(this._timeoutHandle)}_createInvocation(e,t,n,o){if(n)return 0!==o.length?{arguments:t,streamIds:o,target:e,type:J.Invocation}:{arguments:t,target:e,type:J.Invocation};{const n=this._invocationId;return this._invocationId++,0!==o.length?{arguments:t,invocationId:n.toString(),streamIds:o,target:e,type:J.Invocation}:{arguments:t,invocationId:n.toString(),target:e,type:J.Invocation}}}_launchStreams(e,t){if(0!==e.length){t||(t=Promise.resolve());for(const n in e)e[n].subscribe({complete:()=>{t=t.then((()=>this._sendWithProtocol(this._createCompletionMessage(n))))},error:e=>{let o;o=e instanceof Error?e.message:e&&e.toString?e.toString():"Unknown error",t=t.then((()=>this._sendWithProtocol(this._createCompletionMessage(n,o))))},next:e=>{t=t.then((()=>this._sendWithProtocol(this._createStreamItemMessage(n,e))))}})}}_replaceStreamingParams(e){const t=[],n=[];for(let o=0;o<e.length;o++){const r=e[o];if(this._isObservable(r)){const s=this._invocationId;this._invocationId++,t[s]=r,n.push(s.toString()),e.splice(o,1)}}return[t,n]}_isObservable(e){return e&&e.subscribe&&"function"===typeof e.subscribe}_createStreamInvocation(e,t,n){const o=this._invocationId;return this._invocationId++,0!==n.length?{arguments:t,invocationId:o.toString(),streamIds:n,target:e,type:J.StreamInvocation}:{arguments:t,invocationId:o.toString(),target:e,type:J.StreamInvocation}}_createCancelInvocation(e){return{invocationId:e,type:J.CancelInvocation}}_createStreamItemMessage(e,t){return{invocationId:e,item:t,type:J.StreamItem}}_createCompletionMessage(e,t,n){return t?{error:t,invocationId:e,type:J.Completion}:{invocationId:e,result:n,type:J.Completion}}_createCloseMessage(){return{type:J.Close}}}class ee{constructor(){this.name="json",this.version=2,this.transferFormat=W.Text}parseMessages(e,t){if("string"!==typeof e)throw new Error("Invalid input for JSON hub protocol. Expected a string.");if(!e)return[];null===t&&(t=w.instance);const n=V.parse(e),o=[];for(const r of n){const e=JSON.parse(r);if("number"!==typeof e.type)throw new Error("Invalid payload.");switch(e.type){case J.Invocation:this._isInvocationMessage(e);break;case J.StreamItem:this._isStreamItemMessage(e);break;case J.Completion:this._isCompletionMessage(e);break;case J.Ping:case J.Close:break;case J.Ack:this._isAckMessage(e);break;case J.Sequence:this._isSequenceMessage(e);break;default:t.log(v.Information,"Unknown message type '"+e.type+"' ignored.");continue}o.push(e)}return o}writeMessage(e){return V.write(JSON.stringify(e))}_isInvocationMessage(e){this._assertNotEmptyString(e.target,"Invalid payload for Invocation message."),void 0!==e.invocationId&&this._assertNotEmptyString(e.invocationId,"Invalid payload for Invocation message.")}_isStreamItemMessage(e){if(this._assertNotEmptyString(e.invocationId,"Invalid payload for StreamItem message."),void 0===e.item)throw new Error("Invalid payload for StreamItem message.")}_isCompletionMessage(e){if(e.result&&e.error)throw new Error("Invalid payload for Completion message.");!e.result&&e.error&&this._assertNotEmptyString(e.error,"Invalid payload for Completion message."),this._assertNotEmptyString(e.invocationId,"Invalid payload for Completion message.")}_isAckMessage(e){if("number"!==typeof e.sequenceId)throw new Error("Invalid SequenceId for Ack message.")}_isSequenceMessage(e){if("number"!==typeof e.sequenceId)throw new Error("Invalid SequenceId for Sequence message.")}_assertNotEmptyString(e,t){if("string"!==typeof e||""===e)throw new Error(t)}}const te={trace:v.Trace,debug:v.Debug,info:v.Information,information:v.Information,warn:v.Warning,warning:v.Warning,error:v.Error,critical:v.Critical,none:v.None};class ne{configureLogging(e){if(b.isRequired(e,"logging"),void 0!==e.log)this.logger=e;else if("string"===typeof e){const t=function(e){const t=te[e.toLowerCase()];if("undefined"!==typeof t)return t;throw new Error("Unknown log level: ".concat(e))}(e);this.logger=new I(t)}else this.logger=new I(e);return this}withUrl(e,t){return b.isRequired(e,"url"),b.isNotEmpty(e,"url"),this.url=e,this.httpConnectionOptions="object"===typeof t?(0,o.A)((0,o.A)({},this.httpConnectionOptions),t):(0,o.A)((0,o.A)({},this.httpConnectionOptions),{},{transport:t}),this}withHubProtocol(e){return b.isRequired(e,"protocol"),this.protocol=e,this}withAutomaticReconnect(e){if(this.reconnectPolicy)throw new Error("A reconnectPolicy has already been set.");return e?Array.isArray(e)?this.reconnectPolicy=new s(e):this.reconnectPolicy=e:this.reconnectPolicy=new s,this}withServerTimeout(e){return b.isRequired(e,"milliseconds"),this._serverTimeoutInMilliseconds=e,this}withKeepAliveInterval(e){return b.isRequired(e,"milliseconds"),this._keepAliveIntervalInMilliseconds=e,this}withStatefulReconnect(e){return void 0===this.httpConnectionOptions&&(this.httpConnectionOptions={}),this.httpConnectionOptions._useStatefulReconnect=!0,this._statefulReconnectBufferSize=null===e||void 0===e?void 0:e.bufferSize,this}build(){const e=this.httpConnectionOptions||{};if(void 0===e.logger&&(e.logger=this.logger),!this.url)throw new Error("The 'HubConnectionBuilder.withUrl' method must be called before building the connection.");const t=new U(this.url,e);return Y.create(t,this.logger||w.instance,this.protocol||new ee,this.reconnectPolicy,this._serverTimeoutInMilliseconds,this._keepAliveIntervalInMilliseconds,this._statefulReconnectBufferSize)}}}}]);
//# sourceMappingURL=566.b77976e6.chunk.js.map
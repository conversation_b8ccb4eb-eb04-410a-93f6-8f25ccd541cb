{"version": 3, "file": "static/js/904.cc99aeed.chunk.js", "mappings": "gSAgDA,MAkbA,EAlb4BA,KAC1B,MAAMC,GAAQC,EAAAA,EAAAA,QAAc,OACrBC,EAAOC,IAAYC,EAAAA,EAAAA,UAAiB,KACpCC,EAAOC,IAAYF,EAAAA,EAAAA,UAAiB,KACpCG,EAASC,IAAcJ,EAAAA,EAAAA,WAAS,IAChCK,EAAYC,IAAiBN,EAAAA,EAAAA,UAAS,KACtCO,EAAoBC,IAAyBR,EAAAA,EAAAA,UAAwB,OACrES,EAAgBC,IAAqBV,EAAAA,EAAAA,WAAS,IAC9CW,EAAaC,IAAkBZ,EAAAA,EAAAA,UAAmC,OAClEa,EAAeC,IAAoBd,EAAAA,EAAAA,UAAmB,KACtDe,EAAYC,IAAiBhB,EAAAA,EAAAA,WAAS,IACvC,cAAEiB,IAAkBC,EAAAA,EAAAA,KAGpBC,EAAYC,UAChB,IACEJ,GAAc,GACdK,EAAAA,GAAIC,IAAI,uCAAU,CAAEjB,aAAYE,uBAEhC,MAAMgB,QAAiBD,EAAAA,EAAIE,IAAI,8BAA+B,CAC5DC,OAAQ,CAAEC,KAAMrB,KAGlB,IAAIsB,EAAgBJ,EAASK,KAGzBrB,IACFoB,EAAgBJ,EAASK,KAAKC,QAAQC,GACpCA,EAAK7B,OAAS6B,EAAK7B,MAAM8B,MAAMC,GAAmBA,EAAKC,SAAW1B,OAItER,EAAS4B,GACTN,EAAAA,GAAIC,IAAI,mDAAY,CAClBY,MAAOX,EAASK,KAAKO,OACrBC,SAAUT,EAAcQ,OACxBE,WAAY9B,GAGhB,CAAE,MAAO+B,GAAa,IAADC,EACnBlB,EAAAA,GAAIiB,MAAM,mDAAYA,GACT,QAAbC,EAAA3C,EAAM4C,eAAO,IAAAD,GAAbA,EAAeE,KAAK,CAClBC,SAAU,QACVC,QAAS,2BACTC,OAAQ,mDACRC,KAAM,KAEV,CAAC,QACCzC,GAAW,GACXY,GAAc,EAChB,IAiGF8B,EAAAA,EAAAA,YAAU,KACR3B,IA9FgBC,WAChB,IACEC,EAAAA,GAAIC,IAAI,wCAER,MAAMC,QAAiBD,EAAAA,EAAIE,IAAI,uBAC/BtB,EAASqB,EAASK,MAElBP,EAAAA,GAAIC,IAAI,mDAAY,CAAEyB,MAAOxB,EAASK,KAAKO,QAE7C,CAAE,MAAOG,GAAa,IAADU,EACnB3B,EAAAA,GAAIiB,MAAM,mDAAYA,GACT,QAAbU,EAAApD,EAAM4C,eAAO,IAAAQ,GAAbA,EAAeP,KAAK,CAClBC,SAAU,QACVC,QAAS,2BACTC,OAAQ,mDACRC,KAAM,KAEV,GA8EAI,KAEC,KAGHH,EAAAA,EAAAA,YAAU,KACJ7C,EAAMkC,OAAS,GACjBhB,MAGD,CAACZ,IAGJ,MAAM2C,EAAeA,KACnB/B,KA+EIgC,EAAuBlD,EAAMmD,KAAIpB,IAAI,CACzCqB,MAAOrB,EAAKsB,KACZC,MAAOvB,EAAKwB,OAGRC,GACFC,EAAAA,EAAAA,KAACC,EAAAA,EAAM,CACHC,KAAK,SACLC,KAAK,gBACLC,MAAI,EACJC,QAASA,IAAM5C,MAGjB6C,GAAiBN,EAAAA,EAAAA,KAAA,UAEvB,OAAIvD,GAEAuD,EAAAA,EAAAA,KAAA,OAAKO,UAAU,8DAA6DC,UAC1EC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,cAAaC,SAAA,EAC1BR,EAAAA,EAAAA,KAACU,EAAAA,EAAe,KAChBV,EAAAA,EAAAA,KAAA,KAAGO,UAAU,OAAMC,SAAC,wDAO1BC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,aAAYC,SAAA,EACzBR,EAAAA,EAAAA,KAACW,EAAAA,EAAK,CAACC,IAAK1E,KAEZ8D,EAAAA,EAAAA,KAACa,EAAAA,EAAI,CAACC,MAAM,uCAASP,UAAU,OAAMC,UACnCR,EAAAA,EAAAA,KAAA,KAAGO,UAAU,6BAA4BC,SAAC,sUAM5CR,EAAAA,EAAAA,KAACa,EAAAA,EAAI,CAACN,UAAU,OAAMC,UACpBC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,OAAMC,SAAA,EACnBR,EAAAA,EAAAA,KAAA,OAAKO,UAAU,iBAAgBC,UAC7BC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,eAAcC,SAAA,EAC3BR,EAAAA,EAAAA,KAACe,EAAAA,EAAS,CACRC,YAAY,uCACZnB,MAAOlD,EACPsE,SAAWC,GAAMtE,EAAcsE,EAAEC,OAAOtB,OACxCuB,UAAYF,GAAgB,UAAVA,EAAEG,KAAmB7B,OAEzCQ,EAAAA,EAAAA,KAACC,EAAAA,EAAM,CACLE,KAAK,eACLE,QAASb,EACT8B,SAAUjE,UAIhB2C,EAAAA,EAAAA,KAAA,OAAKO,UAAU,iBAAgBC,UAC7BR,EAAAA,EAAAA,KAACuB,EAAAA,EAAQ,CACP1B,MAAOhD,EACP2E,QAAS,CACP,CAAE7B,MAAO,2BAAQE,MAAO,SACrBtD,EAAMmD,KAAIpB,IAAI,CAAOqB,MAAOrB,EAAKsB,KAAMC,MAAOvB,EAAKwB,QAExDmB,SAAWC,GAAMpE,EAAsBoE,EAAErB,OACzCmB,YAAY,2BACZT,UAAU,SACVkB,WAAS,OAGbzB,EAAAA,EAAAA,KAAA,OAAKO,UAAU,kBAAiBC,UAC9BR,EAAAA,EAAAA,KAAA,OAAKO,UAAU,aAAYC,UACzBR,EAAAA,EAAAA,KAACC,EAAAA,EAAM,CACHN,MAAM,2BACNY,UAAU,kBACVJ,KAAK,cACLE,QApJM3C,UACpB,IAAK,IAADgE,QACI9D,EAAAA,EAAIE,IAAI,6BACD,QAAb4D,EAAAxF,EAAM4C,eAAO,IAAA4C,GAAbA,EAAe3C,KAAK,CAClBC,SAAU,UACVC,QAAS,2BACTC,OAAQ,iCACRC,KAAM,KAEV,CAAE,MAAOP,GAAa,IAAD+C,EACnBhE,EAAAA,GAAIiB,MAAM,uCAAUA,GACP,QAAb+C,EAAAzF,EAAM4C,eAAO,IAAA6C,GAAbA,EAAe5C,KAAK,CAClBC,SAAU,QACVC,QAAS,2BACTC,OAAQ,uCACRC,KAAM,KAEV,eA2IEa,EAAAA,EAAAA,KAACa,EAAAA,EAAI,CAAAL,UACHC,EAAAA,EAAAA,MAACmB,EAAAA,EAAS,CACR/B,MAAOzD,EACPyF,WAAS,EACTC,KAAM,GACNC,mBAAoB,CAAC,EAAG,GAAI,GAAI,IAChCC,SAAS,WACTC,eAAa,EACbC,cAAc,OACdC,mBAAoB,CAAC,WAAY,cAAe,aAChDC,aAAa,mDACb7B,UAAU,wBACVR,cAAeA,EACfO,eAAgBA,EAAeE,SAAA,EAE/BR,EAAAA,EAAAA,KAACqC,EAAAA,EAAM,CACLC,MAAM,WACNC,OAAO,2BACPC,UAAQ,EACRrE,QAAM,EACNsE,kBAAkB,2BAClBC,MAAO,CAAEC,SAAU,YAErB3C,EAAAA,EAAAA,KAACqC,EAAAA,EAAM,CACLC,MAAM,cACNC,OAAO,eACPC,UAAQ,EACRrE,QAAM,EACNsE,kBAAkB,2BAClBC,MAAO,CAAEC,SAAU,YAErB3C,EAAAA,EAAAA,KAACqC,EAAAA,EAAM,CACLC,MAAM,YACNC,OAAO,QACPC,UAAQ,EACRrE,QAAM,EACNsE,kBAAkB,oBAClBC,MAAO,CAAEC,SAAU,YAErB3C,EAAAA,EAAAA,KAACqC,EAAAA,EAAM,CACLE,OAAO,2BACPK,KAhLiBC,GAEpBA,EAAQtG,OAAUuG,MAAMC,QAAQF,EAAQtG,QAAmC,IAAzBsG,EAAQtG,MAAMkC,QAWnEuB,EAAAA,EAAAA,KAAA,OAAKO,UAAU,uBAAsBC,SAClCqC,EAAQtG,MAAMmD,KAAKpB,IAClB0B,EAAAA,EAAAA,KAACgD,EAAAA,EAAG,CAEFnD,MAAOvB,EAAK2E,SACZjE,SAAS,OACTuB,UAAU,WAHLjC,EAAKC,aAZdyB,EAAAA,EAAAA,KAACgD,EAAAA,EAAG,CACFnD,MAAM,qBACNb,SAAS,UACTuB,UAAU,YA0KRmC,MAAO,CAAEC,SAAU,YAErB3C,EAAAA,EAAAA,KAACqC,EAAAA,EAAM,CACLC,MAAM,YACNC,OAAO,eACPK,KA1IkBC,IAExB7C,EAAAA,EAAAA,KAACgD,EAAAA,EAAG,CACFnD,MAAOgD,EAAQK,UAAY,eAAO,eAClClE,SAAU6D,EAAQK,UAAY,UAAY,WAuItCV,UAAQ,EACRE,MAAO,CAAEC,SAAU,YAErB3C,EAAAA,EAAAA,KAACqC,EAAAA,EAAM,CACLE,OAAO,eACPK,KAjKkBC,IAExB7C,EAAAA,EAAAA,KAAA,OAAKO,UAAU,aAAYC,SACxBjD,EAAc,qBACbyC,EAAAA,EAAAA,KAACC,EAAAA,EAAM,CACLE,KAAK,eACLI,UAAU,mBACV4C,KAAK,QACL9C,QAASA,IApJS3C,WAC1B,IAEEC,EAAAA,GAAIC,IAAI,uCAAU,CAAEwF,OAAQhF,EAAKgF,SAEjC,MACMC,SADiBzF,EAAAA,EAAIE,IAAI,2BAADwF,OAA4BlF,EAAKgF,UACrClF,KAE1BhB,EAAe,CACbkG,OAAQC,EAASD,OACjBG,SAAUF,EAASE,SACnBC,YAAaH,EAASG,YACtBjH,MAAO8G,EAAS9G,OAAS,KAI3B,MAAMA,EAAQ8G,EAAS9G,OAAS,GAChCa,EAAiBb,EAAMmD,KAAKpB,GAAmBA,EAAKC,UACpDvB,GAAkB,GAElBW,EAAAA,GAAIC,IAAI,mDAAYyF,EAEtB,CAAE,MAAOzE,GAAa,IAAD6E,EACnB9F,EAAAA,GAAIiB,MAAM,mDAAYA,GACT,QAAb6E,EAAAvH,EAAM4C,eAAO,IAAA2E,GAAbA,EAAe1E,KAAK,CAClBC,SAAU,QACVC,QAAS,2BACTC,OAAQ,+DACRC,KAAM,KAEV,GAsHuBuE,CAAoBb,GACnClD,MAAM,mBAyJN+C,MAAO,CAAEC,SAAU,iBAMzB3C,EAAAA,EAAAA,KAAC2D,EAAAA,EAAM,CACLpB,OAAM,sDAAAe,OAA2B,OAAXrG,QAAW,IAAXA,OAAW,EAAXA,EAAasG,UACnCK,QAAS7G,EACT2F,MAAO,CAAEmB,MAAO,SAChBC,OAAQA,IAAM9G,GAAkB,GAChC+G,QACEtD,EAAAA,EAAAA,MAAA,OAAKF,UAAU,iCAAgCC,SAAA,EAC7CR,EAAAA,EAAAA,KAACC,EAAAA,EAAM,CACLN,MAAM,eACNQ,KAAK,cACLE,QAASA,IAAMrD,GAAkB,GACjCuD,UAAU,mBAEZP,EAAAA,EAAAA,KAACC,EAAAA,EAAM,CACLN,MAAM,eACNQ,KAAK,cACLE,QAlSgB3C,UAC1B,GAAKT,EAEL,IAAK,IAAD+G,EACFrG,EAAAA,GAAIC,IAAI,uCAAU,CAChBwF,OAAQnG,EAAYmG,OACpBa,QAAS9G,UAGLS,EAAAA,EAAIsG,IAAI,6BAA8B,CAC1CC,OAAQlH,EAAYmG,OACpBgB,QAASjH,IAGE,QAAb6G,EAAA9H,EAAM4C,eAAO,IAAAkF,GAAbA,EAAejF,KAAK,CAClBC,SAAU,UACVC,QAAS,2BACTC,OAAQ,yDACRC,KAAM,MAGRnC,GAAkB,GAClBS,IAEAE,EAAAA,GAAIC,IAAI,mDAEV,CAAE,MAAOgB,GAAa,IAADyF,EACnB1G,EAAAA,GAAIiB,MAAM,mDAAYA,GACT,QAAbyF,EAAAnI,EAAM4C,eAAO,IAAAuF,GAAbA,EAAetF,KAAK,CAClBC,SAAU,QACVC,QAAS,2BACTC,OAAQ,+DACRC,KAAM,KAEV,GAiQUoB,UAAU,wBAGfC,SAEAvD,IACCwD,EAAAA,EAAAA,MAAA,OAAKF,UAAU,OAAMC,SAAA,EACnBR,EAAAA,EAAAA,KAAA,OAAKO,UAAU,SAAQC,UACrBC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,QAAOC,SAAA,EACpBR,EAAAA,EAAAA,KAAA,SAAOO,UAAU,YAAWC,SAAC,+BAC7BR,EAAAA,EAAAA,KAAA,KAAGO,UAAU,MAAKC,SAAEvD,EAAYuG,oBAGpCxD,EAAAA,EAAAA,KAAA,OAAKO,UAAU,SAAQC,UACrBC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,QAAOC,SAAA,EACpBR,EAAAA,EAAAA,KAAA,SAAOsE,QAAQ,QAAQ/D,UAAU,YAAWC,SAAC,+BAC7CR,EAAAA,EAAAA,KAACuE,EAAAA,EAAW,CACVC,GAAG,QACH3E,MAAO1C,EACPqE,QAAS/B,EACTwB,SAAWC,GAAM9D,EAAiB8D,EAAErB,OACpCmB,YAAY,uCACZT,UAAU,SACVkE,QAAQ,sB", "sources": ["components/Page/UsersPage.tsx"], "sourcesContent": ["import { But<PERSON> } from 'primereact/button';\nimport { Column } from 'primereact/column';\nimport { DataTable } from 'primereact/datatable';\nimport { Dialog } from 'primereact/dialog';\nimport { Dropdown } from 'primereact/dropdown';\nimport { InputText } from 'primereact/inputtext';\nimport { Toast } from 'primereact/toast';\nimport { Tag } from 'primereact/tag';\nimport { MultiSelect } from 'primereact/multiselect';\nimport { Card } from 'primereact/card';\nimport { ProgressSpinner } from 'primereact/progressspinner';\nimport React, { useRef, useState, useEffect } from 'react';\nimport api from '../../services/api';\nimport { log } from '../../utils/logger';\nimport { usePermissions } from '../../hooks/usePermissions';\n\ninterface Role {\n  Id: number;\n  Name: string;\n}\n\ninterface UserRole {\n  roleId: number;\n  roleName: string;\n}\n\ninterface User {\n  userId: number;\n  userName: string;\n  userAccount: string;\n  userEmail?: string;\n  userPhone?: string;\n  address?: string;\n  gender?: string;\n  birthDate?: string;\n  isEnabled: boolean;\n  createdAt: string;\n  updatedAt: string;\n  roles?: UserRole[];\n}\n\ninterface EditUserRolesData {\n  userId: number;\n  userName: string;\n  userAccount: string;\n  roles: UserRole[];\n}\n\nconst UsersPage: React.FC = () => {\n  const toast = useRef<Toast>(null);\n  const [users, setUsers] = useState<User[]>([]);\n  const [roles, setRoles] = useState<Role[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [searchName, setSearchName] = useState('');\n  const [selectedRoleFilter, setSelectedRoleFilter] = useState<number | null>(null);\n  const [showEditDialog, setShowEditDialog] = useState(false);\n  const [editingUser, setEditingUser] = useState<EditUserRolesData | null>(null);\n  const [selectedRoles, setSelectedRoles] = useState<number[]>([]);\n  const [refreshing, setRefreshing] = useState(false);\n  const { hasPermission } = usePermissions();\n\n  // 載入用戶列表\n  const loadUsers = async () => {\n    try {\n      setRefreshing(true);\n      log.api('載入用戶列表', { searchName, selectedRoleFilter });\n\n      const response = await api.get('/api/users/GetUserRolesList', {\n        params: { name: searchName }\n      });\n\n      let filteredUsers = response.data;\n\n      // 如果選擇了角色篩選，則進行前端篩選\n      if (selectedRoleFilter) {\n        filteredUsers = response.data.filter((user: User) =>\n          user.roles && user.roles.some((role: UserRole) => role.roleId === selectedRoleFilter)\n        );\n      }\n\n      setUsers(filteredUsers);\n      log.api('用戶列表載入成功', {\n        total: response.data.length,\n        filtered: filteredUsers.length,\n        roleFilter: selectedRoleFilter\n      });\n\n    } catch (error: any) {\n      log.error('載入用戶列表失敗', error);\n      toast.current?.show({\n        severity: 'error',\n        summary: '載入失敗',\n        detail: '無法載入用戶列表',\n        life: 5000\n      });\n    } finally {\n      setLoading(false);\n      setRefreshing(false);\n    }\n  };\n\n  // 載入角色列表\n  const loadRoles = async () => {\n    try {\n      log.api('載入角色列表');\n      \n      const response = await api.get('/api/users/GetRoles');\n      setRoles(response.data);\n      \n      log.api('角色列表載入成功', { count: response.data.length });\n      \n    } catch (error: any) {\n      log.error('載入角色列表失敗', error);\n      toast.current?.show({\n        severity: 'error',\n        summary: '載入失敗',\n        detail: '無法載入角色列表',\n        life: 5000\n      });\n    }\n  };\n\n  // 打開編輯對話框\n  const handleEditUserRoles = async (user: User) => {\n    try {\n      \n      log.api('載入用戶角色', { userId: user.userId });\n\n      const response = await api.get(`/api/users/GetUserRoles/${user.userId}`);\n      const userData = response.data;\n      \n      setEditingUser({\n        userId: userData.userId,\n        userName: userData.userName,\n        userAccount: userData.userAccount,\n        roles: userData.roles || []\n      });\n\n      // 安全檢查：確保 Roles 存在且為陣列\n      const roles = userData.roles || [];\n      setSelectedRoles(roles.map((role: UserRole) => role.roleId));\n      setShowEditDialog(true);\n      \n      log.api('用戶角色載入成功', userData);\n      \n    } catch (error: any) {\n      log.error('載入用戶角色失敗', error);\n      toast.current?.show({\n        severity: 'error',\n        summary: '載入失敗',\n        detail: '無法載入用戶角色資訊',\n        life: 5000\n      });\n    }\n  };\n\n  // 保存用戶角色\n  const handleSaveUserRoles = async () => {\n    if (!editingUser) return;\n    \n    try {\n      log.api('更新用戶角色', { \n        userId: editingUser.userId, \n        roleIds: selectedRoles \n      });\n      \n      await api.put('/api/users/UpdateUserRoles', {\n        UserId: editingUser.userId,\n        RoleIds: selectedRoles\n      });\n      \n      toast.current?.show({\n        severity: 'success',\n        summary: '更新成功',\n        detail: '用戶角色權限已更新',\n        life: 3000\n      });\n      \n      setShowEditDialog(false);\n      loadUsers(); // 重新載入用戶列表\n      \n      log.api('用戶角色更新成功');\n      \n    } catch (error: any) {\n      log.error('更新用戶角色失敗', error);\n      toast.current?.show({\n        severity: 'error',\n        summary: '更新失敗',\n        detail: '無法更新用戶角色權限',\n        life: 5000\n      });\n    }\n  };\n\n  // 初始化載入\n  useEffect(() => {\n    loadUsers();\n    loadRoles();\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, []);\n\n  // 監聽角色篩選變化\n  useEffect(() => {\n    if (roles.length > 0) {\n      loadUsers();\n    }\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [selectedRoleFilter]);\n\n  // 搜索處理\n  const handleSearch = () => {\n    loadUsers();\n  };\n\n    // 重置緩存\n  const resetMemCache = async () => {\n    try {\n      await api.get('/api/system/ResetMemCache');\n      toast.current?.show({\n        severity: 'success',\n        summary: '重置成功',\n        detail: '緩存已重置',\n        life: 3000\n      });\n    } catch (error: any) {\n      log.error('重置緩存失敗', error);\n      toast.current?.show({\n        severity: 'error',\n        summary: '重置失敗',\n        detail: '無法重置緩存',\n        life: 5000\n      });\n    } \n  };\n\n  // 角色標籤模板\n  const rolesBodyTemplate = (rowData: User) => {\n    // 安全檢查：確保 Roles 存在且為陣列\n    if (!rowData.roles || !Array.isArray(rowData.roles) || rowData.roles.length === 0) {\n      return (\n        <Tag\n          value=\"無角色\"\n          severity=\"warning\"\n          className=\"text-sm\"\n        />\n      );\n    }\n\n    return (\n      <div className=\"flex flex-wrap gap-1\">\n        {rowData.roles.map((role: UserRole) => (\n          <Tag\n            key={role.roleId}\n            value={role.roleName}\n            severity=\"info\"\n            className=\"text-sm\"\n          />\n        ))}\n      </div>\n    );\n  };\n\n  // 操作按鈕模板\n  const actionBodyTemplate = (rowData: User) => {\n    return (\n      <div className=\"flex gap-2\">\n        {hasPermission('userroles.write') && (\n          <Button\n            icon=\"pi pi-pencil\"\n            className=\"p-button-success\"\n            size=\"small\" \n            onClick={() => handleEditUserRoles(rowData)}\n            label=\"編輯\"\n          />\n        )}\n      </div>\n    );\n  };\n\n  // 狀態模板\n  const statusBodyTemplate = (rowData: User) => {\n    return (\n      <Tag \n        value={rowData.isEnabled ? '啟用' : '停用'} \n        severity={rowData.isEnabled ? 'success' : 'danger'}\n      />\n    );\n  };\n\n  // 可選角色選項\n  const availableRoleOptions = roles.map(role => ({\n    label: role.Name,\n    value: role.Id\n  }));\n\n  const paginatorLeft = (\n      <Button\n          type=\"button\"\n          icon=\"pi pi-refresh\"\n          text\n          onClick={() => loadUsers()}\n      />\n  );\n  const paginatorRight = <div></div>;\n\n  if (loading) {\n    return (\n      <div className=\"flex align-items-center justify-content-center min-h-screen\">\n        <div className=\"text-center\">\n          <ProgressSpinner />\n          <p className=\"mt-3\">載入用戶資料中...</p>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"users-page\">\n      <Toast ref={toast} />\n      \n      <Card title=\"用戶權限管理\" className=\"mb-4\">\n        <p className=\"text-600 line-height-3 m-0\">\n          管理系統的用戶權限，包括分配角色和查看用戶詳細信息。您可以編輯用戶的角色權限以控制其在系統中的訪問和操作。\n        </p>\n      </Card>\n      \n      {/* 搜尋條件 */}\n      <Card className=\"mb-4\">\n        <div className=\"grid\">\n          <div className=\"col-6 md:col-4\">\n            <div className=\"p-inputgroup\">\n              <InputText\n                placeholder=\"搜尋用戶名稱\"\n                value={searchName}\n                onChange={(e) => setSearchName(e.target.value)}\n                onKeyDown={(e) => e.key === 'Enter' && handleSearch()}\n              />\n              <Button\n                icon=\"pi pi-search\"\n                onClick={handleSearch}\n                disabled={refreshing}\n              />\n            </div>\n          </div>\n          <div className=\"col-6 md:col-4\">\n            <Dropdown\n              value={selectedRoleFilter}\n              options={[\n                { label: '全部角色', value: null },\n                ...roles.map(role => ({ label: role.Name, value: role.Id }))\n              ]}\n              onChange={(e) => setSelectedRoleFilter(e.value)}\n              placeholder=\"篩選角色\"\n              className=\"w-full\"\n              showClear\n            />\n          </div>\n          <div className=\"col-6 md:col-4 \">\n            <div className=\"flex gap-2\">\n              <Button\n                  label=\"緩存重置\"\n                  className=\"p-button-danger\"\n                  icon=\"pi pi-trash\"\n                  onClick={resetMemCache}\n                />\n            </div>\n          </div>\n        </div>\n      </Card>\n\n      {/* 用戶列表 */}\n      <Card>\n        <DataTable\n          value={users}\n          paginator\n          rows={10}\n          rowsPerPageOptions={[5, 10, 25, 50]}\n          sortMode=\"multiple\"\n          removableSort\n          filterDisplay=\"menu\"\n          globalFilterFields={['userName', 'userAccount', 'userEmail']}\n          emptyMessage=\"沒有找到用戶資料\"\n          className=\"p-datatable-gridlines\"\n          paginatorLeft={paginatorLeft}\n          paginatorRight={paginatorRight}\n        >\n          <Column\n            field=\"userName\"\n            header=\"用戶名稱\"\n            sortable\n            filter\n            filterPlaceholder=\"搜尋名稱\"\n            style={{ minWidth: '150px' }}\n          />\n          <Column\n            field=\"userAccount\"\n            header=\"帳號\"\n            sortable\n            filter\n            filterPlaceholder=\"搜尋帳號\"\n            style={{ minWidth: '120px' }}\n          />\n          <Column\n            field=\"userEmail\"\n            header=\"Email\"\n            sortable\n            filter\n            filterPlaceholder=\"搜尋Email\"\n            style={{ minWidth: '200px' }}\n          />\n          <Column\n            header=\"角色權限\"\n            body={rolesBodyTemplate}\n            style={{ minWidth: '200px' }}\n          />\n          <Column\n            field=\"isEnabled\"\n            header=\"狀態\"\n            body={statusBodyTemplate}\n            sortable\n            style={{ minWidth: '100px' }}\n          />\n          <Column\n            header=\"操作\"\n            body={actionBodyTemplate}\n            style={{ minWidth: '100px' }}\n          />\n        </DataTable>\n      </Card>\n\n      {/* 編輯角色對話框 */}\n      <Dialog\n        header={`編輯用戶角色權限 - ${editingUser?.userName}`}\n        visible={showEditDialog}\n        style={{ width: '500px' }}\n        onHide={() => setShowEditDialog(false)}\n        footer={\n          <div className=\"flex justify-content-end gap-2\">\n            <Button\n              label=\"取消\"\n              icon=\"pi pi-times\"\n              onClick={() => setShowEditDialog(false)}\n              className=\"p-button-text\"\n            />\n            <Button\n              label=\"保存\"\n              icon=\"pi pi-check\"\n              onClick={handleSaveUserRoles}\n              className=\"p-button-primary\"\n            />\n          </div>\n        }\n      >\n        {editingUser && (\n          <div className=\"grid\">\n            <div className=\"col-12\">\n              <div className=\"field\">\n                <label className=\"font-bold\">用戶帳號:</label>\n                <p className=\"m-0\">{editingUser.userAccount}</p>\n              </div>\n            </div>\n            <div className=\"col-12\">\n              <div className=\"field\">\n                <label htmlFor=\"roles\" className=\"font-bold\">角色權限:</label>\n                <MultiSelect\n                  id=\"roles\"\n                  value={selectedRoles}\n                  options={availableRoleOptions}\n                  onChange={(e) => setSelectedRoles(e.value)}\n                  placeholder=\"選擇角色權限\"\n                  className=\"w-full\"\n                  display=\"chip\"\n                />\n              </div>\n            </div>\n          </div>\n        )}\n      </Dialog>\n    </div>\n  );\n};\n\nexport default UsersPage;\n"], "names": ["UsersPage", "toast", "useRef", "users", "setUsers", "useState", "roles", "setRoles", "loading", "setLoading", "searchName", "setSearchName", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "setSelectedRoleFilter", "showEditDialog", "setShowEditDialog", "editingUser", "setEditingUser", "selectedRoles", "setSelectedRoles", "refreshing", "setRefreshing", "hasPermission", "usePermissions", "loadUsers", "async", "log", "api", "response", "get", "params", "name", "filteredUsers", "data", "filter", "user", "some", "role", "roleId", "total", "length", "filtered", "<PERSON><PERSON><PERSON>er", "error", "_toast$current", "current", "show", "severity", "summary", "detail", "life", "useEffect", "count", "_toast$current2", "loadRoles", "handleSearch", "availableRoleOptions", "map", "label", "Name", "value", "Id", "paginatorLeft", "_jsx", "<PERSON><PERSON>", "type", "icon", "text", "onClick", "paginatorRight", "className", "children", "_jsxs", "ProgressSpinner", "Toast", "ref", "Card", "title", "InputText", "placeholder", "onChange", "e", "target", "onKeyDown", "key", "disabled", "Dropdown", "options", "showClear", "_toast$current6", "_toast$current7", "DataTable", "paginator", "rows", "rowsPerPageOptions", "sortMode", "removableSort", "filterDisplay", "globalFilterFields", "emptyMessage", "Column", "field", "header", "sortable", "filterPlaceholder", "style", "min<PERSON><PERSON><PERSON>", "body", "rowData", "Array", "isArray", "Tag", "<PERSON><PERSON><PERSON>", "isEnabled", "size", "userId", "userData", "concat", "userName", "userAccount", "_toast$current3", "handleEditUserRoles", "Dialog", "visible", "width", "onHide", "footer", "_toast$current4", "roleIds", "put", "UserId", "RoleIds", "_toast$current5", "htmlFor", "MultiSelect", "id", "display"], "sourceRoot": ""}
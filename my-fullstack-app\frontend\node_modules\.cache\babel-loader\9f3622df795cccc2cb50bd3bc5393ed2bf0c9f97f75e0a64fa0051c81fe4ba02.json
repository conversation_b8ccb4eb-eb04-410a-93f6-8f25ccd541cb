{"ast": null, "code": "import { buildLocalizeFn } from \"../../_lib/buildLocalizeFn.js\";\n\n// Source: https://www.unicode.org/cldr/charts/32/summary/te.html\n// Source: https://dsal.uchicago.edu/dictionaries/brown/\n\n// CLDR #1605 - #1608\nconst eraValues = {\n  narrow: [\"క్రీ.పూ.\", \"క్రీ.శ.\"],\n  abbreviated: [\"క్రీ.పూ.\", \"క్రీ.శ.\"],\n  wide: [\"క్రీస్తు పూర్వం\", \"క్రీస్తుశకం\"]\n};\n\n// CLDR #1613 - #1628\nconst quarterValues = {\n  narrow: [\"1\", \"2\", \"3\", \"4\"],\n  abbreviated: [\"త్రై1\", \"త్రై2\", \"త్రై3\", \"త్రై4\"],\n  wide: [\"1వ త్రైమాసికం\", \"2వ త్రైమాసికం\", \"3వ త్రైమాసికం\", \"4వ త్రైమాసికం\"]\n};\n\n// CLDR #1637 - #1708\nconst monthValues = {\n  narrow: [\"జ\", \"ఫి\", \"మా\", \"ఏ\", \"మే\", \"జూ\", \"జు\", \"ఆ\", \"సె\", \"అ\", \"న\", \"డి\"],\n  abbreviated: [\"జన\", \"ఫిబ్ర\", \"మార్చి\", \"ఏప్రి\", \"మే\", \"జూన్\", \"జులై\", \"ఆగ\", \"సెప్టెం\", \"అక్టో\", \"నవం\", \"డిసెం\"],\n  wide: [\"జనవరి\", \"ఫిబ్రవరి\", \"మార్చి\", \"ఏప్రిల్\", \"మే\", \"జూన్\", \"జులై\", \"ఆగస్టు\", \"సెప్టెంబర్\", \"అక్టోబర్\", \"నవంబర్\", \"డిసెంబర్\"]\n};\n\n// CLDR #1709 - #1764\nconst dayValues = {\n  narrow: [\"ఆ\", \"సో\", \"మ\", \"బు\", \"గు\", \"శు\", \"శ\"],\n  short: [\"ఆది\", \"సోమ\", \"మంగళ\", \"బుధ\", \"గురు\", \"శుక్ర\", \"శని\"],\n  abbreviated: [\"ఆది\", \"సోమ\", \"మంగళ\", \"బుధ\", \"గురు\", \"శుక్ర\", \"శని\"],\n  wide: [\"ఆదివారం\", \"సోమవారం\", \"మంగళవారం\", \"బుధవారం\", \"గురువారం\", \"శుక్రవారం\", \"శనివారం\"]\n};\n\n// CLDR #1767 - #1806\nconst dayPeriodValues = {\n  narrow: {\n    am: \"పూర్వాహ్నం\",\n    pm: \"అపరాహ్నం\",\n    midnight: \"అర్ధరాత్రి\",\n    noon: \"మిట్టమధ్యాహ్నం\",\n    morning: \"ఉదయం\",\n    afternoon: \"మధ్యాహ్నం\",\n    evening: \"సాయంత్రం\",\n    night: \"రాత్రి\"\n  },\n  abbreviated: {\n    am: \"పూర్వాహ్నం\",\n    pm: \"అపరాహ్నం\",\n    midnight: \"అర్ధరాత్రి\",\n    noon: \"మిట్టమధ్యాహ్నం\",\n    morning: \"ఉదయం\",\n    afternoon: \"మధ్యాహ్నం\",\n    evening: \"సాయంత్రం\",\n    night: \"రాత్రి\"\n  },\n  wide: {\n    am: \"పూర్వాహ్నం\",\n    pm: \"అపరాహ్నం\",\n    midnight: \"అర్ధరాత్రి\",\n    noon: \"మిట్టమధ్యాహ్నం\",\n    morning: \"ఉదయం\",\n    afternoon: \"మధ్యాహ్నం\",\n    evening: \"సాయంత్రం\",\n    night: \"రాత్రి\"\n  }\n};\nconst formattingDayPeriodValues = {\n  narrow: {\n    am: \"పూర్వాహ్నం\",\n    pm: \"అపరాహ్నం\",\n    midnight: \"అర్ధరాత్రి\",\n    noon: \"మిట్టమధ్యాహ్నం\",\n    morning: \"ఉదయం\",\n    afternoon: \"మధ్యాహ్నం\",\n    evening: \"సాయంత్రం\",\n    night: \"రాత్రి\"\n  },\n  abbreviated: {\n    am: \"పూర్వాహ్నం\",\n    pm: \"అపరాహ్నం\",\n    midnight: \"అర్ధరాత్రి\",\n    noon: \"మిట్టమధ్యాహ్నం\",\n    morning: \"ఉదయం\",\n    afternoon: \"మధ్యాహ్నం\",\n    evening: \"సాయంత్రం\",\n    night: \"రాత్రి\"\n  },\n  wide: {\n    am: \"పూర్వాహ్నం\",\n    pm: \"అపరాహ్నం\",\n    midnight: \"అర్ధరాత్రి\",\n    noon: \"మిట్టమధ్యాహ్నం\",\n    morning: \"ఉదయం\",\n    afternoon: \"మధ్యాహ్నం\",\n    evening: \"సాయంత్రం\",\n    night: \"రాత్రి\"\n  }\n};\nconst ordinalNumber = (dirtyNumber, _options) => {\n  const number = Number(dirtyNumber);\n  return number + \"వ\";\n};\nexport const localize = {\n  ordinalNumber,\n  era: buildLocalizeFn({\n    values: eraValues,\n    defaultWidth: \"wide\"\n  }),\n  quarter: buildLocalizeFn({\n    values: quarterValues,\n    defaultWidth: \"wide\",\n    argumentCallback: quarter => quarter - 1\n  }),\n  month: buildLocalizeFn({\n    values: monthValues,\n    defaultWidth: \"wide\"\n  }),\n  day: buildLocalizeFn({\n    values: dayValues,\n    defaultWidth: \"wide\"\n  }),\n  dayPeriod: buildLocalizeFn({\n    values: dayPeriodValues,\n    defaultWidth: \"wide\",\n    formattingValues: formattingDayPeriodValues,\n    defaultFormattingWidth: \"wide\"\n  })\n};", "map": {"version": 3, "names": ["buildLocalizeFn", "<PERSON><PERSON><PERSON><PERSON>", "narrow", "abbreviated", "wide", "quarterValues", "month<PERSON><PERSON><PERSON>", "dayV<PERSON><PERSON>", "short", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "am", "pm", "midnight", "noon", "morning", "afternoon", "evening", "night", "formattingDayPeriodValues", "ordinalNumber", "dirtyNumber", "_options", "number", "Number", "localize", "era", "values", "defaultWidth", "quarter", "argument<PERSON>allback", "month", "day", "<PERSON><PERSON><PERSON><PERSON>", "formattingValues", "defaultFormattingWidth"], "sources": ["C:/Users/<USER>/Desktop/demo-git/demo-react/my-fullstack-app/frontend/node_modules/date-fns/locale/te/_lib/localize.js"], "sourcesContent": ["import { buildLocalizeFn } from \"../../_lib/buildLocalizeFn.js\";\n\n// Source: https://www.unicode.org/cldr/charts/32/summary/te.html\n// Source: https://dsal.uchicago.edu/dictionaries/brown/\n\n// CLDR #1605 - #1608\nconst eraValues = {\n  narrow: [\"క్రీ.పూ.\", \"క్రీ.శ.\"],\n  abbreviated: [\"క్రీ.పూ.\", \"క్రీ.శ.\"],\n  wide: [\"క్రీస్తు పూర్వం\", \"క్రీస్తుశకం\"],\n};\n\n// CLDR #1613 - #1628\nconst quarterValues = {\n  narrow: [\"1\", \"2\", \"3\", \"4\"],\n  abbreviated: [\"త్రై1\", \"త్రై2\", \"త్రై3\", \"త్రై4\"],\n  wide: [\"1వ త్రైమాసికం\", \"2వ త్రైమాసికం\", \"3వ త్రైమాసికం\", \"4వ త్రైమాసికం\"],\n};\n\n// CLDR #1637 - #1708\nconst monthValues = {\n  narrow: [\"జ\", \"ఫి\", \"మా\", \"ఏ\", \"మే\", \"జూ\", \"జు\", \"ఆ\", \"సె\", \"అ\", \"న\", \"డి\"],\n\n  abbreviated: [\n    \"జన\",\n    \"ఫిబ్ర\",\n    \"మార్చి\",\n    \"ఏప్రి\",\n    \"మే\",\n    \"జూన్\",\n    \"జులై\",\n    \"ఆగ\",\n    \"సెప్టెం\",\n    \"అక్టో\",\n    \"నవం\",\n    \"డిసెం\",\n  ],\n\n  wide: [\n    \"జనవరి\",\n    \"ఫిబ్రవరి\",\n    \"మార్చి\",\n    \"ఏప్రిల్\",\n    \"మే\",\n    \"జూన్\",\n    \"జులై\",\n    \"ఆగస్టు\",\n    \"సెప్టెంబర్\",\n    \"అక్టోబర్\",\n    \"నవంబర్\",\n    \"డిసెంబర్\",\n  ],\n};\n\n// CLDR #1709 - #1764\nconst dayValues = {\n  narrow: [\"ఆ\", \"సో\", \"మ\", \"బు\", \"గు\", \"శు\", \"శ\"],\n  short: [\"ఆది\", \"సోమ\", \"మంగళ\", \"బుధ\", \"గురు\", \"శుక్ర\", \"శని\"],\n  abbreviated: [\"ఆది\", \"సోమ\", \"మంగళ\", \"బుధ\", \"గురు\", \"శుక్ర\", \"శని\"],\n  wide: [\n    \"ఆదివారం\",\n    \"సోమవారం\",\n    \"మంగళవారం\",\n    \"బుధవారం\",\n    \"గురువారం\",\n    \"శుక్రవారం\",\n    \"శనివారం\",\n  ],\n};\n\n// CLDR #1767 - #1806\nconst dayPeriodValues = {\n  narrow: {\n    am: \"పూర్వాహ్నం\",\n    pm: \"అపరాహ్నం\",\n    midnight: \"అర్ధరాత్రి\",\n    noon: \"మిట్టమధ్యాహ్నం\",\n    morning: \"ఉదయం\",\n    afternoon: \"మధ్యాహ్నం\",\n    evening: \"సాయంత్రం\",\n    night: \"రాత్రి\",\n  },\n  abbreviated: {\n    am: \"పూర్వాహ్నం\",\n    pm: \"అపరాహ్నం\",\n    midnight: \"అర్ధరాత్రి\",\n    noon: \"మిట్టమధ్యాహ్నం\",\n    morning: \"ఉదయం\",\n    afternoon: \"మధ్యాహ్నం\",\n    evening: \"సాయంత్రం\",\n    night: \"రాత్రి\",\n  },\n  wide: {\n    am: \"పూర్వాహ్నం\",\n    pm: \"అపరాహ్నం\",\n    midnight: \"అర్ధరాత్రి\",\n    noon: \"మిట్టమధ్యాహ్నం\",\n    morning: \"ఉదయం\",\n    afternoon: \"మధ్యాహ్నం\",\n    evening: \"సాయంత్రం\",\n    night: \"రాత్రి\",\n  },\n};\n\nconst formattingDayPeriodValues = {\n  narrow: {\n    am: \"పూర్వాహ్నం\",\n    pm: \"అపరాహ్నం\",\n    midnight: \"అర్ధరాత్రి\",\n    noon: \"మిట్టమధ్యాహ్నం\",\n    morning: \"ఉదయం\",\n    afternoon: \"మధ్యాహ్నం\",\n    evening: \"సాయంత్రం\",\n    night: \"రాత్రి\",\n  },\n  abbreviated: {\n    am: \"పూర్వాహ్నం\",\n    pm: \"అపరాహ్నం\",\n    midnight: \"అర్ధరాత్రి\",\n    noon: \"మిట్టమధ్యాహ్నం\",\n    morning: \"ఉదయం\",\n    afternoon: \"మధ్యాహ్నం\",\n    evening: \"సాయంత్రం\",\n    night: \"రాత్రి\",\n  },\n  wide: {\n    am: \"పూర్వాహ్నం\",\n    pm: \"అపరాహ్నం\",\n    midnight: \"అర్ధరాత్రి\",\n    noon: \"మిట్టమధ్యాహ్నం\",\n    morning: \"ఉదయం\",\n    afternoon: \"మధ్యాహ్నం\",\n    evening: \"సాయంత్రం\",\n    night: \"రాత్రి\",\n  },\n};\n\nconst ordinalNumber = (dirtyNumber, _options) => {\n  const number = Number(dirtyNumber);\n  return number + \"వ\";\n};\n\nexport const localize = {\n  ordinalNumber,\n\n  era: buildLocalizeFn({\n    values: eraValues,\n    defaultWidth: \"wide\",\n  }),\n\n  quarter: buildLocalizeFn({\n    values: quarterValues,\n    defaultWidth: \"wide\",\n    argumentCallback: (quarter) => quarter - 1,\n  }),\n\n  month: buildLocalizeFn({\n    values: monthValues,\n    defaultWidth: \"wide\",\n  }),\n\n  day: buildLocalizeFn({\n    values: dayValues,\n    defaultWidth: \"wide\",\n  }),\n\n  dayPeriod: buildLocalizeFn({\n    values: dayPeriodValues,\n    defaultWidth: \"wide\",\n    formattingValues: formattingDayPeriodValues,\n    defaultFormattingWidth: \"wide\",\n  }),\n};\n"], "mappings": "AAAA,SAASA,eAAe,QAAQ,+BAA+B;;AAE/D;AACA;;AAEA;AACA,MAAMC,SAAS,GAAG;EAChBC,MAAM,EAAE,CAAC,UAAU,EAAE,SAAS,CAAC;EAC/BC,WAAW,EAAE,CAAC,UAAU,EAAE,SAAS,CAAC;EACpCC,IAAI,EAAE,CAAC,iBAAiB,EAAE,aAAa;AACzC,CAAC;;AAED;AACA,MAAMC,aAAa,GAAG;EACpBH,MAAM,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EAC5BC,WAAW,EAAE,CAAC,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,CAAC;EACjDC,IAAI,EAAE,CAAC,eAAe,EAAE,eAAe,EAAE,eAAe,EAAE,eAAe;AAC3E,CAAC;;AAED;AACA,MAAME,WAAW,GAAG;EAClBJ,MAAM,EAAE,CAAC,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,EAAE,IAAI,EAAE,GAAG,EAAE,GAAG,EAAE,IAAI,CAAC;EAE3EC,WAAW,EAAE,CACX,IAAI,EACJ,OAAO,EACP,QAAQ,EACR,OAAO,EACP,IAAI,EACJ,MAAM,EACN,MAAM,EACN,IAAI,EACJ,SAAS,EACT,OAAO,EACP,KAAK,EACL,OAAO,CACR;EAEDC,IAAI,EAAE,CACJ,OAAO,EACP,UAAU,EACV,QAAQ,EACR,SAAS,EACT,IAAI,EACJ,MAAM,EACN,MAAM,EACN,QAAQ,EACR,YAAY,EACZ,UAAU,EACV,QAAQ,EACR,UAAU;AAEd,CAAC;;AAED;AACA,MAAMG,SAAS,GAAG;EAChBL,MAAM,EAAE,CAAC,GAAG,EAAE,IAAI,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,CAAC;EAC/CM,KAAK,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,OAAO,EAAE,KAAK,CAAC;EAC5DL,WAAW,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,OAAO,EAAE,KAAK,CAAC;EAClEC,IAAI,EAAE,CACJ,SAAS,EACT,SAAS,EACT,UAAU,EACV,SAAS,EACT,UAAU,EACV,WAAW,EACX,SAAS;AAEb,CAAC;;AAED;AACA,MAAMK,eAAe,GAAG;EACtBP,MAAM,EAAE;IACNQ,EAAE,EAAE,YAAY;IAChBC,EAAE,EAAE,UAAU;IACdC,QAAQ,EAAE,YAAY;IACtBC,IAAI,EAAE,gBAAgB;IACtBC,OAAO,EAAE,MAAM;IACfC,SAAS,EAAE,WAAW;IACtBC,OAAO,EAAE,UAAU;IACnBC,KAAK,EAAE;EACT,CAAC;EACDd,WAAW,EAAE;IACXO,EAAE,EAAE,YAAY;IAChBC,EAAE,EAAE,UAAU;IACdC,QAAQ,EAAE,YAAY;IACtBC,IAAI,EAAE,gBAAgB;IACtBC,OAAO,EAAE,MAAM;IACfC,SAAS,EAAE,WAAW;IACtBC,OAAO,EAAE,UAAU;IACnBC,KAAK,EAAE;EACT,CAAC;EACDb,IAAI,EAAE;IACJM,EAAE,EAAE,YAAY;IAChBC,EAAE,EAAE,UAAU;IACdC,QAAQ,EAAE,YAAY;IACtBC,IAAI,EAAE,gBAAgB;IACtBC,OAAO,EAAE,MAAM;IACfC,SAAS,EAAE,WAAW;IACtBC,OAAO,EAAE,UAAU;IACnBC,KAAK,EAAE;EACT;AACF,CAAC;AAED,MAAMC,yBAAyB,GAAG;EAChChB,MAAM,EAAE;IACNQ,EAAE,EAAE,YAAY;IAChBC,EAAE,EAAE,UAAU;IACdC,QAAQ,EAAE,YAAY;IACtBC,IAAI,EAAE,gBAAgB;IACtBC,OAAO,EAAE,MAAM;IACfC,SAAS,EAAE,WAAW;IACtBC,OAAO,EAAE,UAAU;IACnBC,KAAK,EAAE;EACT,CAAC;EACDd,WAAW,EAAE;IACXO,EAAE,EAAE,YAAY;IAChBC,EAAE,EAAE,UAAU;IACdC,QAAQ,EAAE,YAAY;IACtBC,IAAI,EAAE,gBAAgB;IACtBC,OAAO,EAAE,MAAM;IACfC,SAAS,EAAE,WAAW;IACtBC,OAAO,EAAE,UAAU;IACnBC,KAAK,EAAE;EACT,CAAC;EACDb,IAAI,EAAE;IACJM,EAAE,EAAE,YAAY;IAChBC,EAAE,EAAE,UAAU;IACdC,QAAQ,EAAE,YAAY;IACtBC,IAAI,EAAE,gBAAgB;IACtBC,OAAO,EAAE,MAAM;IACfC,SAAS,EAAE,WAAW;IACtBC,OAAO,EAAE,UAAU;IACnBC,KAAK,EAAE;EACT;AACF,CAAC;AAED,MAAME,aAAa,GAAGA,CAACC,WAAW,EAAEC,QAAQ,KAAK;EAC/C,MAAMC,MAAM,GAAGC,MAAM,CAACH,WAAW,CAAC;EAClC,OAAOE,MAAM,GAAG,GAAG;AACrB,CAAC;AAED,OAAO,MAAME,QAAQ,GAAG;EACtBL,aAAa;EAEbM,GAAG,EAAEzB,eAAe,CAAC;IACnB0B,MAAM,EAAEzB,SAAS;IACjB0B,YAAY,EAAE;EAChB,CAAC,CAAC;EAEFC,OAAO,EAAE5B,eAAe,CAAC;IACvB0B,MAAM,EAAErB,aAAa;IACrBsB,YAAY,EAAE,MAAM;IACpBE,gBAAgB,EAAGD,OAAO,IAAKA,OAAO,GAAG;EAC3C,CAAC,CAAC;EAEFE,KAAK,EAAE9B,eAAe,CAAC;IACrB0B,MAAM,EAAEpB,WAAW;IACnBqB,YAAY,EAAE;EAChB,CAAC,CAAC;EAEFI,GAAG,EAAE/B,eAAe,CAAC;IACnB0B,MAAM,EAAEnB,SAAS;IACjBoB,YAAY,EAAE;EAChB,CAAC,CAAC;EAEFK,SAAS,EAAEhC,eAAe,CAAC;IACzB0B,MAAM,EAAEjB,eAAe;IACvBkB,YAAY,EAAE,MAAM;IACpBM,gBAAgB,EAAEf,yBAAyB;IAC3CgB,sBAAsB,EAAE;EAC1B,CAAC;AACH,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
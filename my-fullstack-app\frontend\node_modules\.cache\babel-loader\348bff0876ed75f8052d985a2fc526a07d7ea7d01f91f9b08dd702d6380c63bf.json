{"ast": null, "code": "const formatDistanceLocale = {\n  lessThanXSeconds: {\n    one: \"1びょうみまん\",\n    other: \"{{count}}びょうみまん\",\n    oneWithSuffix: \"やく1びょう\",\n    otherWithSuffix: \"やく{{count}}びょう\"\n  },\n  xSeconds: {\n    one: \"1びょう\",\n    other: \"{{count}}びょう\"\n  },\n  halfAMinute: \"30びょう\",\n  lessThanXMinutes: {\n    one: \"1ぷんみまん\",\n    other: \"{{count}}ふんみまん\",\n    oneWithSuffix: \"やく1ぷん\",\n    otherWithSuffix: \"やく{{count}}ふん\"\n  },\n  xMinutes: {\n    one: \"1ぷん\",\n    other: \"{{count}}ふん\"\n  },\n  aboutXHours: {\n    one: \"やく1じかん\",\n    other: \"やく{{count}}じかん\"\n  },\n  xHours: {\n    one: \"1じかん\",\n    other: \"{{count}}じかん\"\n  },\n  xDays: {\n    one: \"1にち\",\n    other: \"{{count}}にち\"\n  },\n  aboutXWeeks: {\n    one: \"やく1しゅうかん\",\n    other: \"やく{{count}}しゅうかん\"\n  },\n  xWeeks: {\n    one: \"1しゅうかん\",\n    other: \"{{count}}しゅうかん\"\n  },\n  aboutXMonths: {\n    one: \"やく1かげつ\",\n    other: \"やく{{count}}かげつ\"\n  },\n  xMonths: {\n    one: \"1かげつ\",\n    other: \"{{count}}かげつ\"\n  },\n  aboutXYears: {\n    one: \"やく1ねん\",\n    other: \"やく{{count}}ねん\"\n  },\n  xYears: {\n    one: \"1ねん\",\n    other: \"{{count}}ねん\"\n  },\n  overXYears: {\n    one: \"1ねんいじょう\",\n    other: \"{{count}}ねんいじょう\"\n  },\n  almostXYears: {\n    one: \"1ねんちかく\",\n    other: \"{{count}}ねんちかく\"\n  }\n};\nexport const formatDistance = (token, count, options) => {\n  options = options || {};\n  let result;\n  const tokenValue = formatDistanceLocale[token];\n  if (typeof tokenValue === \"string\") {\n    result = tokenValue;\n  } else if (count === 1) {\n    if (options.addSuffix && tokenValue.oneWithSuffix) {\n      result = tokenValue.oneWithSuffix;\n    } else {\n      result = tokenValue.one;\n    }\n  } else {\n    if (options.addSuffix && tokenValue.otherWithSuffix) {\n      result = tokenValue.otherWithSuffix.replace(\"{{count}}\", String(count));\n    } else {\n      result = tokenValue.other.replace(\"{{count}}\", String(count));\n    }\n  }\n  if (options.addSuffix) {\n    if (options.comparison && options.comparison > 0) {\n      return result + \"あと\";\n    } else {\n      return result + \"まえ\";\n    }\n  }\n  return result;\n};", "map": {"version": 3, "names": ["formatDistanceLocale", "lessThanXSeconds", "one", "other", "oneWithSuffix", "otherWithSuffix", "xSeconds", "halfAMinute", "lessThanXMinutes", "xMinutes", "aboutXHours", "xHours", "xDays", "aboutXWeeks", "xWeeks", "aboutXMonths", "xMonths", "aboutXYears", "xYears", "overXYears", "almostXYears", "formatDistance", "token", "count", "options", "result", "tokenValue", "addSuffix", "replace", "String", "comparison"], "sources": ["C:/Users/<USER>/Desktop/demo-git/demo-react/my-fullstack-app/frontend/node_modules/date-fns/locale/ja-Hira/_lib/formatDistance.js"], "sourcesContent": ["const formatDistanceLocale = {\n  lessThanXSeconds: {\n    one: \"1びょうみまん\",\n    other: \"{{count}}びょうみまん\",\n    oneWithSuffix: \"やく1びょう\",\n    otherWithSuffix: \"やく{{count}}びょう\",\n  },\n\n  xSeconds: {\n    one: \"1びょう\",\n    other: \"{{count}}びょう\",\n  },\n\n  halfAMinute: \"30びょう\",\n\n  lessThanXMinutes: {\n    one: \"1ぷんみまん\",\n    other: \"{{count}}ふんみまん\",\n    oneWithSuffix: \"やく1ぷん\",\n    otherWithSuffix: \"やく{{count}}ふん\",\n  },\n\n  xMinutes: {\n    one: \"1ぷん\",\n    other: \"{{count}}ふん\",\n  },\n\n  aboutXHours: {\n    one: \"やく1じかん\",\n    other: \"やく{{count}}じかん\",\n  },\n\n  xHours: {\n    one: \"1じかん\",\n    other: \"{{count}}じかん\",\n  },\n\n  xDays: {\n    one: \"1にち\",\n    other: \"{{count}}にち\",\n  },\n\n  aboutXWeeks: {\n    one: \"やく1しゅうかん\",\n    other: \"やく{{count}}しゅうかん\",\n  },\n\n  xWeeks: {\n    one: \"1しゅうかん\",\n    other: \"{{count}}しゅうかん\",\n  },\n\n  aboutXMonths: {\n    one: \"やく1かげつ\",\n    other: \"やく{{count}}かげつ\",\n  },\n\n  xMonths: {\n    one: \"1かげつ\",\n    other: \"{{count}}かげつ\",\n  },\n\n  aboutXYears: {\n    one: \"やく1ねん\",\n    other: \"やく{{count}}ねん\",\n  },\n\n  xYears: {\n    one: \"1ねん\",\n    other: \"{{count}}ねん\",\n  },\n\n  overXYears: {\n    one: \"1ねんいじょう\",\n    other: \"{{count}}ねんいじょう\",\n  },\n\n  almostXYears: {\n    one: \"1ねんちかく\",\n    other: \"{{count}}ねんちかく\",\n  },\n};\n\nexport const formatDistance = (token, count, options) => {\n  options = options || {};\n\n  let result;\n\n  const tokenValue = formatDistanceLocale[token];\n  if (typeof tokenValue === \"string\") {\n    result = tokenValue;\n  } else if (count === 1) {\n    if (options.addSuffix && tokenValue.oneWithSuffix) {\n      result = tokenValue.oneWithSuffix;\n    } else {\n      result = tokenValue.one;\n    }\n  } else {\n    if (options.addSuffix && tokenValue.otherWithSuffix) {\n      result = tokenValue.otherWithSuffix.replace(\"{{count}}\", String(count));\n    } else {\n      result = tokenValue.other.replace(\"{{count}}\", String(count));\n    }\n  }\n\n  if (options.addSuffix) {\n    if (options.comparison && options.comparison > 0) {\n      return result + \"あと\";\n    } else {\n      return result + \"まえ\";\n    }\n  }\n\n  return result;\n};\n"], "mappings": "AAAA,MAAMA,oBAAoB,GAAG;EAC3BC,gBAAgB,EAAE;IAChBC,GAAG,EAAE,SAAS;IACdC,KAAK,EAAE,iBAAiB;IACxBC,aAAa,EAAE,QAAQ;IACvBC,eAAe,EAAE;EACnB,CAAC;EAEDC,QAAQ,EAAE;IACRJ,GAAG,EAAE,MAAM;IACXC,KAAK,EAAE;EACT,CAAC;EAEDI,WAAW,EAAE,OAAO;EAEpBC,gBAAgB,EAAE;IAChBN,GAAG,EAAE,QAAQ;IACbC,KAAK,EAAE,gBAAgB;IACvBC,aAAa,EAAE,OAAO;IACtBC,eAAe,EAAE;EACnB,CAAC;EAEDI,QAAQ,EAAE;IACRP,GAAG,EAAE,KAAK;IACVC,KAAK,EAAE;EACT,CAAC;EAEDO,WAAW,EAAE;IACXR,GAAG,EAAE,QAAQ;IACbC,KAAK,EAAE;EACT,CAAC;EAEDQ,MAAM,EAAE;IACNT,GAAG,EAAE,MAAM;IACXC,KAAK,EAAE;EACT,CAAC;EAEDS,KAAK,EAAE;IACLV,GAAG,EAAE,KAAK;IACVC,KAAK,EAAE;EACT,CAAC;EAEDU,WAAW,EAAE;IACXX,GAAG,EAAE,UAAU;IACfC,KAAK,EAAE;EACT,CAAC;EAEDW,MAAM,EAAE;IACNZ,GAAG,EAAE,QAAQ;IACbC,KAAK,EAAE;EACT,CAAC;EAEDY,YAAY,EAAE;IACZb,GAAG,EAAE,QAAQ;IACbC,KAAK,EAAE;EACT,CAAC;EAEDa,OAAO,EAAE;IACPd,GAAG,EAAE,MAAM;IACXC,KAAK,EAAE;EACT,CAAC;EAEDc,WAAW,EAAE;IACXf,GAAG,EAAE,OAAO;IACZC,KAAK,EAAE;EACT,CAAC;EAEDe,MAAM,EAAE;IACNhB,GAAG,EAAE,KAAK;IACVC,KAAK,EAAE;EACT,CAAC;EAEDgB,UAAU,EAAE;IACVjB,GAAG,EAAE,SAAS;IACdC,KAAK,EAAE;EACT,CAAC;EAEDiB,YAAY,EAAE;IACZlB,GAAG,EAAE,QAAQ;IACbC,KAAK,EAAE;EACT;AACF,CAAC;AAED,OAAO,MAAMkB,cAAc,GAAGA,CAACC,KAAK,EAAEC,KAAK,EAAEC,OAAO,KAAK;EACvDA,OAAO,GAAGA,OAAO,IAAI,CAAC,CAAC;EAEvB,IAAIC,MAAM;EAEV,MAAMC,UAAU,GAAG1B,oBAAoB,CAACsB,KAAK,CAAC;EAC9C,IAAI,OAAOI,UAAU,KAAK,QAAQ,EAAE;IAClCD,MAAM,GAAGC,UAAU;EACrB,CAAC,MAAM,IAAIH,KAAK,KAAK,CAAC,EAAE;IACtB,IAAIC,OAAO,CAACG,SAAS,IAAID,UAAU,CAACtB,aAAa,EAAE;MACjDqB,MAAM,GAAGC,UAAU,CAACtB,aAAa;IACnC,CAAC,MAAM;MACLqB,MAAM,GAAGC,UAAU,CAACxB,GAAG;IACzB;EACF,CAAC,MAAM;IACL,IAAIsB,OAAO,CAACG,SAAS,IAAID,UAAU,CAACrB,eAAe,EAAE;MACnDoB,MAAM,GAAGC,UAAU,CAACrB,eAAe,CAACuB,OAAO,CAAC,WAAW,EAAEC,MAAM,CAACN,KAAK,CAAC,CAAC;IACzE,CAAC,MAAM;MACLE,MAAM,GAAGC,UAAU,CAACvB,KAAK,CAACyB,OAAO,CAAC,WAAW,EAAEC,MAAM,CAACN,KAAK,CAAC,CAAC;IAC/D;EACF;EAEA,IAAIC,OAAO,CAACG,SAAS,EAAE;IACrB,IAAIH,OAAO,CAACM,UAAU,IAAIN,OAAO,CAACM,UAAU,GAAG,CAAC,EAAE;MAChD,OAAOL,MAAM,GAAG,IAAI;IACtB,CAAC,MAAM;MACL,OAAOA,MAAM,GAAG,IAAI;IACtB;EACF;EAEA,OAAOA,MAAM;AACf,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
import React, { useEffect, useState } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { ROUTES } from '../../constants/routes';
import LoadingSpinner from '../Common/LoadingSpinner';

interface PasswordCheckRouteProps {
  children: React.ReactNode;
}

const PasswordCheckRoute: React.FC<PasswordCheckRouteProps> = ({ children }) => {
  const navigate = useNavigate();
  const location = useLocation();
  const [isChecking, setIsChecking] = useState(true);
  const [shouldRender, setShouldRender] = useState(false);

  useEffect(() => {
    const checkPasswordStatus = async () => {
      try {
        const token = localStorage.getItem('token');
        const userId = localStorage.getItem('userId');

        // 如果沒有登入信息，不渲染內容（讓 ProtectedRoute 處理跳轉）
        if (!token || !userId) {
          console.log('PasswordCheckRoute: 沒有登入信息，不渲染內容');
          setShouldRender(false);
          setIsChecking(false);
          return;
        }

        // 如果當前已經在更新密碼頁面，直接渲染
        if (location.pathname === ROUTES.UPDATE_PASSWORD) {
          console.log('PasswordCheckRoute: 當前在更新密碼頁面，直接渲染');
          setShouldRender(true);
          setIsChecking(false);
          return;
        }

        // 檢查用戶是否使用預設密碼
        // 從 localStorage 中讀取登入時儲存的密碼狀態
        const isDefaultPassword = localStorage.getItem('isDefaultPassword');

        if (isDefaultPassword === 'true') {
          console.log('PasswordCheckRoute: 檢測到使用預設密碼，導航到更新密碼頁面');
          navigate(ROUTES.UPDATE_PASSWORD);
          return;
        }

        // 密碼已更新，可以正常渲染頁面
        console.log('PasswordCheckRoute: 密碼已更新，正常渲染頁面');
        setShouldRender(true);

      } catch (error) {
        console.error('PasswordCheckRoute: 檢查密碼狀態失敗:', error);
        // 發生錯誤時，不渲染內容
        setShouldRender(false);
      } finally {
        setIsChecking(false);
      }
    };

    checkPasswordStatus();
  }, [navigate, location.pathname]);

  if (isChecking) {
    return <LoadingSpinner message="檢查用戶狀態中..." />;
  }

  if (!shouldRender) {
    return null;
  }

  return <>{children}</>;
};

export default PasswordCheckRoute;

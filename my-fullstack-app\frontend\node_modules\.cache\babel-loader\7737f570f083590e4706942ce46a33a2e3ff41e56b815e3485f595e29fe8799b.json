{"ast": null, "code": "import{<PERSON>rowser<PERSON>outer,Route,Routes}from\"react-router-dom\";import{ROUTES}from'./constants/routes';import{AuthProvider}from'./contexts/AuthContext';import{componentMap}from'./routes/componentMap';import PasswordCheckRoute from'./components/Auth/PasswordCheckRoute';import ProtectedRoute from'./components/Auth/ProtectedRoute';import Layout from'./components/Layout/Layout';import HomePage from'./components/Page/HomePage';import LoginPage from'./components/Page/LoginPage';import UpdatePasswordPage from'./components/Page/UpdatePasswordPage';import ErrorPage from'./components/Page/ErrorPage';import ErrorFallback from'./components/Common/ErrorFallback';import ErrorBoundary from'./components/Common/ErrorBoundary';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";function AppContent(){return/*#__PURE__*/_jsx(\"div\",{className:\"App\",children:/*#__PURE__*/_jsxs(Routes,{children:[/*#__PURE__*/_jsx(Route,{path:ROUTES.LOGIN,element:/*#__PURE__*/_jsx(LoginPage,{})}),/*#__PURE__*/_jsx(Route,{path:\"/error\",element:/*#__PURE__*/_jsx(ErrorPage,{})}),/*#__PURE__*/_jsx(Route,{path:ROUTES.UPDATE_PASSWORD,element:/*#__PURE__*/_jsx(ProtectedRoute,{children:/*#__PURE__*/_jsx(UpdatePasswordPage,{})})}),/*#__PURE__*/_jsx(Route,{path:ROUTES.HOME,element:/*#__PURE__*/_jsx(ProtectedRoute,{children:/*#__PURE__*/_jsx(PasswordCheckRoute,{children:/*#__PURE__*/_jsx(Layout,{children:/*#__PURE__*/_jsx(HomePage,{})})})})}),Object.entries(componentMap).map(_ref=>{let[path,Component]=_ref;return/*#__PURE__*/_jsx(Route,{path:path,element:/*#__PURE__*/_jsx(ProtectedRoute,{children:/*#__PURE__*/_jsx(PasswordCheckRoute,{children:/*#__PURE__*/_jsx(Layout,{children:/*#__PURE__*/_jsx(Component,{})})})})},path);})]})});}export default function App(){return/*#__PURE__*/_jsx(ErrorBoundary,{fallback:ErrorFallback,children:/*#__PURE__*/_jsx(AuthProvider,{children:/*#__PURE__*/_jsx(BrowserRouter,{children:/*#__PURE__*/_jsx(AppContent,{})})})});}", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Route", "Routes", "ROUTES", "<PERSON>th<PERSON><PERSON><PERSON>", "componentMap", "PasswordCheckRoute", "ProtectedRoute", "Layout", "HomePage", "LoginPage", "UpdatePasswordPage", "ErrorPage", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Error<PERSON>ou<PERSON><PERSON>", "jsx", "_jsx", "jsxs", "_jsxs", "A<PERSON><PERSON><PERSON>nt", "className", "children", "path", "LOGIN", "element", "UPDATE_PASSWORD", "HOME", "Object", "entries", "map", "_ref", "Component", "App", "fallback"], "sources": ["C:/Users/<USER>/Desktop/demo-git/demo-react/my-fullstack-app/frontend/src/App.tsx"], "sourcesContent": ["import { Browser<PERSON>outer, Route, Routes } from \"react-router-dom\";\r\nimport { ROUTES } from './constants/routes';\r\nimport { AuthProvider } from './contexts/AuthContext';\r\nimport { componentMap } from './routes/componentMap';\r\n\r\nimport PasswordCheckRoute from './components/Auth/PasswordCheckRoute';\r\nimport ProtectedRoute from './components/Auth/ProtectedRoute';\r\nimport Layout from './components/Layout/Layout';\r\nimport HomePage from './components/Page/HomePage';\r\nimport LoginPage from './components/Page/LoginPage';\r\nimport UpdatePasswordPage from './components/Page/UpdatePasswordPage';\r\nimport ErrorPage from './components/Page/ErrorPage';\r\nimport ErrorFallback from './components/Common/ErrorFallback';\r\nimport ErrorBoundary from './components/Common/ErrorBoundary';\r\n\r\nfunction AppContent() {\r\n  return (\r\n    <div className=\"App\">\r\n      <Routes>\r\n        <Route path={ROUTES.LOGIN} element={<LoginPage />} />\r\n        <Route path=\"/error\" element={<ErrorPage />} />\r\n        <Route path={ROUTES.UPDATE_PASSWORD} element={\r\n          <ProtectedRoute>\r\n            <UpdatePasswordPage />\r\n          </ProtectedRoute>\r\n        } />\r\n        <Route path={ROUTES.HOME} element={\r\n          <ProtectedRoute>\r\n            <PasswordCheckRoute>\r\n              <Layout>\r\n                <HomePage />\r\n              </Layout>\r\n            </PasswordCheckRoute>\r\n          </ProtectedRoute>\r\n        } />\r\n        {Object.entries(componentMap).map(([path, Component]) => (\r\n          <Route\r\n            key={path}\r\n            path={path}\r\n            element={\r\n              <ProtectedRoute>\r\n                <PasswordCheckRoute>\r\n                  <Layout>\r\n                    <Component />\r\n                  </Layout>\r\n                </PasswordCheckRoute>\r\n              </ProtectedRoute>\r\n            }\r\n          />\r\n        ))}\r\n      </Routes>\r\n    </div>\r\n  );\r\n}\r\n\r\nexport default function App() {\r\n  return (\r\n    <ErrorBoundary fallback={ErrorFallback}>\r\n      <AuthProvider>\r\n        <BrowserRouter>\r\n          <AppContent />\r\n        </BrowserRouter>\r\n      </AuthProvider>\r\n    </ErrorBoundary>\r\n  );\r\n}\r\n"], "mappings": "AAAA,OAASA,aAAa,CAAEC,KAAK,CAAEC,MAAM,KAAQ,kBAAkB,CAC/D,OAASC,MAAM,KAAQ,oBAAoB,CAC3C,OAASC,YAAY,KAAQ,wBAAwB,CACrD,OAASC,YAAY,KAAQ,uBAAuB,CAEpD,MAAO,CAAAC,kBAAkB,KAAM,sCAAsC,CACrE,MAAO,CAAAC,cAAc,KAAM,kCAAkC,CAC7D,MAAO,CAAAC,MAAM,KAAM,4BAA4B,CAC/C,MAAO,CAAAC,QAAQ,KAAM,4BAA4B,CACjD,MAAO,CAAAC,SAAS,KAAM,6BAA6B,CACnD,MAAO,CAAAC,kBAAkB,KAAM,sCAAsC,CACrE,MAAO,CAAAC,SAAS,KAAM,6BAA6B,CACnD,MAAO,CAAAC,aAAa,KAAM,mCAAmC,CAC7D,MAAO,CAAAC,aAAa,KAAM,mCAAmC,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAE9D,QAAS,CAAAC,UAAUA,CAAA,CAAG,CACpB,mBACEH,IAAA,QAAKI,SAAS,CAAC,KAAK,CAAAC,QAAA,cAClBH,KAAA,CAAChB,MAAM,EAAAmB,QAAA,eACLL,IAAA,CAACf,KAAK,EAACqB,IAAI,CAAEnB,MAAM,CAACoB,KAAM,CAACC,OAAO,cAAER,IAAA,CAACN,SAAS,GAAE,CAAE,CAAE,CAAC,cACrDM,IAAA,CAACf,KAAK,EAACqB,IAAI,CAAC,QAAQ,CAACE,OAAO,cAAER,IAAA,CAACJ,SAAS,GAAE,CAAE,CAAE,CAAC,cAC/CI,IAAA,CAACf,KAAK,EAACqB,IAAI,CAAEnB,MAAM,CAACsB,eAAgB,CAACD,OAAO,cAC1CR,IAAA,CAACT,cAAc,EAAAc,QAAA,cACbL,IAAA,CAACL,kBAAkB,GAAE,CAAC,CACR,CACjB,CAAE,CAAC,cACJK,IAAA,CAACf,KAAK,EAACqB,IAAI,CAAEnB,MAAM,CAACuB,IAAK,CAACF,OAAO,cAC/BR,IAAA,CAACT,cAAc,EAAAc,QAAA,cACbL,IAAA,CAACV,kBAAkB,EAAAe,QAAA,cACjBL,IAAA,CAACR,MAAM,EAAAa,QAAA,cACLL,IAAA,CAACP,QAAQ,GAAE,CAAC,CACN,CAAC,CACS,CAAC,CACP,CACjB,CAAE,CAAC,CACHkB,MAAM,CAACC,OAAO,CAACvB,YAAY,CAAC,CAACwB,GAAG,CAACC,IAAA,MAAC,CAACR,IAAI,CAAES,SAAS,CAAC,CAAAD,IAAA,oBAClDd,IAAA,CAACf,KAAK,EAEJqB,IAAI,CAAEA,IAAK,CACXE,OAAO,cACLR,IAAA,CAACT,cAAc,EAAAc,QAAA,cACbL,IAAA,CAACV,kBAAkB,EAAAe,QAAA,cACjBL,IAAA,CAACR,MAAM,EAAAa,QAAA,cACLL,IAAA,CAACe,SAAS,GAAE,CAAC,CACP,CAAC,CACS,CAAC,CACP,CACjB,EAVIT,IAWN,CAAC,EACH,CAAC,EACI,CAAC,CACN,CAAC,CAEV,CAEA,cAAe,SAAS,CAAAU,GAAGA,CAAA,CAAG,CAC5B,mBACEhB,IAAA,CAACF,aAAa,EAACmB,QAAQ,CAAEpB,aAAc,CAAAQ,QAAA,cACrCL,IAAA,CAACZ,YAAY,EAAAiB,QAAA,cACXL,IAAA,CAAChB,aAAa,EAAAqB,QAAA,cACZL,IAAA,CAACG,UAAU,GAAE,CAAC,CACD,CAAC,CACJ,CAAC,CACF,CAAC,CAEpB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using MyApi.Data;
using MyApi.Models;
using MyApi.Services;
using Newtonsoft.Json;
using System.Text.Json;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;

namespace MyApi.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class SystemController : ControllerBase
    {
        private readonly AppDbContext _context;
        private readonly IWebHostEnvironment _env;
        private readonly RedisService _redisService;
        private readonly string _uploadPath = Path.Combine(Directory.GetCurrentDirectory(), "uploads");


        public SystemController(AppDbContext context, IWebHostEnvironment env, RedisService redis)
        {
            _context = context;
            _env = env;
            _redisService = redis;
        }

        [Authorize(Roles = "Admin,Manager,User")]
        [HttpGet("GetMenus")]
        public async Task<IActionResult> GetMenus()
        {

            var redisdata = await _redisService.GetStringAsync("MenuList");

            if (redisdata == null)
            {
                var menuData = await _context.MenuGroups
                .Include(g => g.Menus)
                .OrderBy(g => g.SortOrder)
                .Select(g => new
                {
                    groupId = g.Id,
                    groupName = g.Name,
                    groupIcon = g.Icon,
                    menus = g.Menus
                    .OrderBy(g => g.SortOrder)
                    .Select(m => new {
                        itemId = m.Id,
                        path = m.Path,
                        name = m.Name,
                        isEnabled = m.IsEnabled
                    }).ToList()
                })
                .ToListAsync();

                var redisvalue = JsonConvert.SerializeObject(menuData);

                await _redisService.SetStringAsync("MenuList", redisvalue);

                redisdata = redisvalue;
            }

            return Ok(redisdata);

        }

        [Authorize(Roles = "Admin,Manager,User")]
        [HttpGet("GetDataType")]
        public async Task<IActionResult> GetDataType()
        {

            var redisdata = await _redisService.GetStringAsync("DataTypeList");

            if (redisdata == null)
            {
                var dataTypeData = await _context.DataTypeGroups
                .Include(g => g.DataTypes)
                .Select(g => new
                {
                    groupId = g.Id,
                    groupName = g.Name,
                    isEnabled = g.IsEnabled,
                    dataTypes = g.DataTypes.Select(m => new {
                        itemId = m.Id,
                        number = m.Number,
                        name = m.Name,
                        isEnabled = m.IsEnabled
                    }).ToList()
                })
                .ToListAsync();

                var redisvalue = JsonConvert.SerializeObject(dataTypeData);

                await _redisService.SetStringAsync("DataTypeList", redisvalue);

                redisdata = redisvalue;
            }

            return Ok(redisdata);
        }

        [Authorize(Roles = "Admin,Manager,User")]
        [HttpGet("FileList")]
        public IActionResult GetFileListWithDetail()
        {
            if (!Directory.Exists(_uploadPath))
                return Ok(new List<object>());

            var files = Directory.GetFiles(_uploadPath)
                .Select(filePath =>
                {
                    var info = new FileInfo(filePath);
                    return new
                    {
                        FileName = info.Name,
                        Size = info.Length, // bytes
                        CreatedAt = info.CreationTime
                    };
                })
                .ToList();

            return Ok(files);
        }

        [Authorize(Roles = "Admin,Manager,User")]
        [HttpPost("UploadFile")]
        public async Task<IActionResult> UploadFile([FromForm] IFormFile file, [FromForm] string OrderNo)
        {
            if (file == null || file.Length == 0)
            {
                return BadRequest(new { message = "未選擇檔案" });
            }

            if (string.IsNullOrEmpty(OrderNo))
            {
                return BadRequest(new { message = "缺少單號，請先儲存治療記錄" });
            }

            if (!Directory.Exists(_uploadPath))
            {
                Directory.CreateDirectory(_uploadPath);
            }

            var extension = Path.GetExtension(file.FileName);
            if (string.IsNullOrEmpty(extension))
            {
                return BadRequest(new { message = "檔案缺少副檔名" });
            }

            // 清理 OrderNo，防止路徑遍歷攻擊
            var safeOrderNo = new string(OrderNo.Where(c => !Path.GetInvalidFileNameChars().Contains(c)).ToArray()).Trim();

            var newFileName = $"treatment_{safeOrderNo}_{DateTime.Now:yyyyMMddHHmmss}{extension}";
            var filePath = Path.Combine(_uploadPath, newFileName);

            try
            {
                using (var stream = new FileStream(filePath, FileMode.Create))
                {
                    await file.CopyToAsync(stream);
                }

                return Ok(new { fileName = newFileName, size = file.Length });
            }
            catch (Exception ex)
            {
                // 實際應用中應記錄錯誤日誌
                return StatusCode(500, new { message = $"檔案儲存失敗: {ex.Message}" });
            }
        }

        [Authorize(Roles = "Admin,Manager,User")]
        [HttpGet("DownloadFile")]
        public IActionResult DownloadFile([FromQuery] string filename)
        {
            if (string.IsNullOrEmpty(filename))
                return BadRequest("Filename is required.");

            var filePath = Path.Combine(_uploadPath, filename);

            if (!System.IO.File.Exists(filePath))
                return NotFound("File not found.");

            var fileBytes = System.IO.File.ReadAllBytes(filePath);
            var contentType = GetContentType(filePath);

            return File(fileBytes, contentType, filename);
        }

        private string GetContentType(string path)
        {
            var types = new Dictionary<string, string>(StringComparer.InvariantCultureIgnoreCase)
        {
            { ".txt", "text/plain" },
            { ".pdf", "application/pdf" },
            { ".jpg", "image/jpeg" },
            { ".jpeg", "image/jpeg" },
            { ".png", "image/png" },
            { ".doc", "application/msword" },
            { ".docx", "application/vnd.openxmlformats-officedocument.wordprocessingml.document" },
            { ".xlsx", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet" }
        };

            var ext = Path.GetExtension(path);
            return types.TryGetValue(ext, out string contentType) ? contentType : "application/octet-stream";
        }

        [Authorize(Roles = "Admin,Manager")]
        [HttpDelete("DeleteFile")]
        public IActionResult DeleteFile([FromQuery] string filename)
        {
            if (string.IsNullOrWhiteSpace(filename))
                return BadRequest("檔案名稱不得為空");

            var filePath = Path.Combine(_uploadPath, filename);

            if (!System.IO.File.Exists(filePath))
                return NotFound("找不到指定的檔案");

            try
            {
                System.IO.File.Delete(filePath);
                return Ok("檔案已刪除");
            }
            catch (Exception ex)
            {
                // 實務上可記錄 log
                return StatusCode(500, $"刪除失敗: {ex.Message}");
            }
        }

        [HttpGet("GetMonthSatistics")]
        public async Task<IActionResult> GetMonthSatistics()
        {

            var redisdata = await _redisService.GetStringAsync($"MonthSatistics:{DateTime.Now.Date.ToString("yyyy-MM-dd-HH")}");

            if (redisdata == null)
            {
                // 取得當前日期
                var today = DateTime.Today;
                // 計算六個月前的日期（包含當月，所以是往前推五個月的同一天）
                var sixMonthsAgo = today.AddMonths(-5);

                // 調整為該月的第一天，確保範圍包含整個月份
                sixMonthsAgo = new DateTime(sixMonthsAgo.Year, sixMonthsAgo.Month, 1);

                var schedulesCounts = new Dictionary<string, int>();
                var treatmentsCounts = new Dictionary<string, int>();
                for (int i = 0; i < 6; i++)
                {
                    var date = today.AddMonths(-i);
                    schedulesCounts[$"{date.ToString("MM")}月"] = 0;
                    treatmentsCounts[$"{date.ToString("MM")}月"] = 0;
                }

                var schedules = await _context.Schedules
                    .Where(s => s.StartDateTime >= sixMonthsAgo && s.StartDateTime <= today)
                    .OrderBy(x => x.StartDateTime)
                    .ToListAsync();

                // 在記憶體中進行分組和計數
                var groupedSchedules = schedules
                    .GroupBy(s => new { s.StartDateTime.Month })
                    .Select(g => new
                    {
                        Month = $"{g.Key.Month.ToString("00")}月", // 格式化為 "00"
                        Count = g.Count()
                    })
                    .ToList();

                var treatments = await _context.Treatments
                    .Where(s => s.CreatedAt >= sixMonthsAgo && s.CreatedAt <= today && s.Step >= Helpers.Enums.TreatmentStep.CaseClose)
                    .OrderBy(x => x.CreatedAt)
                    .ToListAsync();

                // 在記憶體中進行分組和計數
                var groupedTreatments = treatments
                    .GroupBy(s => new { s.CreatedAt.Month })
                    .Select(g => new
                    {
                        Month = $"{g.Key.Month.ToString("00")}月", // 格式化為 "MM"
                        Count = g.Count()
                    })
                    .ToList();

                foreach (var group in groupedSchedules)
                {
                    if (schedulesCounts.ContainsKey(group.Month))
                    {
                        schedulesCounts[group.Month] = group.Count;
                    }
                }
                foreach (var group in groupedTreatments)
                {
                    if (treatmentsCounts.ContainsKey(group.Month))
                    {
                        treatmentsCounts[group.Month] = group.Count;
                    }
                }

                schedulesCounts = schedulesCounts.OrderBy(kv => kv.Key).ToDictionary(kv => kv.Key, kv => kv.Value);

                treatmentsCounts = treatmentsCounts.OrderBy(kv => kv.Key).ToDictionary(kv => kv.Key, kv => kv.Value);


                var result = new
                {
                    lable = schedulesCounts.Keys,
                    schedules = schedulesCounts.Values,
                    treatments = treatmentsCounts.Values
                };

                var redisvalue = JsonConvert.SerializeObject(result);

                await _redisService.SetStringWithHoursAsync($"MonthSatistics:{DateTime.Now.Date.ToString("yyyy-MM-dd-HH")}", redisvalue, 6);

                redisdata = redisvalue;
            }

            return Ok(redisdata);

        }

        [HttpGet("GetWeekSatistics")]
        public async Task<IActionResult> GetWeekSatistics()
        {

            var redisdata = await _redisService.GetStringAsync($"WeekSatistics:{DateTime.Now.Date.ToString("yyyy-MM-dd-HH")}");

            if (redisdata == null)
            {
                // 取得當前日期
                var today = DateTime.Today;
                // 計算七天前的日期（包含當天）
                var sixWeekAgo = today.AddDays(-6);

                var schedulesCounts = new Dictionary<string, int>();
                var treatmentsCounts = new Dictionary<string, int>();
                for (int i = 0; i < 7; i++)
                {
                    var date = today.AddDays(-i);
                    schedulesCounts[date.ToString("dd")] = 0;
                    treatmentsCounts[date.ToString("dd")] = 0;
                }

                var schedules = await _context.Schedules
                    .Where(s => s.StartDateTime >= sixWeekAgo && s.StartDateTime <= today)
                    .OrderBy(x => x.StartDateTime)
                    .ToListAsync();

                // 在記憶體中進行分組和計數
                var groupedSchedules = schedules
                    .GroupBy(s => new { s.StartDateTime.Day })
                    .Select(g => new
                    {
                        Week = $"{g.Key.Day.ToString("00")}", // 格式化為 "00"
                        Count = g.Count()
                    })
                    .ToList();

                var treatments = await _context.Treatments
                    .Where(s => s.CreatedAt >= sixWeekAgo && s.CreatedAt <= today && s.Step >= Helpers.Enums.TreatmentStep.CaseClose)
                    .OrderBy(x => x.CreatedAt)
                    .ToListAsync();

                // 在記憶體中進行分組和計數
                var groupedTreatments = treatments
                    .GroupBy(s => new { s.CreatedAt.Day })
                    .Select(g => new
                    {
                        Week = $"{g.Key.Day.ToString("00")}", // 格式化為 "周"
                        Count = g.Count()
                    })
                    .ToList();

                foreach (var group in groupedSchedules)
                {
                    if (schedulesCounts.ContainsKey(group.Week))
                    {
                        schedulesCounts[group.Week] = group.Count;
                    }
                }
                foreach (var group in groupedTreatments)
                {
                    if (treatmentsCounts.ContainsKey(group.Week))
                    {
                        treatmentsCounts[group.Week] = group.Count;
                    }
                }

                schedulesCounts = schedulesCounts.OrderBy(kv => kv.Key).ToDictionary(kv => kv.Key, kv => kv.Value);

                treatmentsCounts = treatmentsCounts.OrderBy(kv => kv.Key).ToDictionary(kv => kv.Key, kv => kv.Value);

                var result = new
                {
                    lable = schedulesCounts.Keys,
                    schedules = schedulesCounts.Values,
                    treatments = treatmentsCounts.Values
                };

                var redisvalue = JsonConvert.SerializeObject(result);

                await _redisService.SetStringWithHoursAsync($"WeekSatistics:{DateTime.Now.Date.ToString("yyyy-MM-dd-HH")}", redisvalue, 6);

                redisdata = redisvalue;
            }

            return Ok(redisdata);

        }

        [Authorize(Roles = "Admin,Manager")]
        [HttpGet("ResetMemCache")]
        public async Task<IActionResult> ResetMemCache()
        {
            string[] keys = {
                "RoleList",
                "PatientList",
                "MenuList",
                "DoctorList",
                "DataTypeList"
            };

            await _redisService.DeleteKeysAsync(keys);

            return Ok();

        }

        #region IP 封鎖管理

        /// <summary>
        /// 添加 IP 封鎖
        /// </summary>
        [Authorize(Roles = "Admin")]
        [HttpPost("AddIpBlock")]
        public async Task<IActionResult> AddIpBlock([FromBody] AddIpBlockRequest request)
        {
            try
            {
                var expiredAt = request.ExpiredAt ?? DateTime.Now.AddDays(3);

                var ipBlock = new IpBlock
                {
                    IPAddress = request.IPAddress,
                    CreatedAt = DateTime.Now,
                    UpdatedAt = DateTime.Now,
                    ExpiredAt = expiredAt
                };

                // 直接添加新記錄（允許同一 IP 有多筆記錄）
                _context.IpBlocks.Add(ipBlock);
                await _context.SaveChangesAsync();

                // 更新 Redis 封鎖列表
                await UpdateIpBlockListInRedis();

                return Ok(new { message = "IP 封鎖已添加", id = ipBlock.Id });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { error = "添加 IP 封鎖失敗", details = ex.Message });
            }
        }

        /// <summary>
        /// 更新 IP 封鎖（解鎖）
        /// </summary>
        [Authorize(Roles = "Admin")]
        [HttpPost("UpdateIpBlock")]
        public async Task<IActionResult> UpdateIpBlock([FromBody] UpdateIpBlockRequest request)
        {
            try
            {
                var ipBlock = await _context.IpBlocks.FindAsync(request.Id);
                if (ipBlock == null)
                {
                    return NotFound(new { error = "找不到指定的 IP 封鎖記錄" });
                }

                // 設定到期時間為現在（解鎖）
                ipBlock.ExpiredAt = DateTime.Now;
                ipBlock.UpdatedAt = DateTime.Now;

                await _context.SaveChangesAsync();

                // 更新 Redis 封鎖列表
                await UpdateIpBlockListInRedis();

                return Ok(new { message = "IP 已解鎖" });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { error = "更新 IP 封鎖失敗", details = ex.Message });
            }
        }

        /// <summary>
        /// 獲取 IP 封鎖列表
        /// </summary>
        [Authorize(Roles = "Admin,Manager")]
        [HttpGet("GetIpBlock")]
        public async Task<IActionResult> GetIpBlock([FromQuery] GetIpBlockRequest request)
        {
            try
            {
                var query = _context.IpBlocks.AsQueryable();

                // 條件篩選
                if (!string.IsNullOrEmpty(request.IPAddress))
                {
                    query = query.Where(x => x.IPAddress.Contains(request.IPAddress));
                }

                if (request.CreatedAtStart.HasValue)
                {
                    query = query.Where(x => x.CreatedAt >= request.CreatedAtStart.Value);
                }

                if (request.CreatedAtEnd.HasValue)
                {
                    query = query.Where(x => x.CreatedAt <= request.CreatedAtEnd.Value);
                }

                // 總數
                var totalCount = await query.CountAsync();

                // 分頁
                var blocks = await query
                    .OrderByDescending(x => x.CreatedAt)
                    .Skip((request.Page - 1) * request.PageSize)
                    .Take(request.PageSize)
                    .Select(x => new IpBlockDto
                    {
                        Id = x.Id,
                        IPAddress = x.IPAddress,
                        CreatedAt = x.CreatedAt,
                        UpdatedAt = x.UpdatedAt,
                        ExpiredAt = x.ExpiredAt
                    })
                    .ToListAsync();

                return Ok(new
                {
                    data = blocks,
                    totalCount = totalCount,
                    page = request.Page,
                    pageSize = request.PageSize,
                    totalPages = (int)Math.Ceiling((double)totalCount / request.PageSize)
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { error = "獲取 IP 封鎖列表失敗", details = ex.Message });
            }
        }

        #endregion

        #region 輔助方法

        private async Task UpdateIpBlockListInRedis()
        {
            try
            {
                var activeBlocks = await _context.IpBlocks
                    .ToListAsync();

                var result = activeBlocks.FindAll(p => p.ExpiredAt > DateTime.Now).Select(p => p.IPAddress);

                var json = System.Text.Json.JsonSerializer.Serialize(result);
                await _redisService.SetAsync("IpBlockList", json);
            }
            catch (Exception ex)
            {
                // 記錄錯誤但不拋出異常
                Console.WriteLine($"更新 Redis IP 封鎖列表失敗: {ex.Message}");
            }
        }

        #endregion

    }

    public class UpdateIpBlockRequest
    {
        public int Id { get; set; }
    }
}

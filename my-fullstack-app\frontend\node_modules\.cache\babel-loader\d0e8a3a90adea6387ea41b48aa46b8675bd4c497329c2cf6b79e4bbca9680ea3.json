{"ast": null, "code": "import { formatDistance } from \"./tr/_lib/formatDistance.js\";\nimport { formatLong } from \"./tr/_lib/formatLong.js\";\nimport { formatRelative } from \"./tr/_lib/formatRelative.js\";\nimport { localize } from \"./tr/_lib/localize.js\";\nimport { match } from \"./tr/_lib/match.js\";\n\n/**\n * @category Locales\n * @summary Turkish locale.\n * @language Turkish\n * @iso-639-2 tur\n * <AUTHOR> [@alpcanaydin](https://github.com/alpcanaydin)\n * <AUTHOR> [@berkaey](https://github.com/berkaey)\n * <AUTHOR> [@bulutfatih](https://github.com/bulutfatih)\n * <AUTHOR> [@dbtek](https://github.com/dbtek)\n * <AUTHOR> [@ikayar](https://github.com/ikayar)\n *\n *\n */\nexport const tr = {\n  code: \"tr\",\n  formatDistance: formatDistance,\n  formatLong: formatLong,\n  formatRelative: formatRelative,\n  localize: localize,\n  match: match,\n  options: {\n    weekStartsOn: 1 /* Monday */,\n    firstWeekContainsDate: 1\n  }\n};\n\n// Fallback for modularized imports:\nexport default tr;", "map": {"version": 3, "names": ["formatDistance", "formatLong", "formatRelative", "localize", "match", "tr", "code", "options", "weekStartsOn", "firstWeekContainsDate"], "sources": ["C:/Users/<USER>/Desktop/demo-git/demo-react/my-fullstack-app/frontend/node_modules/date-fns/locale/tr.js"], "sourcesContent": ["import { formatDistance } from \"./tr/_lib/formatDistance.js\";\nimport { formatLong } from \"./tr/_lib/formatLong.js\";\nimport { formatRelative } from \"./tr/_lib/formatRelative.js\";\nimport { localize } from \"./tr/_lib/localize.js\";\nimport { match } from \"./tr/_lib/match.js\";\n\n/**\n * @category Locales\n * @summary Turkish locale.\n * @language Turkish\n * @iso-639-2 tur\n * <AUTHOR> [@alpcanaydin](https://github.com/alpcanaydin)\n * <AUTHOR> [@berkaey](https://github.com/berkaey)\n * <AUTHOR> [@bulutfatih](https://github.com/bulutfatih)\n * <AUTHOR> [@dbtek](https://github.com/dbtek)\n * <AUTHOR> [@ikayar](https://github.com/ikayar)\n *\n *\n */\nexport const tr = {\n  code: \"tr\",\n  formatDistance: formatDistance,\n  formatLong: formatLong,\n  formatRelative: formatRelative,\n  localize: localize,\n  match: match,\n  options: {\n    weekStartsOn: 1 /* Monday */,\n    firstWeekContainsDate: 1,\n  },\n};\n\n// Fallback for modularized imports:\nexport default tr;\n"], "mappings": "AAAA,SAASA,cAAc,QAAQ,6BAA6B;AAC5D,SAASC,UAAU,QAAQ,yBAAyB;AACpD,SAASC,cAAc,QAAQ,6BAA6B;AAC5D,SAASC,QAAQ,QAAQ,uBAAuB;AAChD,SAASC,KAAK,QAAQ,oBAAoB;;AAE1C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMC,EAAE,GAAG;EAChBC,IAAI,EAAE,IAAI;EACVN,cAAc,EAAEA,cAAc;EAC9BC,UAAU,EAAEA,UAAU;EACtBC,cAAc,EAAEA,cAAc;EAC9BC,QAAQ,EAAEA,QAAQ;EAClBC,KAAK,EAAEA,KAAK;EACZG,OAAO,EAAE;IACPC,YAAY,EAAE,CAAC,CAAC;IAChBC,qBAAqB,EAAE;EACzB;AACF,CAAC;;AAED;AACA,eAAeJ,EAAE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
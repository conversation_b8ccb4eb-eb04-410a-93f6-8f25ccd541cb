
import React, { lazy, Suspense } from 'react';
import LoadingSpinner from '../components/Common/LoadingSpinner';
import { ROUTES } from '../constants/routes';

// Lazy load components for better performance
const DoctorsPage = lazy(() => import('../components/Page/DoctorsPage'));
const DoctorDetailPage = lazy(() => import('../components/Page/DoctorDetailPage'));
const TreatmentsPage = lazy(() => import('../components/Page/TreatmentsPage'));
const TreatmentsDetailPage = lazy(() => import('../components/Page/TreatmentsDetailPage'));
const PatientsPage = lazy(() => import('../components/Page/PatientsPage'));
const PatientsDetailPage = lazy(() => import('../components/Page/PatientsDetailPage'));
const SchedulesPage = lazy(() => import('../components/Page/SchedulesPage'));
const ReceiptsPage = lazy(() => import('../components/Page/ReceiptsPage'));
const ReceiptsDetailPage = lazy(() => import('../components/Page/ReceiptsDetailPage'));
const UsersPage = lazy(() => import('../components/Page/UsersPage'));
const BackupPage = lazy(() => import('../components/Page/BackupPage'));
const ReportManagementPage = lazy(() => import('../components/Page/ReportManagementPage'));
const ImageManagementPage = lazy(() => import('../components/Page/ImageManagementPage'));
const LoginLogsPage = lazy(() => import('../components/Page/LoginLogsPage'));
const IpBlocksPage = lazy(() => import('../components/Page/IpBlocksPage'));
const DebugPage = lazy(() => import('../components/Page/DebugPage'));
const UpdatePasswordPage = lazy(() => import('../components/Page/UpdatePasswordPage'));

// Higher-order component to wrap lazy components with Suspense
const withSuspense = (Component: React.LazyExoticComponent<React.ComponentType<any>>) => {
  return (props: any) => (
    <Suspense fallback={<LoadingSpinner message="Loading page..." />}>
      <Component {...props} />
    </Suspense>
  );
};

export const componentMap: { [key: string]: React.ComponentType<any> } = {
  [ROUTES.DOCTORS]: withSuspense(DoctorsPage),
  [ROUTES.DOCTOR_DETAIL]: withSuspense(DoctorDetailPage),
  [ROUTES.TREATMENTS]: withSuspense(TreatmentsPage),
  [ROUTES.TREATMENT_DETAIL]: withSuspense(TreatmentsDetailPage),
  [ROUTES.PATIENTS]: withSuspense(PatientsPage),
  [ROUTES.PATIENT_DETAIL]: withSuspense(PatientsDetailPage),
  [ROUTES.SCHEDULES]: withSuspense(SchedulesPage),
  [ROUTES.RECEIPTS]: withSuspense(ReceiptsPage),
  [ROUTES.RECEIPT_DETAIL]: withSuspense(ReceiptsDetailPage),
  [ROUTES.USERS]: withSuspense(UsersPage),
  [ROUTES.BACKUP_MANAGEMENT]: withSuspense(BackupPage),
  [ROUTES.REPORT_MANAGEMENT]: withSuspense(ReportManagementPage),
  [ROUTES.IMAGE_MANAGEMENT]: withSuspense(ImageManagementPage),
  [ROUTES.LOGIN_LOGS]: withSuspense(LoginLogsPage),
  [ROUTES.IP_BLOCKS]: withSuspense(IpBlocksPage),
  [ROUTES.DEBUG]: withSuspense(DebugPage),
  [ROUTES.UPDATE_PASSWORD]: withSuspense(UpdatePasswordPage),
};
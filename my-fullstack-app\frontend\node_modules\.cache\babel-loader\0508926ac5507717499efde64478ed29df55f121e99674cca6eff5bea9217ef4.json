{"ast": null, "code": "import React from'react';import{Button}from'primereact/button';import{Card}from'primereact/card';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const ErrorFallback=_ref=>{let{error,resetError}=_ref;const errorMessage=(error===null||error===void 0?void 0:error.message)||'發生未知錯誤';const errorStack=error===null||error===void 0?void 0:error.stack;const handleGoHome=()=>{window.location.href='/';};const handleGoBack=()=>{window.history.back();};const handleReload=()=>{if(resetError){resetError();}else{window.location.reload();}};const handleReportError=()=>{// 這裡可以實現錯誤報告功能\nconst errorReport={message:errorMessage,stack:errorStack,url:window.location.href,userAgent:navigator.userAgent,timestamp:new Date().toISOString()};console.error('錯誤報告:',errorReport);// 可以發送到錯誤追蹤服務\n// 例如: Sentry, LogRocket 等\nalert('錯誤報告已記錄，感謝您的回饋！');};return/*#__PURE__*/_jsx(\"div\",{className:\"error-page min-h-screen flex align-items-center justify-content-center bg-gray-50\",children:/*#__PURE__*/_jsx(\"div\",{className:\"max-w-md w-full\",children:/*#__PURE__*/_jsxs(Card,{className:\"text-center\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"mb-4\",children:[/*#__PURE__*/_jsx(\"i\",{className:\"pi pi-exclamation-triangle text-6xl text-red-500 mb-3\"}),/*#__PURE__*/_jsx(\"h1\",{className:\"text-3xl font-bold text-gray-800 mb-2\",children:\"\\u7CDF\\u7CD5\\uFF01\"}),/*#__PURE__*/_jsx(\"h2\",{className:\"text-xl text-gray-600 mb-4\",children:\"\\u61C9\\u7528\\u7A0B\\u5F0F\\u767C\\u751F\\u932F\\u8AA4\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"mb-4 p-3 bg-red-50 border-round\",children:[/*#__PURE__*/_jsx(\"p\",{className:\"text-red-700 font-medium mb-2\",children:\"\\u932F\\u8AA4\\u8A73\\u60C5\\uFF1A\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-red-600 text-sm\",children:errorMessage})]}),process.env.NODE_ENV==='development'&&errorStack&&/*#__PURE__*/_jsxs(\"div\",{className:\"mb-4 p-3 bg-gray-100 border-round\",children:[/*#__PURE__*/_jsx(\"p\",{className:\"text-gray-700 font-medium mb-2\",children:\"\\u6280\\u8853\\u8A73\\u60C5\\uFF1A\"}),/*#__PURE__*/_jsx(\"pre\",{className:\"text-xs text-gray-600 text-left overflow-auto max-h-20rem\",children:errorStack})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-column gap-2\",children:[/*#__PURE__*/_jsx(Button,{label:\"\\u91CD\\u65B0\\u8F09\\u5165\\u9801\\u9762\",icon:\"pi pi-refresh\",onClick:handleReload,className:\"p-button-primary\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex gap-2\",children:[/*#__PURE__*/_jsx(Button,{label:\"\\u8FD4\\u56DE\\u4E0A\\u9801\",icon:\"pi pi-arrow-left\",onClick:handleGoBack,className:\"p-button-secondary flex-1\",outlined:true}),/*#__PURE__*/_jsx(Button,{label:\"\\u56DE\\u5230\\u9996\\u9801\",icon:\"pi pi-home\",onClick:handleGoHome,className:\"p-button-secondary flex-1\",outlined:true})]}),/*#__PURE__*/_jsx(Button,{label:\"\\u56DE\\u5831\\u554F\\u984C\",icon:\"pi pi-send\",onClick:handleReportError,className:\"p-button-help\",outlined:true,size:\"small\"})]}),/*#__PURE__*/_jsx(\"div\",{className:\"mt-4 pt-3 border-top-1 surface-border\",children:/*#__PURE__*/_jsx(\"p\",{className:\"text-sm text-gray-500\",children:\"\\u5982\\u679C\\u554F\\u984C\\u6301\\u7E8C\\u767C\\u751F\\uFF0C\\u8ACB\\u806F\\u7E6B\\u7CFB\\u7D71\\u7BA1\\u7406\\u54E1\"})})]})})});};export default ErrorFallback;", "map": {"version": 3, "names": ["React", "<PERSON><PERSON>", "Card", "jsx", "_jsx", "jsxs", "_jsxs", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_ref", "error", "resetError", "errorMessage", "message", "errorStack", "stack", "handleGoHome", "window", "location", "href", "handleGoBack", "history", "back", "handleReload", "reload", "handleReportError", "errorReport", "url", "userAgent", "navigator", "timestamp", "Date", "toISOString", "console", "alert", "className", "children", "process", "env", "NODE_ENV", "label", "icon", "onClick", "outlined", "size"], "sources": ["C:/Users/<USER>/Desktop/demo-git/demo-react/my-fullstack-app/frontend/src/components/Common/ErrorFallback.tsx"], "sourcesContent": ["import React from 'react';\r\nimport { Button } from 'primereact/button';\r\nimport { Card } from 'primereact/card';\r\n\r\ninterface ErrorFallbackProps {\r\n  error?: Error;\r\n  resetError?: () => void;\r\n}\r\n\r\nconst ErrorFallback: React.FC<ErrorFallbackProps> = ({ error, resetError }) => {\r\n  const errorMessage = error?.message || '發生未知錯誤';\r\n  const errorStack = error?.stack;\r\n\r\n  const handleGoHome = () => {\r\n    window.location.href = '/';\r\n  };\r\n\r\n  const handleGoBack = () => {\r\n    window.history.back();\r\n  };\r\n\r\n  const handleReload = () => {\r\n    if (resetError) {\r\n      resetError();\r\n    } else {\r\n      window.location.reload();\r\n    }\r\n  };\r\n\r\n  const handleReportError = () => {\r\n    // 這裡可以實現錯誤報告功能\r\n    const errorReport = {\r\n      message: errorMessage,\r\n      stack: errorStack,\r\n      url: window.location.href,\r\n      userAgent: navigator.userAgent,\r\n      timestamp: new Date().toISOString()\r\n    };\r\n    \r\n    console.error('錯誤報告:', errorReport);\r\n    \r\n    // 可以發送到錯誤追蹤服務\r\n    // 例如: Sentry, LogRocket 等\r\n    alert('錯誤報告已記錄，感謝您的回饋！');\r\n  };\r\n\r\n  return (\r\n    <div className=\"error-page min-h-screen flex align-items-center justify-content-center bg-gray-50\">\r\n      <div className=\"max-w-md w-full\">\r\n        <Card className=\"text-center\">\r\n          <div className=\"mb-4\">\r\n            <i className=\"pi pi-exclamation-triangle text-6xl text-red-500 mb-3\"></i>\r\n            <h1 className=\"text-3xl font-bold text-gray-800 mb-2\">糟糕！</h1>\r\n            <h2 className=\"text-xl text-gray-600 mb-4\">應用程式發生錯誤</h2>\r\n          </div>\r\n\r\n          <div className=\"mb-4 p-3 bg-red-50 border-round\">\r\n            <p className=\"text-red-700 font-medium mb-2\">錯誤詳情：</p>\r\n            <p className=\"text-red-600 text-sm\">{errorMessage}</p>\r\n          </div>\r\n\r\n          {process.env.NODE_ENV === 'development' && errorStack && (\r\n            <div className=\"mb-4 p-3 bg-gray-100 border-round\">\r\n              <p className=\"text-gray-700 font-medium mb-2\">技術詳情：</p>\r\n              <pre className=\"text-xs text-gray-600 text-left overflow-auto max-h-20rem\">\r\n                {errorStack}\r\n              </pre>\r\n            </div>\r\n          )}\r\n\r\n          <div className=\"flex flex-column gap-2\">\r\n            <Button\r\n              label=\"重新載入頁面\"\r\n              icon=\"pi pi-refresh\"\r\n              onClick={handleReload}\r\n              className=\"p-button-primary\"\r\n            />\r\n            \r\n            <div className=\"flex gap-2\">\r\n              <Button\r\n                label=\"返回上頁\"\r\n                icon=\"pi pi-arrow-left\"\r\n                onClick={handleGoBack}\r\n                className=\"p-button-secondary flex-1\"\r\n                outlined\r\n              />\r\n              \r\n              <Button\r\n                label=\"回到首頁\"\r\n                icon=\"pi pi-home\"\r\n                onClick={handleGoHome}\r\n                className=\"p-button-secondary flex-1\"\r\n                outlined\r\n              />\r\n            </div>\r\n\r\n            <Button\r\n              label=\"回報問題\"\r\n              icon=\"pi pi-send\"\r\n              onClick={handleReportError}\r\n              className=\"p-button-help\"\r\n              outlined\r\n              size=\"small\"\r\n            />\r\n          </div>\r\n\r\n          <div className=\"mt-4 pt-3 border-top-1 surface-border\">\r\n            <p className=\"text-sm text-gray-500\">\r\n              如果問題持續發生，請聯繫系統管理員\r\n            </p>\r\n          </div>\r\n        </Card>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default ErrorFallback;\r\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CACzB,OAASC,MAAM,KAAQ,mBAAmB,CAC1C,OAASC,IAAI,KAAQ,iBAAiB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAOvC,KAAM,CAAAC,aAA2C,CAAGC,IAAA,EAA2B,IAA1B,CAAEC,KAAK,CAAEC,UAAW,CAAC,CAAAF,IAAA,CACxE,KAAM,CAAAG,YAAY,CAAG,CAAAF,KAAK,SAALA,KAAK,iBAALA,KAAK,CAAEG,OAAO,GAAI,QAAQ,CAC/C,KAAM,CAAAC,UAAU,CAAGJ,KAAK,SAALA,KAAK,iBAALA,KAAK,CAAEK,KAAK,CAE/B,KAAM,CAAAC,YAAY,CAAGA,CAAA,GAAM,CACzBC,MAAM,CAACC,QAAQ,CAACC,IAAI,CAAG,GAAG,CAC5B,CAAC,CAED,KAAM,CAAAC,YAAY,CAAGA,CAAA,GAAM,CACzBH,MAAM,CAACI,OAAO,CAACC,IAAI,CAAC,CAAC,CACvB,CAAC,CAED,KAAM,CAAAC,YAAY,CAAGA,CAAA,GAAM,CACzB,GAAIZ,UAAU,CAAE,CACdA,UAAU,CAAC,CAAC,CACd,CAAC,IAAM,CACLM,MAAM,CAACC,QAAQ,CAACM,MAAM,CAAC,CAAC,CAC1B,CACF,CAAC,CAED,KAAM,CAAAC,iBAAiB,CAAGA,CAAA,GAAM,CAC9B;AACA,KAAM,CAAAC,WAAW,CAAG,CAClBb,OAAO,CAAED,YAAY,CACrBG,KAAK,CAAED,UAAU,CACjBa,GAAG,CAAEV,MAAM,CAACC,QAAQ,CAACC,IAAI,CACzBS,SAAS,CAAEC,SAAS,CAACD,SAAS,CAC9BE,SAAS,CAAE,GAAI,CAAAC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CACpC,CAAC,CAEDC,OAAO,CAACvB,KAAK,CAAC,OAAO,CAAEgB,WAAW,CAAC,CAEnC;AACA;AACAQ,KAAK,CAAC,iBAAiB,CAAC,CAC1B,CAAC,CAED,mBACE7B,IAAA,QAAK8B,SAAS,CAAC,mFAAmF,CAAAC,QAAA,cAChG/B,IAAA,QAAK8B,SAAS,CAAC,iBAAiB,CAAAC,QAAA,cAC9B7B,KAAA,CAACJ,IAAI,EAACgC,SAAS,CAAC,aAAa,CAAAC,QAAA,eAC3B7B,KAAA,QAAK4B,SAAS,CAAC,MAAM,CAAAC,QAAA,eACnB/B,IAAA,MAAG8B,SAAS,CAAC,uDAAuD,CAAI,CAAC,cACzE9B,IAAA,OAAI8B,SAAS,CAAC,uCAAuC,CAAAC,QAAA,CAAC,oBAAG,CAAI,CAAC,cAC9D/B,IAAA,OAAI8B,SAAS,CAAC,4BAA4B,CAAAC,QAAA,CAAC,kDAAQ,CAAI,CAAC,EACrD,CAAC,cAEN7B,KAAA,QAAK4B,SAAS,CAAC,iCAAiC,CAAAC,QAAA,eAC9C/B,IAAA,MAAG8B,SAAS,CAAC,+BAA+B,CAAAC,QAAA,CAAC,gCAAK,CAAG,CAAC,cACtD/B,IAAA,MAAG8B,SAAS,CAAC,sBAAsB,CAAAC,QAAA,CAAExB,YAAY,CAAI,CAAC,EACnD,CAAC,CAELyB,OAAO,CAACC,GAAG,CAACC,QAAQ,GAAK,aAAa,EAAIzB,UAAU,eACnDP,KAAA,QAAK4B,SAAS,CAAC,mCAAmC,CAAAC,QAAA,eAChD/B,IAAA,MAAG8B,SAAS,CAAC,gCAAgC,CAAAC,QAAA,CAAC,gCAAK,CAAG,CAAC,cACvD/B,IAAA,QAAK8B,SAAS,CAAC,2DAA2D,CAAAC,QAAA,CACvEtB,UAAU,CACR,CAAC,EACH,CACN,cAEDP,KAAA,QAAK4B,SAAS,CAAC,wBAAwB,CAAAC,QAAA,eACrC/B,IAAA,CAACH,MAAM,EACLsC,KAAK,CAAC,sCAAQ,CACdC,IAAI,CAAC,eAAe,CACpBC,OAAO,CAAEnB,YAAa,CACtBY,SAAS,CAAC,kBAAkB,CAC7B,CAAC,cAEF5B,KAAA,QAAK4B,SAAS,CAAC,YAAY,CAAAC,QAAA,eACzB/B,IAAA,CAACH,MAAM,EACLsC,KAAK,CAAC,0BAAM,CACZC,IAAI,CAAC,kBAAkB,CACvBC,OAAO,CAAEtB,YAAa,CACtBe,SAAS,CAAC,2BAA2B,CACrCQ,QAAQ,MACT,CAAC,cAEFtC,IAAA,CAACH,MAAM,EACLsC,KAAK,CAAC,0BAAM,CACZC,IAAI,CAAC,YAAY,CACjBC,OAAO,CAAE1B,YAAa,CACtBmB,SAAS,CAAC,2BAA2B,CACrCQ,QAAQ,MACT,CAAC,EACC,CAAC,cAENtC,IAAA,CAACH,MAAM,EACLsC,KAAK,CAAC,0BAAM,CACZC,IAAI,CAAC,YAAY,CACjBC,OAAO,CAAEjB,iBAAkB,CAC3BU,SAAS,CAAC,eAAe,CACzBQ,QAAQ,MACRC,IAAI,CAAC,OAAO,CACb,CAAC,EACC,CAAC,cAENvC,IAAA,QAAK8B,SAAS,CAAC,uCAAuC,CAAAC,QAAA,cACpD/B,IAAA,MAAG8B,SAAS,CAAC,uBAAuB,CAAAC,QAAA,CAAC,wGAErC,CAAG,CAAC,CACD,CAAC,EACF,CAAC,CACJ,CAAC,CACH,CAAC,CAEV,CAAC,CAED,cAAe,CAAA5B,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
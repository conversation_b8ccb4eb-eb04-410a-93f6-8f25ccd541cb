{"ast": null, "code": "import { buildFormatLongFn } from \"../../_lib/buildFormatLongFn.js\";\n\n//Source: https://www.unicode.org/cldr/charts/32/summary/gu.html\nconst dateFormats = {\n  full: \"EEEE, d MMMM, y\",\n  // CLDR #1825\n  long: \"d MMMM, y\",\n  // CLDR #1826\n  medium: \"d MMM, y\",\n  // CLDR #1827\n  short: \"d/M/yy\" // CLDR #1828\n};\nconst timeFormats = {\n  full: \"hh:mm:ss a zzzz\",\n  // CLDR #1829\n  long: \"hh:mm:ss a z\",\n  // CLDR #1830\n  medium: \"hh:mm:ss a\",\n  // CLDR #1831\n  short: \"hh:mm a\" // CLDR #1832\n};\nconst dateTimeFormats = {\n  full: \"{{date}} {{time}}\",\n  // CLDR #1833\n  long: \"{{date}} {{time}}\",\n  // CLDR #1834\n  medium: \"{{date}} {{time}}\",\n  // CLDR #1835\n  short: \"{{date}} {{time}}\" // CLDR #1836\n};\nexport const formatLong = {\n  date: buildFormatLongFn({\n    formats: dateFormats,\n    defaultWidth: \"full\"\n  }),\n  time: buildFormatLongFn({\n    formats: timeFormats,\n    defaultWidth: \"full\"\n  }),\n  dateTime: buildFormatLongFn({\n    formats: dateTimeFormats,\n    defaultWidth: \"full\"\n  })\n};", "map": {"version": 3, "names": ["buildFormatLongFn", "dateFormats", "full", "long", "medium", "short", "timeFormats", "dateTimeFormats", "formatLong", "date", "formats", "defaultWidth", "time", "dateTime"], "sources": ["C:/Users/<USER>/Desktop/demo-git/demo-react/my-fullstack-app/frontend/node_modules/date-fns/locale/gu/_lib/formatLong.js"], "sourcesContent": ["import { buildFormatLongFn } from \"../../_lib/buildFormatLongFn.js\";\n\n//Source: https://www.unicode.org/cldr/charts/32/summary/gu.html\nconst dateFormats = {\n  full: \"EEEE, d MMMM, y\", // CLDR #1825\n  long: \"d MMMM, y\", // CLDR #1826\n  medium: \"d MMM, y\", // CLDR #1827\n  short: \"d/M/yy\", // CLDR #1828\n};\n\nconst timeFormats = {\n  full: \"hh:mm:ss a zzzz\", // CLDR #1829\n  long: \"hh:mm:ss a z\", // CLDR #1830\n  medium: \"hh:mm:ss a\", // CLDR #1831\n  short: \"hh:mm a\", // CLDR #1832\n};\n\nconst dateTimeFormats = {\n  full: \"{{date}} {{time}}\", // CLDR #1833\n  long: \"{{date}} {{time}}\", // CLDR #1834\n  medium: \"{{date}} {{time}}\", // CLDR #1835\n  short: \"{{date}} {{time}}\", // CLDR #1836\n};\n\nexport const formatLong = {\n  date: buildFormatLongFn({\n    formats: dateFormats,\n    defaultWidth: \"full\",\n  }),\n\n  time: buildFormatLongFn({\n    formats: timeFormats,\n    defaultWidth: \"full\",\n  }),\n\n  dateTime: buildFormatLongFn({\n    formats: dateTimeFormats,\n    defaultWidth: \"full\",\n  }),\n};\n"], "mappings": "AAAA,SAASA,iBAAiB,QAAQ,iCAAiC;;AAEnE;AACA,MAAMC,WAAW,GAAG;EAClBC,IAAI,EAAE,iBAAiB;EAAE;EACzBC,IAAI,EAAE,WAAW;EAAE;EACnBC,MAAM,EAAE,UAAU;EAAE;EACpBC,KAAK,EAAE,QAAQ,CAAE;AACnB,CAAC;AAED,MAAMC,WAAW,GAAG;EAClBJ,IAAI,EAAE,iBAAiB;EAAE;EACzBC,IAAI,EAAE,cAAc;EAAE;EACtBC,MAAM,EAAE,YAAY;EAAE;EACtBC,KAAK,EAAE,SAAS,CAAE;AACpB,CAAC;AAED,MAAME,eAAe,GAAG;EACtBL,IAAI,EAAE,mBAAmB;EAAE;EAC3BC,IAAI,EAAE,mBAAmB;EAAE;EAC3BC,MAAM,EAAE,mBAAmB;EAAE;EAC7BC,KAAK,EAAE,mBAAmB,CAAE;AAC9B,CAAC;AAED,OAAO,MAAMG,UAAU,GAAG;EACxBC,IAAI,EAAET,iBAAiB,CAAC;IACtBU,OAAO,EAAET,WAAW;IACpBU,YAAY,EAAE;EAChB,CAAC,CAAC;EAEFC,IAAI,EAAEZ,iBAAiB,CAAC;IACtBU,OAAO,EAAEJ,WAAW;IACpBK,YAAY,EAAE;EAChB,CAAC,CAAC;EAEFE,QAAQ,EAAEb,iBAAiB,CAAC;IAC1BU,OAAO,EAAEH,eAAe;IACxBI,YAAY,EAAE;EAChB,CAAC;AACH,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "import{zhTW}from'date-fns/locale';import{formatUtcToTaipei}from'../../utils/dateUtils';import dayGridPlugin from'@fullcalendar/daygrid';import FullCalendar from'@fullcalendar/react';import timeGridPlugin from'@fullcalendar/timegrid';import{Chart}from'primereact/chart';import React,{useEffect,useState,useRef}from'react';import{useNavigate}from'react-router-dom';import api from'../../services/api';import{log}from'../../utils/logger';import{Toast}from'primereact/toast';import{Dialog}from'primereact/dialog';import{Button}from'primereact/button';import{ROUTES}from'../../constants/routes';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const HomePage=()=>{const navigate=useNavigate();const toast=useRef(null);const[basicData,setBasicData]=useState({});const[lineData,setLineData]=useState({});const[chartOptions,setChartOptions]=useState({});const[todayEvents,setTodayEvents]=useState([]);const[showEventDialog,setShowEventDialog]=useState(false);const[selectedEvent,setSelectedEvent]=useState(null);useEffect(()=>{const loadData=async()=>{// 載入圖表數據\nconst documentStyle=getComputedStyle(document.documentElement);const textColor=documentStyle.getPropertyValue('--text-color');const textColorSecondary=documentStyle.getPropertyValue('--text-color-secondary');const surfaceBorder=documentStyle.getPropertyValue('--surface-border');// Chart Options\nconst options={maintainAspectRatio:false,aspectRatio:0.6,plugins:{legend:{labels:{fontColor:textColor}}},scales:{x:{ticks:{color:textColorSecondary},grid:{color:surfaceBorder}},y:{ticks:{color:textColorSecondary},grid:{color:surfaceBorder}}}};// 載入月統計數據\ntry{log.api('載入月統計數據...');const response=await api.get('/api/system/GetMonthSatistics');log.api('月統計數據',response.data);// 解析 JSON 字符串（因為後端返回的是序列化的 JSON）\nconst MonthData=typeof response.data==='string'?JSON.parse(response.data):response.data;// 轉換數據格式\nconst formattedEvents={lable:MonthData.lable||MonthData.labels||[],schedules:MonthData.schedules||[],treatments:MonthData.treatments||[]};log.ui('月統計原始數據',MonthData);log.ui('月統計格式化數據',formattedEvents);// Basic Chart Data (Bar Chart) - 直接使用解析後的數據\nconst basicChartData={labels:formattedEvents.lable,datasets:[{label:'預約人數',backgroundColor:documentStyle.getPropertyValue('--blue-500'),borderColor:documentStyle.getPropertyValue('--blue-500'),data:formattedEvents.schedules},{label:'治療次數',backgroundColor:documentStyle.getPropertyValue('--pink-500'),borderColor:documentStyle.getPropertyValue('--pink-500'),data:formattedEvents.treatments}]};log.ui('月統計圖表數據',basicChartData);setBasicData(basicChartData);}catch(error){var _toast$current;log.error('載入月統計失敗',error);// 如果 API 失敗，顯示示例數據\n(_toast$current=toast.current)===null||_toast$current===void 0?void 0:_toast$current.show({severity:'error',summary:'錯誤',detail:'載入月統計失敗'});}// 載入周統計數據\ntry{log.api('載入周統計數據...');const response=await api.get('/api/system/GetWeekSatistics');log.api('周統計數據',response.data);// 解析 JSON 字符串（因為後端返回的是序列化的 JSON）\nconst WeekData=typeof response.data==='string'?JSON.parse(response.data):response.data;// 轉換數據格式\nconst formattedEvents={lable:WeekData.lable.map(label=>\"\".concat(label,\" \\u865F\"))||WeekData.labels.map(label=>\"\".concat(label,\" \\u865F\"))||[],schedules:WeekData.schedules||[],treatments:WeekData.treatments||[]};log.ui('周統計原始數據',WeekData);log.ui('周統計格式化數據',formattedEvents);// Line Chart Data - 直接使用解析後的數據\nconst lineChartData={labels:formattedEvents.lable,datasets:[{label:'預約數量',data:formattedEvents.schedules,fill:false,backgroundColor:documentStyle.getPropertyValue('--blue-600'),borderColor:documentStyle.getPropertyValue('--blue-600'),tension:0.4},{label:'完成治療',data:formattedEvents.treatments,fill:false,backgroundColor:documentStyle.getPropertyValue('--pink-600'),borderColor:documentStyle.getPropertyValue('--pink-600'),tension:0.4}]};log.ui('周統計圖表數據',lineChartData);setLineData(lineChartData);}catch(error){var _toast$current2;log.error('載入周統計失敗',error);// 如果 API 失敗，顯示示例數據\n(_toast$current2=toast.current)===null||_toast$current2===void 0?void 0:_toast$current2.show({severity:'error',summary:'錯誤',detail:'載入周統計失敗'});}setChartOptions(options);// 載入當日行程數據\ntry{log.api('載入當日行程數據...');const response=await api.get('/api/schedule/GetTodaySchedules');log.api('當日行程數據',response.data);// 解析 JSON 字符串（因為後端返回的是序列化的 JSON）\nconst todaySchedulesData=typeof response.data==='string'?JSON.parse(response.data):response.data;// 轉換數據格式\nconst formattedEvents=todaySchedulesData.map(event=>({id:event.Id||event.id,title:event.Title||event.title,start:event.Start||event.start,end:event.End||event.end,backgroundColor:event.BackgroundColor||event.backgroundColor||'#3788d8',doctorId:event.DoctorId||event.doctorId,patientId:event.PatientId||event.patientId,treatmentId:event.TreatmentId||event.treatmentId,doctorName:event.DoctorName||event.doctorName,patientName:event.PatientName||event.patientName,description:event.Description||event.description}));log.ui('今日行程',formattedEvents);setTodayEvents(formattedEvents);}catch(error){var _toast$current3;log.error('載入當日行程失敗',error);// 如果 API 失敗，顯示示例數據\n(_toast$current3=toast.current)===null||_toast$current3===void 0?void 0:_toast$current3.show({severity:'error',summary:'錯誤',detail:'載入當日行程失敗'});}};loadData();},[]);// 處理事件點擊\nconst handleEventClick=clickInfo=>{const eventId=clickInfo.event.id;const event=todayEvents.find(e=>e.id===eventId);if(event){setSelectedEvent(event);setShowEventDialog(true);}};// 處理開案\nconst handleOpenCase=async()=>{if(!selectedEvent)return;try{let treatmentId=selectedEvent.treatmentId;if(!treatmentId){// 如果沒有 TreatmentId，創建新的治療案件\nlog.api('創建新的治療案件');const createResponse=await api.post('/api/treatment/Insert',{PatientId:selectedEvent.patientId,DoctorId:selectedEvent.doctorId// 其他必要的初始數據\n});treatmentId=createResponse.data.treatmentId;log.api('新治療案件創建成功',{treatmentId});// 更新 Schedule 的 TreatmentId\nawait api.patch(\"/api/schedule/\".concat(selectedEvent.id,\"/treatment\"),{TreatmentId:treatmentId});log.api('Schedule TreatmentId 更新成功');}// 獲取完整的治療數據\nlog.api('獲取治療數據',{treatmentId});const treatmentResponse=await api.get('/api/treatment',{params:{Id:treatmentId}});const treatmentData=treatmentResponse.data;// 跳轉到 TreatmentsDetailPage\nif(treatmentId){navigate(\"\".concat(ROUTES.TREATMENT_DETAIL,\"?id=\").concat(treatmentId),{state:{treatment:treatmentData,patient:{id:treatmentData.patientId}}});}}catch(error){var _toast$current4;log.error('開案失敗',error);(_toast$current4=toast.current)===null||_toast$current4===void 0?void 0:_toast$current4.show({severity:'error',summary:'開案失敗',detail:'無法開啟治療案件，請稍後再試',life:3000});}};return/*#__PURE__*/_jsxs(\"div\",{className:\"home-page\",children:[/*#__PURE__*/_jsx(Toast,{ref:toast}),/*#__PURE__*/_jsxs(\"div\",{className:\"grid justify-content-center\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"col-12 md:col-4 pl-4 pr-4\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"card\",children:[/*#__PURE__*/_jsxs(\"h3\",{className:\"flex flex-wrap \",children:[/*#__PURE__*/_jsx(\"div\",{className:\"col-12 md:col-6 flex justify-content-center\",children:\"\\u4ECA\\u65E5\\u9810\\u7D04\\u7642\\u7A0B\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"col-12 md:col-6 flex justify-content-center\",children:[\"(\",formatUtcToTaipei(new Date(),'yyyy-MM-dd EEEE',{locale:zhTW}),\")\"]})]}),/*#__PURE__*/_jsx(\"div\",{className:\"calendar-container\",children:/*#__PURE__*/_jsx(FullCalendar,{plugins:[dayGridPlugin,timeGridPlugin],height:\"auto\",initialView:\"timeGridDay\",locale:\"zh-tw\",headerToolbar:{left:'',center:'',right:''},dayHeaderFormat:{day:'numeric',// 顯示日期數字 (例如: 13)\nweekday:'short'// 顯示短格式的星期 (例如: 日, 一, 二)\n},slotLabelFormat:{hour:'2-digit',// 小時顯示為兩位數 (例如: 09)\nminute:'numeric',// 分鐘顯示為兩位數 (例如: 00)\nhour12:false// 禁用 12 小時制，啟用 24 小時制\n},allDaySlot:false,slotMinTime:\"09:00:00\",slotMaxTime:\"22:00:00\",events:todayEvents,eventDisplay:\"block\",dayMaxEvents:false,businessHours:{daysOfWeek:[1,2,3,4,5,6],startTime:'10:00',endTime:'18:00'},eventClick:handleEventClick// 啟用點擊事件\n,selectable:false// 禁用選擇\n,editable:false// 禁用編輯\n})})]})}),/*#__PURE__*/_jsxs(\"div\",{className:\"col-12 md:col-8 pl-4 pr-4 flex flex-wrap\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"col-12 md:col-6\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"card\",children:[/*#__PURE__*/_jsx(\"h3\",{className:\"flex justify-content-center\",children:\"\\u6708\\u5EA6\\u7D71\\u8A08(\\u6BCF\\u5C0F\\u6642\\u66F4\\u65B0)\"}),/*#__PURE__*/_jsx(Chart,{type:\"bar\",data:basicData,options:chartOptions})]})}),/*#__PURE__*/_jsx(\"div\",{className:\"col-12 md:col-6\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"card\",children:[/*#__PURE__*/_jsx(\"h3\",{className:\"flex justify-content-center\",children:\"\\u6BCF\\u9031\\u9810\\u7D04\\u8DA8\\u52E2(\\u6BCF\\u5C0F\\u6642\\u66F4\\u65B0)\"}),/*#__PURE__*/_jsx(Chart,{type:\"line\",data:lineData,options:chartOptions})]})})]})]}),/*#__PURE__*/_jsx(Dialog,{header:\"\\u9810\\u7D04\\u8A73\\u60C5\",visible:showEventDialog,style:{width:'450px'},onHide:()=>setShowEventDialog(false),footer:/*#__PURE__*/_jsxs(\"div\",{className:\"flex justify-content-end gap-2\",children:[/*#__PURE__*/_jsx(Button,{label:\"\\u53D6\\u6D88\",icon:\"pi pi-times\",onClick:()=>setShowEventDialog(false),className:\"p-button-text\"}),/*#__PURE__*/_jsx(Button,{label:\"\\u958B\\u6848\",icon:\"pi pi-folder-open\",onClick:handleOpenCase,className:\"p-button-primary\"})]}),children:selectedEvent&&/*#__PURE__*/_jsxs(\"div\",{className:\"grid\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"col-12\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"field\",children:[/*#__PURE__*/_jsx(\"label\",{className:\"font-bold\",children:\"\\u6CBB\\u7642\\u5E2B:\"}),/*#__PURE__*/_jsx(\"p\",{className:\"m-0\",children:selectedEvent.doctorName})]})}),/*#__PURE__*/_jsx(\"div\",{className:\"col-12\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"field\",children:[/*#__PURE__*/_jsx(\"label\",{className:\"font-bold\",children:\"\\u75C5\\u60A3:\"}),/*#__PURE__*/_jsx(\"p\",{className:\"m-0\",children:selectedEvent.patientName})]})}),/*#__PURE__*/_jsx(\"div\",{className:\"col-6\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"field\",children:[/*#__PURE__*/_jsx(\"label\",{className:\"font-bold\",children:\"\\u958B\\u59CB\\u6642\\u9593:\"}),/*#__PURE__*/_jsx(\"p\",{className:\"m-0\",children:formatUtcToTaipei(selectedEvent.start,'HH:mm')})]})}),/*#__PURE__*/_jsx(\"div\",{className:\"col-6\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"field\",children:[/*#__PURE__*/_jsx(\"label\",{className:\"font-bold\",children:\"\\u7D50\\u675F\\u6642\\u9593:\"}),/*#__PURE__*/_jsx(\"p\",{className:\"m-0\",children:formatUtcToTaipei(selectedEvent.end,'HH:mm')})]})}),selectedEvent.description&&/*#__PURE__*/_jsx(\"div\",{className:\"col-12\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"field\",children:[/*#__PURE__*/_jsx(\"label\",{className:\"font-bold\",children:\"\\u5099\\u8A3B:\"}),/*#__PURE__*/_jsx(\"p\",{className:\"m-0\",children:selectedEvent.description})]})}),selectedEvent.treatmentId&&/*#__PURE__*/_jsx(\"div\",{className:\"col-12\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"field\",children:[/*#__PURE__*/_jsx(\"label\",{className:\"font-bold\",children:\"\\u6CBB\\u7642\\u6848\\u4EF6\\u7DE8\\u865F:\"}),/*#__PURE__*/_jsx(\"p\",{className:\"m-0\",children:selectedEvent.treatmentId})]})})]})}),/*#__PURE__*/_jsx(Toast,{ref:toast})]});};export default HomePage;", "map": {"version": 3, "names": ["zhTW", "formatUtcToTaipei", "dayGridPlugin", "FullCalendar", "timeGridPlugin", "Chart", "React", "useEffect", "useState", "useRef", "useNavigate", "api", "log", "Toast", "Dialog", "<PERSON><PERSON>", "ROUTES", "jsx", "_jsx", "jsxs", "_jsxs", "HomePage", "navigate", "toast", "basicData", "setBasicData", "lineData", "setLineData", "chartOptions", "setChartOptions", "todayEvents", "setTodayEvents", "showEventDialog", "setShowEventDialog", "selectedEvent", "setSelectedEvent", "loadData", "documentStyle", "getComputedStyle", "document", "documentElement", "textColor", "getPropertyValue", "textColorSecondary", "surfaceBorder", "options", "maintainAspectRatio", "aspectRatio", "plugins", "legend", "labels", "fontColor", "scales", "x", "ticks", "color", "grid", "y", "response", "get", "data", "MonthData", "JSON", "parse", "formattedEvents", "lable", "schedules", "treatments", "ui", "basicChartData", "datasets", "label", "backgroundColor", "borderColor", "error", "_toast$current", "current", "show", "severity", "summary", "detail", "WeekData", "map", "concat", "lineChartData", "fill", "tension", "_toast$current2", "todaySchedulesData", "event", "id", "Id", "title", "Title", "start", "Start", "end", "End", "BackgroundColor", "doctorId", "DoctorId", "patientId", "PatientId", "treatmentId", "TreatmentId", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "patientName", "PatientName", "description", "Description", "_toast$current3", "handleEventClick", "clickInfo", "eventId", "find", "e", "handleOpenCase", "createResponse", "post", "patch", "treatmentResponse", "params", "treatmentData", "TREATMENT_DETAIL", "state", "treatment", "patient", "_toast$current4", "life", "className", "children", "ref", "Date", "locale", "height", "initialView", "headerToolbar", "left", "center", "right", "dayHeaderFormat", "day", "weekday", "slotLabelFormat", "hour", "minute", "hour12", "allDaySlot", "slotMinTime", "slotMaxTime", "events", "eventDisplay", "dayMaxEvents", "businessHours", "daysOfWeek", "startTime", "endTime", "eventClick", "selectable", "editable", "type", "header", "visible", "style", "width", "onHide", "footer", "icon", "onClick"], "sources": ["C:/Users/<USER>/Desktop/demo-git/demo-react/my-fullstack-app/frontend/src/components/Page/HomePage.tsx"], "sourcesContent": ["import { zhTW } from 'date-fns/locale';\r\nimport { formatUtcToTaipei } from '../../utils/dateUtils';\r\nimport dayGridPlugin from '@fullcalendar/daygrid';\r\nimport FullCalendar from '@fullcalendar/react';\r\nimport timeGridPlugin from '@fullcalendar/timegrid';\r\nimport { Chart } from 'primereact/chart';\r\nimport React, { useEffect, useState, useRef } from 'react';\r\nimport { useNavigate } from 'react-router-dom';\r\nimport api from '../../services/api';\r\nimport { log } from '../../utils/logger';\r\nimport { Toast } from 'primereact/toast';\r\nimport { Dialog } from 'primereact/dialog';\r\nimport { Button } from 'primereact/button';\r\nimport { ROUTES } from '../../constants/routes';\r\n\r\ninterface CalendarEvent {\r\n  id?: string;\r\n  title: string;\r\n  start: string;\r\n  end: string;\r\n  backgroundColor?: string;\r\n  doctorId?: number;\r\n  patientId?: number;\r\n  treatmentId?: number;\r\n  doctorName?: string;\r\n  patientName?: string;\r\n  description?: string;\r\n}\r\n\r\ninterface SatisticEvent {\r\n  lable?: string[];\r\n  schedules?: number[];\r\n  treatments?: number[];\r\n}\r\n\r\nconst HomePage: React.FC = () => {\r\n  const navigate = useNavigate();\r\n  const toast = useRef<Toast>(null);\r\n  const [basicData, setBasicData] = useState({});\r\n  const [lineData, setLineData] = useState({});\r\n  const [chartOptions, setChartOptions] = useState({});\r\n  const [todayEvents, setTodayEvents] = useState<CalendarEvent[]>([]);\r\n  const [showEventDialog, setShowEventDialog] = useState(false);\r\n  const [selectedEvent, setSelectedEvent] = useState<CalendarEvent | null>(null);\r\n\r\n  useEffect(() => {\r\n    const loadData = async () => {\r\n      // 載入圖表數據\r\n      const documentStyle = getComputedStyle(document.documentElement);\r\n      const textColor = documentStyle.getPropertyValue('--text-color');\r\n      const textColorSecondary = documentStyle.getPropertyValue('--text-color-secondary');\r\n      const surfaceBorder = documentStyle.getPropertyValue('--surface-border');\r\n\r\n      // Chart Options\r\n      const options = {\r\n        maintainAspectRatio: false,\r\n        aspectRatio: 0.6,\r\n        plugins: {\r\n          legend: {\r\n            labels: {\r\n              fontColor: textColor\r\n            }\r\n          }\r\n        },\r\n        scales: {\r\n          x: {\r\n            ticks: {\r\n              color: textColorSecondary\r\n            },\r\n            grid: {\r\n              color: surfaceBorder\r\n            }\r\n          },\r\n          y: {\r\n            ticks: {\r\n              color: textColorSecondary\r\n            },\r\n            grid: {\r\n              color: surfaceBorder\r\n            }\r\n          }\r\n        }\r\n      };\r\n\r\n      // 載入月統計數據\r\n      try {\r\n        log.api('載入月統計數據...');\r\n        const response = await api.get('/api/system/GetMonthSatistics');\r\n        log.api('月統計數據', response.data);\r\n        \r\n        // 解析 JSON 字符串（因為後端返回的是序列化的 JSON）\r\n        const MonthData = typeof response.data === 'string'\r\n          ? JSON.parse(response.data)\r\n          : response.data;\r\n\r\n        // 轉換數據格式\r\n        const formattedEvents: SatisticEvent = {\r\n          lable: MonthData.lable || MonthData.labels || [],\r\n          schedules: MonthData.schedules || [],\r\n          treatments: MonthData.treatments || []\r\n        };\r\n\r\n        log.ui('月統計原始數據', MonthData);\r\n        log.ui('月統計格式化數據', formattedEvents);\r\n\r\n        // Basic Chart Data (Bar Chart) - 直接使用解析後的數據\r\n        const basicChartData = {\r\n          labels: formattedEvents.lable,\r\n          datasets: [\r\n            {\r\n              label: '預約人數',\r\n              backgroundColor: documentStyle.getPropertyValue('--blue-500'),\r\n              borderColor: documentStyle.getPropertyValue('--blue-500'),\r\n              data: formattedEvents.schedules\r\n            },\r\n            {\r\n              label: '治療次數',\r\n              backgroundColor: documentStyle.getPropertyValue('--pink-500'),\r\n              borderColor: documentStyle.getPropertyValue('--pink-500'),\r\n              data: formattedEvents.treatments\r\n            }\r\n          ]\r\n        };\r\n\r\n        log.ui('月統計圖表數據', basicChartData);\r\n        setBasicData(basicChartData);\r\n      } catch (error) {\r\n        log.error('載入月統計失敗', error);\r\n        // 如果 API 失敗，顯示示例數據\r\n        toast.current?.show({\r\n          severity: 'error',\r\n          summary: '錯誤',\r\n          detail: '載入月統計失敗',\r\n        });\r\n      }\r\n\r\n      // 載入周統計數據\r\n      try {\r\n        log.api('載入周統計數據...');\r\n        const response = await api.get('/api/system/GetWeekSatistics');\r\n        log.api('周統計數據', response.data);\r\n        \r\n        // 解析 JSON 字符串（因為後端返回的是序列化的 JSON）\r\n        const WeekData = typeof response.data === 'string'\r\n          ? JSON.parse(response.data)\r\n          : response.data;\r\n\r\n        // 轉換數據格式\r\n        const formattedEvents: SatisticEvent = {\r\n          lable: WeekData.lable.map((label:string) => `${label} 號`) || WeekData.labels.map((label:string) => `${label} 號`) || [],\r\n          schedules: WeekData.schedules || [],\r\n          treatments: WeekData.treatments || []\r\n        };\r\n\r\n        log.ui('周統計原始數據', WeekData);\r\n        log.ui('周統計格式化數據', formattedEvents);\r\n\r\n        // Line Chart Data - 直接使用解析後的數據\r\n        const lineChartData = {\r\n          labels: formattedEvents.lable,\r\n          datasets: [\r\n            {\r\n              label: '預約數量',\r\n              data: formattedEvents.schedules,\r\n              fill: false,\r\n              backgroundColor: documentStyle.getPropertyValue('--blue-600'),\r\n              borderColor: documentStyle.getPropertyValue('--blue-600'),\r\n              tension: 0.4\r\n            },\r\n            {\r\n              label: '完成治療',\r\n              data: formattedEvents.treatments,\r\n              fill: false,\r\n              backgroundColor: documentStyle.getPropertyValue('--pink-600'),\r\n              borderColor: documentStyle.getPropertyValue('--pink-600'),\r\n              tension: 0.4\r\n            }\r\n          ]\r\n        };\r\n\r\n        log.ui('周統計圖表數據', lineChartData);\r\n        setLineData(lineChartData);\r\n      } catch (error) {\r\n        log.error('載入周統計失敗', error);\r\n        // 如果 API 失敗，顯示示例數據\r\n        toast.current?.show({\r\n          severity: 'error',\r\n          summary: '錯誤',\r\n          detail: '載入周統計失敗',\r\n        });\r\n      }\r\n\r\n      setChartOptions(options);\r\n\r\n      // 載入當日行程數據\r\n      try {\r\n        log.api('載入當日行程數據...');\r\n        const response = await api.get('/api/schedule/GetTodaySchedules');\r\n        log.api('當日行程數據', response.data);\r\n\r\n        // 解析 JSON 字符串（因為後端返回的是序列化的 JSON）\r\n        const todaySchedulesData = typeof response.data === 'string'\r\n          ? JSON.parse(response.data)\r\n          : response.data;\r\n\r\n        // 轉換數據格式\r\n        const formattedEvents: CalendarEvent[] = todaySchedulesData.map((event: any) => ({\r\n          id: event.Id || event.id,\r\n          title: event.Title || event.title,\r\n          start: event.Start || event.start,\r\n          end: event.End || event.end,\r\n          backgroundColor: event.BackgroundColor || event.backgroundColor || '#3788d8',\r\n          doctorId: event.DoctorId || event.doctorId,\r\n          patientId: event.PatientId || event.patientId,\r\n          treatmentId: event.TreatmentId || event.treatmentId,\r\n          doctorName: event.DoctorName || event.doctorName,\r\n          patientName: event.PatientName || event.patientName,\r\n          description: event.Description || event.description\r\n        }));\r\n\r\n        log.ui('今日行程', formattedEvents);\r\n        setTodayEvents(formattedEvents);\r\n\r\n      } catch (error) {\r\n        log.error('載入當日行程失敗', error);\r\n        // 如果 API 失敗，顯示示例數據\r\n        toast.current?.show({\r\n          severity: 'error',\r\n          summary: '錯誤',\r\n          detail: '載入當日行程失敗',\r\n        });\r\n      }\r\n\r\n    };\r\n    \r\n    loadData();\r\n  }, []);\r\n\r\n  // 處理事件點擊\r\n  const handleEventClick = (clickInfo: any) => {\r\n    const eventId = clickInfo.event.id;\r\n    const event = todayEvents.find(e => e.id === eventId);\r\n    if (event) {\r\n      setSelectedEvent(event);\r\n      setShowEventDialog(true);\r\n    }\r\n  };\r\n\r\n  // 處理開案\r\n  const handleOpenCase = async () => {\r\n    if (!selectedEvent) return;\r\n\r\n    try {\r\n      let treatmentId = selectedEvent.treatmentId;\r\n\r\n      if (!treatmentId) {\r\n        // 如果沒有 TreatmentId，創建新的治療案件\r\n        log.api('創建新的治療案件');\r\n        const createResponse = await api.post('/api/treatment/Insert', {\r\n          PatientId: selectedEvent.patientId,\r\n          DoctorId: selectedEvent.doctorId,\r\n          // 其他必要的初始數據\r\n        });\r\n\r\n        treatmentId = createResponse.data.treatmentId;\r\n        log.api('新治療案件創建成功', { treatmentId });\r\n\r\n        // 更新 Schedule 的 TreatmentId\r\n        await api.patch(`/api/schedule/${selectedEvent.id}/treatment`, {\r\n          TreatmentId: treatmentId\r\n        });\r\n        log.api('Schedule TreatmentId 更新成功');\r\n      }\r\n\r\n      // 獲取完整的治療數據\r\n      log.api('獲取治療數據', { treatmentId });\r\n\r\n      const treatmentResponse = await api.get('/api/treatment', {\r\n        params: {\r\n          Id: treatmentId\r\n        }\r\n      });\r\n\r\n      const treatmentData = treatmentResponse.data;\r\n\r\n      // 跳轉到 TreatmentsDetailPage\r\n      if (treatmentId) {\r\n        navigate(`${ROUTES.TREATMENT_DETAIL}?id=${treatmentId}`, {\r\n          state: { treatment: treatmentData, patient: { id: treatmentData.patientId } }\r\n        });\r\n      }\r\n\r\n    } catch (error) {\r\n      log.error('開案失敗', error);\r\n      toast.current?.show({\r\n        severity: 'error',\r\n        summary: '開案失敗',\r\n        detail: '無法開啟治療案件，請稍後再試',\r\n        life: 3000\r\n      });\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div className=\"home-page\">\r\n      <Toast ref={toast} />\r\n      <div className=\"grid justify-content-center\">\r\n\r\n        {/* 當日行程表 */}\r\n        <div className=\"col-12 md:col-4 pl-4 pr-4\">\r\n          <div className=\"card\">\r\n            <h3 className='flex flex-wrap '>\r\n              <div className='col-12 md:col-6 flex justify-content-center'>今日預約療程</div>\r\n              <div className='col-12 md:col-6 flex justify-content-center'>({formatUtcToTaipei(new Date(), 'yyyy-MM-dd EEEE', { locale: zhTW })})\r\n              </div>\r\n            </h3>\r\n            <div className=\"calendar-container\">\r\n              <FullCalendar\r\n                plugins={[dayGridPlugin, timeGridPlugin]}\r\n                height=\"auto\"\r\n                initialView=\"timeGridDay\"\r\n                locale=\"zh-tw\"\r\n                headerToolbar={{\r\n                  left: '',\r\n                  center: '',\r\n                  right: '',\r\n                }}\r\n                dayHeaderFormat={{\r\n                  day: 'numeric', // 顯示日期數字 (例如: 13)\r\n                  weekday: 'short' // 顯示短格式的星期 (例如: 日, 一, 二)\r\n                }}\r\n                slotLabelFormat={{\r\n                  hour: '2-digit', // 小時顯示為兩位數 (例如: 09)\r\n                  minute: 'numeric', // 分鐘顯示為兩位數 (例如: 00)\r\n                  hour12: false // 禁用 12 小時制，啟用 24 小時制\r\n                }}\r\n                allDaySlot={false}\r\n                slotMinTime=\"09:00:00\"\r\n                slotMaxTime=\"22:00:00\"\r\n                events={todayEvents}\r\n                eventDisplay=\"block\"\r\n                dayMaxEvents={false}\r\n                businessHours={{\r\n                  daysOfWeek: [1, 2, 3, 4, 5, 6],\r\n                  startTime: '10:00',\r\n                  endTime: '18:00',\r\n                }}\r\n                eventClick={handleEventClick} // 啟用點擊事件\r\n                selectable={false} // 禁用選擇\r\n                editable={false} // 禁用編輯\r\n              />\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        \r\n        <div className=\"col-12 md:col-8 pl-4 pr-4 flex flex-wrap\">\r\n\r\n          {/* Basic Chart */}\r\n          <div className=\"col-12 md:col-6\">\r\n            <div className=\"card\">\r\n              <h3 className='flex justify-content-center'>月度統計(每小時更新)</h3>\r\n              <Chart type=\"bar\" \r\n              data={basicData} \r\n              options={chartOptions} />\r\n            </div>\r\n          </div>\r\n          {/* Line Chart */}\r\n          <div className=\"col-12 md:col-6\">\r\n            <div className=\"card\">\r\n              <h3 className='flex justify-content-center'>每週預約趨勢(每小時更新)</h3>\r\n              <Chart type=\"line\" \r\n              data={lineData} \r\n              options={chartOptions} />\r\n            </div>\r\n          </div>\r\n\r\n        </div>\r\n      </div>\r\n\r\n      {/* 事件詳細視窗 */}\r\n      <Dialog\r\n        header=\"預約詳情\"\r\n        visible={showEventDialog}\r\n        style={{ width: '450px' }}\r\n        onHide={() => setShowEventDialog(false)}\r\n        footer={\r\n          <div className=\"flex justify-content-end gap-2\">\r\n            <Button\r\n              label=\"取消\"\r\n              icon=\"pi pi-times\"\r\n              onClick={() => setShowEventDialog(false)}\r\n              className=\"p-button-text\"\r\n            />\r\n            <Button\r\n              label=\"開案\"\r\n              icon=\"pi pi-folder-open\"\r\n              onClick={handleOpenCase}\r\n              className=\"p-button-primary\"\r\n            />\r\n          </div>\r\n        }\r\n      >\r\n        {selectedEvent && (\r\n          <div className=\"grid\">\r\n            <div className=\"col-12\">\r\n              <div className=\"field\">\r\n                <label className=\"font-bold\">治療師:</label>\r\n                <p className=\"m-0\">{selectedEvent.doctorName}</p>\r\n              </div>\r\n            </div>\r\n            <div className=\"col-12\">\r\n              <div className=\"field\">\r\n                <label className=\"font-bold\">病患:</label>\r\n                <p className=\"m-0\">{selectedEvent.patientName}</p>\r\n              </div>\r\n            </div>\r\n            <div className=\"col-6\">\r\n              <div className=\"field\">\r\n                <label className=\"font-bold\">開始時間:</label>\r\n                <p className=\"m-0\">{formatUtcToTaipei(selectedEvent.start, 'HH:mm')}</p>\r\n              </div>\r\n            </div>\r\n            <div className=\"col-6\">\r\n              <div className=\"field\">\r\n                <label className=\"font-bold\">結束時間:</label>\r\n                <p className=\"m-0\">{formatUtcToTaipei(selectedEvent.end, 'HH:mm')}</p>\r\n              </div>\r\n            </div>\r\n            {selectedEvent.description && (\r\n              <div className=\"col-12\">\r\n                <div className=\"field\">\r\n                  <label className=\"font-bold\">備註:</label>\r\n                  <p className=\"m-0\">{selectedEvent.description}</p>\r\n                </div>\r\n              </div>\r\n            )}\r\n            {selectedEvent.treatmentId && (\r\n              <div className=\"col-12\">\r\n                <div className=\"field\">\r\n                  <label className=\"font-bold\">治療案件編號:</label>\r\n                  <p className=\"m-0\">{selectedEvent.treatmentId}</p>\r\n                </div>\r\n              </div>\r\n            )}\r\n          </div>\r\n        )}\r\n      </Dialog>\r\n\r\n      <Toast ref={toast} />\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default HomePage;"], "mappings": "AAAA,OAASA,IAAI,KAAQ,iBAAiB,CACtC,OAASC,iBAAiB,KAAQ,uBAAuB,CACzD,MAAO,CAAAC,aAAa,KAAM,uBAAuB,CACjD,MAAO,CAAAC,YAAY,KAAM,qBAAqB,CAC9C,MAAO,CAAAC,cAAc,KAAM,wBAAwB,CACnD,OAASC,KAAK,KAAQ,kBAAkB,CACxC,MAAO,CAAAC,KAAK,EAAIC,SAAS,CAAEC,QAAQ,CAAEC,MAAM,KAAQ,OAAO,CAC1D,OAASC,WAAW,KAAQ,kBAAkB,CAC9C,MAAO,CAAAC,GAAG,KAAM,oBAAoB,CACpC,OAASC,GAAG,KAAQ,oBAAoB,CACxC,OAASC,KAAK,KAAQ,kBAAkB,CACxC,OAASC,MAAM,KAAQ,mBAAmB,CAC1C,OAASC,MAAM,KAAQ,mBAAmB,CAC1C,OAASC,MAAM,KAAQ,wBAAwB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAsBhD,KAAM,CAAAC,QAAkB,CAAGA,CAAA,GAAM,CAC/B,KAAM,CAAAC,QAAQ,CAAGZ,WAAW,CAAC,CAAC,CAC9B,KAAM,CAAAa,KAAK,CAAGd,MAAM,CAAQ,IAAI,CAAC,CACjC,KAAM,CAACe,SAAS,CAAEC,YAAY,CAAC,CAAGjB,QAAQ,CAAC,CAAC,CAAC,CAAC,CAC9C,KAAM,CAACkB,QAAQ,CAAEC,WAAW,CAAC,CAAGnB,QAAQ,CAAC,CAAC,CAAC,CAAC,CAC5C,KAAM,CAACoB,YAAY,CAAEC,eAAe,CAAC,CAAGrB,QAAQ,CAAC,CAAC,CAAC,CAAC,CACpD,KAAM,CAACsB,WAAW,CAAEC,cAAc,CAAC,CAAGvB,QAAQ,CAAkB,EAAE,CAAC,CACnE,KAAM,CAACwB,eAAe,CAAEC,kBAAkB,CAAC,CAAGzB,QAAQ,CAAC,KAAK,CAAC,CAC7D,KAAM,CAAC0B,aAAa,CAAEC,gBAAgB,CAAC,CAAG3B,QAAQ,CAAuB,IAAI,CAAC,CAE9ED,SAAS,CAAC,IAAM,CACd,KAAM,CAAA6B,QAAQ,CAAG,KAAAA,CAAA,GAAY,CAC3B;AACA,KAAM,CAAAC,aAAa,CAAGC,gBAAgB,CAACC,QAAQ,CAACC,eAAe,CAAC,CAChE,KAAM,CAAAC,SAAS,CAAGJ,aAAa,CAACK,gBAAgB,CAAC,cAAc,CAAC,CAChE,KAAM,CAAAC,kBAAkB,CAAGN,aAAa,CAACK,gBAAgB,CAAC,wBAAwB,CAAC,CACnF,KAAM,CAAAE,aAAa,CAAGP,aAAa,CAACK,gBAAgB,CAAC,kBAAkB,CAAC,CAExE;AACA,KAAM,CAAAG,OAAO,CAAG,CACdC,mBAAmB,CAAE,KAAK,CAC1BC,WAAW,CAAE,GAAG,CAChBC,OAAO,CAAE,CACPC,MAAM,CAAE,CACNC,MAAM,CAAE,CACNC,SAAS,CAAEV,SACb,CACF,CACF,CAAC,CACDW,MAAM,CAAE,CACNC,CAAC,CAAE,CACDC,KAAK,CAAE,CACLC,KAAK,CAAEZ,kBACT,CAAC,CACDa,IAAI,CAAE,CACJD,KAAK,CAAEX,aACT,CACF,CAAC,CACDa,CAAC,CAAE,CACDH,KAAK,CAAE,CACLC,KAAK,CAAEZ,kBACT,CAAC,CACDa,IAAI,CAAE,CACJD,KAAK,CAAEX,aACT,CACF,CACF,CACF,CAAC,CAED;AACA,GAAI,CACFhC,GAAG,CAACD,GAAG,CAAC,YAAY,CAAC,CACrB,KAAM,CAAA+C,QAAQ,CAAG,KAAM,CAAA/C,GAAG,CAACgD,GAAG,CAAC,+BAA+B,CAAC,CAC/D/C,GAAG,CAACD,GAAG,CAAC,OAAO,CAAE+C,QAAQ,CAACE,IAAI,CAAC,CAE/B;AACA,KAAM,CAAAC,SAAS,CAAG,MAAO,CAAAH,QAAQ,CAACE,IAAI,GAAK,QAAQ,CAC/CE,IAAI,CAACC,KAAK,CAACL,QAAQ,CAACE,IAAI,CAAC,CACzBF,QAAQ,CAACE,IAAI,CAEjB;AACA,KAAM,CAAAI,eAA8B,CAAG,CACrCC,KAAK,CAAEJ,SAAS,CAACI,KAAK,EAAIJ,SAAS,CAACX,MAAM,EAAI,EAAE,CAChDgB,SAAS,CAAEL,SAAS,CAACK,SAAS,EAAI,EAAE,CACpCC,UAAU,CAAEN,SAAS,CAACM,UAAU,EAAI,EACtC,CAAC,CAEDvD,GAAG,CAACwD,EAAE,CAAC,SAAS,CAAEP,SAAS,CAAC,CAC5BjD,GAAG,CAACwD,EAAE,CAAC,UAAU,CAAEJ,eAAe,CAAC,CAEnC;AACA,KAAM,CAAAK,cAAc,CAAG,CACrBnB,MAAM,CAAEc,eAAe,CAACC,KAAK,CAC7BK,QAAQ,CAAE,CACR,CACEC,KAAK,CAAE,MAAM,CACbC,eAAe,CAAEnC,aAAa,CAACK,gBAAgB,CAAC,YAAY,CAAC,CAC7D+B,WAAW,CAAEpC,aAAa,CAACK,gBAAgB,CAAC,YAAY,CAAC,CACzDkB,IAAI,CAAEI,eAAe,CAACE,SACxB,CAAC,CACD,CACEK,KAAK,CAAE,MAAM,CACbC,eAAe,CAAEnC,aAAa,CAACK,gBAAgB,CAAC,YAAY,CAAC,CAC7D+B,WAAW,CAAEpC,aAAa,CAACK,gBAAgB,CAAC,YAAY,CAAC,CACzDkB,IAAI,CAAEI,eAAe,CAACG,UACxB,CAAC,CAEL,CAAC,CAEDvD,GAAG,CAACwD,EAAE,CAAC,SAAS,CAAEC,cAAc,CAAC,CACjC5C,YAAY,CAAC4C,cAAc,CAAC,CAC9B,CAAE,MAAOK,KAAK,CAAE,KAAAC,cAAA,CACd/D,GAAG,CAAC8D,KAAK,CAAC,SAAS,CAAEA,KAAK,CAAC,CAC3B;AACA,CAAAC,cAAA,CAAApD,KAAK,CAACqD,OAAO,UAAAD,cAAA,iBAAbA,cAAA,CAAeE,IAAI,CAAC,CAClBC,QAAQ,CAAE,OAAO,CACjBC,OAAO,CAAE,IAAI,CACbC,MAAM,CAAE,SACV,CAAC,CAAC,CACJ,CAEA;AACA,GAAI,CACFpE,GAAG,CAACD,GAAG,CAAC,YAAY,CAAC,CACrB,KAAM,CAAA+C,QAAQ,CAAG,KAAM,CAAA/C,GAAG,CAACgD,GAAG,CAAC,8BAA8B,CAAC,CAC9D/C,GAAG,CAACD,GAAG,CAAC,OAAO,CAAE+C,QAAQ,CAACE,IAAI,CAAC,CAE/B;AACA,KAAM,CAAAqB,QAAQ,CAAG,MAAO,CAAAvB,QAAQ,CAACE,IAAI,GAAK,QAAQ,CAC9CE,IAAI,CAACC,KAAK,CAACL,QAAQ,CAACE,IAAI,CAAC,CACzBF,QAAQ,CAACE,IAAI,CAEjB;AACA,KAAM,CAAAI,eAA8B,CAAG,CACrCC,KAAK,CAAEgB,QAAQ,CAAChB,KAAK,CAACiB,GAAG,CAAEX,KAAY,KAAAY,MAAA,CAAQZ,KAAK,WAAI,CAAC,EAAIU,QAAQ,CAAC/B,MAAM,CAACgC,GAAG,CAAEX,KAAY,KAAAY,MAAA,CAAQZ,KAAK,WAAI,CAAC,EAAI,EAAE,CACtHL,SAAS,CAAEe,QAAQ,CAACf,SAAS,EAAI,EAAE,CACnCC,UAAU,CAAEc,QAAQ,CAACd,UAAU,EAAI,EACrC,CAAC,CAEDvD,GAAG,CAACwD,EAAE,CAAC,SAAS,CAAEa,QAAQ,CAAC,CAC3BrE,GAAG,CAACwD,EAAE,CAAC,UAAU,CAAEJ,eAAe,CAAC,CAEnC;AACA,KAAM,CAAAoB,aAAa,CAAG,CACpBlC,MAAM,CAAEc,eAAe,CAACC,KAAK,CAC7BK,QAAQ,CAAE,CACR,CACEC,KAAK,CAAE,MAAM,CACbX,IAAI,CAAEI,eAAe,CAACE,SAAS,CAC/BmB,IAAI,CAAE,KAAK,CACXb,eAAe,CAAEnC,aAAa,CAACK,gBAAgB,CAAC,YAAY,CAAC,CAC7D+B,WAAW,CAAEpC,aAAa,CAACK,gBAAgB,CAAC,YAAY,CAAC,CACzD4C,OAAO,CAAE,GACX,CAAC,CACD,CACEf,KAAK,CAAE,MAAM,CACbX,IAAI,CAAEI,eAAe,CAACG,UAAU,CAChCkB,IAAI,CAAE,KAAK,CACXb,eAAe,CAAEnC,aAAa,CAACK,gBAAgB,CAAC,YAAY,CAAC,CAC7D+B,WAAW,CAAEpC,aAAa,CAACK,gBAAgB,CAAC,YAAY,CAAC,CACzD4C,OAAO,CAAE,GACX,CAAC,CAEL,CAAC,CAED1E,GAAG,CAACwD,EAAE,CAAC,SAAS,CAAEgB,aAAa,CAAC,CAChCzD,WAAW,CAACyD,aAAa,CAAC,CAC5B,CAAE,MAAOV,KAAK,CAAE,KAAAa,eAAA,CACd3E,GAAG,CAAC8D,KAAK,CAAC,SAAS,CAAEA,KAAK,CAAC,CAC3B;AACA,CAAAa,eAAA,CAAAhE,KAAK,CAACqD,OAAO,UAAAW,eAAA,iBAAbA,eAAA,CAAeV,IAAI,CAAC,CAClBC,QAAQ,CAAE,OAAO,CACjBC,OAAO,CAAE,IAAI,CACbC,MAAM,CAAE,SACV,CAAC,CAAC,CACJ,CAEAnD,eAAe,CAACgB,OAAO,CAAC,CAExB;AACA,GAAI,CACFjC,GAAG,CAACD,GAAG,CAAC,aAAa,CAAC,CACtB,KAAM,CAAA+C,QAAQ,CAAG,KAAM,CAAA/C,GAAG,CAACgD,GAAG,CAAC,iCAAiC,CAAC,CACjE/C,GAAG,CAACD,GAAG,CAAC,QAAQ,CAAE+C,QAAQ,CAACE,IAAI,CAAC,CAEhC;AACA,KAAM,CAAA4B,kBAAkB,CAAG,MAAO,CAAA9B,QAAQ,CAACE,IAAI,GAAK,QAAQ,CACxDE,IAAI,CAACC,KAAK,CAACL,QAAQ,CAACE,IAAI,CAAC,CACzBF,QAAQ,CAACE,IAAI,CAEjB;AACA,KAAM,CAAAI,eAAgC,CAAGwB,kBAAkB,CAACN,GAAG,CAAEO,KAAU,GAAM,CAC/EC,EAAE,CAAED,KAAK,CAACE,EAAE,EAAIF,KAAK,CAACC,EAAE,CACxBE,KAAK,CAAEH,KAAK,CAACI,KAAK,EAAIJ,KAAK,CAACG,KAAK,CACjCE,KAAK,CAAEL,KAAK,CAACM,KAAK,EAAIN,KAAK,CAACK,KAAK,CACjCE,GAAG,CAAEP,KAAK,CAACQ,GAAG,EAAIR,KAAK,CAACO,GAAG,CAC3BxB,eAAe,CAAEiB,KAAK,CAACS,eAAe,EAAIT,KAAK,CAACjB,eAAe,EAAI,SAAS,CAC5E2B,QAAQ,CAAEV,KAAK,CAACW,QAAQ,EAAIX,KAAK,CAACU,QAAQ,CAC1CE,SAAS,CAAEZ,KAAK,CAACa,SAAS,EAAIb,KAAK,CAACY,SAAS,CAC7CE,WAAW,CAAEd,KAAK,CAACe,WAAW,EAAIf,KAAK,CAACc,WAAW,CACnDE,UAAU,CAAEhB,KAAK,CAACiB,UAAU,EAAIjB,KAAK,CAACgB,UAAU,CAChDE,WAAW,CAAElB,KAAK,CAACmB,WAAW,EAAInB,KAAK,CAACkB,WAAW,CACnDE,WAAW,CAAEpB,KAAK,CAACqB,WAAW,EAAIrB,KAAK,CAACoB,WAC1C,CAAC,CAAC,CAAC,CAEHjG,GAAG,CAACwD,EAAE,CAAC,MAAM,CAAEJ,eAAe,CAAC,CAC/BjC,cAAc,CAACiC,eAAe,CAAC,CAEjC,CAAE,MAAOU,KAAK,CAAE,KAAAqC,eAAA,CACdnG,GAAG,CAAC8D,KAAK,CAAC,UAAU,CAAEA,KAAK,CAAC,CAC5B;AACA,CAAAqC,eAAA,CAAAxF,KAAK,CAACqD,OAAO,UAAAmC,eAAA,iBAAbA,eAAA,CAAelC,IAAI,CAAC,CAClBC,QAAQ,CAAE,OAAO,CACjBC,OAAO,CAAE,IAAI,CACbC,MAAM,CAAE,UACV,CAAC,CAAC,CACJ,CAEF,CAAC,CAED5C,QAAQ,CAAC,CAAC,CACZ,CAAC,CAAE,EAAE,CAAC,CAEN;AACA,KAAM,CAAA4E,gBAAgB,CAAIC,SAAc,EAAK,CAC3C,KAAM,CAAAC,OAAO,CAAGD,SAAS,CAACxB,KAAK,CAACC,EAAE,CAClC,KAAM,CAAAD,KAAK,CAAG3D,WAAW,CAACqF,IAAI,CAACC,CAAC,EAAIA,CAAC,CAAC1B,EAAE,GAAKwB,OAAO,CAAC,CACrD,GAAIzB,KAAK,CAAE,CACTtD,gBAAgB,CAACsD,KAAK,CAAC,CACvBxD,kBAAkB,CAAC,IAAI,CAAC,CAC1B,CACF,CAAC,CAED;AACA,KAAM,CAAAoF,cAAc,CAAG,KAAAA,CAAA,GAAY,CACjC,GAAI,CAACnF,aAAa,CAAE,OAEpB,GAAI,CACF,GAAI,CAAAqE,WAAW,CAAGrE,aAAa,CAACqE,WAAW,CAE3C,GAAI,CAACA,WAAW,CAAE,CAChB;AACA3F,GAAG,CAACD,GAAG,CAAC,UAAU,CAAC,CACnB,KAAM,CAAA2G,cAAc,CAAG,KAAM,CAAA3G,GAAG,CAAC4G,IAAI,CAAC,uBAAuB,CAAE,CAC7DjB,SAAS,CAAEpE,aAAa,CAACmE,SAAS,CAClCD,QAAQ,CAAElE,aAAa,CAACiE,QACxB;AACF,CAAC,CAAC,CAEFI,WAAW,CAAGe,cAAc,CAAC1D,IAAI,CAAC2C,WAAW,CAC7C3F,GAAG,CAACD,GAAG,CAAC,WAAW,CAAE,CAAE4F,WAAY,CAAC,CAAC,CAErC;AACA,KAAM,CAAA5F,GAAG,CAAC6G,KAAK,kBAAArC,MAAA,CAAkBjD,aAAa,CAACwD,EAAE,eAAc,CAC7Dc,WAAW,CAAED,WACf,CAAC,CAAC,CACF3F,GAAG,CAACD,GAAG,CAAC,2BAA2B,CAAC,CACtC,CAEA;AACAC,GAAG,CAACD,GAAG,CAAC,QAAQ,CAAE,CAAE4F,WAAY,CAAC,CAAC,CAElC,KAAM,CAAAkB,iBAAiB,CAAG,KAAM,CAAA9G,GAAG,CAACgD,GAAG,CAAC,gBAAgB,CAAE,CACxD+D,MAAM,CAAE,CACN/B,EAAE,CAAEY,WACN,CACF,CAAC,CAAC,CAEF,KAAM,CAAAoB,aAAa,CAAGF,iBAAiB,CAAC7D,IAAI,CAE5C;AACA,GAAI2C,WAAW,CAAE,CACfjF,QAAQ,IAAA6D,MAAA,CAAInE,MAAM,CAAC4G,gBAAgB,SAAAzC,MAAA,CAAOoB,WAAW,EAAI,CACvDsB,KAAK,CAAE,CAAEC,SAAS,CAAEH,aAAa,CAAEI,OAAO,CAAE,CAAErC,EAAE,CAAEiC,aAAa,CAACtB,SAAU,CAAE,CAC9E,CAAC,CAAC,CACJ,CAEF,CAAE,MAAO3B,KAAK,CAAE,KAAAsD,eAAA,CACdpH,GAAG,CAAC8D,KAAK,CAAC,MAAM,CAAEA,KAAK,CAAC,CACxB,CAAAsD,eAAA,CAAAzG,KAAK,CAACqD,OAAO,UAAAoD,eAAA,iBAAbA,eAAA,CAAenD,IAAI,CAAC,CAClBC,QAAQ,CAAE,OAAO,CACjBC,OAAO,CAAE,MAAM,CACfC,MAAM,CAAE,gBAAgB,CACxBiD,IAAI,CAAE,IACR,CAAC,CAAC,CACJ,CACF,CAAC,CAED,mBACE7G,KAAA,QAAK8G,SAAS,CAAC,WAAW,CAAAC,QAAA,eACxBjH,IAAA,CAACL,KAAK,EAACuH,GAAG,CAAE7G,KAAM,CAAE,CAAC,cACrBH,KAAA,QAAK8G,SAAS,CAAC,6BAA6B,CAAAC,QAAA,eAG1CjH,IAAA,QAAKgH,SAAS,CAAC,2BAA2B,CAAAC,QAAA,cACxC/G,KAAA,QAAK8G,SAAS,CAAC,MAAM,CAAAC,QAAA,eACnB/G,KAAA,OAAI8G,SAAS,CAAC,iBAAiB,CAAAC,QAAA,eAC7BjH,IAAA,QAAKgH,SAAS,CAAC,6CAA6C,CAAAC,QAAA,CAAC,sCAAM,CAAK,CAAC,cACzE/G,KAAA,QAAK8G,SAAS,CAAC,6CAA6C,CAAAC,QAAA,EAAC,GAAC,CAAClI,iBAAiB,CAAC,GAAI,CAAAoI,IAAI,CAAC,CAAC,CAAE,iBAAiB,CAAE,CAAEC,MAAM,CAAEtI,IAAK,CAAC,CAAC,CAAC,GAClI,EAAK,CAAC,EACJ,CAAC,cACLkB,IAAA,QAAKgH,SAAS,CAAC,oBAAoB,CAAAC,QAAA,cACjCjH,IAAA,CAACf,YAAY,EACX6C,OAAO,CAAE,CAAC9C,aAAa,CAAEE,cAAc,CAAE,CACzCmI,MAAM,CAAC,MAAM,CACbC,WAAW,CAAC,aAAa,CACzBF,MAAM,CAAC,OAAO,CACdG,aAAa,CAAE,CACbC,IAAI,CAAE,EAAE,CACRC,MAAM,CAAE,EAAE,CACVC,KAAK,CAAE,EACT,CAAE,CACFC,eAAe,CAAE,CACfC,GAAG,CAAE,SAAS,CAAE;AAChBC,OAAO,CAAE,OAAQ;AACnB,CAAE,CACFC,eAAe,CAAE,CACfC,IAAI,CAAE,SAAS,CAAE;AACjBC,MAAM,CAAE,SAAS,CAAE;AACnBC,MAAM,CAAE,KAAM;AAChB,CAAE,CACFC,UAAU,CAAE,KAAM,CAClBC,WAAW,CAAC,UAAU,CACtBC,WAAW,CAAC,UAAU,CACtBC,MAAM,CAAEzH,WAAY,CACpB0H,YAAY,CAAC,OAAO,CACpBC,YAAY,CAAE,KAAM,CACpBC,aAAa,CAAE,CACbC,UAAU,CAAE,CAAC,CAAC,CAAE,CAAC,CAAE,CAAC,CAAE,CAAC,CAAE,CAAC,CAAE,CAAC,CAAC,CAC9BC,SAAS,CAAE,OAAO,CAClBC,OAAO,CAAE,OACX,CAAE,CACFC,UAAU,CAAE9C,gBAAkB;AAAA,CAC9B+C,UAAU,CAAE,KAAO;AAAA,CACnBC,QAAQ,CAAE,KAAO;AAAA,CAClB,CAAC,CACC,CAAC,EACH,CAAC,CACH,CAAC,cAGN5I,KAAA,QAAK8G,SAAS,CAAC,0CAA0C,CAAAC,QAAA,eAGvDjH,IAAA,QAAKgH,SAAS,CAAC,iBAAiB,CAAAC,QAAA,cAC9B/G,KAAA,QAAK8G,SAAS,CAAC,MAAM,CAAAC,QAAA,eACnBjH,IAAA,OAAIgH,SAAS,CAAC,6BAA6B,CAAAC,QAAA,CAAC,0DAAW,CAAI,CAAC,cAC5DjH,IAAA,CAACb,KAAK,EAAC4J,IAAI,CAAC,KAAK,CACjBrG,IAAI,CAAEpC,SAAU,CAChBqB,OAAO,CAAEjB,YAAa,CAAE,CAAC,EACtB,CAAC,CACH,CAAC,cAENV,IAAA,QAAKgH,SAAS,CAAC,iBAAiB,CAAAC,QAAA,cAC9B/G,KAAA,QAAK8G,SAAS,CAAC,MAAM,CAAAC,QAAA,eACnBjH,IAAA,OAAIgH,SAAS,CAAC,6BAA6B,CAAAC,QAAA,CAAC,sEAAa,CAAI,CAAC,cAC9DjH,IAAA,CAACb,KAAK,EAAC4J,IAAI,CAAC,MAAM,CAClBrG,IAAI,CAAElC,QAAS,CACfmB,OAAO,CAAEjB,YAAa,CAAE,CAAC,EACtB,CAAC,CACH,CAAC,EAEH,CAAC,EACH,CAAC,cAGNV,IAAA,CAACJ,MAAM,EACLoJ,MAAM,CAAC,0BAAM,CACbC,OAAO,CAAEnI,eAAgB,CACzBoI,KAAK,CAAE,CAAEC,KAAK,CAAE,OAAQ,CAAE,CAC1BC,MAAM,CAAEA,CAAA,GAAMrI,kBAAkB,CAAC,KAAK,CAAE,CACxCsI,MAAM,cACJnJ,KAAA,QAAK8G,SAAS,CAAC,gCAAgC,CAAAC,QAAA,eAC7CjH,IAAA,CAACH,MAAM,EACLwD,KAAK,CAAC,cAAI,CACViG,IAAI,CAAC,aAAa,CAClBC,OAAO,CAAEA,CAAA,GAAMxI,kBAAkB,CAAC,KAAK,CAAE,CACzCiG,SAAS,CAAC,eAAe,CAC1B,CAAC,cACFhH,IAAA,CAACH,MAAM,EACLwD,KAAK,CAAC,cAAI,CACViG,IAAI,CAAC,mBAAmB,CACxBC,OAAO,CAAEpD,cAAe,CACxBa,SAAS,CAAC,kBAAkB,CAC7B,CAAC,EACC,CACN,CAAAC,QAAA,CAEAjG,aAAa,eACZd,KAAA,QAAK8G,SAAS,CAAC,MAAM,CAAAC,QAAA,eACnBjH,IAAA,QAAKgH,SAAS,CAAC,QAAQ,CAAAC,QAAA,cACrB/G,KAAA,QAAK8G,SAAS,CAAC,OAAO,CAAAC,QAAA,eACpBjH,IAAA,UAAOgH,SAAS,CAAC,WAAW,CAAAC,QAAA,CAAC,qBAAI,CAAO,CAAC,cACzCjH,IAAA,MAAGgH,SAAS,CAAC,KAAK,CAAAC,QAAA,CAAEjG,aAAa,CAACuE,UAAU,CAAI,CAAC,EAC9C,CAAC,CACH,CAAC,cACNvF,IAAA,QAAKgH,SAAS,CAAC,QAAQ,CAAAC,QAAA,cACrB/G,KAAA,QAAK8G,SAAS,CAAC,OAAO,CAAAC,QAAA,eACpBjH,IAAA,UAAOgH,SAAS,CAAC,WAAW,CAAAC,QAAA,CAAC,eAAG,CAAO,CAAC,cACxCjH,IAAA,MAAGgH,SAAS,CAAC,KAAK,CAAAC,QAAA,CAAEjG,aAAa,CAACyE,WAAW,CAAI,CAAC,EAC/C,CAAC,CACH,CAAC,cACNzF,IAAA,QAAKgH,SAAS,CAAC,OAAO,CAAAC,QAAA,cACpB/G,KAAA,QAAK8G,SAAS,CAAC,OAAO,CAAAC,QAAA,eACpBjH,IAAA,UAAOgH,SAAS,CAAC,WAAW,CAAAC,QAAA,CAAC,2BAAK,CAAO,CAAC,cAC1CjH,IAAA,MAAGgH,SAAS,CAAC,KAAK,CAAAC,QAAA,CAAElI,iBAAiB,CAACiC,aAAa,CAAC4D,KAAK,CAAE,OAAO,CAAC,CAAI,CAAC,EACrE,CAAC,CACH,CAAC,cACN5E,IAAA,QAAKgH,SAAS,CAAC,OAAO,CAAAC,QAAA,cACpB/G,KAAA,QAAK8G,SAAS,CAAC,OAAO,CAAAC,QAAA,eACpBjH,IAAA,UAAOgH,SAAS,CAAC,WAAW,CAAAC,QAAA,CAAC,2BAAK,CAAO,CAAC,cAC1CjH,IAAA,MAAGgH,SAAS,CAAC,KAAK,CAAAC,QAAA,CAAElI,iBAAiB,CAACiC,aAAa,CAAC8D,GAAG,CAAE,OAAO,CAAC,CAAI,CAAC,EACnE,CAAC,CACH,CAAC,CACL9D,aAAa,CAAC2E,WAAW,eACxB3F,IAAA,QAAKgH,SAAS,CAAC,QAAQ,CAAAC,QAAA,cACrB/G,KAAA,QAAK8G,SAAS,CAAC,OAAO,CAAAC,QAAA,eACpBjH,IAAA,UAAOgH,SAAS,CAAC,WAAW,CAAAC,QAAA,CAAC,eAAG,CAAO,CAAC,cACxCjH,IAAA,MAAGgH,SAAS,CAAC,KAAK,CAAAC,QAAA,CAAEjG,aAAa,CAAC2E,WAAW,CAAI,CAAC,EAC/C,CAAC,CACH,CACN,CACA3E,aAAa,CAACqE,WAAW,eACxBrF,IAAA,QAAKgH,SAAS,CAAC,QAAQ,CAAAC,QAAA,cACrB/G,KAAA,QAAK8G,SAAS,CAAC,OAAO,CAAAC,QAAA,eACpBjH,IAAA,UAAOgH,SAAS,CAAC,WAAW,CAAAC,QAAA,CAAC,uCAAO,CAAO,CAAC,cAC5CjH,IAAA,MAAGgH,SAAS,CAAC,KAAK,CAAAC,QAAA,CAAEjG,aAAa,CAACqE,WAAW,CAAI,CAAC,EAC/C,CAAC,CACH,CACN,EACE,CACN,CACK,CAAC,cAETrF,IAAA,CAACL,KAAK,EAACuH,GAAG,CAAE7G,KAAM,CAAE,CAAC,EAClB,CAAC,CAEV,CAAC,CAED,cAAe,CAAAF,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
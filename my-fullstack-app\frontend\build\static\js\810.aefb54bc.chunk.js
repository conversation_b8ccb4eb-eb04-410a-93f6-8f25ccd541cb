"use strict";(self.webpackChunkfrontend=self.webpackChunkfrontend||[]).push([[810],{3810:(e,t,a)=>{a.r(t),a.d(t,{default:()=>g});var l=a(9379),s=a(5855),i=a(4625),d=a(7807),n=a(7285),r=a(3805),o=a(2018),c=a(5371),m=a(1104),u=a(5797),p=a(8060),h=a(2052),v=a(828),b=a(4972),y=a(5831),x=a(5043),j=a(5666),N=a(724),w=a(402),I=a(579);const g=()=>{const e=(0,x.useRef)(null),t=(0,j.Zp)(),[a,g]=(0,x.useState)([]),[f,T]=(0,x.useState)(!1),[C,k]=(0,x.useState)("add"),[D,A]=(0,x.useState)(null),[S,F]=(0,x.useState)([]),[R,$]=(0,x.useState)([]),[H,L]=(0,x.useState)(!1),[M,V]=(0,x.useState)({doctorId:null,patientId:null,startDate:null,endDate:null,startTime:"09:00",endTime:"10:00",isRepeat:!1,repeatType:"daily",repeatCount:1});(0,x.useEffect)((()=>{(async()=>{try{var t;console.log("\u958b\u59cb\u8f09\u5165\u6578\u64da...");const[a,l,s]=await Promise.all([w.A.get("/api/users/DoctorList"),w.A.get("/api/patients/PatientList"),w.A.get("/api/schedule")]);console.log("\u6cbb\u7642\u5e2b\u6578\u64da:",a.data),console.log("\u75c5\u60a3\u6578\u64da:",l.data),console.log("\u884c\u7a0b\u6578\u64da:",s.data);const i=a.data,d=l.data,n=s.data;F(i),$(d);const r=n.map((e=>({id:e.id,title:e.title,start:e.start,end:e.end,backgroundColor:e.backgroundColor||"#3788d8",borderColor:e.borderColor,doctorId:e.doctorId,patientId:e.patientId,doctorName:e.doctorName,patientName:e.patientName})));console.log("\u683c\u5f0f\u5316\u5f8c\u7684\u4e8b\u4ef6:",r),g(r),null===(t=e.current)||void 0===t||t.show({severity:"success",summary:"\u6210\u529f",detail:"\u6578\u64da\u8f09\u5165\u5b8c\u6210"})}catch(l){var a;console.error("\u8f09\u5165\u6578\u64da\u5931\u6557:",l),null===(a=e.current)||void 0===a||a.show({severity:"error",summary:"\u932f\u8aa4",detail:"\u8f09\u5165\u6578\u64da\u5931\u6557"})}})()}),[]);const E=async()=>{try{var t;L(!0);const a=await w.A.get("/api/schedule"),l=a.data.map((e=>({id:e.id,title:e.title,start:e.start,end:e.end,backgroundColor:e.backgroundColor||"#3788d8",borderColor:e.borderColor,doctorId:e.doctorId,patientId:e.patientId,treatmentId:e.treatmentId,doctorName:e.doctorName,patientName:e.patientName,description:e.description})));g(l),null===(t=e.current)||void 0===t||t.show({severity:"success",summary:"\u6210\u529f",detail:"\u884c\u7a0b\u6578\u64da\u5df2\u91cd\u65b0\u8f09\u5165"})}catch(l){var a;console.error("\u91cd\u65b0\u8f09\u5165\u884c\u7a0b\u5931\u6557:",l),null===(a=e.current)||void 0===a||a.show({severity:"error",summary:"\u932f\u8aa4",detail:"\u91cd\u65b0\u8f09\u5165\u884c\u7a0b\u5931\u6557"})}finally{L(!1)}},G=(e,t)=>{V((a=>(0,l.A)((0,l.A)({},a),{},{[e]:t})))},O=async()=>{var t;if(M.doctorId&&M.patientId&&M.startDate&&M.endDate)try{const t=new Date(M.startDate),i=new Date(M.endDate),[d,n]=M.startTime.split(":"),[r,o]=M.endTime.split(":");t.setHours(parseInt(d),parseInt(n)),i.setHours(parseInt(r),parseInt(o));const c=e=>{switch(e){case"daily":return 1;case"weekly":return 2;case"monthly":return 3;default:return 0}},m={doctorId:M.doctorId,patientId:M.patientId,startDateTime:t.toISOString(),endDateTime:i.toISOString(),description:"",backgroundColor:"#3788d8",repeatType:M.isRepeat?c(M.repeatType):0,repeatCount:M.isRepeat?M.repeatCount:1};if("edit"===C&&D&&D.id){var a;const t=(0,l.A)((0,l.A)({},m),{},{id:parseInt(D.id)});await w.A.put("/api/schedule/".concat(D.id),t),await E(),null===(a=e.current)||void 0===a||a.show({severity:"success",summary:"\u6210\u529f",detail:"\u884c\u7a0b\u5df2\u66f4\u65b0"})}else{var s;await w.A.post("/api/schedule",m),await E(),null===(s=e.current)||void 0===s||s.show({severity:"success",summary:"\u6210\u529f",detail:"\u884c\u7a0b\u5df2\u65b0\u589e"})}T(!1),P()}catch(r){var i,d,n;console.error("\u64cd\u4f5c\u5931\u6557:",r),null===(i=e.current)||void 0===i||i.show({severity:"error",summary:"\u932f\u8aa4",detail:(null===(d=r.response)||void 0===d||null===(n=d.data)||void 0===n?void 0:n.message)||"\u64cd\u4f5c\u5931\u6557"})}else null===(t=e.current)||void 0===t||t.show({severity:"warn",summary:"\u8b66\u544a",detail:"\u8acb\u586b\u5beb\u6240\u6709\u5fc5\u8981\u6b04\u4f4d"})},P=()=>{V({doctorId:null,patientId:null,startDate:null,endDate:null,startTime:"09:00",endTime:"10:00",isRepeat:!1,repeatType:"daily",repeatCount:1}),A(null),k("add")};return(0,I.jsxs)("div",{className:"schedules-page",children:[(0,I.jsx)(v.y,{ref:e}),(0,I.jsx)(m.T,{}),(0,I.jsx)("div",{className:"card",hidden:!0,children:(0,I.jsx)("div",{className:"flex pb-3 justify-content-end align-items-center",children:(0,I.jsxs)("div",{className:"flex gap-2",children:[(0,I.jsx)(o.$,{label:"\u91cd\u65b0\u8f09\u5165",icon:H?"pi pi-spin pi-spinner":"pi pi-refresh",onClick:E,className:"p-button-secondary",outlined:!0,disabled:H}),(0,I.jsx)(o.$,{label:"\u65b0\u589e\u884c\u7a0b",icon:"pi pi-plus",onClick:()=>T(!0),className:"p-button-primary"})]})})}),(0,I.jsx)("div",{className:"card",children:(0,I.jsx)("div",{className:"calendar-container",children:(0,I.jsx)(n.A,{plugins:[i.A,r.A,d.Ay],height:"auto",initialView:"timeGridWeek",locale:"zh-tw",weekends:!0,allDaySlot:!1,headerToolbar:{left:"prev,next today",center:"title",right:"reloadDate,addDate dayGridMonth,timeGridWeek"},titleFormat:{year:"numeric",month:"long"},dayHeaderFormat:{day:"numeric",weekday:"narrow"},slotLabelFormat:{hour:"2-digit",minute:"numeric",hour12:!1},customButtons:{reloadDate:{text:"\u91cd\u6574",click:E},addDate:{text:"\u65b0\u589e",click:()=>T(!0)}},slotMinTime:"09:00:00",slotMaxTime:"22:00:00",events:a,eventClick:e=>{const t=a.find((t=>t.id===e.event.id));t&&(A(t),k("view"),V({doctorId:t.doctorId||null,patientId:t.patientId||null,startDate:new Date(t.start),endDate:new Date(t.end),startTime:(0,s.$)(t.start,"HH:mm"),endTime:(0,s.$)(t.end,"HH:mm"),isRepeat:!1,repeatType:"daily",repeatCount:1}),T(!0))},selectable:!0,selectMirror:!0,dayMaxEvents:!0,businessHours:{daysOfWeek:[1,2,3,4,5,6],startTime:"10:00",endTime:"18:00"}})})}),(0,I.jsxs)(u.l,{header:"add"===C?"\u65b0\u589e\u884c\u7a0b":"edit"===C?"\u7de8\u8f2f\u884c\u7a0b":"\u884c\u7a0b\u8a73\u60c5",visible:f,style:{width:"500px"},onHide:()=>{T(!1),P()},footer:(0,I.jsx)("div",{className:"flex gap-2",children:"view"===C?(0,I.jsxs)(I.Fragment,{children:[(0,I.jsx)(o.$,{label:"\u958b\u6848",icon:"pi pi-folder-open",className:"p-button-success",onClick:async()=>{if(D&&D.patientId)try{let e=D.treatmentId;if(!e){console.log("\u5275\u5efa\u65b0\u7684\u6cbb\u7642\u6848\u4ef6");e=(await w.A.post("/api/treatment/Insert",{PatientId:D.patientId,DoctorId:D.doctorId})).data.treatmentId,console.log("\u65b0\u6cbb\u7642\u6848\u4ef6\u5275\u5efa\u6210\u529f",{treatmentId:e}),await w.A.patch("/api/schedule/".concat(D.id,"/treatment"),{TreatmentId:e}),console.log("Schedule TreatmentId \u66f4\u65b0\u6210\u529f"),E()}console.log("\u7372\u53d6\u6cbb\u7642\u6578\u64da",{treatmentId:e});const a=(await w.A.get("/api/treatment",{params:{treatmentsId:e}})).data;e&&t("".concat(N.bw.TREATMENT_DETAIL,"?id=").concat(e),{state:{treatment:a,patient:{id:a.patientId}}})}catch(l){var a;console.error("\u958b\u6848\u5931\u6557",l),null===(a=e.current)||void 0===a||a.show({severity:"error",summary:"\u958b\u6848\u5931\u6557",detail:"\u7121\u6cd5\u958b\u555f\u6cbb\u7642\u6848\u4ef6\uff0c\u8acb\u7a0d\u5f8c\u518d\u8a66",life:3e3})}}}),(0,I.jsx)(o.$,{label:"\u7de8\u8f2f",icon:"pi pi-pencil",onClick:()=>{k("edit")}}),(0,I.jsx)(o.$,{label:"\u522a\u9664",icon:"pi pi-trash",className:"p-button-danger",onClick:()=>{D&&(0,m.Z)({message:'\u78ba\u5b9a\u8981\u522a\u9664\u884c\u7a0b "'.concat(D.title,'" \u55ce\uff1f'),header:"\u78ba\u8a8d\u522a\u9664",icon:"pi pi-exclamation-triangle",accept:async()=>{try{var t;await w.A.delete("/api/schedule/".concat(D.id)),await E(),T(!1),P(),null===(t=e.current)||void 0===t||t.show({severity:"success",summary:"\u6210\u529f",detail:"\u884c\u7a0b\u5df2\u522a\u9664"})}catch(d){var a,l,s;console.error("\u522a\u9664\u5931\u6557:",d);var i=403===d.status?"\u60a8\u7121\u6b0a\u9650\uff0c\u8acb\u901a\u77e5\u7ba1\u7406\u54e1":(null===(a=d.response)||void 0===a||null===(l=a.data)||void 0===l?void 0:l.message)||"\u522a\u9664\u5931\u6557";null===(s=e.current)||void 0===s||s.show({severity:"error",summary:"\u932f\u8aa4",detail:i})}}})}}),(0,I.jsx)(o.$,{label:"\u95dc\u9589",icon:"pi pi-times",className:"p-button-outlined",onClick:()=>{T(!1),P()}})]}):(0,I.jsxs)(I.Fragment,{children:[(0,I.jsx)(o.$,{label:"\u53d6\u6d88",icon:"pi pi-times",className:"p-button-outlined",onClick:()=>{T(!1),P()}}),(0,I.jsx)(o.$,{label:"edit"===C?"\u4fdd\u5b58":"\u65b0\u589e",icon:"pi pi-check",onClick:"edit"===C?()=>{(0,m.Z)({message:"\u78ba\u5b9a\u8981\u4fdd\u5b58\u4fee\u6539\u55ce\uff1f",header:"\u78ba\u8a8d\u7de8\u8f2f",icon:"pi pi-question-circle",accept:()=>{O()}})}:O})]})}),children:[(0,I.jsxs)("div",{className:"grid",children:[(0,I.jsx)("div",{className:"col-6 md:col-6",children:(0,I.jsxs)("div",{className:"field",children:[(0,I.jsx)("label",{htmlFor:"doctor",className:"font-bold block mb-2",children:"\u6cbb\u7642\u5e2b *"}),(0,I.jsx)(p.m,{id:"doctor",value:M.doctorId,options:S,onChange:e=>G("doctorId",e.value),optionLabel:"Name",optionValue:"Id",placeholder:"\u8acb\u9078\u64c7\u6cbb\u7642\u5e2b",className:"w-full",disabled:"view"===C})]})}),(0,I.jsx)("div",{className:"col-6 md:col-6",children:(0,I.jsxs)("div",{className:"field",children:[(0,I.jsx)("label",{htmlFor:"patient",className:"font-bold block mb-2",children:"\u75c5\u60a3 *"}),(0,I.jsx)(p.m,{id:"patient",value:M.patientId,options:R,onChange:e=>G("patientId",e.value),optionLabel:"Name",optionValue:"Id",placeholder:"\u8acb\u9078\u64c7\u75c5\u60a3",className:"w-full",disabled:"view"===C})]})}),(0,I.jsx)("div",{className:"col-12 md:col-6",children:(0,I.jsxs)("div",{className:"field",children:[(0,I.jsx)("label",{htmlFor:"startDate",className:"font-bold block mb-2",children:"\u958b\u59cb\u65e5\u671f *"}),(0,I.jsx)(c.V,{id:"startDate",value:M.startDate,onChange:e=>G("startDate",e.value),dateFormat:"yy-mm-dd",showIcon:!0,className:"w-full",disabled:"view"===C})]})}),(0,I.jsx)("div",{className:"col-12 md:col-6",children:(0,I.jsxs)("div",{className:"field",children:[(0,I.jsx)("label",{htmlFor:"endDate",className:"font-bold block mb-2",children:"\u7d50\u675f\u65e5\u671f *"}),(0,I.jsx)(c.V,{id:"endDate",value:M.endDate,onChange:e=>G("endDate",e.value),dateFormat:"yy-mm-dd",showIcon:!0,className:"w-full",disabled:"view"===C})]})}),(0,I.jsx)("div",{className:"col-6 md:col-6",children:(0,I.jsxs)("div",{className:"field",children:[(0,I.jsx)("label",{htmlFor:"startTime",className:"font-bold block mb-2",children:"\u958b\u59cb\u6642\u9593"}),(0,I.jsx)(h.S,{id:"startTime",type:"time",value:M.startTime,onChange:e=>G("startTime",e.target.value),className:"w-full",disabled:"view"===C})]})}),(0,I.jsx)("div",{className:"col-6 md:col-6",children:(0,I.jsxs)("div",{className:"field",children:[(0,I.jsx)("label",{htmlFor:"endTime",className:"font-bold block mb-2",children:"\u7d50\u675f\u6642\u9593"}),(0,I.jsx)(h.S,{id:"endTime",type:"time",value:M.endTime,onChange:e=>G("endTime",e.target.value),className:"w-full",disabled:"view"===C})]})})]}),"view"!==C&&(0,I.jsxs)("div",{className:"grid",children:[(0,I.jsx)("div",{className:"col-12",children:(0,I.jsxs)("div",{className:"field-checkbox",children:[(0,I.jsx)(b.S,{inputId:"isRepeat",checked:M.isRepeat,onChange:e=>G("isRepeat",e.checked||!1)}),(0,I.jsx)("label",{htmlFor:"isRepeat",className:"ml-2 font-bold",children:"\u91cd\u8907\u884c\u7a0b"})]})}),M.isRepeat&&(0,I.jsxs)(I.Fragment,{children:[(0,I.jsx)("div",{className:"col-6 md:col-6",children:(0,I.jsxs)("div",{className:"field",children:[(0,I.jsx)("label",{htmlFor:"repeatType",className:"font-bold block mb-2",children:"\u91cd\u8907\u985e\u578b"}),(0,I.jsx)(p.m,{id:"repeatType",value:M.repeatType,options:[{label:"\u6bcf\u65e5",value:"daily"},{label:"\u6bcf\u9031",value:"weekly"},{label:"\u6bcf\u6708",value:"monthly"}],onChange:e=>G("repeatType",e.value),className:"w-full",style:{width:"90%"}})]})}),(0,I.jsx)("div",{className:"col-6 md:col-6",children:(0,I.jsxs)("div",{className:"field",children:[(0,I.jsx)("label",{htmlFor:"repeatCount",className:"font-bold block mb-2",children:"\u91cd\u8907\u6b21\u6578"}),(0,I.jsx)(y.Y,{id:"repeatCount",value:M.repeatCount,onValueChange:e=>G("repeatCount",e.value||1),min:1,max:10,inputStyle:{width:"90%"}})]})}),(0,I.jsx)("div",{className:"col-12",children:(0,I.jsx)("div",{className:"p-1 bg-blue-50 border-round",children:(0,I.jsxs)("p",{className:"text-sm text-blue-800 m-0",children:[(0,I.jsx)("i",{className:"pi pi-info-circle mr-2"}),"daily"===M.repeatType&&"\u5c07\u6bcf\u65e5\u91cd\u8907 ".concat(M.repeatCount," \u6b21"),"weekly"===M.repeatType&&"\u5c07\u6bcf\u9031\u91cd\u8907 ".concat(M.repeatCount," \u6b21"),"monthly"===M.repeatType&&"\u5c07\u6bcf\u6708\u91cd\u8907 ".concat(M.repeatCount," \u6b21")]})})})]})]})]})]})}}}]);
//# sourceMappingURL=810.aefb54bc.chunk.js.map
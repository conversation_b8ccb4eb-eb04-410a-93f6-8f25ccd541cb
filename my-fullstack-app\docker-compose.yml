version: '3.9'

services:
  nginx:
    image: nginx:stable
    container_name: nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/conf.d:/etc/nginx/conf.d
      - ./nginx/ssl:/etc/letsencrypt
      - ./nginx/ssl-data:/var/lib/letsencrypt
      - ./nginx/logs:/var/log/nginx
      - ./uploads:/app/uploads
    depends_on:
      - frontend
      - backend
    networks:
      - app-network

  certbot:
    image: certbot/certbot
    container_name: certbot
    volumes:
      - ./nginx/ssl:/etc/letsencrypt
      - ./nginx/ssl-data:/var/lib/letsencrypt
    entrypoint: "/bin/sh -c 'trap exit TERM; while :; do sleep 1; done'"
    networks:
      - app-network
    depends_on:
      - nginx

  db:
    container_name: my-mysql
    image: mysql:8.0
    restart: always
    command: >
      --default-time-zone='+08:00'
      --server-id=1
      --log-bin=mysql-bin
      --binlog-format=ROW
      --binlog_expire_logs_seconds=604800
      --secure-file-priv=''
    environment:
      MYSQL_ROOT_PASSWORD: ${DB_ROOT_PASSWORD}
      MYSQL_DATABASE: ${DB_NAME}
      MYSQL_USER: ${DB_USER}
      MYSQL_PASSWORD: ${DB_PASSWORD}
      TZ: Asia/Taipei
    volumes:
      - mysql-data:/var/lib/mysql
    ports:
      - "3306:3306"
    networks:
      - app-network
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost", "-u${DB_USER}", "-p${DB_PASSWORD}"] # 使用 DB_USER 和 DB_PASSWORD
      interval: 5s
      timeout: 20s
      retries: 10
      start_period: 30s # 等待 MySQL 初始化完成的時間

  redis:
    image: redis:7.2-alpine
    restart: always
    ports:
      - "6379:6379"
    volumes:
      - redis-data:/data
    command: ["redis-server", "--appendonly", "yes"]
    networks:
      - app-network
    healthcheck: 
      test: ["CMD-SHELL", "redis-cli ping | grep PONG"] 
      interval: 5s
      timeout: 5s
      retries: 5
      start_period: 10s # 等待 Redis 初始化完成的時間
      
  frontend:
    build: ./frontend
    ports:
      - "8080:80"
    depends_on:
      - backend
    networks:
      - app-network
    image: a253844/frontend:latest

  backend:
    build: ./backend
    ports:
      - "5001:5000"  # ASP.NET API 對外開放 5001，容器內仍聽 5000
    depends_on:
      db:
        condition: service_healthy
      redis:
        condition: service_healthy
    volumes:
      - ./wwwroot:/app/wwwroot
      - ./uploads:/app/uploads
      - /home/<USER>/mysql-backup:/app/backups
    environment:
      - TZ=Asia/Taipei
      - ConnectionStrings__DefaultConnection=Server=${DB_HOST};port=${DB_PORT};Database=${DB_NAME};User=${DB_USER};Password=${DB_PASSWORD};
      - ConnectionStrings__Redis=${REDIS_HOST}:${REDIS_PORT}
      - ASPNETCORE_URLS=http://+:5000
    networks:
      - app-network
    image: a253844/backend:latest

networks:
  app-network:
    driver: bridge

volumes:
  mysql-data:
  redis-data:
{"ast": null, "code": "import { isSameWeek } from \"../../../isSameWeek.js\";\nconst weekdays = [\"svētdienā\", \"pirmdienā\", \"otrdien<PERSON>\", \"trešdien<PERSON>\", \"ceturtdienā\", \"piektdienā\", \"sestdienā\"];\nconst formatRelativeLocale = {\n  lastWeek: (date, baseDate, options) => {\n    if (isSameWeek(date, baseDate, options)) {\n      return \"eeee 'plkst.' p\";\n    }\n    const weekday = weekdays[date.getDay()];\n    return \"'Pagājušā \" + weekday + \" plkst.' p\";\n  },\n  yesterday: \"'<PERSON><PERSON>r plkst.' p\",\n  today: \"'Š<PERSON><PERSON> plkst.' p\",\n  tomorrow: \"'Rīt plkst.' p\",\n  nextWeek: (date, baseDate, options) => {\n    if (isSameWeek(date, baseDate, options)) {\n      return \"eeee 'plkst.' p\";\n    }\n    const weekday = weekdays[date.getDay()];\n    return \"'Nāka<PERSON>jā \" + weekday + \" plkst.' p\";\n  },\n  other: \"P\"\n};\nexport const formatRelative = (token, date, baseDate, options) => {\n  const format = formatRelativeLocale[token];\n  if (typeof format === \"function\") {\n    return format(date, baseDate, options);\n  }\n  return format;\n};", "map": {"version": 3, "names": ["isSameWeek", "weekdays", "formatRelativeLocale", "lastWeek", "date", "baseDate", "options", "weekday", "getDay", "yesterday", "today", "tomorrow", "nextWeek", "other", "formatRelative", "token", "format"], "sources": ["C:/Users/<USER>/Desktop/demo-git/demo-react/my-fullstack-app/frontend/node_modules/date-fns/locale/lv/_lib/formatRelative.js"], "sourcesContent": ["import { isSameWeek } from \"../../../isSameWeek.js\";\n\nconst weekdays = [\n  \"svētdienā\",\n  \"pirmdienā\",\n  \"otrdien<PERSON>\",\n  \"trešdien<PERSON>\",\n  \"ceturtdienā\",\n  \"piektdienā\",\n  \"sestdienā\",\n];\n\nconst formatRelativeLocale = {\n  lastWeek: (date, baseDate, options) => {\n    if (isSameWeek(date, baseDate, options)) {\n      return \"eeee 'plkst.' p\";\n    }\n\n    const weekday = weekdays[date.getDay()];\n    return \"'Pagā<PERSON><PERSON><PERSON> \" + weekday + \" plkst.' p\";\n  },\n  yesterday: \"'<PERSON><PERSON><PERSON> plkst.' p\",\n  today: \"'Š<PERSON>ien plkst.' p\",\n  tomorrow: \"'Rīt plkst.' p\",\n  nextWeek: (date, baseDate, options) => {\n    if (isSameWeek(date, baseDate, options)) {\n      return \"eeee 'plkst.' p\";\n    }\n\n    const weekday = weekdays[date.getDay()];\n    return \"'Nāka<PERSON>jā \" + weekday + \" plkst.' p\";\n  },\n  other: \"P\",\n};\n\nexport const formatRelative = (token, date, baseDate, options) => {\n  const format = formatRelativeLocale[token];\n\n  if (typeof format === \"function\") {\n    return format(date, baseDate, options);\n  }\n\n  return format;\n};\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,wBAAwB;AAEnD,MAAMC,QAAQ,GAAG,CACf,WAAW,EACX,WAAW,EACX,UAAU,EACV,WAAW,EACX,aAAa,EACb,YAAY,EACZ,WAAW,CACZ;AAED,MAAMC,oBAAoB,GAAG;EAC3BC,QAAQ,EAAEA,CAACC,IAAI,EAAEC,QAAQ,EAAEC,OAAO,KAAK;IACrC,IAAIN,UAAU,CAACI,IAAI,EAAEC,QAAQ,EAAEC,OAAO,CAAC,EAAE;MACvC,OAAO,iBAAiB;IAC1B;IAEA,MAAMC,OAAO,GAAGN,QAAQ,CAACG,IAAI,CAACI,MAAM,CAAC,CAAC,CAAC;IACvC,OAAO,YAAY,GAAGD,OAAO,GAAG,YAAY;EAC9C,CAAC;EACDE,SAAS,EAAE,kBAAkB;EAC7BC,KAAK,EAAE,mBAAmB;EAC1BC,QAAQ,EAAE,gBAAgB;EAC1BC,QAAQ,EAAEA,CAACR,IAAI,EAAEC,QAAQ,EAAEC,OAAO,KAAK;IACrC,IAAIN,UAAU,CAACI,IAAI,EAAEC,QAAQ,EAAEC,OAAO,CAAC,EAAE;MACvC,OAAO,iBAAiB;IAC1B;IAEA,MAAMC,OAAO,GAAGN,QAAQ,CAACG,IAAI,CAACI,MAAM,CAAC,CAAC,CAAC;IACvC,OAAO,YAAY,GAAGD,OAAO,GAAG,YAAY;EAC9C,CAAC;EACDM,KAAK,EAAE;AACT,CAAC;AAED,OAAO,MAAMC,cAAc,GAAGA,CAACC,KAAK,EAAEX,IAAI,EAAEC,QAAQ,EAAEC,OAAO,KAAK;EAChE,MAAMU,MAAM,GAAGd,oBAAoB,CAACa,KAAK,CAAC;EAE1C,IAAI,OAAOC,MAAM,KAAK,UAAU,EAAE;IAChC,OAAOA,MAAM,CAACZ,IAAI,EAAEC,QAAQ,EAAEC,OAAO,CAAC;EACxC;EAEA,OAAOU,MAAM;AACf,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
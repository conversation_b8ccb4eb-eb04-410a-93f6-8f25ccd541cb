import { useState, useEffect } from 'react';
import api from '../services/api';

interface PermissionResponse {
  hasPermission: boolean;
  userRole: string;
  pageName: string;
  requiredRoles: string[];
}

interface UserInfo {
  id: number;
  username: string;
  role: string;
  isEnabled: boolean;
}

/**
 * 權限檢查 Hook
 * @param pageName 頁面名稱
 * @returns 權限檢查結果
 */
export const usePagePermission = (pageName: string) => {
  const [hasPermission, setHasPermission] = useState<boolean>(false);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [permissionData, setPermissionData] = useState<PermissionResponse | null>(null);

  useEffect(() => {
    const checkPermission = async () => {
      try {
        setLoading(true);
        setError(null);

        const response = await api.get('/api/auth/check-page-permission', {
          params: { pageName }
        });

        const data: PermissionResponse = response.data;
        setPermissionData(data);
        setHasPermission(data.hasPermission);
      } catch (err: any) {
        console.error('權限檢查失敗:', err);
        setError(err.response?.data?.error || '權限檢查失敗');
        setHasPermission(false);
      } finally {
        setLoading(false);
      }
    };

    if (pageName) {
      checkPermission();
    }
  }, [pageName]);

  return {
    hasPermission,
    loading,
    error,
    permissionData
  };
};

/**
 * 用戶資訊 Hook
 * @returns 當前用戶資訊
 */
export const useUserInfo = () => {
  const [userInfo, setUserInfo] = useState<UserInfo | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchUserInfo = async () => {
      try {
        setLoading(true);
        setError(null);

        const response = await api.get('/api/auth/user-info');
        setUserInfo(response.data);
      } catch (err: any) {
        console.error('獲取用戶資訊失敗:', err);
        setError(err.response?.data?.error || '獲取用戶資訊失敗');
        setUserInfo(null);
      } finally {
        setLoading(false);
      }
    };

    fetchUserInfo();
  }, []);

  return {
    userInfo,
    loading,
    error
  };
};

/**
 * 角色檢查函數
 * @param userRole 用戶角色
 * @param requiredRoles 需要的角色列表
 * @returns 是否有權限
 */
export const hasRole = (userRole: string, requiredRoles: string[]): boolean => {
  return requiredRoles.some(role => userRole.includes(role));
};

/**
 * 權限檢查組件 Props
 */
export interface PermissionGuardProps {
  children: React.ReactNode;
  pageName?: string;
  requiredRoles?: string[];
  fallback?: React.ReactNode;
  showLoading?: boolean;
}

/**
 * 權限保護組件
 */
export const PermissionGuard: React.FC<PermissionGuardProps> = ({
  children,
  pageName,
  requiredRoles,
  fallback,
  showLoading = true
}) => {
  const { userInfo, loading: userLoading } = useUserInfo();
  const { hasPermission, loading: permissionLoading } = usePagePermission(pageName || '');

  const loading = userLoading || (pageName ? permissionLoading : false);

  // 顯示載入中
  if (loading && showLoading) {
    return (
      <div className="flex justify-content-center align-items-center" style={{ height: '200px' }}>
        <i className="pi pi-spinner pi-spin" style={{ fontSize: '2rem' }}></i>
        <span className="ml-2">檢查權限中...</span>
      </div>
    );
  }

  // 如果指定了頁面名稱，使用頁面權限檢查
  if (pageName) {
    if (!hasPermission) {
      return (
        <div>
          {fallback || (
            <div className="text-center p-4">
              <i className="pi pi-ban text-6xl text-red-500 mb-3"></i>
              <h3 className="text-xl font-semibold mb-2">訪問被拒絕</h3>
              <p className="text-gray-600">您沒有權限訪問此頁面</p>
              <p className="text-sm text-gray-500 mt-2">
                當前角色: {userInfo?.role || '未知'}
              </p>
            </div>
          )}
        </div>
      );
    }
  }

  // 如果指定了角色要求，使用角色檢查
  if (requiredRoles && userInfo) {
    if (!hasRole(userInfo.role, requiredRoles)) {
      return (
        <div>
          {fallback || (
            <div className="text-center p-4">
              <i className="pi pi-ban text-6xl text-red-500 mb-3"></i>
              <h3 className="text-xl font-semibold mb-2">權限不足</h3>
              <p className="text-gray-600">您的角色無法執行此操作</p>
              <p className="text-sm text-gray-500 mt-2">
                當前角色: {userInfo.role} | 需要角色: {requiredRoles.join(', ')}
              </p>
            </div>
          )}
        </div>
      );
    }
  }

  return <>{children}</>;
};

export default {
  usePagePermission,
  useUserInfo,
  hasRole,
  PermissionGuard
};

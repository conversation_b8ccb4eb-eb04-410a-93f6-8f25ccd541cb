{"ast": null, "code": "const formatRelativeLocale = {\n  lastWeek: \"先週のeeeeのp\",\n  yesterday: \"昨日のp\",\n  today: \"今日のp\",\n  tomorrow: \"明日のp\",\n  nextWeek: \"翌週のeeeeのp\",\n  other: \"P\"\n};\nexport const formatRelative = (token, _date, _baseDate, _options) => {\n  return formatRelativeLocale[token];\n};", "map": {"version": 3, "names": ["formatRelativeLocale", "lastWeek", "yesterday", "today", "tomorrow", "nextWeek", "other", "formatRelative", "token", "_date", "_baseDate", "_options"], "sources": ["C:/Users/<USER>/Desktop/demo-git/demo-react/my-fullstack-app/frontend/node_modules/date-fns/locale/ja/_lib/formatRelative.js"], "sourcesContent": ["const formatRelativeLocale = {\n  lastWeek: \"先週のeeeeのp\",\n  yesterday: \"昨日のp\",\n  today: \"今日のp\",\n  tomorrow: \"明日のp\",\n  nextWeek: \"翌週のeeeeのp\",\n  other: \"P\",\n};\n\nexport const formatRelative = (token, _date, _baseDate, _options) => {\n  return formatRelativeLocale[token];\n};\n"], "mappings": "AAAA,MAAMA,oBAAoB,GAAG;EAC3BC,QAAQ,EAAE,WAAW;EACrBC,SAAS,EAAE,MAAM;EACjBC,KAAK,EAAE,MAAM;EACbC,QAAQ,EAAE,MAAM;EAChBC,QAAQ,EAAE,WAAW;EACrBC,KAAK,EAAE;AACT,CAAC;AAED,OAAO,MAAMC,cAAc,GAAGA,CAACC,KAAK,EAAEC,KAAK,EAAEC,SAAS,EAAEC,QAAQ,KAAK;EACnE,OAAOX,oBAAoB,CAACQ,KAAK,CAAC;AACpC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
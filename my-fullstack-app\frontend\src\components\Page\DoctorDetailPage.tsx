import { Button } from 'primereact/button';
import { Calendar } from 'primereact/calendar';
import { Dropdown } from 'primereact/dropdown';
import { InputText } from 'primereact/inputtext';
import { Toast } from 'primereact/toast';
import React, { useEffect, useRef, useState } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import api from '../../services/api';

interface Doctor {
  userId?: number;
  userName: string;
  userAccount: string;
  userEmail: string;
  userPhone: string;
  address: string;
  birthDate: Date | null;
  gender: string;
  isEnabled: boolean;
  roleId: number;
  roleName: string;
}

const DoctorDetailPage: React.FC = () => {
  const location = useLocation();
  const navigate = useNavigate();
  const toast = useRef<Toast>(null);

  const doctor = location.state?.doctor;
  const isEdit = location.state?.isEdit || false;
  const isCreate = !doctor;

  const [formData, setFormData] = useState<Doctor>({
    userName: '',
    userAccount: '',
    userEmail: '',
    userPhone: '',
    address: '',
    birthDate: null,
    gender: '',
    isEnabled: true,
    roleId: 3, // 預設為治療師角色
    roleName: 'user'
  });

  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (doctor) {
      setFormData({
        userId: doctor.userId,
        userName: doctor.userName || '',
        userAccount: doctor.userAccount || '',
        userEmail: doctor.userEmail || '',
        userPhone: doctor.userPhone || '',
        address: doctor.address || '',
        birthDate: doctor.birthDate ? new Date(doctor.birthDate) : null,
        gender: doctor.gender || '',
        isEnabled: doctor.isEnabled ?? true,
        roleId: doctor.roleId || 2,
        roleName: doctor.roleName || '治療師'
      });
    }
  }, [doctor]);

  const handleInputChange = (field: keyof Doctor, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handleSubmit = async () => {
    // 驗證必填欄位
    if (!formData.userName || !formData.userAccount) {
      toast.current?.show({
        severity: 'error',
        summary: '驗證失敗',
        detail: '請填寫姓名和帳號'
      });
      return;
    }

    setLoading(true);
    try {
      let response;
      if (isCreate) {
        // 新增治療師
        response = await api.post('/api/Users/<USER>', {
          name: formData.userName,
          username: formData.userAccount,
          email: formData.userEmail,
          phone: formData.userPhone,
          address: formData.address,
          birthDate: formData.birthDate ? formData.birthDate.toISOString() : null,
          gender: formData.gender,
          isEnabled: formData.isEnabled
        });
      } else {
        // 更新治療師
        response = await api.put('/api/Users/<USER>', {
          id: formData.userId,
          name: formData.userName,
          username: formData.userAccount,
          email: formData.userEmail,
          phone: formData.userPhone,
          address: formData.address,
          birthDate: formData.birthDate ? formData.birthDate.toISOString() : null,
          gender: formData.gender,
          isEnabled: formData.isEnabled
        });
      }

      toast.current?.show({
        severity: 'success',
        summary: isCreate ? '新增成功' : '更新成功',
        detail: response.data.message || `治療師資料已${isCreate ? '新增' : '更新'}成功`
      });

      // 延遲返回列表頁面
      setTimeout(() => {
        navigate('/doctors');
      }, 1500);

    } catch (error: any) {
      toast.current?.show({
        severity: 'error',
        summary: isCreate ? '新增失敗' : '更新失敗',
        detail: error.response?.data || `治療師資料${isCreate ? '新增' : '更新'}失敗，請稍後再試`
      });
    } finally {
      setLoading(false);
    }
  };

    

  return (
    <div className="doctor-detail-page">
      <Toast ref={toast} />

      <div className="card">

        <div className="grid formgrid p-fluid">
          <div className="col-6 md:col-2">
            <div className="field">
              <label htmlFor="userName" className="font-bold">
                姓名 <span className="text-red-500">*</span>
              </label>
              <InputText
                id="userName"
                value={formData.userName}
                onChange={(e) => handleInputChange('userName', e.target.value)}
                disabled={!isEdit && !isCreate}
                placeholder="請輸入姓名"
              />
            </div>
          </div>

          <div className="col-6 md:col-2">
            <div className="field">
              <label htmlFor="userAccount" className="font-bold">
                帳號 <span className="text-red-500">*</span>
              </label>
              <InputText
                id="userAccount"
                value={formData.userAccount}
                onChange={(e) => handleInputChange('userAccount', e.target.value)}
                disabled={!isEdit && !isCreate}
                placeholder="請輸入帳號"
              />
            </div>
          </div>

          <div className="col-6 md:col-2">
            <div className="field">
              <label htmlFor="userEmail" className="font-bold">
                Email
              </label>
              <InputText
                id="userEmail"
                value={formData.userEmail}
                onChange={(e) => handleInputChange('userEmail', e.target.value)}
                disabled={!isEdit && !isCreate}
                placeholder="請輸入 Email"
                type="email"
              />
            </div>
          </div>

          <div className="col-6 md:col-2">
            <div className="field">
              <label htmlFor="userPhone" className="font-bold">
                電話
              </label>
              <InputText
                id="userPhone"
                value={formData.userPhone}
                onChange={(e) => handleInputChange('userPhone', e.target.value)}
                disabled={!isEdit && !isCreate}
                placeholder="請輸入電話號碼"
              />
            </div>
          </div>

                    
          <div className="col-6 md:col-2">
            <div className="field">
              <label htmlFor="gender" className="font-bold">
                性別
              </label>
              <Dropdown
                id="gender"
                value={formData.gender}
                onChange={(e) => handleInputChange('gender', e.value)}
                disabled={!isEdit && !isCreate}
                options={[
                  { label: '男', value: '男' },
                  { label: '女', value: '女' }
                ]}
                placeholder="請選擇性別"
              />
            </div>
          </div>

          <div className="col-6 md:col-2">
            <div className="field">
              <label htmlFor="birthDate" className="font-bold">
                生日
              </label>
              <Calendar
                id="birthDate"
                value={formData.birthDate}
                onChange={(e) => handleInputChange('birthDate', e.value)}
                disabled={!isEdit && !isCreate}
                dateFormat="yy-mm-dd"
                showIcon
                placeholder="請選擇生日"
              />
            </div>
          </div>

          <div className="col-12 md:col-8">
            <div className="field">
              <label htmlFor="address" className="font-bold">
                地址
              </label>
              <InputText
                id="address"
                value={formData.address}
                onChange={(e) => handleInputChange('address', e.target.value)}
                disabled={!isEdit && !isCreate}
                placeholder="請輸入地址"
              />
            </div>
          </div>
          
          <div className="col-0 md:col-4">

          </div>

          <div className="col-6 md:col-2" hidden>
            <div className="field">
              <label htmlFor="roleName" className="font-bold">
                角色
              </label>
              <InputText
                id="roleName"
                value={formData.roleName}
                disabled
              />
            </div>
          </div>

          <div className="col-6 md:col-2">
            <div className="field">
              <label htmlFor="isEnabled" className="font-bold">
                啟用狀態
              </label>
              <Dropdown
                id="isEnabled"
                value={formData.isEnabled}
                onChange={(e) => handleInputChange('isEnabled', e.value)}
                disabled={!isEdit && !isCreate}
                options={[
                  { label: '啟用', value: true },
                  { label: '停用', value: false }
                ]}
                placeholder="請選擇狀態"
              />
            </div>
          </div>
        </div>

        <div className="flex gap-2 justify-content-end mt-4">
          {(isEdit || isCreate) && (
            <Button
              label={isCreate ? '新增' : '更新'}
              icon={isCreate ? 'pi pi-plus' : 'pi pi-check'}
              onClick={handleSubmit}
              loading={loading}
            />
          )}
        </div>
      </div>
    </div>
  );
};

export default DoctorDetailPage;
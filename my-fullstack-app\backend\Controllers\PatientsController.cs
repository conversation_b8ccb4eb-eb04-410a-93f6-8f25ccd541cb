﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http.HttpResults;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using MyApi.Data;
using MyApi.Models;
using MyApi.Services;
using Newtonsoft.Json;
using System;
using System.Linq;
using System.Threading.Tasks;
using System.Xml.Linq;
using static MyApi.Helpers.Enums;
using static System.Runtime.InteropServices.JavaScript.JSType;

namespace MyApi.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class PatientsController : ControllerBase
    {
        private readonly AppDbContext _context;
        private readonly RedisService _redisService;

        public PatientsController(AppDbContext context, RedisService redisService)
        {
            _context = context;
            _redisService = redisService;
        }

        [Authorize(Roles = "Admin,Manager,User")]
        [HttpGet]
        public IActionResult Get([FromQuery] int Id)
        {
            var Patients = _context.Patients
                .Where(p => p.Id == Id && p.IsDelete == false)
                .FirstOrDefault();

            if (Patients == null)
            {
                return BadRequest("未找到病患資料");
            }

            return Ok(Patients);
        }

        [Authorize(Roles = "Admin,Manager,User")]
        [HttpGet("GetList")]
        public IActionResult GetList([FromQuery] string name, DateTime? starttime, DateTime? endtime)
        {
            var Patients = (
                   from patient in _context.Patients
                   where patient.IsDelete == false
                   join user in _context.Users       
                   on patient.OperatorUserId equals user.Id 
                   orderby patient.CreatedAt descending
                   select new  
                   {
                       id = patient.Id,
                       fullName = patient.FullName,
                       gender = patient.Gender,
                       birthDate = patient.BirthDate,
                       createdAt = patient.CreatedAt,
                       updatedAt = patient.UpdatedAt,
                       operatorUserName = user.Name
                   }
                   
               ).ToList();

            if (!string.IsNullOrEmpty(name))
            {
                Patients = Patients.Where(p => p.fullName.Contains(name)).ToList();
            }

            if (starttime!= null && endtime != null)
            {
                Patients = Patients.Where(p => p.createdAt >= starttime && p.createdAt <= endtime).ToList();
            }

            return Ok(Patients);
        }

        [Authorize(Roles = "Admin,Manager,User")]
        [HttpPost("Insert")]
        public async Task<IActionResult> Insert([FromBody] Patient data)
        {
            try
            {
                var userId = User.FindFirst("UserId");

                var Patients = _context.Patients
                .Where(p => p.NationalId == data.NationalId && p.IsDelete == false)
                .ToList();

                if (Patients.Count > 0)
                {
                    return BadRequest("已有病患資料");
                }

                data.BirthDate = data.BirthDate == null ? null : DateTime.Parse(data.BirthDate.ToString()).ToLocalTime();

                data.OperatorUserId = int.Parse(userId.Value);
                data.UpdatedAt = DateTime.Now;

                _context.Patients.Add(data);

                await _context.SaveChangesAsync();

                await _redisService.DeleteKeyAsync("PatientList");

                return Ok("病患資料已新增");
            }catch(Exception ex)
            {
                return StatusCode(500, $"新增失敗: {ex.Message}");
            }
            
        }

        [Authorize(Roles = "Admin,Manager,User")]
        [HttpPut("Update")]
        public async Task<IActionResult> Update([FromBody] Patient data)
        {
            var userId = User.FindFirst("UserId");

            var Patients = _context.Patients
                .Where(p => p.Id == data.Id && p.IsDelete == false)
                .ToList();

            if (Patients.Count == 0)
            {
                return BadRequest("未找到病患資料");
            }

            Patients.First().FullName = data.FullName;
            Patients.First().Gender = data.Gender;
            Patients.First().Phone = data.Phone;
            Patients.First().Address = data.Address;
            Patients.First().Email = data.Email;
            Patients.First().BirthDate = data.BirthDate == null ? null : DateTime.Parse(data.BirthDate.ToString()).ToLocalTime();
            Patients.First().EmergencyContact = data.EmergencyContact;
            Patients.First().EmergencyRelationship = data.EmergencyRelationship;
            Patients.First().EmergencyPhone = data.EmergencyPhone;
            Patients.First().NationalId = data.NationalId;
            Patients.First().MedicalHistory = data.MedicalHistory;
            Patients.First().ExerciseHabit = data.ExerciseHabit;
            Patients.First().ExerciseFrequency = data.ExerciseFrequency;
            Patients.First().InjuryHistory = data.InjuryHistory;
            Patients.First().UpdatedAt = DateTime.Now;
            Patients.First().OperatorUserId = int.Parse(userId.Value);

            await _context.SaveChangesAsync();

            await _redisService.DeleteKeyAsync("PatientList");

            return Ok("病患資料已更新");
        }

        [Authorize(Roles = "Admin,Manager")]
        [HttpGet("Delete")]
        public async Task<IActionResult> Delete([FromQuery] string id)
        {
            var userId = User.FindFirst("UserId");

            var Patients = _context.Patients
                .Where(p => p.Id.ToString() == id && p.IsDelete == false)
                .ToList();

            if (Patients.Count == 0)
            {
                return BadRequest("未找到病患資料");
            }

            Patients.First().IsDelete = true;
            Patients.First().OperatorUserId = int.Parse(userId.Value);
            Patients.First().UpdatedAt = DateTime.Now;

            await _context.SaveChangesAsync();

            await _redisService.DeleteKeyAsync("PatientList");

            return Ok("病患資料已刪除");
        }

        [Authorize(Roles = "Admin,Manager,User")]
        [HttpGet("PatientList")]
        public async Task<IActionResult> PatientList()
        {

            var redisdata = await _redisService.GetStringAsync("PatientList");

            if (redisdata == null)
            {
                var patients = _context.Patients
                    .Where(t => t.IsDelete == false)
                    .OrderByDescending(p => p.CreatedAt)
                    .ToList();

                var result = patients.Select(t => new
                {
                    Id = t.Id,
                    Name = t.FullName
                }).ToList();

                var redisvalue = JsonConvert.SerializeObject(result);

                await _redisService.SetStringAsync("PatientList", redisvalue);

                redisdata = redisvalue;
            }

            return Ok(redisdata);
        }

    }
}

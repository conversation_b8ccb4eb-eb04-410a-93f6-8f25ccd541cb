using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using static MyApi.Helpers.Enums;

namespace MyApi.Models
{
    public class TreatmentDto
    {
        public int Id { get; set; }
        public string OrderNo { get; set; } = string.Empty;
        public TreatmentStep Step { get; set; }
        public string DiscomfortPeriod { get; set; } = string.Empty;
        public string PossibleCauses { get; set; } = string.Empty;
        public string TreatmentHistory { get; set; } = string.Empty;
        public string HowToKnowOur { get; set; } = string.Empty;
        public string HospitalFormUrl { get; set; } = string.Empty;
        public string TreatmentConsentFormUrl { get; set; } = string.Empty;
        public DateTime? HospitalFormRecordDate { get; set; }
        public string Subjective { get; set; } = string.Empty;
        public string Objective { get; set; } = string.Empty;
        public string Assessment { get; set; } = string.Empty;
        public string Plan { get; set; } = string.Empty;
        public string ReceiptUrl { get; set; } = string.Empty;
        public int PatientId { get; set; }
        
        public List<TreatmentDiscomfortAreaDto> DiscomfortAreas { get; set; } = new();
    }
}

"use strict";(self.webpackChunkfrontend=self.webpackChunkfrontend||[]).push([[469],{1104:(e,t,n)=>{n.d(t,{T:()=>w,Z:()=>D});var i=n(5043),r=n(4052),s=n(2018),o=n(1828),l=n(5797),a=n(2028),c=n(9988),h=n(8794),u=n(4504);function d(){return d=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var i in n)({}).hasOwnProperty.call(n,i)&&(e[i]=n[i])}return e},d.apply(null,arguments)}function g(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,i=Array(t);n<t;n++)i[n]=e[n];return i}function p(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var i,r,s,o,l=[],a=!0,c=!1;try{if(s=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;a=!1}else for(;!(a=(i=s.call(n)).done)&&(l.push(i.value),l.length!==t);a=!0);}catch(e){c=!0,r=e}finally{try{if(!a&&null!=n.return&&(o=n.return(),Object(o)!==o))return}finally{if(c)throw r}}return l}}(e,t)||function(e,t){if(e){if("string"==typeof e)return g(e,t);var n={}.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?g(e,t):void 0}}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function v(e){return v="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},v(e)}function m(e){var t=function(e,t){if("object"!=v(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var i=n.call(e,t||"default");if("object"!=v(i))return i;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==v(t)?t:t+""}function f(e,t,n){return(t=m(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var E={root:"p-confirm-dialog",message:"p-confirm-dialog-message",icon:"p-confirm-dialog-icon",acceptButton:"p-confirm-dialog-accept",rejectButton:function(e){var t=e.getPropValue;return(0,u.xW)("p-confirm-dialog-reject",{"p-button-text":!t("rejectClassName")})}},b=o.x.extend({defaultProps:{__TYPE:"ConfirmDialog",accept:null,acceptClassName:null,acceptIcon:null,acceptLabel:null,appendTo:null,breakpoints:null,children:void 0,className:null,content:null,defaultFocus:"accept",footer:null,icon:null,message:null,onHide:null,reject:null,rejectClassName:null,rejectIcon:null,rejectLabel:null,tagKey:void 0,visible:void 0},css:{classes:E}});function S(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);t&&(i=i.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,i)}return n}function y(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?S(Object(n),!0).forEach((function(t){f(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):S(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var D=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};(e=y(y({},e),{visible:void 0===e.visible||e.visible})).visible&&c.s.emit("confirm-dialog",e);return{show:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};c.s.emit("confirm-dialog",y(y(y({},e),t),{visible:!0}))},hide:function(){c.s.emit("confirm-dialog",{visible:!1})}}},w=i.memo(i.forwardRef((function(e,t){var n=(0,a.qV)(),g=i.useContext(r.UM),v=b.getProps(e,g),m=p(i.useState(v.visible),2),f=m[0],E=m[1],S=p(i.useState(!1),2),D=S[0],w=S[1],T=i.useRef(null),j=i.useRef(!1),M=i.useRef(null),C=function(){var e=v.group;return T.current&&(e=T.current.group),Object.assign({},v,T.current,{group:e})},P=function(e){return C()[e]},R=function(e){for(var t=arguments.length,n=new Array(t>1?t-1:0),i=1;i<t;i++)n[i-1]=arguments[i];return u.BF.getPropValue(P(e),n)},x=P("acceptLabel")||(0,r.WP)("accept"),I=P("rejectLabel")||(0,r.WP)("reject"),O={props:v,state:{visible:f}},L=b.setMetaData(O),N=L.ptm,A=L.cx,H=L.isUnstyled;(0,o.j)(b.css.styles,H,{name:"confirmdialog"});var V=function(){j.current||(j.current=!0,R("accept"),_("accept"))},X=function(){j.current||(j.current=!0,R("reject"),_("reject"))},Y=function(){C().group===v.group&&(E(!0),j.current=!1,M.current=document.activeElement)},_=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"cancel";f&&("string"!==typeof e&&(e="cancel"),E(!1),R("onHide",e),u.DV.focus(M.current),M.current=null)},k=function(e){if(e.tagKey===v.tagKey){var t=f!==e.visible;P("target")!==e.target&&!v.target?(_(),T.current=e,w(!0)):t&&(T.current=e,e.visible?Y():_())}};i.useEffect((function(){v.visible?Y():_()}),[v.visible]),i.useEffect((function(){return v.target||v.message||c.s.on("confirm-dialog",k),function(){c.s.off("confirm-dialog",k)}}),[v.target]),(0,a.w5)((function(){D&&Y()}),[D]),(0,a.l0)((function(){c.s.off("confirm-dialog",k)})),i.useImperativeHandle(t,(function(){return{props:v,confirm:k}}));var U=function(){var t=C(),r=u.BF.getJSXElement(P("message"),t),o=n({className:A("icon")},N("icon")),a=u.Hj.getJSXIcon(P("icon"),y({},o),{props:t}),c=function(){var e=P("defaultFocus"),t=(0,u.xW)("p-confirm-dialog-accept",P("acceptClassName")),r=(0,u.xW)("p-confirm-dialog-reject",{"p-button-text":!P("rejectClassName")},P("rejectClassName")),o=n({label:I,autoFocus:"reject"===e,icon:P("rejectIcon"),className:(0,u.xW)(P("rejectClassName"),A("rejectButton",{getPropValue:P})),onClick:X,pt:N("rejectButton"),unstyled:v.unstyled,__parentMetadata:{parent:O}},N("rejectButton")),l=n({label:x,autoFocus:void 0===e||"accept"===e,icon:P("acceptIcon"),className:(0,u.xW)(P("acceptClassName"),A("acceptButton")),onClick:V,pt:N("acceptButton"),unstyled:v.unstyled,__parentMetadata:{parent:O}},N("acceptButton")),a=i.createElement(i.Fragment,null,i.createElement(s.$,o),i.createElement(s.$,l));if(P("footer")){var c={accept:V,reject:X,acceptClassName:t,rejectClassName:r,acceptLabel:x,rejectLabel:I,element:a,props:C()};return u.BF.getJSXElement(P("footer"),c)}return a}(),h=n({className:A("message")},N("message")),g=n({visible:f,className:(0,u.xW)(P("className"),A("root")),footer:c,onHide:_,breakpoints:P("breakpoints"),pt:t.pt,unstyled:v.unstyled,appendTo:P("appendTo"),__parentMetadata:{parent:O}},b.getOtherProps(t));return i.createElement(l.l,d({},g,{content:null===e||void 0===e?void 0:e.content}),a,i.createElement("span",h,r))}();return i.createElement(h.Z,{element:U,appendTo:P("appendTo")})})));w.displayName="ConfirmDialog"},4972:(e,t,n)=>{n.d(t,{S:()=>S});var i=n(5043),r=n(4052),s=n(1828),o=n(2028),l=n(2897),a=n(1356),c=n(4504);function h(){return h=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var i in n)({}).hasOwnProperty.call(n,i)&&(e[i]=n[i])}return e},h.apply(null,arguments)}function u(e){return u="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},u(e)}function d(e){var t=function(e,t){if("object"!=u(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var i=n.call(e,t||"default");if("object"!=u(i))return i;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==u(t)?t:t+""}function g(e,t,n){return(t=d(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function p(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,i=Array(t);n<t;n++)i[n]=e[n];return i}function v(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var i,r,s,o,l=[],a=!0,c=!1;try{if(s=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;a=!1}else for(;!(a=(i=s.call(n)).done)&&(l.push(i.value),l.length!==t);a=!0);}catch(e){c=!0,r=e}finally{try{if(!a&&null!=n.return&&(o=n.return(),Object(o)!==o))return}finally{if(c)throw r}}return l}}(e,t)||function(e,t){if(e){if("string"==typeof e)return p(e,t);var n={}.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?p(e,t):void 0}}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}var m={box:"p-checkbox-box",input:"p-checkbox-input",icon:"p-checkbox-icon",root:function(e){var t=e.props,n=e.checked,i=e.context;return(0,c.xW)("p-checkbox p-component",{"p-highlight":n,"p-disabled":t.disabled,"p-invalid":t.invalid,"p-variant-filled":t.variant?"filled"===t.variant:i&&"filled"===i.inputStyle})}},f=s.x.extend({defaultProps:{__TYPE:"Checkbox",autoFocus:!1,checked:!1,className:null,disabled:!1,falseValue:!1,icon:null,id:null,inputId:null,inputRef:null,invalid:!1,variant:null,name:null,onChange:null,onContextMenu:null,onMouseDown:null,readOnly:!1,required:!1,style:null,tabIndex:null,tooltip:null,tooltipOptions:null,trueValue:!0,value:null,children:void 0},css:{classes:m}});function E(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);t&&(i=i.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,i)}return n}function b(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?E(Object(n),!0).forEach((function(t){g(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):E(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var S=i.memo(i.forwardRef((function(e,t){var n=(0,o.qV)(),u=i.useContext(r.UM),d=f.getProps(e,u),g=v(i.useState(!1),2),p=g[0],m=g[1],E=f.setMetaData({props:d,state:{focused:p},context:{checked:d.checked===d.trueValue,disabled:d.disabled}}),S=E.ptm,y=E.cx,D=E.isUnstyled;(0,s.j)(f.css.styles,D,{name:"checkbox"});var w=i.useRef(null),T=i.useRef(d.inputRef),j=function(){return d.checked===d.trueValue};i.useImperativeHandle(t,(function(){return{props:d,focus:function(){return c.DV.focus(T.current)},getElement:function(){return w.current},getInput:function(){return T.current}}})),i.useEffect((function(){c.BF.combinedRefs(T,d.inputRef)}),[T,d.inputRef]),(0,o.w5)((function(){T.current.checked=j()}),[d.checked,d.trueValue]),(0,o.uU)((function(){d.autoFocus&&c.DV.focus(T.current,d.autoFocus)}));var M=j(),C=c.BF.isNotEmpty(d.tooltip),P=f.getOtherProps(d),R=n({id:d.id,className:(0,c.xW)(d.className,y("root",{checked:M,context:u})),style:d.style,"data-p-highlight":M,"data-p-disabled":d.disabled,onContextMenu:d.onContextMenu,onMouseDown:d.onMouseDown},P,S("root"));return i.createElement(i.Fragment,null,i.createElement("div",h({ref:w},R),function(){var e=c.BF.reduceKeys(P,c.DV.ARIA_PROPS),t=n(b({id:d.inputId,type:"checkbox",className:y("input"),name:d.name,tabIndex:d.tabIndex,onFocus:function(e){return function(e){var t;m(!0),null===d||void 0===d||null===(t=d.onFocus)||void 0===t||t.call(d,e)}(e)},onBlur:function(e){return function(e){var t;m(!1),null===d||void 0===d||null===(t=d.onBlur)||void 0===t||t.call(d,e)}(e)},onChange:function(e){return function(e){if(!d.disabled&&!d.readOnly&&d.onChange){var t,n=j()?d.falseValue:d.trueValue,i={originalEvent:e,value:d.value,checked:n,stopPropagation:function(){null===e||void 0===e||e.stopPropagation()},preventDefault:function(){null===e||void 0===e||e.preventDefault()},target:{type:"checkbox",name:d.name,id:d.id,value:d.value,checked:n}};if(null===d||void 0===d||null===(t=d.onChange)||void 0===t||t.call(d,i),e.defaultPrevented)return;c.DV.focus(T.current)}}(e)},disabled:d.disabled,readOnly:d.readOnly,required:d.required,"aria-invalid":d.invalid,checked:M},e),S("input"));return i.createElement("input",h({ref:T},t))}(),function(){var e=n({className:y("icon")},S("icon")),t=n({className:y("box",{checked:M}),"data-p-highlight":M,"data-p-disabled":d.disabled},S("box")),r=M?d.icon||i.createElement(l.S,e):null,s=c.Hj.getJSXIcon(r,b({},e),{props:d,checked:M});return i.createElement("div",t,s)}()),C&&i.createElement(a.m,h({target:w,content:d.tooltip,pt:S("tooltip")},d.tooltipOptions)))})));S.displayName="Checkbox"},7807:(e,t,n)=>{n.d(t,{Ay:()=>P});var i=n(1922),r=n(6499);r.bG.touchMouseIgnoreWait=500;let s=0,o=0,l=!1;class a{constructor(e){this.subjectEl=null,this.selector="",this.handleSelector="",this.shouldIgnoreMove=!1,this.shouldWatchScroll=!0,this.isDragging=!1,this.isTouchDragging=!1,this.wasTouchScroll=!1,this.handleMouseDown=e=>{if(!this.shouldIgnoreMouse()&&function(e){return 0===e.button&&!e.ctrlKey}(e)&&this.tryStart(e)){let t=this.createEventFromMouse(e,!0);this.emitter.trigger("pointerdown",t),this.initScrollWatch(t),this.shouldIgnoreMove||document.addEventListener("mousemove",this.handleMouseMove),document.addEventListener("mouseup",this.handleMouseUp)}},this.handleMouseMove=e=>{let t=this.createEventFromMouse(e);this.recordCoords(t),this.emitter.trigger("pointermove",t)},this.handleMouseUp=e=>{document.removeEventListener("mousemove",this.handleMouseMove),document.removeEventListener("mouseup",this.handleMouseUp),this.emitter.trigger("pointerup",this.createEventFromMouse(e)),this.cleanup()},this.handleTouchStart=e=>{if(this.tryStart(e)){this.isTouchDragging=!0;let t=this.createEventFromTouch(e,!0);this.emitter.trigger("pointerdown",t),this.initScrollWatch(t);let n=e.target;this.shouldIgnoreMove||n.addEventListener("touchmove",this.handleTouchMove),n.addEventListener("touchend",this.handleTouchEnd),n.addEventListener("touchcancel",this.handleTouchEnd),window.addEventListener("scroll",this.handleTouchScroll,!0)}},this.handleTouchMove=e=>{let t=this.createEventFromTouch(e);this.recordCoords(t),this.emitter.trigger("pointermove",t)},this.handleTouchEnd=e=>{if(this.isDragging){let t=e.target;t.removeEventListener("touchmove",this.handleTouchMove),t.removeEventListener("touchend",this.handleTouchEnd),t.removeEventListener("touchcancel",this.handleTouchEnd),window.removeEventListener("scroll",this.handleTouchScroll,!0),this.emitter.trigger("pointerup",this.createEventFromTouch(e)),this.cleanup(),this.isTouchDragging=!1,s+=1,setTimeout((()=>{s-=1}),r.bG.touchMouseIgnoreWait)}},this.handleTouchScroll=()=>{this.wasTouchScroll=!0},this.handleScroll=e=>{if(!this.shouldIgnoreMove){let t=window.scrollX-this.prevScrollX+this.prevPageX,n=window.scrollY-this.prevScrollY+this.prevPageY;this.emitter.trigger("pointermove",{origEvent:e,isTouch:this.isTouchDragging,subjectEl:this.subjectEl,pageX:t,pageY:n,deltaX:t-this.origPageX,deltaY:n-this.origPageY})}},this.containerEl=e,this.emitter=new r.F,e.addEventListener("mousedown",this.handleMouseDown),e.addEventListener("touchstart",this.handleTouchStart,{passive:!0}),o+=1,1===o&&window.addEventListener("touchmove",c,{passive:!1})}destroy(){this.containerEl.removeEventListener("mousedown",this.handleMouseDown),this.containerEl.removeEventListener("touchstart",this.handleTouchStart,{passive:!0}),o-=1,o||window.removeEventListener("touchmove",c,{passive:!1})}tryStart(e){let t=this.querySubjectEl(e),n=e.target;return!(!t||this.handleSelector&&!(0,r.Z)(n,this.handleSelector))&&(this.subjectEl=t,this.isDragging=!0,this.wasTouchScroll=!1,!0)}cleanup(){l=!1,this.isDragging=!1,this.subjectEl=null,this.destroyScrollWatch()}querySubjectEl(e){return this.selector?(0,r.Z)(e.target,this.selector):this.containerEl}shouldIgnoreMouse(){return s||this.isTouchDragging}cancelTouchScroll(){this.isDragging&&(l=!0)}initScrollWatch(e){this.shouldWatchScroll&&(this.recordCoords(e),window.addEventListener("scroll",this.handleScroll,!0))}recordCoords(e){this.shouldWatchScroll&&(this.prevPageX=e.pageX,this.prevPageY=e.pageY,this.prevScrollX=window.scrollX,this.prevScrollY=window.scrollY)}destroyScrollWatch(){this.shouldWatchScroll&&window.removeEventListener("scroll",this.handleScroll,!0)}createEventFromMouse(e,t){let n=0,i=0;return t?(this.origPageX=e.pageX,this.origPageY=e.pageY):(n=e.pageX-this.origPageX,i=e.pageY-this.origPageY),{origEvent:e,isTouch:!1,subjectEl:this.subjectEl,pageX:e.pageX,pageY:e.pageY,deltaX:n,deltaY:i}}createEventFromTouch(e,t){let n,i,r=e.touches,s=0,o=0;return r&&r.length?(n=r[0].pageX,i=r[0].pageY):(n=e.pageX,i=e.pageY),t?(this.origPageX=n,this.origPageY=i):(s=n-this.origPageX,o=i-this.origPageY),{origEvent:e,isTouch:!0,subjectEl:this.subjectEl,pageX:n,pageY:i,deltaX:s,deltaY:o}}}function c(e){l&&e.preventDefault()}class h{constructor(){this.isVisible=!1,this.sourceEl=null,this.mirrorEl=null,this.sourceElRect=null,this.parentNode=document.body,this.zIndex=9999,this.revertDuration=0}start(e,t,n){this.sourceEl=e,this.sourceElRect=this.sourceEl.getBoundingClientRect(),this.origScreenX=t-window.scrollX,this.origScreenY=n-window.scrollY,this.deltaX=0,this.deltaY=0,this.updateElPosition()}handleMove(e,t){this.deltaX=e-window.scrollX-this.origScreenX,this.deltaY=t-window.scrollY-this.origScreenY,this.updateElPosition()}setIsVisible(e){e?this.isVisible||(this.mirrorEl&&(this.mirrorEl.style.display=""),this.isVisible=e,this.updateElPosition()):this.isVisible&&(this.mirrorEl&&(this.mirrorEl.style.display="none"),this.isVisible=e)}stop(e,t){let n=()=>{this.cleanup(),t()};e&&this.mirrorEl&&this.isVisible&&this.revertDuration&&(this.deltaX||this.deltaY)?this.doRevertAnimation(n,this.revertDuration):setTimeout(n,0)}doRevertAnimation(e,t){let n=this.mirrorEl,i=this.sourceEl.getBoundingClientRect();n.style.transition="top "+t+"ms,left "+t+"ms",(0,r.aN)(n,{left:i.left,top:i.top}),(0,r.b0)(n,(()=>{n.style.transition="",e()}))}cleanup(){this.mirrorEl&&((0,r.aM)(this.mirrorEl),this.mirrorEl=null),this.sourceEl=null}updateElPosition(){this.sourceEl&&this.isVisible&&(0,r.aN)(this.getMirrorEl(),{left:this.sourceElRect.left+this.deltaX,top:this.sourceElRect.top+this.deltaY})}getMirrorEl(){let e=this.sourceElRect,t=this.mirrorEl;return t||(t=this.mirrorEl=this.sourceEl.cloneNode(!0),t.style.userSelect="none",t.style.webkitUserSelect="none",t.style.pointerEvents="none",t.classList.add("fc-event-dragging"),(0,r.aN)(t,{position:"fixed",zIndex:this.zIndex,visibility:"",boxSizing:"border-box",width:e.right-e.left,height:e.bottom-e.top,right:"auto",bottom:"auto",margin:0}),this.parentNode.appendChild(t)),t}}class u extends r.b9{constructor(e,t){super(),this.handleScroll=()=>{this.scrollTop=this.scrollController.getScrollTop(),this.scrollLeft=this.scrollController.getScrollLeft(),this.handleScrollChange()},this.scrollController=e,this.doesListening=t,this.scrollTop=this.origScrollTop=e.getScrollTop(),this.scrollLeft=this.origScrollLeft=e.getScrollLeft(),this.scrollWidth=e.getScrollWidth(),this.scrollHeight=e.getScrollHeight(),this.clientWidth=e.getClientWidth(),this.clientHeight=e.getClientHeight(),this.clientRect=this.computeClientRect(),this.doesListening&&this.getEventTarget().addEventListener("scroll",this.handleScroll)}destroy(){this.doesListening&&this.getEventTarget().removeEventListener("scroll",this.handleScroll)}getScrollTop(){return this.scrollTop}getScrollLeft(){return this.scrollLeft}setScrollTop(e){this.scrollController.setScrollTop(e),this.doesListening||(this.scrollTop=Math.max(Math.min(e,this.getMaxScrollTop()),0),this.handleScrollChange())}setScrollLeft(e){this.scrollController.setScrollLeft(e),this.doesListening||(this.scrollLeft=Math.max(Math.min(e,this.getMaxScrollLeft()),0),this.handleScrollChange())}getClientWidth(){return this.clientWidth}getClientHeight(){return this.clientHeight}getScrollWidth(){return this.scrollWidth}getScrollHeight(){return this.scrollHeight}handleScrollChange(){}}class d extends u{constructor(e,t){super(new r.ba(e),t)}getEventTarget(){return this.scrollController.el}computeClientRect(){return(0,r.b1)(this.scrollController.el)}}class g extends u{constructor(e){super(new r.bb,e)}getEventTarget(){return window}computeClientRect(){return{left:this.scrollLeft,right:this.scrollLeft+this.clientWidth,top:this.scrollTop,bottom:this.scrollTop+this.clientHeight}}handleScrollChange(){this.clientRect=this.computeClientRect()}}const p="function"===typeof performance?performance.now:Date.now;class v{constructor(){this.isEnabled=!0,this.scrollQuery=[window,".fc-scroller"],this.edgeThreshold=50,this.maxVelocity=300,this.pointerScreenX=null,this.pointerScreenY=null,this.isAnimating=!1,this.scrollCaches=null,this.everMovedUp=!1,this.everMovedDown=!1,this.everMovedLeft=!1,this.everMovedRight=!1,this.animate=()=>{if(this.isAnimating){let e=this.computeBestEdge(this.pointerScreenX+window.scrollX,this.pointerScreenY+window.scrollY);if(e){let t=p();this.handleSide(e,(t-this.msSinceRequest)/1e3),this.requestAnimation(t)}else this.isAnimating=!1}}}start(e,t,n){this.isEnabled&&(this.scrollCaches=this.buildCaches(n),this.pointerScreenX=null,this.pointerScreenY=null,this.everMovedUp=!1,this.everMovedDown=!1,this.everMovedLeft=!1,this.everMovedRight=!1,this.handleMove(e,t))}handleMove(e,t){if(this.isEnabled){let n=e-window.scrollX,i=t-window.scrollY,r=null===this.pointerScreenY?0:i-this.pointerScreenY,s=null===this.pointerScreenX?0:n-this.pointerScreenX;r<0?this.everMovedUp=!0:r>0&&(this.everMovedDown=!0),s<0?this.everMovedLeft=!0:s>0&&(this.everMovedRight=!0),this.pointerScreenX=n,this.pointerScreenY=i,this.isAnimating||(this.isAnimating=!0,this.requestAnimation(p()))}}stop(){if(this.isEnabled){this.isAnimating=!1;for(let e of this.scrollCaches)e.destroy();this.scrollCaches=null}}requestAnimation(e){this.msSinceRequest=e,requestAnimationFrame(this.animate)}handleSide(e,t){let{scrollCache:n}=e,{edgeThreshold:i}=this,r=i-e.distance,s=r*r/(i*i)*this.maxVelocity*t,o=1;switch(e.name){case"left":o=-1;case"right":n.setScrollLeft(n.getScrollLeft()+s*o);break;case"top":o=-1;case"bottom":n.setScrollTop(n.getScrollTop()+s*o)}}computeBestEdge(e,t){let{edgeThreshold:n}=this,i=null,r=this.scrollCaches||[];for(let s of r){let r=s.clientRect,o=e-r.left,l=r.right-e,a=t-r.top,c=r.bottom-t;o>=0&&l>=0&&a>=0&&c>=0&&(a<=n&&this.everMovedUp&&s.canScrollUp()&&(!i||i.distance>a)&&(i={scrollCache:s,name:"top",distance:a}),c<=n&&this.everMovedDown&&s.canScrollDown()&&(!i||i.distance>c)&&(i={scrollCache:s,name:"bottom",distance:c}),o<=n&&this.everMovedLeft&&s.canScrollLeft()&&(!i||i.distance>o)&&(i={scrollCache:s,name:"left",distance:o}),l<=n&&this.everMovedRight&&s.canScrollRight()&&(!i||i.distance>l)&&(i={scrollCache:s,name:"right",distance:l}))}return i}buildCaches(e){return this.queryScrollEls(e).map((e=>e===window?new g(!1):new d(e,!1)))}queryScrollEls(e){let t=[];for(let n of this.scrollQuery)"object"===typeof n?t.push(n):t.push(...Array.prototype.slice.call(e.getRootNode().querySelectorAll(n)));return t}}class m extends r.bF{constructor(e,t){super(e),this.containerEl=e,this.delay=null,this.minDistance=0,this.touchScrollAllowed=!0,this.mirrorNeedsRevert=!1,this.isInteracting=!1,this.isDragging=!1,this.isDelayEnded=!1,this.isDistanceSurpassed=!1,this.delayTimeoutId=null,this.onPointerDown=e=>{this.isDragging||(this.isInteracting=!0,this.isDelayEnded=!1,this.isDistanceSurpassed=!1,(0,r.ap)(document.body),(0,r.ar)(document.body),e.isTouch||e.origEvent.preventDefault(),this.emitter.trigger("pointerdown",e),this.isInteracting&&!this.pointer.shouldIgnoreMove&&(this.mirror.setIsVisible(!1),this.mirror.start(e.subjectEl,e.pageX,e.pageY),this.startDelay(e),this.minDistance||this.handleDistanceSurpassed(e)))},this.onPointerMove=e=>{if(this.isInteracting){if(this.emitter.trigger("pointermove",e),!this.isDistanceSurpassed){let t,n=this.minDistance,{deltaX:i,deltaY:r}=e;t=i*i+r*r,t>=n*n&&this.handleDistanceSurpassed(e)}this.isDragging&&("scroll"!==e.origEvent.type&&(this.mirror.handleMove(e.pageX,e.pageY),this.autoScroller.handleMove(e.pageX,e.pageY)),this.emitter.trigger("dragmove",e))}},this.onPointerUp=e=>{this.isInteracting&&(this.isInteracting=!1,(0,r.aq)(document.body),(0,r.as)(document.body),this.emitter.trigger("pointerup",e),this.isDragging&&(this.autoScroller.stop(),this.tryStopDrag(e)),this.delayTimeoutId&&(clearTimeout(this.delayTimeoutId),this.delayTimeoutId=null))};let n=this.pointer=new a(e);n.emitter.on("pointerdown",this.onPointerDown),n.emitter.on("pointermove",this.onPointerMove),n.emitter.on("pointerup",this.onPointerUp),t&&(n.selector=t),this.mirror=new h,this.autoScroller=new v}destroy(){this.pointer.destroy(),this.onPointerUp({})}startDelay(e){"number"===typeof this.delay?this.delayTimeoutId=setTimeout((()=>{this.delayTimeoutId=null,this.handleDelayEnd(e)}),this.delay):this.handleDelayEnd(e)}handleDelayEnd(e){this.isDelayEnded=!0,this.tryStartDrag(e)}handleDistanceSurpassed(e){this.isDistanceSurpassed=!0,this.tryStartDrag(e)}tryStartDrag(e){this.isDelayEnded&&this.isDistanceSurpassed&&(this.pointer.wasTouchScroll&&!this.touchScrollAllowed||(this.isDragging=!0,this.mirrorNeedsRevert=!1,this.autoScroller.start(e.pageX,e.pageY,this.containerEl),this.emitter.trigger("dragstart",e),!1===this.touchScrollAllowed&&this.pointer.cancelTouchScroll()))}tryStopDrag(e){this.mirror.stop(this.mirrorNeedsRevert,this.stopDrag.bind(this,e))}stopDrag(e){this.isDragging=!1,this.emitter.trigger("dragend",e)}setIgnoreMove(e){this.pointer.shouldIgnoreMove=e}setMirrorIsVisible(e){this.mirror.setIsVisible(e)}setMirrorNeedsRevert(e){this.mirrorNeedsRevert=e}setAutoScrollEnabled(e){this.autoScroller.isEnabled=e}}class f{constructor(e){this.el=e,this.origRect=(0,r.b4)(e),this.scrollCaches=(0,r.b3)(e).map((e=>new d(e,!0)))}destroy(){for(let e of this.scrollCaches)e.destroy()}computeLeft(){let e=this.origRect.left;for(let t of this.scrollCaches)e+=t.origScrollLeft-t.getScrollLeft();return e}computeTop(){let e=this.origRect.top;for(let t of this.scrollCaches)e+=t.origScrollTop-t.getScrollTop();return e}isWithinClipping(e,t){let n={left:e,top:t};for(let i of this.scrollCaches)if(!E(i.getEventTarget())&&!(0,r.aD)(n,i.clientRect))return!1;return!0}}function E(e){let t=e.tagName;return"HTML"===t||"BODY"===t}class b{constructor(e,t){this.useSubjectCenter=!1,this.requireInitial=!0,this.disablePointCheck=!1,this.initialHit=null,this.movingHit=null,this.finalHit=null,this.handlePointerDown=e=>{let{dragging:t}=this;this.initialHit=null,this.movingHit=null,this.finalHit=null,this.prepareHits(),this.processFirstCoord(e),this.initialHit||!this.requireInitial?(t.setIgnoreMove(!1),this.emitter.trigger("pointerdown",e)):t.setIgnoreMove(!0)},this.handleDragStart=e=>{this.emitter.trigger("dragstart",e),this.handleMove(e,!0)},this.handleDragMove=e=>{this.emitter.trigger("dragmove",e),this.handleMove(e)},this.handlePointerUp=e=>{this.releaseHits(),this.emitter.trigger("pointerup",e)},this.handleDragEnd=e=>{this.movingHit&&this.emitter.trigger("hitupdate",null,!0,e),this.finalHit=this.movingHit,this.movingHit=null,this.emitter.trigger("dragend",e)},this.droppableStore=t,e.emitter.on("pointerdown",this.handlePointerDown),e.emitter.on("dragstart",this.handleDragStart),e.emitter.on("dragmove",this.handleDragMove),e.emitter.on("pointerup",this.handlePointerUp),e.emitter.on("dragend",this.handleDragEnd),this.dragging=e,this.emitter=new r.F}processFirstCoord(e){let t,n={left:e.pageX,top:e.pageY},i=n,s=e.subjectEl;s instanceof HTMLElement&&(t=(0,r.b4)(s),i=(0,r.aE)(i,t));let o=this.initialHit=this.queryHitForOffset(i.left,i.top);if(o){if(this.useSubjectCenter&&t){let e=(0,r.aC)(t,o.rect);e&&(i=(0,r.aF)(e))}this.coordAdjust=(0,r.aG)(i,n)}else this.coordAdjust={left:0,top:0}}handleMove(e,t){let n=this.queryHitForOffset(e.pageX+this.coordAdjust.left,e.pageY+this.coordAdjust.top);!t&&S(this.movingHit,n)||(this.movingHit=n,this.emitter.trigger("hitupdate",n,!1,e))}prepareHits(){this.offsetTrackers=(0,r.a)(this.droppableStore,(e=>(e.component.prepareHits(),new f(e.el))))}releaseHits(){let{offsetTrackers:e}=this;for(let t in e)e[t].destroy();this.offsetTrackers={}}queryHitForOffset(e,t){let{droppableStore:n,offsetTrackers:i}=this,s=null;for(let o in n){let l=n[o].component,a=i[o];if(a&&a.isWithinClipping(e,t)){let n=a.computeLeft(),i=a.computeTop(),c=e-n,h=t-i,{origRect:u}=a,d=u.right-u.left,g=u.bottom-u.top;if(c>=0&&c<d&&h>=0&&h<g){let e=l.queryHit(c,h,d,g);e&&(0,r.b7)(e.dateProfile.activeRange,e.dateSpan.range)&&(this.disablePointCheck||a.el.contains(a.el.getRootNode().elementFromPoint(c+n-window.scrollX,h+i-window.scrollY)))&&(!s||e.layer>s.layer)&&(e.componentId=o,e.context=l.context,e.rect.left+=n,e.rect.right+=n,e.rect.top+=i,e.rect.bottom+=i,s=e)}}}return s}}function S(e,t){return!e&&!t||Boolean(e)===Boolean(t)&&(0,r.bd)(e.dateSpan,t.dateSpan)}function y(e,t){let n={};for(let s of t.pluginHooks.datePointTransforms)Object.assign(n,s(e,t));var i,r;return Object.assign(n,(i=e,{date:(r=t.dateEnv).toDate(i.range.start),dateStr:r.formatIso(i.range.start,{omitTime:i.allDay}),allDay:i.allDay})),n}class D extends r.X{constructor(e){super(e),this.handlePointerDown=e=>{let{dragging:t}=this,n=e.origEvent.target;t.setIgnoreMove(!this.component.isValidDateDownEl(n))},this.handleDragEnd=e=>{let{component:t}=this,{pointer:n}=this.dragging;if(!n.wasTouchScroll){let{initialHit:n,finalHit:i}=this.hitDragging;if(n&&i&&S(n,i)){let{context:i}=t,r=Object.assign(Object.assign({},y(n.dateSpan,i)),{dayEl:n.dayEl,jsEvent:e.origEvent,view:i.viewApi||i.calendarApi.view});i.emitter.trigger("dateClick",r)}}},this.dragging=new m(e.el),this.dragging.autoScroller.isEnabled=!1;let t=this.hitDragging=new b(this.dragging,(0,r.bE)(e));t.emitter.on("pointerdown",this.handlePointerDown),t.emitter.on("dragend",this.handleDragEnd)}destroy(){this.dragging.destroy()}}class w extends r.X{constructor(e){super(e),this.dragSelection=null,this.handlePointerDown=e=>{let{component:t,dragging:n}=this,{options:i}=t.context,r=i.selectable&&t.isValidDateDownEl(e.origEvent.target);n.setIgnoreMove(!r),n.delay=e.isTouch?function(e){let{options:t}=e.context,n=t.selectLongPressDelay;null==n&&(n=t.longPressDelay);return n}(t):null},this.handleDragStart=e=>{this.component.context.calendarApi.unselect(e)},this.handleHitUpdate=(e,t)=>{let{context:n}=this.component,i=null,s=!1;if(e){let t=this.hitDragging.initialHit;e.componentId===t.componentId&&this.isHitComboAllowed&&!this.isHitComboAllowed(t,e)||(i=function(e,t,n){let i=e.dateSpan,s=t.dateSpan,o=[i.range.start,i.range.end,s.range.start,s.range.end];o.sort(r.at);let l={};for(let r of n){let n=r(e,t);if(!1===n)return null;n&&Object.assign(l,n)}return l.range={start:o[0],end:o[3]},l.allDay=i.allDay,l}(t,e,n.pluginHooks.dateSelectionTransformers)),i&&(0,r.bY)(i,e.dateProfile,n)||(s=!0,i=null)}i?n.dispatch({type:"SELECT_DATES",selection:i}):t||n.dispatch({type:"UNSELECT_DATES"}),s?(0,r.av)():(0,r.au)(),t||(this.dragSelection=i)},this.handlePointerUp=e=>{this.dragSelection&&((0,r.cr)(this.dragSelection,e,this.component.context),this.dragSelection=null)};let{component:t}=e,{options:n}=t.context,i=this.dragging=new m(e.el);i.touchScrollAllowed=!1,i.minDistance=n.selectMinDistance||0,i.autoScroller.isEnabled=n.dragScroll;let s=this.hitDragging=new b(this.dragging,(0,r.bE)(e));s.emitter.on("pointerdown",this.handlePointerDown),s.emitter.on("dragstart",this.handleDragStart),s.emitter.on("hitupdate",this.handleHitUpdate),s.emitter.on("pointerup",this.handlePointerUp)}destroy(){this.dragging.destroy()}}class T extends r.X{constructor(e){super(e),this.subjectEl=null,this.subjectSeg=null,this.isDragging=!1,this.eventRange=null,this.relevantEvents=null,this.receivingContext=null,this.validMutation=null,this.mutatedRelevantEvents=null,this.handlePointerDown=e=>{let t=e.origEvent.target,{component:n,dragging:i}=this,{mirror:s}=i,{options:o}=n.context,l=n.context;this.subjectEl=e.subjectEl;let a=this.subjectSeg=(0,r.Y)(e.subjectEl),c=(this.eventRange=a.eventRange).instance.instanceId;this.relevantEvents=(0,r.aT)(l.getCurrentData().eventStore,c),i.minDistance=e.isTouch?0:o.eventDragMinDistance,i.delay=e.isTouch&&c!==n.props.eventSelection?function(e){let{options:t}=e.context,n=t.eventLongPressDelay;null==n&&(n=t.longPressDelay);return n}(n):null,o.fixedMirrorParent?s.parentNode=o.fixedMirrorParent:s.parentNode=(0,r.Z)(t,".fc"),s.revertDuration=o.dragRevertDuration;let h=n.isValidSegDownEl(t)&&!(0,r.Z)(t,".fc-event-resizer");i.setIgnoreMove(!h),this.isDragging=h&&e.subjectEl.classList.contains("fc-event-draggable")},this.handleDragStart=e=>{let t=this.component.context,n=this.eventRange,i=n.instance.instanceId;e.isTouch?i!==this.component.props.eventSelection&&t.dispatch({type:"SELECT_EVENT",eventInstanceId:i}):t.dispatch({type:"UNSELECT_EVENT"}),this.isDragging&&(t.calendarApi.unselect(e),t.emitter.trigger("eventDragStart",{el:this.subjectEl,event:new r._(t,n.def,n.instance),jsEvent:e.origEvent,view:t.viewApi}))},this.handleHitUpdate=(e,t)=>{if(!this.isDragging)return;let n=this.relevantEvents,i=this.hitDragging.initialHit,s=this.component.context,o=null,l=null,a=null,c=!1,h={affectedEvents:n,mutatedEvents:(0,r.H)(),isEvent:!0};if(e){o=e.context;let t=o.options;s===o||t.editable&&t.droppable?(l=function(e,t,n,i){let s=e.dateSpan,o=t.dateSpan,l=s.range.start,a=o.range.start,c={};s.allDay!==o.allDay&&(c.allDay=o.allDay,c.hasEnd=t.context.options.allDayMaintainDuration,l=o.allDay?(0,r.q)(n):n);let h=(0,r.ay)(l,a,e.context.dateEnv,e.componentId===t.componentId?e.largeUnit:null);h.milliseconds&&(c.allDay=!1);let u={datesDelta:h,standardProps:c};for(let r of i)r(u,e,t);return u}(i,e,this.eventRange.instance.range.start,o.getCurrentData().pluginHooks.eventDragMutationMassagers),l&&(a=(0,r.bV)(n,o.getCurrentData().eventUiBases,l,o),h.mutatedEvents=a,(0,r.bX)(h,e.dateProfile,o)||(c=!0,l=null,a=null,h.mutatedEvents=(0,r.H)()))):o=null}this.displayDrag(o,h),c?(0,r.av)():(0,r.au)(),t||(s===o&&S(i,e)&&(l=null),this.dragging.setMirrorNeedsRevert(!l),this.dragging.setMirrorIsVisible(!e||!this.subjectEl.getRootNode().querySelector(".fc-event-mirror")),this.receivingContext=o,this.validMutation=l,this.mutatedRelevantEvents=a)},this.handlePointerUp=()=>{this.isDragging||this.cleanup()},this.handleDragEnd=e=>{if(this.isDragging){let t=this.component.context,n=t.viewApi,{receivingContext:i,validMutation:s}=this,o=this.eventRange.def,l=this.eventRange.instance,a=new r._(t,o,l),c=this.relevantEvents,h=this.mutatedRelevantEvents,{finalHit:u}=this.hitDragging;if(this.clearDrag(),t.emitter.trigger("eventDragStop",{el:this.subjectEl,event:a,jsEvent:e.origEvent,view:n}),s){if(i===t){let i=new r._(t,h.defs[o.defId],l?h.instances[l.instanceId]:null);t.dispatch({type:"MERGE_EVENTS",eventStore:h});let u={oldEvent:a,event:i,relatedEvents:(0,r.w)(h,t,l),revert(){t.dispatch({type:"MERGE_EVENTS",eventStore:c})}},d={};for(let e of t.getCurrentData().pluginHooks.eventDropTransformers)Object.assign(d,e(s,t));t.emitter.trigger("eventDrop",Object.assign(Object.assign(Object.assign({},u),d),{el:e.subjectEl,delta:s.datesDelta,jsEvent:e.origEvent,view:n})),t.emitter.trigger("eventChange",u)}else if(i){let s={event:a,relatedEvents:(0,r.w)(c,t,l),revert(){t.dispatch({type:"MERGE_EVENTS",eventStore:c})}};t.emitter.trigger("eventLeave",Object.assign(Object.assign({},s),{draggedEl:e.subjectEl,view:n})),t.dispatch({type:"REMOVE_EVENTS",eventStore:c}),t.emitter.trigger("eventRemove",s);let d=h.defs[o.defId],g=h.instances[l.instanceId],p=new r._(i,d,g);i.dispatch({type:"MERGE_EVENTS",eventStore:h});let v={event:p,relatedEvents:(0,r.w)(h,i,g),revert(){i.dispatch({type:"REMOVE_EVENTS",eventStore:h})}};i.emitter.trigger("eventAdd",v),e.isTouch&&i.dispatch({type:"SELECT_EVENT",eventInstanceId:l.instanceId}),i.emitter.trigger("drop",Object.assign(Object.assign({},y(u.dateSpan,i)),{draggedEl:e.subjectEl,jsEvent:e.origEvent,view:u.context.viewApi})),i.emitter.trigger("eventReceive",Object.assign(Object.assign({},v),{draggedEl:e.subjectEl,view:u.context.viewApi}))}}else t.emitter.trigger("_noEventDrop")}this.cleanup()};let{component:t}=this,{options:n}=t.context,i=this.dragging=new m(e.el);i.pointer.selector=T.SELECTOR,i.touchScrollAllowed=!1,i.autoScroller.isEnabled=n.dragScroll;let s=this.hitDragging=new b(this.dragging,r.a5);s.useSubjectCenter=e.useEventCenter,s.emitter.on("pointerdown",this.handlePointerDown),s.emitter.on("dragstart",this.handleDragStart),s.emitter.on("hitupdate",this.handleHitUpdate),s.emitter.on("pointerup",this.handlePointerUp),s.emitter.on("dragend",this.handleDragEnd)}destroy(){this.dragging.destroy()}displayDrag(e,t){let n=this.component.context,i=this.receivingContext;i&&i!==e&&(i===n?i.dispatch({type:"SET_EVENT_DRAG",state:{affectedEvents:t.affectedEvents,mutatedEvents:(0,r.H)(),isEvent:!0}}):i.dispatch({type:"UNSET_EVENT_DRAG"})),e&&e.dispatch({type:"SET_EVENT_DRAG",state:t})}clearDrag(){let e=this.component.context,{receivingContext:t}=this;t&&t.dispatch({type:"UNSET_EVENT_DRAG"}),e!==t&&e.dispatch({type:"UNSET_EVENT_DRAG"})}cleanup(){this.subjectSeg=null,this.isDragging=!1,this.eventRange=null,this.relevantEvents=null,this.receivingContext=null,this.validMutation=null,this.mutatedRelevantEvents=null}}T.SELECTOR=".fc-event-draggable, .fc-event-resizable";class j extends r.X{constructor(e){super(e),this.draggingSegEl=null,this.draggingSeg=null,this.eventRange=null,this.relevantEvents=null,this.validMutation=null,this.mutatedRelevantEvents=null,this.handlePointerDown=e=>{let{component:t}=this,n=this.querySegEl(e),i=(0,r.Y)(n),s=this.eventRange=i.eventRange;this.dragging.minDistance=t.context.options.eventDragMinDistance,this.dragging.setIgnoreMove(!this.component.isValidSegDownEl(e.origEvent.target)||e.isTouch&&this.component.props.eventSelection!==s.instance.instanceId)},this.handleDragStart=e=>{let{context:t}=this.component,n=this.eventRange;this.relevantEvents=(0,r.aT)(t.getCurrentData().eventStore,this.eventRange.instance.instanceId);let i=this.querySegEl(e);this.draggingSegEl=i,this.draggingSeg=(0,r.Y)(i),t.calendarApi.unselect(),t.emitter.trigger("eventResizeStart",{el:i,event:new r._(t,n.def,n.instance),jsEvent:e.origEvent,view:t.viewApi})},this.handleHitUpdate=(e,t,n)=>{let{context:i}=this.component,s=this.relevantEvents,o=this.hitDragging.initialHit,l=this.eventRange.instance,a=null,c=null,h=!1,u={affectedEvents:s,mutatedEvents:(0,r.H)(),isEvent:!0};if(e){e.componentId===o.componentId&&this.isHitComboAllowed&&!this.isHitComboAllowed(o,e)||(a=function(e,t,n,i){let s=e.context.dateEnv,o=e.dateSpan.range.start,l=t.dateSpan.range.start,a=(0,r.ay)(o,l,s,e.largeUnit);if(n){if(s.add(i.start,a)<i.end)return{startDelta:a}}else if(s.add(i.end,a)>i.start)return{endDelta:a};return null}(o,e,n.subjectEl.classList.contains("fc-event-resizer-start"),l.range))}a&&(c=(0,r.bV)(s,i.getCurrentData().eventUiBases,a,i),u.mutatedEvents=c,(0,r.bX)(u,e.dateProfile,i)||(h=!0,a=null,c=null,u.mutatedEvents=null)),c?i.dispatch({type:"SET_EVENT_RESIZE",state:u}):i.dispatch({type:"UNSET_EVENT_RESIZE"}),h?(0,r.av)():(0,r.au)(),t||(a&&S(o,e)&&(a=null),this.validMutation=a,this.mutatedRelevantEvents=c)},this.handleDragEnd=e=>{let{context:t}=this.component,n=this.eventRange.def,i=this.eventRange.instance,s=new r._(t,n,i),o=this.relevantEvents,l=this.mutatedRelevantEvents;if(t.emitter.trigger("eventResizeStop",{el:this.draggingSegEl,event:s,jsEvent:e.origEvent,view:t.viewApi}),this.validMutation){let a=new r._(t,l.defs[n.defId],i?l.instances[i.instanceId]:null);t.dispatch({type:"MERGE_EVENTS",eventStore:l});let c={oldEvent:s,event:a,relatedEvents:(0,r.w)(l,t,i),revert(){t.dispatch({type:"MERGE_EVENTS",eventStore:o})}};t.emitter.trigger("eventResize",Object.assign(Object.assign({},c),{el:this.draggingSegEl,startDelta:this.validMutation.startDelta||(0,r.d)(0),endDelta:this.validMutation.endDelta||(0,r.d)(0),jsEvent:e.origEvent,view:t.viewApi})),t.emitter.trigger("eventChange",c)}else t.emitter.trigger("_noEventResize");this.draggingSeg=null,this.relevantEvents=null,this.validMutation=null};let{component:t}=e,n=this.dragging=new m(e.el);n.pointer.selector=".fc-event-resizer",n.touchScrollAllowed=!1,n.autoScroller.isEnabled=t.context.options.dragScroll;let i=this.hitDragging=new b(this.dragging,(0,r.bE)(e));i.emitter.on("pointerdown",this.handlePointerDown),i.emitter.on("dragstart",this.handleDragStart),i.emitter.on("hitupdate",this.handleHitUpdate),i.emitter.on("dragend",this.handleDragEnd)}destroy(){this.dragging.destroy()}querySegEl(e){return(0,r.Z)(e.subjectEl,".fc-event")}}const M={fixedMirrorParent:r.n},C={dateClick:r.n,eventDragStart:r.n,eventDragStop:r.n,eventDrop:r.n,eventResizeStart:r.n,eventResizeStop:r.n,eventResize:r.n,drop:r.n,eventReceive:r.n,eventLeave:r.n};r.bG.dataAttrPrefix="";r.bF;var P=(0,i.i1)({name:"@fullcalendar/interaction",componentInteractions:[D,w,T,j],calendarInteractions:[class{constructor(e){this.context=e,this.isRecentPointerDateSelect=!1,this.matchesCancel=!1,this.matchesEvent=!1,this.onSelect=e=>{e.jsEvent&&(this.isRecentPointerDateSelect=!0)},this.onDocumentPointerDown=e=>{let t=this.context.options.unselectCancel,n=(0,r.aP)(e.origEvent);this.matchesCancel=!!(0,r.Z)(n,t),this.matchesEvent=!!(0,r.Z)(n,T.SELECTOR)},this.onDocumentPointerUp=e=>{let{context:t}=this,{documentPointer:n}=this,i=t.getCurrentData();if(!n.wasTouchScroll){if(i.dateSelection&&!this.isRecentPointerDateSelect){let n=t.options.unselectAuto;!n||n&&this.matchesCancel||t.calendarApi.unselect(e)}i.eventSelection&&!this.matchesEvent&&t.dispatch({type:"UNSELECT_EVENT"})}this.isRecentPointerDateSelect=!1};let t=this.documentPointer=new a(document);t.shouldIgnoreMove=!0,t.shouldWatchScroll=!1,t.emitter.on("pointerdown",this.onDocumentPointerDown),t.emitter.on("pointerup",this.onDocumentPointerUp),e.emitter.on("select",this.onSelect)}destroy(){this.context.emitter.off("select",this.onSelect),this.documentPointer.destroy()}}],elementDraggingImpl:m,optionRefiners:M,listenerRefiners:C})}}]);
//# sourceMappingURL=469.161764eb.chunk.js.map
{"version": 3, "file": "static/js/122.08b241ad.chunk.js", "mappings": "sOAuBA,MA2RA,EA3RmCA,KAAO,IAADC,EAAAC,EACvC,MAAMC,GAAWC,EAAAA,EAAAA,MACXC,GAAWC,EAAAA,EAAAA,MACXC,GAAQC,EAAAA,EAAAA,QAAc,MAEtBC,EAAuB,QAAjBR,EAAGE,EAASO,aAAK,IAAAT,OAAA,EAAdA,EAAgBQ,OACzBE,GAAuB,QAAdT,EAAAC,EAASO,aAAK,IAAAR,OAAA,EAAdA,EAAgBS,UAAU,EACnCC,GAAYH,GAEXI,EAAUC,IAAeC,EAAAA,EAAAA,UAAiB,CAC/CC,SAAU,GACVC,YAAa,GACbC,UAAW,GACXC,UAAW,GACXC,QAAS,GACTC,UAAW,KACXC,OAAQ,GACRC,WAAW,EACXC,OAAQ,EACRC,SAAU,UAGLC,EAASC,IAAcZ,EAAAA,EAAAA,WAAS,IAEvCa,EAAAA,EAAAA,YAAU,KACK,IAADC,EAARpB,GACFK,EAAY,CACVgB,OAAQrB,EAAOqB,OACfd,SAAUP,EAAOO,UAAY,GAC7BC,YAAaR,EAAOQ,aAAe,GACnCC,UAAWT,EAAOS,WAAa,GAC/BC,UAAWV,EAAOU,WAAa,GAC/BC,QAASX,EAAOW,SAAW,GAC3BC,UAAWZ,EAAOY,UAAY,IAAIU,KAAKtB,EAAOY,WAAa,KAC3DC,OAAQb,EAAOa,QAAU,GACzBC,UAA2B,QAAlBM,EAAEpB,EAAOc,iBAAS,IAAAM,GAAAA,EAC3BL,OAAQf,EAAOe,QAAU,EACzBC,SAAUhB,EAAOgB,UAAY,yBAGhC,CAAChB,IAEJ,MAAMuB,EAAoBA,CAACC,EAAqBC,KAC9CpB,GAAYqB,IAAIC,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GAAUD,GAAI,IAAE,CAACF,GAAQC,OAoE3C,OACEG,EAAAA,EAAAA,MAAA,OAAKC,UAAU,qBAAoBC,SAAA,EACjCC,EAAAA,EAAAA,KAACC,EAAAA,EAAK,CAACC,IAAKnC,KAEZ8B,EAAAA,EAAAA,MAAA,OAAKC,UAAU,OAAMC,SAAA,EAEnBF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,wBAAuBC,SAAA,EACpCC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,iBAAgBC,UAC7BF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,QAAOC,SAAA,EACpBF,EAAAA,EAAAA,MAAA,SAAOM,QAAQ,WAAWL,UAAU,YAAWC,SAAA,CAAC,iBAC3CC,EAAAA,EAAAA,KAAA,QAAMF,UAAU,eAAcC,SAAC,UAEpCC,EAAAA,EAAAA,KAACI,EAAAA,EAAS,CACRC,GAAG,WACHX,MAAOrB,EAASG,SAChB8B,SAAWC,GAAMf,EAAkB,WAAYe,EAAEC,OAAOd,OACxDe,UAAWtC,IAAWC,EACtBsC,YAAY,yCAKlBV,EAAAA,EAAAA,KAAA,OAAKF,UAAU,iBAAgBC,UAC7BF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,QAAOC,SAAA,EACpBF,EAAAA,EAAAA,MAAA,SAAOM,QAAQ,cAAcL,UAAU,YAAWC,SAAA,CAAC,iBAC9CC,EAAAA,EAAAA,KAAA,QAAMF,UAAU,eAAcC,SAAC,UAEpCC,EAAAA,EAAAA,KAACI,EAAAA,EAAS,CACRC,GAAG,cACHX,MAAOrB,EAASI,YAChB6B,SAAWC,GAAMf,EAAkB,cAAee,EAAEC,OAAOd,OAC3De,UAAWtC,IAAWC,EACtBsC,YAAY,yCAKlBV,EAAAA,EAAAA,KAAA,OAAKF,UAAU,iBAAgBC,UAC7BF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,QAAOC,SAAA,EACpBC,EAAAA,EAAAA,KAAA,SAAOG,QAAQ,YAAYL,UAAU,YAAWC,SAAC,WAGjDC,EAAAA,EAAAA,KAACI,EAAAA,EAAS,CACRC,GAAG,YACHX,MAAOrB,EAASK,UAChB4B,SAAWC,GAAMf,EAAkB,YAAae,EAAEC,OAAOd,OACzDe,UAAWtC,IAAWC,EACtBsC,YAAY,2BACZC,KAAK,gBAKXX,EAAAA,EAAAA,KAAA,OAAKF,UAAU,iBAAgBC,UAC7BF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,QAAOC,SAAA,EACpBC,EAAAA,EAAAA,KAAA,SAAOG,QAAQ,YAAYL,UAAU,YAAWC,SAAC,kBAGjDC,EAAAA,EAAAA,KAACI,EAAAA,EAAS,CACRC,GAAG,YACHX,MAAOrB,EAASM,UAChB2B,SAAWC,GAAMf,EAAkB,YAAae,EAAEC,OAAOd,OACzDe,UAAWtC,IAAWC,EACtBsC,YAAY,qDAMlBV,EAAAA,EAAAA,KAAA,OAAKF,UAAU,iBAAgBC,UAC7BF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,QAAOC,SAAA,EACpBC,EAAAA,EAAAA,KAAA,SAAOG,QAAQ,SAASL,UAAU,YAAWC,SAAC,kBAG9CC,EAAAA,EAAAA,KAACY,EAAAA,EAAQ,CACPP,GAAG,SACHX,MAAOrB,EAASS,OAChBwB,SAAWC,GAAMf,EAAkB,SAAUe,EAAEb,OAC/Ce,UAAWtC,IAAWC,EACtByC,QAAS,CACP,CAAEC,MAAO,SAAKpB,MAAO,UACrB,CAAEoB,MAAO,SAAKpB,MAAO,WAEvBgB,YAAY,yCAKlBV,EAAAA,EAAAA,KAAA,OAAKF,UAAU,iBAAgBC,UAC7BF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,QAAOC,SAAA,EACpBC,EAAAA,EAAAA,KAAA,SAAOG,QAAQ,YAAYL,UAAU,YAAWC,SAAC,kBAGjDC,EAAAA,EAAAA,KAACe,EAAAA,EAAQ,CACPV,GAAG,YACHX,MAAOrB,EAASQ,UAChByB,SAAWC,GAAMf,EAAkB,YAAae,EAAEb,OAClDe,UAAWtC,IAAWC,EACtB4C,WAAW,WACXC,UAAQ,EACRP,YAAY,yCAKlBV,EAAAA,EAAAA,KAAA,OAAKF,UAAU,kBAAiBC,UAC9BF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,QAAOC,SAAA,EACpBC,EAAAA,EAAAA,KAAA,SAAOG,QAAQ,UAAUL,UAAU,YAAWC,SAAC,kBAG/CC,EAAAA,EAAAA,KAACI,EAAAA,EAAS,CACRC,GAAG,UACHX,MAAOrB,EAASO,QAChB0B,SAAWC,GAAMf,EAAkB,UAAWe,EAAEC,OAAOd,OACvDe,UAAWtC,IAAWC,EACtBsC,YAAY,yCAKlBV,EAAAA,EAAAA,KAAA,OAAKF,UAAU,oBAIfE,EAAAA,EAAAA,KAAA,OAAKF,UAAU,iBAAiBoB,QAAM,EAAAnB,UACpCF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,QAAOC,SAAA,EACpBC,EAAAA,EAAAA,KAAA,SAAOG,QAAQ,WAAWL,UAAU,YAAWC,SAAC,kBAGhDC,EAAAA,EAAAA,KAACI,EAAAA,EAAS,CACRC,GAAG,WACHX,MAAOrB,EAASY,SAChBwB,UAAQ,UAKdT,EAAAA,EAAAA,KAAA,OAAKF,UAAU,iBAAgBC,UAC7BF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,QAAOC,SAAA,EACpBC,EAAAA,EAAAA,KAAA,SAAOG,QAAQ,YAAYL,UAAU,YAAWC,SAAC,8BAGjDC,EAAAA,EAAAA,KAACY,EAAAA,EAAQ,CACPP,GAAG,YACHX,MAAOrB,EAASU,UAChBuB,SAAWC,GAAMf,EAAkB,YAAae,EAAEb,OAClDe,UAAWtC,IAAWC,EACtByC,QAAS,CACP,CAAEC,MAAO,eAAMpB,OAAO,GACtB,CAAEoB,MAAO,eAAMpB,OAAO,IAExBgB,YAAY,4CAMpBV,EAAAA,EAAAA,KAAA,OAAKF,UAAU,sCAAqCC,UAChD5B,GAAUC,KACV4B,EAAAA,EAAAA,KAACmB,EAAAA,EAAM,CACLL,MAAO1C,EAAW,eAAO,eACzBgD,KAAMhD,EAAW,aAAe,cAChCiD,QAnOSC,UAEnB,GAAKjD,EAASG,UAAaH,EAASI,YAApC,CASAU,GAAW,GACX,IAAK,IAADoC,EACF,IAAIC,EAGFA,EAFEpD,QAEeqD,EAAAA,EAAIC,KAAK,oBAAqB,CAC7CC,KAAMtD,EAASG,SACfoD,SAAUvD,EAASI,YACnBoD,MAAOxD,EAASK,UAChBoD,MAAOzD,EAASM,UAChBC,QAASP,EAASO,QAClBC,UAAWR,EAASQ,UAAYR,EAASQ,UAAUkD,cAAgB,KACnEjD,OAAQT,EAASS,OACjBC,UAAWV,EAASU,kBAIL0C,EAAAA,EAAIO,IAAI,oBAAqB,CAC5C3B,GAAIhC,EAASiB,OACbqC,KAAMtD,EAASG,SACfoD,SAAUvD,EAASI,YACnBoD,MAAOxD,EAASK,UAChBoD,MAAOzD,EAASM,UAChBC,QAASP,EAASO,QAClBC,UAAWR,EAASQ,UAAYR,EAASQ,UAAUkD,cAAgB,KACnEjD,OAAQT,EAASS,OACjBC,UAAWV,EAASU,YAIX,QAAbwC,EAAAxD,EAAMkE,eAAO,IAAAV,GAAbA,EAAeW,KAAK,CAClBC,SAAU,UACVC,QAAShE,EAAW,2BAAS,2BAC7BiE,OAAQb,EAASc,KAAKC,SAAO,uCAAAC,OAAapE,EAAW,eAAO,eAAI,kBAIlEqE,YAAW,KACT5E,EAAS,cACR,KAEL,CAAE,MAAO6E,GAAa,IAADC,EAAAC,EACN,QAAbD,EAAA5E,EAAMkE,eAAO,IAAAU,GAAbA,EAAeT,KAAK,CAClBC,SAAU,QACVC,QAAShE,EAAW,2BAAS,2BAC7BiE,QAAsB,QAAdO,EAAAF,EAAMlB,gBAAQ,IAAAoB,OAAA,EAAdA,EAAgBN,OAAI,iCAAAE,OAAYpE,EAAW,eAAO,eAAI,qDAElE,CAAC,QACCe,GAAW,EACb,CAnDA,KAPA,CAAkD,IAAD0D,EAClC,QAAbA,EAAA9E,EAAMkE,eAAO,IAAAY,GAAbA,EAAeX,KAAK,CAClBC,SAAU,QACVC,QAAS,2BACTC,OAAQ,oDAGZ,GA2NUnD,QAASA,Y", "sources": ["components/Page/DoctorDetailPage.tsx"], "sourcesContent": ["import { Button } from 'primereact/button';\r\nimport { Calendar } from 'primereact/calendar';\r\nimport { Dropdown } from 'primereact/dropdown';\r\nimport { InputText } from 'primereact/inputtext';\r\nimport { Toast } from 'primereact/toast';\r\nimport React, { useEffect, useRef, useState } from 'react';\r\nimport { useLocation, useNavigate } from 'react-router-dom';\r\nimport api from '../../services/api';\r\n\r\ninterface Doctor {\r\n  userId?: number;\r\n  userName: string;\r\n  userAccount: string;\r\n  userEmail: string;\r\n  userPhone: string;\r\n  address: string;\r\n  birthDate: Date | null;\r\n  gender: string;\r\n  isEnabled: boolean;\r\n  roleId: number;\r\n  roleName: string;\r\n}\r\n\r\nconst DoctorDetailPage: React.FC = () => {\r\n  const location = useLocation();\r\n  const navigate = useNavigate();\r\n  const toast = useRef<Toast>(null);\r\n\r\n  const doctor = location.state?.doctor;\r\n  const isEdit = location.state?.isEdit || false;\r\n  const isCreate = !doctor;\r\n\r\n  const [formData, setFormData] = useState<Doctor>({\r\n    userName: '',\r\n    userAccount: '',\r\n    userEmail: '',\r\n    userPhone: '',\r\n    address: '',\r\n    birthDate: null,\r\n    gender: '',\r\n    isEnabled: true,\r\n    roleId: 3, // 預設為治療師角色\r\n    roleName: 'user'\r\n  });\r\n\r\n  const [loading, setLoading] = useState(false);\r\n\r\n  useEffect(() => {\r\n    if (doctor) {\r\n      setFormData({\r\n        userId: doctor.userId,\r\n        userName: doctor.userName || '',\r\n        userAccount: doctor.userAccount || '',\r\n        userEmail: doctor.userEmail || '',\r\n        userPhone: doctor.userPhone || '',\r\n        address: doctor.address || '',\r\n        birthDate: doctor.birthDate ? new Date(doctor.birthDate) : null,\r\n        gender: doctor.gender || '',\r\n        isEnabled: doctor.isEnabled ?? true,\r\n        roleId: doctor.roleId || 2,\r\n        roleName: doctor.roleName || '治療師'\r\n      });\r\n    }\r\n  }, [doctor]);\r\n\r\n  const handleInputChange = (field: keyof Doctor, value: any) => {\r\n    setFormData(prev => ({ ...prev, [field]: value }));\r\n  };\r\n\r\n  const handleSubmit = async () => {\r\n    // 驗證必填欄位\r\n    if (!formData.userName || !formData.userAccount) {\r\n      toast.current?.show({\r\n        severity: 'error',\r\n        summary: '驗證失敗',\r\n        detail: '請填寫姓名和帳號'\r\n      });\r\n      return;\r\n    }\r\n\r\n    setLoading(true);\r\n    try {\r\n      let response;\r\n      if (isCreate) {\r\n        // 新增治療師\r\n        response = await api.post('/api/Users/<USER>', {\r\n          name: formData.userName,\r\n          username: formData.userAccount,\r\n          email: formData.userEmail,\r\n          phone: formData.userPhone,\r\n          address: formData.address,\r\n          birthDate: formData.birthDate ? formData.birthDate.toISOString() : null,\r\n          gender: formData.gender,\r\n          isEnabled: formData.isEnabled\r\n        });\r\n      } else {\r\n        // 更新治療師\r\n        response = await api.put('/api/Users/<USER>', {\r\n          id: formData.userId,\r\n          name: formData.userName,\r\n          username: formData.userAccount,\r\n          email: formData.userEmail,\r\n          phone: formData.userPhone,\r\n          address: formData.address,\r\n          birthDate: formData.birthDate ? formData.birthDate.toISOString() : null,\r\n          gender: formData.gender,\r\n          isEnabled: formData.isEnabled\r\n        });\r\n      }\r\n\r\n      toast.current?.show({\r\n        severity: 'success',\r\n        summary: isCreate ? '新增成功' : '更新成功',\r\n        detail: response.data.message || `治療師資料已${isCreate ? '新增' : '更新'}成功`\r\n      });\r\n\r\n      // 延遲返回列表頁面\r\n      setTimeout(() => {\r\n        navigate('/doctors');\r\n      }, 1500);\r\n\r\n    } catch (error: any) {\r\n      toast.current?.show({\r\n        severity: 'error',\r\n        summary: isCreate ? '新增失敗' : '更新失敗',\r\n        detail: error.response?.data || `治療師資料${isCreate ? '新增' : '更新'}失敗，請稍後再試`\r\n      });\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n    \r\n\r\n  return (\r\n    <div className=\"doctor-detail-page\">\r\n      <Toast ref={toast} />\r\n\r\n      <div className=\"card\">\r\n\r\n        <div className=\"grid formgrid p-fluid\">\r\n          <div className=\"col-6 md:col-2\">\r\n            <div className=\"field\">\r\n              <label htmlFor=\"userName\" className=\"font-bold\">\r\n                姓名 <span className=\"text-red-500\">*</span>\r\n              </label>\r\n              <InputText\r\n                id=\"userName\"\r\n                value={formData.userName}\r\n                onChange={(e) => handleInputChange('userName', e.target.value)}\r\n                disabled={!isEdit && !isCreate}\r\n                placeholder=\"請輸入姓名\"\r\n              />\r\n            </div>\r\n          </div>\r\n\r\n          <div className=\"col-6 md:col-2\">\r\n            <div className=\"field\">\r\n              <label htmlFor=\"userAccount\" className=\"font-bold\">\r\n                帳號 <span className=\"text-red-500\">*</span>\r\n              </label>\r\n              <InputText\r\n                id=\"userAccount\"\r\n                value={formData.userAccount}\r\n                onChange={(e) => handleInputChange('userAccount', e.target.value)}\r\n                disabled={!isEdit && !isCreate}\r\n                placeholder=\"請輸入帳號\"\r\n              />\r\n            </div>\r\n          </div>\r\n\r\n          <div className=\"col-6 md:col-2\">\r\n            <div className=\"field\">\r\n              <label htmlFor=\"userEmail\" className=\"font-bold\">\r\n                Email\r\n              </label>\r\n              <InputText\r\n                id=\"userEmail\"\r\n                value={formData.userEmail}\r\n                onChange={(e) => handleInputChange('userEmail', e.target.value)}\r\n                disabled={!isEdit && !isCreate}\r\n                placeholder=\"請輸入 Email\"\r\n                type=\"email\"\r\n              />\r\n            </div>\r\n          </div>\r\n\r\n          <div className=\"col-6 md:col-2\">\r\n            <div className=\"field\">\r\n              <label htmlFor=\"userPhone\" className=\"font-bold\">\r\n                電話\r\n              </label>\r\n              <InputText\r\n                id=\"userPhone\"\r\n                value={formData.userPhone}\r\n                onChange={(e) => handleInputChange('userPhone', e.target.value)}\r\n                disabled={!isEdit && !isCreate}\r\n                placeholder=\"請輸入電話號碼\"\r\n              />\r\n            </div>\r\n          </div>\r\n\r\n                    \r\n          <div className=\"col-6 md:col-2\">\r\n            <div className=\"field\">\r\n              <label htmlFor=\"gender\" className=\"font-bold\">\r\n                性別\r\n              </label>\r\n              <Dropdown\r\n                id=\"gender\"\r\n                value={formData.gender}\r\n                onChange={(e) => handleInputChange('gender', e.value)}\r\n                disabled={!isEdit && !isCreate}\r\n                options={[\r\n                  { label: '男', value: '男' },\r\n                  { label: '女', value: '女' }\r\n                ]}\r\n                placeholder=\"請選擇性別\"\r\n              />\r\n            </div>\r\n          </div>\r\n\r\n          <div className=\"col-6 md:col-2\">\r\n            <div className=\"field\">\r\n              <label htmlFor=\"birthDate\" className=\"font-bold\">\r\n                生日\r\n              </label>\r\n              <Calendar\r\n                id=\"birthDate\"\r\n                value={formData.birthDate}\r\n                onChange={(e) => handleInputChange('birthDate', e.value)}\r\n                disabled={!isEdit && !isCreate}\r\n                dateFormat=\"yy-mm-dd\"\r\n                showIcon\r\n                placeholder=\"請選擇生日\"\r\n              />\r\n            </div>\r\n          </div>\r\n\r\n          <div className=\"col-12 md:col-8\">\r\n            <div className=\"field\">\r\n              <label htmlFor=\"address\" className=\"font-bold\">\r\n                地址\r\n              </label>\r\n              <InputText\r\n                id=\"address\"\r\n                value={formData.address}\r\n                onChange={(e) => handleInputChange('address', e.target.value)}\r\n                disabled={!isEdit && !isCreate}\r\n                placeholder=\"請輸入地址\"\r\n              />\r\n            </div>\r\n          </div>\r\n          \r\n          <div className=\"col-0 md:col-4\">\r\n\r\n          </div>\r\n\r\n          <div className=\"col-6 md:col-2\" hidden>\r\n            <div className=\"field\">\r\n              <label htmlFor=\"roleName\" className=\"font-bold\">\r\n                角色\r\n              </label>\r\n              <InputText\r\n                id=\"roleName\"\r\n                value={formData.roleName}\r\n                disabled\r\n              />\r\n            </div>\r\n          </div>\r\n\r\n          <div className=\"col-6 md:col-2\">\r\n            <div className=\"field\">\r\n              <label htmlFor=\"isEnabled\" className=\"font-bold\">\r\n                啟用狀態\r\n              </label>\r\n              <Dropdown\r\n                id=\"isEnabled\"\r\n                value={formData.isEnabled}\r\n                onChange={(e) => handleInputChange('isEnabled', e.value)}\r\n                disabled={!isEdit && !isCreate}\r\n                options={[\r\n                  { label: '啟用', value: true },\r\n                  { label: '停用', value: false }\r\n                ]}\r\n                placeholder=\"請選擇狀態\"\r\n              />\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <div className=\"flex gap-2 justify-content-end mt-4\">\r\n          {(isEdit || isCreate) && (\r\n            <Button\r\n              label={isCreate ? '新增' : '更新'}\r\n              icon={isCreate ? 'pi pi-plus' : 'pi pi-check'}\r\n              onClick={handleSubmit}\r\n              loading={loading}\r\n            />\r\n          )}\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default DoctorDetailPage;"], "names": ["DoctorDetailPage", "_location$state", "_location$state2", "location", "useLocation", "navigate", "useNavigate", "toast", "useRef", "doctor", "state", "isEdit", "isCreate", "formData", "setFormData", "useState", "userName", "userAccount", "userEmail", "userPhone", "address", "birthDate", "gender", "isEnabled", "roleId", "<PERSON><PERSON><PERSON>", "loading", "setLoading", "useEffect", "_doctor$isEnabled", "userId", "Date", "handleInputChange", "field", "value", "prev", "_objectSpread", "_jsxs", "className", "children", "_jsx", "Toast", "ref", "htmlFor", "InputText", "id", "onChange", "e", "target", "disabled", "placeholder", "type", "Dropdown", "options", "label", "Calendar", "dateFormat", "showIcon", "hidden", "<PERSON><PERSON>", "icon", "onClick", "async", "_toast$current2", "response", "api", "post", "name", "username", "email", "phone", "toISOString", "put", "current", "show", "severity", "summary", "detail", "data", "message", "concat", "setTimeout", "error", "_toast$current3", "_error$response", "_toast$current"], "sourceRoot": ""}
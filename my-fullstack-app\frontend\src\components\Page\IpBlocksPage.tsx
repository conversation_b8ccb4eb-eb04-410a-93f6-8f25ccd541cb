import React, { useState, useRef, useEffect } from 'react';
import { Button } from 'primereact/button';
import { Column } from 'primereact/column';
import { DataTable } from 'primereact/datatable';
import { Dialog } from 'primereact/dialog';
import { Toast } from 'primereact/toast';
import { ConfirmDialog, confirmDialog } from 'primereact/confirmdialog';
import { Card } from 'primereact/card';
import { ProgressSpinner } from 'primereact/progressspinner';
import { Tag } from 'primereact/tag';
import { InputText } from 'primereact/inputtext';
import { Calendar } from 'primereact/calendar';
import { formatUtcToTaipei } from "../../utils/dateUtils";
import api from '../../services/api';
import { log } from '../../utils/logger';

interface IpBlock {
  id: number;
  ipAddress: string;
  createdAt: string;
  updatedAt: string;
  expiredAt: string;
}

const IpBlocksPage: React.FC = () => {
  const toast = useRef<Toast>(null);
  const [blocks, setBlocks] = useState<IpBlock[]>([]);
  const [loading, setLoading] = useState(true);
  const [totalRecords, setTotalRecords] = useState(0);
  const [first, setFirst] = useState(0);
  const [rows, setRows] = useState(20);
  const [showAddDialog, setShowAddDialog] = useState(false);
  const [newIpAddress, setNewIpAddress] = useState('');
  const [newExpiredAt, setNewExpiredAt] = useState<Date | null>(null);

  // 搜尋條件
  const [filters, setFilters] = useState({
    ipAddress: '',
    createdAtStart: null as Date | null,
    createdAtEnd: null as Date | null
  });

  // 載入 IP 封鎖列表
  const loadBlocks = async (page = 1, pageSize = 20) => {
    try {
      setLoading(true);
      log.api('載入 IP 封鎖列表');

      const params: any = {
        page,
        pageSize
      };

      if (filters.ipAddress) params.ipAddress = filters.ipAddress;
      if (filters.createdAtStart) params.createdAtStart = filters.createdAtStart.toISOString();
      if (filters.createdAtEnd) params.createdAtEnd = filters.createdAtEnd.toISOString();

      const response = await api.get('/api/system/GetIpBlock', { params });
      
      setBlocks(response.data.data);
      setTotalRecords(response.data.totalCount);
      
      log.api('IP 封鎖列表載入成功', { count: response.data.data.length });
      
    } catch (error: any) {
      log.error('載入 IP 封鎖列表失敗', error);
      toast.current?.show({
        severity: 'error',
        summary: '載入失敗',
        detail: '無法載入 IP 封鎖列表',
        life: 5000
      });
    } finally {
      setLoading(false);
    }
  };

  // 新增 IP 封鎖
  const addIpBlock = async () => {
    if (!newIpAddress) {
      toast.current?.show({ severity: 'error', summary: '錯誤', detail: '請輸入 IP 位址' });
      return;
    }

    try {
      log.api('新增 IP 封鎖', { ipAddress: newIpAddress, expiredAt: newExpiredAt });

      await api.post('/api/system/AddIpBlock', { 
        ipAddress: newIpAddress, 
        expiredAt: newExpiredAt ? newExpiredAt.toISOString() : null 
      });

      toast.current?.show({
        severity: 'success',
        summary: '成功',
        detail: `IP ${newIpAddress} 已成功封鎖`,
        life: 3000
      });

      setShowAddDialog(false);
      setNewIpAddress('');
      setNewExpiredAt(null);
      loadBlocks(1, rows);

    } catch (error: any) {
      log.error('新增 IP 封鎖失敗', error);
      toast.current?.show({
        severity: 'error',
        summary: '新增失敗',
        detail: error.response?.data?.error || '新增 IP 封鎖失敗',
        life: 5000
      });
    }
  };

  // 解鎖 IP
  const unlockIp = async (block: IpBlock) => {
    try {
      log.api('解鎖 IP', { id: block.id, ipAddress: block.ipAddress });
      
      await api.post('/api/system/UpdateIpBlock', { id: block.id });
      
      toast.current?.show({
        severity: 'success',
        summary: '解鎖成功',
        detail: `IP ${block.ipAddress} 已解鎖`,
        life: 3000
      });
      
      // 重新載入列表
      loadBlocks(Math.floor(first / rows) + 1, rows);
      
    } catch (error: any) {
      log.error('解鎖 IP 失敗', error);
      toast.current?.show({
        severity: 'error',
        summary: '解鎖失敗',
        detail: error.response?.data?.error || '解鎖 IP 失敗',
        life: 5000
      });
    }
  };

  // 確認解鎖
  const confirmUnlock = (block: IpBlock) => {
    confirmDialog({
      message: `確定要解鎖 IP "${block.ipAddress}" 嗎？`,
      header: '確認解鎖',
      icon: 'pi pi-exclamation-triangle',
      acceptLabel: '確定',
      rejectLabel: '取消',
      accept: () => unlockIp(block),
    });
  };

  // 搜尋
  const handleSearch = () => {
    setFirst(0);
    loadBlocks(1, rows);
  };

  // 分頁變更
  const onPageChange = (event: any) => {
    setFirst(event.first);
    setRows(event.rows);
    const page = Math.floor(event.first / event.rows) + 1;
    loadBlocks(page, event.rows);
  };

  // 格式化日期
  const formatDate = (dateString: string): string => {
    if (!dateString) return '';
    try {
      return formatUtcToTaipei(dateString, 'yyyy/MM/dd HH:mm:ss');
    } catch (error) {
      console.error('Error formatting date:', error);
      return dateString;
    }
  };

  // 檢查是否已封鎖（根據當前時間）
  const isBlocked = (expiredAt: string): boolean => {
    const now = new Date();
    const expiredDate = new Date(expiredAt);
    return expiredDate > now;
  };

  // 狀態標籤模板
  const statusBodyTemplate = (rowData: IpBlock) => {
    const blocked = isBlocked(rowData.expiredAt);
    const severity = blocked ? 'danger' : 'success';
    const label = blocked ? '已封鎖' : '已解鎖';
    return <Tag value={label} severity={severity} />;
  };

  // 建立日期模板
  const createdDateBodyTemplate = (rowData: IpBlock) => {
    return formatDate(rowData.createdAt);
  };

  // 更新日期模板
  const updatedDateBodyTemplate = (rowData: IpBlock) => {
    return formatDate(rowData.updatedAt);
  };

  // 到期日期模板
  const expiredDateBodyTemplate = (rowData: IpBlock) => {
    return formatDate(rowData.expiredAt);
  };

  // 操作按鈕模板
  const actionBodyTemplate = (rowData: IpBlock) => {
    const blocked = isBlocked(rowData.expiredAt);

    // 只有在已封鎖狀態時才顯示解鎖按鈕
    if (!blocked) {
      return (
        <div className="flex gap-2">
          <span className="text-500 text-sm">已解鎖</span>
        </div>
      );
    }

    return (
      <div className="flex gap-2">
        <Button
          label="解鎖"
          icon="pi pi-unlock"
          className="p-button-success p-button-sm"
          onClick={() => confirmUnlock(rowData)}
          tooltip="解鎖此 IP"
          tooltipOptions={{ position: 'top' }}
        />
      </div>
    );
  };

  // 分頁器左側
  const paginatorLeft = (
    <Button
      type="button"
      icon="pi pi-refresh"
      text
      onClick={() => loadBlocks(Math.floor(first / rows) + 1, rows)}
      disabled={loading}
    />
  );

  const paginatorRight = <div></div>;

  useEffect(() => {
    loadBlocks();
  }, []);

  if (loading && blocks.length === 0) {
    return (
      <div className="flex justify-content-center align-items-center" style={{ height: '400px' }}>
        <ProgressSpinner />
      </div>
    );
  }

  return (
    <div>
      <Toast ref={toast} />
      <ConfirmDialog />
      
      <Card title="IP 封鎖管理" className="mb-4">
        <p className="text-600 line-height-3 m-0">
          管理系統中被封鎖的 IP 位址。當 IP 位址在短時間內進行過多請求時，系統會自動封鎖該 IP。您可以手動解鎖被封鎖的 IP 位址。
        </p>
      </Card>

      {/* 搜尋條件 */}
      <Card className="mb-4">
        <div className="grid">
          <div className="col-12 md:col-4">
            <InputText
              id="ipAddress"
              value={filters.ipAddress}
              onChange={(e) => setFilters({ ...filters, ipAddress: e.target.value })}
              placeholder="輸入 IP 位址"
              className="w-full"
            />
          </div>
          
          <div className="col-6 md:col-4">
            <Calendar
              value={filters.createdAtStart}
              onChange={(e) => setFilters({ ...filters, createdAtStart: e.value as Date })}
              placeholder="選擇開始日期"
              className="w-full"
              showIcon
              dateFormat="yy/mm/dd"
            />
          </div>
          
          <div className="col-6 md:col-4">
            <Calendar
              value={filters.createdAtEnd}
              onChange={(e) => setFilters({ ...filters, createdAtEnd: e.value as Date })}
              placeholder="選擇結束日期"
              className="w-full"
              showIcon
              dateFormat="yy/mm/dd"
            />
          </div>
          
          <div className="col-12 md:col-4">
            <div className="flex gap-2">
              <Button
                label="搜尋"
                icon="pi pi-search"
                onClick={handleSearch}
              />
              <Button
                label="添加"
                icon="pi pi-plus"
                onClick={() => setShowAddDialog(true)}
              />
            </div>
          </div>
        </div>
      </Card>

      {/* 資料表 */}
      <Card>
        <DataTable
          value={blocks}
          paginator
          lazy
          first={first}
          rows={rows}
          totalRecords={totalRecords}
          onPage={onPageChange}
          rowsPerPageOptions={[10, 20, 50]}
          emptyMessage="沒有找到 IP 封鎖記錄"
          tableStyle={{ minWidth: '50rem' }}
          paginatorLeft={paginatorLeft}
          paginatorRight={paginatorRight}
          loading={loading}
        >
          <Column field="ipAddress" header="IP 位址" sortable style={{ width: '15%' }} />
          <Column field="status" header="狀態" body={statusBodyTemplate} style={{ width: '10%' }} />
          <Column field="createdAt" header="建立時間" body={createdDateBodyTemplate} sortable style={{ width: '20%' }} />
          <Column field="updatedAt" header="更新時間" body={updatedDateBodyTemplate} sortable style={{ width: '20%' }} />
          <Column field="expiredAt" header="到期時間" body={expiredDateBodyTemplate} sortable style={{ width: '20%' }} />
          <Column header="操作" body={actionBodyTemplate} style={{ width: '15%' }} />
        </DataTable>
      </Card>

      {/* 新增 IP 封鎖對話框 */}
      <Dialog
        header="新增 IP 封鎖"
        visible={showAddDialog}
        style={{ width: '450px' }}
        modal
        onHide={() => setShowAddDialog(false)}
        footer={
          <div>
            <Button label="取消" icon="pi pi-times" onClick={() => setShowAddDialog(false)} className="p-button-text" />
            <Button label="新增" icon="pi pi-check" onClick={addIpBlock} autoFocus />
          </div>
        }
      >
        <div className="p-fluid">
          <div className="field">
            <label htmlFor="newIpAddress">IP 位址</label>
            <InputText id="newIpAddress" value={newIpAddress} onChange={(e) => setNewIpAddress(e.target.value)} />
          </div>
          <div className="field">
            <label htmlFor="newExpiredAt">到期時間 (可選)</label>
            <Calendar id="newExpiredAt" value={newExpiredAt} onChange={(e) => setNewExpiredAt(e.value as Date)} showIcon dateFormat="yy/mm/dd" />
          </div>
        </div>
      </Dialog>
    </div>
  );
};

export default IpBlocksPage;

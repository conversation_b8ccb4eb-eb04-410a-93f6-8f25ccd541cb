{"ast": null, "code": "import React,{useState,useRef,useEffect}from'react';import{Button}from'primereact/button';import{Column}from'primereact/column';import{DataTable}from'primereact/datatable';import{Toast}from'primereact/toast';import{ConfirmDialog,confirmDialog}from'primereact/confirmdialog';import{Card}from'primereact/card';import{ProgressSpinner}from'primereact/progressspinner';import{Tag}from'primereact/tag';import{InputText}from'primereact/inputtext';import{Calendar}from'primereact/calendar';import{formatUtcToTaipei}from\"../../utils/dateUtils\";import api from'../../services/api';import{log}from'../../utils/logger';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const ReportManagementPage=()=>{const toast=useRef(null);const[reports,setReports]=useState([]);const[loading,setLoading]=useState(true);const[refreshing,setRefreshing]=useState(false);const[fileNameFilter,setFileNameFilter]=useState('');const[startDateFilter,setStartDateFilter]=useState(null);const[endDateFilter,setEndDateFilter]=useState(null);// 載入報表列表\nconst loadReports=async()=>{try{setRefreshing(true);log.api('載入報表列表');const params={fileName:fileNameFilter,startDate:startDateFilter?startDateFilter.toISOString():undefined,endDate:endDateFilter?endDateFilter.toISOString():undefined};const response=await api.get('/api/file/GetReportFiles',{params});setReports(response.data);log.api('報表列表載入成功',{count:response.data.length});}catch(error){var _toast$current;log.error('載入報表列表失敗',error);(_toast$current=toast.current)===null||_toast$current===void 0?void 0:_toast$current.show({severity:'error',summary:'載入失敗',detail:'無法載入報表列表',life:5000});}finally{setLoading(false);setRefreshing(false);}};const handleSearch=()=>{loadReports();};const handleReset=()=>{setFileNameFilter('');setStartDateFilter(null);setEndDateFilter(null);loadReports();};// 刪除報表\nconst deleteReport=async report=>{try{var _toast$current2;log.api('刪除報表',{fileName:report.fileName});await api.delete(\"/api/file/DeleteReportFile\",{params:{fileName:report.fileName}});(_toast$current2=toast.current)===null||_toast$current2===void 0?void 0:_toast$current2.show({severity:'success',summary:'刪除成功',detail:\"\\u5831\\u8868 \".concat(report.fileName,\" \\u5DF2\\u522A\\u9664\"),life:3000});// 重新載入列表\nloadReports();}catch(error){var _toast$current3,_error$response,_error$response$data;log.error('刪除報表失敗',error);(_toast$current3=toast.current)===null||_toast$current3===void 0?void 0:_toast$current3.show({severity:'error',summary:'刪除失敗',detail:((_error$response=error.response)===null||_error$response===void 0?void 0:(_error$response$data=_error$response.data)===null||_error$response$data===void 0?void 0:_error$response$data.message)||'刪除報表失敗',life:5000});}};// 下載報表\nconst downloadReport=async report=>{try{var _toast$current4;log.api('下載報表',{fileName:report.fileName});const response=await api.get(\"/api/file/DownloadReportFile\",{params:{fileName:report.fileName},responseType:'blob'});// 創建下載連結\nconst blob=new Blob([response.data],{type:'application/pdf'});const url=window.URL.createObjectURL(blob);const link=document.createElement('a');link.href=url;link.download=report.fileName;document.body.appendChild(link);link.click();document.body.removeChild(link);window.URL.revokeObjectURL(url);(_toast$current4=toast.current)===null||_toast$current4===void 0?void 0:_toast$current4.show({severity:'success',summary:'下載成功',detail:\"\\u5831\\u8868 \".concat(report.fileName,\" \\u4E0B\\u8F09\\u5B8C\\u6210\"),life:3000});}catch(error){var _toast$current5;log.error('下載報表失敗',error);(_toast$current5=toast.current)===null||_toast$current5===void 0?void 0:_toast$current5.show({severity:'error',summary:'下載失敗',detail:'下載報表失敗',life:5000});}};// 預覽報表\nconst previewReport=async report=>{try{log.api('預覽報表',{fileName:report.fileName});const response=await api.get(\"/api/file/DownloadReportFile\",{params:{fileName:report.fileName},responseType:'blob'});// 在新視窗開啟 PDF\nconst blob=new Blob([response.data],{type:'application/pdf'});const url=window.URL.createObjectURL(blob);window.open(url,'_blank');}catch(error){var _toast$current6;log.error('預覽報表失敗',error);(_toast$current6=toast.current)===null||_toast$current6===void 0?void 0:_toast$current6.show({severity:'error',summary:'預覽失敗',detail:'預覽報表失敗',life:5000});}};// 確認刪除\nconst confirmDelete=report=>{confirmDialog({message:\"\\u78BA\\u5B9A\\u8981\\u522A\\u9664\\u5831\\u8868 \\\"\".concat(report.fileName,\"\\\" \\u55CE\\uFF1F\\u6B64\\u64CD\\u4F5C\\u7121\\u6CD5\\u5FA9\\u539F\\u3002\"),header:'確認刪除',icon:'pi pi-exclamation-triangle',acceptLabel:'確定',rejectLabel:'取消',accept:()=>deleteReport(report)});};// 格式化檔案大小\nconst formatFileSize=bytes=>{if(bytes===0)return'0 Bytes';const k=1024;const sizes=['Bytes','KB','MB','GB'];const i=Math.floor(Math.log(bytes)/Math.log(k));return parseFloat((bytes/Math.pow(k,i)).toFixed(2))+' '+sizes[i];};// 格式化日期\nconst formatDate=dateString=>{if(!dateString)return'';try{return formatUtcToTaipei(dateString,'yyyy/MM/dd HH:mm:ss');}catch(error){console.error('Error formatting date:',error);return dateString;}};// 檔案大小模板\nconst fileSizeBodyTemplate=rowData=>{return formatFileSize(rowData.fileSize);};// 建立日期模板\nconst createdDateBodyTemplate=rowData=>{return formatDate(rowData.createdDate);};// 修改日期模板\nconst modifiedDateBodyTemplate=rowData=>{return formatDate(rowData.modifiedDate);};// 檔案類型模板\nconst fileTypeBodyTemplate=rowData=>{var _rowData$fileName$spl;const extension=(_rowData$fileName$spl=rowData.fileName.split('.').pop())===null||_rowData$fileName$spl===void 0?void 0:_rowData$fileName$spl.toUpperCase();let severity='info';switch(extension){case'PDF':severity='danger';break;case'XLSX':case'XLS':severity='success';break;case'DOCX':case'DOC':severity='info';break;default:severity='warning';}return/*#__PURE__*/_jsx(Tag,{value:extension,severity:severity});};// 操作按鈕模板\nconst actionBodyTemplate=rowData=>{return/*#__PURE__*/_jsxs(\"div\",{className:\"flex gap-2\",children:[/*#__PURE__*/_jsx(Button,{icon:\"pi pi-eye\",className:\"p-button-info p-button-sm\",onClick:()=>previewReport(rowData),tooltip:\"\\u9810\\u89BD\",tooltipOptions:{position:'top'}}),/*#__PURE__*/_jsx(Button,{icon:\"pi pi-download\",className:\"p-button-success p-button-sm\",onClick:()=>downloadReport(rowData),tooltip:\"\\u4E0B\\u8F09\",tooltipOptions:{position:'top'}}),/*#__PURE__*/_jsx(Button,{icon:\"pi pi-trash\",className:\"p-button-danger p-button-sm\",onClick:()=>confirmDelete(rowData),tooltip:\"\\u522A\\u9664\",tooltipOptions:{position:'top'}})]});};// 分頁器左側\nconst paginatorLeft=/*#__PURE__*/_jsx(Button,{type:\"button\",icon:\"pi pi-refresh\",text:true,onClick:loadReports,disabled:refreshing});const paginatorRight=/*#__PURE__*/_jsx(\"div\",{});useEffect(()=>{loadReports();},[]);if(loading){return/*#__PURE__*/_jsx(\"div\",{className:\"flex justify-content-center align-items-center\",style:{height:'400px'},children:/*#__PURE__*/_jsx(ProgressSpinner,{})});}return/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(Toast,{ref:toast}),/*#__PURE__*/_jsx(ConfirmDialog,{}),/*#__PURE__*/_jsx(Card,{title:\"\\u5831\\u8868\\u7BA1\\u7406\",className:\"mb-4\",children:/*#__PURE__*/_jsx(\"p\",{className:\"text-600 line-height-3 m-0\",children:\"\\u7BA1\\u7406\\u7CFB\\u7D71\\u751F\\u6210\\u7684 PDF \\u5831\\u8868\\u6A94\\u6848\\uFF0C\\u5305\\u62EC\\u6536\\u64DA\\u3001\\u7D71\\u8A08\\u5831\\u8868\\u7B49\\u3002\\u60A8\\u53EF\\u4EE5\\u9810\\u89BD\\u3001\\u4E0B\\u8F09\\u6216\\u522A\\u9664\\u4E0D\\u9700\\u8981\\u7684\\u5831\\u8868\\u6A94\\u6848\\u3002\"})}),/*#__PURE__*/_jsx(Card,{className:\"mb-4\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"grid\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"col-12 md:col-4\",children:/*#__PURE__*/_jsx(InputText,{id:\"fileNameFilter\",value:fileNameFilter,onChange:e=>setFileNameFilter(e.target.value),placeholder:\"\\u4F9D\\u6A94\\u540D\\u641C\\u5C0B\",className:\"w-full\"})}),/*#__PURE__*/_jsx(\"div\",{className:\"col-6 md:col-3\",children:/*#__PURE__*/_jsx(Calendar,{id:\"startDateFilter\",value:startDateFilter,onChange:e=>setStartDateFilter(e.value),placeholder:\"\\u9078\\u64C7\\u958B\\u59CB\\u65E5\\u671F\",className:\"w-full\",dateFormat:\"yy/mm/dd\",showIcon:true})}),/*#__PURE__*/_jsx(\"div\",{className:\"col-6 md:col-3\",children:/*#__PURE__*/_jsx(Calendar,{id:\"endDateFilter\",value:endDateFilter,onChange:e=>setEndDateFilter(e.value),placeholder:\"\\u9078\\u64C7\\u7D50\\u675F\\u65E5\\u671F\",className:\"w-full\",dateFormat:\"yy/mm/dd\",showIcon:true})}),/*#__PURE__*/_jsx(\"div\",{className:\"col-12 md:col-4\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex gap-2\",children:[/*#__PURE__*/_jsx(Button,{label:\"\\u641C\\u5C0B\",icon:\"pi pi-search\",onClick:handleSearch,className:\"mr-2\"}),/*#__PURE__*/_jsx(Button,{label:\"\\u91CD\\u8A2D\",icon:\"pi pi-undo\",onClick:handleReset,className:\"p-button-secondary\"})]})})]})}),/*#__PURE__*/_jsx(Card,{children:/*#__PURE__*/_jsxs(DataTable,{value:reports,paginator:true,rows:20,rowsPerPageOptions:[10,20,50],emptyMessage:\"\\u6C92\\u6709\\u627E\\u5230\\u5831\\u8868\\u6A94\\u6848\",tableStyle:{minWidth:'50rem'},paginatorLeft:paginatorLeft,paginatorRight:paginatorRight,loading:refreshing,children:[/*#__PURE__*/_jsx(Column,{field:\"fileName\",header:\"\\u6A94\\u6848\\u540D\\u7A31\",sortable:true,style:{width:'30%'}}),/*#__PURE__*/_jsx(Column,{field:\"fileType\",header:\"\\u985E\\u578B\",body:fileTypeBodyTemplate,style:{width:'10%'}}),/*#__PURE__*/_jsx(Column,{field:\"fileSize\",header:\"\\u6A94\\u6848\\u5927\\u5C0F\",body:fileSizeBodyTemplate,sortable:true,style:{width:'15%'}}),/*#__PURE__*/_jsx(Column,{field:\"createdDate\",header:\"\\u5EFA\\u7ACB\\u65E5\\u671F\",body:createdDateBodyTemplate,sortable:true,style:{width:'20%'}}),/*#__PURE__*/_jsx(Column,{field:\"modifiedDate\",header:\"\\u4FEE\\u6539\\u65E5\\u671F\",body:modifiedDateBodyTemplate,sortable:true,style:{width:'20%'}}),/*#__PURE__*/_jsx(Column,{header:\"\\u64CD\\u4F5C\",body:actionBodyTemplate,style:{width:'15%'}})]})})]});};export default ReportManagementPage;", "map": {"version": 3, "names": ["React", "useState", "useRef", "useEffect", "<PERSON><PERSON>", "Column", "DataTable", "Toast", "ConfirmDialog", "confirmDialog", "Card", "ProgressSpinner", "Tag", "InputText", "Calendar", "formatUtcToTaipei", "api", "log", "jsx", "_jsx", "jsxs", "_jsxs", "ReportManagementPage", "toast", "reports", "setReports", "loading", "setLoading", "refreshing", "setRefreshing", "fileNameFilter", "setFileNameFilter", "startDateFilter", "setStartDateFilter", "endDateFilter", "setEndDateFilter", "loadReports", "params", "fileName", "startDate", "toISOString", "undefined", "endDate", "response", "get", "data", "count", "length", "error", "_toast$current", "current", "show", "severity", "summary", "detail", "life", "handleSearch", "handleReset", "deleteReport", "report", "_toast$current2", "delete", "concat", "_toast$current3", "_error$response", "_error$response$data", "message", "downloadReport", "_toast$current4", "responseType", "blob", "Blob", "type", "url", "window", "URL", "createObjectURL", "link", "document", "createElement", "href", "download", "body", "append<PERSON><PERSON><PERSON>", "click", "<PERSON><PERSON><PERSON><PERSON>", "revokeObjectURL", "_toast$current5", "previewReport", "open", "_toast$current6", "confirmDelete", "header", "icon", "acceptLabel", "<PERSON><PERSON><PERSON><PERSON>", "accept", "formatFileSize", "bytes", "k", "sizes", "i", "Math", "floor", "parseFloat", "pow", "toFixed", "formatDate", "dateString", "console", "fileSizeBodyTemplate", "rowData", "fileSize", "createdDateBodyTemplate", "createdDate", "modifiedDateBodyTemplate", "modifiedDate", "fileTypeBodyTemplate", "_rowData$fileName$spl", "extension", "split", "pop", "toUpperCase", "value", "actionBodyTemplate", "className", "children", "onClick", "tooltip", "tooltipOptions", "position", "paginatorLeft", "text", "disabled", "paginatorRight", "style", "height", "ref", "title", "id", "onChange", "e", "target", "placeholder", "dateFormat", "showIcon", "label", "paginator", "rows", "rowsPerPageOptions", "emptyMessage", "tableStyle", "min<PERSON><PERSON><PERSON>", "field", "sortable", "width"], "sources": ["C:/Users/<USER>/Desktop/demo-git/demo-react/my-fullstack-app/frontend/src/components/Page/ReportManagementPage.tsx"], "sourcesContent": ["import React, { useState, useRef, useEffect } from 'react';\r\nimport { Button } from 'primereact/button';\r\nimport { Column } from 'primereact/column';\r\nimport { DataTable } from 'primereact/datatable';\r\nimport { Toast } from 'primereact/toast';\r\nimport { ConfirmDialog, confirmDialog } from 'primereact/confirmdialog';\r\nimport { Card } from 'primereact/card';\r\nimport { ProgressSpinner } from 'primereact/progressspinner';\r\nimport { Tag } from 'primereact/tag';\r\nimport { InputText } from 'primereact/inputtext';\r\nimport { Calendar } from 'primereact/calendar';\r\nimport { formatUtcToTaipei } from \"../../utils/dateUtils\";\r\nimport api from '../../services/api';\r\nimport { log } from '../../utils/logger';\r\n\r\ninterface ReportFile {\r\n  fileName: string;\r\n  filePath: string;\r\n  fileSize: number;\r\n  createdDate: string;\r\n  modifiedDate: string;\r\n}\r\n\r\nconst ReportManagementPage: React.FC = () => {\r\n  const toast = useRef<Toast>(null);\r\n  const [reports, setReports] = useState<ReportFile[]>([]);\r\n  const [loading, setLoading] = useState(true);\r\n  const [refreshing, setRefreshing] = useState(false);\r\n  const [fileNameFilter, setFileNameFilter] = useState('');\r\n  const [startDateFilter, setStartDateFilter] = useState<Date | null>(null);\r\n  const [endDateFilter, setEndDateFilter] = useState<Date | null>(null);\r\n\r\n  // 載入報表列表\r\n  const loadReports = async () => {\r\n    try {\r\n      setRefreshing(true);\r\n      log.api('載入報表列表');\r\n\r\n      const params = {\r\n        fileName: fileNameFilter,\r\n        startDate: startDateFilter ? startDateFilter.toISOString() : undefined,\r\n        endDate: endDateFilter ? endDateFilter.toISOString() : undefined,\r\n      };\r\n\r\n      const response = await api.get('/api/file/GetReportFiles', { params });\r\n      setReports(response.data);\r\n\r\n      log.api('報表列表載入成功', { count: response.data.length });\r\n\r\n    } catch (error: any) {\r\n      log.error('載入報表列表失敗', error);\r\n      toast.current?.show({\r\n        severity: 'error',\r\n        summary: '載入失敗',\r\n        detail: '無法載入報表列表',\r\n        life: 5000\r\n      });\r\n    } finally {\r\n      setLoading(false);\r\n      setRefreshing(false);\r\n    }\r\n  };\r\n\r\n  const handleSearch = () => {\r\n    loadReports();\r\n  };\r\n\r\n  const handleReset = () => {\r\n    setFileNameFilter('');\r\n    setStartDateFilter(null);\r\n    setEndDateFilter(null);\r\n    loadReports();\r\n  };\r\n\r\n  // 刪除報表\r\n  const deleteReport = async (report: ReportFile) => {\r\n    try {\r\n      log.api('刪除報表', { fileName: report.fileName });\r\n\r\n      await api.delete(`/api/file/DeleteReportFile`, {\r\n        params: { fileName: report.fileName }\r\n      });\r\n\r\n      toast.current?.show({\r\n        severity: 'success',\r\n        summary: '刪除成功',\r\n        detail: `報表 ${report.fileName} 已刪除`,\r\n        life: 3000\r\n      });\r\n\r\n      // 重新載入列表\r\n      loadReports();\r\n\r\n    } catch (error: any) {\r\n      log.error('刪除報表失敗', error);\r\n      toast.current?.show({\r\n        severity: 'error',\r\n        summary: '刪除失敗',\r\n        detail: error.response?.data?.message || '刪除報表失敗',\r\n        life: 5000\r\n      });\r\n    }\r\n  };\r\n\r\n  // 下載報表\r\n  const downloadReport = async (report: ReportFile) => {\r\n    try {\r\n      log.api('下載報表', { fileName: report.fileName });\r\n\r\n      const response = await api.get(`/api/file/DownloadReportFile`, {\r\n        params: { fileName: report.fileName },\r\n        responseType: 'blob'\r\n      });\r\n\r\n      // 創建下載連結\r\n      const blob = new Blob([response.data], { type: 'application/pdf' });\r\n      const url = window.URL.createObjectURL(blob);\r\n      const link = document.createElement('a');\r\n      link.href = url;\r\n      link.download = report.fileName;\r\n      document.body.appendChild(link);\r\n      link.click();\r\n      document.body.removeChild(link);\r\n      window.URL.revokeObjectURL(url);\r\n\r\n      toast.current?.show({\r\n        severity: 'success',\r\n        summary: '下載成功',\r\n        detail: `報表 ${report.fileName} 下載完成`,\r\n        life: 3000\r\n      });\r\n\r\n    } catch (error: any) {\r\n      log.error('下載報表失敗', error);\r\n      toast.current?.show({\r\n        severity: 'error',\r\n        summary: '下載失敗',\r\n        detail: '下載報表失敗',\r\n        life: 5000\r\n      });\r\n    }\r\n  };\r\n\r\n  // 預覽報表\r\n  const previewReport = async (report: ReportFile) => {\r\n    try {\r\n      log.api('預覽報表', { fileName: report.fileName });\r\n\r\n      const response = await api.get(`/api/file/DownloadReportFile`, {\r\n        params: { fileName: report.fileName },\r\n        responseType: 'blob'\r\n      });\r\n\r\n      // 在新視窗開啟 PDF\r\n      const blob = new Blob([response.data], { type: 'application/pdf' });\r\n      const url = window.URL.createObjectURL(blob);\r\n      window.open(url, '_blank');\r\n\r\n    } catch (error: any) {\r\n      log.error('預覽報表失敗', error);\r\n      toast.current?.show({\r\n        severity: 'error',\r\n        summary: '預覽失敗',\r\n        detail: '預覽報表失敗',\r\n        life: 5000\r\n      });\r\n    }\r\n  };\r\n\r\n  // 確認刪除\r\n  const confirmDelete = (report: ReportFile) => {\r\n    confirmDialog({\r\n      message: `確定要刪除報表 \"${report.fileName}\" 嗎？此操作無法復原。`,\r\n      header: '確認刪除',\r\n      icon: 'pi pi-exclamation-triangle',\r\n      acceptLabel: '確定',\r\n      rejectLabel: '取消',\r\n      accept: () => deleteReport(report),\r\n    });\r\n  };\r\n\r\n  // 格式化檔案大小\r\n  const formatFileSize = (bytes: number): string => {\r\n    if (bytes === 0) return '0 Bytes';\r\n    const k = 1024;\r\n    const sizes = ['Bytes', 'KB', 'MB', 'GB'];\r\n    const i = Math.floor(Math.log(bytes) / Math.log(k));\r\n    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];\r\n  };\r\n\r\n  // 格式化日期\r\n  const formatDate = (dateString: string): string => {\r\n    if (!dateString) return '';\r\n    try {\r\n      return formatUtcToTaipei(dateString, 'yyyy/MM/dd HH:mm:ss');\r\n    } catch (error) {\r\n      console.error('Error formatting date:', error);\r\n      return dateString;\r\n    }\r\n  };\r\n\r\n  // 檔案大小模板\r\n  const fileSizeBodyTemplate = (rowData: ReportFile) => {\r\n    return formatFileSize(rowData.fileSize);\r\n  };\r\n\r\n  // 建立日期模板\r\n  const createdDateBodyTemplate = (rowData: ReportFile) => {\r\n    return formatDate(rowData.createdDate);\r\n  };\r\n\r\n  // 修改日期模板\r\n  const modifiedDateBodyTemplate = (rowData: ReportFile) => {\r\n    return formatDate(rowData.modifiedDate);\r\n  };\r\n\r\n  // 檔案類型模板\r\n  const fileTypeBodyTemplate = (rowData: ReportFile) => {\r\n    const extension = rowData.fileName.split('.').pop()?.toUpperCase();\r\n    let severity: \"success\" | \"info\" | \"warning\" | \"danger\" = 'info';\r\n\r\n    switch (extension) {\r\n      case 'PDF':\r\n        severity = 'danger';\r\n        break;\r\n      case 'XLSX':\r\n      case 'XLS':\r\n        severity = 'success';\r\n        break;\r\n      case 'DOCX':\r\n      case 'DOC':\r\n        severity = 'info';\r\n        break;\r\n      default:\r\n        severity = 'warning';\r\n    }\r\n\r\n    return <Tag value={extension} severity={severity} />;\r\n  };\r\n\r\n  // 操作按鈕模板\r\n  const actionBodyTemplate = (rowData: ReportFile) => {\r\n    return (\r\n      <div className=\"flex gap-2\">\r\n        <Button\r\n          icon=\"pi pi-eye\"\r\n          className=\"p-button-info p-button-sm\"\r\n          onClick={() => previewReport(rowData)}\r\n          tooltip=\"預覽\"\r\n          tooltipOptions={{ position: 'top' }}\r\n        />\r\n        <Button\r\n          icon=\"pi pi-download\"\r\n          className=\"p-button-success p-button-sm\"\r\n          onClick={() => downloadReport(rowData)}\r\n          tooltip=\"下載\"\r\n          tooltipOptions={{ position: 'top' }}\r\n        />\r\n        <Button\r\n          icon=\"pi pi-trash\"\r\n          className=\"p-button-danger p-button-sm\"\r\n          onClick={() => confirmDelete(rowData)}\r\n          tooltip=\"刪除\"\r\n          tooltipOptions={{ position: 'top' }}\r\n        />\r\n      </div>\r\n    );\r\n  };\r\n\r\n  // 分頁器左側\r\n  const paginatorLeft = (\r\n    <Button\r\n      type=\"button\"\r\n      icon=\"pi pi-refresh\"\r\n      text\r\n      onClick={loadReports}\r\n      disabled={refreshing}\r\n    />\r\n  );\r\n\r\n  const paginatorRight = <div></div>;\r\n\r\n  useEffect(() => {\r\n    loadReports();\r\n  }, []);\r\n\r\n  if (loading) {\r\n    return (\r\n      <div className=\"flex justify-content-center align-items-center\" style={{ height: '400px' }}>\r\n        <ProgressSpinner />\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <div>\r\n      <Toast ref={toast} />\r\n      <ConfirmDialog />\r\n\r\n      <Card title=\"報表管理\" className=\"mb-4\">\r\n        <p className=\"text-600 line-height-3 m-0\">\r\n          管理系統生成的 PDF 報表檔案，包括收據、統計報表等。您可以預覽、下載或刪除不需要的報表檔案。\r\n        </p>\r\n      </Card>\r\n\r\n      {/* 搜尋條件 */}\r\n      <Card className=\"mb-4\">\r\n        <div className=\"grid\">\r\n          <div className=\"col-12 md:col-4\">\r\n            <InputText \r\n              id=\"fileNameFilter\" \r\n              value={fileNameFilter} \r\n              onChange={(e) => setFileNameFilter(e.target.value)} \r\n              placeholder=\"依檔名搜尋\" \r\n              className=\"w-full\"\r\n            />\r\n          </div>\r\n          <div className=\"col-6 md:col-3\">\r\n            <Calendar \r\n              id=\"startDateFilter\" \r\n              value={startDateFilter} \r\n              onChange={(e) => setStartDateFilter(e.value as Date)} \r\n              placeholder=\"選擇開始日期\"\r\n              className=\"w-full\"\r\n              dateFormat=\"yy/mm/dd\" \r\n              showIcon />\r\n          </div>\r\n          <div className=\"col-6 md:col-3\">\r\n            <Calendar \r\n              id=\"endDateFilter\" \r\n              value={endDateFilter} \r\n              onChange={(e) => setEndDateFilter(e.value as Date)} \r\n              placeholder=\"選擇結束日期\"\r\n              className=\"w-full\"\r\n              dateFormat=\"yy/mm/dd\" \r\n              showIcon />\r\n          </div>\r\n          <div className=\"col-12 md:col-4\">\r\n            <div className=\"flex gap-2\">\r\n              <Button \r\n                label=\"搜尋\" \r\n                icon=\"pi pi-search\" \r\n                onClick={handleSearch} \r\n                className=\"mr-2\" />\r\n              <Button \r\n                label=\"重設\" \r\n                icon=\"pi pi-undo\" \r\n                onClick={handleReset} \r\n                className=\"p-button-secondary\" />\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </Card>\r\n\r\n      <Card>\r\n        <DataTable\r\n          value={reports}\r\n          paginator\r\n          rows={20}\r\n          rowsPerPageOptions={[10, 20, 50]}\r\n          emptyMessage=\"沒有找到報表檔案\"\r\n          tableStyle={{ minWidth: '50rem' }}\r\n          paginatorLeft={paginatorLeft}\r\n          paginatorRight={paginatorRight}\r\n          loading={refreshing}\r\n        >\r\n          <Column field=\"fileName\" header=\"檔案名稱\" sortable style={{ width: '30%' }} />\r\n          <Column field=\"fileType\" header=\"類型\" body={fileTypeBodyTemplate} style={{ width: '10%' }} />\r\n          <Column field=\"fileSize\" header=\"檔案大小\" body={fileSizeBodyTemplate} sortable style={{ width: '15%' }} />\r\n          <Column field=\"createdDate\" header=\"建立日期\" body={createdDateBodyTemplate} sortable style={{ width: '20%' }} />\r\n          <Column field=\"modifiedDate\" header=\"修改日期\" body={modifiedDateBodyTemplate} sortable style={{ width: '20%' }} />\r\n          <Column header=\"操作\" body={actionBodyTemplate} style={{ width: '15%' }} />\r\n        </DataTable>\r\n      </Card>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default ReportManagementPage;\r\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,MAAM,CAAEC,SAAS,KAAQ,OAAO,CAC1D,OAASC,MAAM,KAAQ,mBAAmB,CAC1C,OAASC,MAAM,KAAQ,mBAAmB,CAC1C,OAASC,SAAS,KAAQ,sBAAsB,CAChD,OAASC,KAAK,KAAQ,kBAAkB,CACxC,OAASC,aAAa,CAAEC,aAAa,KAAQ,0BAA0B,CACvE,OAASC,IAAI,KAAQ,iBAAiB,CACtC,OAASC,eAAe,KAAQ,4BAA4B,CAC5D,OAASC,GAAG,KAAQ,gBAAgB,CACpC,OAASC,SAAS,KAAQ,sBAAsB,CAChD,OAASC,QAAQ,KAAQ,qBAAqB,CAC9C,OAASC,iBAAiB,KAAQ,uBAAuB,CACzD,MAAO,CAAAC,GAAG,KAAM,oBAAoB,CACpC,OAASC,GAAG,KAAQ,oBAAoB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAUzC,KAAM,CAAAC,oBAA8B,CAAGA,CAAA,GAAM,CAC3C,KAAM,CAAAC,KAAK,CAAGrB,MAAM,CAAQ,IAAI,CAAC,CACjC,KAAM,CAACsB,OAAO,CAAEC,UAAU,CAAC,CAAGxB,QAAQ,CAAe,EAAE,CAAC,CACxD,KAAM,CAACyB,OAAO,CAAEC,UAAU,CAAC,CAAG1B,QAAQ,CAAC,IAAI,CAAC,CAC5C,KAAM,CAAC2B,UAAU,CAAEC,aAAa,CAAC,CAAG5B,QAAQ,CAAC,KAAK,CAAC,CACnD,KAAM,CAAC6B,cAAc,CAAEC,iBAAiB,CAAC,CAAG9B,QAAQ,CAAC,EAAE,CAAC,CACxD,KAAM,CAAC+B,eAAe,CAAEC,kBAAkB,CAAC,CAAGhC,QAAQ,CAAc,IAAI,CAAC,CACzE,KAAM,CAACiC,aAAa,CAAEC,gBAAgB,CAAC,CAAGlC,QAAQ,CAAc,IAAI,CAAC,CAErE;AACA,KAAM,CAAAmC,WAAW,CAAG,KAAAA,CAAA,GAAY,CAC9B,GAAI,CACFP,aAAa,CAAC,IAAI,CAAC,CACnBZ,GAAG,CAACD,GAAG,CAAC,QAAQ,CAAC,CAEjB,KAAM,CAAAqB,MAAM,CAAG,CACbC,QAAQ,CAAER,cAAc,CACxBS,SAAS,CAAEP,eAAe,CAAGA,eAAe,CAACQ,WAAW,CAAC,CAAC,CAAGC,SAAS,CACtEC,OAAO,CAAER,aAAa,CAAGA,aAAa,CAACM,WAAW,CAAC,CAAC,CAAGC,SACzD,CAAC,CAED,KAAM,CAAAE,QAAQ,CAAG,KAAM,CAAA3B,GAAG,CAAC4B,GAAG,CAAC,0BAA0B,CAAE,CAAEP,MAAO,CAAC,CAAC,CACtEZ,UAAU,CAACkB,QAAQ,CAACE,IAAI,CAAC,CAEzB5B,GAAG,CAACD,GAAG,CAAC,UAAU,CAAE,CAAE8B,KAAK,CAAEH,QAAQ,CAACE,IAAI,CAACE,MAAO,CAAC,CAAC,CAEtD,CAAE,MAAOC,KAAU,CAAE,KAAAC,cAAA,CACnBhC,GAAG,CAAC+B,KAAK,CAAC,UAAU,CAAEA,KAAK,CAAC,CAC5B,CAAAC,cAAA,CAAA1B,KAAK,CAAC2B,OAAO,UAAAD,cAAA,iBAAbA,cAAA,CAAeE,IAAI,CAAC,CAClBC,QAAQ,CAAE,OAAO,CACjBC,OAAO,CAAE,MAAM,CACfC,MAAM,CAAE,UAAU,CAClBC,IAAI,CAAE,IACR,CAAC,CAAC,CACJ,CAAC,OAAS,CACR5B,UAAU,CAAC,KAAK,CAAC,CACjBE,aAAa,CAAC,KAAK,CAAC,CACtB,CACF,CAAC,CAED,KAAM,CAAA2B,YAAY,CAAGA,CAAA,GAAM,CACzBpB,WAAW,CAAC,CAAC,CACf,CAAC,CAED,KAAM,CAAAqB,WAAW,CAAGA,CAAA,GAAM,CACxB1B,iBAAiB,CAAC,EAAE,CAAC,CACrBE,kBAAkB,CAAC,IAAI,CAAC,CACxBE,gBAAgB,CAAC,IAAI,CAAC,CACtBC,WAAW,CAAC,CAAC,CACf,CAAC,CAED;AACA,KAAM,CAAAsB,YAAY,CAAG,KAAO,CAAAC,MAAkB,EAAK,CACjD,GAAI,KAAAC,eAAA,CACF3C,GAAG,CAACD,GAAG,CAAC,MAAM,CAAE,CAAEsB,QAAQ,CAAEqB,MAAM,CAACrB,QAAS,CAAC,CAAC,CAE9C,KAAM,CAAAtB,GAAG,CAAC6C,MAAM,8BAA+B,CAC7CxB,MAAM,CAAE,CAAEC,QAAQ,CAAEqB,MAAM,CAACrB,QAAS,CACtC,CAAC,CAAC,CAEF,CAAAsB,eAAA,CAAArC,KAAK,CAAC2B,OAAO,UAAAU,eAAA,iBAAbA,eAAA,CAAeT,IAAI,CAAC,CAClBC,QAAQ,CAAE,SAAS,CACnBC,OAAO,CAAE,MAAM,CACfC,MAAM,iBAAAQ,MAAA,CAAQH,MAAM,CAACrB,QAAQ,uBAAM,CACnCiB,IAAI,CAAE,IACR,CAAC,CAAC,CAEF;AACAnB,WAAW,CAAC,CAAC,CAEf,CAAE,MAAOY,KAAU,CAAE,KAAAe,eAAA,CAAAC,eAAA,CAAAC,oBAAA,CACnBhD,GAAG,CAAC+B,KAAK,CAAC,QAAQ,CAAEA,KAAK,CAAC,CAC1B,CAAAe,eAAA,CAAAxC,KAAK,CAAC2B,OAAO,UAAAa,eAAA,iBAAbA,eAAA,CAAeZ,IAAI,CAAC,CAClBC,QAAQ,CAAE,OAAO,CACjBC,OAAO,CAAE,MAAM,CACfC,MAAM,CAAE,EAAAU,eAAA,CAAAhB,KAAK,CAACL,QAAQ,UAAAqB,eAAA,kBAAAC,oBAAA,CAAdD,eAAA,CAAgBnB,IAAI,UAAAoB,oBAAA,iBAApBA,oBAAA,CAAsBC,OAAO,GAAI,QAAQ,CACjDX,IAAI,CAAE,IACR,CAAC,CAAC,CACJ,CACF,CAAC,CAED;AACA,KAAM,CAAAY,cAAc,CAAG,KAAO,CAAAR,MAAkB,EAAK,CACnD,GAAI,KAAAS,eAAA,CACFnD,GAAG,CAACD,GAAG,CAAC,MAAM,CAAE,CAAEsB,QAAQ,CAAEqB,MAAM,CAACrB,QAAS,CAAC,CAAC,CAE9C,KAAM,CAAAK,QAAQ,CAAG,KAAM,CAAA3B,GAAG,CAAC4B,GAAG,gCAAiC,CAC7DP,MAAM,CAAE,CAAEC,QAAQ,CAAEqB,MAAM,CAACrB,QAAS,CAAC,CACrC+B,YAAY,CAAE,MAChB,CAAC,CAAC,CAEF;AACA,KAAM,CAAAC,IAAI,CAAG,GAAI,CAAAC,IAAI,CAAC,CAAC5B,QAAQ,CAACE,IAAI,CAAC,CAAE,CAAE2B,IAAI,CAAE,iBAAkB,CAAC,CAAC,CACnE,KAAM,CAAAC,GAAG,CAAGC,MAAM,CAACC,GAAG,CAACC,eAAe,CAACN,IAAI,CAAC,CAC5C,KAAM,CAAAO,IAAI,CAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC,CACxCF,IAAI,CAACG,IAAI,CAAGP,GAAG,CACfI,IAAI,CAACI,QAAQ,CAAGtB,MAAM,CAACrB,QAAQ,CAC/BwC,QAAQ,CAACI,IAAI,CAACC,WAAW,CAACN,IAAI,CAAC,CAC/BA,IAAI,CAACO,KAAK,CAAC,CAAC,CACZN,QAAQ,CAACI,IAAI,CAACG,WAAW,CAACR,IAAI,CAAC,CAC/BH,MAAM,CAACC,GAAG,CAACW,eAAe,CAACb,GAAG,CAAC,CAE/B,CAAAL,eAAA,CAAA7C,KAAK,CAAC2B,OAAO,UAAAkB,eAAA,iBAAbA,eAAA,CAAejB,IAAI,CAAC,CAClBC,QAAQ,CAAE,SAAS,CACnBC,OAAO,CAAE,MAAM,CACfC,MAAM,iBAAAQ,MAAA,CAAQH,MAAM,CAACrB,QAAQ,6BAAO,CACpCiB,IAAI,CAAE,IACR,CAAC,CAAC,CAEJ,CAAE,MAAOP,KAAU,CAAE,KAAAuC,eAAA,CACnBtE,GAAG,CAAC+B,KAAK,CAAC,QAAQ,CAAEA,KAAK,CAAC,CAC1B,CAAAuC,eAAA,CAAAhE,KAAK,CAAC2B,OAAO,UAAAqC,eAAA,iBAAbA,eAAA,CAAepC,IAAI,CAAC,CAClBC,QAAQ,CAAE,OAAO,CACjBC,OAAO,CAAE,MAAM,CACfC,MAAM,CAAE,QAAQ,CAChBC,IAAI,CAAE,IACR,CAAC,CAAC,CACJ,CACF,CAAC,CAED;AACA,KAAM,CAAAiC,aAAa,CAAG,KAAO,CAAA7B,MAAkB,EAAK,CAClD,GAAI,CACF1C,GAAG,CAACD,GAAG,CAAC,MAAM,CAAE,CAAEsB,QAAQ,CAAEqB,MAAM,CAACrB,QAAS,CAAC,CAAC,CAE9C,KAAM,CAAAK,QAAQ,CAAG,KAAM,CAAA3B,GAAG,CAAC4B,GAAG,gCAAiC,CAC7DP,MAAM,CAAE,CAAEC,QAAQ,CAAEqB,MAAM,CAACrB,QAAS,CAAC,CACrC+B,YAAY,CAAE,MAChB,CAAC,CAAC,CAEF;AACA,KAAM,CAAAC,IAAI,CAAG,GAAI,CAAAC,IAAI,CAAC,CAAC5B,QAAQ,CAACE,IAAI,CAAC,CAAE,CAAE2B,IAAI,CAAE,iBAAkB,CAAC,CAAC,CACnE,KAAM,CAAAC,GAAG,CAAGC,MAAM,CAACC,GAAG,CAACC,eAAe,CAACN,IAAI,CAAC,CAC5CI,MAAM,CAACe,IAAI,CAAChB,GAAG,CAAE,QAAQ,CAAC,CAE5B,CAAE,MAAOzB,KAAU,CAAE,KAAA0C,eAAA,CACnBzE,GAAG,CAAC+B,KAAK,CAAC,QAAQ,CAAEA,KAAK,CAAC,CAC1B,CAAA0C,eAAA,CAAAnE,KAAK,CAAC2B,OAAO,UAAAwC,eAAA,iBAAbA,eAAA,CAAevC,IAAI,CAAC,CAClBC,QAAQ,CAAE,OAAO,CACjBC,OAAO,CAAE,MAAM,CACfC,MAAM,CAAE,QAAQ,CAChBC,IAAI,CAAE,IACR,CAAC,CAAC,CACJ,CACF,CAAC,CAED;AACA,KAAM,CAAAoC,aAAa,CAAIhC,MAAkB,EAAK,CAC5ClD,aAAa,CAAC,CACZyD,OAAO,iDAAAJ,MAAA,CAAcH,MAAM,CAACrB,QAAQ,mEAAc,CAClDsD,MAAM,CAAE,MAAM,CACdC,IAAI,CAAE,4BAA4B,CAClCC,WAAW,CAAE,IAAI,CACjBC,WAAW,CAAE,IAAI,CACjBC,MAAM,CAAEA,CAAA,GAAMtC,YAAY,CAACC,MAAM,CACnC,CAAC,CAAC,CACJ,CAAC,CAED;AACA,KAAM,CAAAsC,cAAc,CAAIC,KAAa,EAAa,CAChD,GAAIA,KAAK,GAAK,CAAC,CAAE,MAAO,SAAS,CACjC,KAAM,CAAAC,CAAC,CAAG,IAAI,CACd,KAAM,CAAAC,KAAK,CAAG,CAAC,OAAO,CAAE,IAAI,CAAE,IAAI,CAAE,IAAI,CAAC,CACzC,KAAM,CAAAC,CAAC,CAAGC,IAAI,CAACC,KAAK,CAACD,IAAI,CAACrF,GAAG,CAACiF,KAAK,CAAC,CAAGI,IAAI,CAACrF,GAAG,CAACkF,CAAC,CAAC,CAAC,CACnD,MAAO,CAAAK,UAAU,CAAC,CAACN,KAAK,CAAGI,IAAI,CAACG,GAAG,CAACN,CAAC,CAAEE,CAAC,CAAC,EAAEK,OAAO,CAAC,CAAC,CAAC,CAAC,CAAG,GAAG,CAAGN,KAAK,CAACC,CAAC,CAAC,CACzE,CAAC,CAED;AACA,KAAM,CAAAM,UAAU,CAAIC,UAAkB,EAAa,CACjD,GAAI,CAACA,UAAU,CAAE,MAAO,EAAE,CAC1B,GAAI,CACF,MAAO,CAAA7F,iBAAiB,CAAC6F,UAAU,CAAE,qBAAqB,CAAC,CAC7D,CAAE,MAAO5D,KAAK,CAAE,CACd6D,OAAO,CAAC7D,KAAK,CAAC,wBAAwB,CAAEA,KAAK,CAAC,CAC9C,MAAO,CAAA4D,UAAU,CACnB,CACF,CAAC,CAED;AACA,KAAM,CAAAE,oBAAoB,CAAIC,OAAmB,EAAK,CACpD,MAAO,CAAAd,cAAc,CAACc,OAAO,CAACC,QAAQ,CAAC,CACzC,CAAC,CAED;AACA,KAAM,CAAAC,uBAAuB,CAAIF,OAAmB,EAAK,CACvD,MAAO,CAAAJ,UAAU,CAACI,OAAO,CAACG,WAAW,CAAC,CACxC,CAAC,CAED;AACA,KAAM,CAAAC,wBAAwB,CAAIJ,OAAmB,EAAK,CACxD,MAAO,CAAAJ,UAAU,CAACI,OAAO,CAACK,YAAY,CAAC,CACzC,CAAC,CAED;AACA,KAAM,CAAAC,oBAAoB,CAAIN,OAAmB,EAAK,KAAAO,qBAAA,CACpD,KAAM,CAAAC,SAAS,EAAAD,qBAAA,CAAGP,OAAO,CAACzE,QAAQ,CAACkF,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,CAAC,CAAC,UAAAH,qBAAA,iBAAjCA,qBAAA,CAAmCI,WAAW,CAAC,CAAC,CAClE,GAAI,CAAAtE,QAAmD,CAAG,MAAM,CAEhE,OAAQmE,SAAS,EACf,IAAK,KAAK,CACRnE,QAAQ,CAAG,QAAQ,CACnB,MACF,IAAK,MAAM,CACX,IAAK,KAAK,CACRA,QAAQ,CAAG,SAAS,CACpB,MACF,IAAK,MAAM,CACX,IAAK,KAAK,CACRA,QAAQ,CAAG,MAAM,CACjB,MACF,QACEA,QAAQ,CAAG,SAAS,CACxB,CAEA,mBAAOjC,IAAA,CAACP,GAAG,EAAC+G,KAAK,CAAEJ,SAAU,CAACnE,QAAQ,CAAEA,QAAS,CAAE,CAAC,CACtD,CAAC,CAED;AACA,KAAM,CAAAwE,kBAAkB,CAAIb,OAAmB,EAAK,CAClD,mBACE1F,KAAA,QAAKwG,SAAS,CAAC,YAAY,CAAAC,QAAA,eACzB3G,IAAA,CAACf,MAAM,EACLyF,IAAI,CAAC,WAAW,CAChBgC,SAAS,CAAC,2BAA2B,CACrCE,OAAO,CAAEA,CAAA,GAAMvC,aAAa,CAACuB,OAAO,CAAE,CACtCiB,OAAO,CAAC,cAAI,CACZC,cAAc,CAAE,CAAEC,QAAQ,CAAE,KAAM,CAAE,CACrC,CAAC,cACF/G,IAAA,CAACf,MAAM,EACLyF,IAAI,CAAC,gBAAgB,CACrBgC,SAAS,CAAC,8BAA8B,CACxCE,OAAO,CAAEA,CAAA,GAAM5D,cAAc,CAAC4C,OAAO,CAAE,CACvCiB,OAAO,CAAC,cAAI,CACZC,cAAc,CAAE,CAAEC,QAAQ,CAAE,KAAM,CAAE,CACrC,CAAC,cACF/G,IAAA,CAACf,MAAM,EACLyF,IAAI,CAAC,aAAa,CAClBgC,SAAS,CAAC,6BAA6B,CACvCE,OAAO,CAAEA,CAAA,GAAMpC,aAAa,CAACoB,OAAO,CAAE,CACtCiB,OAAO,CAAC,cAAI,CACZC,cAAc,CAAE,CAAEC,QAAQ,CAAE,KAAM,CAAE,CACrC,CAAC,EACC,CAAC,CAEV,CAAC,CAED;AACA,KAAM,CAAAC,aAAa,cACjBhH,IAAA,CAACf,MAAM,EACLoE,IAAI,CAAC,QAAQ,CACbqB,IAAI,CAAC,eAAe,CACpBuC,IAAI,MACJL,OAAO,CAAE3F,WAAY,CACrBiG,QAAQ,CAAEzG,UAAW,CACtB,CACF,CAED,KAAM,CAAA0G,cAAc,cAAGnH,IAAA,SAAU,CAAC,CAElChB,SAAS,CAAC,IAAM,CACdiC,WAAW,CAAC,CAAC,CACf,CAAC,CAAE,EAAE,CAAC,CAEN,GAAIV,OAAO,CAAE,CACX,mBACEP,IAAA,QAAK0G,SAAS,CAAC,gDAAgD,CAACU,KAAK,CAAE,CAAEC,MAAM,CAAE,OAAQ,CAAE,CAAAV,QAAA,cACzF3G,IAAA,CAACR,eAAe,GAAE,CAAC,CAChB,CAAC,CAEV,CAEA,mBACEU,KAAA,QAAAyG,QAAA,eACE3G,IAAA,CAACZ,KAAK,EAACkI,GAAG,CAAElH,KAAM,CAAE,CAAC,cACrBJ,IAAA,CAACX,aAAa,GAAE,CAAC,cAEjBW,IAAA,CAACT,IAAI,EAACgI,KAAK,CAAC,0BAAM,CAACb,SAAS,CAAC,MAAM,CAAAC,QAAA,cACjC3G,IAAA,MAAG0G,SAAS,CAAC,4BAA4B,CAAAC,QAAA,CAAC,yQAE1C,CAAG,CAAC,CACA,CAAC,cAGP3G,IAAA,CAACT,IAAI,EAACmH,SAAS,CAAC,MAAM,CAAAC,QAAA,cACpBzG,KAAA,QAAKwG,SAAS,CAAC,MAAM,CAAAC,QAAA,eACnB3G,IAAA,QAAK0G,SAAS,CAAC,iBAAiB,CAAAC,QAAA,cAC9B3G,IAAA,CAACN,SAAS,EACR8H,EAAE,CAAC,gBAAgB,CACnBhB,KAAK,CAAE7F,cAAe,CACtB8G,QAAQ,CAAGC,CAAC,EAAK9G,iBAAiB,CAAC8G,CAAC,CAACC,MAAM,CAACnB,KAAK,CAAE,CACnDoB,WAAW,CAAC,gCAAO,CACnBlB,SAAS,CAAC,QAAQ,CACnB,CAAC,CACC,CAAC,cACN1G,IAAA,QAAK0G,SAAS,CAAC,gBAAgB,CAAAC,QAAA,cAC7B3G,IAAA,CAACL,QAAQ,EACP6H,EAAE,CAAC,iBAAiB,CACpBhB,KAAK,CAAE3F,eAAgB,CACvB4G,QAAQ,CAAGC,CAAC,EAAK5G,kBAAkB,CAAC4G,CAAC,CAAClB,KAAa,CAAE,CACrDoB,WAAW,CAAC,sCAAQ,CACpBlB,SAAS,CAAC,QAAQ,CAClBmB,UAAU,CAAC,UAAU,CACrBC,QAAQ,MAAE,CAAC,CACV,CAAC,cACN9H,IAAA,QAAK0G,SAAS,CAAC,gBAAgB,CAAAC,QAAA,cAC7B3G,IAAA,CAACL,QAAQ,EACP6H,EAAE,CAAC,eAAe,CAClBhB,KAAK,CAAEzF,aAAc,CACrB0G,QAAQ,CAAGC,CAAC,EAAK1G,gBAAgB,CAAC0G,CAAC,CAAClB,KAAa,CAAE,CACnDoB,WAAW,CAAC,sCAAQ,CACpBlB,SAAS,CAAC,QAAQ,CAClBmB,UAAU,CAAC,UAAU,CACrBC,QAAQ,MAAE,CAAC,CACV,CAAC,cACN9H,IAAA,QAAK0G,SAAS,CAAC,iBAAiB,CAAAC,QAAA,cAC9BzG,KAAA,QAAKwG,SAAS,CAAC,YAAY,CAAAC,QAAA,eACzB3G,IAAA,CAACf,MAAM,EACL8I,KAAK,CAAC,cAAI,CACVrD,IAAI,CAAC,cAAc,CACnBkC,OAAO,CAAEvE,YAAa,CACtBqE,SAAS,CAAC,MAAM,CAAE,CAAC,cACrB1G,IAAA,CAACf,MAAM,EACL8I,KAAK,CAAC,cAAI,CACVrD,IAAI,CAAC,YAAY,CACjBkC,OAAO,CAAEtE,WAAY,CACrBoE,SAAS,CAAC,oBAAoB,CAAE,CAAC,EAChC,CAAC,CACH,CAAC,EACH,CAAC,CACF,CAAC,cAEP1G,IAAA,CAACT,IAAI,EAAAoH,QAAA,cACHzG,KAAA,CAACf,SAAS,EACRqH,KAAK,CAAEnG,OAAQ,CACf2H,SAAS,MACTC,IAAI,CAAE,EAAG,CACTC,kBAAkB,CAAE,CAAC,EAAE,CAAE,EAAE,CAAE,EAAE,CAAE,CACjCC,YAAY,CAAC,kDAAU,CACvBC,UAAU,CAAE,CAAEC,QAAQ,CAAE,OAAQ,CAAE,CAClCrB,aAAa,CAAEA,aAAc,CAC7BG,cAAc,CAAEA,cAAe,CAC/B5G,OAAO,CAAEE,UAAW,CAAAkG,QAAA,eAEpB3G,IAAA,CAACd,MAAM,EAACoJ,KAAK,CAAC,UAAU,CAAC7D,MAAM,CAAC,0BAAM,CAAC8D,QAAQ,MAACnB,KAAK,CAAE,CAAEoB,KAAK,CAAE,KAAM,CAAE,CAAE,CAAC,cAC3ExI,IAAA,CAACd,MAAM,EAACoJ,KAAK,CAAC,UAAU,CAAC7D,MAAM,CAAC,cAAI,CAACV,IAAI,CAAEmC,oBAAqB,CAACkB,KAAK,CAAE,CAAEoB,KAAK,CAAE,KAAM,CAAE,CAAE,CAAC,cAC5FxI,IAAA,CAACd,MAAM,EAACoJ,KAAK,CAAC,UAAU,CAAC7D,MAAM,CAAC,0BAAM,CAACV,IAAI,CAAE4B,oBAAqB,CAAC4C,QAAQ,MAACnB,KAAK,CAAE,CAAEoB,KAAK,CAAE,KAAM,CAAE,CAAE,CAAC,cACvGxI,IAAA,CAACd,MAAM,EAACoJ,KAAK,CAAC,aAAa,CAAC7D,MAAM,CAAC,0BAAM,CAACV,IAAI,CAAE+B,uBAAwB,CAACyC,QAAQ,MAACnB,KAAK,CAAE,CAAEoB,KAAK,CAAE,KAAM,CAAE,CAAE,CAAC,cAC7GxI,IAAA,CAACd,MAAM,EAACoJ,KAAK,CAAC,cAAc,CAAC7D,MAAM,CAAC,0BAAM,CAACV,IAAI,CAAEiC,wBAAyB,CAACuC,QAAQ,MAACnB,KAAK,CAAE,CAAEoB,KAAK,CAAE,KAAM,CAAE,CAAE,CAAC,cAC/GxI,IAAA,CAACd,MAAM,EAACuF,MAAM,CAAC,cAAI,CAACV,IAAI,CAAE0C,kBAAmB,CAACW,KAAK,CAAE,CAAEoB,KAAK,CAAE,KAAM,CAAE,CAAE,CAAC,EAChE,CAAC,CACR,CAAC,EACJ,CAAC,CAEV,CAAC,CAED,cAAe,CAAArI,oBAAoB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using MyApi.Data;
using MyApi.Models;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;

namespace MyApi.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class FileController : ControllerBase
    {
        private readonly string _uploadPath = Path.Combine(Directory.GetCurrentDirectory(), "uploads");
        private readonly string _reportPath = Path.Combine(Directory.GetCurrentDirectory(), "wwwroot");
        private readonly ILogger<FileController> _logger;
        private readonly AppDbContext _context;

        public FileController(ILogger<FileController> logger, AppDbContext context)
        {
            _logger = logger;
            _context = context;
        }

        #region 報表檔案管理

        /// <summary>
        /// 獲取報表檔案列表
        /// </summary>
        [Authorize(Roles = "Admin,Manager")]
        [HttpGet("GetReportFiles")]
        public IActionResult GetReportFiles([FromQuery] string? fileName, [FromQuery] DateTime? startDate, [FromQuery] DateTime? endDate)
        {
            try
            {
                if (!Directory.Exists(_reportPath))
                    return Ok(new List<object>());

                var query = Directory.GetFiles(_reportPath, "*.pdf")
                    .Select(filePath => new FileInfo(filePath));

                if (!string.IsNullOrEmpty(fileName))
                {
                    query = query.Where(info => info.Name.Contains(fileName, StringComparison.OrdinalIgnoreCase));
                }

                if (startDate.HasValue)
                {
                    query = query.Where(info => info.CreationTime.Date >= startDate.Value.Date);
                }

                if (endDate.HasValue)
                {
                    query = query.Where(info => info.CreationTime.Date <= endDate.Value.Date);
                }

                var files = query
                    .Select(info => new
                    {
                        fileName = info.Name,
                        filePath = info.FullName,
                        fileSize = info.Length,
                        createdDate = info.CreationTime,
                        modifiedDate = info.LastWriteTime
                    })
                    .OrderByDescending(f => f.modifiedDate)
                    .ToList();

                _logger.LogInformation("獲取報表檔案列表成功，共 {Count} 個檔案", files.Count);
                return Ok(files);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "獲取報表檔案列表時發生錯誤");
                return StatusCode(500, new { error = "獲取報表檔案列表失敗", details = ex.Message });
            }
        }

        /// <summary>
        /// 下載報表檔案
        /// </summary>
        [Authorize(Roles = "Admin,Manager")]
        [HttpGet("DownloadReportFile")]
        public IActionResult DownloadReportFile([FromQuery] string fileName)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(fileName))
                    return BadRequest(new { error = "檔案名稱不能為空" });

                // 安全檢查：只允許 PDF 檔案
                if (!fileName.EndsWith(".pdf", StringComparison.OrdinalIgnoreCase))
                    return BadRequest(new { error = "只允許下載 PDF 檔案" });

                // 防止路徑遍歷攻擊
                var safeFileName = Path.GetFileName(fileName);
                var filePath = Path.Combine(_reportPath, safeFileName);

                // 確保檔案路徑在報表目錄內
                var fullReportPath = Path.GetFullPath(_reportPath);
                var fullFilePath = Path.GetFullPath(filePath);

                if (!fullFilePath.StartsWith(fullReportPath))
                {
                    _logger.LogWarning("嘗試訪問報表目錄外的檔案: {FilePath}", filePath);
                    return BadRequest(new { error = "無效的檔案路徑" });
                }

                if (!System.IO.File.Exists(filePath))
                {
                    _logger.LogWarning("報表檔案不存在: {FilePath}", filePath);
                    return NotFound(new { error = "檔案不存在" });
                }

                _logger.LogInformation("下載報表檔案: {FileName}", safeFileName);

                var fileBytes = System.IO.File.ReadAllBytes(filePath);
                return File(fileBytes, "application/pdf", safeFileName);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "下載報表檔案時發生錯誤: {FileName}", fileName);
                return StatusCode(500, new { error = "下載檔案失敗", details = ex.Message });
            }
        }

        /// <summary>
        /// 刪除報表檔案
        /// </summary>
        [Authorize(Roles = "Admin,Manager")]
        [HttpDelete("DeleteReportFile")]
        public IActionResult DeleteReportFile([FromQuery] string fileName)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(fileName))
                    return BadRequest(new { error = "檔案名稱不能為空" });

                // 安全檢查：只允許刪除 PDF 檔案
                if (!fileName.EndsWith(".pdf", StringComparison.OrdinalIgnoreCase))
                    return BadRequest(new { error = "只允許刪除 PDF 檔案" });

                // 防止路徑遍歷攻擊
                var safeFileName = Path.GetFileName(fileName);
                var filePath = Path.Combine(_reportPath, safeFileName);

                // 確保檔案路徑在報表目錄內
                var fullReportPath = Path.GetFullPath(_reportPath);
                var fullFilePath = Path.GetFullPath(filePath);

                if (!fullFilePath.StartsWith(fullReportPath))
                {
                    _logger.LogWarning("嘗試刪除報表目錄外的檔案: {FilePath}", filePath);
                    return BadRequest(new { error = "無效的檔案路徑" });
                }

                if (!System.IO.File.Exists(filePath))
                {
                    _logger.LogWarning("要刪除的報表檔案不存在: {FilePath}", filePath);
                    return NotFound(new { error = "檔案不存在" });
                }

                System.IO.File.Delete(filePath);
                _logger.LogInformation("報表檔案已刪除: {FileName}", safeFileName);

                var treatment = _context.Treatments
                .Where(t => t.ReceiptUrl == fileName)
                .FirstOrDefault();

                if (treatment != null)
                {
                    treatment.ReceiptUrl = string.Empty;
                }

                _context.SaveChanges();

                return Ok(new { message = "檔案已刪除" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "刪除報表檔案時發生錯誤: {FileName}", fileName);
                return StatusCode(500, new { error = "刪除檔案失敗", details = ex.Message });
            }
        }

        #endregion

        #region 圖片檔案管理

        /// <summary>
        /// 獲取圖片檔案列表
        /// </summary>
        [Authorize(Roles = "Admin,Manager")]
        [HttpGet("GetImageFiles")]
        public IActionResult GetImageFiles([FromQuery] string? fileName, [FromQuery] DateTime? startDate, [FromQuery] DateTime? endDate)
        {
            try
            {
                if (!Directory.Exists(_uploadPath))
                    return Ok(new List<object>());

                var imageExtensions = new[] { ".jpg", ".jpeg", ".png", ".gif", ".bmp", ".webp" };

                var query = Directory.GetFiles(_uploadPath)
                    .Where(filePath => imageExtensions.Contains(Path.GetExtension(filePath).ToLowerInvariant()))
                    .Select(filePath => new FileInfo(filePath));

                if (!string.IsNullOrEmpty(fileName))
                {
                    query = query.Where(info => info.Name.Contains(fileName, StringComparison.OrdinalIgnoreCase));
                }

                if (startDate.HasValue)
                {
                    query = query.Where(info => info.CreationTime.Date >= startDate.Value.Date);
                }

                if (endDate.HasValue)
                {
                    query = query.Where(info => info.CreationTime.Date <= endDate.Value.Date);
                }

                var files = query
                    .Select(info => new
                    {
                        fileName = info.Name,
                        filePath = info.FullName,
                        fileSize = info.Length,
                        createdDate = info.CreationTime,
                        modifiedDate = info.LastWriteTime,
                        imageUrl = $"/uploads/{info.Name}" // 用於前端顯示
                    })
                    .OrderByDescending(f => f.modifiedDate)
                    .ToList();

                _logger.LogInformation("獲取圖片檔案列表成功，共 {Count} 個檔案", files.Count);
                return Ok(files);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "獲取圖片檔案列表時發生錯誤");
                return StatusCode(500, new { error = "獲取圖片檔案列表失敗", details = ex.Message });
            }
        }

        /// <summary>
        /// 下載圖片檔案
        /// </summary>
        [Authorize(Roles = "Admin,Manager")]
        [HttpGet("DownloadImageFile")]
        public IActionResult DownloadImageFile([FromQuery] string fileName)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(fileName))
                    return BadRequest(new { error = "檔案名稱不能為空" });

                var imageExtensions = new[] { ".jpg", ".jpeg", ".png", ".gif", ".bmp", ".webp" };
                var extension = Path.GetExtension(fileName).ToLowerInvariant();
                
                // 安全檢查：只允許圖片檔案
                if (!imageExtensions.Contains(extension))
                    return BadRequest(new { error = "只允許下載圖片檔案" });

                // 防止路徑遍歷攻擊
                var safeFileName = Path.GetFileName(fileName);
                var filePath = Path.Combine(_uploadPath, safeFileName);

                // 確保檔案路徑在上傳目錄內
                var fullUploadPath = Path.GetFullPath(_uploadPath);
                var fullFilePath = Path.GetFullPath(filePath);

                if (!fullFilePath.StartsWith(fullUploadPath))
                {
                    _logger.LogWarning("嘗試訪問上傳目錄外的檔案: {FilePath}", filePath);
                    return BadRequest(new { error = "無效的檔案路徑" });
                }

                if (!System.IO.File.Exists(filePath))
                {
                    _logger.LogWarning("圖片檔案不存在: {FilePath}", filePath);
                    return NotFound(new { error = "檔案不存在" });
                }

                _logger.LogInformation("下載圖片檔案: {FileName}", safeFileName);

                var fileBytes = System.IO.File.ReadAllBytes(filePath);
                var contentType = GetImageContentType(extension);
                
                return File(fileBytes, contentType, safeFileName);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "下載圖片檔案時發生錯誤: {FileName}", fileName);
                return StatusCode(500, new { error = "下載檔案失敗", details = ex.Message });
            }
        }

        /// <summary>
        /// 刪除圖片檔案
        /// </summary>
        [Authorize(Roles = "Admin,Manager")]
        [HttpDelete("DeleteImageFile")]
        public IActionResult DeleteImageFile([FromQuery] string fileName)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(fileName))
                    return BadRequest(new { error = "檔案名稱不能為空" });

                var imageExtensions = new[] { ".jpg", ".jpeg", ".png", ".gif", ".bmp", ".webp" };
                var extension = Path.GetExtension(fileName).ToLowerInvariant();
                
                // 安全檢查：只允許刪除圖片檔案
                if (!imageExtensions.Contains(extension))
                    return BadRequest(new { error = "只允許刪除圖片檔案" });

                // 防止路徑遍歷攻擊
                var safeFileName = Path.GetFileName(fileName);
                var filePath = Path.Combine(_uploadPath, safeFileName);

                // 確保檔案路徑在上傳目錄內
                var fullUploadPath = Path.GetFullPath(_uploadPath);
                var fullFilePath = Path.GetFullPath(filePath);

                if (!fullFilePath.StartsWith(fullUploadPath))
                {
                    _logger.LogWarning("嘗試刪除上傳目錄外的檔案: {FilePath}", filePath);
                    return BadRequest(new { error = "無效的檔案路徑" });
                }

                if (!System.IO.File.Exists(filePath))
                {
                    _logger.LogWarning("要刪除的圖片檔案不存在: {FilePath}", filePath);
                    return NotFound(new { error = "檔案不存在" });
                }

                System.IO.File.Delete(filePath);
                _logger.LogInformation("圖片檔案已刪除: {FileName}", safeFileName);

                var treatment = _context.Treatments
                .Where(t => t.HospitalFormUrl == fileName )
                .FirstOrDefault();

                if (treatment != null)
                {
                    treatment.HospitalFormUrl = string.Empty;
                }

                treatment = _context.Treatments
                .Where(t =>  t.TreatmentConsentFormUrl == fileName)
                .FirstOrDefault();

                if (treatment != null)
                {
                    treatment.TreatmentConsentFormUrl = string.Empty;
                }

                _context.SaveChanges();

                return Ok(new { message = "檔案已刪除" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "刪除圖片檔案時發生錯誤: {FileName}", fileName);
                return StatusCode(500, new { error = "刪除檔案失敗", details = ex.Message });
            }
        }

        #endregion

        #region 輔助方法

        /// <summary>
        /// 根據檔案副檔名獲取圖片 MIME 類型
        /// </summary>
        private static string GetImageContentType(string extension)
        {
            return extension.ToLowerInvariant() switch
            {
                ".jpg" or ".jpeg" => "image/jpeg",
                ".png" => "image/png",
                ".gif" => "image/gif",
                ".bmp" => "image/bmp",
                ".webp" => "image/webp",
                _ => "application/octet-stream"
            };
        }

        #endregion
    }
}

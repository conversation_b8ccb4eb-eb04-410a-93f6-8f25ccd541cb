#!/bin/bash

# === 設定 ===
BACKUP_DIR="/home/<USER>/mysql-backup/binlog"
TODAY=$(date +%F)
YESTERDAY=$(date -d "yesterday" +%F)
FILE_NAME="binlog-${YESTERDAY}.sql.gz"
FILE_PATH="${BACKUP_DIR}/${FILE_NAME}"
RETENTION_DAYS=7

# MySQL 資訊
MYSQL_CONTAINER="my-mysql"
MYSQL_USER="root"
MYSQL_PASSWORD="example"

# Dropbox Remote 名稱
RCLONE_REMOTE="dropbox:mysql-incremental-backup"

mkdir -p "$BACKUP_DIR"

echo "🔄 [增量備份] $YESTERDAY"

# 匯出 binlog（使用 datetime 範圍）
docker exec "$MYSQL_CONTAINER" mysqlbinlog \
  --read-from-remote-server \
  --user="$MYSQL_USER" \
  --password="$MYSQL_PASSWORD" \
  --host=127.0.0.1 \
  --start-datetime="${YESTERDAY} 00:00:00" \
  --stop-datetime="${YESTERDAY} 23:59:59" \
  > "/tmp/${FILE_NAME%.gz}"

# 壓縮
gzip "/tmp/${FILE_NAME%.gz}"
mv "/tmp/${FILE_NAME}" "$FILE_PATH"

# 上傳至 Dropbox
rclone copy "$FILE_PATH" "$RCLONE_REMOTE"
rclone delete --min-age ${RETENTION_DAYS}d "$RCLONE_REMOTE"

# 清除本機過期檔
find "$BACKUP_DIR" -type f -name "*.sql.gz" -mtime +$RETENTION_DAYS -exec rm -f {} \;

echo "✅ 完成增量備份：$FILE_NAME"
{"ast": null, "code": "import React,{createContext,useContext,useEffect,useState}from'react';import{AuthApi}from'../services/apiService';import{log}from'../utils/logger';import{jsx as _jsx}from\"react/jsx-runtime\";const AuthContext=/*#__PURE__*/createContext(undefined);export const AuthProvider=_ref=>{let{children}=_ref;const[user,setUser]=useState(null);const[token,setToken]=useState(null);const[isLoading,setIsLoading]=useState(true);useEffect(()=>{// Check for existing token on mount\nconst storedToken=localStorage.getItem('token');const storedUserId=localStorage.getItem('userId');const storedUsername=localStorage.getItem('username');log.auth('初始化檢查',{hasToken:!!storedToken,hasUserId:!!storedUserId,hasUsername:!!storedUsername});if(storedToken&&storedUserId&&storedUsername){setToken(storedToken);setUser({id:parseInt(storedUserId),username:storedUsername});log.auth('從 localStorage 恢復用戶狀態');}else{// 清除不完整的登入狀態\nlocalStorage.removeItem('token');localStorage.removeItem('userId');localStorage.removeItem('username');localStorage.removeItem('isDefaultPassword');log.auth('清除不完整的登入狀態');}setIsLoading(false);},[]);const login=async credentials=>{try{log.auth('開始登入流程',{username:credentials.username});setIsLoading(true);// 調用標準化的 API 服務\nconst res=await AuthApi.login(credentials);log.auth('API 回應',res);// 從 API 回應中獲取數據\nconst{token,username,userId,isDefaultPassword}=res;if(token){// 創建用戶對象\nconst user={id:userId,username:username};log.auth('設置 token 和 user',{token:!!token,user,isDefaultPassword});setToken(token);setUser(user);localStorage.setItem('token',token);localStorage.setItem('userId',userId.toString());localStorage.setItem('username',username);localStorage.setItem('isDefaultPassword',isDefaultPassword.toString());log.auth('登入成功');return{isDefaultPassword:isDefaultPassword||false,userId,username};}else{throw new Error('API 回應格式錯誤：缺少 token');}}catch(error){log.error('AuthContext: 登入失敗',error);// 錯誤已經在 api.ts 中被標準化，直接拋出即可\nthrow error;}finally{setIsLoading(false);}};const logout=()=>{setUser(null);setToken(null);localStorage.removeItem('token');localStorage.removeItem('userId');localStorage.removeItem('username');localStorage.removeItem('isDefaultPassword');AuthApi.logout(user===null||user===void 0?void 0:user.id).catch(error=>log.error('登出API調用失敗',error));};const refreshToken=async()=>{try{const response=await AuthApi.refreshToken();setToken(response.token);localStorage.setItem('token',response.token);}catch(error){log.error('Token refresh failed:',error);logout();throw error;}};const value={user,token,isAuthenticated:!!token&&!!user,isLoading,login,logout,refreshToken};return/*#__PURE__*/_jsx(AuthContext.Provider,{value:value,children:children});};export const useAuth=()=>{const context=useContext(AuthContext);if(context===undefined){throw new Error('useAuth must be used within an AuthProvider');}return context;};", "map": {"version": 3, "names": ["React", "createContext", "useContext", "useEffect", "useState", "AuthA<PERSON>", "log", "jsx", "_jsx", "AuthContext", "undefined", "<PERSON>th<PERSON><PERSON><PERSON>", "_ref", "children", "user", "setUser", "token", "setToken", "isLoading", "setIsLoading", "storedToken", "localStorage", "getItem", "storedUserId", "storedUsername", "auth", "hasToken", "hasUserId", "hasUsername", "id", "parseInt", "username", "removeItem", "login", "credentials", "res", "userId", "isDefaultPassword", "setItem", "toString", "Error", "error", "logout", "catch", "refreshToken", "response", "value", "isAuthenticated", "Provider", "useAuth", "context"], "sources": ["C:/Users/<USER>/Desktop/demo-git/demo-react/my-fullstack-app/frontend/src/contexts/AuthContext.tsx"], "sourcesContent": ["import React, { createContext, ReactNode, useContext, useEffect, useState } from 'react';\r\nimport { AuthApi } from '../services/apiService';\r\nimport { log } from '../utils/logger';\r\n\r\ninterface User {\r\n  id: number;\r\n  username: string;\r\n  email?: string;\r\n  role?: string;\r\n}\r\n\r\ninterface LoginResult {\r\n  isDefaultPassword: boolean;\r\n  userId: number;\r\n  username: string;\r\n}\r\n\r\ninterface AuthContextType {\r\n  user: User | null;\r\n  token: string | null;\r\n  isAuthenticated: boolean;\r\n  isLoading: boolean;\r\n  login: (credentials: { username: string; password: string }) => Promise<LoginResult>;\r\n  logout: () => void;\r\n  refreshToken: () => Promise<void>;\r\n}\r\n\r\nconst AuthContext = createContext<AuthContextType | undefined>(undefined);\r\n\r\ninterface AuthProviderProps {\r\n  children: ReactNode;\r\n}\r\n\r\nexport const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {\r\n  const [user, setUser] = useState<User | null>(null);\r\n  const [token, setToken] = useState<string | null>(null);\r\n  const [isLoading, setIsLoading] = useState(true);\r\n\r\n  useEffect(() => {\r\n    // Check for existing token on mount\r\n    const storedToken = localStorage.getItem('token');\r\n    const storedUserId = localStorage.getItem('userId');\r\n    const storedUsername = localStorage.getItem('username');\r\n\r\n    log.auth('初始化檢查', {\r\n      hasToken: !!storedToken,\r\n      hasUserId: !!storedUserId,\r\n      hasUsername: !!storedUsername\r\n    });\r\n\r\n    if (storedToken && storedUserId && storedUsername) {\r\n      setToken(storedToken);\r\n      setUser({\r\n        id: parseInt(storedUserId),\r\n        username: storedUsername\r\n      });\r\n      log.auth('從 localStorage 恢復用戶狀態');\r\n    } else {\r\n      // 清除不完整的登入狀態\r\n      localStorage.removeItem('token');\r\n      localStorage.removeItem('userId');\r\n      localStorage.removeItem('username');\r\n      localStorage.removeItem('isDefaultPassword');\r\n      log.auth('清除不完整的登入狀態');\r\n    }\r\n    setIsLoading(false);\r\n  }, []);\r\n\r\n  const login = async (credentials: { username: string; password: string }): Promise<LoginResult> => {\r\n    try {\r\n      log.auth('開始登入流程', { username: credentials.username });\r\n      setIsLoading(true);\r\n\r\n      // 調用標準化的 API 服務\r\n      const res = await AuthApi.login(credentials);\r\n\r\n      log.auth('API 回應', res);\r\n\r\n      // 從 API 回應中獲取數據\r\n      const { token, username, userId, isDefaultPassword } = res;\r\n      if (token) {\r\n        // 創建用戶對象\r\n        const user = {\r\n          id: userId,\r\n          username: username,\r\n        };\r\n\r\n        log.auth('設置 token 和 user', { token: !!token, user, isDefaultPassword });\r\n        setToken(token);\r\n        setUser(user);\r\n        localStorage.setItem('token', token);\r\n        localStorage.setItem('userId', userId.toString());\r\n        localStorage.setItem('username', username);\r\n        localStorage.setItem('isDefaultPassword', isDefaultPassword.toString());\r\n        log.auth('登入成功');\r\n\r\n        return {\r\n          isDefaultPassword: isDefaultPassword || false,\r\n          userId,\r\n          username\r\n        };\r\n      } else {\r\n        throw new Error('API 回應格式錯誤：缺少 token');\r\n      }\r\n    } catch (error: any) {\r\n      log.error('AuthContext: 登入失敗', error);\r\n      // 錯誤已經在 api.ts 中被標準化，直接拋出即可\r\n      throw error;\r\n    } finally {\r\n      setIsLoading(false);\r\n    }\r\n  };\r\n\r\n  const logout = () => {\r\n    setUser(null);\r\n    setToken(null);\r\n    localStorage.removeItem('token');\r\n    localStorage.removeItem('userId');\r\n    localStorage.removeItem('username');\r\n    localStorage.removeItem('isDefaultPassword');\r\n\r\n    AuthApi.logout(user?.id as number).catch((error) => log.error('登出API調用失敗', error));\r\n  };\r\n\r\n  const refreshToken = async () => {\r\n    try {\r\n      const response = await AuthApi.refreshToken();\r\n      setToken(response.token);\r\n      localStorage.setItem('token', response.token);\r\n    } catch (error) {\r\n      log.error('Token refresh failed:', error);\r\n      logout();\r\n      throw error;\r\n    }\r\n  };\r\n\r\n  const value: AuthContextType = {\r\n    user,\r\n    token,\r\n    isAuthenticated: !!token && !!user,\r\n    isLoading,\r\n    login,\r\n    logout,\r\n    refreshToken,\r\n  };\r\n\r\n  return (\r\n    <AuthContext.Provider value={value}>\r\n      {children}\r\n    </AuthContext.Provider>\r\n  );\r\n};\r\n\r\nexport const useAuth = (): AuthContextType => {\r\n  const context = useContext(AuthContext);\r\n  if (context === undefined) {\r\n    throw new Error('useAuth must be used within an AuthProvider');\r\n  }\r\n  return context;\r\n};\r\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,aAAa,CAAaC,UAAU,CAAEC,SAAS,CAAEC,QAAQ,KAAQ,OAAO,CACxF,OAASC,OAAO,KAAQ,wBAAwB,CAChD,OAASC,GAAG,KAAQ,iBAAiB,CAAC,OAAAC,GAAA,IAAAC,IAAA,yBAyBtC,KAAM,CAAAC,WAAW,cAAGR,aAAa,CAA8BS,SAAS,CAAC,CAMzE,MAAO,MAAM,CAAAC,YAAyC,CAAGC,IAAA,EAAkB,IAAjB,CAAEC,QAAS,CAAC,CAAAD,IAAA,CACpE,KAAM,CAACE,IAAI,CAAEC,OAAO,CAAC,CAAGX,QAAQ,CAAc,IAAI,CAAC,CACnD,KAAM,CAACY,KAAK,CAAEC,QAAQ,CAAC,CAAGb,QAAQ,CAAgB,IAAI,CAAC,CACvD,KAAM,CAACc,SAAS,CAAEC,YAAY,CAAC,CAAGf,QAAQ,CAAC,IAAI,CAAC,CAEhDD,SAAS,CAAC,IAAM,CACd;AACA,KAAM,CAAAiB,WAAW,CAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC,CACjD,KAAM,CAAAC,YAAY,CAAGF,YAAY,CAACC,OAAO,CAAC,QAAQ,CAAC,CACnD,KAAM,CAAAE,cAAc,CAAGH,YAAY,CAACC,OAAO,CAAC,UAAU,CAAC,CAEvDhB,GAAG,CAACmB,IAAI,CAAC,OAAO,CAAE,CAChBC,QAAQ,CAAE,CAAC,CAACN,WAAW,CACvBO,SAAS,CAAE,CAAC,CAACJ,YAAY,CACzBK,WAAW,CAAE,CAAC,CAACJ,cACjB,CAAC,CAAC,CAEF,GAAIJ,WAAW,EAAIG,YAAY,EAAIC,cAAc,CAAE,CACjDP,QAAQ,CAACG,WAAW,CAAC,CACrBL,OAAO,CAAC,CACNc,EAAE,CAAEC,QAAQ,CAACP,YAAY,CAAC,CAC1BQ,QAAQ,CAAEP,cACZ,CAAC,CAAC,CACFlB,GAAG,CAACmB,IAAI,CAAC,uBAAuB,CAAC,CACnC,CAAC,IAAM,CACL;AACAJ,YAAY,CAACW,UAAU,CAAC,OAAO,CAAC,CAChCX,YAAY,CAACW,UAAU,CAAC,QAAQ,CAAC,CACjCX,YAAY,CAACW,UAAU,CAAC,UAAU,CAAC,CACnCX,YAAY,CAACW,UAAU,CAAC,mBAAmB,CAAC,CAC5C1B,GAAG,CAACmB,IAAI,CAAC,YAAY,CAAC,CACxB,CACAN,YAAY,CAAC,KAAK,CAAC,CACrB,CAAC,CAAE,EAAE,CAAC,CAEN,KAAM,CAAAc,KAAK,CAAG,KAAO,CAAAC,WAAmD,EAA2B,CACjG,GAAI,CACF5B,GAAG,CAACmB,IAAI,CAAC,QAAQ,CAAE,CAAEM,QAAQ,CAAEG,WAAW,CAACH,QAAS,CAAC,CAAC,CACtDZ,YAAY,CAAC,IAAI,CAAC,CAElB;AACA,KAAM,CAAAgB,GAAG,CAAG,KAAM,CAAA9B,OAAO,CAAC4B,KAAK,CAACC,WAAW,CAAC,CAE5C5B,GAAG,CAACmB,IAAI,CAAC,QAAQ,CAAEU,GAAG,CAAC,CAEvB;AACA,KAAM,CAAEnB,KAAK,CAAEe,QAAQ,CAAEK,MAAM,CAAEC,iBAAkB,CAAC,CAAGF,GAAG,CAC1D,GAAInB,KAAK,CAAE,CACT;AACA,KAAM,CAAAF,IAAI,CAAG,CACXe,EAAE,CAAEO,MAAM,CACVL,QAAQ,CAAEA,QACZ,CAAC,CAEDzB,GAAG,CAACmB,IAAI,CAAC,iBAAiB,CAAE,CAAET,KAAK,CAAE,CAAC,CAACA,KAAK,CAAEF,IAAI,CAAEuB,iBAAkB,CAAC,CAAC,CACxEpB,QAAQ,CAACD,KAAK,CAAC,CACfD,OAAO,CAACD,IAAI,CAAC,CACbO,YAAY,CAACiB,OAAO,CAAC,OAAO,CAAEtB,KAAK,CAAC,CACpCK,YAAY,CAACiB,OAAO,CAAC,QAAQ,CAAEF,MAAM,CAACG,QAAQ,CAAC,CAAC,CAAC,CACjDlB,YAAY,CAACiB,OAAO,CAAC,UAAU,CAAEP,QAAQ,CAAC,CAC1CV,YAAY,CAACiB,OAAO,CAAC,mBAAmB,CAAED,iBAAiB,CAACE,QAAQ,CAAC,CAAC,CAAC,CACvEjC,GAAG,CAACmB,IAAI,CAAC,MAAM,CAAC,CAEhB,MAAO,CACLY,iBAAiB,CAAEA,iBAAiB,EAAI,KAAK,CAC7CD,MAAM,CACNL,QACF,CAAC,CACH,CAAC,IAAM,CACL,KAAM,IAAI,CAAAS,KAAK,CAAC,qBAAqB,CAAC,CACxC,CACF,CAAE,MAAOC,KAAU,CAAE,CACnBnC,GAAG,CAACmC,KAAK,CAAC,mBAAmB,CAAEA,KAAK,CAAC,CACrC;AACA,KAAM,CAAAA,KAAK,CACb,CAAC,OAAS,CACRtB,YAAY,CAAC,KAAK,CAAC,CACrB,CACF,CAAC,CAED,KAAM,CAAAuB,MAAM,CAAGA,CAAA,GAAM,CACnB3B,OAAO,CAAC,IAAI,CAAC,CACbE,QAAQ,CAAC,IAAI,CAAC,CACdI,YAAY,CAACW,UAAU,CAAC,OAAO,CAAC,CAChCX,YAAY,CAACW,UAAU,CAAC,QAAQ,CAAC,CACjCX,YAAY,CAACW,UAAU,CAAC,UAAU,CAAC,CACnCX,YAAY,CAACW,UAAU,CAAC,mBAAmB,CAAC,CAE5C3B,OAAO,CAACqC,MAAM,CAAC5B,IAAI,SAAJA,IAAI,iBAAJA,IAAI,CAAEe,EAAY,CAAC,CAACc,KAAK,CAAEF,KAAK,EAAKnC,GAAG,CAACmC,KAAK,CAAC,WAAW,CAAEA,KAAK,CAAC,CAAC,CACpF,CAAC,CAED,KAAM,CAAAG,YAAY,CAAG,KAAAA,CAAA,GAAY,CAC/B,GAAI,CACF,KAAM,CAAAC,QAAQ,CAAG,KAAM,CAAAxC,OAAO,CAACuC,YAAY,CAAC,CAAC,CAC7C3B,QAAQ,CAAC4B,QAAQ,CAAC7B,KAAK,CAAC,CACxBK,YAAY,CAACiB,OAAO,CAAC,OAAO,CAAEO,QAAQ,CAAC7B,KAAK,CAAC,CAC/C,CAAE,MAAOyB,KAAK,CAAE,CACdnC,GAAG,CAACmC,KAAK,CAAC,uBAAuB,CAAEA,KAAK,CAAC,CACzCC,MAAM,CAAC,CAAC,CACR,KAAM,CAAAD,KAAK,CACb,CACF,CAAC,CAED,KAAM,CAAAK,KAAsB,CAAG,CAC7BhC,IAAI,CACJE,KAAK,CACL+B,eAAe,CAAE,CAAC,CAAC/B,KAAK,EAAI,CAAC,CAACF,IAAI,CAClCI,SAAS,CACTe,KAAK,CACLS,MAAM,CACNE,YACF,CAAC,CAED,mBACEpC,IAAA,CAACC,WAAW,CAACuC,QAAQ,EAACF,KAAK,CAAEA,KAAM,CAAAjC,QAAA,CAChCA,QAAQ,CACW,CAAC,CAE3B,CAAC,CAED,MAAO,MAAM,CAAAoC,OAAO,CAAGA,CAAA,GAAuB,CAC5C,KAAM,CAAAC,OAAO,CAAGhD,UAAU,CAACO,WAAW,CAAC,CACvC,GAAIyC,OAAO,GAAKxC,SAAS,CAAE,CACzB,KAAM,IAAI,CAAA8B,KAAK,CAAC,6CAA6C,CAAC,CAChE,CACA,MAAO,CAAAU,OAAO,CAChB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
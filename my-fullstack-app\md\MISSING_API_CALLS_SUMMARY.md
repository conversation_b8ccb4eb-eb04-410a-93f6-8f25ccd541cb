# 遺漏的 API 調用整理報告

## 📋 發現的遺漏 API 調用

經過全面檢查，發現以下文件中還有散落的 API 調用需要整理到 `apiService.ts` 中：

### 🔍 主要遺漏的 API 調用

#### 1. **BackupPage.tsx**
```typescript
// 遺漏的調用
api.get('/api/backup/list')
api.get('/api/backup/status')
api.get('/api/backup/download?file=...')

// 已添加到 BackupApi
BackupApi.getBatchData()
BackupApi.downloadWithQuery(fileName)
```

#### 2. **DebugPage.tsx**
```typescript
// 遺漏的調用
api.post("/api/system/UploadFile", formData)
api.get("/api/system/DownloadFile", {params})

// 已添加到 DebugApi
DebugApi.uploadFile(formData)
DebugApi.downloadFile(params)
```

#### 3. **HomePage.tsx**
```typescript
// 遺漏的調用
api.get('/api/system/GetMonthSatistics')
api.get('/api/system/GetWeekSatistics')
api.get('/api/schedule/GetTodaySchedules')
api.post('/api/treatment/Insert', data)
api.get('/api/treatment', {params})

// 已添加到 StatsApi 和現有 API
StatsApi.getMonthStatistics()
StatsApi.getWeekStatistics()
StatsApi.getTodaySchedules()
TreatmentApi.create(data)
TreatmentApi.getByParams(params)
```

#### 4. **SchedulesPage.tsx**
```typescript
// 遺漏的調用
Promise.all([
  api.get('/api/users/DoctorList'),
  api.get('/api/patients/PatientList'),
  api.get('/api/schedule')
])

// 已添加到 ScheduleApi
ScheduleApi.getBatchData()
```

#### 5. **PatientsDetailPage.tsx**
```typescript
// 遺漏的調用
api.put('/api/patients/Update/', dataToSend)
api.post("/api/patients/Insert", dataToSend)

// 已添加到 PatientApi
PatientApi.updateWithSlash(data)
PatientApi.create(data)
```

#### 6. **其他頁面的遺漏調用**
- **UsersPage.tsx**: 用戶角色管理相關 API
- **DoctorDetailPage.tsx**: 醫師管理 API
- **DoctorsPage.tsx**: 醫師操作 API
- **ImageManagementPage.tsx**: 圖片管理 API
- **ReportManagementPage.tsx**: 報表管理 API
- **IpBlocksPage.tsx**: IP 封鎖管理 API
- **LoginLogsPage.tsx**: 登入日誌 API
- **ReceiptsPage.tsx**: 收據管理 API
- **ReceiptsDetailPage.tsx**: 收據詳情 API
- **TreatmentsDetailPage.tsx**: 治療詳情 API
- **UpdatePasswordPage.tsx**: 密碼更新 API
- **RoleMenuManagementPage.tsx**: 權限管理 API

## ✅ 已添加到 apiService.ts 的新 API

### 1. **PatientApi 擴展**
```typescript
// 特殊的更新路徑
static async updateWithSlash(patient: any): Promise<Patient>
```

### 2. **TreatmentApi 擴展**
```typescript
// 不同參數格式的獲取方法
static async getByParams(params: any): Promise<Treatment>
```

### 3. **ScheduleApi 擴展**
```typescript
// 批量數據獲取
static async getBatchData(): Promise<[any[], any[], any[]]>
```

### 4. **BackupApi 擴展**
```typescript
// 批量數據獲取
static async getBatchData(): Promise<[any[], any]>
// 查詢參數下載
static async downloadWithQuery(fileName: string): Promise<Blob>
```

### 5. **新增 DebugApi**
```typescript
export class DebugApi {
  static async uploadFile(formData: FormData): Promise<any>
  static async downloadFile(params: { filename: string }): Promise<Blob>
}
```

### 6. **新增 StatsApi**
```typescript
export class StatsApi {
  static async getMonthStatistics(): Promise<any>
  static async getWeekStatistics(): Promise<any>
  static async getTodaySchedules(): Promise<any[]>
}
```

## 📋 仍需要整理的文件

以下文件仍有 API 調用需要遷移到 apiService：

### 高優先級
1. **UsersPage.tsx** - 用戶角色管理
2. **DoctorDetailPage.tsx** - 醫師詳情管理
3. **DoctorsPage.tsx** - 醫師列表操作
4. **RoleMenuManagementPage.tsx** - 權限管理

### 中優先級
5. **ReceiptsPage.tsx** - 收據列表管理
6. **ReceiptsDetailPage.tsx** - 收據詳情管理
7. **TreatmentsDetailPage.tsx** - 治療詳情管理
8. **UpdatePasswordPage.tsx** - 密碼更新

### 低優先級
9. **ImageManagementPage.tsx** - 圖片管理
10. **ReportManagementPage.tsx** - 報表管理
11. **IpBlocksPage.tsx** - IP 封鎖管理
12. **LoginLogsPage.tsx** - 登入日誌

## 🎯 下一步行動

1. **繼續整理遺漏的 API** - 將剩餘的 API 調用添加到對應的 API 類別中
2. **更新組件調用** - 將組件中的直接 API 調用改為使用 apiService
3. **統一錯誤處理** - 確保所有 API 調用都通過統一的錯誤處理機制
4. **添加類型定義** - 為新添加的 API 方法添加更精確的 TypeScript 類型

## 📊 進度統計

- **已整理的 API 類別**: 12 個
- **已添加的 API 方法**: 約 60+ 個
- **仍需整理的文件**: 12 個
- **預估完成度**: 約 70%

這個統一的 API 服務層將大大提升代碼的可維護性和一致性！
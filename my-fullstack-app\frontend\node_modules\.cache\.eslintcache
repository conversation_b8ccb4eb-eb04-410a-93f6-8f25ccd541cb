[{"C:\\Users\\<USER>\\Desktop\\demo-git\\demo-react\\my-fullstack-app\\frontend\\src\\index.tsx": "1", "C:\\Users\\<USER>\\Desktop\\demo-git\\demo-react\\my-fullstack-app\\frontend\\src\\reportWebVitals.ts": "2", "C:\\Users\\<USER>\\Desktop\\demo-git\\demo-react\\my-fullstack-app\\frontend\\src\\App.tsx": "3", "C:\\Users\\<USER>\\Desktop\\demo-git\\demo-react\\my-fullstack-app\\frontend\\src\\hooks\\useMenu.ts": "4", "C:\\Users\\<USER>\\Desktop\\demo-git\\demo-react\\my-fullstack-app\\frontend\\src\\routes\\componentMap.tsx": "5", "C:\\Users\\<USER>\\Desktop\\demo-git\\demo-react\\my-fullstack-app\\frontend\\src\\components\\Page\\LoginPage.tsx": "6", "C:\\Users\\<USER>\\Desktop\\demo-git\\demo-react\\my-fullstack-app\\frontend\\src\\components\\Layout\\Layout.tsx": "7", "C:\\Users\\<USER>\\Desktop\\demo-git\\demo-react\\my-fullstack-app\\frontend\\src\\components\\Page\\PatientsPage.tsx": "8", "C:\\Users\\<USER>\\Desktop\\demo-git\\demo-react\\my-fullstack-app\\frontend\\src\\components\\Page\\PatientsDetailPage.tsx": "9", "C:\\Users\\<USER>\\Desktop\\demo-git\\demo-react\\my-fullstack-app\\frontend\\src\\components\\Page\\DebugPage.tsx": "10", "C:\\Users\\<USER>\\Desktop\\demo-git\\demo-react\\my-fullstack-app\\frontend\\src\\hooks\\usePatient.ts": "11", "C:\\Users\\<USER>\\Desktop\\demo-git\\demo-react\\my-fullstack-app\\frontend\\src\\hooks\\useDataType.ts": "12", "C:\\Users\\<USER>\\Desktop\\demo-git\\demo-react\\my-fullstack-app\\frontend\\src\\components\\Page\\TreatmentsPage.tsx": "13", "C:\\Users\\<USER>\\Desktop\\demo-git\\demo-react\\my-fullstack-app\\frontend\\src\\hooks\\useTreatment.ts": "14", "C:\\Users\\<USER>\\Desktop\\demo-git\\demo-react\\my-fullstack-app\\frontend\\src\\hooks\\useUser.ts": "15", "C:\\Users\\<USER>\\Desktop\\demo-git\\demo-react\\my-fullstack-app\\frontend\\src\\components\\Page\\TreatmentsDetailPage.tsx": "16", "C:\\Users\\<USER>\\Desktop\\demo-git\\demo-react\\my-fullstack-app\\frontend\\src\\services\\imagepath.js": "17", "C:\\Users\\<USER>\\Desktop\\demo-git\\demo-react\\my-fullstack-app\\frontend\\src\\components\\Page\\ReceiptsDetailPage.tsx": "18", "C:\\Users\\<USER>\\Desktop\\demo-git\\demo-react\\my-fullstack-app\\frontend\\src\\services\\signalr.ts": "19", "C:\\Users\\<USER>\\Desktop\\demo-git\\demo-react\\my-fullstack-app\\frontend\\src\\components\\Page\\ReceiptsPage.tsx": "20", "C:\\Users\\<USER>\\Desktop\\demo-git\\demo-react\\my-fullstack-app\\frontend\\src\\hooks\\useReceipt.ts": "21", "C:\\Users\\<USER>\\Desktop\\demo-git\\demo-react\\my-fullstack-app\\frontend\\src\\constants\\routes.ts": "22", "C:\\Users\\<USER>\\Desktop\\demo-git\\demo-react\\my-fullstack-app\\frontend\\src\\components\\Common\\BreadcrumbNav.tsx": "23", "C:\\Users\\<USER>\\Desktop\\demo-git\\demo-react\\my-fullstack-app\\frontend\\src\\components\\Page\\ImageManagementPage.tsx": "24", "C:\\Users\\<USER>\\Desktop\\demo-git\\demo-react\\my-fullstack-app\\frontend\\src\\components\\Page\\ReportManagementPage.tsx": "25", "C:\\Users\\<USER>\\Desktop\\demo-git\\demo-react\\my-fullstack-app\\frontend\\src\\components\\Page\\HomePage.tsx": "26", "C:\\Users\\<USER>\\Desktop\\demo-git\\demo-react\\my-fullstack-app\\frontend\\src\\components\\Page\\SchedulesPage.tsx": "27", "C:\\Users\\<USER>\\Desktop\\demo-git\\demo-react\\my-fullstack-app\\frontend\\src\\components\\Page\\DoctorsPage.tsx": "28", "C:\\Users\\<USER>\\Desktop\\demo-git\\demo-react\\my-fullstack-app\\frontend\\src\\components\\Page\\DoctorDetailPage.tsx": "29", "C:\\Users\\<USER>\\Desktop\\demo-git\\demo-react\\my-fullstack-app\\frontend\\src\\services\\api.ts": "30", "C:\\Users\\<USER>\\Desktop\\demo-git\\demo-react\\my-fullstack-app\\frontend\\src\\components\\Page\\UsersPage.tsx": "31", "C:\\Users\\<USER>\\Desktop\\demo-git\\demo-react\\my-fullstack-app\\frontend\\src\\config\\env.ts": "32", "C:\\Users\\<USER>\\Desktop\\demo-git\\demo-react\\my-fullstack-app\\frontend\\src\\hooks\\useErrorHandler.ts": "33", "C:\\Users\\<USER>\\Desktop\\demo-git\\demo-react\\my-fullstack-app\\frontend\\src\\services\\apiService.ts": "34", "C:\\Users\\<USER>\\Desktop\\demo-git\\demo-react\\my-fullstack-app\\frontend\\src\\components\\Page\\BackupPage.tsx": "35", "C:\\Users\\<USER>\\Desktop\\demo-git\\demo-react\\my-fullstack-app\\frontend\\src\\hooks\\useApiData.ts": "36", "C:\\Users\\<USER>\\Desktop\\demo-git\\demo-react\\my-fullstack-app\\frontend\\src\\components\\Auth\\PasswordCheckRoute.tsx": "37", "C:\\Users\\<USER>\\Desktop\\demo-git\\demo-react\\my-fullstack-app\\frontend\\src\\components\\Page\\ErrorPage.tsx": "38", "C:\\Users\\<USER>\\Desktop\\demo-git\\demo-react\\my-fullstack-app\\frontend\\src\\components\\Common\\ErrorBoundary.tsx": "39", "C:\\Users\\<USER>\\Desktop\\demo-git\\demo-react\\my-fullstack-app\\frontend\\src\\components\\Common\\LoadingSpinner.tsx": "40", "C:\\Users\\<USER>\\Desktop\\demo-git\\demo-react\\my-fullstack-app\\frontend\\src\\components\\Common\\ErrorFallback.tsx": "41", "C:\\Users\\<USER>\\Desktop\\demo-git\\demo-react\\my-fullstack-app\\frontend\\src\\components\\Auth\\ProtectedRoute.tsx": "42", "C:\\Users\\<USER>\\Desktop\\demo-git\\demo-react\\my-fullstack-app\\frontend\\src\\contexts\\AuthContext.tsx": "43", "C:\\Users\\<USER>\\Desktop\\demo-git\\demo-react\\my-fullstack-app\\frontend\\src\\utils\\logger.ts": "44", "C:\\Users\\<USER>\\Desktop\\demo-git\\demo-react\\my-fullstack-app\\frontend\\src\\components\\Page\\UpdatePasswordPage.tsx": "45", "C:\\Users\\<USER>\\Desktop\\demo-git\\demo-react\\my-fullstack-app\\frontend\\src\\components\\Page\\IpBlocksPage.tsx": "46", "C:\\Users\\<USER>\\Desktop\\demo-git\\demo-react\\my-fullstack-app\\frontend\\src\\components\\Page\\LoginLogsPage.tsx": "47", "C:\\Users\\<USER>\\Desktop\\demo-git\\demo-react\\my-fullstack-app\\frontend\\src\\utils\\dateUtils.ts": "48"}, {"size": 801, "mtime": 1752734419511, "results": "49", "hashOfConfig": "50"}, {"size": 440, "mtime": 1750727188691, "results": "51", "hashOfConfig": "50"}, {"size": 2118, "mtime": 1752734419397, "results": "52", "hashOfConfig": "50"}, {"size": 1255, "mtime": 1752734419494, "results": "53", "hashOfConfig": "50"}, {"size": 2948, "mtime": 1753329394930, "results": "54", "hashOfConfig": "55"}, {"size": 4223, "mtime": 1753179356007, "results": "56", "hashOfConfig": "50"}, {"size": 3364, "mtime": 1753329394926, "results": "57", "hashOfConfig": "55"}, {"size": 10832, "mtime": 1753317902266, "results": "58", "hashOfConfig": "50"}, {"size": 9472, "mtime": 1753317902265, "results": "59", "hashOfConfig": "50"}, {"size": 4223, "mtime": 1752734419431, "results": "60", "hashOfConfig": "50"}, {"size": 1184, "mtime": 1752734419495, "results": "61", "hashOfConfig": "50"}, {"size": 851, "mtime": 1752734419489, "results": "62", "hashOfConfig": "50"}, {"size": 14722, "mtime": 1753317902271, "results": "63", "hashOfConfig": "50"}, {"size": 1528, "mtime": 1752734419504, "results": "64", "hashOfConfig": "50"}, {"size": 1121, "mtime": 1752734419506, "results": "65", "hashOfConfig": "50"}, {"size": 27682, "mtime": 1753317902270, "results": "66", "hashOfConfig": "50"}, {"size": 94, "mtime": 1751602091872, "results": "67", "hashOfConfig": "50"}, {"size": 11297, "mtime": 1753317902267, "results": "68", "hashOfConfig": "50"}, {"size": 233, "mtime": 1751602091883, "results": "69", "hashOfConfig": "50"}, {"size": 20079, "mtime": 1753259129581, "results": "70", "hashOfConfig": "50"}, {"size": 1293, "mtime": 1753179356295, "results": "71", "hashOfConfig": "50"}, {"size": 4467, "mtime": 1753329394929, "results": "72", "hashOfConfig": "55"}, {"size": 4405, "mtime": 1753179355987, "results": "73", "hashOfConfig": "50"}, {"size": 13469, "mtime": 1753317902262, "results": "74", "hashOfConfig": "50"}, {"size": 11737, "mtime": 1753179356029, "results": "75", "hashOfConfig": "50"}, {"size": 15815, "mtime": 1753317902260, "results": "76", "hashOfConfig": "50"}, {"size": 24447, "mtime": 1753317902269, "results": "77", "hashOfConfig": "50"}, {"size": 8999, "mtime": 1753317902259, "results": "78", "hashOfConfig": "50"}, {"size": 9824, "mtime": 1753317902257, "results": "79", "hashOfConfig": "50"}, {"size": 3926, "mtime": 1753236913773, "results": "80", "hashOfConfig": "50"}, {"size": 13813, "mtime": 1753329394927, "results": "81", "hashOfConfig": "55"}, {"size": 1361, "mtime": 1752734419477, "results": "82", "hashOfConfig": "50"}, {"size": 3180, "mtime": 1752734419492, "results": "83", "hashOfConfig": "50"}, {"size": 4808, "mtime": 1753329394931, "results": "84", "hashOfConfig": "55"}, {"size": 10032, "mtime": 1753179355989, "results": "85", "hashOfConfig": "50"}, {"size": 3661, "mtime": 1752734419486, "results": "86", "hashOfConfig": "50"}, {"size": 2515, "mtime": 1752734419400, "results": "87", "hashOfConfig": "50"}, {"size": 4039, "mtime": 1753179355997, "results": "88", "hashOfConfig": "50"}, {"size": 3477, "mtime": 1752734419405, "results": "89", "hashOfConfig": "50"}, {"size": 1059, "mtime": 1752734419414, "results": "90", "hashOfConfig": "50"}, {"size": 3679, "mtime": 1752734419410, "results": "91", "hashOfConfig": "50"}, {"size": 1724, "mtime": 1752734419402, "results": "92", "hashOfConfig": "50"}, {"size": 4655, "mtime": 1753179356291, "results": "93", "hashOfConfig": "50"}, {"size": 4261, "mtime": 1752734419533, "results": "94", "hashOfConfig": "50"}, {"size": 7372, "mtime": 1753317902272, "results": "95", "hashOfConfig": "50"}, {"size": 12119, "mtime": 1753317902263, "results": "96", "hashOfConfig": "50"}, {"size": 8720, "mtime": 1753317902264, "results": "97", "hashOfConfig": "50"}, {"size": 910, "mtime": 1753179356307, "results": "98", "hashOfConfig": "50"}, {"filePath": "99", "messages": "100", "suppressedMessages": "101", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "ewh0yd", {"filePath": "102", "messages": "103", "suppressedMessages": "104", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "105", "messages": "106", "suppressedMessages": "107", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "108", "messages": "109", "suppressedMessages": "110", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "111", "messages": "112", "suppressedMessages": "113", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "107qa9v", {"filePath": "114", "messages": "115", "suppressedMessages": "116", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "117", "messages": "118", "suppressedMessages": "119", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "120", "messages": "121", "suppressedMessages": "122", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "123", "messages": "124", "suppressedMessages": "125", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "126", "messages": "127", "suppressedMessages": "128", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "129", "messages": "130", "suppressedMessages": "131", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "132", "messages": "133", "suppressedMessages": "134", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "135", "messages": "136", "suppressedMessages": "137", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "138", "messages": "139", "suppressedMessages": "140", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "141", "messages": "142", "suppressedMessages": "143", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "144", "messages": "145", "suppressedMessages": "146", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "147", "messages": "148", "suppressedMessages": "149", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "150", "messages": "151", "suppressedMessages": "152", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "153", "messages": "154", "suppressedMessages": "155", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "156", "messages": "157", "suppressedMessages": "158", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "159", "messages": "160", "suppressedMessages": "161", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "162", "messages": "163", "suppressedMessages": "164", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "165", "messages": "166", "suppressedMessages": "167", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "168", "messages": "169", "suppressedMessages": "170", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "171", "messages": "172", "suppressedMessages": "173", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "174", "messages": "175", "suppressedMessages": "176", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "177", "messages": "178", "suppressedMessages": "179", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 14, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "180", "messages": "181", "suppressedMessages": "182", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "183", "messages": "184", "suppressedMessages": "185", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "186", "messages": "187", "suppressedMessages": "188", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "189", "messages": "190", "suppressedMessages": "191", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "192", "messages": "193", "suppressedMessages": "194", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "195", "messages": "196", "suppressedMessages": "197", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "198", "messages": "199", "suppressedMessages": "200", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "201", "messages": "202", "suppressedMessages": "203", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "204", "messages": "205", "suppressedMessages": "206", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "207", "messages": "208", "suppressedMessages": "209", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "210", "messages": "211", "suppressedMessages": "212", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "213", "messages": "214", "suppressedMessages": "215", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "216", "messages": "217", "suppressedMessages": "218", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "219", "messages": "220", "suppressedMessages": "221", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "222", "messages": "223", "suppressedMessages": "224", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "225", "messages": "226", "suppressedMessages": "227", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "228", "messages": "229", "suppressedMessages": "230", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 9, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "231", "messages": "232", "suppressedMessages": "233", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "234", "messages": "235", "suppressedMessages": "236", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "237", "messages": "238", "suppressedMessages": "239", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "240", "messages": "241", "suppressedMessages": "242", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "C:\\Users\\<USER>\\Desktop\\demo-git\\demo-react\\my-fullstack-app\\frontend\\src\\index.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\demo-git\\demo-react\\my-fullstack-app\\frontend\\src\\reportWebVitals.ts", [], [], "C:\\Users\\<USER>\\Desktop\\demo-git\\demo-react\\my-fullstack-app\\frontend\\src\\App.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\demo-git\\demo-react\\my-fullstack-app\\frontend\\src\\hooks\\useMenu.ts", ["243"], [], "C:\\Users\\<USER>\\Desktop\\demo-git\\demo-react\\my-fullstack-app\\frontend\\src\\routes\\componentMap.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\demo-git\\demo-react\\my-fullstack-app\\frontend\\src\\components\\Page\\LoginPage.tsx", ["244", "245", "246", "247", "248", "249", "250"], [], "C:\\Users\\<USER>\\Desktop\\demo-git\\demo-react\\my-fullstack-app\\frontend\\src\\components\\Layout\\Layout.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\demo-git\\demo-react\\my-fullstack-app\\frontend\\src\\components\\Page\\PatientsPage.tsx", ["251"], [], "C:\\Users\\<USER>\\Desktop\\demo-git\\demo-react\\my-fullstack-app\\frontend\\src\\components\\Page\\PatientsDetailPage.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\demo-git\\demo-react\\my-fullstack-app\\frontend\\src\\components\\Page\\DebugPage.tsx", ["252"], [], "C:\\Users\\<USER>\\Desktop\\demo-git\\demo-react\\my-fullstack-app\\frontend\\src\\hooks\\usePatient.ts", [], [], "C:\\Users\\<USER>\\Desktop\\demo-git\\demo-react\\my-fullstack-app\\frontend\\src\\hooks\\useDataType.ts", ["253"], [], "C:\\Users\\<USER>\\Desktop\\demo-git\\demo-react\\my-fullstack-app\\frontend\\src\\components\\Page\\TreatmentsPage.tsx", ["254", "255", "256"], [], "C:\\Users\\<USER>\\Desktop\\demo-git\\demo-react\\my-fullstack-app\\frontend\\src\\hooks\\useTreatment.ts", [], [], "C:\\Users\\<USER>\\Desktop\\demo-git\\demo-react\\my-fullstack-app\\frontend\\src\\hooks\\useUser.ts", ["257"], [], "C:\\Users\\<USER>\\Desktop\\demo-git\\demo-react\\my-fullstack-app\\frontend\\src\\components\\Page\\TreatmentsDetailPage.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\demo-git\\demo-react\\my-fullstack-app\\frontend\\src\\services\\imagepath.js", [], [], "C:\\Users\\<USER>\\Desktop\\demo-git\\demo-react\\my-fullstack-app\\frontend\\src\\components\\Page\\ReceiptsDetailPage.tsx", ["258", "259", "260", "261", "262", "263", "264"], [], "C:\\Users\\<USER>\\Desktop\\demo-git\\demo-react\\my-fullstack-app\\frontend\\src\\services\\signalr.ts", [], [], "C:\\Users\\<USER>\\Desktop\\demo-git\\demo-react\\my-fullstack-app\\frontend\\src\\components\\Page\\ReceiptsPage.tsx", ["265", "266"], [], "C:\\Users\\<USER>\\Desktop\\demo-git\\demo-react\\my-fullstack-app\\frontend\\src\\hooks\\useReceipt.ts", ["267"], [], "C:\\Users\\<USER>\\Desktop\\demo-git\\demo-react\\my-fullstack-app\\frontend\\src\\constants\\routes.ts", [], [], "C:\\Users\\<USER>\\Desktop\\demo-git\\demo-react\\my-fullstack-app\\frontend\\src\\components\\Common\\BreadcrumbNav.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\demo-git\\demo-react\\my-fullstack-app\\frontend\\src\\components\\Page\\ImageManagementPage.tsx", ["268", "269"], [], "C:\\Users\\<USER>\\Desktop\\demo-git\\demo-react\\my-fullstack-app\\frontend\\src\\components\\Page\\ReportManagementPage.tsx", ["270", "271"], [], "C:\\Users\\<USER>\\Desktop\\demo-git\\demo-react\\my-fullstack-app\\frontend\\src\\components\\Page\\HomePage.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\demo-git\\demo-react\\my-fullstack-app\\frontend\\src\\components\\Page\\SchedulesPage.tsx", ["272", "273", "274", "275", "276", "277", "278", "279", "280", "281", "282", "283", "284", "285"], [], "C:\\Users\\<USER>\\Desktop\\demo-git\\demo-react\\my-fullstack-app\\frontend\\src\\components\\Page\\DoctorsPage.tsx", ["286"], [], "C:\\Users\\<USER>\\Desktop\\demo-git\\demo-react\\my-fullstack-app\\frontend\\src\\components\\Page\\DoctorDetailPage.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\demo-git\\demo-react\\my-fullstack-app\\frontend\\src\\services\\api.ts", [], [], "C:\\Users\\<USER>\\Desktop\\demo-git\\demo-react\\my-fullstack-app\\frontend\\src\\components\\Page\\UsersPage.tsx", [], ["287", "288"], "C:\\Users\\<USER>\\Desktop\\demo-git\\demo-react\\my-fullstack-app\\frontend\\src\\config\\env.ts", [], [], "C:\\Users\\<USER>\\Desktop\\demo-git\\demo-react\\my-fullstack-app\\frontend\\src\\hooks\\useErrorHandler.ts", [], [], "C:\\Users\\<USER>\\Desktop\\demo-git\\demo-react\\my-fullstack-app\\frontend\\src\\services\\apiService.ts", [], [], "C:\\Users\\<USER>\\Desktop\\demo-git\\demo-react\\my-fullstack-app\\frontend\\src\\components\\Page\\BackupPage.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\demo-git\\demo-react\\my-fullstack-app\\frontend\\src\\hooks\\useApiData.ts", ["289"], [], "C:\\Users\\<USER>\\Desktop\\demo-git\\demo-react\\my-fullstack-app\\frontend\\src\\components\\Auth\\PasswordCheckRoute.tsx", ["290", "291", "292", "293", "294"], [], "C:\\Users\\<USER>\\Desktop\\demo-git\\demo-react\\my-fullstack-app\\frontend\\src\\components\\Page\\ErrorPage.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\demo-git\\demo-react\\my-fullstack-app\\frontend\\src\\components\\Common\\ErrorBoundary.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\demo-git\\demo-react\\my-fullstack-app\\frontend\\src\\components\\Common\\LoadingSpinner.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\demo-git\\demo-react\\my-fullstack-app\\frontend\\src\\components\\Common\\ErrorFallback.tsx", ["295"], [], "C:\\Users\\<USER>\\Desktop\\demo-git\\demo-react\\my-fullstack-app\\frontend\\src\\components\\Auth\\ProtectedRoute.tsx", ["296", "297", "298"], [], "C:\\Users\\<USER>\\Desktop\\demo-git\\demo-react\\my-fullstack-app\\frontend\\src\\contexts\\AuthContext.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\demo-git\\demo-react\\my-fullstack-app\\frontend\\src\\utils\\logger.ts", ["299", "300", "301", "302", "303", "304", "305", "306", "307"], [], "C:\\Users\\<USER>\\Desktop\\demo-git\\demo-react\\my-fullstack-app\\frontend\\src\\components\\Page\\UpdatePasswordPage.tsx", ["308", "309"], [], "C:\\Users\\<USER>\\Desktop\\demo-git\\demo-react\\my-fullstack-app\\frontend\\src\\components\\Page\\IpBlocksPage.tsx", ["310", "311"], [], "C:\\Users\\<USER>\\Desktop\\demo-git\\demo-react\\my-fullstack-app\\frontend\\src\\components\\Page\\LoginLogsPage.tsx", ["312", "313"], [], "C:\\Users\\<USER>\\Desktop\\demo-git\\demo-react\\my-fullstack-app\\frontend\\src\\utils\\dateUtils.ts", ["314"], [], {"ruleId": "315", "severity": 1, "message": "316", "line": 33, "column": 7, "nodeType": "317", "messageId": "318", "endLine": 33, "endColumn": 20, "suggestions": "319"}, {"ruleId": "315", "severity": 1, "message": "316", "line": 20, "column": 5, "nodeType": "317", "messageId": "318", "endLine": 20, "endColumn": 16, "suggestions": "320"}, {"ruleId": "315", "severity": 1, "message": "316", "line": 27, "column": 7, "nodeType": "317", "messageId": "318", "endLine": 27, "endColumn": 18, "suggestions": "321"}, {"ruleId": "315", "severity": 1, "message": "316", "line": 40, "column": 7, "nodeType": "317", "messageId": "318", "endLine": 40, "endColumn": 18, "suggestions": "322"}, {"ruleId": "315", "severity": 1, "message": "316", "line": 43, "column": 7, "nodeType": "317", "messageId": "318", "endLine": 43, "endColumn": 18, "suggestions": "323"}, {"ruleId": "315", "severity": 1, "message": "316", "line": 47, "column": 9, "nodeType": "317", "messageId": "318", "endLine": 47, "endColumn": 20, "suggestions": "324"}, {"ruleId": "315", "severity": 1, "message": "316", "line": 53, "column": 9, "nodeType": "317", "messageId": "318", "endLine": 53, "endColumn": 20, "suggestions": "325"}, {"ruleId": "315", "severity": 1, "message": "316", "line": 60, "column": 7, "nodeType": "317", "messageId": "318", "endLine": 60, "endColumn": 20, "suggestions": "326"}, {"ruleId": "327", "severity": 1, "message": "328", "line": 51, "column": 8, "nodeType": "329", "endLine": 51, "endColumn": 17, "suggestions": "330"}, {"ruleId": "315", "severity": 1, "message": "316", "line": 29, "column": 13, "nodeType": "317", "messageId": "318", "endLine": 29, "endColumn": 24, "suggestions": "331"}, {"ruleId": "315", "severity": 1, "message": "316", "line": 28, "column": 21, "nodeType": "317", "messageId": "318", "endLine": 28, "endColumn": 34}, {"ruleId": "315", "severity": 1, "message": "316", "line": 93, "column": 17, "nodeType": "317", "messageId": "318", "endLine": 93, "endColumn": 28, "suggestions": "332"}, {"ruleId": "315", "severity": 1, "message": "316", "line": 96, "column": 17, "nodeType": "317", "messageId": "318", "endLine": 96, "endColumn": 28, "suggestions": "333"}, {"ruleId": "315", "severity": 1, "message": "316", "line": 106, "column": 17, "nodeType": "317", "messageId": "318", "endLine": 106, "endColumn": 30, "suggestions": "334"}, {"ruleId": "315", "severity": 1, "message": "316", "line": 33, "column": 21, "nodeType": "317", "messageId": "318", "endLine": 33, "endColumn": 34}, {"ruleId": "315", "severity": 1, "message": "316", "line": 39, "column": 3, "nodeType": "317", "messageId": "318", "endLine": 39, "endColumn": 14, "suggestions": "335"}, {"ruleId": "315", "severity": 1, "message": "316", "line": 51, "column": 7, "nodeType": "317", "messageId": "318", "endLine": 51, "endColumn": 18, "suggestions": "336"}, {"ruleId": "315", "severity": 1, "message": "316", "line": 54, "column": 19, "nodeType": "317", "messageId": "318", "endLine": 54, "endColumn": 32}, {"ruleId": "327", "severity": 1, "message": "337", "line": 68, "column": 6, "nodeType": "329", "endLine": 68, "endColumn": 8, "suggestions": "338"}, {"ruleId": "339", "severity": 1, "message": "340", "line": 156, "column": 24, "nodeType": "341", "messageId": "318", "endLine": 156, "endColumn": 26}, {"ruleId": "339", "severity": 1, "message": "340", "line": 166, "column": 24, "nodeType": "341", "messageId": "318", "endLine": 166, "endColumn": 26}, {"ruleId": "339", "severity": 1, "message": "340", "line": 194, "column": 24, "nodeType": "341", "messageId": "318", "endLine": 194, "endColumn": 26}, {"ruleId": "315", "severity": 1, "message": "316", "line": 79, "column": 9, "nodeType": "317", "messageId": "318", "endLine": 79, "endColumn": 20, "suggestions": "342"}, {"ruleId": "315", "severity": 1, "message": "316", "line": 82, "column": 23, "nodeType": "317", "messageId": "318", "endLine": 82, "endColumn": 36}, {"ruleId": "315", "severity": 1, "message": "316", "line": 38, "column": 25, "nodeType": "317", "messageId": "318", "endLine": 38, "endColumn": 38}, {"ruleId": "315", "severity": 1, "message": "316", "line": 182, "column": 7, "nodeType": "317", "messageId": "318", "endLine": 182, "endColumn": 20, "suggestions": "343"}, {"ruleId": "327", "severity": 1, "message": "344", "line": 290, "column": 6, "nodeType": "329", "endLine": 290, "endColumn": 8, "suggestions": "345"}, {"ruleId": "315", "severity": 1, "message": "316", "line": 197, "column": 7, "nodeType": "317", "messageId": "318", "endLine": 197, "endColumn": 20, "suggestions": "346"}, {"ruleId": "327", "severity": 1, "message": "347", "line": 285, "column": 6, "nodeType": "329", "endLine": 285, "endColumn": 8, "suggestions": "348"}, {"ruleId": "315", "severity": 1, "message": "316", "line": 88, "column": 9, "nodeType": "317", "messageId": "318", "endLine": 88, "endColumn": 20, "suggestions": "349"}, {"ruleId": "315", "severity": 1, "message": "316", "line": 97, "column": 9, "nodeType": "317", "messageId": "318", "endLine": 97, "endColumn": 20, "suggestions": "350"}, {"ruleId": "315", "severity": 1, "message": "316", "line": 98, "column": 9, "nodeType": "317", "messageId": "318", "endLine": 98, "endColumn": 20, "suggestions": "351"}, {"ruleId": "315", "severity": 1, "message": "316", "line": 99, "column": 9, "nodeType": "317", "messageId": "318", "endLine": 99, "endColumn": 20, "suggestions": "352"}, {"ruleId": "315", "severity": 1, "message": "316", "line": 122, "column": 9, "nodeType": "317", "messageId": "318", "endLine": 122, "endColumn": 20, "suggestions": "353"}, {"ruleId": "315", "severity": 1, "message": "316", "line": 131, "column": 9, "nodeType": "317", "messageId": "318", "endLine": 131, "endColumn": 22, "suggestions": "354"}, {"ruleId": "315", "severity": 1, "message": "316", "line": 173, "column": 7, "nodeType": "317", "messageId": "318", "endLine": 173, "endColumn": 20, "suggestions": "355"}, {"ruleId": "315", "severity": 1, "message": "316", "line": 269, "column": 7, "nodeType": "317", "messageId": "318", "endLine": 269, "endColumn": 20, "suggestions": "356"}, {"ruleId": "315", "severity": 1, "message": "316", "line": 344, "column": 11, "nodeType": "317", "messageId": "318", "endLine": 344, "endColumn": 24, "suggestions": "357"}, {"ruleId": "315", "severity": 1, "message": "316", "line": 365, "column": 9, "nodeType": "317", "messageId": "318", "endLine": 365, "endColumn": 20, "suggestions": "358"}, {"ruleId": "315", "severity": 1, "message": "316", "line": 373, "column": 9, "nodeType": "317", "messageId": "318", "endLine": 373, "endColumn": 20, "suggestions": "359"}, {"ruleId": "315", "severity": 1, "message": "316", "line": 379, "column": 9, "nodeType": "317", "messageId": "318", "endLine": 379, "endColumn": 20, "suggestions": "360"}, {"ruleId": "315", "severity": 1, "message": "316", "line": 386, "column": 7, "nodeType": "317", "messageId": "318", "endLine": 386, "endColumn": 18, "suggestions": "361"}, {"ruleId": "315", "severity": 1, "message": "316", "line": 404, "column": 7, "nodeType": "317", "messageId": "318", "endLine": 404, "endColumn": 20, "suggestions": "362"}, {"ruleId": "315", "severity": 1, "message": "316", "line": 43, "column": 5, "nodeType": "317", "messageId": "318", "endLine": 43, "endColumn": 16, "suggestions": "363"}, {"ruleId": "327", "severity": 1, "message": "364", "line": 198, "column": 6, "nodeType": "329", "endLine": 198, "endColumn": 8, "suggestions": "365", "suppressions": "366"}, {"ruleId": "327", "severity": 1, "message": "367", "line": 206, "column": 6, "nodeType": "329", "endLine": 206, "endColumn": 26, "suggestions": "368", "suppressions": "369"}, {"ruleId": "327", "severity": 1, "message": "370", "line": 69, "column": 18, "nodeType": "371", "endLine": 69, "endColumn": 33}, {"ruleId": "315", "severity": 1, "message": "316", "line": 24, "column": 11, "nodeType": "317", "messageId": "318", "endLine": 24, "endColumn": 22, "suggestions": "372"}, {"ruleId": "315", "severity": 1, "message": "316", "line": 32, "column": 11, "nodeType": "317", "messageId": "318", "endLine": 32, "endColumn": 22, "suggestions": "373"}, {"ruleId": "315", "severity": 1, "message": "316", "line": 43, "column": 11, "nodeType": "317", "messageId": "318", "endLine": 43, "endColumn": 22, "suggestions": "374"}, {"ruleId": "315", "severity": 1, "message": "316", "line": 49, "column": 9, "nodeType": "317", "messageId": "318", "endLine": 49, "endColumn": 20, "suggestions": "375"}, {"ruleId": "315", "severity": 1, "message": "316", "line": 53, "column": 9, "nodeType": "317", "messageId": "318", "endLine": 53, "endColumn": 22, "suggestions": "376"}, {"ruleId": "315", "severity": 1, "message": "316", "line": 40, "column": 5, "nodeType": "317", "messageId": "318", "endLine": 40, "endColumn": 18, "suggestions": "377"}, {"ruleId": "315", "severity": 1, "message": "316", "line": 19, "column": 3, "nodeType": "317", "messageId": "318", "endLine": 19, "endColumn": 14, "suggestions": "378"}, {"ruleId": "315", "severity": 1, "message": "316", "line": 28, "column": 5, "nodeType": "317", "messageId": "318", "endLine": 28, "endColumn": 16, "suggestions": "379"}, {"ruleId": "315", "severity": 1, "message": "316", "line": 35, "column": 5, "nodeType": "317", "messageId": "318", "endLine": 35, "endColumn": 16, "suggestions": "380"}, {"ruleId": "381", "severity": 1, "message": "382", "line": 6, "column": 3, "nodeType": "383", "messageId": "384", "endLine": 6, "endColumn": 8}, {"ruleId": "381", "severity": 1, "message": "385", "line": 7, "column": 3, "nodeType": "383", "messageId": "384", "endLine": 7, "endColumn": 7}, {"ruleId": "381", "severity": 1, "message": "386", "line": 8, "column": 3, "nodeType": "383", "messageId": "384", "endLine": 8, "endColumn": 7}, {"ruleId": "381", "severity": 1, "message": "387", "line": 9, "column": 3, "nodeType": "383", "messageId": "384", "endLine": 9, "endColumn": 8}, {"ruleId": "381", "severity": 1, "message": "388", "line": 10, "column": 3, "nodeType": "383", "messageId": "384", "endLine": 10, "endColumn": 7}, {"ruleId": "315", "severity": 1, "message": "316", "line": 65, "column": 11, "nodeType": "317", "messageId": "318", "endLine": 65, "endColumn": 22, "suggestions": "389"}, {"ruleId": "315", "severity": 1, "message": "316", "line": 68, "column": 11, "nodeType": "317", "messageId": "318", "endLine": 68, "endColumn": 23, "suggestions": "390"}, {"ruleId": "315", "severity": 1, "message": "316", "line": 71, "column": 11, "nodeType": "317", "messageId": "318", "endLine": 71, "endColumn": 23, "suggestions": "391"}, {"ruleId": "315", "severity": 1, "message": "316", "line": 74, "column": 11, "nodeType": "317", "messageId": "318", "endLine": 74, "endColumn": 24, "suggestions": "392"}, {"ruleId": "315", "severity": 1, "message": "316", "line": 122, "column": 5, "nodeType": "317", "messageId": "318", "endLine": 122, "endColumn": 16, "suggestions": "393"}, {"ruleId": "315", "severity": 1, "message": "316", "line": 127, "column": 7, "nodeType": "317", "messageId": "318", "endLine": 127, "endColumn": 18, "suggestions": "394"}, {"ruleId": "315", "severity": 1, "message": "316", "line": 176, "column": 7, "nodeType": "317", "messageId": "318", "endLine": 176, "endColumn": 20, "suggestions": "395"}, {"ruleId": "327", "severity": 1, "message": "396", "line": 253, "column": 6, "nodeType": "329", "endLine": 253, "endColumn": 8, "suggestions": "397"}, {"ruleId": "315", "severity": 1, "message": "316", "line": 122, "column": 7, "nodeType": "317", "messageId": "318", "endLine": 122, "endColumn": 20, "suggestions": "398"}, {"ruleId": "327", "severity": 1, "message": "399", "line": 155, "column": 6, "nodeType": "329", "endLine": 155, "endColumn": 8, "suggestions": "400"}, {"ruleId": "315", "severity": 1, "message": "316", "line": 27, "column": 5, "nodeType": "317", "messageId": "318", "endLine": 27, "endColumn": 18, "suggestions": "401"}, "no-console", "Unexpected console statement.", "MemberExpression", "unexpected", ["402"], ["403"], ["404"], ["405"], ["406"], ["407"], ["408"], ["409"], "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'deletedFlag'. Either include it or remove the dependency array.", "ArrayExpression", ["410"], ["411"], ["412"], ["413"], ["414"], ["415"], ["416"], "React Hook useEffect has missing dependencies: 'loadReceipts' and 'treatment.receiptUrl'. Either include them or remove the dependency array.", ["417"], "eqeqeq", "Expected '===' and instead saw '=='.", "BinaryExpression", ["418"], ["419"], "React Hook useEffect has a missing dependency: 'loadImages'. Either include it or remove the dependency array.", ["420"], ["421"], "React Hook useEffect has a missing dependency: 'loadReports'. Either include it or remove the dependency array.", ["422"], ["423"], ["424"], ["425"], ["426"], ["427"], ["428"], ["429"], ["430"], ["431"], ["432"], ["433"], ["434"], ["435"], ["436"], ["437"], "React Hook useEffect has a missing dependency: 'loadUsers'. Either include it or remove the dependency array.", ["438"], ["439"], "React Hook useEffect has missing dependencies: 'loadUsers' and 'roles.length'. Either include them or remove the dependency array.", ["440"], ["441"], "React Hook useEffect has a spread element in its dependency array. This means we can't statically verify whether you've passed the correct dependencies.", "SpreadElement", ["442"], ["443"], ["444"], ["445"], ["446"], ["447"], ["448"], ["449"], ["450"], "no-unused-vars", "'DEBUG' is defined but never used.", "Identifier", "unusedVar", "'INFO' is defined but never used.", "'WARN' is defined but never used.", "'ERROR' is defined but never used.", "'NONE' is defined but never used.", ["451"], ["452"], ["453"], ["454"], ["455"], ["456"], ["457"], "React Hook useEffect has a missing dependency: 'loadBlocks'. Either include it or remove the dependency array.", ["458"], ["459"], "React Hook useEffect has a missing dependency: 'loadLogs'. Either include it or remove the dependency array.", ["460"], ["461"], {"messageId": "462", "data": "463", "fix": "464", "desc": "465"}, {"messageId": "462", "data": "466", "fix": "467", "desc": "468"}, {"messageId": "462", "data": "469", "fix": "470", "desc": "468"}, {"messageId": "462", "data": "471", "fix": "472", "desc": "468"}, {"messageId": "462", "data": "473", "fix": "474", "desc": "468"}, {"messageId": "462", "data": "475", "fix": "476", "desc": "468"}, {"messageId": "462", "data": "477", "fix": "478", "desc": "468"}, {"messageId": "462", "data": "479", "fix": "480", "desc": "465"}, {"desc": "481", "fix": "482"}, {"messageId": "462", "data": "483", "fix": "484", "desc": "468"}, {"messageId": "462", "data": "485", "fix": "486", "desc": "468"}, {"messageId": "462", "data": "487", "fix": "488", "desc": "468"}, {"messageId": "462", "data": "489", "fix": "490", "desc": "465"}, {"messageId": "462", "data": "491", "fix": "492", "desc": "468"}, {"messageId": "462", "data": "493", "fix": "494", "desc": "468"}, {"desc": "495", "fix": "496"}, {"messageId": "462", "data": "497", "fix": "498", "desc": "468"}, {"messageId": "462", "data": "499", "fix": "500", "desc": "465"}, {"desc": "501", "fix": "502"}, {"messageId": "462", "data": "503", "fix": "504", "desc": "465"}, {"desc": "505", "fix": "506"}, {"messageId": "462", "data": "507", "fix": "508", "desc": "468"}, {"messageId": "462", "data": "509", "fix": "510", "desc": "468"}, {"messageId": "462", "data": "511", "fix": "512", "desc": "468"}, {"messageId": "462", "data": "513", "fix": "514", "desc": "468"}, {"messageId": "462", "data": "515", "fix": "516", "desc": "468"}, {"messageId": "462", "data": "517", "fix": "518", "desc": "465"}, {"messageId": "462", "data": "519", "fix": "520", "desc": "465"}, {"messageId": "462", "data": "521", "fix": "522", "desc": "465"}, {"messageId": "462", "data": "523", "fix": "524", "desc": "465"}, {"messageId": "462", "data": "525", "fix": "526", "desc": "468"}, {"messageId": "462", "data": "527", "fix": "528", "desc": "468"}, {"messageId": "462", "data": "529", "fix": "530", "desc": "468"}, {"messageId": "462", "data": "531", "fix": "532", "desc": "468"}, {"messageId": "462", "data": "533", "fix": "534", "desc": "465"}, {"messageId": "462", "data": "535", "fix": "536", "desc": "468"}, {"desc": "537", "fix": "538"}, {"kind": "539", "justification": "540"}, {"desc": "541", "fix": "542"}, {"kind": "539", "justification": "540"}, {"messageId": "462", "data": "543", "fix": "544", "desc": "468"}, {"messageId": "462", "data": "545", "fix": "546", "desc": "468"}, {"messageId": "462", "data": "547", "fix": "548", "desc": "468"}, {"messageId": "462", "data": "549", "fix": "550", "desc": "468"}, {"messageId": "462", "data": "551", "fix": "552", "desc": "465"}, {"messageId": "462", "data": "553", "fix": "554", "desc": "465"}, {"messageId": "462", "data": "555", "fix": "556", "desc": "468"}, {"messageId": "462", "data": "557", "fix": "558", "desc": "468"}, {"messageId": "462", "data": "559", "fix": "560", "desc": "468"}, {"messageId": "462", "data": "561", "fix": "562", "desc": "468"}, {"messageId": "462", "data": "563", "fix": "564", "desc": "565"}, {"messageId": "462", "data": "566", "fix": "567", "desc": "568"}, {"messageId": "462", "data": "569", "fix": "570", "desc": "465"}, {"messageId": "462", "data": "571", "fix": "572", "desc": "468"}, {"messageId": "462", "data": "573", "fix": "574", "desc": "468"}, {"messageId": "462", "data": "575", "fix": "576", "desc": "465"}, {"desc": "577", "fix": "578"}, {"messageId": "462", "data": "579", "fix": "580", "desc": "465"}, {"desc": "581", "fix": "582"}, {"messageId": "462", "data": "583", "fix": "584", "desc": "465"}, "removeConsole", {"propertyName": "585"}, {"range": "586", "text": "540"}, "Remove the console.error().", {"propertyName": "587"}, {"range": "588", "text": "540"}, "Remove the console.log().", {"propertyName": "587"}, {"range": "589", "text": "540"}, {"propertyName": "587"}, {"range": "590", "text": "540"}, {"propertyName": "587"}, {"range": "591", "text": "540"}, {"propertyName": "587"}, {"range": "592", "text": "540"}, {"propertyName": "587"}, {"range": "593", "text": "540"}, {"propertyName": "585"}, {"range": "594", "text": "540"}, "Update the dependencies array to be: [deletedFlag, loading]", {"range": "595", "text": "596"}, {"propertyName": "587"}, {"range": "597", "text": "540"}, {"propertyName": "587"}, {"range": "598", "text": "540"}, {"propertyName": "587"}, {"range": "599", "text": "540"}, {"propertyName": "585"}, {"range": "600", "text": "540"}, {"propertyName": "587"}, {"range": "601", "text": "540"}, {"propertyName": "587"}, {"range": "602", "text": "540"}, "Update the dependencies array to be: [loadReceipts, treatment.receiptUrl]", {"range": "603", "text": "604"}, {"propertyName": "587"}, {"range": "605", "text": "540"}, {"propertyName": "585"}, {"range": "606", "text": "540"}, "Update the dependencies array to be: [loadImages]", {"range": "607", "text": "608"}, {"propertyName": "585"}, {"range": "609", "text": "540"}, "Update the dependencies array to be: [loadReports]", {"range": "610", "text": "611"}, {"propertyName": "587"}, {"range": "612", "text": "540"}, {"propertyName": "587"}, {"range": "613", "text": "540"}, {"propertyName": "587"}, {"range": "614", "text": "540"}, {"propertyName": "587"}, {"range": "615", "text": "540"}, {"propertyName": "587"}, {"range": "616", "text": "540"}, {"propertyName": "585"}, {"range": "617", "text": "540"}, {"propertyName": "585"}, {"range": "618", "text": "540"}, {"propertyName": "585"}, {"range": "619", "text": "540"}, {"propertyName": "585"}, {"range": "620", "text": "540"}, {"propertyName": "587"}, {"range": "621", "text": "540"}, {"propertyName": "587"}, {"range": "622", "text": "540"}, {"propertyName": "587"}, {"range": "623", "text": "540"}, {"propertyName": "587"}, {"range": "624", "text": "540"}, {"propertyName": "585"}, {"range": "625", "text": "540"}, {"propertyName": "587"}, {"range": "626", "text": "540"}, "Update the dependencies array to be: [loadUsers]", {"range": "627", "text": "628"}, "directive", "", "Update the dependencies array to be: [loadUsers, roles.length, selectedRoleFilter]", {"range": "629", "text": "630"}, {"propertyName": "587"}, {"range": "631", "text": "540"}, {"propertyName": "587"}, {"range": "632", "text": "540"}, {"propertyName": "587"}, {"range": "633", "text": "540"}, {"propertyName": "587"}, {"range": "634", "text": "540"}, {"propertyName": "585"}, {"range": "635", "text": "540"}, {"propertyName": "585"}, {"range": "636", "text": "540"}, {"propertyName": "587"}, {"range": "637", "text": "540"}, {"propertyName": "587"}, {"range": "638", "text": "540"}, {"propertyName": "587"}, {"range": "639", "text": "540"}, {"propertyName": "587"}, {"range": "640", "text": "540"}, {"propertyName": "641"}, {"range": "642", "text": "540"}, "Remove the console.info().", {"propertyName": "643"}, {"range": "644", "text": "540"}, "Remove the console.warn().", {"propertyName": "585"}, {"range": "645", "text": "540"}, {"propertyName": "587"}, {"range": "646", "text": "540"}, {"propertyName": "587"}, {"range": "647", "text": "540"}, {"propertyName": "585"}, {"range": "648", "text": "540"}, "Update the dependencies array to be: [loadBlocks]", {"range": "649", "text": "650"}, {"propertyName": "585"}, {"range": "651", "text": "540"}, "Update the dependencies array to be: [loadLogs]", {"range": "652", "text": "653"}, {"propertyName": "585"}, {"range": "654", "text": "540"}, "error", [992, 1030], "log", [761, 909], [961, 997], [1276, 1323], [1392, 1430], [1503, 1535], [1674, 1701], [1842, 1870], [1977, 1986], "[deletedFlag, loading]", [1044, 1071], [3097, 3125], [3219, 3256], [3567, 3602], [1521, 1577], [1808, 1836], [2193, 2195], "[loadReceipts, treatment.receiptUrl]", [2917, 2945], [5311, 5358], [8615, 8617], "[loadImages]", [5688, 5735], [7884, 7886], "[loadReports]", [2610, 2635], [2912, 2956], [2966, 3010], [3020, 3065], [3851, 3892], [4097, 4129], [5266, 5300], [8024, 8054], [9991, 10021], [10556, 10580], [10851, 10893], [11066, 11107], [11197, 11236], [11718, 11747], [1376, 1411], [5278, 5280], "[loadUsers]", [5444, 5464], "[loadUsers, roles.length, selectedRoleFilter]", [852, 900], [1104, 1154], [1456, 1511], [1625, 1673], [1742, 1796], [925, 961], [576, 742], [770, 808], [1015, 1059], [1582, 1624], "info", [1683, 1726], "warn", [1785, 1828], [1888, 1932], [3284, 3324], [3432, 3475], [5107, 5154], [6999, 7001], "[loadBlocks]", [3480, 3527], [4358, 4360], "[loadLogs]", [808, 873]]
{"ast": null, "code": "import { buildMatchFn } from \"../../_lib/buildMatchFn.js\";\nimport { buildMatchPatternFn } from \"../../_lib/buildMatchPatternFn.js\";\nconst matchOrdinalNumberPattern = /^(\\d+)(-?(е|й|є|а|я))?/i;\nconst parseOrdinalNumberPattern = /\\d+/i;\nconst matchEraPatterns = {\n  narrow: /^((до )?н\\.?\\s?е\\.?)/i,\n  abbreviated: /^((до )?н\\.?\\s?е\\.?)/i,\n  wide: /^(до нашої ери|нашої ери|наша ера)/i\n};\nconst parseEraPatterns = {\n  any: [/^д/i, /^н/i]\n};\nconst matchQuarterPatterns = {\n  narrow: /^[1234]/i,\n  abbreviated: /^[1234](-?[иі]?й?)? кв.?/i,\n  wide: /^[1234](-?[иі]?й?)? квартал/i\n};\nconst parseQuarterPatterns = {\n  any: [/1/i, /2/i, /3/i, /4/i]\n};\nconst matchMonthPatterns = {\n  narrow: /^[слбктчвжг]/i,\n  abbreviated: /^(січ|лют|бер(ез)?|квіт|трав|черв|лип|серп|вер(ес)?|жовт|лис(топ)?|груд)\\.?/i,\n  wide: /^(січень|січня|лютий|лютого|березень|березня|квітень|квітня|травень|травня|червня|червень|липень|липня|серпень|серпня|вересень|вересня|жовтень|жовтня|листопад[а]?|грудень|грудня)/i\n};\nconst parseMonthPatterns = {\n  narrow: [/^с/i, /^л/i, /^б/i, /^к/i, /^т/i, /^ч/i, /^л/i, /^с/i, /^в/i, /^ж/i, /^л/i, /^г/i],\n  any: [/^сі/i, /^лю/i, /^б/i, /^к/i, /^т/i, /^ч/i, /^лип/i, /^се/i, /^в/i, /^ж/i, /^лис/i, /^г/i]\n};\nconst matchDayPatterns = {\n  narrow: /^[нпвсч]/i,\n  short: /^(нд|пн|вт|ср|чт|пт|сб)\\.?/i,\n  abbreviated: /^(нед|пон|вів|сер|че?тв|птн?|суб)\\.?/i,\n  wide: /^(неділ[яі]|понеділ[ок][ка]|вівтор[ок][ка]|серед[аи]|четвер(га)?|п\\W*?ятниц[яі]|субот[аи])/i\n};\nconst parseDayPatterns = {\n  narrow: [/^н/i, /^п/i, /^в/i, /^с/i, /^ч/i, /^п/i, /^с/i],\n  any: [/^н/i, /^п[он]/i, /^в/i, /^с[ер]/i, /^ч/i, /^п\\W*?[ят]/i, /^с[уб]/i]\n};\nconst matchDayPeriodPatterns = {\n  narrow: /^([дп]п|півн\\.?|пол\\.?|ранок|ранку|день|дня|веч\\.?|ніч|ночі)/i,\n  abbreviated: /^([дп]п|півн\\.?|пол\\.?|ранок|ранку|день|дня|веч\\.?|ніч|ночі)/i,\n  wide: /^([дп]п|північ|полудень|ранок|ранку|день|дня|вечір|вечора|ніч|ночі)/i\n};\nconst parseDayPeriodPatterns = {\n  any: {\n    am: /^дп/i,\n    pm: /^пп/i,\n    midnight: /^півн/i,\n    noon: /^пол/i,\n    morning: /^р/i,\n    afternoon: /^д[ен]/i,\n    evening: /^в/i,\n    night: /^н/i\n  }\n};\nexport const match = {\n  ordinalNumber: buildMatchPatternFn({\n    matchPattern: matchOrdinalNumberPattern,\n    parsePattern: parseOrdinalNumberPattern,\n    valueCallback: value => parseInt(value, 10)\n  }),\n  era: buildMatchFn({\n    matchPatterns: matchEraPatterns,\n    defaultMatchWidth: \"wide\",\n    parsePatterns: parseEraPatterns,\n    defaultParseWidth: \"any\"\n  }),\n  quarter: buildMatchFn({\n    matchPatterns: matchQuarterPatterns,\n    defaultMatchWidth: \"wide\",\n    parsePatterns: parseQuarterPatterns,\n    defaultParseWidth: \"any\",\n    valueCallback: index => index + 1\n  }),\n  month: buildMatchFn({\n    matchPatterns: matchMonthPatterns,\n    defaultMatchWidth: \"wide\",\n    parsePatterns: parseMonthPatterns,\n    defaultParseWidth: \"any\"\n  }),\n  day: buildMatchFn({\n    matchPatterns: matchDayPatterns,\n    defaultMatchWidth: \"wide\",\n    parsePatterns: parseDayPatterns,\n    defaultParseWidth: \"any\"\n  }),\n  dayPeriod: buildMatchFn({\n    matchPatterns: matchDayPeriodPatterns,\n    defaultMatchWidth: \"wide\",\n    parsePatterns: parseDayPeriodPatterns,\n    defaultParseWidth: \"any\"\n  })\n};", "map": {"version": 3, "names": ["buildMatchFn", "buildMatchPatternFn", "matchOrdinalNumberPattern", "parseOrdinalNumberPattern", "matchEraPatterns", "narrow", "abbreviated", "wide", "parseEraPatterns", "any", "matchQuarterPatterns", "parseQuarterPatterns", "matchMonthPatterns", "parseMonthPatterns", "matchDayPatterns", "short", "parseDayPatterns", "matchDayPeriodPatterns", "parseDayPeriodPatterns", "am", "pm", "midnight", "noon", "morning", "afternoon", "evening", "night", "match", "ordinalNumber", "matchPattern", "parsePattern", "valueCallback", "value", "parseInt", "era", "matchPatterns", "defaultMatchWidth", "parsePatterns", "defaultParseWidth", "quarter", "index", "month", "day", "<PERSON><PERSON><PERSON><PERSON>"], "sources": ["C:/Users/<USER>/Desktop/demo-git/demo-react/my-fullstack-app/frontend/node_modules/date-fns/locale/uk/_lib/match.js"], "sourcesContent": ["import { buildMatchFn } from \"../../_lib/buildMatchFn.js\";\nimport { buildMatchPatternFn } from \"../../_lib/buildMatchPatternFn.js\";\n\nconst matchOrdinalNumberPattern = /^(\\d+)(-?(е|й|є|а|я))?/i;\nconst parseOrdinalNumberPattern = /\\d+/i;\n\nconst matchEraPatterns = {\n  narrow: /^((до )?н\\.?\\s?е\\.?)/i,\n  abbreviated: /^((до )?н\\.?\\s?е\\.?)/i,\n  wide: /^(до нашої ери|нашої ери|наша ера)/i,\n};\n\nconst parseEraPatterns = {\n  any: [/^д/i, /^н/i],\n};\n\nconst matchQuarterPatterns = {\n  narrow: /^[1234]/i,\n  abbreviated: /^[1234](-?[иі]?й?)? кв.?/i,\n  wide: /^[1234](-?[иі]?й?)? квартал/i,\n};\n\nconst parseQuarterPatterns = {\n  any: [/1/i, /2/i, /3/i, /4/i],\n};\n\nconst matchMonthPatterns = {\n  narrow: /^[слбктчвжг]/i,\n  abbreviated:\n    /^(січ|лют|бер(ез)?|квіт|трав|черв|лип|серп|вер(ес)?|жовт|лис(топ)?|груд)\\.?/i,\n  wide: /^(січень|січня|лютий|лютого|березень|березня|квітень|квітня|травень|травня|червня|червень|липень|липня|серпень|серпня|вересень|вересня|жовтень|жовтня|листопад[а]?|грудень|грудня)/i,\n};\n\nconst parseMonthPatterns = {\n  narrow: [\n    /^с/i,\n    /^л/i,\n    /^б/i,\n    /^к/i,\n    /^т/i,\n    /^ч/i,\n    /^л/i,\n    /^с/i,\n    /^в/i,\n    /^ж/i,\n    /^л/i,\n    /^г/i,\n  ],\n\n  any: [\n    /^сі/i,\n    /^лю/i,\n    /^б/i,\n    /^к/i,\n    /^т/i,\n    /^ч/i,\n    /^лип/i,\n    /^се/i,\n    /^в/i,\n    /^ж/i,\n    /^лис/i,\n    /^г/i,\n  ],\n};\n\nconst matchDayPatterns = {\n  narrow: /^[нпвсч]/i,\n  short: /^(нд|пн|вт|ср|чт|пт|сб)\\.?/i,\n  abbreviated: /^(нед|пон|вів|сер|че?тв|птн?|суб)\\.?/i,\n  wide: /^(неділ[яі]|понеділ[ок][ка]|вівтор[ок][ка]|серед[аи]|четвер(га)?|п\\W*?ятниц[яі]|субот[аи])/i,\n};\n\nconst parseDayPatterns = {\n  narrow: [/^н/i, /^п/i, /^в/i, /^с/i, /^ч/i, /^п/i, /^с/i],\n  any: [/^н/i, /^п[он]/i, /^в/i, /^с[ер]/i, /^ч/i, /^п\\W*?[ят]/i, /^с[уб]/i],\n};\n\nconst matchDayPeriodPatterns = {\n  narrow: /^([дп]п|півн\\.?|пол\\.?|ранок|ранку|день|дня|веч\\.?|ніч|ночі)/i,\n  abbreviated: /^([дп]п|півн\\.?|пол\\.?|ранок|ранку|день|дня|веч\\.?|ніч|ночі)/i,\n  wide: /^([дп]п|північ|полудень|ранок|ранку|день|дня|вечір|вечора|ніч|ночі)/i,\n};\n\nconst parseDayPeriodPatterns = {\n  any: {\n    am: /^дп/i,\n    pm: /^пп/i,\n    midnight: /^півн/i,\n    noon: /^пол/i,\n    morning: /^р/i,\n    afternoon: /^д[ен]/i,\n    evening: /^в/i,\n    night: /^н/i,\n  },\n};\n\nexport const match = {\n  ordinalNumber: buildMatchPatternFn({\n    matchPattern: matchOrdinalNumberPattern,\n    parsePattern: parseOrdinalNumberPattern,\n    valueCallback: (value) => parseInt(value, 10),\n  }),\n\n  era: buildMatchFn({\n    matchPatterns: matchEraPatterns,\n    defaultMatchWidth: \"wide\",\n    parsePatterns: parseEraPatterns,\n    defaultParseWidth: \"any\",\n  }),\n\n  quarter: buildMatchFn({\n    matchPatterns: matchQuarterPatterns,\n    defaultMatchWidth: \"wide\",\n    parsePatterns: parseQuarterPatterns,\n    defaultParseWidth: \"any\",\n    valueCallback: (index) => index + 1,\n  }),\n\n  month: buildMatchFn({\n    matchPatterns: matchMonthPatterns,\n    defaultMatchWidth: \"wide\",\n    parsePatterns: parseMonthPatterns,\n    defaultParseWidth: \"any\",\n  }),\n\n  day: buildMatchFn({\n    matchPatterns: matchDayPatterns,\n    defaultMatchWidth: \"wide\",\n    parsePatterns: parseDayPatterns,\n    defaultParseWidth: \"any\",\n  }),\n\n  dayPeriod: buildMatchFn({\n    matchPatterns: matchDayPeriodPatterns,\n    defaultMatchWidth: \"wide\",\n    parsePatterns: parseDayPeriodPatterns,\n    defaultParseWidth: \"any\",\n  }),\n};\n"], "mappings": "AAAA,SAASA,YAAY,QAAQ,4BAA4B;AACzD,SAASC,mBAAmB,QAAQ,mCAAmC;AAEvE,MAAMC,yBAAyB,GAAG,yBAAyB;AAC3D,MAAMC,yBAAyB,GAAG,MAAM;AAExC,MAAMC,gBAAgB,GAAG;EACvBC,MAAM,EAAE,uBAAuB;EAC/BC,WAAW,EAAE,uBAAuB;EACpCC,IAAI,EAAE;AACR,CAAC;AAED,MAAMC,gBAAgB,GAAG;EACvBC,GAAG,EAAE,CAAC,KAAK,EAAE,KAAK;AACpB,CAAC;AAED,MAAMC,oBAAoB,GAAG;EAC3BL,MAAM,EAAE,UAAU;EAClBC,WAAW,EAAE,2BAA2B;EACxCC,IAAI,EAAE;AACR,CAAC;AAED,MAAMI,oBAAoB,GAAG;EAC3BF,GAAG,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI;AAC9B,CAAC;AAED,MAAMG,kBAAkB,GAAG;EACzBP,MAAM,EAAE,eAAe;EACvBC,WAAW,EACT,8EAA8E;EAChFC,IAAI,EAAE;AACR,CAAC;AAED,MAAMM,kBAAkB,GAAG;EACzBR,MAAM,EAAE,CACN,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,CACN;EAEDI,GAAG,EAAE,CACH,MAAM,EACN,MAAM,EACN,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,OAAO,EACP,MAAM,EACN,KAAK,EACL,KAAK,EACL,OAAO,EACP,KAAK;AAET,CAAC;AAED,MAAMK,gBAAgB,GAAG;EACvBT,MAAM,EAAE,WAAW;EACnBU,KAAK,EAAE,6BAA6B;EACpCT,WAAW,EAAE,uCAAuC;EACpDC,IAAI,EAAE;AACR,CAAC;AAED,MAAMS,gBAAgB,GAAG;EACvBX,MAAM,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC;EACzDI,GAAG,EAAE,CAAC,KAAK,EAAE,SAAS,EAAE,KAAK,EAAE,SAAS,EAAE,KAAK,EAAE,aAAa,EAAE,SAAS;AAC3E,CAAC;AAED,MAAMQ,sBAAsB,GAAG;EAC7BZ,MAAM,EAAE,+DAA+D;EACvEC,WAAW,EAAE,+DAA+D;EAC5EC,IAAI,EAAE;AACR,CAAC;AAED,MAAMW,sBAAsB,GAAG;EAC7BT,GAAG,EAAE;IACHU,EAAE,EAAE,MAAM;IACVC,EAAE,EAAE,MAAM;IACVC,QAAQ,EAAE,QAAQ;IAClBC,IAAI,EAAE,OAAO;IACbC,OAAO,EAAE,KAAK;IACdC,SAAS,EAAE,SAAS;IACpBC,OAAO,EAAE,KAAK;IACdC,KAAK,EAAE;EACT;AACF,CAAC;AAED,OAAO,MAAMC,KAAK,GAAG;EACnBC,aAAa,EAAE3B,mBAAmB,CAAC;IACjC4B,YAAY,EAAE3B,yBAAyB;IACvC4B,YAAY,EAAE3B,yBAAyB;IACvC4B,aAAa,EAAGC,KAAK,IAAKC,QAAQ,CAACD,KAAK,EAAE,EAAE;EAC9C,CAAC,CAAC;EAEFE,GAAG,EAAElC,YAAY,CAAC;IAChBmC,aAAa,EAAE/B,gBAAgB;IAC/BgC,iBAAiB,EAAE,MAAM;IACzBC,aAAa,EAAE7B,gBAAgB;IAC/B8B,iBAAiB,EAAE;EACrB,CAAC,CAAC;EAEFC,OAAO,EAAEvC,YAAY,CAAC;IACpBmC,aAAa,EAAEzB,oBAAoB;IACnC0B,iBAAiB,EAAE,MAAM;IACzBC,aAAa,EAAE1B,oBAAoB;IACnC2B,iBAAiB,EAAE,KAAK;IACxBP,aAAa,EAAGS,KAAK,IAAKA,KAAK,GAAG;EACpC,CAAC,CAAC;EAEFC,KAAK,EAAEzC,YAAY,CAAC;IAClBmC,aAAa,EAAEvB,kBAAkB;IACjCwB,iBAAiB,EAAE,MAAM;IACzBC,aAAa,EAAExB,kBAAkB;IACjCyB,iBAAiB,EAAE;EACrB,CAAC,CAAC;EAEFI,GAAG,EAAE1C,YAAY,CAAC;IAChBmC,aAAa,EAAErB,gBAAgB;IAC/BsB,iBAAiB,EAAE,MAAM;IACzBC,aAAa,EAAErB,gBAAgB;IAC/BsB,iBAAiB,EAAE;EACrB,CAAC,CAAC;EAEFK,SAAS,EAAE3C,YAAY,CAAC;IACtBmC,aAAa,EAAElB,sBAAsB;IACrCmB,iBAAiB,EAAE,MAAM;IACzBC,aAAa,EAAEnB,sBAAsB;IACrCoB,iBAAiB,EAAE;EACrB,CAAC;AACH,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
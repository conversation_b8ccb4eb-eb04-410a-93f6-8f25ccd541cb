{"ast": null, "code": "import _objectSpread from\"C:/Users/<USER>/Desktop/demo-git/demo-react/my-fullstack-app/frontend/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";import React,{useState,useRef,useEffect}from'react';import{But<PERSON>}from'primereact/button';import{Column}from'primereact/column';import{DataTable}from'primereact/datatable';import{Dialog}from'primereact/dialog';import{Toast}from'primereact/toast';import{ConfirmDialog,confirmDialog}from'primereact/confirmdialog';import{Card}from'primereact/card';import{ProgressSpinner}from'primereact/progressspinner';import{Tag}from'primereact/tag';import{InputText}from'primereact/inputtext';import{Calendar}from'primereact/calendar';import{formatUtcToTaipei}from\"../../utils/dateUtils\";import api from'../../services/api';import{log}from'../../utils/logger';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const IpBlocksPage=()=>{const toast=useRef(null);const[blocks,setBlocks]=useState([]);const[loading,setLoading]=useState(true);const[totalRecords,setTotalRecords]=useState(0);const[first,setFirst]=useState(0);const[rows,setRows]=useState(20);const[showAddDialog,setShowAddDialog]=useState(false);const[newIpAddress,setNewIpAddress]=useState('');const[newExpiredAt,setNewExpiredAt]=useState(null);// 搜尋條件\nconst[filters,setFilters]=useState({ipAddress:'',createdAtStart:null,createdAtEnd:null});// 載入 IP 封鎖列表\nconst loadBlocks=async function(){let page=arguments.length>0&&arguments[0]!==undefined?arguments[0]:1;let pageSize=arguments.length>1&&arguments[1]!==undefined?arguments[1]:20;try{setLoading(true);log.api('載入 IP 封鎖列表');const params={page,pageSize};if(filters.ipAddress)params.ipAddress=filters.ipAddress;if(filters.createdAtStart)params.createdAtStart=filters.createdAtStart.toISOString();if(filters.createdAtEnd)params.createdAtEnd=filters.createdAtEnd.toISOString();const response=await api.get('/api/system/GetIpBlock',{params});setBlocks(response.data.data);setTotalRecords(response.data.totalCount);log.api('IP 封鎖列表載入成功',{count:response.data.data.length});}catch(error){var _toast$current;log.error('載入 IP 封鎖列表失敗',error);(_toast$current=toast.current)===null||_toast$current===void 0?void 0:_toast$current.show({severity:'error',summary:'載入失敗',detail:'無法載入 IP 封鎖列表',life:5000});}finally{setLoading(false);}};// 新增 IP 封鎖\nconst addIpBlock=async()=>{if(!newIpAddress){var _toast$current2;(_toast$current2=toast.current)===null||_toast$current2===void 0?void 0:_toast$current2.show({severity:'error',summary:'錯誤',detail:'請輸入 IP 位址'});return;}try{var _toast$current3;log.api('新增 IP 封鎖',{ipAddress:newIpAddress,expiredAt:newExpiredAt});await api.post('/api/system/AddIpBlock',{ipAddress:newIpAddress,expiredAt:newExpiredAt?newExpiredAt.toISOString():null});(_toast$current3=toast.current)===null||_toast$current3===void 0?void 0:_toast$current3.show({severity:'success',summary:'成功',detail:\"IP \".concat(newIpAddress,\" \\u5DF2\\u6210\\u529F\\u5C01\\u9396\"),life:3000});setShowAddDialog(false);setNewIpAddress('');setNewExpiredAt(null);loadBlocks(1,rows);}catch(error){var _toast$current4,_error$response,_error$response$data;log.error('新增 IP 封鎖失敗',error);(_toast$current4=toast.current)===null||_toast$current4===void 0?void 0:_toast$current4.show({severity:'error',summary:'新增失敗',detail:((_error$response=error.response)===null||_error$response===void 0?void 0:(_error$response$data=_error$response.data)===null||_error$response$data===void 0?void 0:_error$response$data.error)||'新增 IP 封鎖失敗',life:5000});}};// 解鎖 IP\nconst unlockIp=async block=>{try{var _toast$current5;log.api('解鎖 IP',{id:block.id,ipAddress:block.ipAddress});await api.post('/api/system/UpdateIpBlock',{id:block.id});(_toast$current5=toast.current)===null||_toast$current5===void 0?void 0:_toast$current5.show({severity:'success',summary:'解鎖成功',detail:\"IP \".concat(block.ipAddress,\" \\u5DF2\\u89E3\\u9396\"),life:3000});// 重新載入列表\nloadBlocks(Math.floor(first/rows)+1,rows);}catch(error){var _toast$current6,_error$response2,_error$response2$data;log.error('解鎖 IP 失敗',error);(_toast$current6=toast.current)===null||_toast$current6===void 0?void 0:_toast$current6.show({severity:'error',summary:'解鎖失敗',detail:((_error$response2=error.response)===null||_error$response2===void 0?void 0:(_error$response2$data=_error$response2.data)===null||_error$response2$data===void 0?void 0:_error$response2$data.error)||'解鎖 IP 失敗',life:5000});}};// 確認解鎖\nconst confirmUnlock=block=>{confirmDialog({message:\"\\u78BA\\u5B9A\\u8981\\u89E3\\u9396 IP \\\"\".concat(block.ipAddress,\"\\\" \\u55CE\\uFF1F\"),header:'確認解鎖',icon:'pi pi-exclamation-triangle',acceptLabel:'確定',rejectLabel:'取消',accept:()=>unlockIp(block)});};// 搜尋\nconst handleSearch=()=>{setFirst(0);loadBlocks(1,rows);};// 分頁變更\nconst onPageChange=event=>{setFirst(event.first);setRows(event.rows);const page=Math.floor(event.first/event.rows)+1;loadBlocks(page,event.rows);};// 格式化日期\nconst formatDate=dateString=>{if(!dateString)return'';try{return formatUtcToTaipei(dateString,'yyyy/MM/dd HH:mm:ss');}catch(error){console.error('Error formatting date:',error);return dateString;}};// 檢查是否已封鎖（根據當前時間）\nconst isBlocked=expiredAt=>{const now=new Date();const expiredDate=new Date(expiredAt);return expiredDate>now;};// 狀態標籤模板\nconst statusBodyTemplate=rowData=>{const blocked=isBlocked(rowData.expiredAt);const severity=blocked?'danger':'success';const label=blocked?'已封鎖':'已解鎖';return/*#__PURE__*/_jsx(Tag,{value:label,severity:severity});};// 建立日期模板\nconst createdDateBodyTemplate=rowData=>{return formatDate(rowData.createdAt);};// 更新日期模板\nconst updatedDateBodyTemplate=rowData=>{return formatDate(rowData.updatedAt);};// 到期日期模板\nconst expiredDateBodyTemplate=rowData=>{return formatDate(rowData.expiredAt);};// 操作按鈕模板\nconst actionBodyTemplate=rowData=>{const blocked=isBlocked(rowData.expiredAt);// 只有在已封鎖狀態時才顯示解鎖按鈕\nif(!blocked){return/*#__PURE__*/_jsx(\"div\",{className:\"flex gap-2\",children:/*#__PURE__*/_jsx(\"span\",{className:\"text-500 text-sm\",children:\"\\u5DF2\\u89E3\\u9396\"})});}return/*#__PURE__*/_jsx(\"div\",{className:\"flex gap-2\",children:/*#__PURE__*/_jsx(Button,{label:\"\\u89E3\\u9396\",icon:\"pi pi-unlock\",className:\"p-button-success p-button-sm\",onClick:()=>confirmUnlock(rowData),tooltip:\"\\u89E3\\u9396\\u6B64 IP\",tooltipOptions:{position:'top'}})});};// 分頁器左側\nconst paginatorLeft=/*#__PURE__*/_jsx(Button,{type:\"button\",icon:\"pi pi-refresh\",text:true,onClick:()=>loadBlocks(Math.floor(first/rows)+1,rows),disabled:loading});const paginatorRight=/*#__PURE__*/_jsx(\"div\",{});useEffect(()=>{loadBlocks();},[]);if(loading&&blocks.length===0){return/*#__PURE__*/_jsx(\"div\",{className:\"flex justify-content-center align-items-center\",style:{height:'400px'},children:/*#__PURE__*/_jsx(ProgressSpinner,{})});}return/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(Toast,{ref:toast}),/*#__PURE__*/_jsx(ConfirmDialog,{}),/*#__PURE__*/_jsx(Card,{title:\"IP \\u5C01\\u9396\\u7BA1\\u7406\",className:\"mb-4\",children:/*#__PURE__*/_jsx(\"p\",{className:\"text-600 line-height-3 m-0\",children:\"\\u7BA1\\u7406\\u7CFB\\u7D71\\u4E2D\\u88AB\\u5C01\\u9396\\u7684 IP \\u4F4D\\u5740\\u3002\\u7576 IP \\u4F4D\\u5740\\u5728\\u77ED\\u6642\\u9593\\u5167\\u9032\\u884C\\u904E\\u591A\\u8ACB\\u6C42\\u6642\\uFF0C\\u7CFB\\u7D71\\u6703\\u81EA\\u52D5\\u5C01\\u9396\\u8A72 IP\\u3002\\u60A8\\u53EF\\u4EE5\\u624B\\u52D5\\u89E3\\u9396\\u88AB\\u5C01\\u9396\\u7684 IP \\u4F4D\\u5740\\u3002\"})}),/*#__PURE__*/_jsx(Card,{className:\"mb-4\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"grid\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"col-12 md:col-4\",children:/*#__PURE__*/_jsx(InputText,{id:\"ipAddress\",value:filters.ipAddress,onChange:e=>setFilters(_objectSpread(_objectSpread({},filters),{},{ipAddress:e.target.value})),placeholder:\"\\u8F38\\u5165 IP \\u4F4D\\u5740\",className:\"w-full\"})}),/*#__PURE__*/_jsx(\"div\",{className:\"col-6 md:col-4\",children:/*#__PURE__*/_jsx(Calendar,{value:filters.createdAtStart,onChange:e=>setFilters(_objectSpread(_objectSpread({},filters),{},{createdAtStart:e.value})),placeholder:\"\\u9078\\u64C7\\u958B\\u59CB\\u65E5\\u671F\",className:\"w-full\",showIcon:true,dateFormat:\"yy/mm/dd\"})}),/*#__PURE__*/_jsx(\"div\",{className:\"col-6 md:col-4\",children:/*#__PURE__*/_jsx(Calendar,{value:filters.createdAtEnd,onChange:e=>setFilters(_objectSpread(_objectSpread({},filters),{},{createdAtEnd:e.value})),placeholder:\"\\u9078\\u64C7\\u7D50\\u675F\\u65E5\\u671F\",className:\"w-full\",showIcon:true,dateFormat:\"yy/mm/dd\"})}),/*#__PURE__*/_jsx(\"div\",{className:\"col-12 md:col-4\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex gap-2\",children:[/*#__PURE__*/_jsx(Button,{label:\"\\u641C\\u5C0B\",icon:\"pi pi-search\",onClick:handleSearch}),/*#__PURE__*/_jsx(Button,{label:\"\\u6DFB\\u52A0\",icon:\"pi pi-plus\",onClick:()=>setShowAddDialog(true)})]})})]})}),/*#__PURE__*/_jsx(Card,{children:/*#__PURE__*/_jsxs(DataTable,{value:blocks,paginator:true,lazy:true,first:first,rows:rows,totalRecords:totalRecords,onPage:onPageChange,rowsPerPageOptions:[10,20,50],emptyMessage:\"\\u6C92\\u6709\\u627E\\u5230 IP \\u5C01\\u9396\\u8A18\\u9304\",tableStyle:{minWidth:'50rem'},paginatorLeft:paginatorLeft,paginatorRight:paginatorRight,loading:loading,children:[/*#__PURE__*/_jsx(Column,{field:\"ipAddress\",header:\"IP \\u4F4D\\u5740\",sortable:true,style:{width:'15%'}}),/*#__PURE__*/_jsx(Column,{field:\"status\",header:\"\\u72C0\\u614B\",body:statusBodyTemplate,style:{width:'10%'}}),/*#__PURE__*/_jsx(Column,{field:\"createdAt\",header:\"\\u5EFA\\u7ACB\\u6642\\u9593\",body:createdDateBodyTemplate,sortable:true,style:{width:'20%'}}),/*#__PURE__*/_jsx(Column,{field:\"updatedAt\",header:\"\\u66F4\\u65B0\\u6642\\u9593\",body:updatedDateBodyTemplate,sortable:true,style:{width:'20%'}}),/*#__PURE__*/_jsx(Column,{field:\"expiredAt\",header:\"\\u5230\\u671F\\u6642\\u9593\",body:expiredDateBodyTemplate,sortable:true,style:{width:'20%'}}),/*#__PURE__*/_jsx(Column,{header:\"\\u64CD\\u4F5C\",body:actionBodyTemplate,style:{width:'15%'}})]})}),/*#__PURE__*/_jsx(Dialog,{header:\"\\u65B0\\u589E IP \\u5C01\\u9396\",visible:showAddDialog,style:{width:'450px'},modal:true,onHide:()=>setShowAddDialog(false),footer:/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(Button,{label:\"\\u53D6\\u6D88\",icon:\"pi pi-times\",onClick:()=>setShowAddDialog(false),className:\"p-button-text\"}),/*#__PURE__*/_jsx(Button,{label:\"\\u65B0\\u589E\",icon:\"pi pi-check\",onClick:addIpBlock,autoFocus:true})]}),children:/*#__PURE__*/_jsxs(\"div\",{className:\"p-fluid\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"field\",children:[/*#__PURE__*/_jsx(\"label\",{htmlFor:\"newIpAddress\",children:\"IP \\u4F4D\\u5740\"}),/*#__PURE__*/_jsx(InputText,{id:\"newIpAddress\",value:newIpAddress,onChange:e=>setNewIpAddress(e.target.value)})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"field\",children:[/*#__PURE__*/_jsx(\"label\",{htmlFor:\"newExpiredAt\",children:\"\\u5230\\u671F\\u6642\\u9593 (\\u53EF\\u9078)\"}),/*#__PURE__*/_jsx(Calendar,{id:\"newExpiredAt\",value:newExpiredAt,onChange:e=>setNewExpiredAt(e.value),showIcon:true,dateFormat:\"yy/mm/dd\"})]})]})})]});};export default IpBlocksPage;", "map": {"version": 3, "names": ["React", "useState", "useRef", "useEffect", "<PERSON><PERSON>", "Column", "DataTable", "Dialog", "Toast", "ConfirmDialog", "confirmDialog", "Card", "ProgressSpinner", "Tag", "InputText", "Calendar", "formatUtcToTaipei", "api", "log", "jsx", "_jsx", "jsxs", "_jsxs", "IpBlocksPage", "toast", "blocks", "setBlocks", "loading", "setLoading", "totalRecords", "setTotalRecords", "first", "<PERSON><PERSON><PERSON><PERSON>", "rows", "setRows", "showAddDialog", "setShowAddDialog", "newIpAddress", "setNewIpAddress", "newExpiredAt", "setNewExpiredAt", "filters", "setFilters", "ip<PERSON><PERSON><PERSON>", "createdAtStart", "createdAtEnd", "loadBlocks", "page", "arguments", "length", "undefined", "pageSize", "params", "toISOString", "response", "get", "data", "totalCount", "count", "error", "_toast$current", "current", "show", "severity", "summary", "detail", "life", "addIpBlock", "_toast$current2", "_toast$current3", "expiredAt", "post", "concat", "_toast$current4", "_error$response", "_error$response$data", "unlockIp", "block", "_toast$current5", "id", "Math", "floor", "_toast$current6", "_error$response2", "_error$response2$data", "confirm<PERSON>n<PERSON>", "message", "header", "icon", "acceptLabel", "<PERSON><PERSON><PERSON><PERSON>", "accept", "handleSearch", "onPageChange", "event", "formatDate", "dateString", "console", "isBlocked", "now", "Date", "expiredDate", "statusBodyTemplate", "rowData", "blocked", "label", "value", "createdDateBodyTemplate", "createdAt", "updatedDateBodyTemplate", "updatedAt", "expiredDateBodyTemplate", "actionBodyTemplate", "className", "children", "onClick", "tooltip", "tooltipOptions", "position", "paginatorLeft", "type", "text", "disabled", "paginatorRight", "style", "height", "ref", "title", "onChange", "e", "_objectSpread", "target", "placeholder", "showIcon", "dateFormat", "paginator", "lazy", "onPage", "rowsPerPageOptions", "emptyMessage", "tableStyle", "min<PERSON><PERSON><PERSON>", "field", "sortable", "width", "body", "visible", "modal", "onHide", "footer", "autoFocus", "htmlFor"], "sources": ["C:/Users/<USER>/Desktop/demo-git/demo-react/my-fullstack-app/frontend/src/components/Page/IpBlocksPage.tsx"], "sourcesContent": ["import React, { useState, useRef, useEffect } from 'react';\r\nimport { Button } from 'primereact/button';\r\nimport { Column } from 'primereact/column';\r\nimport { DataTable } from 'primereact/datatable';\r\nimport { Dialog } from 'primereact/dialog';\r\nimport { Toast } from 'primereact/toast';\r\nimport { ConfirmDialog, confirmDialog } from 'primereact/confirmdialog';\r\nimport { Card } from 'primereact/card';\r\nimport { ProgressSpinner } from 'primereact/progressspinner';\r\nimport { Tag } from 'primereact/tag';\r\nimport { InputText } from 'primereact/inputtext';\r\nimport { Calendar } from 'primereact/calendar';\r\nimport { formatUtcToTaipei } from \"../../utils/dateUtils\";\r\nimport api from '../../services/api';\r\nimport { log } from '../../utils/logger';\r\n\r\ninterface IpBlock {\r\n  id: number;\r\n  ipAddress: string;\r\n  createdAt: string;\r\n  updatedAt: string;\r\n  expiredAt: string;\r\n}\r\n\r\nconst IpBlocksPage: React.FC = () => {\r\n  const toast = useRef<Toast>(null);\r\n  const [blocks, setBlocks] = useState<IpBlock[]>([]);\r\n  const [loading, setLoading] = useState(true);\r\n  const [totalRecords, setTotalRecords] = useState(0);\r\n  const [first, setFirst] = useState(0);\r\n  const [rows, setRows] = useState(20);\r\n  const [showAddDialog, setShowAddDialog] = useState(false);\r\n  const [newIpAddress, setNewIpAddress] = useState('');\r\n  const [newExpiredAt, setNewExpiredAt] = useState<Date | null>(null);\r\n\r\n  // 搜尋條件\r\n  const [filters, setFilters] = useState({\r\n    ipAddress: '',\r\n    createdAtStart: null as Date | null,\r\n    createdAtEnd: null as Date | null\r\n  });\r\n\r\n  // 載入 IP 封鎖列表\r\n  const loadBlocks = async (page = 1, pageSize = 20) => {\r\n    try {\r\n      setLoading(true);\r\n      log.api('載入 IP 封鎖列表');\r\n\r\n      const params: any = {\r\n        page,\r\n        pageSize\r\n      };\r\n\r\n      if (filters.ipAddress) params.ipAddress = filters.ipAddress;\r\n      if (filters.createdAtStart) params.createdAtStart = filters.createdAtStart.toISOString();\r\n      if (filters.createdAtEnd) params.createdAtEnd = filters.createdAtEnd.toISOString();\r\n\r\n      const response = await api.get('/api/system/GetIpBlock', { params });\r\n      \r\n      setBlocks(response.data.data);\r\n      setTotalRecords(response.data.totalCount);\r\n      \r\n      log.api('IP 封鎖列表載入成功', { count: response.data.data.length });\r\n      \r\n    } catch (error: any) {\r\n      log.error('載入 IP 封鎖列表失敗', error);\r\n      toast.current?.show({\r\n        severity: 'error',\r\n        summary: '載入失敗',\r\n        detail: '無法載入 IP 封鎖列表',\r\n        life: 5000\r\n      });\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  // 新增 IP 封鎖\r\n  const addIpBlock = async () => {\r\n    if (!newIpAddress) {\r\n      toast.current?.show({ severity: 'error', summary: '錯誤', detail: '請輸入 IP 位址' });\r\n      return;\r\n    }\r\n\r\n    try {\r\n      log.api('新增 IP 封鎖', { ipAddress: newIpAddress, expiredAt: newExpiredAt });\r\n\r\n      await api.post('/api/system/AddIpBlock', { \r\n        ipAddress: newIpAddress, \r\n        expiredAt: newExpiredAt ? newExpiredAt.toISOString() : null \r\n      });\r\n\r\n      toast.current?.show({\r\n        severity: 'success',\r\n        summary: '成功',\r\n        detail: `IP ${newIpAddress} 已成功封鎖`,\r\n        life: 3000\r\n      });\r\n\r\n      setShowAddDialog(false);\r\n      setNewIpAddress('');\r\n      setNewExpiredAt(null);\r\n      loadBlocks(1, rows);\r\n\r\n    } catch (error: any) {\r\n      log.error('新增 IP 封鎖失敗', error);\r\n      toast.current?.show({\r\n        severity: 'error',\r\n        summary: '新增失敗',\r\n        detail: error.response?.data?.error || '新增 IP 封鎖失敗',\r\n        life: 5000\r\n      });\r\n    }\r\n  };\r\n\r\n  // 解鎖 IP\r\n  const unlockIp = async (block: IpBlock) => {\r\n    try {\r\n      log.api('解鎖 IP', { id: block.id, ipAddress: block.ipAddress });\r\n      \r\n      await api.post('/api/system/UpdateIpBlock', { id: block.id });\r\n      \r\n      toast.current?.show({\r\n        severity: 'success',\r\n        summary: '解鎖成功',\r\n        detail: `IP ${block.ipAddress} 已解鎖`,\r\n        life: 3000\r\n      });\r\n      \r\n      // 重新載入列表\r\n      loadBlocks(Math.floor(first / rows) + 1, rows);\r\n      \r\n    } catch (error: any) {\r\n      log.error('解鎖 IP 失敗', error);\r\n      toast.current?.show({\r\n        severity: 'error',\r\n        summary: '解鎖失敗',\r\n        detail: error.response?.data?.error || '解鎖 IP 失敗',\r\n        life: 5000\r\n      });\r\n    }\r\n  };\r\n\r\n  // 確認解鎖\r\n  const confirmUnlock = (block: IpBlock) => {\r\n    confirmDialog({\r\n      message: `確定要解鎖 IP \"${block.ipAddress}\" 嗎？`,\r\n      header: '確認解鎖',\r\n      icon: 'pi pi-exclamation-triangle',\r\n      acceptLabel: '確定',\r\n      rejectLabel: '取消',\r\n      accept: () => unlockIp(block),\r\n    });\r\n  };\r\n\r\n  // 搜尋\r\n  const handleSearch = () => {\r\n    setFirst(0);\r\n    loadBlocks(1, rows);\r\n  };\r\n\r\n  // 分頁變更\r\n  const onPageChange = (event: any) => {\r\n    setFirst(event.first);\r\n    setRows(event.rows);\r\n    const page = Math.floor(event.first / event.rows) + 1;\r\n    loadBlocks(page, event.rows);\r\n  };\r\n\r\n  // 格式化日期\r\n  const formatDate = (dateString: string): string => {\r\n    if (!dateString) return '';\r\n    try {\r\n      return formatUtcToTaipei(dateString, 'yyyy/MM/dd HH:mm:ss');\r\n    } catch (error) {\r\n      console.error('Error formatting date:', error);\r\n      return dateString;\r\n    }\r\n  };\r\n\r\n  // 檢查是否已封鎖（根據當前時間）\r\n  const isBlocked = (expiredAt: string): boolean => {\r\n    const now = new Date();\r\n    const expiredDate = new Date(expiredAt);\r\n    return expiredDate > now;\r\n  };\r\n\r\n  // 狀態標籤模板\r\n  const statusBodyTemplate = (rowData: IpBlock) => {\r\n    const blocked = isBlocked(rowData.expiredAt);\r\n    const severity = blocked ? 'danger' : 'success';\r\n    const label = blocked ? '已封鎖' : '已解鎖';\r\n    return <Tag value={label} severity={severity} />;\r\n  };\r\n\r\n  // 建立日期模板\r\n  const createdDateBodyTemplate = (rowData: IpBlock) => {\r\n    return formatDate(rowData.createdAt);\r\n  };\r\n\r\n  // 更新日期模板\r\n  const updatedDateBodyTemplate = (rowData: IpBlock) => {\r\n    return formatDate(rowData.updatedAt);\r\n  };\r\n\r\n  // 到期日期模板\r\n  const expiredDateBodyTemplate = (rowData: IpBlock) => {\r\n    return formatDate(rowData.expiredAt);\r\n  };\r\n\r\n  // 操作按鈕模板\r\n  const actionBodyTemplate = (rowData: IpBlock) => {\r\n    const blocked = isBlocked(rowData.expiredAt);\r\n\r\n    // 只有在已封鎖狀態時才顯示解鎖按鈕\r\n    if (!blocked) {\r\n      return (\r\n        <div className=\"flex gap-2\">\r\n          <span className=\"text-500 text-sm\">已解鎖</span>\r\n        </div>\r\n      );\r\n    }\r\n\r\n    return (\r\n      <div className=\"flex gap-2\">\r\n        <Button\r\n          label=\"解鎖\"\r\n          icon=\"pi pi-unlock\"\r\n          className=\"p-button-success p-button-sm\"\r\n          onClick={() => confirmUnlock(rowData)}\r\n          tooltip=\"解鎖此 IP\"\r\n          tooltipOptions={{ position: 'top' }}\r\n        />\r\n      </div>\r\n    );\r\n  };\r\n\r\n  // 分頁器左側\r\n  const paginatorLeft = (\r\n    <Button\r\n      type=\"button\"\r\n      icon=\"pi pi-refresh\"\r\n      text\r\n      onClick={() => loadBlocks(Math.floor(first / rows) + 1, rows)}\r\n      disabled={loading}\r\n    />\r\n  );\r\n\r\n  const paginatorRight = <div></div>;\r\n\r\n  useEffect(() => {\r\n    loadBlocks();\r\n  }, []);\r\n\r\n  if (loading && blocks.length === 0) {\r\n    return (\r\n      <div className=\"flex justify-content-center align-items-center\" style={{ height: '400px' }}>\r\n        <ProgressSpinner />\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <div>\r\n      <Toast ref={toast} />\r\n      <ConfirmDialog />\r\n      \r\n      <Card title=\"IP 封鎖管理\" className=\"mb-4\">\r\n        <p className=\"text-600 line-height-3 m-0\">\r\n          管理系統中被封鎖的 IP 位址。當 IP 位址在短時間內進行過多請求時，系統會自動封鎖該 IP。您可以手動解鎖被封鎖的 IP 位址。\r\n        </p>\r\n      </Card>\r\n\r\n      {/* 搜尋條件 */}\r\n      <Card className=\"mb-4\">\r\n        <div className=\"grid\">\r\n          <div className=\"col-12 md:col-4\">\r\n            <InputText\r\n              id=\"ipAddress\"\r\n              value={filters.ipAddress}\r\n              onChange={(e) => setFilters({ ...filters, ipAddress: e.target.value })}\r\n              placeholder=\"輸入 IP 位址\"\r\n              className=\"w-full\"\r\n            />\r\n          </div>\r\n          \r\n          <div className=\"col-6 md:col-4\">\r\n            <Calendar\r\n              value={filters.createdAtStart}\r\n              onChange={(e) => setFilters({ ...filters, createdAtStart: e.value as Date })}\r\n              placeholder=\"選擇開始日期\"\r\n              className=\"w-full\"\r\n              showIcon\r\n              dateFormat=\"yy/mm/dd\"\r\n            />\r\n          </div>\r\n          \r\n          <div className=\"col-6 md:col-4\">\r\n            <Calendar\r\n              value={filters.createdAtEnd}\r\n              onChange={(e) => setFilters({ ...filters, createdAtEnd: e.value as Date })}\r\n              placeholder=\"選擇結束日期\"\r\n              className=\"w-full\"\r\n              showIcon\r\n              dateFormat=\"yy/mm/dd\"\r\n            />\r\n          </div>\r\n          \r\n          <div className=\"col-12 md:col-4\">\r\n            <div className=\"flex gap-2\">\r\n              <Button\r\n                label=\"搜尋\"\r\n                icon=\"pi pi-search\"\r\n                onClick={handleSearch}\r\n              />\r\n              <Button\r\n                label=\"添加\"\r\n                icon=\"pi pi-plus\"\r\n                onClick={() => setShowAddDialog(true)}\r\n              />\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </Card>\r\n\r\n      {/* 資料表 */}\r\n      <Card>\r\n        <DataTable\r\n          value={blocks}\r\n          paginator\r\n          lazy\r\n          first={first}\r\n          rows={rows}\r\n          totalRecords={totalRecords}\r\n          onPage={onPageChange}\r\n          rowsPerPageOptions={[10, 20, 50]}\r\n          emptyMessage=\"沒有找到 IP 封鎖記錄\"\r\n          tableStyle={{ minWidth: '50rem' }}\r\n          paginatorLeft={paginatorLeft}\r\n          paginatorRight={paginatorRight}\r\n          loading={loading}\r\n        >\r\n          <Column field=\"ipAddress\" header=\"IP 位址\" sortable style={{ width: '15%' }} />\r\n          <Column field=\"status\" header=\"狀態\" body={statusBodyTemplate} style={{ width: '10%' }} />\r\n          <Column field=\"createdAt\" header=\"建立時間\" body={createdDateBodyTemplate} sortable style={{ width: '20%' }} />\r\n          <Column field=\"updatedAt\" header=\"更新時間\" body={updatedDateBodyTemplate} sortable style={{ width: '20%' }} />\r\n          <Column field=\"expiredAt\" header=\"到期時間\" body={expiredDateBodyTemplate} sortable style={{ width: '20%' }} />\r\n          <Column header=\"操作\" body={actionBodyTemplate} style={{ width: '15%' }} />\r\n        </DataTable>\r\n      </Card>\r\n\r\n      {/* 新增 IP 封鎖對話框 */}\r\n      <Dialog\r\n        header=\"新增 IP 封鎖\"\r\n        visible={showAddDialog}\r\n        style={{ width: '450px' }}\r\n        modal\r\n        onHide={() => setShowAddDialog(false)}\r\n        footer={\r\n          <div>\r\n            <Button label=\"取消\" icon=\"pi pi-times\" onClick={() => setShowAddDialog(false)} className=\"p-button-text\" />\r\n            <Button label=\"新增\" icon=\"pi pi-check\" onClick={addIpBlock} autoFocus />\r\n          </div>\r\n        }\r\n      >\r\n        <div className=\"p-fluid\">\r\n          <div className=\"field\">\r\n            <label htmlFor=\"newIpAddress\">IP 位址</label>\r\n            <InputText id=\"newIpAddress\" value={newIpAddress} onChange={(e) => setNewIpAddress(e.target.value)} />\r\n          </div>\r\n          <div className=\"field\">\r\n            <label htmlFor=\"newExpiredAt\">到期時間 (可選)</label>\r\n            <Calendar id=\"newExpiredAt\" value={newExpiredAt} onChange={(e) => setNewExpiredAt(e.value as Date)} showIcon dateFormat=\"yy/mm/dd\" />\r\n          </div>\r\n        </div>\r\n      </Dialog>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default IpBlocksPage;\r\n"], "mappings": "wJAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,MAAM,CAAEC,SAAS,KAAQ,OAAO,CAC1D,OAASC,MAAM,KAAQ,mBAAmB,CAC1C,OAASC,MAAM,KAAQ,mBAAmB,CAC1C,OAASC,SAAS,KAAQ,sBAAsB,CAChD,OAASC,MAAM,KAAQ,mBAAmB,CAC1C,OAASC,KAAK,KAAQ,kBAAkB,CACxC,OAASC,aAAa,CAAEC,aAAa,KAAQ,0BAA0B,CACvE,OAASC,IAAI,KAAQ,iBAAiB,CACtC,OAASC,eAAe,KAAQ,4BAA4B,CAC5D,OAASC,GAAG,KAAQ,gBAAgB,CACpC,OAASC,SAAS,KAAQ,sBAAsB,CAChD,OAASC,QAAQ,KAAQ,qBAAqB,CAC9C,OAASC,iBAAiB,KAAQ,uBAAuB,CACzD,MAAO,CAAAC,GAAG,KAAM,oBAAoB,CACpC,OAASC,GAAG,KAAQ,oBAAoB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAUzC,KAAM,CAAAC,YAAsB,CAAGA,CAAA,GAAM,CACnC,KAAM,CAAAC,KAAK,CAAGtB,MAAM,CAAQ,IAAI,CAAC,CACjC,KAAM,CAACuB,MAAM,CAAEC,SAAS,CAAC,CAAGzB,QAAQ,CAAY,EAAE,CAAC,CACnD,KAAM,CAAC0B,OAAO,CAAEC,UAAU,CAAC,CAAG3B,QAAQ,CAAC,IAAI,CAAC,CAC5C,KAAM,CAAC4B,YAAY,CAAEC,eAAe,CAAC,CAAG7B,QAAQ,CAAC,CAAC,CAAC,CACnD,KAAM,CAAC8B,KAAK,CAAEC,QAAQ,CAAC,CAAG/B,QAAQ,CAAC,CAAC,CAAC,CACrC,KAAM,CAACgC,IAAI,CAAEC,OAAO,CAAC,CAAGjC,QAAQ,CAAC,EAAE,CAAC,CACpC,KAAM,CAACkC,aAAa,CAAEC,gBAAgB,CAAC,CAAGnC,QAAQ,CAAC,KAAK,CAAC,CACzD,KAAM,CAACoC,YAAY,CAAEC,eAAe,CAAC,CAAGrC,QAAQ,CAAC,EAAE,CAAC,CACpD,KAAM,CAACsC,YAAY,CAAEC,eAAe,CAAC,CAAGvC,QAAQ,CAAc,IAAI,CAAC,CAEnE;AACA,KAAM,CAACwC,OAAO,CAAEC,UAAU,CAAC,CAAGzC,QAAQ,CAAC,CACrC0C,SAAS,CAAE,EAAE,CACbC,cAAc,CAAE,IAAmB,CACnCC,YAAY,CAAE,IAChB,CAAC,CAAC,CAEF;AACA,KAAM,CAAAC,UAAU,CAAG,cAAAA,CAAA,CAAmC,IAA5B,CAAAC,IAAI,CAAAC,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAE,SAAA,CAAAF,SAAA,IAAG,CAAC,IAAE,CAAAG,QAAQ,CAAAH,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAE,SAAA,CAAAF,SAAA,IAAG,EAAE,CAC/C,GAAI,CACFpB,UAAU,CAAC,IAAI,CAAC,CAChBV,GAAG,CAACD,GAAG,CAAC,YAAY,CAAC,CAErB,KAAM,CAAAmC,MAAW,CAAG,CAClBL,IAAI,CACJI,QACF,CAAC,CAED,GAAIV,OAAO,CAACE,SAAS,CAAES,MAAM,CAACT,SAAS,CAAGF,OAAO,CAACE,SAAS,CAC3D,GAAIF,OAAO,CAACG,cAAc,CAAEQ,MAAM,CAACR,cAAc,CAAGH,OAAO,CAACG,cAAc,CAACS,WAAW,CAAC,CAAC,CACxF,GAAIZ,OAAO,CAACI,YAAY,CAAEO,MAAM,CAACP,YAAY,CAAGJ,OAAO,CAACI,YAAY,CAACQ,WAAW,CAAC,CAAC,CAElF,KAAM,CAAAC,QAAQ,CAAG,KAAM,CAAArC,GAAG,CAACsC,GAAG,CAAC,wBAAwB,CAAE,CAAEH,MAAO,CAAC,CAAC,CAEpE1B,SAAS,CAAC4B,QAAQ,CAACE,IAAI,CAACA,IAAI,CAAC,CAC7B1B,eAAe,CAACwB,QAAQ,CAACE,IAAI,CAACC,UAAU,CAAC,CAEzCvC,GAAG,CAACD,GAAG,CAAC,aAAa,CAAE,CAAEyC,KAAK,CAAEJ,QAAQ,CAACE,IAAI,CAACA,IAAI,CAACP,MAAO,CAAC,CAAC,CAE9D,CAAE,MAAOU,KAAU,CAAE,KAAAC,cAAA,CACnB1C,GAAG,CAACyC,KAAK,CAAC,cAAc,CAAEA,KAAK,CAAC,CAChC,CAAAC,cAAA,CAAApC,KAAK,CAACqC,OAAO,UAAAD,cAAA,iBAAbA,cAAA,CAAeE,IAAI,CAAC,CAClBC,QAAQ,CAAE,OAAO,CACjBC,OAAO,CAAE,MAAM,CACfC,MAAM,CAAE,cAAc,CACtBC,IAAI,CAAE,IACR,CAAC,CAAC,CACJ,CAAC,OAAS,CACRtC,UAAU,CAAC,KAAK,CAAC,CACnB,CACF,CAAC,CAED;AACA,KAAM,CAAAuC,UAAU,CAAG,KAAAA,CAAA,GAAY,CAC7B,GAAI,CAAC9B,YAAY,CAAE,KAAA+B,eAAA,CACjB,CAAAA,eAAA,CAAA5C,KAAK,CAACqC,OAAO,UAAAO,eAAA,iBAAbA,eAAA,CAAeN,IAAI,CAAC,CAAEC,QAAQ,CAAE,OAAO,CAAEC,OAAO,CAAE,IAAI,CAAEC,MAAM,CAAE,WAAY,CAAC,CAAC,CAC9E,OACF,CAEA,GAAI,KAAAI,eAAA,CACFnD,GAAG,CAACD,GAAG,CAAC,UAAU,CAAE,CAAE0B,SAAS,CAAEN,YAAY,CAAEiC,SAAS,CAAE/B,YAAa,CAAC,CAAC,CAEzE,KAAM,CAAAtB,GAAG,CAACsD,IAAI,CAAC,wBAAwB,CAAE,CACvC5B,SAAS,CAAEN,YAAY,CACvBiC,SAAS,CAAE/B,YAAY,CAAGA,YAAY,CAACc,WAAW,CAAC,CAAC,CAAG,IACzD,CAAC,CAAC,CAEF,CAAAgB,eAAA,CAAA7C,KAAK,CAACqC,OAAO,UAAAQ,eAAA,iBAAbA,eAAA,CAAeP,IAAI,CAAC,CAClBC,QAAQ,CAAE,SAAS,CACnBC,OAAO,CAAE,IAAI,CACbC,MAAM,OAAAO,MAAA,CAAQnC,YAAY,mCAAQ,CAClC6B,IAAI,CAAE,IACR,CAAC,CAAC,CAEF9B,gBAAgB,CAAC,KAAK,CAAC,CACvBE,eAAe,CAAC,EAAE,CAAC,CACnBE,eAAe,CAAC,IAAI,CAAC,CACrBM,UAAU,CAAC,CAAC,CAAEb,IAAI,CAAC,CAErB,CAAE,MAAO0B,KAAU,CAAE,KAAAc,eAAA,CAAAC,eAAA,CAAAC,oBAAA,CACnBzD,GAAG,CAACyC,KAAK,CAAC,YAAY,CAAEA,KAAK,CAAC,CAC9B,CAAAc,eAAA,CAAAjD,KAAK,CAACqC,OAAO,UAAAY,eAAA,iBAAbA,eAAA,CAAeX,IAAI,CAAC,CAClBC,QAAQ,CAAE,OAAO,CACjBC,OAAO,CAAE,MAAM,CACfC,MAAM,CAAE,EAAAS,eAAA,CAAAf,KAAK,CAACL,QAAQ,UAAAoB,eAAA,kBAAAC,oBAAA,CAAdD,eAAA,CAAgBlB,IAAI,UAAAmB,oBAAA,iBAApBA,oBAAA,CAAsBhB,KAAK,GAAI,YAAY,CACnDO,IAAI,CAAE,IACR,CAAC,CAAC,CACJ,CACF,CAAC,CAED;AACA,KAAM,CAAAU,QAAQ,CAAG,KAAO,CAAAC,KAAc,EAAK,CACzC,GAAI,KAAAC,eAAA,CACF5D,GAAG,CAACD,GAAG,CAAC,OAAO,CAAE,CAAE8D,EAAE,CAAEF,KAAK,CAACE,EAAE,CAAEpC,SAAS,CAAEkC,KAAK,CAAClC,SAAU,CAAC,CAAC,CAE9D,KAAM,CAAA1B,GAAG,CAACsD,IAAI,CAAC,2BAA2B,CAAE,CAAEQ,EAAE,CAAEF,KAAK,CAACE,EAAG,CAAC,CAAC,CAE7D,CAAAD,eAAA,CAAAtD,KAAK,CAACqC,OAAO,UAAAiB,eAAA,iBAAbA,eAAA,CAAehB,IAAI,CAAC,CAClBC,QAAQ,CAAE,SAAS,CACnBC,OAAO,CAAE,MAAM,CACfC,MAAM,OAAAO,MAAA,CAAQK,KAAK,CAAClC,SAAS,uBAAM,CACnCuB,IAAI,CAAE,IACR,CAAC,CAAC,CAEF;AACApB,UAAU,CAACkC,IAAI,CAACC,KAAK,CAAClD,KAAK,CAAGE,IAAI,CAAC,CAAG,CAAC,CAAEA,IAAI,CAAC,CAEhD,CAAE,MAAO0B,KAAU,CAAE,KAAAuB,eAAA,CAAAC,gBAAA,CAAAC,qBAAA,CACnBlE,GAAG,CAACyC,KAAK,CAAC,UAAU,CAAEA,KAAK,CAAC,CAC5B,CAAAuB,eAAA,CAAA1D,KAAK,CAACqC,OAAO,UAAAqB,eAAA,iBAAbA,eAAA,CAAepB,IAAI,CAAC,CAClBC,QAAQ,CAAE,OAAO,CACjBC,OAAO,CAAE,MAAM,CACfC,MAAM,CAAE,EAAAkB,gBAAA,CAAAxB,KAAK,CAACL,QAAQ,UAAA6B,gBAAA,kBAAAC,qBAAA,CAAdD,gBAAA,CAAgB3B,IAAI,UAAA4B,qBAAA,iBAApBA,qBAAA,CAAsBzB,KAAK,GAAI,UAAU,CACjDO,IAAI,CAAE,IACR,CAAC,CAAC,CACJ,CACF,CAAC,CAED;AACA,KAAM,CAAAmB,aAAa,CAAIR,KAAc,EAAK,CACxCnE,aAAa,CAAC,CACZ4E,OAAO,wCAAAd,MAAA,CAAeK,KAAK,CAAClC,SAAS,mBAAM,CAC3C4C,MAAM,CAAE,MAAM,CACdC,IAAI,CAAE,4BAA4B,CAClCC,WAAW,CAAE,IAAI,CACjBC,WAAW,CAAE,IAAI,CACjBC,MAAM,CAAEA,CAAA,GAAMf,QAAQ,CAACC,KAAK,CAC9B,CAAC,CAAC,CACJ,CAAC,CAED;AACA,KAAM,CAAAe,YAAY,CAAGA,CAAA,GAAM,CACzB5D,QAAQ,CAAC,CAAC,CAAC,CACXc,UAAU,CAAC,CAAC,CAAEb,IAAI,CAAC,CACrB,CAAC,CAED;AACA,KAAM,CAAA4D,YAAY,CAAIC,KAAU,EAAK,CACnC9D,QAAQ,CAAC8D,KAAK,CAAC/D,KAAK,CAAC,CACrBG,OAAO,CAAC4D,KAAK,CAAC7D,IAAI,CAAC,CACnB,KAAM,CAAAc,IAAI,CAAGiC,IAAI,CAACC,KAAK,CAACa,KAAK,CAAC/D,KAAK,CAAG+D,KAAK,CAAC7D,IAAI,CAAC,CAAG,CAAC,CACrDa,UAAU,CAACC,IAAI,CAAE+C,KAAK,CAAC7D,IAAI,CAAC,CAC9B,CAAC,CAED;AACA,KAAM,CAAA8D,UAAU,CAAIC,UAAkB,EAAa,CACjD,GAAI,CAACA,UAAU,CAAE,MAAO,EAAE,CAC1B,GAAI,CACF,MAAO,CAAAhF,iBAAiB,CAACgF,UAAU,CAAE,qBAAqB,CAAC,CAC7D,CAAE,MAAOrC,KAAK,CAAE,CACdsC,OAAO,CAACtC,KAAK,CAAC,wBAAwB,CAAEA,KAAK,CAAC,CAC9C,MAAO,CAAAqC,UAAU,CACnB,CACF,CAAC,CAED;AACA,KAAM,CAAAE,SAAS,CAAI5B,SAAiB,EAAc,CAChD,KAAM,CAAA6B,GAAG,CAAG,GAAI,CAAAC,IAAI,CAAC,CAAC,CACtB,KAAM,CAAAC,WAAW,CAAG,GAAI,CAAAD,IAAI,CAAC9B,SAAS,CAAC,CACvC,MAAO,CAAA+B,WAAW,CAAGF,GAAG,CAC1B,CAAC,CAED;AACA,KAAM,CAAAG,kBAAkB,CAAIC,OAAgB,EAAK,CAC/C,KAAM,CAAAC,OAAO,CAAGN,SAAS,CAACK,OAAO,CAACjC,SAAS,CAAC,CAC5C,KAAM,CAAAP,QAAQ,CAAGyC,OAAO,CAAG,QAAQ,CAAG,SAAS,CAC/C,KAAM,CAAAC,KAAK,CAAGD,OAAO,CAAG,KAAK,CAAG,KAAK,CACrC,mBAAOpF,IAAA,CAACP,GAAG,EAAC6F,KAAK,CAAED,KAAM,CAAC1C,QAAQ,CAAEA,QAAS,CAAE,CAAC,CAClD,CAAC,CAED;AACA,KAAM,CAAA4C,uBAAuB,CAAIJ,OAAgB,EAAK,CACpD,MAAO,CAAAR,UAAU,CAACQ,OAAO,CAACK,SAAS,CAAC,CACtC,CAAC,CAED;AACA,KAAM,CAAAC,uBAAuB,CAAIN,OAAgB,EAAK,CACpD,MAAO,CAAAR,UAAU,CAACQ,OAAO,CAACO,SAAS,CAAC,CACtC,CAAC,CAED;AACA,KAAM,CAAAC,uBAAuB,CAAIR,OAAgB,EAAK,CACpD,MAAO,CAAAR,UAAU,CAACQ,OAAO,CAACjC,SAAS,CAAC,CACtC,CAAC,CAED;AACA,KAAM,CAAA0C,kBAAkB,CAAIT,OAAgB,EAAK,CAC/C,KAAM,CAAAC,OAAO,CAAGN,SAAS,CAACK,OAAO,CAACjC,SAAS,CAAC,CAE5C;AACA,GAAI,CAACkC,OAAO,CAAE,CACZ,mBACEpF,IAAA,QAAK6F,SAAS,CAAC,YAAY,CAAAC,QAAA,cACzB9F,IAAA,SAAM6F,SAAS,CAAC,kBAAkB,CAAAC,QAAA,CAAC,oBAAG,CAAM,CAAC,CAC1C,CAAC,CAEV,CAEA,mBACE9F,IAAA,QAAK6F,SAAS,CAAC,YAAY,CAAAC,QAAA,cACzB9F,IAAA,CAAChB,MAAM,EACLqG,KAAK,CAAC,cAAI,CACVjB,IAAI,CAAC,cAAc,CACnByB,SAAS,CAAC,8BAA8B,CACxCE,OAAO,CAAEA,CAAA,GAAM9B,aAAa,CAACkB,OAAO,CAAE,CACtCa,OAAO,CAAC,uBAAQ,CAChBC,cAAc,CAAE,CAAEC,QAAQ,CAAE,KAAM,CAAE,CACrC,CAAC,CACC,CAAC,CAEV,CAAC,CAED;AACA,KAAM,CAAAC,aAAa,cACjBnG,IAAA,CAAChB,MAAM,EACLoH,IAAI,CAAC,QAAQ,CACbhC,IAAI,CAAC,eAAe,CACpBiC,IAAI,MACJN,OAAO,CAAEA,CAAA,GAAMrE,UAAU,CAACkC,IAAI,CAACC,KAAK,CAAClD,KAAK,CAAGE,IAAI,CAAC,CAAG,CAAC,CAAEA,IAAI,CAAE,CAC9DyF,QAAQ,CAAE/F,OAAQ,CACnB,CACF,CAED,KAAM,CAAAgG,cAAc,cAAGvG,IAAA,SAAU,CAAC,CAElCjB,SAAS,CAAC,IAAM,CACd2C,UAAU,CAAC,CAAC,CACd,CAAC,CAAE,EAAE,CAAC,CAEN,GAAInB,OAAO,EAAIF,MAAM,CAACwB,MAAM,GAAK,CAAC,CAAE,CAClC,mBACE7B,IAAA,QAAK6F,SAAS,CAAC,gDAAgD,CAACW,KAAK,CAAE,CAAEC,MAAM,CAAE,OAAQ,CAAE,CAAAX,QAAA,cACzF9F,IAAA,CAACR,eAAe,GAAE,CAAC,CAChB,CAAC,CAEV,CAEA,mBACEU,KAAA,QAAA4F,QAAA,eACE9F,IAAA,CAACZ,KAAK,EAACsH,GAAG,CAAEtG,KAAM,CAAE,CAAC,cACrBJ,IAAA,CAACX,aAAa,GAAE,CAAC,cAEjBW,IAAA,CAACT,IAAI,EAACoH,KAAK,CAAC,6BAAS,CAACd,SAAS,CAAC,MAAM,CAAAC,QAAA,cACpC9F,IAAA,MAAG6F,SAAS,CAAC,4BAA4B,CAAAC,QAAA,CAAC,mUAE1C,CAAG,CAAC,CACA,CAAC,cAGP9F,IAAA,CAACT,IAAI,EAACsG,SAAS,CAAC,MAAM,CAAAC,QAAA,cACpB5F,KAAA,QAAK2F,SAAS,CAAC,MAAM,CAAAC,QAAA,eACnB9F,IAAA,QAAK6F,SAAS,CAAC,iBAAiB,CAAAC,QAAA,cAC9B9F,IAAA,CAACN,SAAS,EACRiE,EAAE,CAAC,WAAW,CACd2B,KAAK,CAAEjE,OAAO,CAACE,SAAU,CACzBqF,QAAQ,CAAGC,CAAC,EAAKvF,UAAU,CAAAwF,aAAA,CAAAA,aAAA,IAAMzF,OAAO,MAAEE,SAAS,CAAEsF,CAAC,CAACE,MAAM,CAACzB,KAAK,EAAE,CAAE,CACvE0B,WAAW,CAAC,8BAAU,CACtBnB,SAAS,CAAC,QAAQ,CACnB,CAAC,CACC,CAAC,cAEN7F,IAAA,QAAK6F,SAAS,CAAC,gBAAgB,CAAAC,QAAA,cAC7B9F,IAAA,CAACL,QAAQ,EACP2F,KAAK,CAAEjE,OAAO,CAACG,cAAe,CAC9BoF,QAAQ,CAAGC,CAAC,EAAKvF,UAAU,CAAAwF,aAAA,CAAAA,aAAA,IAAMzF,OAAO,MAAEG,cAAc,CAAEqF,CAAC,CAACvB,KAAa,EAAE,CAAE,CAC7E0B,WAAW,CAAC,sCAAQ,CACpBnB,SAAS,CAAC,QAAQ,CAClBoB,QAAQ,MACRC,UAAU,CAAC,UAAU,CACtB,CAAC,CACC,CAAC,cAENlH,IAAA,QAAK6F,SAAS,CAAC,gBAAgB,CAAAC,QAAA,cAC7B9F,IAAA,CAACL,QAAQ,EACP2F,KAAK,CAAEjE,OAAO,CAACI,YAAa,CAC5BmF,QAAQ,CAAGC,CAAC,EAAKvF,UAAU,CAAAwF,aAAA,CAAAA,aAAA,IAAMzF,OAAO,MAAEI,YAAY,CAAEoF,CAAC,CAACvB,KAAa,EAAE,CAAE,CAC3E0B,WAAW,CAAC,sCAAQ,CACpBnB,SAAS,CAAC,QAAQ,CAClBoB,QAAQ,MACRC,UAAU,CAAC,UAAU,CACtB,CAAC,CACC,CAAC,cAENlH,IAAA,QAAK6F,SAAS,CAAC,iBAAiB,CAAAC,QAAA,cAC9B5F,KAAA,QAAK2F,SAAS,CAAC,YAAY,CAAAC,QAAA,eACzB9F,IAAA,CAAChB,MAAM,EACLqG,KAAK,CAAC,cAAI,CACVjB,IAAI,CAAC,cAAc,CACnB2B,OAAO,CAAEvB,YAAa,CACvB,CAAC,cACFxE,IAAA,CAAChB,MAAM,EACLqG,KAAK,CAAC,cAAI,CACVjB,IAAI,CAAC,YAAY,CACjB2B,OAAO,CAAEA,CAAA,GAAM/E,gBAAgB,CAAC,IAAI,CAAE,CACvC,CAAC,EACC,CAAC,CACH,CAAC,EACH,CAAC,CACF,CAAC,cAGPhB,IAAA,CAACT,IAAI,EAAAuG,QAAA,cACH5F,KAAA,CAAChB,SAAS,EACRoG,KAAK,CAAEjF,MAAO,CACd8G,SAAS,MACTC,IAAI,MACJzG,KAAK,CAAEA,KAAM,CACbE,IAAI,CAAEA,IAAK,CACXJ,YAAY,CAAEA,YAAa,CAC3B4G,MAAM,CAAE5C,YAAa,CACrB6C,kBAAkB,CAAE,CAAC,EAAE,CAAE,EAAE,CAAE,EAAE,CAAE,CACjCC,YAAY,CAAC,sDAAc,CAC3BC,UAAU,CAAE,CAAEC,QAAQ,CAAE,OAAQ,CAAE,CAClCtB,aAAa,CAAEA,aAAc,CAC7BI,cAAc,CAAEA,cAAe,CAC/BhG,OAAO,CAAEA,OAAQ,CAAAuF,QAAA,eAEjB9F,IAAA,CAACf,MAAM,EAACyI,KAAK,CAAC,WAAW,CAACvD,MAAM,CAAC,iBAAO,CAACwD,QAAQ,MAACnB,KAAK,CAAE,CAAEoB,KAAK,CAAE,KAAM,CAAE,CAAE,CAAC,cAC7E5H,IAAA,CAACf,MAAM,EAACyI,KAAK,CAAC,QAAQ,CAACvD,MAAM,CAAC,cAAI,CAAC0D,IAAI,CAAE3C,kBAAmB,CAACsB,KAAK,CAAE,CAAEoB,KAAK,CAAE,KAAM,CAAE,CAAE,CAAC,cACxF5H,IAAA,CAACf,MAAM,EAACyI,KAAK,CAAC,WAAW,CAACvD,MAAM,CAAC,0BAAM,CAAC0D,IAAI,CAAEtC,uBAAwB,CAACoC,QAAQ,MAACnB,KAAK,CAAE,CAAEoB,KAAK,CAAE,KAAM,CAAE,CAAE,CAAC,cAC3G5H,IAAA,CAACf,MAAM,EAACyI,KAAK,CAAC,WAAW,CAACvD,MAAM,CAAC,0BAAM,CAAC0D,IAAI,CAAEpC,uBAAwB,CAACkC,QAAQ,MAACnB,KAAK,CAAE,CAAEoB,KAAK,CAAE,KAAM,CAAE,CAAE,CAAC,cAC3G5H,IAAA,CAACf,MAAM,EAACyI,KAAK,CAAC,WAAW,CAACvD,MAAM,CAAC,0BAAM,CAAC0D,IAAI,CAAElC,uBAAwB,CAACgC,QAAQ,MAACnB,KAAK,CAAE,CAAEoB,KAAK,CAAE,KAAM,CAAE,CAAE,CAAC,cAC3G5H,IAAA,CAACf,MAAM,EAACkF,MAAM,CAAC,cAAI,CAAC0D,IAAI,CAAEjC,kBAAmB,CAACY,KAAK,CAAE,CAAEoB,KAAK,CAAE,KAAM,CAAE,CAAE,CAAC,EAChE,CAAC,CACR,CAAC,cAGP5H,IAAA,CAACb,MAAM,EACLgF,MAAM,CAAC,8BAAU,CACjB2D,OAAO,CAAE/G,aAAc,CACvByF,KAAK,CAAE,CAAEoB,KAAK,CAAE,OAAQ,CAAE,CAC1BG,KAAK,MACLC,MAAM,CAAEA,CAAA,GAAMhH,gBAAgB,CAAC,KAAK,CAAE,CACtCiH,MAAM,cACJ/H,KAAA,QAAA4F,QAAA,eACE9F,IAAA,CAAChB,MAAM,EAACqG,KAAK,CAAC,cAAI,CAACjB,IAAI,CAAC,aAAa,CAAC2B,OAAO,CAAEA,CAAA,GAAM/E,gBAAgB,CAAC,KAAK,CAAE,CAAC6E,SAAS,CAAC,eAAe,CAAE,CAAC,cAC1G7F,IAAA,CAAChB,MAAM,EAACqG,KAAK,CAAC,cAAI,CAACjB,IAAI,CAAC,aAAa,CAAC2B,OAAO,CAAEhD,UAAW,CAACmF,SAAS,MAAE,CAAC,EACpE,CACN,CAAApC,QAAA,cAED5F,KAAA,QAAK2F,SAAS,CAAC,SAAS,CAAAC,QAAA,eACtB5F,KAAA,QAAK2F,SAAS,CAAC,OAAO,CAAAC,QAAA,eACpB9F,IAAA,UAAOmI,OAAO,CAAC,cAAc,CAAArC,QAAA,CAAC,iBAAK,CAAO,CAAC,cAC3C9F,IAAA,CAACN,SAAS,EAACiE,EAAE,CAAC,cAAc,CAAC2B,KAAK,CAAErE,YAAa,CAAC2F,QAAQ,CAAGC,CAAC,EAAK3F,eAAe,CAAC2F,CAAC,CAACE,MAAM,CAACzB,KAAK,CAAE,CAAE,CAAC,EACnG,CAAC,cACNpF,KAAA,QAAK2F,SAAS,CAAC,OAAO,CAAAC,QAAA,eACpB9F,IAAA,UAAOmI,OAAO,CAAC,cAAc,CAAArC,QAAA,CAAC,yCAAS,CAAO,CAAC,cAC/C9F,IAAA,CAACL,QAAQ,EAACgE,EAAE,CAAC,cAAc,CAAC2B,KAAK,CAAEnE,YAAa,CAACyF,QAAQ,CAAGC,CAAC,EAAKzF,eAAe,CAACyF,CAAC,CAACvB,KAAa,CAAE,CAAC2B,QAAQ,MAACC,UAAU,CAAC,UAAU,CAAE,CAAC,EAClI,CAAC,EACH,CAAC,CACA,CAAC,EACN,CAAC,CAEV,CAAC,CAED,cAAe,CAAA/G,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "import { isSameWeek } from \"../../../isSameWeek.js\";\nimport { toDate } from \"../../../toDate.js\";\nconst accusativeWeekdays = [\"неділю\", \"понеділок\", \"вівторок\", \"середу\", \"четвер\", \"п’ятницю\", \"суботу\"];\nfunction lastWeek(day) {\n  const weekday = accusativeWeekdays[day];\n  switch (day) {\n    case 0:\n    case 3:\n    case 5:\n    case 6:\n      return \"'у минулу \" + weekday + \" о' p\";\n    case 1:\n    case 2:\n    case 4:\n      return \"'у минулий \" + weekday + \" о' p\";\n  }\n}\nfunction thisWeek(day) {\n  const weekday = accusativeWeekdays[day];\n  return \"'у \" + weekday + \" о' p\";\n}\nfunction nextWeek(day) {\n  const weekday = accusativeWeekdays[day];\n  switch (day) {\n    case 0:\n    case 3:\n    case 5:\n    case 6:\n      return \"'у наступну \" + weekday + \" о' p\";\n    case 1:\n    case 2:\n    case 4:\n      return \"'у наступний \" + weekday + \" о' p\";\n  }\n}\nconst lastWeekFormat = (dirtyDate, baseDate, options) => {\n  const date = toDate(dirtyDate);\n  const day = date.getDay();\n  if (isSameWeek(date, baseDate, options)) {\n    return thisWeek(day);\n  } else {\n    return lastWeek(day);\n  }\n};\nconst nextWeekFormat = (dirtyDate, baseDate, options) => {\n  const date = toDate(dirtyDate);\n  const day = date.getDay();\n  if (isSameWeek(date, baseDate, options)) {\n    return thisWeek(day);\n  } else {\n    return nextWeek(day);\n  }\n};\nconst formatRelativeLocale = {\n  lastWeek: lastWeekFormat,\n  yesterday: \"'вчора о' p\",\n  today: \"'сьогодні о' p\",\n  tomorrow: \"'завтра о' p\",\n  nextWeek: nextWeekFormat,\n  other: \"P\"\n};\nexport const formatRelative = (token, date, baseDate, options) => {\n  const format = formatRelativeLocale[token];\n  if (typeof format === \"function\") {\n    return format(date, baseDate, options);\n  }\n  return format;\n};", "map": {"version": 3, "names": ["isSameWeek", "toDate", "accusativeWeekdays", "lastWeek", "day", "weekday", "thisWeek", "nextWeek", "lastWeekFormat", "dirtyDate", "baseDate", "options", "date", "getDay", "nextWeekFormat", "formatRelativeLocale", "yesterday", "today", "tomorrow", "other", "formatRelative", "token", "format"], "sources": ["C:/Users/<USER>/Desktop/demo-git/demo-react/my-fullstack-app/frontend/node_modules/date-fns/locale/uk/_lib/formatRelative.js"], "sourcesContent": ["import { isSameWeek } from \"../../../isSameWeek.js\";\nimport { toDate } from \"../../../toDate.js\";\n\nconst accusativeWeekdays = [\n  \"неділю\",\n  \"понеділок\",\n  \"вівторок\",\n  \"середу\",\n  \"четвер\",\n  \"п’ятницю\",\n  \"суботу\",\n];\n\nfunction lastWeek(day) {\n  const weekday = accusativeWeekdays[day];\n\n  switch (day) {\n    case 0:\n    case 3:\n    case 5:\n    case 6:\n      return \"'у минулу \" + weekday + \" о' p\";\n    case 1:\n    case 2:\n    case 4:\n      return \"'у минулий \" + weekday + \" о' p\";\n  }\n}\n\nfunction thisWeek(day) {\n  const weekday = accusativeWeekdays[day];\n\n  return \"'у \" + weekday + \" о' p\";\n}\n\nfunction nextWeek(day) {\n  const weekday = accusativeWeekdays[day];\n\n  switch (day) {\n    case 0:\n    case 3:\n    case 5:\n    case 6:\n      return \"'у наступну \" + weekday + \" о' p\";\n    case 1:\n    case 2:\n    case 4:\n      return \"'у наступний \" + weekday + \" о' p\";\n  }\n}\n\nconst lastWeekFormat = (dirtyDate, baseDate, options) => {\n  const date = toDate(dirtyDate);\n  const day = date.getDay();\n\n  if (isSameWeek(date, baseDate, options)) {\n    return thisWeek(day);\n  } else {\n    return lastWeek(day);\n  }\n};\n\nconst nextWeekFormat = (dirtyDate, baseDate, options) => {\n  const date = toDate(dirtyDate);\n  const day = date.getDay();\n  if (isSameWeek(date, baseDate, options)) {\n    return thisWeek(day);\n  } else {\n    return nextWeek(day);\n  }\n};\n\nconst formatRelativeLocale = {\n  lastWeek: lastWeekFormat,\n  yesterday: \"'вчора о' p\",\n  today: \"'сьогодні о' p\",\n  tomorrow: \"'завтра о' p\",\n  nextWeek: nextWeekFormat,\n  other: \"P\",\n};\n\nexport const formatRelative = (token, date, baseDate, options) => {\n  const format = formatRelativeLocale[token];\n\n  if (typeof format === \"function\") {\n    return format(date, baseDate, options);\n  }\n\n  return format;\n};\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,wBAAwB;AACnD,SAASC,MAAM,QAAQ,oBAAoB;AAE3C,MAAMC,kBAAkB,GAAG,CACzB,QAAQ,EACR,WAAW,EACX,UAAU,EACV,QAAQ,EACR,QAAQ,EACR,UAAU,EACV,QAAQ,CACT;AAED,SAASC,QAAQA,CAACC,GAAG,EAAE;EACrB,MAAMC,OAAO,GAAGH,kBAAkB,CAACE,GAAG,CAAC;EAEvC,QAAQA,GAAG;IACT,KAAK,CAAC;IACN,KAAK,CAAC;IACN,KAAK,CAAC;IACN,KAAK,CAAC;MACJ,OAAO,YAAY,GAAGC,OAAO,GAAG,OAAO;IACzC,KAAK,CAAC;IACN,KAAK,CAAC;IACN,KAAK,CAAC;MACJ,OAAO,aAAa,GAAGA,OAAO,GAAG,OAAO;EAC5C;AACF;AAEA,SAASC,QAAQA,CAACF,GAAG,EAAE;EACrB,MAAMC,OAAO,GAAGH,kBAAkB,CAACE,GAAG,CAAC;EAEvC,OAAO,KAAK,GAAGC,OAAO,GAAG,OAAO;AAClC;AAEA,SAASE,QAAQA,CAACH,GAAG,EAAE;EACrB,MAAMC,OAAO,GAAGH,kBAAkB,CAACE,GAAG,CAAC;EAEvC,QAAQA,GAAG;IACT,KAAK,CAAC;IACN,KAAK,CAAC;IACN,KAAK,CAAC;IACN,KAAK,CAAC;MACJ,OAAO,cAAc,GAAGC,OAAO,GAAG,OAAO;IAC3C,KAAK,CAAC;IACN,KAAK,CAAC;IACN,KAAK,CAAC;MACJ,OAAO,eAAe,GAAGA,OAAO,GAAG,OAAO;EAC9C;AACF;AAEA,MAAMG,cAAc,GAAGA,CAACC,SAAS,EAAEC,QAAQ,EAAEC,OAAO,KAAK;EACvD,MAAMC,IAAI,GAAGX,MAAM,CAACQ,SAAS,CAAC;EAC9B,MAAML,GAAG,GAAGQ,IAAI,CAACC,MAAM,CAAC,CAAC;EAEzB,IAAIb,UAAU,CAACY,IAAI,EAAEF,QAAQ,EAAEC,OAAO,CAAC,EAAE;IACvC,OAAOL,QAAQ,CAACF,GAAG,CAAC;EACtB,CAAC,MAAM;IACL,OAAOD,QAAQ,CAACC,GAAG,CAAC;EACtB;AACF,CAAC;AAED,MAAMU,cAAc,GAAGA,CAACL,SAAS,EAAEC,QAAQ,EAAEC,OAAO,KAAK;EACvD,MAAMC,IAAI,GAAGX,MAAM,CAACQ,SAAS,CAAC;EAC9B,MAAML,GAAG,GAAGQ,IAAI,CAACC,MAAM,CAAC,CAAC;EACzB,IAAIb,UAAU,CAACY,IAAI,EAAEF,QAAQ,EAAEC,OAAO,CAAC,EAAE;IACvC,OAAOL,QAAQ,CAACF,GAAG,CAAC;EACtB,CAAC,MAAM;IACL,OAAOG,QAAQ,CAACH,GAAG,CAAC;EACtB;AACF,CAAC;AAED,MAAMW,oBAAoB,GAAG;EAC3BZ,QAAQ,EAAEK,cAAc;EACxBQ,SAAS,EAAE,aAAa;EACxBC,KAAK,EAAE,gBAAgB;EACvBC,QAAQ,EAAE,cAAc;EACxBX,QAAQ,EAAEO,cAAc;EACxBK,KAAK,EAAE;AACT,CAAC;AAED,OAAO,MAAMC,cAAc,GAAGA,CAACC,KAAK,EAAET,IAAI,EAAEF,QAAQ,EAAEC,OAAO,KAAK;EAChE,MAAMW,MAAM,GAAGP,oBAAoB,CAACM,KAAK,CAAC;EAE1C,IAAI,OAAOC,MAAM,KAAK,UAAU,EAAE;IAChC,OAAOA,MAAM,CAACV,IAAI,EAAEF,QAAQ,EAAEC,OAAO,CAAC;EACxC;EAEA,OAAOW,MAAM;AACf,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
﻿using Microsoft.EntityFrameworkCore;
using MyApi.Models;

namespace MyApi.Data
{
    public class AppDbContext : DbContext
    {
        public AppDbContext(DbContextOptions<AppDbContext> options)
        : base(options) { }

        public DbSet<User> Users => Set<User>();
        public DbSet<Role> Roles => Set<Role>();
        public DbSet<UserRole> UserRoles => Set<UserRole>();
        public DbSet<Menu> Menus => Set<Menu>();
        public DbSet<MenuGroup> MenuGroups => Set<MenuGroup>();
        public DbSet<Patient> Patients => Set<Patient>();
        public DbSet<Treatment> Treatments => Set<Treatment>();
        public DbSet<Receipt> Receipts => Set<Receipt>();
        public DbSet<DataType> DataTypes => Set<DataType>();
        public DbSet<DataTypeGroup> DataTypeGroups => Set<DataTypeGroup>();
        public DbSet<Schedule> Schedules => Set<Schedule>();
        public DbSet<TreatmentDiscomfortArea> TreatmentDiscomfortAreas => Set<TreatmentDiscomfortArea>();
        public DbSet<IpBlock> IpBlocks => Set<IpBlock>();
        public DbSet<UserLoginLog> UserLoginLogs => Set<UserLoginLog>();

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            modelBuilder.Entity<UserRole>()
                .HasKey(ur => new { ur.UserId, ur.RoleId });

            modelBuilder.Entity<UserRole>()
                .HasOne(ur => ur.User)
                .WithMany(u => u.UserRoles)
                .HasForeignKey(ur => ur.UserId);

            modelBuilder.Entity<UserRole>()
                .HasOne(ur => ur.Role)
                .WithMany(r => r.UserRoles)
                .HasForeignKey(ur => ur.RoleId);

            // enum 轉 string 存入資料庫
            modelBuilder.Entity<Patient>()
                .Property(p => p.Gender)
                .HasConversion<string>();

            modelBuilder.Entity<Treatment>()
                .Property(t => t.Step)
                .HasConversion<string>();

            modelBuilder.Entity<Treatment>()
                .HasKey(t => t.Id); 

            modelBuilder.Entity<Treatment>()
                .HasOne(t => t.User)
                .WithMany(u => u.Treatments)
                .HasForeignKey(t => t.UserId);

            modelBuilder.Entity<Treatment>()
                .HasOne(t => t.Patient)
                .WithMany(p => p.Treatments)
                .HasForeignKey(t => t.PatientId);

            modelBuilder.Entity<Receipt>()
                .HasKey(t => t.Id);

            modelBuilder.Entity<Receipt>()
                .HasOne(t => t.Treatment)
                .WithMany(u => u.Receipts)
                .HasForeignKey(t => t.TreatmentId);

            modelBuilder.Entity<Receipt>()
                .HasOne(t => t.Patient)
                .WithMany(p => p.Receipts)
                .HasForeignKey(t => t.PatientId);

            // Schedule 配置
            modelBuilder.Entity<Schedule>()
                .HasKey(s => s.Id);

            modelBuilder.Entity<Schedule>()
                .HasOne(s => s.Doctor)
                .WithMany()
                .HasForeignKey(s => s.DoctorId)
                .OnDelete(DeleteBehavior.Restrict);

            modelBuilder.Entity<Schedule>()
                .HasOne(s => s.Patient)
                .WithMany()
                .HasForeignKey(s => s.PatientId)
                .OnDelete(DeleteBehavior.Restrict);

            modelBuilder.Entity<Schedule>()
                .HasOne(s => s.Creator)
                .WithMany()
                .HasForeignKey(s => s.CreatedBy)
                .OnDelete(DeleteBehavior.Restrict);

            modelBuilder.Entity<Schedule>()
                .HasOne(s => s.Updater)
                .WithMany()
                .HasForeignKey(s => s.UpdatedBy)
                .OnDelete(DeleteBehavior.Restrict);

            // TreatmentDiscomfortArea 關聯配置
            modelBuilder.Entity<TreatmentDiscomfortArea>()
                .HasOne(tda => tda.Treatment)
                .WithMany(t => t.DiscomfortAreas)
                .HasForeignKey(tda => tda.TreatmentId)
                .OnDelete(DeleteBehavior.Cascade);

        }

    }
}

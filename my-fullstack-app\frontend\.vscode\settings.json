{"editor.formatOnSave": true, "editor.defaultFormatter": "esbenp.prettier-vscode", "editor.codeActionsOnSave": {"source.fixAll.eslint": "explicit", "source.organizeImports": "explicit"}, "typescript.preferences.importModuleSpecifier": "relative", "typescript.suggest.autoImports": true, "typescript.updateImportsOnFileMove.enabled": "always", "emmet.includeLanguages": {"typescript": "html", "typescriptreact": "html"}, "files.associations": {"*.tsx": "typescriptreact", "*.ts": "typescript"}, "search.exclude": {"**/node_modules": true, "**/build": true, "**/dist": true, "**/.git": true, "**/coverage": true}, "files.exclude": {"**/node_modules": true, "**/build": true, "**/dist": true, "**/.git": true, "**/coverage": true}, "typescript.preferences.quoteStyle": "single", "javascript.preferences.quoteStyle": "single", "prettier.requireConfig": true, "eslint.validate": ["javascript", "javascriptreact", "typescript", "typescriptreact"], "eslint.format.enable": false}
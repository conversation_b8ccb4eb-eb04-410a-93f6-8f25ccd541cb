{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\demo-git\\\\demo-react\\\\my-fullstack-app\\\\frontend\\\\src\\\\components\\\\Page\\\\PermissionPage.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useCallback, useRef } from 'react';\nimport { Dropdown } from 'primereact/dropdown';\nimport { Checkbox } from 'primereact/checkbox';\nimport { Button } from 'primereact/button';\nimport { Toast } from 'primereact/toast';\nimport { Card } from 'primereact/card';\nimport { PermissionApi } from '../../services/apiService'; // 導入新的 PermissionApi\n\n// 導入 Permission 類型\nimport { UserApi } from '../../services/apiService'; // 導入 UserApi 以獲取角色列表\n\n// Type Definitions\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst PermissionPage = () => {\n  _s();\n  const toast = useRef(null);\n  const [roles, setRoles] = useState([]);\n  const [selectedRole, setSelectedRole] = useState(null);\n  const [allPermissions, setAllPermissions] = useState([]); // 所有可用的細粒度權限\n  const [rolePermissions, setRolePermissions] = useState([]); // 當前角色擁有的權限代碼列表\n  const [loading, setLoading] = useState(false);\n\n  // 獲取所有角色\n  const fetchRoles = useCallback(async () => {\n    try {\n      // 假設獲取角色列表的 API 在 UserApi 中\n      const response = await UserApi.getRoles();\n      setRoles(response.data.map(r => ({\n        id: r.id,\n        name: r.name\n      })));\n    } catch (error) {\n      var _toast$current, _error$response, _error$response$data;\n      (_toast$current = toast.current) === null || _toast$current === void 0 ? void 0 : _toast$current.show({\n        severity: 'error',\n        summary: '獲取角色列表失敗',\n        detail: ((_error$response = error.response) === null || _error$response === void 0 ? void 0 : (_error$response$data = _error$response.data) === null || _error$response$data === void 0 ? void 0 : _error$response$data.message) || error.message\n      });\n    }\n  }, []);\n\n  // 獲取所有權限定義\n  const fetchAllPermissions = useCallback(async () => {\n    try {\n      const permissions = await PermissionApi.getAllPermissions();\n      setAllPermissions(permissions);\n    } catch (error) {\n      var _toast$current2, _error$response2, _error$response2$data;\n      (_toast$current2 = toast.current) === null || _toast$current2 === void 0 ? void 0 : _toast$current2.show({\n        severity: 'error',\n        summary: '獲取所有權限失敗',\n        detail: ((_error$response2 = error.response) === null || _error$response2 === void 0 ? void 0 : (_error$response2$data = _error$response2.data) === null || _error$response2$data === void 0 ? void 0 : _error$response2$data.message) || error.message\n      });\n    }\n  }, []);\n\n  // 獲取指定角色的權限\n  const fetchRolePermissions = useCallback(async roleId => {\n    setLoading(true);\n    try {\n      const permissions = await PermissionApi.getRolePermissions(roleId);\n      setRolePermissions(permissions);\n    } catch (error) {\n      var _toast$current3, _error$response3, _error$response3$data;\n      (_toast$current3 = toast.current) === null || _toast$current3 === void 0 ? void 0 : _toast$current3.show({\n        severity: 'error',\n        summary: '獲取角色權限失敗',\n        detail: ((_error$response3 = error.response) === null || _error$response3 === void 0 ? void 0 : (_error$response3$data = _error$response3.data) === null || _error$response3$data === void 0 ? void 0 : _error$response3$data.message) || error.message\n      });\n      setRolePermissions([]);\n    } finally {\n      setLoading(false);\n    }\n  }, []);\n  useEffect(() => {\n    fetchRoles();\n    fetchAllPermissions(); // 頁面載入時獲取所有權限定義\n  }, [fetchRoles, fetchAllPermissions]);\n  useEffect(() => {\n    if (selectedRole) {\n      fetchRolePermissions(selectedRole);\n    } else {\n      setRolePermissions([]);\n    }\n  }, [selectedRole, fetchRolePermissions]);\n  const handlePermissionChange = (permissionCode, isChecked) => {\n    setRolePermissions(prevCodes => {\n      if (isChecked) {\n        return [...prevCodes, permissionCode];\n      } else {\n        return prevCodes.filter(code => code !== permissionCode);\n      }\n    });\n  };\n  const handleSaveChanges = async () => {\n    if (!selectedRole) return;\n    setLoading(true);\n    try {\n      var _toast$current4;\n      await PermissionApi.updateRolePermissions(selectedRole, rolePermissions);\n      (_toast$current4 = toast.current) === null || _toast$current4 === void 0 ? void 0 : _toast$current4.show({\n        severity: 'success',\n        summary: '權限更新成功',\n        detail: \"\"\n      });\n      fetchRolePermissions(selectedRole); // 重新獲取以確保狀態同步\n    } catch (error) {\n      var _toast$current5, _error$response4, _error$response4$data;\n      (_toast$current5 = toast.current) === null || _toast$current5 === void 0 ? void 0 : _toast$current5.show({\n        severity: 'error',\n        summary: '權限更新失敗',\n        detail: ((_error$response4 = error.response) === null || _error$response4 === void 0 ? void 0 : (_error$response4$data = _error$response4.data) === null || _error$response4$data === void 0 ? void 0 : _error$response4$data.message) || error.message\n      });\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 將權限按類別分組\n  const groupedPermissions = allPermissions.reduce((acc, permission) => {\n    const category = permission.Category || '未分類';\n    if (!acc[category]) {\n      acc[category] = [];\n    }\n    acc[category].push(permission);\n    return acc;\n  }, {});\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"p-grid p-fluid\",\n    children: [/*#__PURE__*/_jsxDEV(Toast, {\n      ref: toast\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 115,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"p-col-12\",\n      children: /*#__PURE__*/_jsxDEV(Card, {\n        title: \"\\u6B0A\\u9650\\u7BA1\\u7406\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-field p-grid mb-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            htmlFor: \"role\",\n            className: \"p-col-12 p-md-2\",\n            children: \"\\u9078\\u64C7\\u89D2\\u8272\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 119,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"p-col-12 p-md-10\",\n            children: /*#__PURE__*/_jsxDEV(Dropdown, {\n              id: \"role\",\n              value: selectedRole,\n              options: roles,\n              onChange: e => setSelectedRole(e.value),\n              optionLabel: \"name\",\n              optionValue: \"id\",\n              placeholder: \"\\u8ACB\\u9078\\u64C7\\u4E00\\u500B\\u89D2\\u8272\",\n              style: {\n                width: '100%'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 121,\n              columnNumber: 29\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 120,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 118,\n          columnNumber: 21\n        }, this), selectedRole && Object.keys(groupedPermissions).map(category => /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-field mb-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n            className: \"text-lg font-bold mb-2\",\n            children: category\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 136,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"p-grid\",\n            children: groupedPermissions[category].map(permission => /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"p-col-12 p-md-4\",\n              children: [/*#__PURE__*/_jsxDEV(Checkbox, {\n                inputId: permission.Code,\n                checked: rolePermissions.includes(permission.Code),\n                onChange: e => handlePermissionChange(permission.Code, !!e.checked)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 140,\n                columnNumber: 41\n              }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                htmlFor: permission.Code,\n                className: \"p-ml-2\",\n                children: [permission.Name, \" (\", permission.Code, \")\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 145,\n                columnNumber: 41\n              }, this)]\n            }, permission.Code, true, {\n              fileName: _jsxFileName,\n              lineNumber: 139,\n              columnNumber: 37\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 137,\n            columnNumber: 29\n          }, this)]\n        }, category, true, {\n          fileName: _jsxFileName,\n          lineNumber: 135,\n          columnNumber: 25\n        }, this)), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-d-flex p-jc-end mt-4\",\n          children: /*#__PURE__*/_jsxDEV(Button, {\n            label: \"\\u5132\\u5B58\\u8B8A\\u66F4\",\n            icon: \"pi pi-check\",\n            onClick: handleSaveChanges,\n            disabled: !selectedRole || loading,\n            loading: loading\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 155,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 154,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 117,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 116,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 114,\n    columnNumber: 9\n  }, this);\n};\n_s(PermissionPage, \"YhPLH0/PtmZCW3c9iF+aFrqI6aw=\");\n_c = PermissionPage;\nexport default PermissionPage;\nvar _c;\n$RefreshReg$(_c, \"PermissionPage\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useCallback", "useRef", "Dropdown", "Checkbox", "<PERSON><PERSON>", "Toast", "Card", "PermissionApi", "UserApi", "jsxDEV", "_jsxDEV", "PermissionPage", "_s", "toast", "roles", "setRoles", "selectedR<PERSON>", "setSelectedRole", "allPermissions", "setAllPermissions", "rolePermissions", "setRolePermissions", "loading", "setLoading", "fetchRoles", "response", "getRoles", "data", "map", "r", "id", "name", "error", "_toast$current", "_error$response", "_error$response$data", "current", "show", "severity", "summary", "detail", "message", "fetchAllPermissions", "permissions", "getAllPermissions", "_toast$current2", "_error$response2", "_error$response2$data", "fetchRolePermissions", "roleId", "getRolePermissions", "_toast$current3", "_error$response3", "_error$response3$data", "handlePermissionChange", "permissionCode", "isChecked", "prevCodes", "filter", "code", "handleSaveChanges", "_toast$current4", "updateRolePermissions", "_toast$current5", "_error$response4", "_error$response4$data", "groupedPermissions", "reduce", "acc", "permission", "category", "Category", "push", "className", "children", "ref", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "title", "htmlFor", "value", "options", "onChange", "e", "optionLabel", "optionValue", "placeholder", "style", "width", "Object", "keys", "inputId", "Code", "checked", "includes", "Name", "label", "icon", "onClick", "disabled", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/demo-git/demo-react/my-fullstack-app/frontend/src/components/Page/PermissionPage.tsx"], "sourcesContent": ["import React, { useState, useEffect, useCallback, useRef } from 'react';\nimport { Dropdown, DropdownChangeEvent } from 'primereact/dropdown';\nimport { Checkbox, CheckboxChangeEvent } from 'primereact/checkbox';\nimport { Button } from 'primereact/button';\nimport { Toast } from 'primereact/toast';\nimport { Card } from 'primereact/card';\nimport { classNames } from 'primereact/utils';\nimport { PermissionApi } from '../../services/apiService'; // 導入新的 PermissionApi\nimport { Permission } from '../../types/api'; // 導入 Permission 類型\nimport { UserApi } from '../../services/apiService'; // 導入 UserApi 以獲取角色列表\n\n// Type Definitions\ninterface Role {\n    id: number;\n    name: string;\n}\n\ninterface GroupedPermissions {\n    [category: string]: Permission[];\n}\n\nconst PermissionPage: React.FC = () => {\n    const toast = useRef<Toast>(null);\n    const [roles, setRoles] = useState<Role[]>([]);\n    const [selectedRole, setSelectedRole] = useState<number | null>(null);\n    const [allPermissions, setAllPermissions] = useState<Permission[]>([]); // 所有可用的細粒度權限\n    const [rolePermissions, setRolePermissions] = useState<string[]>([]); // 當前角色擁有的權限代碼列表\n    const [loading, setLoading] = useState(false);\n\n    // 獲取所有角色\n    const fetchRoles = useCallback(async () => {\n        try {\n            // 假設獲取角色列表的 API 在 UserApi 中\n            const response = await UserApi.getRoles(); \n            setRoles(response.data.map((r: any) => ({ id: r.id, name: r.name })));\n        } catch (error: any) {\n            toast.current?.show({ severity: 'error', summary: '獲取角色列表失敗', detail: error.response?.data?.message || error.message });\n        }\n    }, []);\n\n    // 獲取所有權限定義\n    const fetchAllPermissions = useCallback(async () => {\n        try {\n            const permissions = await PermissionApi.getAllPermissions();\n            setAllPermissions(permissions);\n        } catch (error: any) {\n            toast.current?.show({ severity: 'error', summary: '獲取所有權限失敗', detail: error.response?.data?.message || error.message });\n        }\n    }, []);\n\n    // 獲取指定角色的權限\n    const fetchRolePermissions = useCallback(async (roleId: number) => {\n        setLoading(true);\n        try {\n            const permissions = await PermissionApi.getRolePermissions(roleId);\n            setRolePermissions(permissions);\n        } catch (error: any) {\n            toast.current?.show({ severity: 'error', summary: '獲取角色權限失敗', detail: error.response?.data?.message || error.message });\n            setRolePermissions([]);\n        } finally {\n            setLoading(false);\n        }\n    }, []);\n\n    useEffect(() => {\n        fetchRoles();\n        fetchAllPermissions(); // 頁面載入時獲取所有權限定義\n    }, [fetchRoles, fetchAllPermissions]);\n\n    useEffect(() => {\n        if (selectedRole) {\n            fetchRolePermissions(selectedRole);\n        } else {\n            setRolePermissions([]);\n        }\n    }, [selectedRole, fetchRolePermissions]);\n\n    const handlePermissionChange = (permissionCode: string, isChecked: boolean) => {\n        setRolePermissions(prevCodes => {\n            if (isChecked) {\n                return [...prevCodes, permissionCode];\n            } else {\n                return prevCodes.filter(code => code !== permissionCode);\n            }\n        });\n    };\n    \n    const handleSaveChanges = async () => {\n        if (!selectedRole) return;\n        setLoading(true);\n\n        try {\n            await PermissionApi.updateRolePermissions(selectedRole, rolePermissions);\n            toast.current?.show({ severity: 'success', summary: '權限更新成功', detail: \"\" });\n            fetchRolePermissions(selectedRole); // 重新獲取以確保狀態同步\n        } catch (error: any) {\n            toast.current?.show({ severity: 'error', summary: '權限更新失敗', detail: error.response?.data?.message || error.message });\n        } finally {\n            setLoading(false);\n        }\n    };\n\n    // 將權限按類別分組\n    const groupedPermissions = allPermissions.reduce<GroupedPermissions>((acc, permission) => {\n        const category = permission.Category || '未分類';\n        if (!acc[category]) {\n            acc[category] = [];\n        }\n        acc[category].push(permission);\n        return acc;\n    }, {});\n\n    return (\n        <div className=\"p-grid p-fluid\">\n            <Toast ref={toast} />\n            <div className=\"p-col-12\">\n                <Card title=\"權限管理\">\n                    <div className=\"p-field p-grid mb-4\">\n                        <label htmlFor=\"role\" className=\"p-col-12 p-md-2\">選擇角色</label>\n                        <div className=\"p-col-12 p-md-10\">\n                            <Dropdown\n                                id=\"role\"\n                                value={selectedRole}\n                                options={roles}\n                                onChange={(e: DropdownChangeEvent) => setSelectedRole(e.value)}\n                                optionLabel=\"name\"\n                                optionValue=\"id\"\n                                placeholder=\"請選擇一個角色\"\n                                style={{ width: '100%' }}\n                            />\n                        </div>\n                    </div>\n\n                    {selectedRole && Object.keys(groupedPermissions).map(category => (\n                        <div key={category} className=\"p-field mb-4\">\n                            <h5 className=\"text-lg font-bold mb-2\">{category}</h5>\n                            <div className=\"p-grid\">\n                                {groupedPermissions[category].map(permission => (\n                                    <div key={permission.Code} className=\"p-col-12 p-md-4\">\n                                        <Checkbox\n                                            inputId={permission.Code}\n                                            checked={rolePermissions.includes(permission.Code)}\n                                            onChange={(e: CheckboxChangeEvent) => handlePermissionChange(permission.Code, !!e.checked)}\n                                        />\n                                        <label htmlFor={permission.Code} className=\"p-ml-2\">\n                                            {permission.Name} ({permission.Code})\n                                        </label>\n                                    </div>\n                                ))}\n                            </div>\n                        </div>\n                    ))}\n\n                    <div className=\"p-d-flex p-jc-end mt-4\">\n                        <Button\n                            label=\"儲存變更\"\n                            icon=\"pi pi-check\"\n                            onClick={handleSaveChanges}\n                            disabled={!selectedRole || loading}\n                            loading={loading}\n                        />\n                    </div>\n                </Card>\n            </div>\n        </div>\n    );\n};\n\nexport default PermissionPage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,WAAW,EAAEC,MAAM,QAAQ,OAAO;AACvE,SAASC,QAAQ,QAA6B,qBAAqB;AACnE,SAASC,QAAQ,QAA6B,qBAAqB;AACnE,SAASC,MAAM,QAAQ,mBAAmB;AAC1C,SAASC,KAAK,QAAQ,kBAAkB;AACxC,SAASC,IAAI,QAAQ,iBAAiB;AAEtC,SAASC,aAAa,QAAQ,2BAA2B,CAAC,CAAC;;AACb;AAC9C,SAASC,OAAO,QAAQ,2BAA2B,CAAC,CAAC;;AAErD;AAAA,SAAAC,MAAA,IAAAC,OAAA;AAUA,MAAMC,cAAwB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACnC,MAAMC,KAAK,GAAGZ,MAAM,CAAQ,IAAI,CAAC;EACjC,MAAM,CAACa,KAAK,EAAEC,QAAQ,CAAC,GAAGjB,QAAQ,CAAS,EAAE,CAAC;EAC9C,MAAM,CAACkB,YAAY,EAAEC,eAAe,CAAC,GAAGnB,QAAQ,CAAgB,IAAI,CAAC;EACrE,MAAM,CAACoB,cAAc,EAAEC,iBAAiB,CAAC,GAAGrB,QAAQ,CAAe,EAAE,CAAC,CAAC,CAAC;EACxE,MAAM,CAACsB,eAAe,EAAEC,kBAAkB,CAAC,GAAGvB,QAAQ,CAAW,EAAE,CAAC,CAAC,CAAC;EACtE,MAAM,CAACwB,OAAO,EAAEC,UAAU,CAAC,GAAGzB,QAAQ,CAAC,KAAK,CAAC;;EAE7C;EACA,MAAM0B,UAAU,GAAGxB,WAAW,CAAC,YAAY;IACvC,IAAI;MACA;MACA,MAAMyB,QAAQ,GAAG,MAAMjB,OAAO,CAACkB,QAAQ,CAAC,CAAC;MACzCX,QAAQ,CAACU,QAAQ,CAACE,IAAI,CAACC,GAAG,CAAEC,CAAM,KAAM;QAAEC,EAAE,EAAED,CAAC,CAACC,EAAE;QAAEC,IAAI,EAAEF,CAAC,CAACE;MAAK,CAAC,CAAC,CAAC,CAAC;IACzE,CAAC,CAAC,OAAOC,KAAU,EAAE;MAAA,IAAAC,cAAA,EAAAC,eAAA,EAAAC,oBAAA;MACjB,CAAAF,cAAA,GAAApB,KAAK,CAACuB,OAAO,cAAAH,cAAA,uBAAbA,cAAA,CAAeI,IAAI,CAAC;QAAEC,QAAQ,EAAE,OAAO;QAAEC,OAAO,EAAE,UAAU;QAAEC,MAAM,EAAE,EAAAN,eAAA,GAAAF,KAAK,CAACP,QAAQ,cAAAS,eAAA,wBAAAC,oBAAA,GAAdD,eAAA,CAAgBP,IAAI,cAAAQ,oBAAA,uBAApBA,oBAAA,CAAsBM,OAAO,KAAIT,KAAK,CAACS;MAAQ,CAAC,CAAC;IAC3H;EACJ,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMC,mBAAmB,GAAG1C,WAAW,CAAC,YAAY;IAChD,IAAI;MACA,MAAM2C,WAAW,GAAG,MAAMpC,aAAa,CAACqC,iBAAiB,CAAC,CAAC;MAC3DzB,iBAAiB,CAACwB,WAAW,CAAC;IAClC,CAAC,CAAC,OAAOX,KAAU,EAAE;MAAA,IAAAa,eAAA,EAAAC,gBAAA,EAAAC,qBAAA;MACjB,CAAAF,eAAA,GAAAhC,KAAK,CAACuB,OAAO,cAAAS,eAAA,uBAAbA,eAAA,CAAeR,IAAI,CAAC;QAAEC,QAAQ,EAAE,OAAO;QAAEC,OAAO,EAAE,UAAU;QAAEC,MAAM,EAAE,EAAAM,gBAAA,GAAAd,KAAK,CAACP,QAAQ,cAAAqB,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBnB,IAAI,cAAAoB,qBAAA,uBAApBA,qBAAA,CAAsBN,OAAO,KAAIT,KAAK,CAACS;MAAQ,CAAC,CAAC;IAC3H;EACJ,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMO,oBAAoB,GAAGhD,WAAW,CAAC,MAAOiD,MAAc,IAAK;IAC/D1B,UAAU,CAAC,IAAI,CAAC;IAChB,IAAI;MACA,MAAMoB,WAAW,GAAG,MAAMpC,aAAa,CAAC2C,kBAAkB,CAACD,MAAM,CAAC;MAClE5B,kBAAkB,CAACsB,WAAW,CAAC;IACnC,CAAC,CAAC,OAAOX,KAAU,EAAE;MAAA,IAAAmB,eAAA,EAAAC,gBAAA,EAAAC,qBAAA;MACjB,CAAAF,eAAA,GAAAtC,KAAK,CAACuB,OAAO,cAAAe,eAAA,uBAAbA,eAAA,CAAed,IAAI,CAAC;QAAEC,QAAQ,EAAE,OAAO;QAAEC,OAAO,EAAE,UAAU;QAAEC,MAAM,EAAE,EAAAY,gBAAA,GAAApB,KAAK,CAACP,QAAQ,cAAA2B,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBzB,IAAI,cAAA0B,qBAAA,uBAApBA,qBAAA,CAAsBZ,OAAO,KAAIT,KAAK,CAACS;MAAQ,CAAC,CAAC;MACvHpB,kBAAkB,CAAC,EAAE,CAAC;IAC1B,CAAC,SAAS;MACNE,UAAU,CAAC,KAAK,CAAC;IACrB;EACJ,CAAC,EAAE,EAAE,CAAC;EAENxB,SAAS,CAAC,MAAM;IACZyB,UAAU,CAAC,CAAC;IACZkB,mBAAmB,CAAC,CAAC,CAAC,CAAC;EAC3B,CAAC,EAAE,CAAClB,UAAU,EAAEkB,mBAAmB,CAAC,CAAC;EAErC3C,SAAS,CAAC,MAAM;IACZ,IAAIiB,YAAY,EAAE;MACdgC,oBAAoB,CAAChC,YAAY,CAAC;IACtC,CAAC,MAAM;MACHK,kBAAkB,CAAC,EAAE,CAAC;IAC1B;EACJ,CAAC,EAAE,CAACL,YAAY,EAAEgC,oBAAoB,CAAC,CAAC;EAExC,MAAMM,sBAAsB,GAAGA,CAACC,cAAsB,EAAEC,SAAkB,KAAK;IAC3EnC,kBAAkB,CAACoC,SAAS,IAAI;MAC5B,IAAID,SAAS,EAAE;QACX,OAAO,CAAC,GAAGC,SAAS,EAAEF,cAAc,CAAC;MACzC,CAAC,MAAM;QACH,OAAOE,SAAS,CAACC,MAAM,CAACC,IAAI,IAAIA,IAAI,KAAKJ,cAAc,CAAC;MAC5D;IACJ,CAAC,CAAC;EACN,CAAC;EAED,MAAMK,iBAAiB,GAAG,MAAAA,CAAA,KAAY;IAClC,IAAI,CAAC5C,YAAY,EAAE;IACnBO,UAAU,CAAC,IAAI,CAAC;IAEhB,IAAI;MAAA,IAAAsC,eAAA;MACA,MAAMtD,aAAa,CAACuD,qBAAqB,CAAC9C,YAAY,EAAEI,eAAe,CAAC;MACxE,CAAAyC,eAAA,GAAAhD,KAAK,CAACuB,OAAO,cAAAyB,eAAA,uBAAbA,eAAA,CAAexB,IAAI,CAAC;QAAEC,QAAQ,EAAE,SAAS;QAAEC,OAAO,EAAE,QAAQ;QAAEC,MAAM,EAAE;MAAG,CAAC,CAAC;MAC3EQ,oBAAoB,CAAChC,YAAY,CAAC,CAAC,CAAC;IACxC,CAAC,CAAC,OAAOgB,KAAU,EAAE;MAAA,IAAA+B,eAAA,EAAAC,gBAAA,EAAAC,qBAAA;MACjB,CAAAF,eAAA,GAAAlD,KAAK,CAACuB,OAAO,cAAA2B,eAAA,uBAAbA,eAAA,CAAe1B,IAAI,CAAC;QAAEC,QAAQ,EAAE,OAAO;QAAEC,OAAO,EAAE,QAAQ;QAAEC,MAAM,EAAE,EAAAwB,gBAAA,GAAAhC,KAAK,CAACP,QAAQ,cAAAuC,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBrC,IAAI,cAAAsC,qBAAA,uBAApBA,qBAAA,CAAsBxB,OAAO,KAAIT,KAAK,CAACS;MAAQ,CAAC,CAAC;IACzH,CAAC,SAAS;MACNlB,UAAU,CAAC,KAAK,CAAC;IACrB;EACJ,CAAC;;EAED;EACA,MAAM2C,kBAAkB,GAAGhD,cAAc,CAACiD,MAAM,CAAqB,CAACC,GAAG,EAAEC,UAAU,KAAK;IACtF,MAAMC,QAAQ,GAAGD,UAAU,CAACE,QAAQ,IAAI,KAAK;IAC7C,IAAI,CAACH,GAAG,CAACE,QAAQ,CAAC,EAAE;MAChBF,GAAG,CAACE,QAAQ,CAAC,GAAG,EAAE;IACtB;IACAF,GAAG,CAACE,QAAQ,CAAC,CAACE,IAAI,CAACH,UAAU,CAAC;IAC9B,OAAOD,GAAG;EACd,CAAC,EAAE,CAAC,CAAC,CAAC;EAEN,oBACI1D,OAAA;IAAK+D,SAAS,EAAC,gBAAgB;IAAAC,QAAA,gBAC3BhE,OAAA,CAACL,KAAK;MAACsE,GAAG,EAAE9D;IAAM;MAAA+D,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACrBrE,OAAA;MAAK+D,SAAS,EAAC,UAAU;MAAAC,QAAA,eACrBhE,OAAA,CAACJ,IAAI;QAAC0E,KAAK,EAAC,0BAAM;QAAAN,QAAA,gBACdhE,OAAA;UAAK+D,SAAS,EAAC,qBAAqB;UAAAC,QAAA,gBAChChE,OAAA;YAAOuE,OAAO,EAAC,MAAM;YAACR,SAAS,EAAC,iBAAiB;YAAAC,QAAA,EAAC;UAAI;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAC9DrE,OAAA;YAAK+D,SAAS,EAAC,kBAAkB;YAAAC,QAAA,eAC7BhE,OAAA,CAACR,QAAQ;cACL4B,EAAE,EAAC,MAAM;cACToD,KAAK,EAAElE,YAAa;cACpBmE,OAAO,EAAErE,KAAM;cACfsE,QAAQ,EAAGC,CAAsB,IAAKpE,eAAe,CAACoE,CAAC,CAACH,KAAK,CAAE;cAC/DI,WAAW,EAAC,MAAM;cAClBC,WAAW,EAAC,IAAI;cAChBC,WAAW,EAAC,4CAAS;cACrBC,KAAK,EAAE;gBAAEC,KAAK,EAAE;cAAO;YAAE;cAAAd,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5B;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,EAEL/D,YAAY,IAAI2E,MAAM,CAACC,IAAI,CAAC1B,kBAAkB,CAAC,CAACtC,GAAG,CAAC0C,QAAQ,iBACzD5D,OAAA;UAAoB+D,SAAS,EAAC,cAAc;UAAAC,QAAA,gBACxChE,OAAA;YAAI+D,SAAS,EAAC,wBAAwB;YAAAC,QAAA,EAAEJ;UAAQ;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACtDrE,OAAA;YAAK+D,SAAS,EAAC,QAAQ;YAAAC,QAAA,EAClBR,kBAAkB,CAACI,QAAQ,CAAC,CAAC1C,GAAG,CAACyC,UAAU,iBACxC3D,OAAA;cAA2B+D,SAAS,EAAC,iBAAiB;cAAAC,QAAA,gBAClDhE,OAAA,CAACP,QAAQ;gBACL0F,OAAO,EAAExB,UAAU,CAACyB,IAAK;gBACzBC,OAAO,EAAE3E,eAAe,CAAC4E,QAAQ,CAAC3B,UAAU,CAACyB,IAAI,CAAE;gBACnDV,QAAQ,EAAGC,CAAsB,IAAK/B,sBAAsB,CAACe,UAAU,CAACyB,IAAI,EAAE,CAAC,CAACT,CAAC,CAACU,OAAO;cAAE;gBAAAnB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9F,CAAC,eACFrE,OAAA;gBAAOuE,OAAO,EAAEZ,UAAU,CAACyB,IAAK;gBAACrB,SAAS,EAAC,QAAQ;gBAAAC,QAAA,GAC9CL,UAAU,CAAC4B,IAAI,EAAC,IAAE,EAAC5B,UAAU,CAACyB,IAAI,EAAC,GACxC;cAAA;gBAAAlB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA,GARFV,UAAU,CAACyB,IAAI;cAAAlB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OASpB,CACR;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC;QAAA,GAfAT,QAAQ;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAgBb,CACR,CAAC,eAEFrE,OAAA;UAAK+D,SAAS,EAAC,wBAAwB;UAAAC,QAAA,eACnChE,OAAA,CAACN,MAAM;YACH8F,KAAK,EAAC,0BAAM;YACZC,IAAI,EAAC,aAAa;YAClBC,OAAO,EAAExC,iBAAkB;YAC3ByC,QAAQ,EAAE,CAACrF,YAAY,IAAIM,OAAQ;YACnCA,OAAO,EAAEA;UAAQ;YAAAsD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEd,CAAC;AAACnE,EAAA,CAjJID,cAAwB;AAAA2F,EAAA,GAAxB3F,cAAwB;AAmJ9B,eAAeA,cAAc;AAAC,IAAA2F,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
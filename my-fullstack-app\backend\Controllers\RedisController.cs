﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore.Metadata.Internal;
using MyApi.Services;
using Newtonsoft.Json.Linq;
using System;
using System.Threading.Tasks;

namespace MyApi.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    [Authorize(Roles = "Admin")]  // 限制管理員角色
    public class RedisController : ControllerBase
    {
        private readonly RedisService _redisService;

        public RedisController(RedisService redisService)
        {
            _redisService = redisService;
        }

        [HttpPost("set")]
        public async Task<IActionResult> Set([FromQuery] string key, [FromQuery] string value, [FromQuery] int? expirySeconds = null)
        {
            if (expirySeconds.HasValue)
            {
                await _redisService.SetStringAsync(key, value, expirySeconds.Value);
            }
            else
            {
                await _redisService.SetStringAsync(key, value);
            }
            return Ok("Stored");
        }

        [HttpPost("set-with-expiry")]
        public async Task<IActionResult> SetWithExpiry([FromQuery] string key, [FromQuery] string value, [FromQuery] int expirySeconds)
        {
            await _redisService.SetStringAsync(key, value, expirySeconds);
            return Ok($"Stored with {expirySeconds} seconds expiry");
        }

        [HttpPost("set-expiry")]
        public async Task<IActionResult> SetExpiry([FromQuery] string key, [FromQuery] int expirySeconds)
        {
            var result = await _redisService.SetExpiryAsync(key, expirySeconds);
            return result ? Ok($"Expiry set to {expirySeconds} seconds") : NotFound("Key not found");
        }

        [HttpGet("get")]
        public async Task<IActionResult> Get([FromQuery] string key)
        {
            var value = await _redisService.GetStringAsync(key);
            return value != null ? Ok(value) : NotFound();
        }

        [HttpDelete("delete")]
        public async Task<IActionResult> Delete([FromQuery] string key)
        {
            var deleted = await _redisService.DeleteKeyAsync(key);
            return deleted ? Ok("Deleted") : NotFound();
        }

        [HttpGet("exists")]
        public async Task<IActionResult> Exists([FromQuery] string key)
        {
            var exists = await _redisService.KeyExistsAsync(key);
            return Ok(new { key, exists });
        }

        [HttpGet("ttl")]
        public async Task<IActionResult> GetTimeToLive([FromQuery] string key)
        {
            var ttl = await _redisService.GetTimeToLiveAsync(key);
            return Ok(new { key, ttl = ttl?.TotalSeconds });
        }

        [HttpPost("persist")]
        public async Task<IActionResult> Persist([FromQuery] string key)
        {
            var result = await _redisService.PersistAsync(key);
            return result ? Ok("Key set to never expire") : NotFound("Key not found");
        }
    }
}

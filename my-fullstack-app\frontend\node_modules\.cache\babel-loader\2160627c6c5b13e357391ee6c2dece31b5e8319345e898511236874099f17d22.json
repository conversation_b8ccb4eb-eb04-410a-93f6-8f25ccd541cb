{"ast": null, "code": "const formatDistanceLocale = {\n  lessThanXSeconds: {\n    one: \"dưới 1 giây\",\n    other: \"dưới {{count}} giây\"\n  },\n  xSeconds: {\n    one: \"1 giây\",\n    other: \"{{count}} giây\"\n  },\n  halfAMinute: \"nửa phút\",\n  lessThanXMinutes: {\n    one: \"dưới 1 phút\",\n    other: \"dưới {{count}} phút\"\n  },\n  xMinutes: {\n    one: \"1 phút\",\n    other: \"{{count}} phút\"\n  },\n  aboutXHours: {\n    one: \"khoảng 1 giờ\",\n    other: \"khoảng {{count}} giờ\"\n  },\n  xHours: {\n    one: \"1 giờ\",\n    other: \"{{count}} giờ\"\n  },\n  xDays: {\n    one: \"1 ngày\",\n    other: \"{{count}} ngày\"\n  },\n  aboutXWeeks: {\n    one: \"khoảng 1 tuần\",\n    other: \"khoảng {{count}} tuần\"\n  },\n  xWeeks: {\n    one: \"1 tuần\",\n    other: \"{{count}} tuần\"\n  },\n  aboutXMonths: {\n    one: \"khoảng 1 tháng\",\n    other: \"khoảng {{count}} tháng\"\n  },\n  xMonths: {\n    one: \"1 tháng\",\n    other: \"{{count}} tháng\"\n  },\n  aboutXYears: {\n    one: \"khoảng 1 năm\",\n    other: \"khoảng {{count}} năm\"\n  },\n  xYears: {\n    one: \"1 năm\",\n    other: \"{{count}} năm\"\n  },\n  overXYears: {\n    one: \"hơn 1 năm\",\n    other: \"hơn {{count}} năm\"\n  },\n  almostXYears: {\n    one: \"gần 1 năm\",\n    other: \"gần {{count}} năm\"\n  }\n};\nexport const formatDistance = (token, count, options) => {\n  let result;\n  const tokenValue = formatDistanceLocale[token];\n  if (typeof tokenValue === \"string\") {\n    result = tokenValue;\n  } else if (count === 1) {\n    result = tokenValue.one;\n  } else {\n    result = tokenValue.other.replace(\"{{count}}\", String(count));\n  }\n  if (options !== null && options !== void 0 && options.addSuffix) {\n    if (options.comparison && options.comparison > 0) {\n      return result + \" nữa\";\n    } else {\n      return result + \" trước\";\n    }\n  }\n  return result;\n};", "map": {"version": 3, "names": ["formatDistanceLocale", "lessThanXSeconds", "one", "other", "xSeconds", "halfAMinute", "lessThanXMinutes", "xMinutes", "aboutXHours", "xHours", "xDays", "aboutXWeeks", "xWeeks", "aboutXMonths", "xMonths", "aboutXYears", "xYears", "overXYears", "almostXYears", "formatDistance", "token", "count", "options", "result", "tokenValue", "replace", "String", "addSuffix", "comparison"], "sources": ["C:/Users/<USER>/Desktop/demo-git/demo-react/my-fullstack-app/frontend/node_modules/date-fns/locale/vi/_lib/formatDistance.js"], "sourcesContent": ["const formatDistanceLocale = {\n  lessThanXSeconds: {\n    one: \"dưới 1 giây\",\n    other: \"dưới {{count}} giây\",\n  },\n\n  xSeconds: {\n    one: \"1 giây\",\n    other: \"{{count}} giây\",\n  },\n\n  halfAMinute: \"nửa phút\",\n\n  lessThanXMinutes: {\n    one: \"dưới 1 phút\",\n    other: \"dưới {{count}} phút\",\n  },\n\n  xMinutes: {\n    one: \"1 phút\",\n    other: \"{{count}} phút\",\n  },\n\n  aboutXHours: {\n    one: \"khoảng 1 giờ\",\n    other: \"khoảng {{count}} giờ\",\n  },\n\n  xHours: {\n    one: \"1 giờ\",\n    other: \"{{count}} giờ\",\n  },\n\n  xDays: {\n    one: \"1 ngày\",\n    other: \"{{count}} ngày\",\n  },\n\n  aboutXWeeks: {\n    one: \"khoảng 1 tuần\",\n    other: \"khoảng {{count}} tuần\",\n  },\n\n  xWeeks: {\n    one: \"1 tuần\",\n    other: \"{{count}} tuần\",\n  },\n\n  aboutXMonths: {\n    one: \"khoảng 1 tháng\",\n    other: \"khoảng {{count}} tháng\",\n  },\n\n  xMonths: {\n    one: \"1 tháng\",\n    other: \"{{count}} tháng\",\n  },\n\n  aboutXYears: {\n    one: \"khoảng 1 năm\",\n    other: \"khoảng {{count}} năm\",\n  },\n\n  xYears: {\n    one: \"1 năm\",\n    other: \"{{count}} năm\",\n  },\n\n  overXYears: {\n    one: \"hơn 1 năm\",\n    other: \"hơn {{count}} năm\",\n  },\n\n  almostXYears: {\n    one: \"gần 1 năm\",\n    other: \"gần {{count}} năm\",\n  },\n};\n\nexport const formatDistance = (token, count, options) => {\n  let result;\n\n  const tokenValue = formatDistanceLocale[token];\n  if (typeof tokenValue === \"string\") {\n    result = tokenValue;\n  } else if (count === 1) {\n    result = tokenValue.one;\n  } else {\n    result = tokenValue.other.replace(\"{{count}}\", String(count));\n  }\n\n  if (options?.addSuffix) {\n    if (options.comparison && options.comparison > 0) {\n      return result + \" nữa\";\n    } else {\n      return result + \" trước\";\n    }\n  }\n\n  return result;\n};\n"], "mappings": "AAAA,MAAMA,oBAAoB,GAAG;EAC3BC,gBAAgB,EAAE;IAChBC,GAAG,EAAE,aAAa;IAClBC,KAAK,EAAE;EACT,CAAC;EAEDC,QAAQ,EAAE;IACRF,GAAG,EAAE,QAAQ;IACbC,KAAK,EAAE;EACT,CAAC;EAEDE,WAAW,EAAE,UAAU;EAEvBC,gBAAgB,EAAE;IAChBJ,GAAG,EAAE,aAAa;IAClBC,KAAK,EAAE;EACT,CAAC;EAEDI,QAAQ,EAAE;IACRL,GAAG,EAAE,QAAQ;IACbC,KAAK,EAAE;EACT,CAAC;EAEDK,WAAW,EAAE;IACXN,GAAG,EAAE,cAAc;IACnBC,KAAK,EAAE;EACT,CAAC;EAEDM,MAAM,EAAE;IACNP,GAAG,EAAE,OAAO;IACZC,KAAK,EAAE;EACT,CAAC;EAEDO,KAAK,EAAE;IACLR,GAAG,EAAE,QAAQ;IACbC,KAAK,EAAE;EACT,CAAC;EAEDQ,WAAW,EAAE;IACXT,GAAG,EAAE,eAAe;IACpBC,KAAK,EAAE;EACT,CAAC;EAEDS,MAAM,EAAE;IACNV,GAAG,EAAE,QAAQ;IACbC,KAAK,EAAE;EACT,CAAC;EAEDU,YAAY,EAAE;IACZX,GAAG,EAAE,gBAAgB;IACrBC,KAAK,EAAE;EACT,CAAC;EAEDW,OAAO,EAAE;IACPZ,GAAG,EAAE,SAAS;IACdC,KAAK,EAAE;EACT,CAAC;EAEDY,WAAW,EAAE;IACXb,GAAG,EAAE,cAAc;IACnBC,KAAK,EAAE;EACT,CAAC;EAEDa,MAAM,EAAE;IACNd,GAAG,EAAE,OAAO;IACZC,KAAK,EAAE;EACT,CAAC;EAEDc,UAAU,EAAE;IACVf,GAAG,EAAE,WAAW;IAChBC,KAAK,EAAE;EACT,CAAC;EAEDe,YAAY,EAAE;IACZhB,GAAG,EAAE,WAAW;IAChBC,KAAK,EAAE;EACT;AACF,CAAC;AAED,OAAO,MAAMgB,cAAc,GAAGA,CAACC,KAAK,EAAEC,KAAK,EAAEC,OAAO,KAAK;EACvD,IAAIC,MAAM;EAEV,MAAMC,UAAU,GAAGxB,oBAAoB,CAACoB,KAAK,CAAC;EAC9C,IAAI,OAAOI,UAAU,KAAK,QAAQ,EAAE;IAClCD,MAAM,GAAGC,UAAU;EACrB,CAAC,MAAM,IAAIH,KAAK,KAAK,CAAC,EAAE;IACtBE,MAAM,GAAGC,UAAU,CAACtB,GAAG;EACzB,CAAC,MAAM;IACLqB,MAAM,GAAGC,UAAU,CAACrB,KAAK,CAACsB,OAAO,CAAC,WAAW,EAAEC,MAAM,CAACL,KAAK,CAAC,CAAC;EAC/D;EAEA,IAAIC,OAAO,aAAPA,OAAO,eAAPA,OAAO,CAAEK,SAAS,EAAE;IACtB,IAAIL,OAAO,CAACM,UAAU,IAAIN,OAAO,CAACM,UAAU,GAAG,CAAC,EAAE;MAChD,OAAOL,MAAM,GAAG,MAAM;IACxB,CAAC,MAAM;MACL,OAAOA,MAAM,GAAG,QAAQ;IAC1B;EACF;EAEA,OAAOA,MAAM;AACf,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
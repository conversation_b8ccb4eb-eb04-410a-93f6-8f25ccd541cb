"use strict";(self.webpackChunkfrontend=self.webpackChunkfrontend||[]).push([[419],{1104:(e,t,r)=>{r.d(t,{T:()=>x,Z:()=>N});var n=r(5043),a=r(4052),o=r(2018),l=r(1828),i=r(5797),c=r(2028),s=r(9988),u=r(8794),p=r(4504);function f(){return f=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},f.apply(null,arguments)}function d(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}function m(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var n,a,o,l,i=[],c=!0,s=!1;try{if(o=(r=r.call(e)).next,0===t){if(Object(r)!==r)return;c=!1}else for(;!(c=(n=o.call(r)).done)&&(i.push(n.value),i.length!==t);c=!0);}catch(e){s=!0,a=e}finally{try{if(!c&&null!=r.return&&(l=r.return(),Object(l)!==l))return}finally{if(s)throw a}}return i}}(e,t)||function(e,t){if(e){if("string"==typeof e)return d(e,t);var r={}.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?d(e,t):void 0}}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function y(e){return y="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},y(e)}function v(e){var t=function(e,t){if("object"!=y(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=y(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==y(t)?t:t+""}function b(e,t,r){return(t=v(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}var g={root:"p-confirm-dialog",message:"p-confirm-dialog-message",icon:"p-confirm-dialog-icon",acceptButton:"p-confirm-dialog-accept",rejectButton:function(e){var t=e.getPropValue;return(0,p.xW)("p-confirm-dialog-reject",{"p-button-text":!t("rejectClassName")})}},j=l.x.extend({defaultProps:{__TYPE:"ConfirmDialog",accept:null,acceptClassName:null,acceptIcon:null,acceptLabel:null,appendTo:null,breakpoints:null,children:void 0,className:null,content:null,defaultFocus:"accept",footer:null,icon:null,message:null,onHide:null,reject:null,rejectClassName:null,rejectIcon:null,rejectLabel:null,tagKey:void 0,visible:void 0},css:{classes:g}});function h(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function w(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?h(Object(r),!0).forEach((function(t){b(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):h(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}var N=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};(e=w(w({},e),{visible:void 0===e.visible||e.visible})).visible&&s.s.emit("confirm-dialog",e);return{show:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};s.s.emit("confirm-dialog",w(w(w({},e),t),{visible:!0}))},hide:function(){s.s.emit("confirm-dialog",{visible:!1})}}},x=n.memo(n.forwardRef((function(e,t){var r=(0,c.qV)(),d=n.useContext(a.UM),y=j.getProps(e,d),v=m(n.useState(y.visible),2),b=v[0],g=v[1],h=m(n.useState(!1),2),N=h[0],x=h[1],O=n.useRef(null),S=n.useRef(!1),P=n.useRef(null),C=function(){var e=y.group;return O.current&&(e=O.current.group),Object.assign({},y,O.current,{group:e})},D=function(e){return C()[e]},E=function(e){for(var t=arguments.length,r=new Array(t>1?t-1:0),n=1;n<t;n++)r[n-1]=arguments[n];return p.BF.getPropValue(D(e),r)},R=D("acceptLabel")||(0,a.WP)("accept"),k=D("rejectLabel")||(0,a.WP)("reject"),F={props:y,state:{visible:b}},B=j.setMetaData(F),L=B.ptm,T=B.cx,M=B.isUnstyled;(0,l.j)(j.css.styles,M,{name:"confirmdialog"});var I=function(){S.current||(S.current=!0,E("accept"),A("accept"))},V=function(){S.current||(S.current=!0,E("reject"),A("reject"))},U=function(){C().group===y.group&&(g(!0),S.current=!1,P.current=document.activeElement)},A=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"cancel";b&&("string"!==typeof e&&(e="cancel"),g(!1),E("onHide",e),p.DV.focus(P.current),P.current=null)},W=function(e){if(e.tagKey===y.tagKey){var t=b!==e.visible;D("target")!==e.target&&!y.target?(A(),O.current=e,x(!0)):t&&(O.current=e,e.visible?U():A())}};n.useEffect((function(){y.visible?U():A()}),[y.visible]),n.useEffect((function(){return y.target||y.message||s.s.on("confirm-dialog",W),function(){s.s.off("confirm-dialog",W)}}),[y.target]),(0,c.w5)((function(){N&&U()}),[N]),(0,c.l0)((function(){s.s.off("confirm-dialog",W)})),n.useImperativeHandle(t,(function(){return{props:y,confirm:W}}));var _=function(){var t=C(),a=p.BF.getJSXElement(D("message"),t),l=r({className:T("icon")},L("icon")),c=p.Hj.getJSXIcon(D("icon"),w({},l),{props:t}),s=function(){var e=D("defaultFocus"),t=(0,p.xW)("p-confirm-dialog-accept",D("acceptClassName")),a=(0,p.xW)("p-confirm-dialog-reject",{"p-button-text":!D("rejectClassName")},D("rejectClassName")),l=r({label:k,autoFocus:"reject"===e,icon:D("rejectIcon"),className:(0,p.xW)(D("rejectClassName"),T("rejectButton",{getPropValue:D})),onClick:V,pt:L("rejectButton"),unstyled:y.unstyled,__parentMetadata:{parent:F}},L("rejectButton")),i=r({label:R,autoFocus:void 0===e||"accept"===e,icon:D("acceptIcon"),className:(0,p.xW)(D("acceptClassName"),T("acceptButton")),onClick:I,pt:L("acceptButton"),unstyled:y.unstyled,__parentMetadata:{parent:F}},L("acceptButton")),c=n.createElement(n.Fragment,null,n.createElement(o.$,l),n.createElement(o.$,i));if(D("footer")){var s={accept:I,reject:V,acceptClassName:t,rejectClassName:a,acceptLabel:R,rejectLabel:k,element:c,props:C()};return p.BF.getJSXElement(D("footer"),s)}return c}(),u=r({className:T("message")},L("message")),d=r({visible:b,className:(0,p.xW)(D("className"),T("root")),footer:s,onHide:A,breakpoints:D("breakpoints"),pt:t.pt,unstyled:y.unstyled,appendTo:D("appendTo"),__parentMetadata:{parent:F}},j.getOtherProps(t));return n.createElement(i.l,f({},d,{content:null===e||void 0===e?void 0:e.content}),c,n.createElement("span",u,a))}();return n.createElement(u.Z,{element:_,appendTo:D("appendTo")})})));x.displayName="ConfirmDialog"},4419:(e,t,r)=>{r.r(t),r.d(t,{default:()=>g});var n=r(5043),a=r(2018),o=r(9642),l=r(1063),i=r(828),c=r(1104),s=r(8150),u=r(3740),p=r(6104),f=r(2052),d=r(5371),m=r(5855),y=r(402),v=r(8018),b=r(579);const g=()=>{const e=(0,n.useRef)(null),[t,r]=(0,n.useState)([]),[g,j]=(0,n.useState)(!0),[h,w]=(0,n.useState)(!1),[N,x]=(0,n.useState)(""),[O,S]=(0,n.useState)(null),[P,C]=(0,n.useState)(null),D=async()=>{try{w(!0),v.Rm.api("\u8f09\u5165\u5831\u8868\u5217\u8868");const e={fileName:N,startDate:O?O.toISOString():void 0,endDate:P?P.toISOString():void 0},t=await y.A.get("/api/file/GetReportFiles",{params:e});r(t.data),v.Rm.api("\u5831\u8868\u5217\u8868\u8f09\u5165\u6210\u529f",{count:t.data.length})}catch(n){var t;v.Rm.error("\u8f09\u5165\u5831\u8868\u5217\u8868\u5931\u6557",n),null===(t=e.current)||void 0===t||t.show({severity:"error",summary:"\u8f09\u5165\u5931\u6557",detail:"\u7121\u6cd5\u8f09\u5165\u5831\u8868\u5217\u8868",life:5e3})}finally{j(!1),w(!1)}},E=t=>{(0,c.Z)({message:'\u78ba\u5b9a\u8981\u522a\u9664\u5831\u8868 "'.concat(t.fileName,'" \u55ce\uff1f\u6b64\u64cd\u4f5c\u7121\u6cd5\u5fa9\u539f\u3002'),header:"\u78ba\u8a8d\u522a\u9664",icon:"pi pi-exclamation-triangle",acceptLabel:"\u78ba\u5b9a",rejectLabel:"\u53d6\u6d88",accept:()=>(async t=>{try{var r;v.Rm.api("\u522a\u9664\u5831\u8868",{fileName:t.fileName}),await y.A.delete("/api/file/DeleteReportFile",{params:{fileName:t.fileName}}),null===(r=e.current)||void 0===r||r.show({severity:"success",summary:"\u522a\u9664\u6210\u529f",detail:"\u5831\u8868 ".concat(t.fileName," \u5df2\u522a\u9664"),life:3e3}),D()}catch(l){var n,a,o;v.Rm.error("\u522a\u9664\u5831\u8868\u5931\u6557",l),null===(n=e.current)||void 0===n||n.show({severity:"error",summary:"\u522a\u9664\u5931\u6557",detail:(null===(a=l.response)||void 0===a||null===(o=a.data)||void 0===o?void 0:o.message)||"\u522a\u9664\u5831\u8868\u5931\u6557",life:5e3})}})(t)})},R=e=>{if(!e)return"";try{return(0,m.$)(e,"yyyy/MM/dd HH:mm:ss")}catch(t){return console.error("Error formatting date:",t),e}},k=(0,b.jsx)(a.$,{type:"button",icon:"pi pi-refresh",text:!0,onClick:D,disabled:h}),F=(0,b.jsx)("div",{});return(0,n.useEffect)((()=>{D()}),[]),g?(0,b.jsx)("div",{className:"flex justify-content-center align-items-center",style:{height:"400px"},children:(0,b.jsx)(u.p,{})}):(0,b.jsxs)("div",{children:[(0,b.jsx)(i.y,{ref:e}),(0,b.jsx)(c.T,{}),(0,b.jsx)(s.Z,{title:"\u5831\u8868\u7ba1\u7406",className:"mb-4",children:(0,b.jsx)("p",{className:"text-600 line-height-3 m-0",children:"\u7ba1\u7406\u7cfb\u7d71\u751f\u6210\u7684 PDF \u5831\u8868\u6a94\u6848\uff0c\u5305\u62ec\u6536\u64da\u3001\u7d71\u8a08\u5831\u8868\u7b49\u3002\u60a8\u53ef\u4ee5\u9810\u89bd\u3001\u4e0b\u8f09\u6216\u522a\u9664\u4e0d\u9700\u8981\u7684\u5831\u8868\u6a94\u6848\u3002"})}),(0,b.jsx)(s.Z,{className:"mb-4",children:(0,b.jsxs)("div",{className:"grid",children:[(0,b.jsx)("div",{className:"col-12 md:col-4",children:(0,b.jsx)(f.S,{id:"fileNameFilter",value:N,onChange:e=>x(e.target.value),placeholder:"\u4f9d\u6a94\u540d\u641c\u5c0b",className:"w-full"})}),(0,b.jsx)("div",{className:"col-6 md:col-3",children:(0,b.jsx)(d.V,{id:"startDateFilter",value:O,onChange:e=>S(e.value),placeholder:"\u9078\u64c7\u958b\u59cb\u65e5\u671f",className:"w-full",dateFormat:"yy/mm/dd",showIcon:!0})}),(0,b.jsx)("div",{className:"col-6 md:col-3",children:(0,b.jsx)(d.V,{id:"endDateFilter",value:P,onChange:e=>C(e.value),placeholder:"\u9078\u64c7\u7d50\u675f\u65e5\u671f",className:"w-full",dateFormat:"yy/mm/dd",showIcon:!0})}),(0,b.jsx)("div",{className:"col-12 md:col-4",children:(0,b.jsxs)("div",{className:"flex gap-2",children:[(0,b.jsx)(a.$,{label:"\u641c\u5c0b",icon:"pi pi-search",onClick:()=>{D()},className:"mr-2"}),(0,b.jsx)(a.$,{label:"\u91cd\u8a2d",icon:"pi pi-undo",onClick:()=>{x(""),S(null),C(null),D()},className:"p-button-secondary"})]})})]})}),(0,b.jsx)(s.Z,{children:(0,b.jsxs)(l.b,{value:t,paginator:!0,rows:20,rowsPerPageOptions:[10,20,50],emptyMessage:"\u6c92\u6709\u627e\u5230\u5831\u8868\u6a94\u6848",tableStyle:{minWidth:"50rem"},paginatorLeft:k,paginatorRight:F,loading:h,children:[(0,b.jsx)(o.V,{field:"fileName",header:"\u6a94\u6848\u540d\u7a31",sortable:!0,style:{width:"30%"}}),(0,b.jsx)(o.V,{field:"fileType",header:"\u985e\u578b",body:e=>{var t;const r=null===(t=e.fileName.split(".").pop())||void 0===t?void 0:t.toUpperCase();let n="info";switch(r){case"PDF":n="danger";break;case"XLSX":case"XLS":n="success";break;case"DOCX":case"DOC":n="info";break;default:n="warning"}return(0,b.jsx)(p.v,{value:r,severity:n})},style:{width:"10%"}}),(0,b.jsx)(o.V,{field:"fileSize",header:"\u6a94\u6848\u5927\u5c0f",body:e=>(e=>{if(0===e)return"0 Bytes";const t=Math.floor(Math.log(e)/Math.log(1024));return parseFloat((e/Math.pow(1024,t)).toFixed(2))+" "+["Bytes","KB","MB","GB"][t]})(e.fileSize),sortable:!0,style:{width:"15%"}}),(0,b.jsx)(o.V,{field:"createdDate",header:"\u5efa\u7acb\u65e5\u671f",body:e=>R(e.createdDate),sortable:!0,style:{width:"20%"}}),(0,b.jsx)(o.V,{field:"modifiedDate",header:"\u4fee\u6539\u65e5\u671f",body:e=>R(e.modifiedDate),sortable:!0,style:{width:"20%"}}),(0,b.jsx)(o.V,{header:"\u64cd\u4f5c",body:t=>(0,b.jsxs)("div",{className:"flex gap-2",children:[(0,b.jsx)(a.$,{icon:"pi pi-eye",className:"p-button-info p-button-sm",onClick:()=>(async t=>{try{v.Rm.api("\u9810\u89bd\u5831\u8868",{fileName:t.fileName});const e=await y.A.get("/api/file/DownloadReportFile",{params:{fileName:t.fileName},responseType:"blob"}),r=new Blob([e.data],{type:"application/pdf"}),n=window.URL.createObjectURL(r);window.open(n,"_blank")}catch(n){var r;v.Rm.error("\u9810\u89bd\u5831\u8868\u5931\u6557",n),null===(r=e.current)||void 0===r||r.show({severity:"error",summary:"\u9810\u89bd\u5931\u6557",detail:"\u9810\u89bd\u5831\u8868\u5931\u6557",life:5e3})}})(t),tooltip:"\u9810\u89bd",tooltipOptions:{position:"top"}}),(0,b.jsx)(a.$,{icon:"pi pi-download",className:"p-button-success p-button-sm",onClick:()=>(async t=>{try{var r;v.Rm.api("\u4e0b\u8f09\u5831\u8868",{fileName:t.fileName});const n=await y.A.get("/api/file/DownloadReportFile",{params:{fileName:t.fileName},responseType:"blob"}),a=new Blob([n.data],{type:"application/pdf"}),o=window.URL.createObjectURL(a),l=document.createElement("a");l.href=o,l.download=t.fileName,document.body.appendChild(l),l.click(),document.body.removeChild(l),window.URL.revokeObjectURL(o),null===(r=e.current)||void 0===r||r.show({severity:"success",summary:"\u4e0b\u8f09\u6210\u529f",detail:"\u5831\u8868 ".concat(t.fileName," \u4e0b\u8f09\u5b8c\u6210"),life:3e3})}catch(a){var n;v.Rm.error("\u4e0b\u8f09\u5831\u8868\u5931\u6557",a),null===(n=e.current)||void 0===n||n.show({severity:"error",summary:"\u4e0b\u8f09\u5931\u6557",detail:"\u4e0b\u8f09\u5831\u8868\u5931\u6557",life:5e3})}})(t),tooltip:"\u4e0b\u8f09",tooltipOptions:{position:"top"}}),(0,b.jsx)(a.$,{icon:"pi pi-trash",className:"p-button-danger p-button-sm",onClick:()=>E(t),tooltip:"\u522a\u9664",tooltipOptions:{position:"top"}})]}),style:{width:"15%"}})]})})]})}},6104:(e,t,r)=>{r.d(t,{v:()=>m});var n=r(5043),a=r(4052),o=r(1828),l=r(2028),i=r(4504);function c(e){return c="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},c(e)}function s(e){var t=function(e,t){if("object"!=c(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=c(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==c(t)?t:t+""}function u(e,t,r){return(t=s(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}var p={value:"p-tag-value",icon:"p-tag-icon",root:function(e){var t=e.props;return(0,i.xW)("p-tag p-component",u(u({},"p-tag-".concat(t.severity),null!==t.severity),"p-tag-rounded",t.rounded))}},f=o.x.extend({defaultProps:{__TYPE:"Tag",value:null,severity:null,rounded:!1,icon:null,style:null,className:null,children:void 0},css:{classes:p,styles:"\n@layer primereact {\n    .p-tag {\n        display: inline-flex;\n        align-items: center;\n        justify-content: center;\n    }\n    \n    .p-tag-icon,\n    .p-tag-value,\n    .p-tag-icon.pi {\n        line-height: 1.5;\n    }\n    \n    .p-tag.p-tag-rounded {\n        border-radius: 10rem;\n    }\n}\n"}});function d(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}var m=n.forwardRef((function(e,t){var r=(0,l.qV)(),c=n.useContext(a.UM),s=f.getProps(e,c),p=f.setMetaData({props:s}),m=p.ptm,y=p.cx,v=p.isUnstyled;(0,o.j)(f.css.styles,v,{name:"tag"});var b=n.useRef(null),g=r({className:y("icon")},m("icon")),j=i.Hj.getJSXIcon(s.icon,function(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?d(Object(r),!0).forEach((function(t){u(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):d(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}({},g),{props:s});n.useImperativeHandle(t,(function(){return{props:s,getElement:function(){return b.current}}}));var h=r({ref:b,className:(0,i.xW)(s.className,y("root")),style:s.style},f.getOtherProps(s),m("root")),w=r({className:y("value")},m("value"));return n.createElement("span",h,j,n.createElement("span",w,s.value),n.createElement("span",null,s.children))}));m.displayName="Tag"}}]);
//# sourceMappingURL=419.4eff717a.chunk.js.map
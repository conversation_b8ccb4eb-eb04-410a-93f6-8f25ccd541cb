import { But<PERSON> } from 'primereact/button';
import { Column } from 'primereact/column';
import { DataTable } from 'primereact/datatable';
import { Dialog } from 'primereact/dialog';
import { Dropdown } from 'primereact/dropdown';
import { InputText } from 'primereact/inputtext';
import { Toast } from 'primereact/toast';
import { Tag } from 'primereact/tag';
import { MultiSelect } from 'primereact/multiselect';
import { Card } from 'primereact/card';
import { ProgressSpinner } from 'primereact/progressspinner';
import React, { useRef, useState, useEffect } from 'react';
import api from '../../services/api';
import { log } from '../../utils/logger';
import { usePagePermission, useUserInfo, PermissionGuard } from '../../hooks/usePermission';

interface Role {
  Id: number;
  Name: string;
}

interface UserRole {
  roleId: number;
  roleName: string;
}

interface User {
  userId: number;
  userName: string;
  userAccount: string;
  userEmail?: string;
  userPhone?: string;
  address?: string;
  gender?: string;
  birthDate?: string;
  isEnabled: boolean;
  createdAt: string;
  updatedAt: string;
  roles?: UserRole[];
}

interface EditUserRolesData {
  userId: number;
  userName: string;
  userAccount: string;
  roles: UserRole[];
}

const UsersPage: React.FC = () => {
  // 權限檢查
  const { hasPermission, loading: permissionLoading, error: permissionError } = usePagePermission('users');
  const { userInfo } = useUserInfo();

  const toast = useRef<Toast>(null);
  const [users, setUsers] = useState<User[]>([]);
  const [roles, setRoles] = useState<Role[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchName, setSearchName] = useState('');
  const [selectedRoleFilter, setSelectedRoleFilter] = useState<number | null>(null);
  const [showEditDialog, setShowEditDialog] = useState(false);
  const [editingUser, setEditingUser] = useState<EditUserRolesData | null>(null);
  const [selectedRoles, setSelectedRoles] = useState<number[]>([]);
  const [refreshing, setRefreshing] = useState(false);

  // 載入用戶列表
  const loadUsers = async () => {
    try {
      setRefreshing(true);
      log.api('載入用戶列表', { searchName, selectedRoleFilter });

      const response = await api.get('/api/users/GetUserRolesList', {
        params: { name: searchName }
      });

      let filteredUsers = response.data;

      // 如果選擇了角色篩選，則進行前端篩選
      if (selectedRoleFilter) {
        filteredUsers = response.data.filter((user: User) =>
          user.roles && user.roles.some((role: UserRole) => role.roleId === selectedRoleFilter)
        );
      }

      setUsers(filteredUsers);
      log.api('用戶列表載入成功', {
        total: response.data.length,
        filtered: filteredUsers.length,
        roleFilter: selectedRoleFilter
      });

    } catch (error: any) {
      log.error('載入用戶列表失敗', error);
      toast.current?.show({
        severity: 'error',
        summary: '載入失敗',
        detail: '無法載入用戶列表',
        life: 5000
      });
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  // 載入角色列表
  const loadRoles = async () => {
    try {
      log.api('載入角色列表');
      
      const response = await api.get('/api/users/GetRoles');
      setRoles(response.data);
      
      log.api('角色列表載入成功', { count: response.data.length });
      
    } catch (error: any) {
      log.error('載入角色列表失敗', error);
      toast.current?.show({
        severity: 'error',
        summary: '載入失敗',
        detail: '無法載入角色列表',
        life: 5000
      });
    }
  };

  // 打開編輯對話框
  const handleEditUserRoles = async (user: User) => {
    try {
      
      log.api('載入用戶角色', { userId: user.userId });

      const response = await api.get(`/api/users/GetUserRoles/${user.userId}`);
      const userData = response.data;
      
      setEditingUser({
        userId: userData.userId,
        userName: userData.userName,
        userAccount: userData.userAccount,
        roles: userData.roles || []
      });

      // 安全檢查：確保 Roles 存在且為陣列
      const roles = userData.roles || [];
      setSelectedRoles(roles.map((role: UserRole) => role.roleId));
      setShowEditDialog(true);
      
      log.api('用戶角色載入成功', userData);
      
    } catch (error: any) {
      log.error('載入用戶角色失敗', error);
      toast.current?.show({
        severity: 'error',
        summary: '載入失敗',
        detail: '無法載入用戶角色資訊',
        life: 5000
      });
    }
  };

  // 保存用戶角色
  const handleSaveUserRoles = async () => {
    if (!editingUser) return;
    
    try {
      log.api('更新用戶角色', { 
        userId: editingUser.userId, 
        roleIds: selectedRoles 
      });
      
      await api.put('/api/users/UpdateUserRoles', {
        UserId: editingUser.userId,
        RoleIds: selectedRoles
      });
      
      toast.current?.show({
        severity: 'success',
        summary: '更新成功',
        detail: '用戶角色權限已更新',
        life: 3000
      });
      
      setShowEditDialog(false);
      loadUsers(); // 重新載入用戶列表
      
      log.api('用戶角色更新成功');
      
    } catch (error: any) {
      log.error('更新用戶角色失敗', error);
      toast.current?.show({
        severity: 'error',
        summary: '更新失敗',
        detail: '無法更新用戶角色權限',
        life: 5000
      });
    }
  };

  // 初始化載入
  useEffect(() => {
    loadUsers();
    loadRoles();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  // 監聽角色篩選變化
  useEffect(() => {
    if (roles.length > 0) {
      loadUsers();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [selectedRoleFilter]);

  // 搜索處理
  const handleSearch = () => {
    loadUsers();
  };

    // 重置緩存
  const resetMemCache = async () => {
    try {
      await api.get('/api/system/ResetMemCache');
      toast.current?.show({
        severity: 'success',
        summary: '重置成功',
        detail: '緩存已重置',
        life: 3000
      });
    } catch (error: any) {
      log.error('重置緩存失敗', error);
      toast.current?.show({
        severity: 'error',
        summary: '重置失敗',
        detail: '無法重置緩存',
        life: 5000
      });
    } 
  };

  // 角色標籤模板
  const rolesBodyTemplate = (rowData: User) => {
    // 安全檢查：確保 Roles 存在且為陣列
    if (!rowData.roles || !Array.isArray(rowData.roles) || rowData.roles.length === 0) {
      return (
        <Tag
          value="無角色"
          severity="warning"
          className="text-sm"
        />
      );
    }

    return (
      <div className="flex flex-wrap gap-1">
        {rowData.roles.map((role: UserRole) => (
          <Tag
            key={role.roleId}
            value={role.roleName}
            severity="info"
            className="text-sm"
          />
        ))}
      </div>
    );
  };

  // 操作按鈕模板
  const actionBodyTemplate = (rowData: User) => {
    return (
      <div className="flex gap-2">
        <Button
          icon="pi pi-pencil"
          className="p-button-success"
          size="small" 
          onClick={() => handleEditUserRoles(rowData)}
          label="編輯"
        />
      </div>
    );
  };

  // 狀態模板
  const statusBodyTemplate = (rowData: User) => {
    return (
      <Tag 
        value={rowData.isEnabled ? '啟用' : '停用'} 
        severity={rowData.isEnabled ? 'success' : 'danger'}
      />
    );
  };

  // 可選角色選項
  const availableRoleOptions = roles.map(role => ({
    label: role.Name,
    value: role.Id
  }));

  const paginatorLeft = (
      <Button
          type="button"
          icon="pi pi-refresh"
          text
          onClick={() => loadUsers()}
      />
  );
  const paginatorRight = <div></div>;

  // 權限檢查載入中
  if (permissionLoading) {
    return (
      <div className="flex align-items-center justify-content-center min-h-screen">
        <div className="text-center">
          <ProgressSpinner />
          <p className="mt-3">檢查權限中...</p>
        </div>
      </div>
    );
  }

  // 權限檢查失敗
  if (permissionError) {
    return (
      <div className="flex align-items-center justify-content-center min-h-screen">
        <div className="text-center">
          <i className="pi pi-exclamation-triangle text-6xl text-orange-500 mb-3"></i>
          <h3 className="text-xl font-semibold mb-2">權限檢查失敗</h3>
          <p className="text-gray-600 mb-4">{permissionError}</p>
          <Button
            label="重新載入"
            icon="pi pi-refresh"
            onClick={() => window.location.reload()}
          />
        </div>
      </div>
    );
  }

  // 沒有權限
  if (!hasPermission) {
    return (
      <div className="flex align-items-center justify-content-center min-h-screen">
        <div className="text-center">
          <i className="pi pi-ban text-6xl text-red-500 mb-3"></i>
          <h3 className="text-xl font-semibold mb-2">訪問被拒絕</h3>
          <p className="text-gray-600 mb-2">您沒有權限訪問用戶管理頁面</p>
          <p className="text-sm text-gray-500 mb-4">
            當前角色: {userInfo?.role || '未知'} | 需要角色: Admin, Manager
          </p>
          <Button
            label="返回首頁"
            icon="pi pi-home"
            onClick={() => window.location.href = '/'}
          />
        </div>
      </div>
    );
  }

  // 資料載入中
  if (loading) {
    return (
      <div className="flex align-items-center justify-content-center min-h-screen">
        <div className="text-center">
          <ProgressSpinner />
          <p className="mt-3">載入用戶資料中...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="users-page">
      <Toast ref={toast} />
      
      <Card title="用戶權限管理" className="mb-4">
        <p className="text-600 line-height-3 m-0">
          管理系統的用戶權限，包括分配角色和查看用戶詳細信息。您可以編輯用戶的角色權限以控制其在系統中的訪問和操作。
        </p>
      </Card>
      
      {/* 搜尋條件 */}
      <Card className="mb-4">
        <div className="grid">
          <div className="col-6 md:col-4">
            <div className="p-inputgroup">
              <InputText
                placeholder="搜尋用戶名稱"
                value={searchName}
                onChange={(e) => setSearchName(e.target.value)}
                onKeyDown={(e) => e.key === 'Enter' && handleSearch()}
              />
              <Button
                icon="pi pi-search"
                onClick={handleSearch}
                disabled={refreshing}
              />
            </div>
          </div>
          <div className="col-6 md:col-4">
            <Dropdown
              value={selectedRoleFilter}
              options={[
                { label: '全部角色', value: null },
                ...roles.map(role => ({ label: role.Name, value: role.Id }))
              ]}
              onChange={(e) => setSelectedRoleFilter(e.value)}
              placeholder="篩選角色"
              className="w-full"
              showClear
            />
          </div>
          <div className="col-6 md:col-4 ">
            <div className="flex gap-2">
              <Button
                  label="緩存重置"
                  className="p-button-danger"
                  icon="pi pi-trash"
                  onClick={resetMemCache}
                />
            </div>
          </div>
        </div>
      </Card>

      {/* 用戶列表 */}
      <Card>
        <DataTable
          value={users}
          paginator
          rows={10}
          rowsPerPageOptions={[5, 10, 25, 50]}
          sortMode="multiple"
          removableSort
          filterDisplay="menu"
          globalFilterFields={['userName', 'userAccount', 'userEmail']}
          emptyMessage="沒有找到用戶資料"
          className="p-datatable-gridlines"
          paginatorLeft={paginatorLeft}
          paginatorRight={paginatorRight}
        >
          <Column
            field="userName"
            header="用戶名稱"
            sortable
            filter
            filterPlaceholder="搜尋名稱"
            style={{ minWidth: '150px' }}
          />
          <Column
            field="userAccount"
            header="帳號"
            sortable
            filter
            filterPlaceholder="搜尋帳號"
            style={{ minWidth: '120px' }}
          />
          <Column
            field="userEmail"
            header="Email"
            sortable
            filter
            filterPlaceholder="搜尋Email"
            style={{ minWidth: '200px' }}
          />
          <Column
            header="角色權限"
            body={rolesBodyTemplate}
            style={{ minWidth: '200px' }}
          />
          <Column
            field="isEnabled"
            header="狀態"
            body={statusBodyTemplate}
            sortable
            style={{ minWidth: '100px' }}
          />
          <Column
            header="操作"
            body={actionBodyTemplate}
            style={{ minWidth: '100px' }}
          />
        </DataTable>
      </Card>

      {/* 編輯角色對話框 */}
      <Dialog
        header={`編輯用戶角色權限 - ${editingUser?.userName}`}
        visible={showEditDialog}
        style={{ width: '500px' }}
        onHide={() => setShowEditDialog(false)}
        footer={
          <div className="flex justify-content-end gap-2">
            <Button
              label="取消"
              icon="pi pi-times"
              onClick={() => setShowEditDialog(false)}
              className="p-button-text"
            />
            <Button
              label="保存"
              icon="pi pi-check"
              onClick={handleSaveUserRoles}
              className="p-button-primary"
            />
          </div>
        }
      >
        {editingUser && (
          <div className="grid">
            <div className="col-12">
              <div className="field">
                <label className="font-bold">用戶帳號:</label>
                <p className="m-0">{editingUser.userAccount}</p>
              </div>
            </div>
            <div className="col-12">
              <div className="field">
                <label htmlFor="roles" className="font-bold">角色權限:</label>
                <MultiSelect
                  id="roles"
                  value={selectedRoles}
                  options={availableRoleOptions}
                  onChange={(e) => setSelectedRoles(e.value)}
                  placeholder="選擇角色權限"
                  className="w-full"
                  display="chip"
                />
              </div>
            </div>
          </div>
        )}
      </Dialog>
    </div>
  );
};

export default UsersPage;

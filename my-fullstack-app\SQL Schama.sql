INSERT INTO MenuGroups ( Name , SortOrder, Icon, CreatedAt, UpdatedAt, IsEnabled, OptionUserId) VALUES
  ('治療相關', 0, 'pi pi-user-edit', NOW(), NOW(), TRUE, 1),
  ('病患管理', 1, 'pi pi-users', NOW(), NOW(), TRUE, 1),
  ('系統管理', 2, 'pi pi-server', NOW(), NOW(), TRUE, 1);
  
INSERT INTO Menus (Path, Name, IsEnabled, GroupId, SortOrder, OptionUserId, CreatedAt, UpdatedAt) VALUES

-- 群組 1: 治療師相關
('/doctors', '治療師列表', TRUE, 1, 1, 1, NOW(), NOW()),
('/doctordetail', '治療師詳情', TRUE, 1, 2, 1, NOW(), NOW()),
('/schedules', '排班表', TRUE, 1, 3, 1, NOW(), NOW()),

-- 群組 2: 病患管理
('/patients', '病患列表', TRUE, 2 , 1, 1, NOW(), NOW()),
('/patientsdetail', '病患詳情', TRUE, 2, 2, 1, NOW(), NOW()),
('/treatments', '診療紀錄', TRUE, 2, 3, 1, NOW(), NOW()),
('/treatmentsdetail', '診療詳情', TRUE, 2, 4, 1, NOW(), NOW()),
('/receipts', '收據列表', TRUE, 2 ,5, 1, NOW(), NOW()),
('/receiptsdetail', '收據詳情', TRUE, 2 ,6, 1, NOW(), NOW()),

-- 群組 3: 系統管理
('/userrole-management', '權限管理', TRUE, 3, 1, 1, NOW(), NOW()),
('/backup-management', '備份監控', TRUE, 3, 2, 1, NOW(), NOW());
('/report-management', '報表管理', TRUE, 3, 3, 1, NOW(), NOW());
('/image-management', '圖片管理', TRUE, 3, 4, 1, NOW(), NOW());
('/login-logs', '登入紀錄', TRUE, 3, 5, 1, NOW(), NOW());
('/ip-blocks', 'IP封鎖', TRUE, 3, 6, 1, NOW(), NOW());
('/debug', '測試', TRUE, 3, 10, 1, NOW(), NOW());

INSERT INTO Users (Name, Username, PasswordHash, Gender, OptionUserId, CreatedAt, UpdatedAt, IsEnabled) VALUES
('系統管理員', 'Admin', '$2a$11$BzHelCHTnyZ1YaBmntBbF.fYiWnxD6vmluEYHficTzB1cBEhEIc9a', 1, 1, NOW(), NOW(), true);

INSERT INTO Roles (Name, IsEnabled, OperatorUserId, CreatedAt, UpdatedAt) VALUES
('Admin', true, 1, NOW(), NOW()),
('Manager', true, 1, NOW(), NOW()),
('User', true, 1, NOW(), NOW()),
('Option', true, 1, NOW(), NOW()),
('Guest', true, 1, NOW(), NOW());

INSERT INTO UserRoles (UserId, RoleId, CreatedAt, UpdatedAt, IsEnabled, OperatorUserId) VALUES
(1, 1, NOW(), NOW(), true, 1),
(1, 2, NOW(), NOW(), true, 1),
(1, 3, NOW(), NOW(), true, 1);

INSERT INTO DataTypeGroups (Name, IsEnabled, CreatedAt, UpdatedAt, OperatorUserId) VALUES
( '正背面', true, NOW(), NOW(), 1),
( '不適部位', true, NOW(), NOW(), 1),
( '不適情形', true, NOW(), NOW(), 1),
( '不適期間', true, NOW(), NOW(), 1),
( '可能引發的原因', true, NOW(), NOW(), 1),
( '曾接受過相關處置', true, NOW(), NOW(), 1),
( '如何知道我們院所', true, NOW(), NOW(), 1),
( '系統性疾病史', true, NOW(), NOW(), 1),
( '物理治療評估', true, NOW(), NOW(), 1);

INSERT INTO DataTypes (Number, Name, IsEnabled, CreatedAt, UpdatedAt, OperatorUserId, DataTypeGroupId) VALUES
('101', '正面左邊', true, NOW(), NOW(), 1, 1),
('102', '正面右邊', true, NOW(), NOW(), 1, 1),
('103', '背面左邊', true, NOW(), NOW(), 1, 1),
('104', '背面右邊', true, NOW(), NOW(), 1, 1),

('201', '頭頸部', true, NOW(), NOW(), 1, 2),
('202', '胸肋部', true, NOW(), NOW(), 1, 2),
('203', '腰臀部', true, NOW(), NOW(), 1, 2),
('204', '間肘手部', true, NOW(), NOW(), 1, 2),
('205', '腿膝足部', true, NOW(), NOW(), 1, 2),
('206', '肢體活動控制受限', true, NOW(), NOW(), 1, 2),

('301', '用力即有拉扯感或刺痛', true, NOW(), NOW(), 1, 3),
('302', '隱約疲頓感不動也痛', true, NOW(), NOW(), 1, 3),
('303', '脹麻感', true, NOW(), NOW(), 1, 3),
('304', '無法敘述的不適感', true, NOW(), NOW(), 1, 3),
('305', '活動受限', true, NOW(), NOW(), 1, 3),

('401', '0-2周', true, NOW(), NOW(), 1, 4),
('402', '2周-2個月', true, NOW(), NOW(), 1, 4),
('403', '2個月-6個月', true, NOW(), NOW(), 1, 4),
('404', '6個月-12個月', true, NOW(), NOW(), 1, 4),
('405', '12個月以上', true, NOW(), NOW(), 1, 4),

('501', '運動後或明確跌撞傷', true, NOW(), NOW(), 1, 5),
('502', '疑似長時間固定姿勢', true, NOW(), NOW(), 1, 5),
('503', '姿勢不良或器具使用不當', true, NOW(), NOW(), 1, 5),
('599', '其他', true, NOW(), NOW(), 1, 5),

('601', '西醫針劑治療', true, NOW(), NOW(), 1, 6),
('602', '西醫健保復健', true, NOW(), NOW(), 1, 6),
('603', '中醫藥或針灸', true, NOW(), NOW(), 1, 6),
('604', '西醫徒手治療或中醫推拿', true, NOW(), NOW(), 1, 6),
('605', '按摩整骨相關', true, NOW(), NOW(), 1, 6),
('699', '其他', true, NOW(), NOW(), 1, 6),

('701', '親友介紹', true, NOW(), NOW(), 1, 7),
('702', '地圖搜尋', true, NOW(), NOW(), 1, 7),
('703', '網頁搜尋或網路評價', true, NOW(), NOW(), 1, 7),
('704', '路過門口招牌', true, NOW(), NOW(), 1, 7),
('705', '相關院所轉介', true, NOW(), NOW(), 1, 7),
('799', '其他', true, NOW(), NOW(), 1, 7),

('801', '心臟病', true, NOW(), NOW(), 1, 8),
('802', '高血壓', true, NOW(), NOW(), 1, 8),
('803', '骨質疏鬆症', true, NOW(), NOW(), 1, 8),
('804', '糖尿病', true, NOW(), NOW(), 1, 8),
('805', '腦出/缺血(中風)', true, NOW(), NOW(), 1, 8),
('806', '巴金森氏症', true, NOW(), NOW(), 1, 8),
('899', '其他', true, NOW(), NOW(), 1, 8),

('901', '筋膜鬆動術', true, NOW(), NOW(), 1, 9),
('902', '關節鬆動術', true, NOW(), NOW(), 1, 9),
('903', '物理治療評估', true, NOW(), NOW(), 1, 9),
('904', '運動治療', true, NOW(), NOW(), 1, 9),
('999', '其他', true, NOW(), NOW(), 1, 9);


SELECT table_schema AS "Database Name",
  ROUND(SUM(data_length + index_length) / 1024 / 1024, 2)
  AS "Size in (MB)"
  FROM information_schema.TABLES
  WHERE table_schema = "mydb";
  
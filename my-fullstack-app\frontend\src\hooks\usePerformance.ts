import { useEffect, useRef, useState } from 'react';
import { isDebugEnabled } from '../config/env';

/**
 * Hook for monitoring component performance
 */
export function usePerformance(componentName: string) {
  const mountTimeRef = useRef<number>(Date.now());
  const renderTimeRef = useRef<number>(Date.now());

  useEffect(() => {
    if (!isDebugEnabled()) return;

    const mountTime = Date.now() - mountTimeRef.current;
    
    console.log(`[Performance] ${componentName} mounted in ${mountTime}ms`);

    return () => {
      const totalTime = Date.now() - mountTimeRef.current;
      console.log(`[Performance] ${componentName} unmounted after ${totalTime}ms`);
    };
  }, [componentName]);

  useEffect(() => {
    if (!isDebugEnabled()) return;

    const renderTime = Date.now() - renderTimeRef.current;
    console.log(`[Performance] ${componentName} rendered in ${renderTime}ms`);
    renderTimeRef.current = Date.now();
  });

  return {
    markRenderStart: () => {
      renderTimeRef.current = Date.now();
    },
    markRenderEnd: () => {
      if (isDebugEnabled()) {
        const renderTime = Date.now() - renderTimeRef.current;
        console.log(`[Performance] ${componentName} render completed in ${renderTime}ms`);
      }
    },
  };
}

/**
 * Hook for measuring API call performance
 */
export function useApiPerformance() {
  const measureApiCall = async <T>(
    apiCall: () => Promise<T>,
    operationName: string
  ): Promise<T> => {
    if (!isDebugEnabled()) {
      return apiCall();
    }

    const startTime = Date.now();
    
    try {
      const result = await apiCall();
      const endTime = Date.now();
      console.log(`[API Performance] ${operationName} completed in ${endTime - startTime}ms`);
      return result;
    } catch (error) {
      const endTime = Date.now();
      console.log(`[API Performance] ${operationName} failed after ${endTime - startTime}ms`);
      throw error;
    }
  };

  return { measureApiCall };
}

/**
 * Hook for debouncing values to improve performance
 */
export function useDebounce<T>(value: T, delay: number): T {
  const [debouncedValue, setDebouncedValue] = useState<T>(value);

  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedValue(value);
    }, delay);

    return () => {
      clearTimeout(handler);
    };
  }, [value, delay]);

  return debouncedValue;
}

/**
 * Hook for throttling function calls
 */
export function useThrottle<T extends (...args: any[]) => any>(
  callback: T,
  delay: number
): T {
  const lastRun = useRef<number>(Date.now());

  return useRef((...args: Parameters<T>) => {
    if (Date.now() - lastRun.current >= delay) {
      callback(...args);
      lastRun.current = Date.now();
    }
  }).current as T;
}

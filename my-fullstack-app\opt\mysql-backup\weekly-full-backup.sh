#!/bin/bash

# === 設定 ===
BACKUP_DIR="/home/<USER>/mysql-backup/full"
TODAY=$(date +%F)
FILE_NAME="mysql-full-${TODAY}.sql.gz"
FILE_PATH="${BACKUP_DIR}/${FILE_NAME}"
RETENTION_DAYS=7

# MySQL 資訊
MYSQL_CONTAINER="my-mysql"
MYSQL_USER="root"
MYSQL_PASSWORD="example"

# Dropbox Remote 名稱
RCLONE_REMOTE="dropbox:mysql-full-backup"

mkdir -p "$BACKUP_DIR"

echo "🔄 [完整備份] $TODAY"

# 匯出所有資料庫
docker exec "$MYSQL_CONTAINER" sh -c \
  "mysqldump -u$MYSQL_USER -p$MYSQL_PASSWORD --all-databases" \
  | gzip > "$FILE_PATH"

# 上傳至 Dropbox
rclone copy "$FILE_PATH" "$RCLONE_REMOTE"
rclone delete --min-age ${RETENTION_DAYS}d "$RCLONE_REMOTE"

# 清除本機過期檔
find "$BACKUP_DIR" -type f -name "*.sql.gz" -mtime +$RETENTION_DAYS -exec rm -f {} \;

echo "✅ 完成完整備份：$FILE_NAME"
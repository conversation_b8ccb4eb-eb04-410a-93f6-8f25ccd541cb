{"ast": null, "code": "const formatRelativeLocale = {\n  lastWeek: \"'sonun<PERSON>' eeee p -'də'\",\n  yesterday: \"'dünən' p -'də'\",\n  today: \"'bugün' p -'də'\",\n  tomorrow: \"'sabah' p -'də'\",\n  nextWeek: \"eeee p -'də'\",\n  other: \"P\"\n};\nexport const formatRelative = (token, _date, _baseDate, _options) => formatRelativeLocale[token];", "map": {"version": 3, "names": ["formatRelativeLocale", "lastWeek", "yesterday", "today", "tomorrow", "nextWeek", "other", "formatRelative", "token", "_date", "_baseDate", "_options"], "sources": ["C:/Users/<USER>/Desktop/demo-git/demo-react/my-fullstack-app/frontend/node_modules/date-fns/locale/az/_lib/formatRelative.js"], "sourcesContent": ["const formatRelativeLocale = {\n  lastWeek: \"'sonun<PERSON>' eeee p -'də'\",\n  yesterday: \"'dünən' p -'də'\",\n  today: \"'bugün' p -'də'\",\n  tomorrow: \"'sabah' p -'də'\",\n  nextWeek: \"eeee p -'də'\",\n  other: \"P\",\n};\n\nexport const formatRelative = (token, _date, _baseDate, _options) =>\n  formatRelativeLocale[token];\n"], "mappings": "AAAA,MAAMA,oBAAoB,GAAG;EAC3BC,QAAQ,EAAE,wBAAwB;EAClCC,SAAS,EAAE,iBAAiB;EAC5BC,KAAK,EAAE,iBAAiB;EACxBC,QAAQ,EAAE,iBAAiB;EAC3BC,QAAQ,EAAE,cAAc;EACxBC,KAAK,EAAE;AACT,CAAC;AAED,OAAO,MAAMC,cAAc,GAAGA,CAACC,KAAK,EAAEC,KAAK,EAAEC,SAAS,EAAEC,QAAQ,KAC9DX,oBAAoB,CAACQ,KAAK,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
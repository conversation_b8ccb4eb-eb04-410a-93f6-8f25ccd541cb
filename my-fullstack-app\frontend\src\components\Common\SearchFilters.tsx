import React, { useState, useCallback } from 'react';
import { InputText } from 'primereact/inputtext';
import { Calendar } from 'primereact/calendar';
import { Dropdown } from 'primereact/dropdown';
import { Button } from 'primereact/button';
import { Card } from 'primereact/card';

interface FilterField {
  key: string;
  label: string;
  type: 'text' | 'date' | 'dateRange' | 'select';
  placeholder?: string;
  options?: { label: string; value: any }[];
  value?: any;
}

interface SearchFiltersProps {
  fields: FilterField[];
  onSearch: (filters: Record<string, any>) => void;
  onReset?: () => void;
  loading?: boolean;
  className?: string;
}

const SearchFilters: React.FC<SearchFiltersProps> = ({
  fields,
  onSearch,
  onReset,
  loading = false,
  className = '',
}) => {
  const [filters, setFilters] = useState<Record<string, any>>(() => {
    const initialFilters: Record<string, any> = {};
    fields.forEach(field => {
      initialFilters[field.key] = field.value || '';
    });
    return initialFilters;
  });

  const handleFilterChange = useCallback((key: string, value: any) => {
    setFilters(prev => ({
      ...prev,
      [key]: value,
    }));
  }, []);

  const handleSearch = useCallback(() => {
    onSearch(filters);
  }, [filters, onSearch]);

  const handleReset = useCallback(() => {
    const resetFilters: Record<string, any> = {};
    fields.forEach(field => {
      resetFilters[field.key] = '';
    });
    setFilters(resetFilters);
    onReset?.();
  }, [fields, onReset]);

  const renderField = (field: FilterField) => {
    const commonProps = {
      value: filters[field.key],
      onChange: (e: any) => handleFilterChange(field.key, e.target?.value || e.value),
      placeholder: field.placeholder,
      className: 'w-full',
    };

    switch (field.type) {
      case 'text':
        return <InputText {...commonProps} />;
      
      case 'date':
        return (
          <Calendar
            {...commonProps}
            dateFormat="yy-mm-dd"
            showIcon
          />
        );
      
      case 'dateRange':
        return (
          <Calendar
            {...commonProps}
            selectionMode="range"
            dateFormat="yy-mm-dd"
            showIcon
          />
        );
      
      case 'select':
        return (
          <Dropdown
            {...commonProps}
            options={field.options}
            showClear
          />
        );
      
      default:
        return <InputText {...commonProps} />;
    }
  };

  return (
    <Card className={`search-filters ${className}`}>
      <div className="grid">
        {fields.map((field) => (
          <div key={field.key} className="col-12 md:col-6 lg:col-4">
            <div className="field">
              <label htmlFor={field.key} className="block text-900 font-medium mb-2">
                {field.label}
              </label>
              {renderField(field)}
            </div>
          </div>
        ))}
        
        <div className="col-12">
          <div className="flex gap-2 justify-content-end">
            <Button
              label="Reset"
              icon="pi pi-times"
              className="p-button-outlined"
              onClick={handleReset}
              disabled={loading}
            />
            <Button
              label="Search"
              icon="pi pi-search"
              onClick={handleSearch}
              loading={loading}
            />
          </div>
        </div>
      </div>
    </Card>
  );
};

export default SearchFilters;

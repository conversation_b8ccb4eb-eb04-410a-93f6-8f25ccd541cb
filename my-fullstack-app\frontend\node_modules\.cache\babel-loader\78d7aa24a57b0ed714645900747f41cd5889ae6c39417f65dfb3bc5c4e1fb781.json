{"ast": null, "code": "import { buildMatchFn } from \"../../_lib/buildMatchFn.js\";\nimport { buildMatchPatternFn } from \"../../_lib/buildMatchPatternFn.js\";\nconst matchOrdinalNumberPattern = /^(\\d+)?/i;\nconst parseOrdinalNumberPattern = /\\d+/i;\nconst matchEraPatterns = {\n  narrow: /^(Î|D)/i,\n  abbreviated: /^(Î\\.?\\s?d\\.?\\s?C\\.?|Î\\.?\\s?e\\.?\\s?n\\.?|D\\.?\\s?C\\.?|e\\.?\\s?n\\.?)/i,\n  wide: /^(Înainte de Cristos|Înaintea erei noastre|Dup<PERSON> Cristo<PERSON>|Era noastră)/i\n};\nconst parseEraPatterns = {\n  any: [/^ÎC/i, /^DC/i],\n  wide: [/^(Înainte de Cristos|Înaintea erei noastre)/i, /^(<PERSON><PERSON><PERSON>risto<PERSON>|Era noastră)/i]\n};\nconst matchQuarterPatterns = {\n  narrow: /^[1234]/i,\n  abbreviated: /^T[1234]/i,\n  wide: /^trimestrul [1234]/i\n};\nconst parseQuarterPatterns = {\n  any: [/1/i, /2/i, /3/i, /4/i]\n};\nconst matchMonthPatterns = {\n  narrow: /^[ifmaasond]/i,\n  abbreviated: /^(ian|feb|mar|apr|mai|iun|iul|aug|sep|oct|noi|dec)/i,\n  wide: /^(ianuarie|februarie|martie|aprilie|mai|iunie|iulie|august|septembrie|octombrie|noiembrie|decembrie)/i\n};\nconst parseMonthPatterns = {\n  narrow: [/^i/i, /^f/i, /^m/i, /^a/i, /^m/i, /^i/i, /^i/i, /^a/i, /^s/i, /^o/i, /^n/i, /^d/i],\n  any: [/^ia/i, /^f/i, /^mar/i, /^ap/i, /^mai/i, /^iun/i, /^iul/i, /^au/i, /^s/i, /^o/i, /^n/i, /^d/i]\n};\nconst matchDayPatterns = {\n  narrow: /^[dlmjvs]/i,\n  short: /^(d|l|ma|mi|j|v|s)/i,\n  abbreviated: /^(dum|lun|mar|mie|jo|vi|sâ)/i,\n  wide: /^(duminica|luni|marţi|miercuri|joi|vineri|sâmbătă)/i\n};\nconst parseDayPatterns = {\n  narrow: [/^d/i, /^l/i, /^m/i, /^m/i, /^j/i, /^v/i, /^s/i],\n  any: [/^d/i, /^l/i, /^ma/i, /^mi/i, /^j/i, /^v/i, /^s/i]\n};\nconst matchDayPeriodPatterns = {\n  narrow: /^(a|p|mn|a|(dimineaţa|după-amiaza|seara|noaptea))/i,\n  any: /^([ap]\\.?\\s?m\\.?|miezul nopții|amiaza|(dimineaţa|după-amiaza|seara|noaptea))/i\n};\nconst parseDayPeriodPatterns = {\n  any: {\n    am: /^a/i,\n    pm: /^p/i,\n    midnight: /^mn/i,\n    noon: /amiaza/i,\n    morning: /dimineaţa/i,\n    afternoon: /după-amiaza/i,\n    evening: /seara/i,\n    night: /noaptea/i\n  }\n};\nexport const match = {\n  ordinalNumber: buildMatchPatternFn({\n    matchPattern: matchOrdinalNumberPattern,\n    parsePattern: parseOrdinalNumberPattern,\n    valueCallback: value => parseInt(value, 10)\n  }),\n  era: buildMatchFn({\n    matchPatterns: matchEraPatterns,\n    defaultMatchWidth: \"wide\",\n    parsePatterns: parseEraPatterns,\n    defaultParseWidth: \"any\"\n  }),\n  quarter: buildMatchFn({\n    matchPatterns: matchQuarterPatterns,\n    defaultMatchWidth: \"wide\",\n    parsePatterns: parseQuarterPatterns,\n    defaultParseWidth: \"any\",\n    valueCallback: index => index + 1\n  }),\n  month: buildMatchFn({\n    matchPatterns: matchMonthPatterns,\n    defaultMatchWidth: \"wide\",\n    parsePatterns: parseMonthPatterns,\n    defaultParseWidth: \"any\"\n  }),\n  day: buildMatchFn({\n    matchPatterns: matchDayPatterns,\n    defaultMatchWidth: \"wide\",\n    parsePatterns: parseDayPatterns,\n    defaultParseWidth: \"any\"\n  }),\n  dayPeriod: buildMatchFn({\n    matchPatterns: matchDayPeriodPatterns,\n    defaultMatchWidth: \"any\",\n    parsePatterns: parseDayPeriodPatterns,\n    defaultParseWidth: \"any\"\n  })\n};", "map": {"version": 3, "names": ["buildMatchFn", "buildMatchPatternFn", "matchOrdinalNumberPattern", "parseOrdinalNumberPattern", "matchEraPatterns", "narrow", "abbreviated", "wide", "parseEraPatterns", "any", "matchQuarterPatterns", "parseQuarterPatterns", "matchMonthPatterns", "parseMonthPatterns", "matchDayPatterns", "short", "parseDayPatterns", "matchDayPeriodPatterns", "parseDayPeriodPatterns", "am", "pm", "midnight", "noon", "morning", "afternoon", "evening", "night", "match", "ordinalNumber", "matchPattern", "parsePattern", "valueCallback", "value", "parseInt", "era", "matchPatterns", "defaultMatchWidth", "parsePatterns", "defaultParseWidth", "quarter", "index", "month", "day", "<PERSON><PERSON><PERSON><PERSON>"], "sources": ["C:/Users/<USER>/Desktop/demo-git/demo-react/my-fullstack-app/frontend/node_modules/date-fns/locale/ro/_lib/match.js"], "sourcesContent": ["import { buildMatchFn } from \"../../_lib/buildMatchFn.js\";\nimport { buildMatchPatternFn } from \"../../_lib/buildMatchPatternFn.js\";\n\nconst matchOrdinalNumberPattern = /^(\\d+)?/i;\nconst parseOrdinalNumberPattern = /\\d+/i;\n\nconst matchEraPatterns = {\n  narrow: /^(Î|D)/i,\n  abbreviated:\n    /^(Î\\.?\\s?d\\.?\\s?C\\.?|Î\\.?\\s?e\\.?\\s?n\\.?|D\\.?\\s?C\\.?|e\\.?\\s?n\\.?)/i,\n  wide: /^(Înainte de Cristos|Înaintea erei noastre|Dup<PERSON> Cristo<PERSON>|Era noastră)/i,\n};\nconst parseEraPatterns = {\n  any: [/^ÎC/i, /^DC/i],\n  wide: [\n    /^(Înainte de Cristos|Înaintea erei noastre)/i,\n    /^(<PERSON><PERSON><PERSON>risto<PERSON>|Era noastră)/i,\n  ],\n};\n\nconst matchQuarterPatterns = {\n  narrow: /^[1234]/i,\n  abbreviated: /^T[1234]/i,\n  wide: /^trimestrul [1234]/i,\n};\nconst parseQuarterPatterns = {\n  any: [/1/i, /2/i, /3/i, /4/i],\n};\n\nconst matchMonthPatterns = {\n  narrow: /^[ifmaasond]/i,\n  abbreviated: /^(ian|feb|mar|apr|mai|iun|iul|aug|sep|oct|noi|dec)/i,\n  wide: /^(ianuarie|februarie|martie|aprilie|mai|iunie|iulie|august|septembrie|octombrie|noiembrie|decembrie)/i,\n};\nconst parseMonthPatterns = {\n  narrow: [\n    /^i/i,\n    /^f/i,\n    /^m/i,\n    /^a/i,\n    /^m/i,\n    /^i/i,\n    /^i/i,\n    /^a/i,\n    /^s/i,\n    /^o/i,\n    /^n/i,\n    /^d/i,\n  ],\n\n  any: [\n    /^ia/i,\n    /^f/i,\n    /^mar/i,\n    /^ap/i,\n    /^mai/i,\n    /^iun/i,\n    /^iul/i,\n    /^au/i,\n    /^s/i,\n    /^o/i,\n    /^n/i,\n    /^d/i,\n  ],\n};\n\nconst matchDayPatterns = {\n  narrow: /^[dlmjvs]/i,\n  short: /^(d|l|ma|mi|j|v|s)/i,\n  abbreviated: /^(dum|lun|mar|mie|jo|vi|sâ)/i,\n  wide: /^(duminica|luni|marţi|miercuri|joi|vineri|sâmbătă)/i,\n};\nconst parseDayPatterns = {\n  narrow: [/^d/i, /^l/i, /^m/i, /^m/i, /^j/i, /^v/i, /^s/i],\n  any: [/^d/i, /^l/i, /^ma/i, /^mi/i, /^j/i, /^v/i, /^s/i],\n};\n\nconst matchDayPeriodPatterns = {\n  narrow: /^(a|p|mn|a|(dimineaţa|după-amiaza|seara|noaptea))/i,\n  any: /^([ap]\\.?\\s?m\\.?|miezul nopții|amiaza|(dimineaţa|după-amiaza|seara|noaptea))/i,\n};\nconst parseDayPeriodPatterns = {\n  any: {\n    am: /^a/i,\n    pm: /^p/i,\n    midnight: /^mn/i,\n    noon: /amiaza/i,\n    morning: /dimineaţa/i,\n    afternoon: /după-amiaza/i,\n    evening: /seara/i,\n    night: /noaptea/i,\n  },\n};\n\nexport const match = {\n  ordinalNumber: buildMatchPatternFn({\n    matchPattern: matchOrdinalNumberPattern,\n    parsePattern: parseOrdinalNumberPattern,\n    valueCallback: (value) => parseInt(value, 10),\n  }),\n\n  era: buildMatchFn({\n    matchPatterns: matchEraPatterns,\n    defaultMatchWidth: \"wide\",\n    parsePatterns: parseEraPatterns,\n    defaultParseWidth: \"any\",\n  }),\n\n  quarter: buildMatchFn({\n    matchPatterns: matchQuarterPatterns,\n    defaultMatchWidth: \"wide\",\n    parsePatterns: parseQuarterPatterns,\n    defaultParseWidth: \"any\",\n    valueCallback: (index) => index + 1,\n  }),\n\n  month: buildMatchFn({\n    matchPatterns: matchMonthPatterns,\n    defaultMatchWidth: \"wide\",\n    parsePatterns: parseMonthPatterns,\n    defaultParseWidth: \"any\",\n  }),\n\n  day: buildMatchFn({\n    matchPatterns: matchDayPatterns,\n    defaultMatchWidth: \"wide\",\n    parsePatterns: parseDayPatterns,\n    defaultParseWidth: \"any\",\n  }),\n\n  dayPeriod: buildMatchFn({\n    matchPatterns: matchDayPeriodPatterns,\n    defaultMatchWidth: \"any\",\n    parsePatterns: parseDayPeriodPatterns,\n    defaultParseWidth: \"any\",\n  }),\n};\n"], "mappings": "AAAA,SAASA,YAAY,QAAQ,4BAA4B;AACzD,SAASC,mBAAmB,QAAQ,mCAAmC;AAEvE,MAAMC,yBAAyB,GAAG,UAAU;AAC5C,MAAMC,yBAAyB,GAAG,MAAM;AAExC,MAAMC,gBAAgB,GAAG;EACvBC,MAAM,EAAE,SAAS;EACjBC,WAAW,EACT,mEAAmE;EACrEC,IAAI,EAAE;AACR,CAAC;AACD,MAAMC,gBAAgB,GAAG;EACvBC,GAAG,EAAE,CAAC,MAAM,EAAE,MAAM,CAAC;EACrBF,IAAI,EAAE,CACJ,8CAA8C,EAC9C,8BAA8B;AAElC,CAAC;AAED,MAAMG,oBAAoB,GAAG;EAC3BL,MAAM,EAAE,UAAU;EAClBC,WAAW,EAAE,WAAW;EACxBC,IAAI,EAAE;AACR,CAAC;AACD,MAAMI,oBAAoB,GAAG;EAC3BF,GAAG,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI;AAC9B,CAAC;AAED,MAAMG,kBAAkB,GAAG;EACzBP,MAAM,EAAE,eAAe;EACvBC,WAAW,EAAE,qDAAqD;EAClEC,IAAI,EAAE;AACR,CAAC;AACD,MAAMM,kBAAkB,GAAG;EACzBR,MAAM,EAAE,CACN,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,CACN;EAEDI,GAAG,EAAE,CACH,MAAM,EACN,KAAK,EACL,OAAO,EACP,MAAM,EACN,OAAO,EACP,OAAO,EACP,OAAO,EACP,MAAM,EACN,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK;AAET,CAAC;AAED,MAAMK,gBAAgB,GAAG;EACvBT,MAAM,EAAE,YAAY;EACpBU,KAAK,EAAE,qBAAqB;EAC5BT,WAAW,EAAE,8BAA8B;EAC3CC,IAAI,EAAE;AACR,CAAC;AACD,MAAMS,gBAAgB,GAAG;EACvBX,MAAM,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC;EACzDI,GAAG,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK;AACzD,CAAC;AAED,MAAMQ,sBAAsB,GAAG;EAC7BZ,MAAM,EAAE,oDAAoD;EAC5DI,GAAG,EAAE;AACP,CAAC;AACD,MAAMS,sBAAsB,GAAG;EAC7BT,GAAG,EAAE;IACHU,EAAE,EAAE,KAAK;IACTC,EAAE,EAAE,KAAK;IACTC,QAAQ,EAAE,MAAM;IAChBC,IAAI,EAAE,SAAS;IACfC,OAAO,EAAE,YAAY;IACrBC,SAAS,EAAE,cAAc;IACzBC,OAAO,EAAE,QAAQ;IACjBC,KAAK,EAAE;EACT;AACF,CAAC;AAED,OAAO,MAAMC,KAAK,GAAG;EACnBC,aAAa,EAAE3B,mBAAmB,CAAC;IACjC4B,YAAY,EAAE3B,yBAAyB;IACvC4B,YAAY,EAAE3B,yBAAyB;IACvC4B,aAAa,EAAGC,KAAK,IAAKC,QAAQ,CAACD,KAAK,EAAE,EAAE;EAC9C,CAAC,CAAC;EAEFE,GAAG,EAAElC,YAAY,CAAC;IAChBmC,aAAa,EAAE/B,gBAAgB;IAC/BgC,iBAAiB,EAAE,MAAM;IACzBC,aAAa,EAAE7B,gBAAgB;IAC/B8B,iBAAiB,EAAE;EACrB,CAAC,CAAC;EAEFC,OAAO,EAAEvC,YAAY,CAAC;IACpBmC,aAAa,EAAEzB,oBAAoB;IACnC0B,iBAAiB,EAAE,MAAM;IACzBC,aAAa,EAAE1B,oBAAoB;IACnC2B,iBAAiB,EAAE,KAAK;IACxBP,aAAa,EAAGS,KAAK,IAAKA,KAAK,GAAG;EACpC,CAAC,CAAC;EAEFC,KAAK,EAAEzC,YAAY,CAAC;IAClBmC,aAAa,EAAEvB,kBAAkB;IACjCwB,iBAAiB,EAAE,MAAM;IACzBC,aAAa,EAAExB,kBAAkB;IACjCyB,iBAAiB,EAAE;EACrB,CAAC,CAAC;EAEFI,GAAG,EAAE1C,YAAY,CAAC;IAChBmC,aAAa,EAAErB,gBAAgB;IAC/BsB,iBAAiB,EAAE,MAAM;IACzBC,aAAa,EAAErB,gBAAgB;IAC/BsB,iBAAiB,EAAE;EACrB,CAAC,CAAC;EAEFK,SAAS,EAAE3C,YAAY,CAAC;IACtBmC,aAAa,EAAElB,sBAAsB;IACrCmB,iBAAiB,EAAE,KAAK;IACxBC,aAAa,EAAEnB,sBAAsB;IACrCoB,iBAAiB,EAAE;EACrB,CAAC;AACH,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
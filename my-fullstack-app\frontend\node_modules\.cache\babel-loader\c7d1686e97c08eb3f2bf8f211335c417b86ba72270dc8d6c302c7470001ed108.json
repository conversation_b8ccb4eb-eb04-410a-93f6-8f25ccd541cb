{"ast": null, "code": "import{formatUtcToTaipei}from\"../../utils/dateUtils\";import{Button}from'primereact/button';import{Calendar}from'primereact/calendar';import{Column}from'primereact/column';import{ConfirmDialog}from'primereact/confirmdialog';import{DataTable}from'primereact/datatable';import{Dialog}from'primereact/dialog';import{Dropdown}from'primereact/dropdown';import{InputText}from\"primereact/inputtext\";import{ProgressBar}from'primereact/progressbar';import LoadingSpinner from'../Common/LoadingSpinner';import{Toast}from\"primereact/toast\";import React,{useRef,useState,useEffect}from'react';import{useNavigate}from\"react-router-dom\";import{ROUTES}from\"../../constants/routes\";import useReceipt from'../../hooks/useReceipt';import api from'../../services/api';import connection from\"../../services/signalr\";import{Card}from'primereact/card';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const ReceiptsPage=()=>{const navigate=useNavigate();const toast=useRef(null);const[name,setName]=useState('');const[nationalId,setNationalId]=useState('');const[starttime,setStarttime]=useState(undefined);const[endtime,setEndtime]=useState(undefined);const[refreshKey,setRefreshKey]=useState(0);const[searchParams,setSearchParams]=useState({name:'',nationalId:'',starttime:null,endtime:null,refreshKey:0});// 批量匯出相關狀態\nconst[selectedReceipts,setSelectedReceipts]=useState([]);const[showExportDialog,setShowExportDialog]=useState(false);const[titleCode,setTitleCode]=useState(0);const[exportProgress,setExportProgress]=useState(0);const[isExporting,setIsExporting]=useState(false);// 送信相關狀態\nconst[showEmailDialog,setShowEmailDialog]=useState(false);const[selectedReceiptForEmail,setSelectedReceiptForEmail]=useState(null);const[recipientEmail,setRecipientEmail]=useState('');const[isSendingEmail,setIsSendingEmail]=useState(false);const{receipts,loading}=useReceipt(searchParams.name,searchParams.nationalId,searchParams.starttime,searchParams.endtime,refreshKey);// 下拉選單選項\nconst titleOptions=[{label:'全部',value:0},{label:'繳款人收執聯',value:1},{label:'單位存根聯',value:2},{label:'單位扣底聯',value:3}];const genderdict={\"1\":\"男性\",\"2\":\"女性\",\"3\":\"其他\"};// 初始化 SignalR 連接\nuseEffect(()=>{connection.start().then(()=>{console.log(\"已連線至 SignalR\");//console.log(\"連線 ID\", connection.connectionId);\n}).catch(err=>console.error(\"SignalR 連線失敗:\",err));connection.on(\"ReportProgress\",value=>{setExportProgress(value);});connection.on(\"ReportFinished\",msg=>{setExportProgress(msg);});return()=>{connection.stop();};},[]);const handleSearchClick=()=>{setRefreshKey(refreshKey+1);setSearchParams({name,nationalId,starttime,endtime,refreshKey});};const Reload=()=>{// 重新觸發 usePatient，等於重新查詢\nsetRefreshKey(prev=>prev+1);};// 批量匯出按鈕點擊\nconst handleBatchExport=()=>{if(selectedReceipts.length===0){var _toast$current;(_toast$current=toast.current)===null||_toast$current===void 0?void 0:_toast$current.show({severity:'error',summary:'錯誤',detail:'請勾選欲匯出的收據'});return;}setShowExportDialog(true);};// 執行批量匯出\nconst executeBatchExport=async()=>{if(!connection){var _toast$current2;(_toast$current2=toast.current)===null||_toast$current2===void 0?void 0:_toast$current2.show({severity:'error',summary:'錯誤',detail:'SignalR 連接未建立'});return;}try{setIsExporting(true);setExportProgress(0);const treatmentIds=selectedReceipts.map(receipt=>receipt.id);const orderNos=selectedReceipts.map(receipt=>receipt.receiptOrderNo);const requestBody={TreatmentId:treatmentIds,orderNo:orderNos,Titlecode:titleCode};const response=await api.post('/api/receipt/ExportReceiptsLisrPdf',requestBody,{params:{connectionId:connection.connectionId},responseType:'blob',headers:{'Content-Type':'application/json'// Axios 通常會自動設定，但明確指定更佳\n}});// 產生 blob url\nconst file=new Blob([response.data],{type:'application/pdf'});const fileURL=URL.createObjectURL(file);// 在新分頁開啟 PDF\nwindow.open(fileURL);setShowExportDialog(false);setSelectedReceipts([]);}catch(error){var _toast$current3,_error$response;setIsExporting(false);setExportProgress(0);(_toast$current3=toast.current)===null||_toast$current3===void 0?void 0:_toast$current3.show({severity:'error',summary:'錯誤',detail:((_error$response=error.response)===null||_error$response===void 0?void 0:_error$response.data)||'匯出失敗'});}};// 開啟送信對話框\nconst handleSendEmail=receipt=>{setSelectedReceiptForEmail(receipt);setRecipientEmail(receipt.patientEmail);setShowEmailDialog(true);};// 執行送信\nconst executeSendEmail=async()=>{if(!recipientEmail.trim()){var _toast$current4;(_toast$current4=toast.current)===null||_toast$current4===void 0?void 0:_toast$current4.show({severity:'error',summary:'錯誤',detail:'請輸入收件人郵箱'});return;}// 簡單的郵箱格式驗證\nconst emailRegex=/^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;if(!emailRegex.test(recipientEmail)){var _toast$current5;(_toast$current5=toast.current)===null||_toast$current5===void 0?void 0:_toast$current5.show({severity:'error',summary:'錯誤',detail:'請輸入有效的郵箱地址'});return;}try{var _toast$current6;setIsSendingEmail(true);await api.post('/api/receipt/SendReceiptEmail',{email:recipientEmail,orderNo:selectedReceiptForEmail.receiptOrderNo});(_toast$current6=toast.current)===null||_toast$current6===void 0?void 0:_toast$current6.show({severity:'success',summary:'成功',detail:'收據郵件發送成功'});setShowEmailDialog(false);setRecipientEmail('');setSelectedReceiptForEmail(null);}catch(error){var _toast$current7,_error$response2,_error$response2$data;(_toast$current7=toast.current)===null||_toast$current7===void 0?void 0:_toast$current7.show({severity:'error',summary:'錯誤',detail:((_error$response2=error.response)===null||_error$response2===void 0?void 0:(_error$response2$data=_error$response2.data)===null||_error$response2$data===void 0?void 0:_error$response2$data.error)||'郵件發送失敗'});}finally{setIsSendingEmail(false);}};const paginatorLeft=/*#__PURE__*/_jsx(Button,{type:\"button\",icon:\"pi pi-refresh\",text:true,onClick:()=>Reload()});const paginatorRight=/*#__PURE__*/_jsx(\"div\",{});const optionBodyTemplate=rowData=>{return/*#__PURE__*/_jsxs(\"div\",{className:\"flex gap-2\",children:[/*#__PURE__*/_jsx(Button,{label:\"\\u6AA2\\u8996\",type:\"button\",icon:\"pi pi-file-edit\",onClick:()=>navigate(ROUTES.RECEIPT_DETAIL,{state:{treatment:rowData}}),size:\"small\",severity:\"info\",style:{fontSize:'1rem',margin:'3px'}}),/*#__PURE__*/_jsx(Button,{label:\"\\u9001\\u4FE1\",type:\"button\",icon:\"pi pi-send\",onClick:()=>handleSendEmail(rowData),size:\"small\",severity:\"success\",style:{fontSize:'1rem',margin:'3px'},disabled:rowData.patientEmail?false:true})]});};const genderBodyTemplate=rowData=>{var data=String(rowData.patientGender);const gendar=genderdict[data];return/*#__PURE__*/_jsx(\"div\",{children:gendar});};const formatDate=value=>{if(!value)return'';return formatUtcToTaipei(value,\"yyyy/MM/dd HH:mm:ss\");};const formatAge=value=>{if(!value)return\"\";const date=new Date(value);const today=new Date();let age=today.getFullYear()-date.getFullYear();const hasNotHadBirthdayThisYear=today.getMonth()<date.getMonth()||today.getMonth()===date.getMonth()&&today.getDate()<date.getDate();if(hasNotHadBirthdayThisYear){age--;}return age;};if(loading){return/*#__PURE__*/_jsx(LoadingSpinner,{message:\"\\u8F09\\u5165\\u6536\\u64DA\\u8CC7\\u6599\\u4E2D...\"});}return/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(Toast,{ref:toast}),/*#__PURE__*/_jsx(ConfirmDialog,{}),/*#__PURE__*/_jsx(Card,{title:\"\\u6536\\u64DA\\u7BA1\\u7406\",className:\"mb-4\",children:/*#__PURE__*/_jsx(\"p\",{className:\"text-600 line-height-3 m-0\",children:\"\\u6536\\u64DA\\u7BA1\\u7406\\u9801\\u9762\\uFF0C\\u53EF\\u4EE5\\u67E5\\u8A62\\u3001\\u6AA2\\u8996\\u3001\\u88FD\\u4F5CPDF\\u3001\\u767C\\u9001Email\\u6536\\u64DA\\u8CC7\\u6599\\u3002\"})}),/*#__PURE__*/_jsx(Card,{className:\"mb-4\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"grid\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"col-6 md:col-3\",children:/*#__PURE__*/_jsx(InputText,{id:\"name\",type:\"text\",value:name,onChange:e=>setName(e.target.value),className:\"w-full\",placeholder:\"\\u75C5\\u60A3\\u59D3\\u540D\"})}),/*#__PURE__*/_jsx(\"div\",{className:\"col-6 md:col-3\",children:/*#__PURE__*/_jsx(InputText,{id:\"nationalId\",type:\"text\",value:nationalId,onChange:e=>setNationalId(e.target.value),className:\"w-full\",placeholder:\"\\u75C5\\u60A3\\u8EAB\\u5206\\u8B49\"})}),/*#__PURE__*/_jsx(\"div\",{className:\"col-6 md:col-3\",children:/*#__PURE__*/_jsx(Calendar,{id:\"starttime\",value:starttime,onChange:e=>setStarttime(e.value),placeholder:\"\\u958B\\u59CB\\u6642\\u9593\",className:\"w-full\",dateFormat:\"yy/mm/dd\",showIcon:true})}),/*#__PURE__*/_jsx(\"div\",{className:\"col-6 md:col-3\",children:/*#__PURE__*/_jsx(Calendar,{id:\"endtime\",value:endtime,onChange:e=>setEndtime(e.value),placeholder:\"\\u7D50\\u675F\\u6642\\u9593\",className:\"w-full\",dateFormat:\"yy/mm/dd\",showIcon:true})}),/*#__PURE__*/_jsx(\"div\",{className:\"col-12 md:col-4\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex gap-2\",children:[/*#__PURE__*/_jsx(Button,{label:\"\\u67E5\\u8A62\",icon:\"pi pi-search\",onClick:handleSearchClick}),/*#__PURE__*/_jsx(Button,{label:\"\\u6279\\u91CF\\u532F\\u51FA\",icon:\"pi pi-download\",onClick:handleBatchExport,className:\"p-button-success\",disabled:selectedReceipts.length===0})]})})]})}),/*#__PURE__*/_jsx(Card,{children:/*#__PURE__*/_jsxs(DataTable,{value:receipts,selection:selectedReceipts,onSelectionChange:e=>setSelectedReceipts(e.value),paginator:true,rows:10,rowsPerPageOptions:[10,20,30,40],emptyMessage:\"\\u6C92\\u6709\\u627E\\u5230\\u6536\\u64DA\\u8CC7\\u6599\",tableStyle:{minWidth:'50rem'},paginatorLeft:paginatorLeft,paginatorRight:paginatorRight,children:[/*#__PURE__*/_jsx(Column,{selectionMode:\"multiple\",headerStyle:{width:'2%'}}),/*#__PURE__*/_jsx(Column,{field:\"orderNo\",header:\"\\u6848\\u865F\",style:{width:'5%'}}),/*#__PURE__*/_jsx(Column,{field:\"receiptOrderNo\",header:\"\\u6536\\u64DA\\u7DE8\\u865F\",style:{width:'5%'}}),/*#__PURE__*/_jsx(Column,{field:\"patientName\",header:\"\\u75C5\\u60A3\\u59D3\\u540D\",style:{width:'5%'}}),/*#__PURE__*/_jsx(Column,{field:\"patientGender\",header:\"\\u6027\\u5225\",style:{width:'3%'},body:genderBodyTemplate}),/*#__PURE__*/_jsx(Column,{field:\"patientBirthDate\",header:\"\\u5E74\\u9F61\",style:{width:'3%'},body:rowData=>formatAge(rowData.patientBirthDate)}),/*#__PURE__*/_jsx(Column,{field:\"receiptCreatedAt\",header:\"\\u65B0\\u589E\\u65E5\\u671F\",style:{width:'8%'},body:rowData=>formatDate(rowData.receiptCreatedAt)}),/*#__PURE__*/_jsx(Column,{field:\"receiptUpdatedAt\",header:\"\\u66F4\\u65B0\\u65E5\\u671F\",style:{width:'8%'},body:rowData=>formatDate(rowData.receiptUpdatedAt)}),/*#__PURE__*/_jsx(Column,{field:\"receiptOperatorUserName\",header:\"\\u64CD\\u4F5C\\u4EBA\",style:{width:'5%'}}),/*#__PURE__*/_jsx(Column,{field:\"Option\",header:\"\\u529F\\u80FD\",style:{width:'12%'},body:optionBodyTemplate})]})}),/*#__PURE__*/_jsxs(Dialog,{header:\"\\u6279\\u91CF\\u532F\\u51FA\\u6536\\u64DA\",visible:showExportDialog,style:{width:'50vw'},onHide:()=>setShowExportDialog(false),modal:true,children:[/*#__PURE__*/_jsxs(\"div\",{className:\"mb-4\",children:[/*#__PURE__*/_jsxs(\"h5\",{children:[\"\\u5DF2\\u9078\\u64C7\\u7684\\u6536\\u64DA (\",selectedReceipts.length,\" \\u7B46)\"]}),/*#__PURE__*/_jsxs(DataTable,{value:selectedReceipts,scrollable:true,scrollHeight:\"300px\",emptyMessage:\"\\u6C92\\u6709\\u9078\\u64C7\\u7684\\u6536\\u64DA\",children:[/*#__PURE__*/_jsx(Column,{field:\"orderNo\",header:\"\\u6848\\u865F\",style:{width:'15%'}}),/*#__PURE__*/_jsx(Column,{field:\"receiptorderNo\",header:\"\\u6536\\u64DA\\u7DE8\\u865F\",style:{width:'15%'}}),/*#__PURE__*/_jsx(Column,{field:\"patientName\",header:\"\\u75C5\\u60A3\\u59D3\\u540D\",style:{width:'15%'}}),/*#__PURE__*/_jsx(Column,{field:\"receiptCreatedAt\",header:\"\\u65B0\\u589E\\u65E5\\u671F\",style:{width:'20%'},body:rowData=>formatDate(rowData.receiptCreatedAt)})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"grid align-items-end\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"col-12 md:col-4\",children:[/*#__PURE__*/_jsx(\"label\",{htmlFor:\"titleCode\",className:\"font-bold block mb-2\",children:\"\\u6536\\u64DA\\u985E\\u578B\"}),/*#__PURE__*/_jsx(Dropdown,{id:\"titleCode\",value:titleCode,options:titleOptions,onChange:e=>setTitleCode(e.value),placeholder:\"\\u9078\\u64C7\\u6536\\u64DA\\u985E\\u578B\",className:\"w-full\"})]}),/*#__PURE__*/_jsx(\"div\",{className:\"col-12 md:col-4\",children:/*#__PURE__*/_jsx(Button,{label:\"\\u532F\\u51FA\",icon:\"pi pi-download\",onClick:executeBatchExport,disabled:isExporting,className:\"p-button-success w-full\"})}),/*#__PURE__*/_jsxs(\"div\",{className:\"col-12 md:col-4\",children:[/*#__PURE__*/_jsx(\"label\",{className:\"font-bold block mb-2\",children:\"\\u532F\\u51FA\\u9032\\u5EA6\"}),/*#__PURE__*/_jsx(ProgressBar,{value:exportProgress,showValue:false,style:{height:'1.5rem'}}),/*#__PURE__*/_jsxs(\"small\",{className:\"text-center block mt-1\",children:[exportProgress,\"%\"]})]})]})]}),/*#__PURE__*/_jsxs(Dialog,{header:\"\\u767C\\u9001\\u6536\\u64DA\\u90F5\\u4EF6\",visible:showEmailDialog,style:{width:'500px'},onHide:()=>setShowEmailDialog(false),modal:true,children:[/*#__PURE__*/_jsxs(\"div\",{className:\"mb-4\",children:[selectedReceiptForEmail&&/*#__PURE__*/_jsxs(\"div\",{className:\"p-3 bg-gray-50 border-round mb-3\",children:[/*#__PURE__*/_jsx(\"h3\",{className:\"mt-0 mb-2\",children:\"\\u6536\\u64DA\\u8CC7\\u8A0A\"}),/*#__PURE__*/_jsxs(\"p\",{className:\"mb-1\",children:[/*#__PURE__*/_jsx(\"strong\",{children:\"\\u6536\\u64DA\\u7DE8\\u865F:\"}),\" \",selectedReceiptForEmail.receiptOrderNo]}),/*#__PURE__*/_jsxs(\"p\",{className:\"mb-1\",children:[/*#__PURE__*/_jsx(\"strong\",{children:\"\\u75C5\\u60A3\\u59D3\\u540D:\"}),\" \",selectedReceiptForEmail.patientName]}),/*#__PURE__*/_jsxs(\"p\",{className:\"mb-0\",children:[/*#__PURE__*/_jsx(\"strong\",{children:\"\\u5EFA\\u7ACB\\u65E5\\u671F:\"}),\" \",formatDate(selectedReceiptForEmail.receiptCreatedAt)]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"field\",children:[/*#__PURE__*/_jsx(\"label\",{htmlFor:\"recipientEmail\",className:\"font-bold block mb-2\",children:\"\\u6536\\u4EF6\\u4EBA\\u90F5\\u7BB1 *\"}),/*#__PURE__*/_jsx(InputText,{id:\"recipientEmail\",value:recipientEmail,onChange:e=>setRecipientEmail(e.target.value),placeholder:\"\\u8ACB\\u8F38\\u5165\\u6536\\u4EF6\\u4EBA\\u90F5\\u7BB1\\u5730\\u5740\",className:\"w-full\",disabled:isSendingEmail})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex justify-content-end gap-2\",children:[/*#__PURE__*/_jsx(Button,{label:\"\\u53D6\\u6D88\",icon:\"pi pi-times\",onClick:()=>setShowEmailDialog(false),className:\"p-button-secondary\",disabled:isSendingEmail}),/*#__PURE__*/_jsx(Button,{label:isSendingEmail?\"發送中...\":\"發送\",icon:isSendingEmail?\"pi pi-spin pi-spinner\":\"pi pi-send\",onClick:executeSendEmail,disabled:isSendingEmail||!recipientEmail.trim(),className:\"p-button-primary\"})]})]})]});};export default ReceiptsPage;", "map": {"version": 3, "names": ["formatUtcToTaipei", "<PERSON><PERSON>", "Calendar", "Column", "ConfirmDialog", "DataTable", "Dialog", "Dropdown", "InputText", "ProgressBar", "LoadingSpinner", "Toast", "React", "useRef", "useState", "useEffect", "useNavigate", "ROUTES", "useReceipt", "api", "connection", "Card", "jsx", "_jsx", "jsxs", "_jsxs", "ReceiptsPage", "navigate", "toast", "name", "setName", "nationalId", "setNationalId", "starttime", "set<PERSON><PERSON><PERSON><PERSON>", "undefined", "endtime", "setEndtime", "refresh<PERSON><PERSON>", "setRefresh<PERSON>ey", "searchParams", "setSearchParams", "selectedReceipts", "setSelectedReceipts", "showExportDialog", "setShowExportDialog", "titleCode", "setTitleCode", "exportProgress", "setExportProgress", "isExporting", "setIsExporting", "showEmailDialog", "setShowEmailDialog", "selectedReceiptForEmail", "setSelectedReceiptForEmail", "recipientEmail", "setRecipientEmail", "isSendingEmail", "setIsSendingEmail", "receipts", "loading", "titleOptions", "label", "value", "genderdict", "start", "then", "console", "log", "catch", "err", "error", "on", "msg", "stop", "handleSearchClick", "Reload", "prev", "handleBatchExport", "length", "_toast$current", "current", "show", "severity", "summary", "detail", "executeBatchExport", "_toast$current2", "treatmentIds", "map", "receipt", "id", "orderNos", "receiptOrderNo", "requestBody", "TreatmentId", "orderNo", "Titlecode", "response", "post", "params", "connectionId", "responseType", "headers", "file", "Blob", "data", "type", "fileURL", "URL", "createObjectURL", "window", "open", "_toast$current3", "_error$response", "handleSendEmail", "patientEmail", "executeSendEmail", "trim", "_toast$current4", "emailRegex", "test", "_toast$current5", "_toast$current6", "email", "_toast$current7", "_error$response2", "_error$response2$data", "paginatorLeft", "icon", "text", "onClick", "paginatorRight", "optionBodyTemplate", "rowData", "className", "children", "RECEIPT_DETAIL", "state", "treatment", "size", "style", "fontSize", "margin", "disabled", "genderBodyTemplate", "String", "patientGender", "gendar", "formatDate", "formatAge", "date", "Date", "today", "age", "getFullYear", "hasNotHadBirthdayThisYear", "getMonth", "getDate", "message", "ref", "title", "onChange", "e", "target", "placeholder", "dateFormat", "showIcon", "selection", "onSelectionChange", "paginator", "rows", "rowsPerPageOptions", "emptyMessage", "tableStyle", "min<PERSON><PERSON><PERSON>", "selectionMode", "headerStyle", "width", "field", "header", "body", "patientBirthDate", "receiptCreatedAt", "receiptUpdatedAt", "visible", "onHide", "modal", "scrollable", "scrollHeight", "htmlFor", "options", "showValue", "height", "patientName"], "sources": ["C:/Users/<USER>/Desktop/demo-git/demo-react/my-fullstack-app/frontend/src/components/Page/ReceiptsPage.tsx"], "sourcesContent": ["import { formatUtcToTaipei } from \"../../utils/dateUtils\";\r\nimport { Button } from 'primereact/button';\r\nimport { Calendar } from 'primereact/calendar';\r\nimport { Column } from 'primereact/column';\r\nimport { ConfirmDialog } from 'primereact/confirmdialog';\r\nimport { DataTable } from 'primereact/datatable';\r\nimport { Dialog } from 'primereact/dialog';\r\nimport { Dropdown } from 'primereact/dropdown';\r\nimport { InputText } from \"primereact/inputtext\";\r\nimport { ProgressBar } from 'primereact/progressbar';\r\nimport LoadingSpinner from '../Common/LoadingSpinner';\r\nimport { Toast } from \"primereact/toast\";\r\nimport React, { useRef, useState, useEffect } from 'react';\r\nimport { useNavigate } from \"react-router-dom\";\r\nimport { ROUTES } from \"../../constants/routes\";\r\nimport useReceipt from '../../hooks/useReceipt';\r\nimport api from '../../services/api';\r\nimport connection from \"../../services/signalr\";\r\nimport { Card } from 'primereact/card';\r\n\r\nconst ReceiptsPage: React.FC = () => {\r\n    const navigate = useNavigate();\r\n    const toast = useRef<Toast>(null);\r\n    const [name, setName] = useState('');\r\n    const [nationalId, setNationalId] = useState('');\r\n    const [starttime, setStarttime] = useState<Date | null | undefined>(undefined);\r\n    const [endtime, setEndtime] = useState<Date | null | undefined>(undefined);\r\n    const [refreshKey, setRefreshKey] = useState(0);\r\n\r\n    const [searchParams, setSearchParams] = useState({\r\n        name: '',\r\n        nationalId: '',\r\n        starttime: null as Date | null | undefined,\r\n        endtime: null as Date | null | undefined,\r\n        refreshKey: 0,\r\n    });\r\n\r\n    // 批量匯出相關狀態\r\n    const [selectedReceipts, setSelectedReceipts] = useState<any[]>([]);\r\n    const [showExportDialog, setShowExportDialog] = useState(false);\r\n    const [titleCode, setTitleCode] = useState(0);\r\n    const [exportProgress, setExportProgress] = useState(0);\r\n    const [isExporting, setIsExporting] = useState(false);\r\n\r\n    // 送信相關狀態\r\n    const [showEmailDialog, setShowEmailDialog] = useState(false);\r\n    const [selectedReceiptForEmail, setSelectedReceiptForEmail] = useState<any>(null);\r\n    const [recipientEmail, setRecipientEmail] = useState('');\r\n    const [isSendingEmail, setIsSendingEmail] = useState(false);\r\n\r\n    const { receipts, loading } = useReceipt(\r\n        searchParams.name, \r\n        searchParams.nationalId, \r\n        searchParams.starttime, \r\n        searchParams.endtime, \r\n        refreshKey\r\n    );\r\n\r\n    // 下拉選單選項\r\n    const titleOptions = [\r\n        { label: '全部', value: 0 },\r\n        { label: '繳款人收執聯', value: 1 },\r\n        { label: '單位存根聯', value: 2 },\r\n        { label: '單位扣底聯', value: 3 }\r\n    ];\r\n\r\n    const genderdict: { [key: string]: string } = {\r\n        \"1\": \"男性\",\r\n        \"2\": \"女性\",\r\n        \"3\": \"其他\"\r\n    };\r\n\r\n    // 初始化 SignalR 連接\r\n    useEffect(() => {\r\n\r\n        connection\r\n        .start()\r\n        .then(() => {\r\n        console.log(\"已連線至 SignalR\");\r\n        //console.log(\"連線 ID\", connection.connectionId);\r\n        })\r\n        .catch(err => console.error(\"SignalR 連線失敗:\", err));\r\n\r\n        connection.on(\"ReportProgress\", (value) => {\r\n        setExportProgress(value);\r\n        });\r\n\r\n        connection.on(\"ReportFinished\", (msg) => {\r\n        setExportProgress(msg);\r\n        });\r\n\r\n        return () => {\r\n        connection.stop();\r\n        };\r\n    }, []);\r\n\r\n    const handleSearchClick = () => {\r\n        setRefreshKey(refreshKey + 1)\r\n        setSearchParams({ name, nationalId, starttime, endtime, refreshKey});\r\n    };\r\n\r\n    const Reload = () => {\r\n        // 重新觸發 usePatient，等於重新查詢\r\n        setRefreshKey(prev => prev + 1);\r\n    };\r\n\r\n    // 批量匯出按鈕點擊\r\n    const handleBatchExport = () => {\r\n        if (selectedReceipts.length === 0) {\r\n            toast.current?.show({\r\n                severity: 'error',\r\n                summary: '錯誤',\r\n                detail: '請勾選欲匯出的收據'\r\n            });\r\n            return;\r\n        }\r\n        setShowExportDialog(true);\r\n    };\r\n\r\n    // 執行批量匯出\r\n    const executeBatchExport = async () => {\r\n        if (!connection) {\r\n            toast.current?.show({\r\n                severity: 'error',\r\n                summary: '錯誤',\r\n                detail: 'SignalR 連接未建立'\r\n            });\r\n            return;\r\n        }\r\n\r\n        try {\r\n            setIsExporting(true);\r\n            setExportProgress(0);\r\n\r\n            const treatmentIds = selectedReceipts.map(receipt => receipt.id);\r\n            const orderNos = selectedReceipts.map(receipt => receipt.receiptOrderNo);\r\n            \r\n            const requestBody = {\r\n                TreatmentId: treatmentIds,\r\n                orderNo: orderNos,\r\n                Titlecode: titleCode\r\n            };\r\n\r\n            const response = await api.post('/api/receipt/ExportReceiptsLisrPdf', requestBody, {\r\n                params: {\r\n                    connectionId: connection.connectionId\r\n                },\r\n                responseType: 'blob',\r\n                headers: {\r\n                    'Content-Type': 'application/json' // Axios 通常會自動設定，但明確指定更佳\r\n                }\r\n            });\r\n\r\n            // 產生 blob url\r\n            const file = new Blob([response.data], { type: 'application/pdf' });\r\n            const fileURL = URL.createObjectURL(file);\r\n\r\n            // 在新分頁開啟 PDF\r\n            window.open(fileURL);\r\n\r\n            setShowExportDialog(false);\r\n            setSelectedReceipts([]);\r\n\r\n        } catch (error: any) {\r\n            setIsExporting(false);\r\n            setExportProgress(0);\r\n            toast.current?.show({\r\n                severity: 'error',\r\n                summary: '錯誤',\r\n                detail: error.response?.data || '匯出失敗'\r\n            });\r\n        }\r\n    };\r\n\r\n    // 開啟送信對話框\r\n    const handleSendEmail = (receipt: any) => {\r\n        setSelectedReceiptForEmail(receipt);\r\n        setRecipientEmail(receipt.patientEmail);\r\n        setShowEmailDialog(true);\r\n    };\r\n\r\n    // 執行送信\r\n    const executeSendEmail = async () => {\r\n        if (!recipientEmail.trim()) {\r\n            toast.current?.show({\r\n                severity: 'error',\r\n                summary: '錯誤',\r\n                detail: '請輸入收件人郵箱'\r\n            });\r\n            return;\r\n        }\r\n\r\n        // 簡單的郵箱格式驗證\r\n        const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\r\n        if (!emailRegex.test(recipientEmail)) {\r\n            toast.current?.show({\r\n                severity: 'error',\r\n                summary: '錯誤',\r\n                detail: '請輸入有效的郵箱地址'\r\n            });\r\n            return;\r\n        }\r\n\r\n        try {\r\n            setIsSendingEmail(true);\r\n\r\n            await api.post('/api/receipt/SendReceiptEmail', {\r\n                email: recipientEmail,\r\n                orderNo: selectedReceiptForEmail.receiptOrderNo\r\n            });\r\n\r\n            toast.current?.show({\r\n                severity: 'success',\r\n                summary: '成功',\r\n                detail: '收據郵件發送成功'\r\n            });\r\n\r\n            setShowEmailDialog(false);\r\n            setRecipientEmail('');\r\n            setSelectedReceiptForEmail(null);\r\n\r\n        } catch (error: any) {\r\n            toast.current?.show({\r\n                severity: 'error',\r\n                summary: '錯誤',\r\n                detail: error.response?.data?.error || '郵件發送失敗'\r\n            });\r\n        } finally {\r\n            setIsSendingEmail(false);\r\n        }\r\n    };\r\n\r\n    const paginatorLeft = (\r\n        <Button\r\n            type=\"button\"\r\n            icon=\"pi pi-refresh\"\r\n            text\r\n            onClick={() => Reload()}\r\n        />\r\n    );\r\n    const paginatorRight = <div></div>;\r\n    const optionBodyTemplate = (rowData: any) => {\r\n        return (\r\n            <div className=\"flex gap-2\">\r\n                    <Button \r\n                        label=\"檢視\" \r\n                        type=\"button\" \r\n                        icon=\"pi pi-file-edit\" \r\n                        onClick={() => navigate(ROUTES.RECEIPT_DETAIL, { state: { treatment: rowData } })}\r\n                        size=\"small\" \r\n                        severity=\"info\" \r\n                        style={{ fontSize: '1rem', margin: '3px' }} \r\n                    />\r\n                    <Button\r\n                        label=\"送信\"\r\n                        type=\"button\"\r\n                        icon=\"pi pi-send\"\r\n                        onClick={() => handleSendEmail(rowData)}\r\n                        size=\"small\"\r\n                        severity=\"success\"\r\n                        style={{ fontSize: '1rem', margin: '3px' }}\r\n                        disabled={ rowData.patientEmail ? false : true}\r\n                    />\r\n            </div>\r\n        );\r\n    };\r\n\r\n    const genderBodyTemplate = (rowData: any) => {\r\n        var data = String(rowData.patientGender)\r\n        const gendar = genderdict[data]\r\n            return (\r\n                <div>\r\n                    {gendar}\r\n                </div>\r\n            );\r\n        };\r\n\r\n    const formatDate = (value: string) => {\r\n    if (!value) return '';\r\n    return formatUtcToTaipei(value, \"yyyy/MM/dd HH:mm:ss\");\r\n  };\r\n\r\n    const formatAge = (value: string) => {\r\n        if (!value) return \"\";\r\n        const date = new Date(value);\r\n        const today = new Date();\r\n        let age = today.getFullYear() - date.getFullYear();\r\n\r\n        const hasNotHadBirthdayThisYear =\r\n            today.getMonth() < date.getMonth() ||\r\n            (today.getMonth() === date.getMonth() && today.getDate() < date.getDate());\r\n\r\n        if (hasNotHadBirthdayThisYear) {\r\n            age--;\r\n        }\r\n\r\n        return age;\r\n        \r\n    };\r\n\r\n    if (loading) {\r\n        return <LoadingSpinner message=\"載入收據資料中...\" />;\r\n    }\r\n\r\n    return (\r\n        <div>\r\n            <Toast ref={toast} />\r\n            <ConfirmDialog />\r\n            <Card title=\"收據管理\" className=\"mb-4\">\r\n                <p className=\"text-600 line-height-3 m-0\">\r\n                    收據管理頁面，可以查詢、檢視、製作PDF、發送Email收據資料。\r\n                </p>\r\n            </Card>\r\n\r\n            {/* 搜尋條件 */}\r\n            <Card className=\"mb-4\">\r\n                <div className=\"grid\">\r\n                    <div className=\"col-6 md:col-3\">\r\n                        <InputText\r\n                            id=\"name\"\r\n                            type=\"text\"\r\n                            value={name}\r\n                            onChange={(e) => setName(e.target.value)}\r\n                            className=\"w-full\"\r\n                            placeholder=\"病患姓名\" />\r\n                    </div>\r\n                    <div className=\"col-6 md:col-3\">\r\n                        <InputText\r\n                            id=\"nationalId\"\r\n                            type=\"text\"\r\n                            value={nationalId}\r\n                            onChange={(e) => setNationalId(e.target.value)}\r\n                            className=\"w-full\"\r\n                            placeholder=\"病患身分證\" />\r\n                    </div>\r\n\r\n                    <div className=\"col-6 md:col-3\">\r\n                        <Calendar\r\n                            id=\"starttime\"\r\n                            value={starttime}\r\n                            onChange={(e) => setStarttime(e.value)}\r\n                            placeholder=\"開始時間\"\r\n                            className=\"w-full\"\r\n                            dateFormat=\"yy/mm/dd\"\r\n                            showIcon/>\r\n                    </div>\r\n                    <div className=\"col-6 md:col-3\">\r\n                        <Calendar\r\n                            id=\"endtime\"\r\n                            value={endtime}\r\n                            onChange={(e) => setEndtime(e.value)}\r\n                            placeholder=\"結束時間\"\r\n                            className=\"w-full\"\r\n                            dateFormat=\"yy/mm/dd\"\r\n                            showIcon/>\r\n                    </div>\r\n                    <div className=\"col-12 md:col-4\">\r\n                        <div className=\"flex gap-2\">\r\n                            <Button label=\"查詢\" icon=\"pi pi-search\" onClick={handleSearchClick}/>\r\n                            <Button\r\n                                label=\"批量匯出\"\r\n                                icon=\"pi pi-download\"\r\n                                onClick={handleBatchExport}\r\n                                className=\"p-button-success\"\r\n                                disabled={selectedReceipts.length === 0}\r\n                            />\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n            </Card>\r\n\r\n            <Card>\r\n                <DataTable\r\n                    value={receipts}\r\n                    selection={selectedReceipts}\r\n                    onSelectionChange={(e) => setSelectedReceipts(e.value)}\r\n                    paginator\r\n                    rows={10}\r\n                    rowsPerPageOptions={[10, 20, 30, 40]}\r\n                    emptyMessage=\"沒有找到收據資料\"\r\n                    tableStyle={{ minWidth: '50rem' }}\r\n                    paginatorLeft={paginatorLeft}\r\n                    paginatorRight={paginatorRight}\r\n                >\r\n                    <Column selectionMode=\"multiple\" headerStyle={{ width: '2%' }} />\r\n                    <Column field=\"orderNo\" header=\"案號\" style={{ width: '5%' }} />\r\n                    <Column field=\"receiptOrderNo\" header=\"收據編號\" style={{ width: '5%' }} />\r\n                    <Column field=\"patientName\" header=\"病患姓名\" style={{ width: '5%' }} />\r\n                    <Column field=\"patientGender\" header=\"性別\" style={{ width: '3%' }} body={genderBodyTemplate}/>\r\n                    <Column field=\"patientBirthDate\" header=\"年齡\" style={{ width: '3%' }}  body={(rowData) => formatAge(rowData.patientBirthDate)}/>\r\n                    <Column field=\"receiptCreatedAt\" header=\"新增日期\" style={{ width: '8%' }} body={(rowData) => formatDate(rowData.receiptCreatedAt)} />\r\n                    <Column field=\"receiptUpdatedAt\" header=\"更新日期\" style={{ width: '8%' }} body={(rowData) => formatDate(rowData.receiptUpdatedAt)}/>\r\n                    <Column field=\"receiptOperatorUserName\" header=\"操作人\" style={{ width: '5%' }} />\r\n                    <Column field=\"Option\" header=\"功能\" style={{ width: '12%' }} body={optionBodyTemplate} />\r\n                </DataTable>\r\n            </Card>\r\n\r\n            {/* 批量匯出彈跳視窗 */}\r\n            <Dialog\r\n                header=\"批量匯出收據\"\r\n                visible={showExportDialog}\r\n                style={{ width: '50vw' }}\r\n                onHide={() => setShowExportDialog(false)}\r\n                modal\r\n            >\r\n                <div className=\"mb-4\">\r\n                    <h5>已選擇的收據 ({selectedReceipts.length} 筆)</h5>\r\n                    <DataTable\r\n                        value={selectedReceipts}\r\n                        scrollable\r\n                        scrollHeight=\"300px\"\r\n                        emptyMessage=\"沒有選擇的收據\"\r\n                    >\r\n                        <Column field=\"orderNo\" header=\"案號\" style={{ width: '15%' }} />\r\n                        <Column field=\"receiptorderNo\" header=\"收據編號\" style={{ width: '15%' }} />\r\n                        <Column field=\"patientName\" header=\"病患姓名\" style={{ width: '15%' }} />\r\n                        <Column field=\"receiptCreatedAt\" header=\"新增日期\" style={{ width: '20%' }} body={(rowData) => formatDate(rowData.receiptCreatedAt)} />\r\n                    </DataTable>\r\n                </div>\r\n\r\n                <div className=\"grid align-items-end\">\r\n                    <div className=\"col-12 md:col-4\">\r\n                        <label htmlFor=\"titleCode\" className=\"font-bold block mb-2\">收據類型</label>\r\n                        <Dropdown\r\n                            id=\"titleCode\"\r\n                            value={titleCode}\r\n                            options={titleOptions}\r\n                            onChange={(e) => setTitleCode(e.value)}\r\n                            placeholder=\"選擇收據類型\"\r\n                            className=\"w-full\"\r\n                        />\r\n                    </div>\r\n                    <div className=\"col-12 md:col-4\">\r\n                        <Button\r\n                            label=\"匯出\"\r\n                            icon=\"pi pi-download\"\r\n                            onClick={executeBatchExport}\r\n                            disabled={isExporting}\r\n                            className=\"p-button-success w-full\"\r\n                        />\r\n                    </div>\r\n                    <div className=\"col-12 md:col-4\">\r\n                        <label className=\"font-bold block mb-2\">匯出進度</label>\r\n                        <ProgressBar\r\n                            value={exportProgress}\r\n                            showValue={false}\r\n                            style={{ height: '1.5rem' }}\r\n                        />\r\n                        <small className=\"text-center block mt-1\">{exportProgress}%</small>\r\n                    </div>\r\n                </div>\r\n            </Dialog>\r\n\r\n            {/* 送信對話框 */}\r\n            <Dialog\r\n                header=\"發送收據郵件\"\r\n                visible={showEmailDialog}\r\n                style={{ width: '500px' }}\r\n                onHide={() => setShowEmailDialog(false)}\r\n                modal\r\n            >\r\n                <div className=\"mb-4\">\r\n                    {selectedReceiptForEmail && (\r\n                        <div className=\"p-3 bg-gray-50 border-round mb-3\">\r\n                            <h3 className=\"mt-0 mb-2\">收據資訊</h3>\r\n                            <p className=\"mb-1\"><strong>收據編號:</strong> {selectedReceiptForEmail.receiptOrderNo}</p>\r\n                            <p className=\"mb-1\"><strong>病患姓名:</strong> {selectedReceiptForEmail.patientName}</p>\r\n                            <p className=\"mb-0\"><strong>建立日期:</strong> {formatDate(selectedReceiptForEmail.receiptCreatedAt)}</p>\r\n                        </div>\r\n                    )}\r\n\r\n                    <div className=\"field\">\r\n                        <label htmlFor=\"recipientEmail\" className=\"font-bold block mb-2\">收件人郵箱 *</label>\r\n                        <InputText\r\n                            id=\"recipientEmail\"\r\n                            value={recipientEmail}\r\n                            onChange={(e) => setRecipientEmail(e.target.value)}\r\n                            placeholder=\"請輸入收件人郵箱地址\"\r\n                            className=\"w-full\"\r\n                            disabled={isSendingEmail}\r\n                        />\r\n                    </div>\r\n                </div>\r\n\r\n                <div className=\"flex justify-content-end gap-2\">\r\n                    <Button\r\n                        label=\"取消\"\r\n                        icon=\"pi pi-times\"\r\n                        onClick={() => setShowEmailDialog(false)}\r\n                        className=\"p-button-secondary\"\r\n                        disabled={isSendingEmail}\r\n                    />\r\n                    <Button\r\n                        label={isSendingEmail ? \"發送中...\" : \"發送\"}\r\n                        icon={isSendingEmail ? \"pi pi-spin pi-spinner\" : \"pi pi-send\"}\r\n                        onClick={executeSendEmail}\r\n                        disabled={isSendingEmail || !recipientEmail.trim()}\r\n                        className=\"p-button-primary\"\r\n                    />\r\n                </div>\r\n            </Dialog>\r\n        </div>\r\n    );\r\n};\r\n\r\nexport default ReceiptsPage;"], "mappings": "AAAA,OAASA,iBAAiB,KAAQ,uBAAuB,CACzD,OAASC,MAAM,KAAQ,mBAAmB,CAC1C,OAASC,QAAQ,KAAQ,qBAAqB,CAC9C,OAASC,MAAM,KAAQ,mBAAmB,CAC1C,OAASC,aAAa,KAAQ,0BAA0B,CACxD,OAASC,SAAS,KAAQ,sBAAsB,CAChD,OAASC,MAAM,KAAQ,mBAAmB,CAC1C,OAASC,QAAQ,KAAQ,qBAAqB,CAC9C,OAASC,SAAS,KAAQ,sBAAsB,CAChD,OAASC,WAAW,KAAQ,wBAAwB,CACpD,MAAO,CAAAC,cAAc,KAAM,0BAA0B,CACrD,OAASC,KAAK,KAAQ,kBAAkB,CACxC,MAAO,CAAAC,KAAK,EAAIC,MAAM,CAAEC,QAAQ,CAAEC,SAAS,KAAQ,OAAO,CAC1D,OAASC,WAAW,KAAQ,kBAAkB,CAC9C,OAASC,MAAM,KAAQ,wBAAwB,CAC/C,MAAO,CAAAC,UAAU,KAAM,wBAAwB,CAC/C,MAAO,CAAAC,GAAG,KAAM,oBAAoB,CACpC,MAAO,CAAAC,UAAU,KAAM,wBAAwB,CAC/C,OAASC,IAAI,KAAQ,iBAAiB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAEvC,KAAM,CAAAC,YAAsB,CAAGA,CAAA,GAAM,CACjC,KAAM,CAAAC,QAAQ,CAAGX,WAAW,CAAC,CAAC,CAC9B,KAAM,CAAAY,KAAK,CAAGf,MAAM,CAAQ,IAAI,CAAC,CACjC,KAAM,CAACgB,IAAI,CAAEC,OAAO,CAAC,CAAGhB,QAAQ,CAAC,EAAE,CAAC,CACpC,KAAM,CAACiB,UAAU,CAAEC,aAAa,CAAC,CAAGlB,QAAQ,CAAC,EAAE,CAAC,CAChD,KAAM,CAACmB,SAAS,CAAEC,YAAY,CAAC,CAAGpB,QAAQ,CAA0BqB,SAAS,CAAC,CAC9E,KAAM,CAACC,OAAO,CAAEC,UAAU,CAAC,CAAGvB,QAAQ,CAA0BqB,SAAS,CAAC,CAC1E,KAAM,CAACG,UAAU,CAAEC,aAAa,CAAC,CAAGzB,QAAQ,CAAC,CAAC,CAAC,CAE/C,KAAM,CAAC0B,YAAY,CAAEC,eAAe,CAAC,CAAG3B,QAAQ,CAAC,CAC7Ce,IAAI,CAAE,EAAE,CACRE,UAAU,CAAE,EAAE,CACdE,SAAS,CAAE,IAA+B,CAC1CG,OAAO,CAAE,IAA+B,CACxCE,UAAU,CAAE,CAChB,CAAC,CAAC,CAEF;AACA,KAAM,CAACI,gBAAgB,CAAEC,mBAAmB,CAAC,CAAG7B,QAAQ,CAAQ,EAAE,CAAC,CACnE,KAAM,CAAC8B,gBAAgB,CAAEC,mBAAmB,CAAC,CAAG/B,QAAQ,CAAC,KAAK,CAAC,CAC/D,KAAM,CAACgC,SAAS,CAAEC,YAAY,CAAC,CAAGjC,QAAQ,CAAC,CAAC,CAAC,CAC7C,KAAM,CAACkC,cAAc,CAAEC,iBAAiB,CAAC,CAAGnC,QAAQ,CAAC,CAAC,CAAC,CACvD,KAAM,CAACoC,WAAW,CAAEC,cAAc,CAAC,CAAGrC,QAAQ,CAAC,KAAK,CAAC,CAErD;AACA,KAAM,CAACsC,eAAe,CAAEC,kBAAkB,CAAC,CAAGvC,QAAQ,CAAC,KAAK,CAAC,CAC7D,KAAM,CAACwC,uBAAuB,CAAEC,0BAA0B,CAAC,CAAGzC,QAAQ,CAAM,IAAI,CAAC,CACjF,KAAM,CAAC0C,cAAc,CAAEC,iBAAiB,CAAC,CAAG3C,QAAQ,CAAC,EAAE,CAAC,CACxD,KAAM,CAAC4C,cAAc,CAAEC,iBAAiB,CAAC,CAAG7C,QAAQ,CAAC,KAAK,CAAC,CAE3D,KAAM,CAAE8C,QAAQ,CAAEC,OAAQ,CAAC,CAAG3C,UAAU,CACpCsB,YAAY,CAACX,IAAI,CACjBW,YAAY,CAACT,UAAU,CACvBS,YAAY,CAACP,SAAS,CACtBO,YAAY,CAACJ,OAAO,CACpBE,UACJ,CAAC,CAED;AACA,KAAM,CAAAwB,YAAY,CAAG,CACjB,CAAEC,KAAK,CAAE,IAAI,CAAEC,KAAK,CAAE,CAAE,CAAC,CACzB,CAAED,KAAK,CAAE,QAAQ,CAAEC,KAAK,CAAE,CAAE,CAAC,CAC7B,CAAED,KAAK,CAAE,OAAO,CAAEC,KAAK,CAAE,CAAE,CAAC,CAC5B,CAAED,KAAK,CAAE,OAAO,CAAEC,KAAK,CAAE,CAAE,CAAC,CAC/B,CAED,KAAM,CAAAC,UAAqC,CAAG,CAC1C,GAAG,CAAE,IAAI,CACT,GAAG,CAAE,IAAI,CACT,GAAG,CAAE,IACT,CAAC,CAED;AACAlD,SAAS,CAAC,IAAM,CAEZK,UAAU,CACT8C,KAAK,CAAC,CAAC,CACPC,IAAI,CAAC,IAAM,CACZC,OAAO,CAACC,GAAG,CAAC,cAAc,CAAC,CAC3B;AACA,CAAC,CAAC,CACDC,KAAK,CAACC,GAAG,EAAIH,OAAO,CAACI,KAAK,CAAC,eAAe,CAAED,GAAG,CAAC,CAAC,CAElDnD,UAAU,CAACqD,EAAE,CAAC,gBAAgB,CAAGT,KAAK,EAAK,CAC3Cf,iBAAiB,CAACe,KAAK,CAAC,CACxB,CAAC,CAAC,CAEF5C,UAAU,CAACqD,EAAE,CAAC,gBAAgB,CAAGC,GAAG,EAAK,CACzCzB,iBAAiB,CAACyB,GAAG,CAAC,CACtB,CAAC,CAAC,CAEF,MAAO,IAAM,CACbtD,UAAU,CAACuD,IAAI,CAAC,CAAC,CACjB,CAAC,CACL,CAAC,CAAE,EAAE,CAAC,CAEN,KAAM,CAAAC,iBAAiB,CAAGA,CAAA,GAAM,CAC5BrC,aAAa,CAACD,UAAU,CAAG,CAAC,CAAC,CAC7BG,eAAe,CAAC,CAAEZ,IAAI,CAAEE,UAAU,CAAEE,SAAS,CAAEG,OAAO,CAAEE,UAAU,CAAC,CAAC,CACxE,CAAC,CAED,KAAM,CAAAuC,MAAM,CAAGA,CAAA,GAAM,CACjB;AACAtC,aAAa,CAACuC,IAAI,EAAIA,IAAI,CAAG,CAAC,CAAC,CACnC,CAAC,CAED;AACA,KAAM,CAAAC,iBAAiB,CAAGA,CAAA,GAAM,CAC5B,GAAIrC,gBAAgB,CAACsC,MAAM,GAAK,CAAC,CAAE,KAAAC,cAAA,CAC/B,CAAAA,cAAA,CAAArD,KAAK,CAACsD,OAAO,UAAAD,cAAA,iBAAbA,cAAA,CAAeE,IAAI,CAAC,CAChBC,QAAQ,CAAE,OAAO,CACjBC,OAAO,CAAE,IAAI,CACbC,MAAM,CAAE,WACZ,CAAC,CAAC,CACF,OACJ,CACAzC,mBAAmB,CAAC,IAAI,CAAC,CAC7B,CAAC,CAED;AACA,KAAM,CAAA0C,kBAAkB,CAAG,KAAAA,CAAA,GAAY,CACnC,GAAI,CAACnE,UAAU,CAAE,KAAAoE,eAAA,CACb,CAAAA,eAAA,CAAA5D,KAAK,CAACsD,OAAO,UAAAM,eAAA,iBAAbA,eAAA,CAAeL,IAAI,CAAC,CAChBC,QAAQ,CAAE,OAAO,CACjBC,OAAO,CAAE,IAAI,CACbC,MAAM,CAAE,eACZ,CAAC,CAAC,CACF,OACJ,CAEA,GAAI,CACAnC,cAAc,CAAC,IAAI,CAAC,CACpBF,iBAAiB,CAAC,CAAC,CAAC,CAEpB,KAAM,CAAAwC,YAAY,CAAG/C,gBAAgB,CAACgD,GAAG,CAACC,OAAO,EAAIA,OAAO,CAACC,EAAE,CAAC,CAChE,KAAM,CAAAC,QAAQ,CAAGnD,gBAAgB,CAACgD,GAAG,CAACC,OAAO,EAAIA,OAAO,CAACG,cAAc,CAAC,CAExE,KAAM,CAAAC,WAAW,CAAG,CAChBC,WAAW,CAAEP,YAAY,CACzBQ,OAAO,CAAEJ,QAAQ,CACjBK,SAAS,CAAEpD,SACf,CAAC,CAED,KAAM,CAAAqD,QAAQ,CAAG,KAAM,CAAAhF,GAAG,CAACiF,IAAI,CAAC,oCAAoC,CAAEL,WAAW,CAAE,CAC/EM,MAAM,CAAE,CACJC,YAAY,CAAElF,UAAU,CAACkF,YAC7B,CAAC,CACDC,YAAY,CAAE,MAAM,CACpBC,OAAO,CAAE,CACL,cAAc,CAAE,kBAAmB;AACvC,CACJ,CAAC,CAAC,CAEF;AACA,KAAM,CAAAC,IAAI,CAAG,GAAI,CAAAC,IAAI,CAAC,CAACP,QAAQ,CAACQ,IAAI,CAAC,CAAE,CAAEC,IAAI,CAAE,iBAAkB,CAAC,CAAC,CACnE,KAAM,CAAAC,OAAO,CAAGC,GAAG,CAACC,eAAe,CAACN,IAAI,CAAC,CAEzC;AACAO,MAAM,CAACC,IAAI,CAACJ,OAAO,CAAC,CAEpBhE,mBAAmB,CAAC,KAAK,CAAC,CAC1BF,mBAAmB,CAAC,EAAE,CAAC,CAE3B,CAAE,MAAO6B,KAAU,CAAE,KAAA0C,eAAA,CAAAC,eAAA,CACjBhE,cAAc,CAAC,KAAK,CAAC,CACrBF,iBAAiB,CAAC,CAAC,CAAC,CACpB,CAAAiE,eAAA,CAAAtF,KAAK,CAACsD,OAAO,UAAAgC,eAAA,iBAAbA,eAAA,CAAe/B,IAAI,CAAC,CAChBC,QAAQ,CAAE,OAAO,CACjBC,OAAO,CAAE,IAAI,CACbC,MAAM,CAAE,EAAA6B,eAAA,CAAA3C,KAAK,CAAC2B,QAAQ,UAAAgB,eAAA,iBAAdA,eAAA,CAAgBR,IAAI,GAAI,MACpC,CAAC,CAAC,CACN,CACJ,CAAC,CAED;AACA,KAAM,CAAAS,eAAe,CAAIzB,OAAY,EAAK,CACtCpC,0BAA0B,CAACoC,OAAO,CAAC,CACnClC,iBAAiB,CAACkC,OAAO,CAAC0B,YAAY,CAAC,CACvChE,kBAAkB,CAAC,IAAI,CAAC,CAC5B,CAAC,CAED;AACA,KAAM,CAAAiE,gBAAgB,CAAG,KAAAA,CAAA,GAAY,CACjC,GAAI,CAAC9D,cAAc,CAAC+D,IAAI,CAAC,CAAC,CAAE,KAAAC,eAAA,CACxB,CAAAA,eAAA,CAAA5F,KAAK,CAACsD,OAAO,UAAAsC,eAAA,iBAAbA,eAAA,CAAerC,IAAI,CAAC,CAChBC,QAAQ,CAAE,OAAO,CACjBC,OAAO,CAAE,IAAI,CACbC,MAAM,CAAE,UACZ,CAAC,CAAC,CACF,OACJ,CAEA;AACA,KAAM,CAAAmC,UAAU,CAAG,4BAA4B,CAC/C,GAAI,CAACA,UAAU,CAACC,IAAI,CAAClE,cAAc,CAAC,CAAE,KAAAmE,eAAA,CAClC,CAAAA,eAAA,CAAA/F,KAAK,CAACsD,OAAO,UAAAyC,eAAA,iBAAbA,eAAA,CAAexC,IAAI,CAAC,CAChBC,QAAQ,CAAE,OAAO,CACjBC,OAAO,CAAE,IAAI,CACbC,MAAM,CAAE,YACZ,CAAC,CAAC,CACF,OACJ,CAEA,GAAI,KAAAsC,eAAA,CACAjE,iBAAiB,CAAC,IAAI,CAAC,CAEvB,KAAM,CAAAxC,GAAG,CAACiF,IAAI,CAAC,+BAA+B,CAAE,CAC5CyB,KAAK,CAAErE,cAAc,CACrByC,OAAO,CAAE3C,uBAAuB,CAACwC,cACrC,CAAC,CAAC,CAEF,CAAA8B,eAAA,CAAAhG,KAAK,CAACsD,OAAO,UAAA0C,eAAA,iBAAbA,eAAA,CAAezC,IAAI,CAAC,CAChBC,QAAQ,CAAE,SAAS,CACnBC,OAAO,CAAE,IAAI,CACbC,MAAM,CAAE,UACZ,CAAC,CAAC,CAEFjC,kBAAkB,CAAC,KAAK,CAAC,CACzBI,iBAAiB,CAAC,EAAE,CAAC,CACrBF,0BAA0B,CAAC,IAAI,CAAC,CAEpC,CAAE,MAAOiB,KAAU,CAAE,KAAAsD,eAAA,CAAAC,gBAAA,CAAAC,qBAAA,CACjB,CAAAF,eAAA,CAAAlG,KAAK,CAACsD,OAAO,UAAA4C,eAAA,iBAAbA,eAAA,CAAe3C,IAAI,CAAC,CAChBC,QAAQ,CAAE,OAAO,CACjBC,OAAO,CAAE,IAAI,CACbC,MAAM,CAAE,EAAAyC,gBAAA,CAAAvD,KAAK,CAAC2B,QAAQ,UAAA4B,gBAAA,kBAAAC,qBAAA,CAAdD,gBAAA,CAAgBpB,IAAI,UAAAqB,qBAAA,iBAApBA,qBAAA,CAAsBxD,KAAK,GAAI,QAC3C,CAAC,CAAC,CACN,CAAC,OAAS,CACNb,iBAAiB,CAAC,KAAK,CAAC,CAC5B,CACJ,CAAC,CAED,KAAM,CAAAsE,aAAa,cACf1G,IAAA,CAACtB,MAAM,EACH2G,IAAI,CAAC,QAAQ,CACbsB,IAAI,CAAC,eAAe,CACpBC,IAAI,MACJC,OAAO,CAAEA,CAAA,GAAMvD,MAAM,CAAC,CAAE,CAC3B,CACJ,CACD,KAAM,CAAAwD,cAAc,cAAG9G,IAAA,SAAU,CAAC,CAClC,KAAM,CAAA+G,kBAAkB,CAAIC,OAAY,EAAK,CACzC,mBACI9G,KAAA,QAAK+G,SAAS,CAAC,YAAY,CAAAC,QAAA,eACnBlH,IAAA,CAACtB,MAAM,EACH8D,KAAK,CAAC,cAAI,CACV6C,IAAI,CAAC,QAAQ,CACbsB,IAAI,CAAC,iBAAiB,CACtBE,OAAO,CAAEA,CAAA,GAAMzG,QAAQ,CAACV,MAAM,CAACyH,cAAc,CAAE,CAAEC,KAAK,CAAE,CAAEC,SAAS,CAAEL,OAAQ,CAAE,CAAC,CAAE,CAClFM,IAAI,CAAC,OAAO,CACZzD,QAAQ,CAAC,MAAM,CACf0D,KAAK,CAAE,CAAEC,QAAQ,CAAE,MAAM,CAAEC,MAAM,CAAE,KAAM,CAAE,CAC9C,CAAC,cACFzH,IAAA,CAACtB,MAAM,EACH8D,KAAK,CAAC,cAAI,CACV6C,IAAI,CAAC,QAAQ,CACbsB,IAAI,CAAC,YAAY,CACjBE,OAAO,CAAEA,CAAA,GAAMhB,eAAe,CAACmB,OAAO,CAAE,CACxCM,IAAI,CAAC,OAAO,CACZzD,QAAQ,CAAC,SAAS,CAClB0D,KAAK,CAAE,CAAEC,QAAQ,CAAE,MAAM,CAAEC,MAAM,CAAE,KAAM,CAAE,CAC3CC,QAAQ,CAAGV,OAAO,CAAClB,YAAY,CAAG,KAAK,CAAG,IAAK,CAClD,CAAC,EACL,CAAC,CAEd,CAAC,CAED,KAAM,CAAA6B,kBAAkB,CAAIX,OAAY,EAAK,CACzC,GAAI,CAAA5B,IAAI,CAAGwC,MAAM,CAACZ,OAAO,CAACa,aAAa,CAAC,CACxC,KAAM,CAAAC,MAAM,CAAGpF,UAAU,CAAC0C,IAAI,CAAC,CAC3B,mBACIpF,IAAA,QAAAkH,QAAA,CACKY,MAAM,CACN,CAAC,CAEd,CAAC,CAEL,KAAM,CAAAC,UAAU,CAAItF,KAAa,EAAK,CACtC,GAAI,CAACA,KAAK,CAAE,MAAO,EAAE,CACrB,MAAO,CAAAhE,iBAAiB,CAACgE,KAAK,CAAE,qBAAqB,CAAC,CACxD,CAAC,CAEC,KAAM,CAAAuF,SAAS,CAAIvF,KAAa,EAAK,CACjC,GAAI,CAACA,KAAK,CAAE,MAAO,EAAE,CACrB,KAAM,CAAAwF,IAAI,CAAG,GAAI,CAAAC,IAAI,CAACzF,KAAK,CAAC,CAC5B,KAAM,CAAA0F,KAAK,CAAG,GAAI,CAAAD,IAAI,CAAC,CAAC,CACxB,GAAI,CAAAE,GAAG,CAAGD,KAAK,CAACE,WAAW,CAAC,CAAC,CAAGJ,IAAI,CAACI,WAAW,CAAC,CAAC,CAElD,KAAM,CAAAC,yBAAyB,CAC3BH,KAAK,CAACI,QAAQ,CAAC,CAAC,CAAGN,IAAI,CAACM,QAAQ,CAAC,CAAC,EACjCJ,KAAK,CAACI,QAAQ,CAAC,CAAC,GAAKN,IAAI,CAACM,QAAQ,CAAC,CAAC,EAAIJ,KAAK,CAACK,OAAO,CAAC,CAAC,CAAGP,IAAI,CAACO,OAAO,CAAC,CAAE,CAE9E,GAAIF,yBAAyB,CAAE,CAC3BF,GAAG,EAAE,CACT,CAEA,MAAO,CAAAA,GAAG,CAEd,CAAC,CAED,GAAI9F,OAAO,CAAE,CACT,mBAAOtC,IAAA,CAACb,cAAc,EAACsJ,OAAO,CAAC,+CAAY,CAAE,CAAC,CAClD,CAEA,mBACIvI,KAAA,QAAAgH,QAAA,eACIlH,IAAA,CAACZ,KAAK,EAACsJ,GAAG,CAAErI,KAAM,CAAE,CAAC,cACrBL,IAAA,CAACnB,aAAa,GAAE,CAAC,cACjBmB,IAAA,CAACF,IAAI,EAAC6I,KAAK,CAAC,0BAAM,CAAC1B,SAAS,CAAC,MAAM,CAAAC,QAAA,cAC/BlH,IAAA,MAAGiH,SAAS,CAAC,4BAA4B,CAAAC,QAAA,CAAC,gKAE1C,CAAG,CAAC,CACF,CAAC,cAGPlH,IAAA,CAACF,IAAI,EAACmH,SAAS,CAAC,MAAM,CAAAC,QAAA,cAClBhH,KAAA,QAAK+G,SAAS,CAAC,MAAM,CAAAC,QAAA,eACjBlH,IAAA,QAAKiH,SAAS,CAAC,gBAAgB,CAAAC,QAAA,cAC3BlH,IAAA,CAACf,SAAS,EACNoF,EAAE,CAAC,MAAM,CACTgB,IAAI,CAAC,MAAM,CACX5C,KAAK,CAAEnC,IAAK,CACZsI,QAAQ,CAAGC,CAAC,EAAKtI,OAAO,CAACsI,CAAC,CAACC,MAAM,CAACrG,KAAK,CAAE,CACzCwE,SAAS,CAAC,QAAQ,CAClB8B,WAAW,CAAC,0BAAM,CAAE,CAAC,CACxB,CAAC,cACN/I,IAAA,QAAKiH,SAAS,CAAC,gBAAgB,CAAAC,QAAA,cAC3BlH,IAAA,CAACf,SAAS,EACNoF,EAAE,CAAC,YAAY,CACfgB,IAAI,CAAC,MAAM,CACX5C,KAAK,CAAEjC,UAAW,CAClBoI,QAAQ,CAAGC,CAAC,EAAKpI,aAAa,CAACoI,CAAC,CAACC,MAAM,CAACrG,KAAK,CAAE,CAC/CwE,SAAS,CAAC,QAAQ,CAClB8B,WAAW,CAAC,gCAAO,CAAE,CAAC,CACzB,CAAC,cAEN/I,IAAA,QAAKiH,SAAS,CAAC,gBAAgB,CAAAC,QAAA,cAC3BlH,IAAA,CAACrB,QAAQ,EACL0F,EAAE,CAAC,WAAW,CACd5B,KAAK,CAAE/B,SAAU,CACjBkI,QAAQ,CAAGC,CAAC,EAAKlI,YAAY,CAACkI,CAAC,CAACpG,KAAK,CAAE,CACvCsG,WAAW,CAAC,0BAAM,CAClB9B,SAAS,CAAC,QAAQ,CAClB+B,UAAU,CAAC,UAAU,CACrBC,QAAQ,MAAC,CAAC,CACb,CAAC,cACNjJ,IAAA,QAAKiH,SAAS,CAAC,gBAAgB,CAAAC,QAAA,cAC3BlH,IAAA,CAACrB,QAAQ,EACL0F,EAAE,CAAC,SAAS,CACZ5B,KAAK,CAAE5B,OAAQ,CACf+H,QAAQ,CAAGC,CAAC,EAAK/H,UAAU,CAAC+H,CAAC,CAACpG,KAAK,CAAE,CACrCsG,WAAW,CAAC,0BAAM,CAClB9B,SAAS,CAAC,QAAQ,CAClB+B,UAAU,CAAC,UAAU,CACrBC,QAAQ,MAAC,CAAC,CACb,CAAC,cACNjJ,IAAA,QAAKiH,SAAS,CAAC,iBAAiB,CAAAC,QAAA,cAC5BhH,KAAA,QAAK+G,SAAS,CAAC,YAAY,CAAAC,QAAA,eACvBlH,IAAA,CAACtB,MAAM,EAAC8D,KAAK,CAAC,cAAI,CAACmE,IAAI,CAAC,cAAc,CAACE,OAAO,CAAExD,iBAAkB,CAAC,CAAC,cACpErD,IAAA,CAACtB,MAAM,EACH8D,KAAK,CAAC,0BAAM,CACZmE,IAAI,CAAC,gBAAgB,CACrBE,OAAO,CAAErD,iBAAkB,CAC3ByD,SAAS,CAAC,kBAAkB,CAC5BS,QAAQ,CAAEvG,gBAAgB,CAACsC,MAAM,GAAK,CAAE,CAC3C,CAAC,EACD,CAAC,CACL,CAAC,EACL,CAAC,CACJ,CAAC,cAEPzD,IAAA,CAACF,IAAI,EAAAoH,QAAA,cACDhH,KAAA,CAACpB,SAAS,EACN2D,KAAK,CAAEJ,QAAS,CAChB6G,SAAS,CAAE/H,gBAAiB,CAC5BgI,iBAAiB,CAAGN,CAAC,EAAKzH,mBAAmB,CAACyH,CAAC,CAACpG,KAAK,CAAE,CACvD2G,SAAS,MACTC,IAAI,CAAE,EAAG,CACTC,kBAAkB,CAAE,CAAC,EAAE,CAAE,EAAE,CAAE,EAAE,CAAE,EAAE,CAAE,CACrCC,YAAY,CAAC,kDAAU,CACvBC,UAAU,CAAE,CAAEC,QAAQ,CAAE,OAAQ,CAAE,CAClC/C,aAAa,CAAEA,aAAc,CAC7BI,cAAc,CAAEA,cAAe,CAAAI,QAAA,eAE/BlH,IAAA,CAACpB,MAAM,EAAC8K,aAAa,CAAC,UAAU,CAACC,WAAW,CAAE,CAAEC,KAAK,CAAE,IAAK,CAAE,CAAE,CAAC,cACjE5J,IAAA,CAACpB,MAAM,EAACiL,KAAK,CAAC,SAAS,CAACC,MAAM,CAAC,cAAI,CAACvC,KAAK,CAAE,CAAEqC,KAAK,CAAE,IAAK,CAAE,CAAE,CAAC,cAC9D5J,IAAA,CAACpB,MAAM,EAACiL,KAAK,CAAC,gBAAgB,CAACC,MAAM,CAAC,0BAAM,CAACvC,KAAK,CAAE,CAAEqC,KAAK,CAAE,IAAK,CAAE,CAAE,CAAC,cACvE5J,IAAA,CAACpB,MAAM,EAACiL,KAAK,CAAC,aAAa,CAACC,MAAM,CAAC,0BAAM,CAACvC,KAAK,CAAE,CAAEqC,KAAK,CAAE,IAAK,CAAE,CAAE,CAAC,cACpE5J,IAAA,CAACpB,MAAM,EAACiL,KAAK,CAAC,eAAe,CAACC,MAAM,CAAC,cAAI,CAACvC,KAAK,CAAE,CAAEqC,KAAK,CAAE,IAAK,CAAE,CAACG,IAAI,CAAEpC,kBAAmB,CAAC,CAAC,cAC7F3H,IAAA,CAACpB,MAAM,EAACiL,KAAK,CAAC,kBAAkB,CAACC,MAAM,CAAC,cAAI,CAACvC,KAAK,CAAE,CAAEqC,KAAK,CAAE,IAAK,CAAE,CAAEG,IAAI,CAAG/C,OAAO,EAAKgB,SAAS,CAAChB,OAAO,CAACgD,gBAAgB,CAAE,CAAC,CAAC,cAC/HhK,IAAA,CAACpB,MAAM,EAACiL,KAAK,CAAC,kBAAkB,CAACC,MAAM,CAAC,0BAAM,CAACvC,KAAK,CAAE,CAAEqC,KAAK,CAAE,IAAK,CAAE,CAACG,IAAI,CAAG/C,OAAO,EAAKe,UAAU,CAACf,OAAO,CAACiD,gBAAgB,CAAE,CAAE,CAAC,cAClIjK,IAAA,CAACpB,MAAM,EAACiL,KAAK,CAAC,kBAAkB,CAACC,MAAM,CAAC,0BAAM,CAACvC,KAAK,CAAE,CAAEqC,KAAK,CAAE,IAAK,CAAE,CAACG,IAAI,CAAG/C,OAAO,EAAKe,UAAU,CAACf,OAAO,CAACkD,gBAAgB,CAAE,CAAC,CAAC,cACjIlK,IAAA,CAACpB,MAAM,EAACiL,KAAK,CAAC,yBAAyB,CAACC,MAAM,CAAC,oBAAK,CAACvC,KAAK,CAAE,CAAEqC,KAAK,CAAE,IAAK,CAAE,CAAE,CAAC,cAC/E5J,IAAA,CAACpB,MAAM,EAACiL,KAAK,CAAC,QAAQ,CAACC,MAAM,CAAC,cAAI,CAACvC,KAAK,CAAE,CAAEqC,KAAK,CAAE,KAAM,CAAE,CAACG,IAAI,CAAEhD,kBAAmB,CAAE,CAAC,EACjF,CAAC,CACV,CAAC,cAGP7G,KAAA,CAACnB,MAAM,EACH+K,MAAM,CAAC,sCAAQ,CACfK,OAAO,CAAE9I,gBAAiB,CAC1BkG,KAAK,CAAE,CAAEqC,KAAK,CAAE,MAAO,CAAE,CACzBQ,MAAM,CAAEA,CAAA,GAAM9I,mBAAmB,CAAC,KAAK,CAAE,CACzC+I,KAAK,MAAAnD,QAAA,eAELhH,KAAA,QAAK+G,SAAS,CAAC,MAAM,CAAAC,QAAA,eACjBhH,KAAA,OAAAgH,QAAA,EAAI,wCAAQ,CAAC/F,gBAAgB,CAACsC,MAAM,CAAC,UAAG,EAAI,CAAC,cAC7CvD,KAAA,CAACpB,SAAS,EACN2D,KAAK,CAAEtB,gBAAiB,CACxBmJ,UAAU,MACVC,YAAY,CAAC,OAAO,CACpBhB,YAAY,CAAC,4CAAS,CAAArC,QAAA,eAEtBlH,IAAA,CAACpB,MAAM,EAACiL,KAAK,CAAC,SAAS,CAACC,MAAM,CAAC,cAAI,CAACvC,KAAK,CAAE,CAAEqC,KAAK,CAAE,KAAM,CAAE,CAAE,CAAC,cAC/D5J,IAAA,CAACpB,MAAM,EAACiL,KAAK,CAAC,gBAAgB,CAACC,MAAM,CAAC,0BAAM,CAACvC,KAAK,CAAE,CAAEqC,KAAK,CAAE,KAAM,CAAE,CAAE,CAAC,cACxE5J,IAAA,CAACpB,MAAM,EAACiL,KAAK,CAAC,aAAa,CAACC,MAAM,CAAC,0BAAM,CAACvC,KAAK,CAAE,CAAEqC,KAAK,CAAE,KAAM,CAAE,CAAE,CAAC,cACrE5J,IAAA,CAACpB,MAAM,EAACiL,KAAK,CAAC,kBAAkB,CAACC,MAAM,CAAC,0BAAM,CAACvC,KAAK,CAAE,CAAEqC,KAAK,CAAE,KAAM,CAAE,CAACG,IAAI,CAAG/C,OAAO,EAAKe,UAAU,CAACf,OAAO,CAACiD,gBAAgB,CAAE,CAAE,CAAC,EAC5H,CAAC,EACX,CAAC,cAEN/J,KAAA,QAAK+G,SAAS,CAAC,sBAAsB,CAAAC,QAAA,eACjChH,KAAA,QAAK+G,SAAS,CAAC,iBAAiB,CAAAC,QAAA,eAC5BlH,IAAA,UAAOwK,OAAO,CAAC,WAAW,CAACvD,SAAS,CAAC,sBAAsB,CAAAC,QAAA,CAAC,0BAAI,CAAO,CAAC,cACxElH,IAAA,CAAChB,QAAQ,EACLqF,EAAE,CAAC,WAAW,CACd5B,KAAK,CAAElB,SAAU,CACjBkJ,OAAO,CAAElI,YAAa,CACtBqG,QAAQ,CAAGC,CAAC,EAAKrH,YAAY,CAACqH,CAAC,CAACpG,KAAK,CAAE,CACvCsG,WAAW,CAAC,sCAAQ,CACpB9B,SAAS,CAAC,QAAQ,CACrB,CAAC,EACD,CAAC,cACNjH,IAAA,QAAKiH,SAAS,CAAC,iBAAiB,CAAAC,QAAA,cAC5BlH,IAAA,CAACtB,MAAM,EACH8D,KAAK,CAAC,cAAI,CACVmE,IAAI,CAAC,gBAAgB,CACrBE,OAAO,CAAE7C,kBAAmB,CAC5B0D,QAAQ,CAAE/F,WAAY,CACtBsF,SAAS,CAAC,yBAAyB,CACtC,CAAC,CACD,CAAC,cACN/G,KAAA,QAAK+G,SAAS,CAAC,iBAAiB,CAAAC,QAAA,eAC5BlH,IAAA,UAAOiH,SAAS,CAAC,sBAAsB,CAAAC,QAAA,CAAC,0BAAI,CAAO,CAAC,cACpDlH,IAAA,CAACd,WAAW,EACRuD,KAAK,CAAEhB,cAAe,CACtBiJ,SAAS,CAAE,KAAM,CACjBnD,KAAK,CAAE,CAAEoD,MAAM,CAAE,QAAS,CAAE,CAC/B,CAAC,cACFzK,KAAA,UAAO+G,SAAS,CAAC,wBAAwB,CAAAC,QAAA,EAAEzF,cAAc,CAAC,GAAC,EAAO,CAAC,EAClE,CAAC,EACL,CAAC,EACF,CAAC,cAGTvB,KAAA,CAACnB,MAAM,EACH+K,MAAM,CAAC,sCAAQ,CACfK,OAAO,CAAEtI,eAAgB,CACzB0F,KAAK,CAAE,CAAEqC,KAAK,CAAE,OAAQ,CAAE,CAC1BQ,MAAM,CAAEA,CAAA,GAAMtI,kBAAkB,CAAC,KAAK,CAAE,CACxCuI,KAAK,MAAAnD,QAAA,eAELhH,KAAA,QAAK+G,SAAS,CAAC,MAAM,CAAAC,QAAA,EAChBnF,uBAAuB,eACpB7B,KAAA,QAAK+G,SAAS,CAAC,kCAAkC,CAAAC,QAAA,eAC7ClH,IAAA,OAAIiH,SAAS,CAAC,WAAW,CAAAC,QAAA,CAAC,0BAAI,CAAI,CAAC,cACnChH,KAAA,MAAG+G,SAAS,CAAC,MAAM,CAAAC,QAAA,eAAClH,IAAA,WAAAkH,QAAA,CAAQ,2BAAK,CAAQ,CAAC,IAAC,CAACnF,uBAAuB,CAACwC,cAAc,EAAI,CAAC,cACvFrE,KAAA,MAAG+G,SAAS,CAAC,MAAM,CAAAC,QAAA,eAAClH,IAAA,WAAAkH,QAAA,CAAQ,2BAAK,CAAQ,CAAC,IAAC,CAACnF,uBAAuB,CAAC6I,WAAW,EAAI,CAAC,cACpF1K,KAAA,MAAG+G,SAAS,CAAC,MAAM,CAAAC,QAAA,eAAClH,IAAA,WAAAkH,QAAA,CAAQ,2BAAK,CAAQ,CAAC,IAAC,CAACa,UAAU,CAAChG,uBAAuB,CAACkI,gBAAgB,CAAC,EAAI,CAAC,EACpG,CACR,cAED/J,KAAA,QAAK+G,SAAS,CAAC,OAAO,CAAAC,QAAA,eAClBlH,IAAA,UAAOwK,OAAO,CAAC,gBAAgB,CAACvD,SAAS,CAAC,sBAAsB,CAAAC,QAAA,CAAC,kCAAO,CAAO,CAAC,cAChFlH,IAAA,CAACf,SAAS,EACNoF,EAAE,CAAC,gBAAgB,CACnB5B,KAAK,CAAER,cAAe,CACtB2G,QAAQ,CAAGC,CAAC,EAAK3G,iBAAiB,CAAC2G,CAAC,CAACC,MAAM,CAACrG,KAAK,CAAE,CACnDsG,WAAW,CAAC,8DAAY,CACxB9B,SAAS,CAAC,QAAQ,CAClBS,QAAQ,CAAEvF,cAAe,CAC5B,CAAC,EACD,CAAC,EACL,CAAC,cAENjC,KAAA,QAAK+G,SAAS,CAAC,gCAAgC,CAAAC,QAAA,eAC3ClH,IAAA,CAACtB,MAAM,EACH8D,KAAK,CAAC,cAAI,CACVmE,IAAI,CAAC,aAAa,CAClBE,OAAO,CAAEA,CAAA,GAAM/E,kBAAkB,CAAC,KAAK,CAAE,CACzCmF,SAAS,CAAC,oBAAoB,CAC9BS,QAAQ,CAAEvF,cAAe,CAC5B,CAAC,cACFnC,IAAA,CAACtB,MAAM,EACH8D,KAAK,CAAEL,cAAc,CAAG,QAAQ,CAAG,IAAK,CACxCwE,IAAI,CAAExE,cAAc,CAAG,uBAAuB,CAAG,YAAa,CAC9D0E,OAAO,CAAEd,gBAAiB,CAC1B2B,QAAQ,CAAEvF,cAAc,EAAI,CAACF,cAAc,CAAC+D,IAAI,CAAC,CAAE,CACnDiB,SAAS,CAAC,kBAAkB,CAC/B,CAAC,EACD,CAAC,EACF,CAAC,EACR,CAAC,CAEd,CAAC,CAED,cAAe,CAAA9G,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
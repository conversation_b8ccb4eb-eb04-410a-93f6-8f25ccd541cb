{"ast": null, "code": "var _s = $RefreshSig$();\nimport { useState, useEffect, useCallback } from 'react';\nimport api from '../services/api';\nimport { log } from '../utils/logger';\n\n// Type Definitions\n\nexport const usePermissions = () => {\n  _s();\n  const [permissions, setPermissions] = useState({});\n  const [loading, setLoading] = useState(true);\n  const fetchPermissions = useCallback(async () => {\n    setLoading(true);\n    try {\n      const response = await api.get('/api/RoleMenu/GetUserPermissions');\n      setPermissions(response.data || {});\n      log.info('Permissions loaded', response.data);\n    } catch (error) {\n      log.error(\"Failed to fetch permissions\", error);\n      setPermissions({});\n    } finally {\n      setLoading(false);\n    }\n  }, []);\n  useEffect(() => {\n    fetchPermissions();\n  }, [fetchPermissions]);\n  const hasPermission = useCallback((menuPath, action) => {\n    if (loading) {\n      return false; // Don't allow access while loading\n    }\n    const menuPermissions = permissions[menuPath];\n    if (!menuPermissions) {\n      return false; // No permissions defined for this menu path\n    }\n    return menuPermissions[action] === true;\n  }, [permissions, loading]);\n  return {\n    permissions,\n    hasPermission,\n    loading,\n    refreshPermissions: fetchPermissions\n  };\n};\n_s(usePermissions, \"EtCB2WX8HJ/xtHjEI/WHR50zztc=\");", "map": {"version": 3, "names": ["useState", "useEffect", "useCallback", "api", "log", "usePermissions", "_s", "permissions", "setPermissions", "loading", "setLoading", "fetchPermissions", "response", "get", "data", "info", "error", "hasPermission", "menuPath", "action", "menuPermissions", "refreshPermissions"], "sources": ["C:/Users/<USER>/Desktop/demo-git/demo-react/my-fullstack-app/frontend/src/hooks/usePermissions.ts"], "sourcesContent": ["import { useState, useEffect, useCallback } from 'react';\nimport api from '../services/api';\nimport { log } from '../utils/logger';\n\n// Type Definitions\ninterface ActionPermissions {\n    canRead: boolean;\n    canWrite: boolean;\n    canDelete: boolean;\n    canExport: boolean;\n    canImport: boolean;\n}\n\ntype PermissionsMap = Record<string, Partial<ActionPermissions>>;\n\nexport const usePermissions = () => {\n    const [permissions, setPermissions] = useState<PermissionsMap>({});\n    const [loading, setLoading] = useState(true);\n\n    const fetchPermissions = useCallback(async () => {\n        setLoading(true);\n        try {\n            const response = await api.get<PermissionsMap>('/api/RoleMenu/GetUserPermissions');\n            setPermissions(response.data || {});\n            log.info('Permissions loaded', response.data);\n        } catch (error: any) {\n            log.error(\"Failed to fetch permissions\", error);\n            setPermissions({});\n        } finally {\n            setLoading(false);\n        }\n    }, []);\n\n    useEffect(() => {\n        fetchPermissions();\n    }, [fetchPermissions]);\n\n    const hasPermission = useCallback(\n        (menuPath: string, action: keyof ActionPermissions): boolean => {\n            if (loading) {\n                return false; // Don't allow access while loading\n            }\n            \n            const menuPermissions = permissions[menuPath];\n            \n            if (!menuPermissions) {\n                return false; // No permissions defined for this menu path\n            }\n            \n            return menuPermissions[action] === true;\n        },\n        [permissions, loading]\n    );\n\n    return { permissions, hasPermission, loading, refreshPermissions: fetchPermissions };\n};"], "mappings": ";AAAA,SAASA,QAAQ,EAAEC,SAAS,EAAEC,WAAW,QAAQ,OAAO;AACxD,OAAOC,GAAG,MAAM,iBAAiB;AACjC,SAASC,GAAG,QAAQ,iBAAiB;;AAErC;;AAWA,OAAO,MAAMC,cAAc,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAChC,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGR,QAAQ,CAAiB,CAAC,CAAC,CAAC;EAClE,MAAM,CAACS,OAAO,EAAEC,UAAU,CAAC,GAAGV,QAAQ,CAAC,IAAI,CAAC;EAE5C,MAAMW,gBAAgB,GAAGT,WAAW,CAAC,YAAY;IAC7CQ,UAAU,CAAC,IAAI,CAAC;IAChB,IAAI;MACA,MAAME,QAAQ,GAAG,MAAMT,GAAG,CAACU,GAAG,CAAiB,kCAAkC,CAAC;MAClFL,cAAc,CAACI,QAAQ,CAACE,IAAI,IAAI,CAAC,CAAC,CAAC;MACnCV,GAAG,CAACW,IAAI,CAAC,oBAAoB,EAAEH,QAAQ,CAACE,IAAI,CAAC;IACjD,CAAC,CAAC,OAAOE,KAAU,EAAE;MACjBZ,GAAG,CAACY,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;MAC/CR,cAAc,CAAC,CAAC,CAAC,CAAC;IACtB,CAAC,SAAS;MACNE,UAAU,CAAC,KAAK,CAAC;IACrB;EACJ,CAAC,EAAE,EAAE,CAAC;EAENT,SAAS,CAAC,MAAM;IACZU,gBAAgB,CAAC,CAAC;EACtB,CAAC,EAAE,CAACA,gBAAgB,CAAC,CAAC;EAEtB,MAAMM,aAAa,GAAGf,WAAW,CAC7B,CAACgB,QAAgB,EAAEC,MAA+B,KAAc;IAC5D,IAAIV,OAAO,EAAE;MACT,OAAO,KAAK,CAAC,CAAC;IAClB;IAEA,MAAMW,eAAe,GAAGb,WAAW,CAACW,QAAQ,CAAC;IAE7C,IAAI,CAACE,eAAe,EAAE;MAClB,OAAO,KAAK,CAAC,CAAC;IAClB;IAEA,OAAOA,eAAe,CAACD,MAAM,CAAC,KAAK,IAAI;EAC3C,CAAC,EACD,CAACZ,WAAW,EAAEE,OAAO,CACzB,CAAC;EAED,OAAO;IAAEF,WAAW;IAAEU,aAAa;IAAER,OAAO;IAAEY,kBAAkB,EAAEV;EAAiB,CAAC;AACxF,CAAC;AAACL,EAAA,CAxCWD,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
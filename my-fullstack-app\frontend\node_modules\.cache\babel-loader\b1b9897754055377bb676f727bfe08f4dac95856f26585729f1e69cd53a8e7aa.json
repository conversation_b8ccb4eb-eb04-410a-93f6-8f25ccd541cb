{"ast": null, "code": "import _objectSpread from\"C:/Users/<USER>/Desktop/demo-git/demo-react/my-fullstack-app/frontend/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";var _Logger;/**\r\n * 統一日誌管理系統\r\n */export let LogLevel=/*#__PURE__*/function(LogLevel){LogLevel[LogLevel[\"DEBUG\"]=0]=\"DEBUG\";LogLevel[LogLevel[\"INFO\"]=1]=\"INFO\";LogLevel[LogLevel[\"WARN\"]=2]=\"WARN\";LogLevel[LogLevel[\"ERROR\"]=3]=\"ERROR\";LogLevel[LogLevel[\"NONE\"]=4]=\"NONE\";return LogLevel;}({});class Logger{constructor(){this.logLevel=void 0;this.isDevelopment=void 0;this.logLevel=process.env.NODE_ENV==='production'?LogLevel.WARN:LogLevel.DEBUG;this.isDevelopment=process.env.NODE_ENV==='development';}static getInstance(){if(!Logger.instance){Logger.instance=new Logger();}return Logger.instance;}shouldLog(level){return level>=this.logLevel;}formatMessage(level,message,source){const timestamp=new Date().toISOString();const levelName=LogLevel[level];const sourcePrefix=source?\"[\".concat(source,\"]\"):'';return\"\".concat(timestamp,\" [\").concat(levelName,\"]\").concat(sourcePrefix,\" \").concat(message);}log(level,message,data,source){if(!this.shouldLog(level))return;const formattedMessage=this.formatMessage(level,message,source);const logEntry=_objectSpread({level,message,data,timestamp:new Date().toISOString()},source&&{source});// 在開發環境輸出到控制台\nif(this.isDevelopment){switch(level){case LogLevel.DEBUG:console.log(formattedMessage,data||'');break;case LogLevel.INFO:console.info(formattedMessage,data||'');break;case LogLevel.WARN:console.warn(formattedMessage,data||'');break;case LogLevel.ERROR:console.error(formattedMessage,data||'');break;}}// 在生產環境可以發送到監控服務\nif(level>=LogLevel.ERROR){this.sendToMonitoring(logEntry);}}sendToMonitoring(_logEntry){// TODO: 實現發送到監控服務的邏輯\n// 例如: Sentry, LogRocket, 或自定義監控服務\n}// 公共方法\ndebug(message,data,source){this.log(LogLevel.DEBUG,message,data,source);}info(message,data,source){this.log(LogLevel.INFO,message,data,source);}warn(message,data,source){this.log(LogLevel.WARN,message,data,source);}error(message,data,source){this.log(LogLevel.ERROR,message,data,source);}// 特定領域的日誌方法\nauth(message,data){this.log(LogLevel.INFO,message,data,'AUTH');}api(message,data){this.log(LogLevel.INFO,message,data,'API');}route(message,data){this.log(LogLevel.INFO,message,data,'ROUTE');}ui(message,data){this.log(LogLevel.DEBUG,message,data,'UI');}// 設置日誌級別\nsetLogLevel(level){this.logLevel=level;}// 獲取當前日誌級別\ngetLogLevel(){return this.logLevel;}}// 導出單例實例\n_Logger=Logger;Logger.instance=void 0;export const log=Logger.getInstance();// 導出便捷方法\nexport const logger={debug:(message,data,source)=>log.debug(message,data,source),info:(message,data,source)=>log.info(message,data,source),warn:(message,data,source)=>log.warn(message,data,source),error:(message,data,source)=>log.error(message,data,source),auth:(message,data)=>log.auth(message,data),api:(message,data)=>log.api(message,data),route:(message,data)=>log.route(message,data),ui:(message,data)=>log.ui(message,data)};export default log;", "map": {"version": 3, "names": ["LogLevel", "<PERSON><PERSON>", "constructor", "logLevel", "isDevelopment", "process", "env", "NODE_ENV", "WARN", "DEBUG", "getInstance", "instance", "shouldLog", "level", "formatMessage", "message", "source", "timestamp", "Date", "toISOString", "levelName", "sourcePrefix", "concat", "log", "data", "formattedMessage", "logEntry", "_objectSpread", "console", "INFO", "info", "warn", "ERROR", "error", "sendToMonitoring", "_logEntry", "debug", "auth", "api", "route", "ui", "setLogLevel", "getLogLevel", "_<PERSON>gger", "logger"], "sources": ["C:/Users/<USER>/Desktop/demo-git/demo-react/my-fullstack-app/frontend/src/utils/logger.ts"], "sourcesContent": ["/**\r\n * 統一日誌管理系統\r\n */\r\n\r\nexport enum LogLevel {\r\n  DEBUG = 0,\r\n  INFO = 1,\r\n  WARN = 2,\r\n  ERROR = 3,\r\n  NONE = 4\r\n}\r\n\r\ninterface LogEntry {\r\n  level: LogLevel;\r\n  message: string;\r\n  data?: any;\r\n  timestamp: string;\r\n  source?: string;\r\n}\r\n\r\nclass Logger {\r\n  private static instance: Logger;\r\n  private logLevel: LogLevel;\r\n  private isDevelopment: boolean;\r\n\r\n  private constructor() {\r\n    this.logLevel = process.env.NODE_ENV === 'production' ? LogLevel.WARN : LogLevel.DEBUG;\r\n    this.isDevelopment = process.env.NODE_ENV === 'development';\r\n  }\r\n\r\n  static getInstance(): Logger {\r\n    if (!Logger.instance) {\r\n      Logger.instance = new Logger();\r\n    }\r\n    return Logger.instance;\r\n  }\r\n\r\n  private shouldLog(level: LogLevel): boolean {\r\n    return level >= this.logLevel;\r\n  }\r\n\r\n  private formatMessage(level: LogLevel, message: string, source?: string): string {\r\n    const timestamp = new Date().toISOString();\r\n    const levelName = LogLevel[level];\r\n    const sourcePrefix = source ? `[${source}]` : '';\r\n    return `${timestamp} [${levelName}]${sourcePrefix} ${message}`;\r\n  }\r\n\r\n  private log(level: LogLevel, message: string, data?: any, source?: string): void {\r\n    if (!this.shouldLog(level)) return;\r\n\r\n    const formattedMessage = this.formatMessage(level, message, source);\r\n    const logEntry: LogEntry = {\r\n      level,\r\n      message,\r\n      data,\r\n      timestamp: new Date().toISOString(),\r\n      ...(source && { source })\r\n    };\r\n\r\n    // 在開發環境輸出到控制台\r\n    if (this.isDevelopment) {\r\n      switch (level) {\r\n        case LogLevel.DEBUG:\r\n          console.log(formattedMessage, data || '');\r\n          break;\r\n        case LogLevel.INFO:\r\n          console.info(formattedMessage, data || '');\r\n          break;\r\n        case LogLevel.WARN:\r\n          console.warn(formattedMessage, data || '');\r\n          break;\r\n        case LogLevel.ERROR:\r\n          console.error(formattedMessage, data || '');\r\n          break;\r\n      }\r\n    }\r\n\r\n    // 在生產環境可以發送到監控服務\r\n    if (level >= LogLevel.ERROR) {\r\n      this.sendToMonitoring(logEntry);\r\n    }\r\n  }\r\n\r\n  private sendToMonitoring(_logEntry: LogEntry): void {\r\n    // TODO: 實現發送到監控服務的邏輯\r\n    // 例如: Sentry, LogRocket, 或自定義監控服務\r\n  }\r\n\r\n  // 公共方法\r\n  debug(message: string, data?: any, source?: string): void {\r\n    this.log(LogLevel.DEBUG, message, data, source);\r\n  }\r\n\r\n  info(message: string, data?: any, source?: string): void {\r\n    this.log(LogLevel.INFO, message, data, source);\r\n  }\r\n\r\n  warn(message: string, data?: any, source?: string): void {\r\n    this.log(LogLevel.WARN, message, data, source);\r\n  }\r\n\r\n  error(message: string, data?: any, source?: string): void {\r\n    this.log(LogLevel.ERROR, message, data, source);\r\n  }\r\n\r\n  // 特定領域的日誌方法\r\n  auth(message: string, data?: any): void {\r\n    this.log(LogLevel.INFO, message, data, 'AUTH');\r\n  }\r\n\r\n  api(message: string, data?: any): void {\r\n    this.log(LogLevel.INFO, message, data, 'API');\r\n  }\r\n\r\n  route(message: string, data?: any): void {\r\n    this.log(LogLevel.INFO, message, data, 'ROUTE');\r\n  }\r\n\r\n  ui(message: string, data?: any): void {\r\n    this.log(LogLevel.DEBUG, message, data, 'UI');\r\n  }\r\n\r\n  // 設置日誌級別\r\n  setLogLevel(level: LogLevel): void {\r\n    this.logLevel = level;\r\n  }\r\n\r\n  // 獲取當前日誌級別\r\n  getLogLevel(): LogLevel {\r\n    return this.logLevel;\r\n  }\r\n}\r\n\r\n// 導出單例實例\r\nexport const log = Logger.getInstance();\r\n\r\n// 導出便捷方法\r\nexport const logger = {\r\n  debug: (message: string, data?: any, source?: string) => log.debug(message, data, source),\r\n  info: (message: string, data?: any, source?: string) => log.info(message, data, source),\r\n  warn: (message: string, data?: any, source?: string) => log.warn(message, data, source),\r\n  error: (message: string, data?: any, source?: string) => log.error(message, data, source),\r\n  auth: (message: string, data?: any) => log.auth(message, data),\r\n  api: (message: string, data?: any) => log.api(message, data),\r\n  route: (message: string, data?: any) => log.route(message, data),\r\n  ui: (message: string, data?: any) => log.ui(message, data),\r\n};\r\n\r\nexport default log;\r\n"], "mappings": "oKAAA;AACA;AACA,GAEA,UAAY,CAAAA,QAAQ,uBAARA,QAAQ,EAARA,QAAQ,CAARA,QAAQ,qBAARA,QAAQ,CAARA,QAAQ,mBAARA,QAAQ,CAARA,QAAQ,mBAARA,QAAQ,CAARA,QAAQ,qBAARA,QAAQ,CAARA,QAAQ,yBAAR,CAAAA,QAAQ,OAgBpB,KAAM,CAAAC,MAAO,CAKHC,WAAWA,CAAA,CAAG,MAHdC,QAAQ,aACRC,aAAa,QAGnB,IAAI,CAACD,QAAQ,CAAGE,OAAO,CAACC,GAAG,CAACC,QAAQ,GAAK,YAAY,CAAGP,QAAQ,CAACQ,IAAI,CAAGR,QAAQ,CAACS,KAAK,CACtF,IAAI,CAACL,aAAa,CAAGC,OAAO,CAACC,GAAG,CAACC,QAAQ,GAAK,aAAa,CAC7D,CAEA,MAAO,CAAAG,WAAWA,CAAA,CAAW,CAC3B,GAAI,CAACT,MAAM,CAACU,QAAQ,CAAE,CACpBV,MAAM,CAACU,QAAQ,CAAG,GAAI,CAAAV,MAAM,CAAC,CAAC,CAChC,CACA,MAAO,CAAAA,MAAM,CAACU,QAAQ,CACxB,CAEQC,SAASA,CAACC,KAAe,CAAW,CAC1C,MAAO,CAAAA,KAAK,EAAI,IAAI,CAACV,QAAQ,CAC/B,CAEQW,aAAaA,CAACD,KAAe,CAAEE,OAAe,CAAEC,MAAe,CAAU,CAC/E,KAAM,CAAAC,SAAS,CAAG,GAAI,CAAAC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAC1C,KAAM,CAAAC,SAAS,CAAGpB,QAAQ,CAACa,KAAK,CAAC,CACjC,KAAM,CAAAQ,YAAY,CAAGL,MAAM,KAAAM,MAAA,CAAON,MAAM,MAAM,EAAE,CAChD,SAAAM,MAAA,CAAUL,SAAS,OAAAK,MAAA,CAAKF,SAAS,MAAAE,MAAA,CAAID,YAAY,MAAAC,MAAA,CAAIP,OAAO,EAC9D,CAEQQ,GAAGA,CAACV,KAAe,CAAEE,OAAe,CAAES,IAAU,CAAER,MAAe,CAAQ,CAC/E,GAAI,CAAC,IAAI,CAACJ,SAAS,CAACC,KAAK,CAAC,CAAE,OAE5B,KAAM,CAAAY,gBAAgB,CAAG,IAAI,CAACX,aAAa,CAACD,KAAK,CAAEE,OAAO,CAAEC,MAAM,CAAC,CACnE,KAAM,CAAAU,QAAkB,CAAAC,aAAA,EACtBd,KAAK,CACLE,OAAO,CACPS,IAAI,CACJP,SAAS,CAAE,GAAI,CAAAC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,EAC/BH,MAAM,EAAI,CAAEA,MAAO,CAAC,CACzB,CAED;AACA,GAAI,IAAI,CAACZ,aAAa,CAAE,CACtB,OAAQS,KAAK,EACX,IAAK,CAAAb,QAAQ,CAACS,KAAK,CACjBmB,OAAO,CAACL,GAAG,CAACE,gBAAgB,CAAED,IAAI,EAAI,EAAE,CAAC,CACzC,MACF,IAAK,CAAAxB,QAAQ,CAAC6B,IAAI,CAChBD,OAAO,CAACE,IAAI,CAACL,gBAAgB,CAAED,IAAI,EAAI,EAAE,CAAC,CAC1C,MACF,IAAK,CAAAxB,QAAQ,CAACQ,IAAI,CAChBoB,OAAO,CAACG,IAAI,CAACN,gBAAgB,CAAED,IAAI,EAAI,EAAE,CAAC,CAC1C,MACF,IAAK,CAAAxB,QAAQ,CAACgC,KAAK,CACjBJ,OAAO,CAACK,KAAK,CAACR,gBAAgB,CAAED,IAAI,EAAI,EAAE,CAAC,CAC3C,MACJ,CACF,CAEA;AACA,GAAIX,KAAK,EAAIb,QAAQ,CAACgC,KAAK,CAAE,CAC3B,IAAI,CAACE,gBAAgB,CAACR,QAAQ,CAAC,CACjC,CACF,CAEQQ,gBAAgBA,CAACC,SAAmB,CAAQ,CAClD;AACA;AAAA,CAGF;AACAC,KAAKA,CAACrB,OAAe,CAAES,IAAU,CAAER,MAAe,CAAQ,CACxD,IAAI,CAACO,GAAG,CAACvB,QAAQ,CAACS,KAAK,CAAEM,OAAO,CAAES,IAAI,CAAER,MAAM,CAAC,CACjD,CAEAc,IAAIA,CAACf,OAAe,CAAES,IAAU,CAAER,MAAe,CAAQ,CACvD,IAAI,CAACO,GAAG,CAACvB,QAAQ,CAAC6B,IAAI,CAAEd,OAAO,CAAES,IAAI,CAAER,MAAM,CAAC,CAChD,CAEAe,IAAIA,CAAChB,OAAe,CAAES,IAAU,CAAER,MAAe,CAAQ,CACvD,IAAI,CAACO,GAAG,CAACvB,QAAQ,CAACQ,IAAI,CAAEO,OAAO,CAAES,IAAI,CAAER,MAAM,CAAC,CAChD,CAEAiB,KAAKA,CAAClB,OAAe,CAAES,IAAU,CAAER,MAAe,CAAQ,CACxD,IAAI,CAACO,GAAG,CAACvB,QAAQ,CAACgC,KAAK,CAAEjB,OAAO,CAAES,IAAI,CAAER,MAAM,CAAC,CACjD,CAEA;AACAqB,IAAIA,CAACtB,OAAe,CAAES,IAAU,CAAQ,CACtC,IAAI,CAACD,GAAG,CAACvB,QAAQ,CAAC6B,IAAI,CAAEd,OAAO,CAAES,IAAI,CAAE,MAAM,CAAC,CAChD,CAEAc,GAAGA,CAACvB,OAAe,CAAES,IAAU,CAAQ,CACrC,IAAI,CAACD,GAAG,CAACvB,QAAQ,CAAC6B,IAAI,CAAEd,OAAO,CAAES,IAAI,CAAE,KAAK,CAAC,CAC/C,CAEAe,KAAKA,CAACxB,OAAe,CAAES,IAAU,CAAQ,CACvC,IAAI,CAACD,GAAG,CAACvB,QAAQ,CAAC6B,IAAI,CAAEd,OAAO,CAAES,IAAI,CAAE,OAAO,CAAC,CACjD,CAEAgB,EAAEA,CAACzB,OAAe,CAAES,IAAU,CAAQ,CACpC,IAAI,CAACD,GAAG,CAACvB,QAAQ,CAACS,KAAK,CAAEM,OAAO,CAAES,IAAI,CAAE,IAAI,CAAC,CAC/C,CAEA;AACAiB,WAAWA,CAAC5B,KAAe,CAAQ,CACjC,IAAI,CAACV,QAAQ,CAAGU,KAAK,CACvB,CAEA;AACA6B,WAAWA,CAAA,CAAa,CACtB,MAAO,KAAI,CAACvC,QAAQ,CACtB,CACF,CAEA;AAAAwC,OAAA,CAlHM1C,MAAM,CAANA,MAAM,CACKU,QAAQ,QAkHzB,MAAO,MAAM,CAAAY,GAAG,CAAGtB,MAAM,CAACS,WAAW,CAAC,CAAC,CAEvC;AACA,MAAO,MAAM,CAAAkC,MAAM,CAAG,CACpBR,KAAK,CAAEA,CAACrB,OAAe,CAAES,IAAU,CAAER,MAAe,GAAKO,GAAG,CAACa,KAAK,CAACrB,OAAO,CAAES,IAAI,CAAER,MAAM,CAAC,CACzFc,IAAI,CAAEA,CAACf,OAAe,CAAES,IAAU,CAAER,MAAe,GAAKO,GAAG,CAACO,IAAI,CAACf,OAAO,CAAES,IAAI,CAAER,MAAM,CAAC,CACvFe,IAAI,CAAEA,CAAChB,OAAe,CAAES,IAAU,CAAER,MAAe,GAAKO,GAAG,CAACQ,IAAI,CAAChB,OAAO,CAAES,IAAI,CAAER,MAAM,CAAC,CACvFiB,KAAK,CAAEA,CAAClB,OAAe,CAAES,IAAU,CAAER,MAAe,GAAKO,GAAG,CAACU,KAAK,CAAClB,OAAO,CAAES,IAAI,CAAER,MAAM,CAAC,CACzFqB,IAAI,CAAEA,CAACtB,OAAe,CAAES,IAAU,GAAKD,GAAG,CAACc,IAAI,CAACtB,OAAO,CAAES,IAAI,CAAC,CAC9Dc,GAAG,CAAEA,CAACvB,OAAe,CAAES,IAAU,GAAKD,GAAG,CAACe,GAAG,CAACvB,OAAO,CAAES,IAAI,CAAC,CAC5De,KAAK,CAAEA,CAACxB,OAAe,CAAES,IAAU,GAAKD,GAAG,CAACgB,KAAK,CAACxB,OAAO,CAAES,IAAI,CAAC,CAChEgB,EAAE,CAAEA,CAACzB,OAAe,CAAES,IAAU,GAAKD,GAAG,CAACiB,EAAE,CAACzB,OAAO,CAAES,IAAI,CAC3D,CAAC,CAED,cAAe,CAAAD,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
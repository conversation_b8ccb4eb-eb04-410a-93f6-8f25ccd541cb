﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using MyApi.Data;
using MyApi.Models;
using MyApi.Services;
using System.Security.Claims;
using System.Collections.Generic;
using System.Threading.Tasks;
using System;
using System.Linq;
using Newtonsoft.Json;
using static SkiaSharp.HarfBuzz.SKShaper;
using static System.Runtime.InteropServices.JavaScript.JSType;
using Microsoft.AspNetCore.Http.HttpResults;

namespace MyApi.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    // [Authorize] // 暫時註釋掉以便測試
    public class ScheduleController : ControllerBase
    {
        private readonly AppDbContext _context;
        private readonly IGoogleCalendarService _googleCalendarService;
        private readonly RedisService _redisService;

        public ScheduleController(AppDbContext context, IGoogleCalendarService googleCalendarService, RedisService redisService)
        {
            _context = context;
            _googleCalendarService = googleCalendarService;
            _redisService = redisService;
        }

        // GET: api/Schedule
        [Authorize(Roles = "Admin,Manager,User")]
        [HttpGet]
        public async Task<ActionResult<IEnumerable<CalendarEventDto>>> GetSchedules([FromQuery] ScheduleQueryDto query)
        {
            try
            {
                var schedulesQuery = _context.Schedules
                    .Include(s => s.Doctor)
                    .Include(s => s.Patient)
                    .Where(s => s.IsActive);

                // 應用查詢過濾器
                if (query.DoctorId.HasValue)
                    schedulesQuery = schedulesQuery.Where(s => s.DoctorId == query.DoctorId.Value);

                if (query.PatientId.HasValue)
                    schedulesQuery = schedulesQuery.Where(s => s.PatientId == query.PatientId.Value);

                if (query.StartDate.HasValue)
                    schedulesQuery = schedulesQuery.Where(s => s.StartDateTime >= query.StartDate.Value);

                if (query.EndDate.HasValue)
                    schedulesQuery = schedulesQuery.Where(s => s.EndDateTime <= query.EndDate.Value);

                var schedules = await schedulesQuery
                    .OrderBy(s => s.StartDateTime)
                    .ToListAsync();

                var calendarEvents = schedules.Select(s => new CalendarEventDto
                {
                    Id = s.Id.ToString(),
                    Title = $"{s.Doctor?.Name}-{s.Patient?.FullName}",
                    Start = s.StartDateTime.ToString("yyyy-MM-ddTHH:mm:ss"),
                    End = s.EndDateTime.ToString("yyyy-MM-ddTHH:mm:ss"),
                    BackgroundColor = s.BackgroundColor,
                    BorderColor = s.BorderColor,
                    DoctorId = s.DoctorId,
                    DoctorName = s.Doctor?.Name ?? "",
                    PatientId = s.PatientId,
                    PatientName = s.Patient?.FullName ?? "",
                    TreatmentId = s.TreatmentId,
                    Description = s.Description
                }).ToList();

                return Ok(calendarEvents);
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { error = ex.Message });
            }
        }

        // GET: api/Schedule/5
        [Authorize(Roles = "Admin,Manager,User")]
        [HttpGet("{id}")]
        public async Task<ActionResult<ScheduleResponseDto>> GetSchedule(int id)
        {
            try
            {
                var schedule = await _context.Schedules
                    .Include(s => s.Doctor)
                    .Include(s => s.Patient)
                    .FirstOrDefaultAsync(s => s.Id == id && s.IsActive);

                if (schedule == null)
                {
                    return NotFound(new { message = "找不到指定的行程" });
                }

                var response = new ScheduleResponseDto
                {
                    Id = schedule.Id,
                    Title = schedule.Title,
                    StartDateTime = schedule.StartDateTime,
                    EndDateTime = schedule.EndDateTime,
                    DoctorId = schedule.DoctorId,
                    DoctorName = schedule.Doctor?.Name ?? "",
                    PatientId = schedule.PatientId,
                    PatientName = schedule.Patient?.FullName ?? "",
                    Description = schedule.Description,
                    BackgroundColor = schedule.BackgroundColor,
                    BorderColor = schedule.BorderColor,
                    IsActive = schedule.IsActive,
                    CreatedAt = schedule.CreatedAt,
                    UpdatedAt = schedule.UpdatedAt
                };

                return Ok(response);
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { error = ex.Message });
            }
        }

        // POST: api/Schedule
        [Authorize(Roles = "Admin,Manager")]
        [HttpPost]
        public async Task<ActionResult<ScheduleResponseDto>> CreateSchedule([FromBody] ScheduleCreateDto createDto)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                // 驗證治療師和病患是否存在
                var doctor = await _context.Users.FindAsync(createDto.DoctorId);
                var patient = await _context.Patients.FindAsync(createDto.PatientId);

                if (doctor == null)
                {
                    return BadRequest(new { message = "找不到指定的治療師" });
                }

                if (patient == null)
                {
                    return BadRequest(new { message = "找不到指定的病患" });
                }

                var userId = GetCurrentUserId();
                var createdSchedules = new List<Schedule>();

                createDto.StartDateTime = DateTime.Parse(createDto.StartDateTime.ToString()).ToLocalTime();
                createDto.EndDateTime = DateTime.Parse(createDto.EndDateTime.ToString()).ToLocalTime();

                // 處理重複行程
                if (createDto.RepeatType != RepeatType.None && createDto.RepeatCount > 1)
                {
                    var repeatGroupId = Guid.NewGuid().ToString();

                    for (int i = 0; i < createDto.RepeatCount; i++)
                    {
                        var scheduleDateTime = CalculateRepeatDateTime(createDto.StartDateTime, createDto.RepeatType, i);
                        var endDateTime = CalculateRepeatDateTime(createDto.EndDateTime, createDto.RepeatType, i);

                        // 檢查時間衝突 - 允許同時最多5筆資料
                        var conflictCount = await _context.Schedules
                            .CountAsync(s => s.DoctorId == createDto.DoctorId &&
                                             s.IsActive &&
                                             ((s.StartDateTime <= scheduleDateTime && s.EndDateTime > scheduleDateTime) ||
                                              (s.StartDateTime < endDateTime && s.EndDateTime >= endDateTime) ||
                                              (s.StartDateTime >= scheduleDateTime && s.EndDateTime <= endDateTime)));

                        if (conflictCount >= 5)
                        {
                            // 跳過衝突的時段，繼續創建其他時段
                            continue;
                        }

                        var schedule = new Schedule
                        {
                            Title = $"{doctor.Name}-{patient.FullName}",
                            StartDateTime = scheduleDateTime,
                            EndDateTime = endDateTime,
                            DoctorId = createDto.DoctorId,
                            PatientId = createDto.PatientId,
                            Description = createDto.Description,
                            BackgroundColor = createDto.BackgroundColor ?? "#3788d8",
                            BorderColor = createDto.BorderColor,
                            RepeatGroupId = repeatGroupId,
                            RepeatType = (int)createDto.RepeatType,
                            RepeatSequence = i + 1,
                            CreatedBy = userId,
                            UpdatedBy = userId,
                            CreatedAt = DateTime.Now,
                            UpdatedAt = DateTime.Now
                        };

                        _context.Schedules.Add(schedule);
                        createdSchedules.Add(schedule);
                    }
                }
                else
                {
                    // 單次行程
                    // 檢查時間衝突 - 允許同時最多5筆資料
                    var conflictCount = await _context.Schedules
                        .CountAsync(s => s.DoctorId == createDto.DoctorId &&
                                         s.IsActive &&
                                         ((s.StartDateTime <= createDto.StartDateTime && s.EndDateTime > createDto.StartDateTime) ||
                                          (s.StartDateTime < createDto.EndDateTime && s.EndDateTime >= createDto.EndDateTime) ||
                                          (s.StartDateTime >= createDto.StartDateTime && s.EndDateTime <= createDto.EndDateTime)));

                    if (conflictCount >= 5)
                    {
                        return BadRequest(new { message = "該時間段已達到最大預約數量限制（5筆）" });
                    }

                    var schedule = new Schedule
                    {
                        Title = $"{doctor.Name}-{patient.FullName}",
                        StartDateTime = createDto.StartDateTime,
                        EndDateTime = createDto.EndDateTime,
                        DoctorId = createDto.DoctorId,
                        PatientId = createDto.PatientId,
                        Description = createDto.Description,
                        BackgroundColor = createDto.BackgroundColor ?? "#3788d8",
                        BorderColor = createDto.BorderColor,
                        RepeatType = (int)createDto.RepeatType,
                        CreatedBy = userId,
                        UpdatedBy = userId,
                        CreatedAt = DateTime.Now,
                        UpdatedAt = DateTime.Now
                    };

                    _context.Schedules.Add(schedule);
                    createdSchedules.Add(schedule);
                }

                // 先同步到 Google Calendar，成功後才保存到數據庫
                var googleEventIds = new List<string>();
                foreach (var schedule in createdSchedules)
                {
                    var googleEventId = await _googleCalendarService.CreateEventAsync(schedule);
                    if (string.IsNullOrEmpty(googleEventId))
                    {
                        // Google Calendar 同步失敗，回滾所有已創建的事件
                        foreach (var eventId in googleEventIds)
                        {
                            await _googleCalendarService.DeleteEventAsync(eventId);
                        }
                        return BadRequest(new { message = "Google Calendar 同步失敗，行程創建取消" });
                    }

                    schedule.GoogleCalendarEventId = googleEventId;
                    googleEventIds.Add(googleEventId);
                }

                // Google Calendar 同步成功，保存到數據庫
                await _context.SaveChangesAsync();

                // 返回第一個創建的行程信息
                var firstSchedule = createdSchedules.First();
                var createdSchedule = await _context.Schedules
                    .Include(s => s.Doctor)
                    .Include(s => s.Patient)
                    .FirstOrDefaultAsync(s => s.Id == firstSchedule.Id);

                var response = new ScheduleResponseDto
                {
                    Id = createdSchedule!.Id,
                    Title = createdSchedule.Title,
                    StartDateTime = createdSchedule.StartDateTime,
                    EndDateTime = createdSchedule.EndDateTime,
                    DoctorId = createdSchedule.DoctorId,
                    DoctorName = createdSchedule.Doctor?.Name ?? "",
                    PatientId = createdSchedule.PatientId,
                    PatientName = createdSchedule.Patient?.FullName ?? "",
                    Description = createdSchedule.Description,
                    BackgroundColor = createdSchedule.BackgroundColor,
                    BorderColor = createdSchedule.BorderColor,
                    IsActive = createdSchedule.IsActive,
                    CreatedAt = createdSchedule.CreatedAt,
                    UpdatedAt = createdSchedule.UpdatedAt
                };

                await _redisService.DeleteKeyAsync($"TodaySchedules:{DateTime.Now.Date.ToString("yyyy-MM-dd")}");

                return CreatedAtAction(nameof(GetSchedule), new { id = firstSchedule.Id }, new {
                    schedule = response,
                    createdCount = createdSchedules.Count,
                    message = createdSchedules.Count > 1 ? $"成功創建 {createdSchedules.Count} 個重複行程" : "行程創建成功"
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { error = ex.Message });
            }
        }

        // PUT: api/Schedule/5
        [Authorize(Roles = "Admin,Manager")]
        [HttpPut("{id}")]
        public async Task<ActionResult<ScheduleResponseDto>> UpdateSchedule(int id, [FromBody] ScheduleUpdateDto updateDto)
        {
            try
            {
                if (id != updateDto.Id)
                {
                    return BadRequest(new { message = "ID 不匹配" });
                }

                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                var schedule = await _context.Schedules.FindAsync(id);
                if (schedule == null || !schedule.IsActive)
                {
                    return NotFound(new { message = "找不到指定的行程" });
                }

                // 驗證治療師和病患是否存在
                var doctor = await _context.Users.FindAsync(updateDto.DoctorId);
                var patient = await _context.Patients.FindAsync(updateDto.PatientId);

                if (doctor == null)
                {
                    return BadRequest(new { message = "找不到指定的治療師" });
                }

                if (patient == null)
                {
                    return BadRequest(new { message = "找不到指定的病患" });
                }

                updateDto.StartDateTime = DateTime.Parse(updateDto.StartDateTime.ToString()).ToLocalTime();
                updateDto.EndDateTime = DateTime.Parse(updateDto.EndDateTime.ToString()).ToLocalTime();


                // 檢查時間衝突（排除當前行程）- 允許同時最多5筆資料
                var conflictCount = await _context.Schedules
                    .CountAsync(s => s.Id != id &&
                                     s.DoctorId == updateDto.DoctorId &&
                                     s.IsActive &&
                                     ((s.StartDateTime <= updateDto.StartDateTime && s.EndDateTime > updateDto.StartDateTime) ||
                                      (s.StartDateTime < updateDto.EndDateTime && s.EndDateTime >= updateDto.EndDateTime) ||
                                      (s.StartDateTime >= updateDto.StartDateTime && s.EndDateTime <= updateDto.EndDateTime)));

                if (conflictCount >= 5)
                {
                    return BadRequest(new { message = "該時間段已達到最大預約數量限制（5筆）" });
                }

                // 先更新 Google Calendar，成功後才更新數據庫
                var tempSchedule = new Schedule
                {
                    Id = schedule.Id,
                    Title = $"{doctor.Name}-{patient.FullName}",
                    StartDateTime = updateDto.StartDateTime,
                    EndDateTime = updateDto.EndDateTime,
                    DoctorId = updateDto.DoctorId,
                    PatientId = updateDto.PatientId,
                    Description = updateDto.Description,
                    BackgroundColor = updateDto.BackgroundColor ?? "#3788d8",
                    BorderColor = updateDto.BorderColor
                };

                // 同步更新到 Google Calendar
                if (!string.IsNullOrEmpty(schedule.GoogleCalendarEventId))
                {
                    var success = await _googleCalendarService.UpdateEventAsync(tempSchedule, schedule.GoogleCalendarEventId);
                    if (!success)
                    {
                        return BadRequest(new { message = "Google Calendar 同步失敗，行程更新取消" });
                    }
                }
                else
                {
                    // 如果沒有 Google Calendar ID，先創建事件
                    var googleEventId = await _googleCalendarService.CreateEventAsync(tempSchedule);
                    if (string.IsNullOrEmpty(googleEventId))
                    {
                        return BadRequest(new { message = "Google Calendar 同步失敗，行程更新取消" });
                    }
                    schedule.GoogleCalendarEventId = googleEventId;
                }

                // Google Calendar 同步成功，更新數據庫
                schedule.Title = tempSchedule.Title;
                schedule.StartDateTime = tempSchedule.StartDateTime;
                schedule.EndDateTime = tempSchedule.EndDateTime;
                schedule.DoctorId = tempSchedule.DoctorId;
                schedule.PatientId = tempSchedule.PatientId;
                schedule.Description = tempSchedule.Description;
                schedule.BackgroundColor = tempSchedule.BackgroundColor;
                schedule.BorderColor = tempSchedule.BorderColor;
                schedule.UpdatedBy = GetCurrentUserId();
                schedule.UpdatedAt = DateTime.Now;

                await _context.SaveChangesAsync();

                // 重新查詢以獲取完整信息
                var updatedSchedule = await _context.Schedules
                    .Include(s => s.Doctor)
                    .Include(s => s.Patient)
                    .FirstOrDefaultAsync(s => s.Id == id);

                var response = new ScheduleResponseDto
                {
                    Id = updatedSchedule!.Id,
                    Title = updatedSchedule.Title,
                    StartDateTime = updatedSchedule.StartDateTime,
                    EndDateTime = updatedSchedule.EndDateTime,
                    DoctorId = updatedSchedule.DoctorId,
                    DoctorName = updatedSchedule.Doctor?.Name ?? "",
                    PatientId = updatedSchedule.PatientId,
                    PatientName = updatedSchedule.Patient?.FullName ?? "",
                    Description = updatedSchedule.Description,
                    BackgroundColor = updatedSchedule.BackgroundColor,
                    BorderColor = updatedSchedule.BorderColor,
                    IsActive = updatedSchedule.IsActive,
                    CreatedAt = updatedSchedule.CreatedAt,
                    UpdatedAt = updatedSchedule.UpdatedAt
                };

                await _redisService.DeleteKeyAsync($"TodaySchedules:{DateTime.Now.Date.ToString("yyyy-MM-dd")}");

                return Ok(response);
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { error = ex.Message });
            }
        }

        // DELETE: api/Schedule/5
        [Authorize(Roles = "Admin,Manager")]
        [HttpDelete("{id}")]
        public async Task<IActionResult> DeleteSchedule(int id)
        {
            try
            {
                var schedule = await _context.Schedules.FindAsync(id);
                if (schedule == null || !schedule.IsActive)
                {
                    return NotFound(new { message = "找不到指定的行程" });
                }

                // 先從 Google Calendar 刪除，成功後才軟刪除數據庫記錄
                if (!string.IsNullOrEmpty(schedule.GoogleCalendarEventId))
                {
                    var success = await _googleCalendarService.DeleteEventAsync(schedule.GoogleCalendarEventId);
                    if (!success)
                    {
                        return BadRequest(new { message = "Google Calendar 同步失敗，行程刪除取消" });
                    }
                }

                // Google Calendar 刪除成功，軟刪除數據庫記錄
                schedule.IsActive = false;
                schedule.UpdatedBy = GetCurrentUserId();
                schedule.UpdatedAt = DateTime.Now;

                await _context.SaveChangesAsync();

                return Ok(new { message = "行程已刪除" });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { error = ex.Message });
            }
        }

        // 計算重複日期的輔助方法
        private DateTime CalculateRepeatDateTime(DateTime baseDateTime, RepeatType repeatType, int repeatIndex)
        {
            return repeatType switch
            {
                RepeatType.Daily => baseDateTime.AddDays(repeatIndex),
                RepeatType.Weekly => baseDateTime.AddDays(repeatIndex * 7),
                RepeatType.Monthly => baseDateTime.AddMonths(repeatIndex),
                _ => baseDateTime
            };
        }

        // PATCH: api/Schedule/5/treatment
        [Authorize(Roles = "Admin,Manager,User")]
        [HttpPatch("{id}/treatment")]
        public async Task<IActionResult> UpdateScheduleTreatmentId(int id, [FromBody] UpdateTreatmentIdDto dto)
        {
            try
            {
                var schedule = await _context.Schedules.FindAsync(id);
                if (schedule == null || !schedule.IsActive)
                {
                    return NotFound(new { message = "找不到指定的行程" });
                }

                schedule.TreatmentId = dto.TreatmentId;
                schedule.UpdatedBy = GetCurrentUserId();
                schedule.UpdatedAt = DateTime.Now;

                await _context.SaveChangesAsync();

                await _redisService.DeleteKeyAsync($"TodaySchedules:{DateTime.Now.Date.ToString("yyyy-MM-dd")}");

                return Ok(new { message = "TreatmentId 更新成功", treatmentId = dto.TreatmentId });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { error = ex.Message });
            }
        }

        // 獲取當前用戶 ID 的輔助方法
        private int GetCurrentUserId()
        {
            var userIdClaim = User.FindFirst("UserId");
            return userIdClaim != null ? int.Parse(userIdClaim.Value) : 1; // 默認為 1
        }

        [Authorize(Roles = "Admin,Manager,User")]
        [HttpGet("GetTodaySchedules")]
        public async Task<ActionResult<IEnumerable<CalendarEventDto>>> GetTodaySchedules()
        {
            try
            {
                var redisdata = await _redisService.GetStringAsync($"TodaySchedules:{DateTime.Now.Date.ToString("yyyy-MM-dd")}" );

                if (redisdata == null)
                {
                    var schedulesQuery = _context.Schedules
                    .Include(s => s.Doctor)
                    .Include(s => s.Patient)
                    .Where(s => s.IsActive &&
                    s.StartDateTime >= DateTime.Now.Date &&
                    s.EndDateTime <= DateTime.Now.AddDays(1).Date
                    );

                    var schedules = await schedulesQuery
                        .OrderBy(s => s.StartDateTime)
                        .ToListAsync();

                    var calendarEvents = schedules.Select(s => new CalendarEventDto
                    {
                        Id = s.Id.ToString(),
                        Title = $"{s.Doctor?.Name}-{s.Patient?.FullName}",
                        Start = s.StartDateTime.ToString("yyyy-MM-ddTHH:mm:ss"),
                        End = s.EndDateTime.ToString("yyyy-MM-ddTHH:mm:ss"),
                        BackgroundColor = s.BackgroundColor,
                        BorderColor = s.BorderColor,
                        DoctorId = s.DoctorId,
                        DoctorName = s.Doctor?.Name ?? "",
                        PatientId = s.PatientId,
                        PatientName = s.Patient?.FullName ?? "",
                        TreatmentId = s.TreatmentId,
                        Description = s.Description
                    }).ToList();

                    var redisvalue = JsonConvert.SerializeObject(calendarEvents);

                    await _redisService.SetStringWithDaysAsync($"TodaySchedules:{DateTime.Now.Date.ToString("yyyy-MM-dd")}", redisvalue, 3);

                    redisdata = redisvalue;
                }

                return Ok(redisdata);
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { error = ex.Message });
            }
        }
    }
}

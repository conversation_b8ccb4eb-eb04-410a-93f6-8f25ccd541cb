{"ast": null, "code": "const formatDistanceLocale = {\n  lessThanXSeconds: {\n    one: \"کەمتر لە یەک چرکە\",\n    other: \"کەمتر لە {{count}} چرکە\"\n  },\n  xSeconds: {\n    one: \"1 چرکە\",\n    other: \"{{count}} چرکە\"\n  },\n  halfAMinute: \"نیو کاتژمێر\",\n  lessThanXMinutes: {\n    one: \"کەمتر لە یەک خولەک\",\n    other: \"کەمتر لە {{count}} خولەک\"\n  },\n  xMinutes: {\n    one: \"1 خولەک\",\n    other: \"{{count}} خولەک\"\n  },\n  aboutXHours: {\n    one: \"دەوروبەری 1 کاتژمێر\",\n    other: \"دەوروبەری {{count}} کاتژمێر\"\n  },\n  xHours: {\n    one: \"1 کاتژمێر\",\n    other: \"{{count}} کاتژمێر\"\n  },\n  xDays: {\n    one: \"1 ڕۆژ\",\n    other: \"{{count}} ژۆژ\"\n  },\n  aboutXWeeks: {\n    one: \"دەوروبەری 1 هەفتە\",\n    other: \"دوروبەری {{count}} هەفتە\"\n  },\n  xWeeks: {\n    one: \"1 هەفتە\",\n    other: \"{{count}} هەفتە\"\n  },\n  aboutXMonths: {\n    one: \"داوروبەری 1 مانگ\",\n    other: \"دەوروبەری {{count}} مانگ\"\n  },\n  xMonths: {\n    one: \"1 مانگ\",\n    other: \"{{count}} مانگ\"\n  },\n  aboutXYears: {\n    one: \"دەوروبەری  1 ساڵ\",\n    other: \"دەوروبەری {{count}} ساڵ\"\n  },\n  xYears: {\n    one: \"1 ساڵ\",\n    other: \"{{count}} ساڵ\"\n  },\n  overXYears: {\n    one: \"زیاتر لە ساڵێک\",\n    other: \"زیاتر لە {{count}} ساڵ\"\n  },\n  almostXYears: {\n    one: \"بەنزیکەیی ساڵێک  \",\n    other: \"بەنزیکەیی {{count}} ساڵ\"\n  }\n};\nexport const formatDistance = (token, count, options) => {\n  let result;\n  const tokenValue = formatDistanceLocale[token];\n  if (typeof tokenValue === \"string\") {\n    result = tokenValue;\n  } else if (count === 1) {\n    result = tokenValue.one;\n  } else {\n    result = tokenValue.other.replace(\"{{count}}\", count.toString());\n  }\n  if (options !== null && options !== void 0 && options.addSuffix) {\n    if (options.comparison && options.comparison > 0) {\n      return \"لە ماوەی \" + result + \"دا\";\n    } else {\n      return result + \"پێش ئێستا\";\n    }\n  }\n  return result;\n};", "map": {"version": 3, "names": ["formatDistanceLocale", "lessThanXSeconds", "one", "other", "xSeconds", "halfAMinute", "lessThanXMinutes", "xMinutes", "aboutXHours", "xHours", "xDays", "aboutXWeeks", "xWeeks", "aboutXMonths", "xMonths", "aboutXYears", "xYears", "overXYears", "almostXYears", "formatDistance", "token", "count", "options", "result", "tokenValue", "replace", "toString", "addSuffix", "comparison"], "sources": ["C:/Users/<USER>/Desktop/demo-git/demo-react/my-fullstack-app/frontend/node_modules/date-fns/locale/ckb/_lib/formatDistance.js"], "sourcesContent": ["const formatDistanceLocale = {\n  lessThanXSeconds: {\n    one: \"کەمتر لە یەک چرکە\",\n    other: \"کەمتر لە {{count}} چرکە\",\n  },\n\n  xSeconds: {\n    one: \"1 چرکە\",\n    other: \"{{count}} چرکە\",\n  },\n\n  halfAMinute: \"نیو کاتژمێر\",\n\n  lessThanXMinutes: {\n    one: \"کەمتر لە یەک خولەک\",\n    other: \"کەمتر لە {{count}} خولەک\",\n  },\n\n  xMinutes: {\n    one: \"1 خولەک\",\n    other: \"{{count}} خولەک\",\n  },\n\n  aboutXHours: {\n    one: \"دەوروبەری 1 کاتژمێر\",\n    other: \"دەوروبەری {{count}} کاتژمێر\",\n  },\n\n  xHours: {\n    one: \"1 کاتژمێر\",\n    other: \"{{count}} کاتژمێر\",\n  },\n\n  xDays: {\n    one: \"1 ڕۆژ\",\n    other: \"{{count}} ژۆژ\",\n  },\n\n  aboutXWeeks: {\n    one: \"دەوروبەری 1 هەفتە\",\n    other: \"دوروبەری {{count}} هەفتە\",\n  },\n\n  xWeeks: {\n    one: \"1 هەفتە\",\n    other: \"{{count}} هەفتە\",\n  },\n\n  aboutXMonths: {\n    one: \"داوروبەری 1 مانگ\",\n    other: \"دەوروبەری {{count}} مانگ\",\n  },\n\n  xMonths: {\n    one: \"1 مانگ\",\n    other: \"{{count}} مانگ\",\n  },\n\n  aboutXYears: {\n    one: \"دەوروبەری  1 ساڵ\",\n    other: \"دەوروبەری {{count}} ساڵ\",\n  },\n\n  xYears: {\n    one: \"1 ساڵ\",\n    other: \"{{count}} ساڵ\",\n  },\n\n  overXYears: {\n    one: \"زیاتر لە ساڵێک\",\n    other: \"زیاتر لە {{count}} ساڵ\",\n  },\n\n  almostXYears: {\n    one: \"بەنزیکەیی ساڵێک  \",\n    other: \"بەنزیکەیی {{count}} ساڵ\",\n  },\n};\n\nexport const formatDistance = (token, count, options) => {\n  let result;\n\n  const tokenValue = formatDistanceLocale[token];\n  if (typeof tokenValue === \"string\") {\n    result = tokenValue;\n  } else if (count === 1) {\n    result = tokenValue.one;\n  } else {\n    result = tokenValue.other.replace(\"{{count}}\", count.toString());\n  }\n\n  if (options?.addSuffix) {\n    if (options.comparison && options.comparison > 0) {\n      return \"لە ماوەی \" + result + \"دا\";\n    } else {\n      return result + \"پێش ئێستا\";\n    }\n  }\n\n  return result;\n};\n"], "mappings": "AAAA,MAAMA,oBAAoB,GAAG;EAC3BC,gBAAgB,EAAE;IAChBC,GAAG,EAAE,mBAAmB;IACxBC,KAAK,EAAE;EACT,CAAC;EAEDC,QAAQ,EAAE;IACRF,GAAG,EAAE,QAAQ;IACbC,KAAK,EAAE;EACT,CAAC;EAEDE,WAAW,EAAE,aAAa;EAE1BC,gBAAgB,EAAE;IAChBJ,GAAG,EAAE,oBAAoB;IACzBC,KAAK,EAAE;EACT,CAAC;EAEDI,QAAQ,EAAE;IACRL,GAAG,EAAE,SAAS;IACdC,KAAK,EAAE;EACT,CAAC;EAEDK,WAAW,EAAE;IACXN,GAAG,EAAE,qBAAqB;IAC1BC,KAAK,EAAE;EACT,CAAC;EAEDM,MAAM,EAAE;IACNP,GAAG,EAAE,WAAW;IAChBC,KAAK,EAAE;EACT,CAAC;EAEDO,KAAK,EAAE;IACLR,GAAG,EAAE,OAAO;IACZC,KAAK,EAAE;EACT,CAAC;EAEDQ,WAAW,EAAE;IACXT,GAAG,EAAE,mBAAmB;IACxBC,KAAK,EAAE;EACT,CAAC;EAEDS,MAAM,EAAE;IACNV,GAAG,EAAE,SAAS;IACdC,KAAK,EAAE;EACT,CAAC;EAEDU,YAAY,EAAE;IACZX,GAAG,EAAE,kBAAkB;IACvBC,KAAK,EAAE;EACT,CAAC;EAEDW,OAAO,EAAE;IACPZ,GAAG,EAAE,QAAQ;IACbC,KAAK,EAAE;EACT,CAAC;EAEDY,WAAW,EAAE;IACXb,GAAG,EAAE,kBAAkB;IACvBC,KAAK,EAAE;EACT,CAAC;EAEDa,MAAM,EAAE;IACNd,GAAG,EAAE,OAAO;IACZC,KAAK,EAAE;EACT,CAAC;EAEDc,UAAU,EAAE;IACVf,GAAG,EAAE,gBAAgB;IACrBC,KAAK,EAAE;EACT,CAAC;EAEDe,YAAY,EAAE;IACZhB,GAAG,EAAE,mBAAmB;IACxBC,KAAK,EAAE;EACT;AACF,CAAC;AAED,OAAO,MAAMgB,cAAc,GAAGA,CAACC,KAAK,EAAEC,KAAK,EAAEC,OAAO,KAAK;EACvD,IAAIC,MAAM;EAEV,MAAMC,UAAU,GAAGxB,oBAAoB,CAACoB,KAAK,CAAC;EAC9C,IAAI,OAAOI,UAAU,KAAK,QAAQ,EAAE;IAClCD,MAAM,GAAGC,UAAU;EACrB,CAAC,MAAM,IAAIH,KAAK,KAAK,CAAC,EAAE;IACtBE,MAAM,GAAGC,UAAU,CAACtB,GAAG;EACzB,CAAC,MAAM;IACLqB,MAAM,GAAGC,UAAU,CAACrB,KAAK,CAACsB,OAAO,CAAC,WAAW,EAAEJ,KAAK,CAACK,QAAQ,CAAC,CAAC,CAAC;EAClE;EAEA,IAAIJ,OAAO,aAAPA,OAAO,eAAPA,OAAO,CAAEK,SAAS,EAAE;IACtB,IAAIL,OAAO,CAACM,UAAU,IAAIN,OAAO,CAACM,UAAU,GAAG,CAAC,EAAE;MAChD,OAAO,WAAW,GAAGL,MAAM,GAAG,IAAI;IACpC,CAAC,MAAM;MACL,OAAOA,MAAM,GAAG,WAAW;IAC7B;EACF;EAEA,OAAOA,MAAM;AACf,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
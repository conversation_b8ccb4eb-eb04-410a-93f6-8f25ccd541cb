{"ast": null, "code": "const formatRelativeLocale = {\n  lastWeek: \"eeee'ที่แล้วเวลา' p\",\n  yesterday: \"'เมื่อวานนี้เวลา' p\",\n  today: \"'วันนี้เวลา' p\",\n  tomorrow: \"'พรุ่งนี้เวลา' p\",\n  nextWeek: \"eeee 'เวลา' p\",\n  other: \"P\"\n};\nexport const formatRelative = (token, _date, _baseDate, _options) => formatRelativeLocale[token];", "map": {"version": 3, "names": ["formatRelativeLocale", "lastWeek", "yesterday", "today", "tomorrow", "nextWeek", "other", "formatRelative", "token", "_date", "_baseDate", "_options"], "sources": ["C:/Users/<USER>/Desktop/demo-git/demo-react/my-fullstack-app/frontend/node_modules/date-fns/locale/th/_lib/formatRelative.js"], "sourcesContent": ["const formatRelativeLocale = {\n  lastWeek: \"eeee'ที่แล้วเวลา' p\",\n  yesterday: \"'เมื่อวานนี้เวลา' p\",\n  today: \"'วันนี้เวลา' p\",\n  tomorrow: \"'พรุ่งนี้เวลา' p\",\n  nextWeek: \"eeee 'เวลา' p\",\n  other: \"P\",\n};\n\nexport const formatRelative = (token, _date, _baseDate, _options) =>\n  formatRelativeLocale[token];\n"], "mappings": "AAAA,MAAMA,oBAAoB,GAAG;EAC3BC,QAAQ,EAAE,qBAAqB;EAC/BC,SAAS,EAAE,qBAAqB;EAChCC,KAAK,EAAE,gBAAgB;EACvBC,QAAQ,EAAE,kBAAkB;EAC5BC,QAAQ,EAAE,eAAe;EACzBC,KAAK,EAAE;AACT,CAAC;AAED,OAAO,MAAMC,cAAc,GAAGA,CAACC,KAAK,EAAEC,KAAK,EAAEC,SAAS,EAAEC,QAAQ,KAC9DX,oBAAoB,CAACQ,KAAK,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "import _objectSpread from\"C:/Users/<USER>/Desktop/demo-git/demo-react/my-fullstack-app/frontend/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";import{Button}from'primereact/button';import{Column}from'primereact/column';import{ConfirmDialog,confirmDialog}from'primereact/confirmdialog';import{DataTable}from'primereact/datatable';import{Dropdown}from'primereact/dropdown';import{InputNumber}from'primereact/inputnumber';import{Toast}from'primereact/toast';import React,{useEffect,useRef,useState}from'react';import{useLocation}from\"react-router-dom\";import api from\"../../services/api\";import connection from\"../../services/signalr\";import useDataType from\"../../hooks/useDataType\";import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const ReceiptsDetailPage=()=>{var _location$state;const location=useLocation();const treatment=(_location$state=location.state)===null||_location$state===void 0?void 0:_location$state.treatment;const[treatmentItem,setTreatmentItem]=useState('');const[treatmentMoney,setTreatmentMoney]=useState(0);const[orderNo,setorderNo]=useState('');const[isFileCreate,setisFileCreate]=useState(false);const[DisableCopyBtn,setDisableCopyBtn]=useState(false);const toast=useRef(null);const[receipts,setReceipts]=useState([]);const{dataType,loading}=useDataType();const[progress,setProgress]=useState(0);const[message,setMessage]=useState(\"\");// Use message to avoid unused variable warning\nconsole.log('Progress:',progress,'Message:',message);const treatmentItemsUsed=receipts.map(r=>r.treatmentItem);useEffect(()=>{setisFileCreate(treatment.receiptUrl?true:false);loadReceipts();connection.start().then(()=>{console.log(\"已連線至 SignalR\");//console.log(\"連線 ID\", connection.connectionId);\n}).catch(err=>console.error(\"SignalR 連線失敗:\",err));connection.on(\"ReportProgress\",value=>{setProgress(value);});connection.on(\"ReportFinished\",msg=>{setMessage(msg);});return()=>{connection.stop();};},[]);const loadReceipts=async()=>{try{const res=await api.get('/api/receipt/',{params:{Id:treatment.id}});setReceipts(res.data);if(res.data.length>0){setorderNo(res.data[0].orderNo);// 綁定 orderNo\nsetDisableCopyBtn(true);}}catch(err){var _toast$current;(_toast$current=toast.current)===null||_toast$current===void 0?void 0:_toast$current.show({severity:'error',summary:'錯誤',detail:'讀取收據資料失敗'});}};const getNo=()=>{const min=10000;const max=99999;return Math.floor(Math.random()*(max-min+1))+min;};const getOptions=groupId=>{var _dataType$find;return((_dataType$find=dataType.find(group=>group.groupId===groupId))===null||_dataType$find===void 0?void 0:_dataType$find.dataTypes.map(item=>({label:item.name,value:item.name})))||[];};const addRow=()=>{if(!treatmentItem||!treatmentMoney)return alert('請填寫完整欄位');if(receipts.length>=4)return alert('最多只能新增 4 筆資料');if(treatmentItemsUsed.includes(treatmentItem))return alert('項目名稱不可重複');const newRow={id:getNo(),treatmentItem,treatmentMoney,treatmentId:treatment.id,patientId:treatment.patientId};setReceipts([...receipts,newRow]);setTreatmentItem('');setTreatmentMoney(0);};const copyLatestRecord=()=>{confirmDialog({message:'是否複製上一筆收據紀錄',header:'複製確認',icon:'pi pi-exclamation-triangle',accept:async()=>{try{const response=await api.get(\"/api/Receipt/GetLatestRecord/\".concat(treatment.patientId),{method:'GET',headers:{'Content-Type':'application/json'}});if(response){var _toast$current2;const latestRecords=await response.data;// 清空現有的收據記錄\nsetReceipts([]);// 複製上一筆記錄，但生成新的 ID\nconst copiedRecords=latestRecords.map(record=>_objectSpread(_objectSpread({},record),{},{id:getNo(),treatmentId:treatment.id,orderNo:undefined// 清空 orderNo，讓它成為新記錄\n}));setReceipts(copiedRecords);(_toast$current2=toast.current)===null||_toast$current2===void 0?void 0:_toast$current2.show({severity:\"success\",summary:\"複製成功\",detail:\"已複製上一筆收據紀錄\"});}}catch(error){var _toast$current3;(_toast$current3=toast.current)===null||_toast$current3===void 0?void 0:_toast$current3.show({severity:\"error\",summary:\"複製失敗\",detail:error.details});}}});};const deleteRow=rowData=>{if(receipts.length==1){var _toast$current4;(_toast$current4=toast.current)===null||_toast$current4===void 0?void 0:_toast$current4.show({severity:'error',summary:'錯誤',detail:\"資料請勿少於一筆\"});}else{const updated=receipts.filter(r=>r.id!==rowData.id);setReceipts(updated);}};const saveToServer=async()=>{if(receipts.length==0){var _toast$current5;(_toast$current5=toast.current)===null||_toast$current5===void 0?void 0:_toast$current5.show({severity:'error',summary:'錯誤',detail:\"資料請勿少於一筆\"});return;}try{if(!orderNo){var _toast$current6;// 新增模式\nconst res=await api.post('/api/receipt/Insert',receipts);(_toast$current6=toast.current)===null||_toast$current6===void 0?void 0:_toast$current6.show({severity:'success',summary:'成功',detail:res.data.msg});setorderNo(res.data.orderNo);// 取得新的 orderNo\nsetDisableCopyBtn(true);}else{var _toast$current7;// 更新模式\nconst updated=receipts.map(r=>_objectSpread(_objectSpread({},r),{},{orderNo}));await api.put('/api/receipt/Update',updated);(_toast$current7=toast.current)===null||_toast$current7===void 0?void 0:_toast$current7.show({severity:'success',summary:'成功',detail:'更新完成'});}}catch(err){var _err$response,_toast$current8;const detail=(err===null||err===void 0?void 0:(_err$response=err.response)===null||_err$response===void 0?void 0:_err$response.data)||'儲存失敗';(_toast$current8=toast.current)===null||_toast$current8===void 0?void 0:_toast$current8.show({severity:'error',summary:'錯誤',detail});}};const exportToPDF=async()=>{if(receipts.length==0){var _toast$current9;(_toast$current9=toast.current)===null||_toast$current9===void 0?void 0:_toast$current9.show({severity:'error',summary:'錯誤',detail:\"資料請勿少於一筆\"});return;}try{var _toast$current0;const response=await api.get(\"/api/receipt/ExportReceiptsPdf\",{params:{TreatmentId:treatment.id,orderNo:orderNo,connectionId:connection.connectionId},responseType:'blob'// blob 格式取得資料\n});(_toast$current0=toast.current)===null||_toast$current0===void 0?void 0:_toast$current0.show({severity:\"success\",summary:\"成功\",detail:\"收據製作成功\"});// 產生blob url\nconst file=new Blob([response.data],{type:'application/pdf'});const fileURL=URL.createObjectURL(file);setisFileCreate(true);// 在新分頁開啟PDF\nwindow.open(fileURL);}catch(error){var _toast$current1;(_toast$current1=toast.current)===null||_toast$current1===void 0?void 0:_toast$current1.show({severity:\"error\",summary:\"錯誤\",detail:error.message});}};const actionBodyTemplate=rowData=>{return/*#__PURE__*/_jsx(Button,{icon:\"pi pi-trash\",className:\"p-button-danger\",onClick:()=>deleteRow(rowData)});};const confirm=()=>{confirmDialog({message:'收據開立後就無法修改內容，確定要開立收據嗎？',header:'收據開立確認',icon:'pi pi-exclamation-triangle',defaultFocus:'accept',acceptLabel:'確定',rejectLabel:'取消',accept:()=>exportToPDF()});};if(loading)return/*#__PURE__*/_jsx(\"p\",{children:\"Loading...\"});const toolbar=/*#__PURE__*/_jsxs(\"div\",{className:\"card flex flex-wrap p-fluid\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"col-6 md:col-2\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex-auto\",children:[/*#__PURE__*/_jsx(\"label\",{className:\"font-bold block mb-2\",children:\"\\u6CBB\\u7642\\u9805\\u76EE\"}),/*#__PURE__*/_jsx(Dropdown,{value:treatmentItem,options:getOptions(9).filter(o=>!treatmentItemsUsed.includes(o.value)),onChange:e=>setTreatmentItem(e.value),placeholder:\"\\u8ACB\\u9078\\u64C7\"})]})}),/*#__PURE__*/_jsx(\"div\",{className:\"col-5 md:col-2\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex-auto\",children:[/*#__PURE__*/_jsx(\"label\",{htmlFor:\"mile\",className:\"font-bold block mb-2\",children:\"\\u91D1\\u984D\"}),/*#__PURE__*/_jsx(InputNumber,{value:treatmentMoney,onValueChange:e=>setTreatmentMoney(Number(e.target.value))})]})}),/*#__PURE__*/_jsxs(\"div\",{className:\" flex flex-wrap col-11 md:col-5\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"flex col-6 md:col-2\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex-auto\",children:[/*#__PURE__*/_jsx(\"label\",{className:\"font-bold block mb-2\",children:\" \"}),/*#__PURE__*/_jsx(Button,{label:\"\\u65B0\\u589E\",icon:\"pi pi-plus\",onClick:addRow,disabled:receipts.length>=5})]})}),/*#__PURE__*/_jsx(\"div\",{className:\"flex col-5 md:col-2\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex-auto\",children:[/*#__PURE__*/_jsx(\"label\",{className:\"font-bold block mb-2\",children:\" \"}),/*#__PURE__*/_jsx(Button,{label:\"\\u8907\\u88FD\",icon:\"pi pi-copy\",severity:\"info\",onClick:copyLatestRecord,disabled:DisableCopyBtn})]})}),/*#__PURE__*/_jsx(\"div\",{className:\"flex col-6 md:col-2\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex-auto\",children:[/*#__PURE__*/_jsx(\"label\",{className:\"font-bold block mb-2\",children:\" \"}),/*#__PURE__*/_jsx(Button,{label:\"\\u5132\\u5B58\",icon:\"pi pi-save\",severity:\"success\",onClick:saveToServer})]})}),isFileCreate&&/*#__PURE__*/_jsx(\"div\",{className:\"flex col-6 md:col-3\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex-auto\",children:[/*#__PURE__*/_jsx(\"label\",{className:\"font-bold block mb-2\",children:\" \"}),/*#__PURE__*/_jsx(Button,{label:\"\\u6AA2\\u8996\\u6536\\u64DA\",icon:\"pi pi-file-pdf\",severity:\"secondary\",onClick:exportToPDF})]})}),!isFileCreate&&/*#__PURE__*/_jsx(\"div\",{className:\"flex col-5 md:col-3\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex-auto\",children:[/*#__PURE__*/_jsx(\"label\",{className:\"font-bold block mb-2\",children:\" \"}),/*#__PURE__*/_jsx(Button,{label:\"\\u958B\\u7ACB\\u6536\\u64DA\",icon:\"pi pi-file-pdf\",severity:\"secondary\",onClick:confirm})]})}),/*#__PURE__*/_jsx(\"div\",{className:\"flex col-5 md:col-2\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex-auto\",children:[/*#__PURE__*/_jsx(\"label\",{className:\"font-bold block mb-2\",children:\"\\u5831\\u8868\\u7522\\u751F\\u9032\\u5EA6\"}),/*#__PURE__*/_jsx(\"progress\",{value:progress,max:\"100\",style:{width:'100%'}})]})})]})]});return/*#__PURE__*/_jsxs(\"div\",{className:\"card\",children:[/*#__PURE__*/_jsx(Toast,{ref:toast}),/*#__PURE__*/_jsx(ConfirmDialog,{}),toolbar,/*#__PURE__*/_jsxs(DataTable,{value:receipts,dataKey:\"id\",children:[/*#__PURE__*/_jsx(Column,{field:\"treatmentItem\",header:\"\\u9805\\u76EE\"}),/*#__PURE__*/_jsx(Column,{dataType:\"numeric\",field:\"treatmentMoney\",header:\"\\u91D1\\u984D\"}),/*#__PURE__*/_jsx(Column,{body:actionBodyTemplate,header:\"\\u522A\\u9664\"})]})]});};export default ReceiptsDetailPage;", "map": {"version": 3, "names": ["<PERSON><PERSON>", "Column", "ConfirmDialog", "confirmDialog", "DataTable", "Dropdown", "InputNumber", "Toast", "React", "useEffect", "useRef", "useState", "useLocation", "api", "connection", "useDataType", "jsx", "_jsx", "jsxs", "_jsxs", "ReceiptsDetailPage", "_location$state", "location", "treatment", "state", "treatmentItem", "setTreatmentItem", "treatmentMoney", "setTreatmentMoney", "orderNo", "setorderNo", "isFileCreate", "setisFileCreate", "DisableCopyBtn", "setDisableCopyBtn", "toast", "receipts", "setReceipts", "dataType", "loading", "progress", "setProgress", "message", "setMessage", "console", "log", "treatmentItemsUsed", "map", "r", "receiptUrl", "loadReceipts", "start", "then", "catch", "err", "error", "on", "value", "msg", "stop", "res", "get", "params", "Id", "id", "data", "length", "_toast$current", "current", "show", "severity", "summary", "detail", "getNo", "min", "max", "Math", "floor", "random", "getOptions", "groupId", "_dataType$find", "find", "group", "dataTypes", "item", "label", "name", "addRow", "alert", "includes", "newRow", "treatmentId", "patientId", "copyLatestRecord", "header", "icon", "accept", "response", "concat", "method", "headers", "_toast$current2", "latestRecords", "copiedRecords", "record", "_objectSpread", "undefined", "_toast$current3", "details", "deleteRow", "rowData", "_toast$current4", "updated", "filter", "saveToServer", "_toast$current5", "_toast$current6", "post", "_toast$current7", "put", "_err$response", "_toast$current8", "exportToPDF", "_toast$current9", "_toast$current0", "TreatmentId", "connectionId", "responseType", "file", "Blob", "type", "fileURL", "URL", "createObjectURL", "window", "open", "_toast$current1", "actionBodyTemplate", "className", "onClick", "confirm", "defaultFocus", "acceptLabel", "<PERSON><PERSON><PERSON><PERSON>", "children", "toolbar", "options", "o", "onChange", "e", "placeholder", "htmlFor", "onValueChange", "Number", "target", "disabled", "style", "width", "ref", "dataKey", "field", "body"], "sources": ["C:/Users/<USER>/Desktop/demo-git/demo-react/my-fullstack-app/frontend/src/components/Page/ReceiptsDetailPage.tsx"], "sourcesContent": ["import { But<PERSON> } from 'primereact/button';\r\nimport { Column } from 'primereact/column';\r\nimport { ConfirmDialog, confirmDialog } from 'primereact/confirmdialog';\r\nimport { DataTable } from 'primereact/datatable';\r\nimport { Dropdown } from 'primereact/dropdown';\r\nimport { InputNumber } from 'primereact/inputnumber';\r\nimport { Toast } from 'primereact/toast';\r\nimport React, { useEffect, useRef, useState } from 'react';\r\nimport { useLocation } from \"react-router-dom\";\r\nimport api from \"../../services/api\";\r\nimport connection from \"../../services/signalr\";\r\nimport useDataType from \"../../hooks/useDataType\";\r\n\r\ninterface Receipt {\r\n  id?: number;\r\n  treatmentItem: string;\r\n  treatmentMoney: number;\r\n  treatmentId: number;\r\n  patientId: number;\r\n  orderNo?: string;\r\n}\r\n\r\nconst ReceiptsDetailPage: React.FC = () => {\r\n  const location = useLocation();\r\n  const treatment = location.state?.treatment;\r\n  \r\n  const [treatmentItem, setTreatmentItem] = useState('');\r\n  const [treatmentMoney, setTreatmentMoney] = useState<number>(0);\r\n  const [orderNo, setorderNo] = useState<string>('');\r\n  const [isFileCreate, setisFileCreate] = useState<boolean>(false);\r\n  const [DisableCopyBtn, setDisableCopyBtn] = useState(false);\r\n  const toast = useRef<Toast>(null);\r\n  const [receipts, setReceipts] = useState<Receipt[]>([]);\r\n  const { dataType, loading } = useDataType();\r\n  const [progress, setProgress] = useState(0);\r\n  const [message, setMessage] = useState(\"\");\r\n\r\n  // Use message to avoid unused variable warning\r\n  console.log('Progress:', progress, 'Message:', message);\r\n\r\n  const treatmentItemsUsed = receipts.map(r => r.treatmentItem);\r\n\r\n  useEffect(() => {\r\n    setisFileCreate(treatment.receiptUrl? true : false )\r\n\r\n    loadReceipts();\r\n\r\n    connection\r\n    .start()\r\n    .then(() => {\r\n      console.log(\"已連線至 SignalR\");\r\n      //console.log(\"連線 ID\", connection.connectionId);\r\n    })\r\n    .catch(err => console.error(\"SignalR 連線失敗:\", err));\r\n\r\n    connection.on(\"ReportProgress\", (value) => {\r\n      setProgress(value);\r\n    });\r\n\r\n    connection.on(\"ReportFinished\", (msg) => {\r\n      setMessage(msg);\r\n    });\r\n\r\n    return () => {\r\n      connection.stop();\r\n    };\r\n\r\n  }, []);\r\n\r\n  const loadReceipts = async () => {\r\n\r\n    try {\r\n      const res = await api.get('/api/receipt/', {\r\n        params: {\r\n          Id: treatment.id\r\n        }\r\n      });\r\n      setReceipts(res.data);\r\n      if (res.data.length > 0) {\r\n        setorderNo(res.data[0].orderNo); // 綁定 orderNo\r\n        setDisableCopyBtn(true);\r\n      }\r\n    } catch (err) {\r\n      toast.current?.show({ severity: 'error', summary: '錯誤', detail: '讀取收據資料失敗' });\r\n    }\r\n  };\r\n\r\n\r\n  const getNo = () => {\r\n    const min = 10000; \r\n    const max = 99999; \r\n\r\n    return Math.floor(Math.random() * (max - min + 1)) + min;\r\n  };\r\n\r\n    const getOptions = (groupId: number) => {\r\n    return dataType.find(group => group.groupId === groupId)?.dataTypes.map(item => ({\r\n      label: item.name,\r\n      value: item.name\r\n    })) || [];\r\n  };\r\n\r\n  const addRow = () => {\r\n    if (!treatmentItem || !treatmentMoney) return alert('請填寫完整欄位');\r\n    if (receipts.length >= 4) return alert('最多只能新增 4 筆資料');\r\n    if (treatmentItemsUsed.includes(treatmentItem)) return alert('項目名稱不可重複');\r\n\r\n    const newRow: Receipt = {\r\n      id: getNo(),\r\n      treatmentItem,\r\n      treatmentMoney,\r\n      treatmentId: treatment.id,\r\n      patientId: treatment.patientId,\r\n    };\r\n    setReceipts([...receipts, newRow]);\r\n    setTreatmentItem('');\r\n    setTreatmentMoney(0);\r\n  };\r\n\r\n  const copyLatestRecord = () => {\r\n    confirmDialog({\r\n      message: '是否複製上一筆收據紀錄',\r\n      header: '複製確認',\r\n      icon: 'pi pi-exclamation-triangle',\r\n      accept: async () => {\r\n        try {\r\n          const response = await api.get(`/api/Receipt/GetLatestRecord/${treatment.patientId}`, {\r\n            method: 'GET',\r\n            headers: {\r\n              'Content-Type': 'application/json',\r\n            },\r\n          });\r\n\r\n          if (response) {\r\n            const latestRecords = await response.data;\r\n            // 清空現有的收據記錄\r\n            setReceipts([]);\r\n            // 複製上一筆記錄，但生成新的 ID\r\n            const copiedRecords = latestRecords.map((record: Receipt) => ({\r\n              ...record,\r\n              id: getNo(),\r\n              treatmentId: treatment.id,\r\n              orderNo: undefined // 清空 orderNo，讓它成為新記錄\r\n            }));\r\n            setReceipts(copiedRecords);\r\n            toast.current?.show({ severity: \"success\", summary: \"複製成功\", detail: \"已複製上一筆收據紀錄\" });\r\n          } \r\n        } catch (error:any) {\r\n          toast.current?.show({ severity: \"error\", summary: \"複製失敗\", detail: error.details });\r\n        }\r\n      }\r\n    });\r\n  };\r\n\r\n  const deleteRow = (rowData: Receipt) => {\r\n    if(receipts.length == 1){\r\n      toast.current?.show({ severity: 'error', summary: '錯誤', detail: \"資料請勿少於一筆\" });\r\n    }else{\r\n      const updated = receipts.filter((r) => r.id !== rowData.id);\r\n      setReceipts(updated);\r\n    }\r\n    \r\n  };\r\n\r\n  const saveToServer = async () => {\r\n    if(receipts.length == 0){\r\n      toast.current?.show({ severity: 'error', summary: '錯誤', detail: \"資料請勿少於一筆\" });\r\n      return \r\n    }\r\n\r\n    try {\r\n      if (!orderNo) {\r\n        // 新增模式\r\n        const res = await api.post('/api/receipt/Insert', receipts);\r\n        toast.current?.show({ severity: 'success', summary: '成功', detail: res.data.msg });\r\n        setorderNo(res.data.orderNo); // 取得新的 orderNo\r\n        setDisableCopyBtn(true);\r\n      } else {\r\n        // 更新模式\r\n        const updated = receipts.map(r => ({\r\n          ...r,\r\n          orderNo\r\n        }));\r\n        await api.put('/api/receipt/Update', updated);\r\n        toast.current?.show({ severity: 'success', summary: '成功', detail: '更新完成' });\r\n      }\r\n    } catch (err: any) {\r\n      const detail = err?.response?.data || '儲存失敗';\r\n      toast.current?.show({ severity: 'error', summary: '錯誤', detail });\r\n    }\r\n  };\r\n\r\n  const exportToPDF = async () => {\r\n    if(receipts.length == 0){\r\n      toast.current?.show({ severity: 'error', summary: '錯誤', detail: \"資料請勿少於一筆\" });\r\n      return \r\n    }\r\n\r\n    try {\r\n      const response = await api.get(\"/api/receipt/ExportReceiptsPdf\", {\r\n        params: { \r\n          TreatmentId: treatment.id,\r\n          orderNo: orderNo,\r\n          connectionId: connection.connectionId\r\n        },\r\n        responseType: 'blob'  // blob 格式取得資料\r\n      });\r\n      \r\n      toast.current?.show({ severity: \"success\", summary: \"成功\", detail: \"收據製作成功\"});\r\n\r\n      // 產生blob url\r\n      const file = new Blob([response.data], { type: 'application/pdf' });\r\n      const fileURL = URL.createObjectURL(file);\r\n      setisFileCreate(true)\r\n      // 在新分頁開啟PDF\r\n      window.open(fileURL);\r\n    } \r\n    catch (error:any) {\r\n        toast.current?.show({ severity: \"error\", summary: \"錯誤\", detail: error.message });\r\n    }\r\n  };\r\n\r\n  const actionBodyTemplate = (rowData: Receipt) => {\r\n    return (\r\n      <Button\r\n        icon=\"pi pi-trash\"\r\n        className=\"p-button-danger\"\r\n        onClick={() => deleteRow(rowData)}\r\n      />\r\n    );\r\n  };\r\n\r\n  const confirm = () => {\r\n      confirmDialog({\r\n          message: '收據開立後就無法修改內容，確定要開立收據嗎？',\r\n          header: '收據開立確認',\r\n          icon: 'pi pi-exclamation-triangle',\r\n          defaultFocus: 'accept',\r\n          acceptLabel: '確定',\r\n          rejectLabel: '取消',\r\n          accept: () => exportToPDF(),\r\n      });\r\n  };\r\n\r\n  if (loading) return <p>Loading...</p>;\r\n\r\n  const toolbar = (\r\n        <div className=\"card flex flex-wrap p-fluid\">\r\n          <div className=\"col-6 md:col-2\">\r\n            <div className=\"flex-auto\">\r\n              <label className=\"font-bold block mb-2\">治療項目</label>\r\n              <Dropdown\r\n                value={treatmentItem}\r\n                options={getOptions(9).filter(o => !treatmentItemsUsed.includes(o.value))}\r\n                onChange={(e) => setTreatmentItem(e.value)}\r\n                placeholder=\"請選擇\"\r\n              />\r\n            </div>\r\n          </div>\r\n          <div className=\"col-5 md:col-2\">\r\n            <div className=\"flex-auto\">\r\n              <label htmlFor=\"mile\" className=\"font-bold block mb-2\">金額</label>\r\n              <InputNumber \r\n                value={treatmentMoney}\r\n                onValueChange={(e) => setTreatmentMoney(Number(e.target.value))}\r\n                \r\n              />\r\n            </div>\r\n          </div>\r\n          <div className=\" flex flex-wrap col-11 md:col-5\">\r\n            <div className=\"flex col-6 md:col-2\">\r\n              <div className=\"flex-auto\">\r\n                <label className=\"font-bold block mb-2\"> </label>\r\n                <Button label=\"新增\" icon=\"pi pi-plus\" onClick={addRow} disabled={receipts.length >= 5} />\r\n              </div>\r\n            </div>\r\n            <div className=\"flex col-5 md:col-2\">\r\n              <div className=\"flex-auto\">\r\n                <label className=\"font-bold block mb-2\"> </label>\r\n                <Button label=\"複製\" icon=\"pi pi-copy\" severity=\"info\" onClick={copyLatestRecord} disabled={DisableCopyBtn} />\r\n              </div>\r\n            </div>\r\n            <div className=\"flex col-6 md:col-2\">\r\n              <div className=\"flex-auto\">\r\n                <label className=\"font-bold block mb-2\"> </label>\r\n                <Button label=\"儲存\" icon=\"pi pi-save\" severity=\"success\" onClick={saveToServer} />\r\n              </div>\r\n            </div>\r\n            { isFileCreate && (<div className=\"flex col-6 md:col-3\">\r\n              <div className=\"flex-auto\">\r\n                <label className=\"font-bold block mb-2\"> </label>\r\n                <Button label=\"檢視收據\" icon=\"pi pi-file-pdf\" severity=\"secondary\" onClick={exportToPDF} /> \r\n              </div>\r\n            </div>)}\r\n            { !isFileCreate && (<div className=\"flex col-5 md:col-3\">\r\n              <div className=\"flex-auto\">\r\n                <label className=\"font-bold block mb-2\"> </label>\r\n                <Button label=\"開立收據\" icon=\"pi pi-file-pdf\" severity=\"secondary\" onClick={confirm} /> \r\n              </div>\r\n            </div>) }\r\n            <div className=\"flex col-5 md:col-2\">\r\n              <div className=\"flex-auto\">\r\n                <label className=\"font-bold block mb-2\">報表產生進度</label>\r\n                <progress value={progress} max=\"100\" style={{ width: '100%' }} />\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n  );\r\n\r\nreturn (\r\n    <div className=\"card\">\r\n      <Toast ref={toast} />\r\n      <ConfirmDialog />\r\n      {toolbar}\r\n      <DataTable value={receipts} dataKey=\"id\">\r\n        <Column field=\"treatmentItem\" header=\"項目\" />\r\n        <Column dataType=\"numeric\" field=\"treatmentMoney\" header=\"金額\" />\r\n        <Column body={actionBodyTemplate} header=\"刪除\" />\r\n      </DataTable>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default ReceiptsDetailPage;"], "mappings": "wJAAA,OAASA,MAAM,KAAQ,mBAAmB,CAC1C,OAASC,MAAM,KAAQ,mBAAmB,CAC1C,OAASC,aAAa,CAAEC,aAAa,KAAQ,0BAA0B,CACvE,OAASC,SAAS,KAAQ,sBAAsB,CAChD,OAASC,QAAQ,KAAQ,qBAAqB,CAC9C,OAASC,WAAW,KAAQ,wBAAwB,CACpD,OAASC,KAAK,KAAQ,kBAAkB,CACxC,MAAO,CAAAC,KAAK,EAAIC,SAAS,CAAEC,MAAM,CAAEC,QAAQ,KAAQ,OAAO,CAC1D,OAASC,WAAW,KAAQ,kBAAkB,CAC9C,MAAO,CAAAC,GAAG,KAAM,oBAAoB,CACpC,MAAO,CAAAC,UAAU,KAAM,wBAAwB,CAC/C,MAAO,CAAAC,WAAW,KAAM,yBAAyB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAWlD,KAAM,CAAAC,kBAA4B,CAAGA,CAAA,GAAM,KAAAC,eAAA,CACzC,KAAM,CAAAC,QAAQ,CAAGV,WAAW,CAAC,CAAC,CAC9B,KAAM,CAAAW,SAAS,EAAAF,eAAA,CAAGC,QAAQ,CAACE,KAAK,UAAAH,eAAA,iBAAdA,eAAA,CAAgBE,SAAS,CAE3C,KAAM,CAACE,aAAa,CAAEC,gBAAgB,CAAC,CAAGf,QAAQ,CAAC,EAAE,CAAC,CACtD,KAAM,CAACgB,cAAc,CAAEC,iBAAiB,CAAC,CAAGjB,QAAQ,CAAS,CAAC,CAAC,CAC/D,KAAM,CAACkB,OAAO,CAAEC,UAAU,CAAC,CAAGnB,QAAQ,CAAS,EAAE,CAAC,CAClD,KAAM,CAACoB,YAAY,CAAEC,eAAe,CAAC,CAAGrB,QAAQ,CAAU,KAAK,CAAC,CAChE,KAAM,CAACsB,cAAc,CAAEC,iBAAiB,CAAC,CAAGvB,QAAQ,CAAC,KAAK,CAAC,CAC3D,KAAM,CAAAwB,KAAK,CAAGzB,MAAM,CAAQ,IAAI,CAAC,CACjC,KAAM,CAAC0B,QAAQ,CAAEC,WAAW,CAAC,CAAG1B,QAAQ,CAAY,EAAE,CAAC,CACvD,KAAM,CAAE2B,QAAQ,CAAEC,OAAQ,CAAC,CAAGxB,WAAW,CAAC,CAAC,CAC3C,KAAM,CAACyB,QAAQ,CAAEC,WAAW,CAAC,CAAG9B,QAAQ,CAAC,CAAC,CAAC,CAC3C,KAAM,CAAC+B,OAAO,CAAEC,UAAU,CAAC,CAAGhC,QAAQ,CAAC,EAAE,CAAC,CAE1C;AACAiC,OAAO,CAACC,GAAG,CAAC,WAAW,CAAEL,QAAQ,CAAE,UAAU,CAAEE,OAAO,CAAC,CAEvD,KAAM,CAAAI,kBAAkB,CAAGV,QAAQ,CAACW,GAAG,CAACC,CAAC,EAAIA,CAAC,CAACvB,aAAa,CAAC,CAE7DhB,SAAS,CAAC,IAAM,CACduB,eAAe,CAACT,SAAS,CAAC0B,UAAU,CAAE,IAAI,CAAG,KAAM,CAAC,CAEpDC,YAAY,CAAC,CAAC,CAEdpC,UAAU,CACTqC,KAAK,CAAC,CAAC,CACPC,IAAI,CAAC,IAAM,CACVR,OAAO,CAACC,GAAG,CAAC,cAAc,CAAC,CAC3B;AACF,CAAC,CAAC,CACDQ,KAAK,CAACC,GAAG,EAAIV,OAAO,CAACW,KAAK,CAAC,eAAe,CAAED,GAAG,CAAC,CAAC,CAElDxC,UAAU,CAAC0C,EAAE,CAAC,gBAAgB,CAAGC,KAAK,EAAK,CACzChB,WAAW,CAACgB,KAAK,CAAC,CACpB,CAAC,CAAC,CAEF3C,UAAU,CAAC0C,EAAE,CAAC,gBAAgB,CAAGE,GAAG,EAAK,CACvCf,UAAU,CAACe,GAAG,CAAC,CACjB,CAAC,CAAC,CAEF,MAAO,IAAM,CACX5C,UAAU,CAAC6C,IAAI,CAAC,CAAC,CACnB,CAAC,CAEH,CAAC,CAAE,EAAE,CAAC,CAEN,KAAM,CAAAT,YAAY,CAAG,KAAAA,CAAA,GAAY,CAE/B,GAAI,CACF,KAAM,CAAAU,GAAG,CAAG,KAAM,CAAA/C,GAAG,CAACgD,GAAG,CAAC,eAAe,CAAE,CACzCC,MAAM,CAAE,CACNC,EAAE,CAAExC,SAAS,CAACyC,EAChB,CACF,CAAC,CAAC,CACF3B,WAAW,CAACuB,GAAG,CAACK,IAAI,CAAC,CACrB,GAAIL,GAAG,CAACK,IAAI,CAACC,MAAM,CAAG,CAAC,CAAE,CACvBpC,UAAU,CAAC8B,GAAG,CAACK,IAAI,CAAC,CAAC,CAAC,CAACpC,OAAO,CAAC,CAAE;AACjCK,iBAAiB,CAAC,IAAI,CAAC,CACzB,CACF,CAAE,MAAOoB,GAAG,CAAE,KAAAa,cAAA,CACZ,CAAAA,cAAA,CAAAhC,KAAK,CAACiC,OAAO,UAAAD,cAAA,iBAAbA,cAAA,CAAeE,IAAI,CAAC,CAAEC,QAAQ,CAAE,OAAO,CAAEC,OAAO,CAAE,IAAI,CAAEC,MAAM,CAAE,UAAW,CAAC,CAAC,CAC/E,CACF,CAAC,CAGD,KAAM,CAAAC,KAAK,CAAGA,CAAA,GAAM,CAClB,KAAM,CAAAC,GAAG,CAAG,KAAK,CACjB,KAAM,CAAAC,GAAG,CAAG,KAAK,CAEjB,MAAO,CAAAC,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,MAAM,CAAC,CAAC,EAAIH,GAAG,CAAGD,GAAG,CAAG,CAAC,CAAC,CAAC,CAAGA,GAAG,CAC1D,CAAC,CAEC,KAAM,CAAAK,UAAU,CAAIC,OAAe,EAAK,KAAAC,cAAA,CACxC,MAAO,EAAAA,cAAA,CAAA3C,QAAQ,CAAC4C,IAAI,CAACC,KAAK,EAAIA,KAAK,CAACH,OAAO,GAAKA,OAAO,CAAC,UAAAC,cAAA,iBAAjDA,cAAA,CAAmDG,SAAS,CAACrC,GAAG,CAACsC,IAAI,GAAK,CAC/EC,KAAK,CAAED,IAAI,CAACE,IAAI,CAChB9B,KAAK,CAAE4B,IAAI,CAACE,IACd,CAAC,CAAC,CAAC,GAAI,EAAE,CACX,CAAC,CAED,KAAM,CAAAC,MAAM,CAAGA,CAAA,GAAM,CACnB,GAAI,CAAC/D,aAAa,EAAI,CAACE,cAAc,CAAE,MAAO,CAAA8D,KAAK,CAAC,SAAS,CAAC,CAC9D,GAAIrD,QAAQ,CAAC8B,MAAM,EAAI,CAAC,CAAE,MAAO,CAAAuB,KAAK,CAAC,cAAc,CAAC,CACtD,GAAI3C,kBAAkB,CAAC4C,QAAQ,CAACjE,aAAa,CAAC,CAAE,MAAO,CAAAgE,KAAK,CAAC,UAAU,CAAC,CAExE,KAAM,CAAAE,MAAe,CAAG,CACtB3B,EAAE,CAAES,KAAK,CAAC,CAAC,CACXhD,aAAa,CACbE,cAAc,CACdiE,WAAW,CAAErE,SAAS,CAACyC,EAAE,CACzB6B,SAAS,CAAEtE,SAAS,CAACsE,SACvB,CAAC,CACDxD,WAAW,CAAC,CAAC,GAAGD,QAAQ,CAAEuD,MAAM,CAAC,CAAC,CAClCjE,gBAAgB,CAAC,EAAE,CAAC,CACpBE,iBAAiB,CAAC,CAAC,CAAC,CACtB,CAAC,CAED,KAAM,CAAAkE,gBAAgB,CAAGA,CAAA,GAAM,CAC7B3F,aAAa,CAAC,CACZuC,OAAO,CAAE,aAAa,CACtBqD,MAAM,CAAE,MAAM,CACdC,IAAI,CAAE,4BAA4B,CAClCC,MAAM,CAAE,KAAAA,CAAA,GAAY,CAClB,GAAI,CACF,KAAM,CAAAC,QAAQ,CAAG,KAAM,CAAArF,GAAG,CAACgD,GAAG,iCAAAsC,MAAA,CAAiC5E,SAAS,CAACsE,SAAS,EAAI,CACpFO,MAAM,CAAE,KAAK,CACbC,OAAO,CAAE,CACP,cAAc,CAAE,kBAClB,CACF,CAAC,CAAC,CAEF,GAAIH,QAAQ,CAAE,KAAAI,eAAA,CACZ,KAAM,CAAAC,aAAa,CAAG,KAAM,CAAAL,QAAQ,CAACjC,IAAI,CACzC;AACA5B,WAAW,CAAC,EAAE,CAAC,CACf;AACA,KAAM,CAAAmE,aAAa,CAAGD,aAAa,CAACxD,GAAG,CAAE0D,MAAe,EAAAC,aAAA,CAAAA,aAAA,IACnDD,MAAM,MACTzC,EAAE,CAAES,KAAK,CAAC,CAAC,CACXmB,WAAW,CAAErE,SAAS,CAACyC,EAAE,CACzBnC,OAAO,CAAE8E,SAAU;AAAA,EACnB,CAAC,CACHtE,WAAW,CAACmE,aAAa,CAAC,CAC1B,CAAAF,eAAA,CAAAnE,KAAK,CAACiC,OAAO,UAAAkC,eAAA,iBAAbA,eAAA,CAAejC,IAAI,CAAC,CAAEC,QAAQ,CAAE,SAAS,CAAEC,OAAO,CAAE,MAAM,CAAEC,MAAM,CAAE,YAAa,CAAC,CAAC,CACrF,CACF,CAAE,MAAOjB,KAAS,CAAE,KAAAqD,eAAA,CAClB,CAAAA,eAAA,CAAAzE,KAAK,CAACiC,OAAO,UAAAwC,eAAA,iBAAbA,eAAA,CAAevC,IAAI,CAAC,CAAEC,QAAQ,CAAE,OAAO,CAAEC,OAAO,CAAE,MAAM,CAAEC,MAAM,CAAEjB,KAAK,CAACsD,OAAQ,CAAC,CAAC,CACpF,CACF,CACF,CAAC,CAAC,CACJ,CAAC,CAED,KAAM,CAAAC,SAAS,CAAIC,OAAgB,EAAK,CACtC,GAAG3E,QAAQ,CAAC8B,MAAM,EAAI,CAAC,CAAC,KAAA8C,eAAA,CACtB,CAAAA,eAAA,CAAA7E,KAAK,CAACiC,OAAO,UAAA4C,eAAA,iBAAbA,eAAA,CAAe3C,IAAI,CAAC,CAAEC,QAAQ,CAAE,OAAO,CAAEC,OAAO,CAAE,IAAI,CAAEC,MAAM,CAAE,UAAW,CAAC,CAAC,CAC/E,CAAC,IAAI,CACH,KAAM,CAAAyC,OAAO,CAAG7E,QAAQ,CAAC8E,MAAM,CAAElE,CAAC,EAAKA,CAAC,CAACgB,EAAE,GAAK+C,OAAO,CAAC/C,EAAE,CAAC,CAC3D3B,WAAW,CAAC4E,OAAO,CAAC,CACtB,CAEF,CAAC,CAED,KAAM,CAAAE,YAAY,CAAG,KAAAA,CAAA,GAAY,CAC/B,GAAG/E,QAAQ,CAAC8B,MAAM,EAAI,CAAC,CAAC,KAAAkD,eAAA,CACtB,CAAAA,eAAA,CAAAjF,KAAK,CAACiC,OAAO,UAAAgD,eAAA,iBAAbA,eAAA,CAAe/C,IAAI,CAAC,CAAEC,QAAQ,CAAE,OAAO,CAAEC,OAAO,CAAE,IAAI,CAAEC,MAAM,CAAE,UAAW,CAAC,CAAC,CAC7E,OACF,CAEA,GAAI,CACF,GAAI,CAAC3C,OAAO,CAAE,KAAAwF,eAAA,CACZ;AACA,KAAM,CAAAzD,GAAG,CAAG,KAAM,CAAA/C,GAAG,CAACyG,IAAI,CAAC,qBAAqB,CAAElF,QAAQ,CAAC,CAC3D,CAAAiF,eAAA,CAAAlF,KAAK,CAACiC,OAAO,UAAAiD,eAAA,iBAAbA,eAAA,CAAehD,IAAI,CAAC,CAAEC,QAAQ,CAAE,SAAS,CAAEC,OAAO,CAAE,IAAI,CAAEC,MAAM,CAAEZ,GAAG,CAACK,IAAI,CAACP,GAAI,CAAC,CAAC,CACjF5B,UAAU,CAAC8B,GAAG,CAACK,IAAI,CAACpC,OAAO,CAAC,CAAE;AAC9BK,iBAAiB,CAAC,IAAI,CAAC,CACzB,CAAC,IAAM,KAAAqF,eAAA,CACL;AACA,KAAM,CAAAN,OAAO,CAAG7E,QAAQ,CAACW,GAAG,CAACC,CAAC,EAAA0D,aAAA,CAAAA,aAAA,IACzB1D,CAAC,MACJnB,OAAO,EACP,CAAC,CACH,KAAM,CAAAhB,GAAG,CAAC2G,GAAG,CAAC,qBAAqB,CAAEP,OAAO,CAAC,CAC7C,CAAAM,eAAA,CAAApF,KAAK,CAACiC,OAAO,UAAAmD,eAAA,iBAAbA,eAAA,CAAelD,IAAI,CAAC,CAAEC,QAAQ,CAAE,SAAS,CAAEC,OAAO,CAAE,IAAI,CAAEC,MAAM,CAAE,MAAO,CAAC,CAAC,CAC7E,CACF,CAAE,MAAOlB,GAAQ,CAAE,KAAAmE,aAAA,CAAAC,eAAA,CACjB,KAAM,CAAAlD,MAAM,CAAG,CAAAlB,GAAG,SAAHA,GAAG,kBAAAmE,aAAA,CAAHnE,GAAG,CAAE4C,QAAQ,UAAAuB,aAAA,iBAAbA,aAAA,CAAexD,IAAI,GAAI,MAAM,CAC5C,CAAAyD,eAAA,CAAAvF,KAAK,CAACiC,OAAO,UAAAsD,eAAA,iBAAbA,eAAA,CAAerD,IAAI,CAAC,CAAEC,QAAQ,CAAE,OAAO,CAAEC,OAAO,CAAE,IAAI,CAAEC,MAAO,CAAC,CAAC,CACnE,CACF,CAAC,CAED,KAAM,CAAAmD,WAAW,CAAG,KAAAA,CAAA,GAAY,CAC9B,GAAGvF,QAAQ,CAAC8B,MAAM,EAAI,CAAC,CAAC,KAAA0D,eAAA,CACtB,CAAAA,eAAA,CAAAzF,KAAK,CAACiC,OAAO,UAAAwD,eAAA,iBAAbA,eAAA,CAAevD,IAAI,CAAC,CAAEC,QAAQ,CAAE,OAAO,CAAEC,OAAO,CAAE,IAAI,CAAEC,MAAM,CAAE,UAAW,CAAC,CAAC,CAC7E,OACF,CAEA,GAAI,KAAAqD,eAAA,CACF,KAAM,CAAA3B,QAAQ,CAAG,KAAM,CAAArF,GAAG,CAACgD,GAAG,CAAC,gCAAgC,CAAE,CAC/DC,MAAM,CAAE,CACNgE,WAAW,CAAEvG,SAAS,CAACyC,EAAE,CACzBnC,OAAO,CAAEA,OAAO,CAChBkG,YAAY,CAAEjH,UAAU,CAACiH,YAC3B,CAAC,CACDC,YAAY,CAAE,MAAQ;AACxB,CAAC,CAAC,CAEF,CAAAH,eAAA,CAAA1F,KAAK,CAACiC,OAAO,UAAAyD,eAAA,iBAAbA,eAAA,CAAexD,IAAI,CAAC,CAAEC,QAAQ,CAAE,SAAS,CAAEC,OAAO,CAAE,IAAI,CAAEC,MAAM,CAAE,QAAQ,CAAC,CAAC,CAE5E;AACA,KAAM,CAAAyD,IAAI,CAAG,GAAI,CAAAC,IAAI,CAAC,CAAChC,QAAQ,CAACjC,IAAI,CAAC,CAAE,CAAEkE,IAAI,CAAE,iBAAkB,CAAC,CAAC,CACnE,KAAM,CAAAC,OAAO,CAAGC,GAAG,CAACC,eAAe,CAACL,IAAI,CAAC,CACzCjG,eAAe,CAAC,IAAI,CAAC,CACrB;AACAuG,MAAM,CAACC,IAAI,CAACJ,OAAO,CAAC,CACtB,CACA,MAAO7E,KAAS,CAAE,KAAAkF,eAAA,CACd,CAAAA,eAAA,CAAAtG,KAAK,CAACiC,OAAO,UAAAqE,eAAA,iBAAbA,eAAA,CAAepE,IAAI,CAAC,CAAEC,QAAQ,CAAE,OAAO,CAAEC,OAAO,CAAE,IAAI,CAAEC,MAAM,CAAEjB,KAAK,CAACb,OAAQ,CAAC,CAAC,CACpF,CACF,CAAC,CAED,KAAM,CAAAgG,kBAAkB,CAAI3B,OAAgB,EAAK,CAC/C,mBACE9F,IAAA,CAACjB,MAAM,EACLgG,IAAI,CAAC,aAAa,CAClB2C,SAAS,CAAC,iBAAiB,CAC3BC,OAAO,CAAEA,CAAA,GAAM9B,SAAS,CAACC,OAAO,CAAE,CACnC,CAAC,CAEN,CAAC,CAED,KAAM,CAAA8B,OAAO,CAAGA,CAAA,GAAM,CAClB1I,aAAa,CAAC,CACVuC,OAAO,CAAE,wBAAwB,CACjCqD,MAAM,CAAE,QAAQ,CAChBC,IAAI,CAAE,4BAA4B,CAClC8C,YAAY,CAAE,QAAQ,CACtBC,WAAW,CAAE,IAAI,CACjBC,WAAW,CAAE,IAAI,CACjB/C,MAAM,CAAEA,CAAA,GAAM0B,WAAW,CAAC,CAC9B,CAAC,CAAC,CACN,CAAC,CAED,GAAIpF,OAAO,CAAE,mBAAOtB,IAAA,MAAAgI,QAAA,CAAG,YAAU,CAAG,CAAC,CAErC,KAAM,CAAAC,OAAO,cACP/H,KAAA,QAAKwH,SAAS,CAAC,6BAA6B,CAAAM,QAAA,eAC1ChI,IAAA,QAAK0H,SAAS,CAAC,gBAAgB,CAAAM,QAAA,cAC7B9H,KAAA,QAAKwH,SAAS,CAAC,WAAW,CAAAM,QAAA,eACxBhI,IAAA,UAAO0H,SAAS,CAAC,sBAAsB,CAAAM,QAAA,CAAC,0BAAI,CAAO,CAAC,cACpDhI,IAAA,CAACZ,QAAQ,EACPoD,KAAK,CAAEhC,aAAc,CACrB0H,OAAO,CAAEpE,UAAU,CAAC,CAAC,CAAC,CAACmC,MAAM,CAACkC,CAAC,EAAI,CAACtG,kBAAkB,CAAC4C,QAAQ,CAAC0D,CAAC,CAAC3F,KAAK,CAAC,CAAE,CAC1E4F,QAAQ,CAAGC,CAAC,EAAK5H,gBAAgB,CAAC4H,CAAC,CAAC7F,KAAK,CAAE,CAC3C8F,WAAW,CAAC,oBAAK,CAClB,CAAC,EACC,CAAC,CACH,CAAC,cACNtI,IAAA,QAAK0H,SAAS,CAAC,gBAAgB,CAAAM,QAAA,cAC7B9H,KAAA,QAAKwH,SAAS,CAAC,WAAW,CAAAM,QAAA,eACxBhI,IAAA,UAAOuI,OAAO,CAAC,MAAM,CAACb,SAAS,CAAC,sBAAsB,CAAAM,QAAA,CAAC,cAAE,CAAO,CAAC,cACjEhI,IAAA,CAACX,WAAW,EACVmD,KAAK,CAAE9B,cAAe,CACtB8H,aAAa,CAAGH,CAAC,EAAK1H,iBAAiB,CAAC8H,MAAM,CAACJ,CAAC,CAACK,MAAM,CAAClG,KAAK,CAAC,CAAE,CAEjE,CAAC,EACC,CAAC,CACH,CAAC,cACNtC,KAAA,QAAKwH,SAAS,CAAC,iCAAiC,CAAAM,QAAA,eAC9ChI,IAAA,QAAK0H,SAAS,CAAC,qBAAqB,CAAAM,QAAA,cAClC9H,KAAA,QAAKwH,SAAS,CAAC,WAAW,CAAAM,QAAA,eACxBhI,IAAA,UAAO0H,SAAS,CAAC,sBAAsB,CAAAM,QAAA,CAAC,GAAC,CAAO,CAAC,cACjDhI,IAAA,CAACjB,MAAM,EAACsF,KAAK,CAAC,cAAI,CAACU,IAAI,CAAC,YAAY,CAAC4C,OAAO,CAAEpD,MAAO,CAACoE,QAAQ,CAAExH,QAAQ,CAAC8B,MAAM,EAAI,CAAE,CAAE,CAAC,EACrF,CAAC,CACH,CAAC,cACNjD,IAAA,QAAK0H,SAAS,CAAC,qBAAqB,CAAAM,QAAA,cAClC9H,KAAA,QAAKwH,SAAS,CAAC,WAAW,CAAAM,QAAA,eACxBhI,IAAA,UAAO0H,SAAS,CAAC,sBAAsB,CAAAM,QAAA,CAAC,GAAC,CAAO,CAAC,cACjDhI,IAAA,CAACjB,MAAM,EAACsF,KAAK,CAAC,cAAI,CAACU,IAAI,CAAC,YAAY,CAAC1B,QAAQ,CAAC,MAAM,CAACsE,OAAO,CAAE9C,gBAAiB,CAAC8D,QAAQ,CAAE3H,cAAe,CAAE,CAAC,EACzG,CAAC,CACH,CAAC,cACNhB,IAAA,QAAK0H,SAAS,CAAC,qBAAqB,CAAAM,QAAA,cAClC9H,KAAA,QAAKwH,SAAS,CAAC,WAAW,CAAAM,QAAA,eACxBhI,IAAA,UAAO0H,SAAS,CAAC,sBAAsB,CAAAM,QAAA,CAAC,GAAC,CAAO,CAAC,cACjDhI,IAAA,CAACjB,MAAM,EAACsF,KAAK,CAAC,cAAI,CAACU,IAAI,CAAC,YAAY,CAAC1B,QAAQ,CAAC,SAAS,CAACsE,OAAO,CAAEzB,YAAa,CAAE,CAAC,EAC9E,CAAC,CACH,CAAC,CACJpF,YAAY,eAAKd,IAAA,QAAK0H,SAAS,CAAC,qBAAqB,CAAAM,QAAA,cACrD9H,KAAA,QAAKwH,SAAS,CAAC,WAAW,CAAAM,QAAA,eACxBhI,IAAA,UAAO0H,SAAS,CAAC,sBAAsB,CAAAM,QAAA,CAAC,GAAC,CAAO,CAAC,cACjDhI,IAAA,CAACjB,MAAM,EAACsF,KAAK,CAAC,0BAAM,CAACU,IAAI,CAAC,gBAAgB,CAAC1B,QAAQ,CAAC,WAAW,CAACsE,OAAO,CAAEjB,WAAY,CAAE,CAAC,EACrF,CAAC,CACH,CAAE,CACL,CAAC5F,YAAY,eAAKd,IAAA,QAAK0H,SAAS,CAAC,qBAAqB,CAAAM,QAAA,cACtD9H,KAAA,QAAKwH,SAAS,CAAC,WAAW,CAAAM,QAAA,eACxBhI,IAAA,UAAO0H,SAAS,CAAC,sBAAsB,CAAAM,QAAA,CAAC,GAAC,CAAO,CAAC,cACjDhI,IAAA,CAACjB,MAAM,EAACsF,KAAK,CAAC,0BAAM,CAACU,IAAI,CAAC,gBAAgB,CAAC1B,QAAQ,CAAC,WAAW,CAACsE,OAAO,CAAEC,OAAQ,CAAE,CAAC,EACjF,CAAC,CACH,CAAE,cACP5H,IAAA,QAAK0H,SAAS,CAAC,qBAAqB,CAAAM,QAAA,cAClC9H,KAAA,QAAKwH,SAAS,CAAC,WAAW,CAAAM,QAAA,eACxBhI,IAAA,UAAO0H,SAAS,CAAC,sBAAsB,CAAAM,QAAA,CAAC,sCAAM,CAAO,CAAC,cACtDhI,IAAA,aAAUwC,KAAK,CAAEjB,QAAS,CAACmC,GAAG,CAAC,KAAK,CAACkF,KAAK,CAAE,CAAEC,KAAK,CAAE,MAAO,CAAE,CAAE,CAAC,EAC9D,CAAC,CACH,CAAC,EACH,CAAC,EACH,CACV,CAEH,mBACI3I,KAAA,QAAKwH,SAAS,CAAC,MAAM,CAAAM,QAAA,eACnBhI,IAAA,CAACV,KAAK,EAACwJ,GAAG,CAAE5H,KAAM,CAAE,CAAC,cACrBlB,IAAA,CAACf,aAAa,GAAE,CAAC,CAChBgJ,OAAO,cACR/H,KAAA,CAACf,SAAS,EAACqD,KAAK,CAAErB,QAAS,CAAC4H,OAAO,CAAC,IAAI,CAAAf,QAAA,eACtChI,IAAA,CAAChB,MAAM,EAACgK,KAAK,CAAC,eAAe,CAAClE,MAAM,CAAC,cAAI,CAAE,CAAC,cAC5C9E,IAAA,CAAChB,MAAM,EAACqC,QAAQ,CAAC,SAAS,CAAC2H,KAAK,CAAC,gBAAgB,CAAClE,MAAM,CAAC,cAAI,CAAE,CAAC,cAChE9E,IAAA,CAAChB,MAAM,EAACiK,IAAI,CAAExB,kBAAmB,CAAC3C,MAAM,CAAC,cAAI,CAAE,CAAC,EACvC,CAAC,EACT,CAAC,CAEV,CAAC,CAED,cAAe,CAAA3E,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
import React, { ReactNode, useRef } from 'react';
import { useNavigate } from "react-router-dom";

import { Button } from "primereact/button";
import { <PERSON><PERSON><PERSON> } from "primereact/menubar";
import { MenuItem } from "primereact/menuitem";
import { Toast } from "primereact/toast";
import { log } from '../../utils/logger';

import { useAuth } from '../../contexts/AuthContext';
import useMenu from '../../hooks/useMenu';
import BreadcrumbNav from '../Common/BreadcrumbNav';
import LoadingSpinner from '../Common/LoadingSpinner';

interface LayoutProps {
  children: ReactNode;
}

const Layout: React.FC<LayoutProps> = ({ children }) => {
  const navigate = useNavigate();
  const { isAuthenticated, logout: authLogout, user } = useAuth();
  const { menu, loading } = useMenu(isAuthenticated);
  const toast = useRef<Toast>(null);

  const handleLogout = () => {
    log.auth('Layout: 執行登出');
    // 使用 AuthContext 的 logout 方法
    authLogout();

    // 使用 Toast 顯示登出訊息
    toast.current?.show({
      severity: 'success',
      summary: '登出成功',
      detail: '您已成功登出系統',
      life: 2000
    });

    // 延遲導航，確保狀態清除完成和Toast顯示
    setTimeout(() => {
      log.route('Layout: 導航到登入頁面');
      navigate("/login", { replace: true });
    }, 1000);
  };

  if (loading) {
    return <LoadingSpinner message="載入選單中..." />;
  }

  const HeaderItems: MenuItem[] = [
    {
      label: "首頁",
      icon: "pi pi-home",
      command: () => {
        navigate("/");
      }
    }
  ];

  // 建立群組選單
  menu.forEach((group) => {
    const templist = group.menus
      .filter((data) => data.isEnabled)
      .filter((data) => !data.path.includes('detail')) // 過濾掉詳情頁面
      .map((data) => ({
        key: data.itemId,
        label: data.name,
        command: () => {
          navigate(data.path);
        }
      }));

    // 只有當群組有選單項目時才添加到標題選單
    if (templist.length > 0) {
      HeaderItems.push({
        label: group.groupName,
        icon: group.groupIcon,
        items: templist,
      });
    }
  });

  // 獲取用戶姓名，如果沒有則從 localStorage 獲取
  const getUserDisplayName = () => {
    if (user?.username) {
      return user.username + " 治療師";
    }
    // 從 localStorage 獲取用戶名稱
    const storedUsername = localStorage.getItem('username');
    return storedUsername || '用戶';
  };

  const HeaderEndItems = (
    <div className="flex align-items-center gap-2">
      <label className="font-bold block">{getUserDisplayName()}您好</label>
      <Button
        icon="pi pi-sign-out"
        label="登出"
        className="p-button-text"
        onClick={handleLogout}
      />
    </div>
  );

  return (
    <div className="layout-wrapper min-h-screen flex flex-column">
      <Toast ref={toast} />
      <Menubar model={HeaderItems} end={HeaderEndItems} className="border-none" />
      <div className="breadcrumb-container p-3 bg-gray-50 surface-border">
        <BreadcrumbNav />
      </div>
      <div className="main-content flex-1 p-3">
        {children}
      </div>
    </div>
  );
};

export default Layout;
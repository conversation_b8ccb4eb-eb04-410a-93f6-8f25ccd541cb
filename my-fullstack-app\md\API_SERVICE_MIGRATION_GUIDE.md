# API Service 遷移指南

## 📋 概述

`apiService.ts` 是一個統一的 API 服務層，提供：

1. **類型安全的 API 調用**
2. **統一的錯誤處理**
3. **按功能模組化的 API 組織**
4. **基於 `apiClient` 的統一接口**

## 🔧 API 服務結構

### 核心組件
- **`apiClient`**: 基礎 HTTP 客戶端，包含攔截器和錯誤處理
- **各功能 API 類別**: 按業務邏輯組織的 API 端點
- **統一導出**: `apiService` 對象包含所有 API 服務

### API 類別組織

```typescript
export const apiService = {
  system: SystemApi,      // 系統相關 API
  patient: PatientApi,    // 病患管理 API
  treatment: TreatmentApi, // 治療記錄 API
  receipt: ReceiptApi,    // 收據管理 API
  user: UserApi,          // 用戶管理 API
  auth: AuthApi,          // 認證相關 API
  schedule: ScheduleApi,  // 排程管理 API
  file: FileApi,          // 文件管理 API
  backup: BackupApi,      // 備份管理 API
  roleMenu: RoleMenuApi,  // 權限管理 API
};
```

## 🚀 遷移對照表

### 1. 系統管理 API

| 舊的調用方式 | 新的調用方式 |
|-------------|-------------|
| `api.get('/api/system/GetMenus')` | `apiService.system.getMenus()` |
| `api.get('/api/system/GetDataType')` | `apiService.system.getDataTypes()` |
| `api.post('/api/system/UploadFile', formData)` | `apiService.system.uploadFile(formData)` |
| `api.get('/api/system/DownloadFile', {params})` | `apiService.system.downloadFile(filename)` |
| `api.get('/api/system/GetMonthSatistics')` | `apiService.system.getMonthStatistics()` |
| `api.get('/api/system/GetWeekSatistics')` | `apiService.system.getWeekStatistics()` |
| `api.get('/api/system/ResetMemCache')` | `apiService.system.resetMemCache()` |

### 2. 病患管理 API

| 舊的調用方式 | 新的調用方式 |
|-------------|-------------|
| `api.get('/api/patients/GetList', {params})` | `apiService.patient.getList(params)` |
| `api.get('/api/patients/', {params: {id}})` | `apiService.patient.getById(id)` |
| `api.post('/api/patients/Insert', data)` | `apiService.patient.create(data)` |
| `api.put('/api/patients/Update', data)` | `apiService.patient.update(data)` |
| `api.get('/api/patients/Delete', {params})` | `apiService.patient.delete(id)` |
| `api.get('/api/patients/PatientList')` | `apiService.patient.getPatientList()` |

### 3. 治療記錄 API

| 舊的調用方式 | 新的調用方式 |
|-------------|-------------|
| `api.get('/api/treatment/GetList', {params})` | `apiService.treatment.getList(params)` |
| `api.get('/api/treatment/', {params})` | `apiService.treatment.getById(id)` |
| `api.post('/api/treatment/Insert', data)` | `apiService.treatment.create(data)` |
| `api.put('/api/treatment/Update', data)` | `apiService.treatment.update(data)` |
| `api.get('/api/treatment/Delete', {params})` | `apiService.treatment.delete(orderNo)` |

### 4. 用戶管理 API

| 舊的調用方式 | 新的調用方式 |
|-------------|-------------|
| `api.get('/api/users/DoctorList')` | `apiService.user.getDoctorList()` |
| `api.get('/api/users/GetUserRolesList', {params})` | `apiService.user.getUserRolesList(params)` |
| `api.get('/api/users/GetRoles')` | `apiService.user.getRoles()` |
| `api.post('/api/Users/<USER>', data)` | `apiService.user.create(data)` |
| `api.put('/api/Users/<USER>', data)` | `apiService.user.update(data)` |
| `api.delete('/api/Users/<USER>/${id}')` | `apiService.user.delete(id)` |

### 5. 認證 API

| 舊的調用方式 | 新的調用方式 |
|-------------|-------------|
| `api.post('/api/auth/login', credentials)` | `apiService.auth.login(credentials)` |
| `api.post('/api/auth/logout', userid)` | `apiService.auth.logout(userid)` |
| `api.get('/api/auth/GetUserLoginLog', {params})` | `apiService.auth.getUserLoginLog(params)` |

### 6. 權限管理 API

| 舊的調用方式 | 新的調用方式 |
|-------------|-------------|
| `api.get('/api/RoleMenu/GetUserPermissions')` | `apiService.roleMenu.getUserPermissions()` |
| `api.get('/api/RoleMenu/GetRoles')` | `apiService.roleMenu.getRoles()` |
| `api.get('/api/RoleMenu/GetRoleMenus/${id}')` | `apiService.roleMenu.getRoleMenus(id)` |
| `api.post('/api/RoleMenu/UpdateRoleMenus', data)` | `apiService.roleMenu.updateRoleMenus(data)` |

## 📝 使用示例

### 舊的方式
```typescript
// 分散在各個組件中的 API 調用
const response = await api.get('/api/patients/GetList', { 
  params: { name: 'John' } 
});

const patient = await api.get('/api/patients/', { 
  params: { id: 123 } 
});

await api.post('/api/patients/Insert', patientData);
```

### 新的方式
```typescript
// 使用統一的 API 服務
const patients = await apiService.patient.getList({ name: 'John' });
const patient = await apiService.patient.getById(123);
await apiService.patient.create(patientData);
```

## ✅ 遷移優勢

### 1. **類型安全**
- 所有 API 調用都有明確的類型定義
- 減少運行時錯誤

### 2. **代碼組織**
- 按業務邏輯組織 API 端點
- 更容易維護和查找

### 3. **統一錯誤處理**
- 所有 API 調用共享相同的錯誤處理邏輯
- 統一的認證和攔截器

### 4. **更好的開發體驗**
- IDE 自動完成和類型提示
- 更清晰的 API 文檔

### 5. **向後兼容**
- 保留了原始的 `get`, `post`, `put`, `delete` 方法
- 漸進式遷移

## 🔄 遷移步驟

### 1. 立即可用
現有代碼可以繼續使用：
```typescript
import { apiService } from '../services/apiService';

// 向後兼容的調用方式
await apiService.get('/api/patients/GetList');
```

### 2. 逐步遷移
建議按頁面逐步遷移到新的 API 方式：
```typescript
// 新的推薦方式
await apiService.patient.getList();
```

### 3. 完全遷移
最終目標是所有組件都使用新的 API 服務結構。

## 📋 待遷移的文件

以下文件需要更新 API 調用：

### 高優先級（已部分更新）
- ✅ `PatientsPage.tsx` - 已更新部分 API 調用
- ✅ `TreatmentsPage.tsx` - 已更新部分 API 調用
- ✅ `usePermissions.ts` - 已更新權限 API

### 中優先級
- `HomePage.tsx` - 統計數據和治療記錄 API
- `SchedulesPage.tsx` - 排程管理 API
- `UsersPage.tsx` - 用戶管理 API
- `ReceiptsPage.tsx` - 收據管理 API

### 低優先級
- `BackupPage.tsx` - 備份管理 API
- `ImageManagementPage.tsx` - 文件管理 API
- `ReportManagementPage.tsx` - 報表管理 API
- `IpBlocksPage.tsx` - IP 封鎖管理 API

## 🎯 下一步行動

1. **完成權限頁面遷移** - 將 `RoleMenuManagementPage.tsx` 更新為使用新的 API 服務
2. **更新核心頁面** - 遷移 `HomePage.tsx`, `SchedulesPage.tsx` 等主要頁面
3. **移除舊的 API 調用** - 逐步移除直接的 `api.get/post` 調用
4. **添加更多類型定義** - 為 API 響應添加更精確的 TypeScript 類型

這個遷移將大大提升代碼的可維護性和開發體驗！
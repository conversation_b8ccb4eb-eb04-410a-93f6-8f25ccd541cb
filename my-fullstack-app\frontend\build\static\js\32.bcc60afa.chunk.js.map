{"version": 3, "file": "static/js/32.bcc60afa.chunk.js", "mappings": "2QAsBA,MA8SA,EA9SqCA,KAAO,IAADC,EACzC,MACMC,EAA0B,QAAjBD,GADEE,EAAAA,EAAAA,MACUC,aAAK,IAAAH,OAAA,EAAdA,EAAgBC,WAE3BG,EAAeC,IAAoBC,EAAAA,EAAAA,UAAS,KAC5CC,EAAgBC,IAAqBF,EAAAA,EAAAA,UAAiB,IACtDG,EAASC,IAAcJ,EAAAA,EAAAA,UAAiB,KACxCK,EAAcC,IAAmBN,EAAAA,EAAAA,WAAkB,IACnDO,EAAgBC,IAAqBR,EAAAA,EAAAA,WAAS,GAC/CS,GAAQC,EAAAA,EAAAA,QAAc,OACrBC,EAAUC,IAAeZ,EAAAA,EAAAA,UAAoB,KAC9C,SAAEa,EAAQ,QAAEC,IAAYC,EAAAA,EAAAA,MACvBC,EAAUC,IAAejB,EAAAA,EAAAA,UAAS,IAClCkB,EAASC,IAAcnB,EAAAA,EAAAA,UAAS,IAGvCoB,QAAQC,IAAI,YAAaL,EAAU,WAAYE,GAE/C,MAAMI,EAAqBX,EAASY,KAAIC,GAAKA,EAAE1B,iBAE/C2B,EAAAA,EAAAA,YAAU,KACRnB,IAAgBX,EAAU+B,YAE1BC,IAEAC,EAAAA,EACCC,QACAC,MAAK,KACJV,QAAQC,IAAI,uCAGbU,OAAMC,GAAOZ,QAAQa,MAAM,oCAAiBD,KAE7CJ,EAAAA,EAAWM,GAAG,kBAAmBC,IAC/BlB,EAAYkB,MAGdP,EAAAA,EAAWM,GAAG,kBAAmBE,IAC/BjB,EAAWiB,MAGN,KACLR,EAAAA,EAAWS,UAGZ,IAEH,MAAMV,EAAeW,UAEnB,IACE,MAAMC,QAAYC,EAAAA,EAAIC,IAAI,gBAAiB,CACzCC,OAAQ,CACNC,GAAIhD,EAAUiD,MAGlBhC,EAAY2B,EAAIM,MACZN,EAAIM,KAAKC,OAAS,IACpB1C,EAAWmC,EAAIM,KAAK,GAAG1C,SACvBK,GAAkB,GAEtB,CAAE,MAAOwB,GAAM,IAADe,EACC,QAAbA,EAAAtC,EAAMuC,eAAO,IAAAD,GAAbA,EAAeE,KAAK,CAAEC,SAAU,QAASC,QAAS,eAAMC,OAAQ,oDAClE,GAIIC,EAAQA,IAILC,KAAKC,MAAmB,IAAbD,KAAKE,UAHX,IAuGRC,EAAcnB,UACO,IAADoB,EAAxB,GAAsB,GAAnB/C,EAASmC,OAKZ,IAAK,IAADa,EACF,MAAMC,QAAiBpB,EAAAA,EAAIC,IAAI,iCAAkC,CAC/DC,OAAQ,CACNmB,YAAalE,EAAUiD,GACvBzC,QAASA,EACT2D,aAAclC,EAAAA,EAAWkC,cAE3BC,aAAc,SAGH,QAAbJ,EAAAlD,EAAMuC,eAAO,IAAAW,GAAbA,EAAeV,KAAK,CAAEC,SAAU,UAAWC,QAAS,eAAMC,OAAQ,yCAGlE,MAAMY,EAAO,IAAIC,KAAK,CAACL,EAASf,MAAO,CAAEqB,KAAM,oBACzCC,EAAUC,IAAIC,gBAAgBL,GACpC1D,GAAgB,GAEhBgE,OAAOC,KAAKJ,EACd,CACA,MAAOlC,GAAY,IAADuC,EACD,QAAbA,EAAA/D,EAAMuC,eAAO,IAAAwB,GAAbA,EAAevB,KAAK,CAAEC,SAAU,QAASC,QAAS,eAAMC,OAAQnB,EAAMf,SAC1E,MAzBe,QAAbwC,EAAAjD,EAAMuC,eAAO,IAAAU,GAAbA,EAAeT,KAAK,CAAEC,SAAU,QAASC,QAAS,eAAMC,OAAQ,sDAkDpE,GAAItC,EAAS,OAAO2D,EAAAA,EAAAA,KAAA,KAAAC,SAAG,eAEvB,MAAMC,GACAC,EAAAA,EAAAA,MAAA,OAAKC,UAAU,8BAA6BH,SAAA,EAC1CD,EAAAA,EAAAA,KAAA,OAAKI,UAAU,iBAAgBH,UAC7BE,EAAAA,EAAAA,MAAA,OAAKC,UAAU,YAAWH,SAAA,EACxBD,EAAAA,EAAAA,KAAA,SAAOI,UAAU,uBAAsBH,SAAC,8BACxCD,EAAAA,EAAAA,KAACK,EAAAA,EAAQ,CACP3C,MAAOrC,EACPiF,QA9JQC,KAAqB,IAADC,EACxC,OAAwD,QAAjDA,EAAApE,EAASqE,MAAKC,GAASA,EAAMH,UAAYA,WAAQ,IAAAC,OAAA,EAAjDA,EAAmDG,UAAU7D,KAAI8D,IAAI,CAC1EC,MAAOD,EAAKE,KACZpD,MAAOkD,EAAKE,WACP,IA0JcC,CAAW,GAAGC,QAAOC,IAAMpE,EAAmBqE,SAASD,EAAEvD,SAClEyD,SAAWC,GAAM9F,EAAiB8F,EAAE1D,OACpC2D,YAAY,6BAIlBrB,EAAAA,EAAAA,KAAA,OAAKI,UAAU,iBAAgBH,UAC7BE,EAAAA,EAAAA,MAAA,OAAKC,UAAU,YAAWH,SAAA,EACxBD,EAAAA,EAAAA,KAAA,SAAOsB,QAAQ,OAAOlB,UAAU,uBAAsBH,SAAC,kBACvDD,EAAAA,EAAAA,KAACuB,EAAAA,EAAW,CACV7D,MAAOlC,EACPgG,cAAgBJ,GAAM3F,EAAkBgG,OAAOL,EAAEM,OAAOhE,gBAK9DyC,EAAAA,EAAAA,MAAA,OAAKC,UAAU,kCAAiCH,SAAA,EAC9CD,EAAAA,EAAAA,KAAA,OAAKI,UAAU,sBAAqBH,UAClCE,EAAAA,EAAAA,MAAA,OAAKC,UAAU,YAAWH,SAAA,EACxBD,EAAAA,EAAAA,KAAA,SAAOI,UAAU,uBAAsBH,SAAC,OACxCD,EAAAA,EAAAA,KAAC2B,EAAAA,EAAM,CAACd,MAAM,eAAKe,KAAK,aAAaC,QA3KpCC,KACb,IAAKzG,IAAkBG,EAAgB,OAAOuG,MAAM,8CACpD,GAAI7F,EAASmC,QAAU,EAAG,OAAO0D,MAAM,6DACvC,GAAIlF,EAAmBqE,SAAS7F,GAAgB,OAAO0G,MAAM,oDAE7D,MAAMC,EAAkB,CACtB7D,GAAIS,IACJvD,gBACAG,iBACAyG,YAAa/G,EAAUiD,GACvB+D,UAAWhH,EAAUgH,WAEvB/F,EAAY,IAAID,EAAU8F,IAC1B1G,EAAiB,IACjBG,EAAkB,IA6JgD0G,SAAUjG,EAASmC,QAAU,UAGvF2B,EAAAA,EAAAA,KAAA,OAAKI,UAAU,sBAAqBH,UAClCE,EAAAA,EAAAA,MAAA,OAAKC,UAAU,YAAWH,SAAA,EACxBD,EAAAA,EAAAA,KAAA,SAAOI,UAAU,uBAAsBH,SAAC,OACxCD,EAAAA,EAAAA,KAAC2B,EAAAA,EAAM,CAACd,MAAM,eAAKe,KAAK,aAAanD,SAAS,OAAOoD,QAhK1CO,MACvBC,EAAAA,EAAAA,GAAc,CACZ5F,QAAS,qEACT6F,OAAQ,2BACRV,KAAM,6BACNW,OAAQ1E,UACN,IACE,MAAMsB,QAAiBpB,EAAAA,EAAIC,IAAI,gCAADwE,OAAiCtH,EAAUgH,WAAa,CACpFO,OAAQ,MACRC,QAAS,CACP,eAAgB,sBAIpB,GAAIvD,EAAU,CAAC,IAADwD,EACZ,MAAMC,QAAsBzD,EAASf,KAErCjC,EAAY,IAEZ,MAAM0G,EAAgBD,EAAc9F,KAAKgG,IAAeC,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACnDD,GAAM,IACT3E,GAAIS,IACJqD,YAAa/G,EAAUiD,GACvBzC,aAASsH,MAEX7G,EAAY0G,GACC,QAAbF,EAAA3G,EAAMuC,eAAO,IAAAoE,GAAbA,EAAenE,KAAK,CAAEC,SAAU,UAAWC,QAAS,2BAAQC,OAAQ,gEACtE,CACF,CAAE,MAAOnB,GAAY,IAADyF,EACL,QAAbA,EAAAjH,EAAMuC,eAAO,IAAA0E,GAAbA,EAAezE,KAAK,CAAEC,SAAU,QAASC,QAAS,2BAAQC,OAAQnB,EAAM0F,SAC1E,MAkIwFf,SAAUrG,UAG9FkE,EAAAA,EAAAA,KAAA,OAAKI,UAAU,sBAAqBH,UAClCE,EAAAA,EAAAA,MAAA,OAAKC,UAAU,YAAWH,SAAA,EACxBD,EAAAA,EAAAA,KAAA,SAAOI,UAAU,uBAAsBH,SAAC,OACxCD,EAAAA,EAAAA,KAAC2B,EAAAA,EAAM,CAACd,MAAM,eAAKe,KAAK,aAAanD,SAAS,UAAUoD,QAzHjDhE,UACM,IAADsF,EAAxB,GAAsB,GAAnBjH,EAASmC,OAKZ,IACE,GAAK3C,EAME,CAAC,IAAD0H,EAEL,MAAMC,EAAUnH,EAASY,KAAIC,IAACgG,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACzBhG,GAAC,IACJrB,oBAEIqC,EAAAA,EAAIuF,IAAI,sBAAuBD,GACxB,QAAbD,EAAApH,EAAMuC,eAAO,IAAA6E,GAAbA,EAAe5E,KAAK,CAAEC,SAAU,UAAWC,QAAS,eAAMC,OAAQ,4BACpE,KAdc,CAAC,IAAD4E,EAEZ,MAAMzF,QAAYC,EAAAA,EAAIyF,KAAK,sBAAuBtH,GACrC,QAAbqH,EAAAvH,EAAMuC,eAAO,IAAAgF,GAAbA,EAAe/E,KAAK,CAAEC,SAAU,UAAWC,QAAS,eAAMC,OAAQb,EAAIM,KAAKT,MAC3EhC,EAAWmC,EAAIM,KAAK1C,SACpBK,GAAkB,EACpB,CASF,CAAE,MAAOwB,GAAW,IAADkG,EAAAC,EACjB,MAAM/E,GAAY,OAAHpB,QAAG,IAAHA,GAAa,QAAVkG,EAAHlG,EAAK4B,gBAAQ,IAAAsE,OAAV,EAAHA,EAAerF,OAAQ,2BACzB,QAAbsF,EAAA1H,EAAMuC,eAAO,IAAAmF,GAAbA,EAAelF,KAAK,CAAEC,SAAU,QAASC,QAAS,eAAMC,UAC1D,MAvBe,QAAbwE,EAAAnH,EAAMuC,eAAO,IAAA4E,GAAbA,EAAe3E,KAAK,CAAEC,SAAU,QAASC,QAAS,eAAMC,OAAQ,6DA0HxD/C,IAAiBoE,EAAAA,EAAAA,KAAA,OAAKI,UAAU,sBAAqBH,UACrDE,EAAAA,EAAAA,MAAA,OAAKC,UAAU,YAAWH,SAAA,EACxBD,EAAAA,EAAAA,KAAA,SAAOI,UAAU,uBAAsBH,SAAC,OACxCD,EAAAA,EAAAA,KAAC2B,EAAAA,EAAM,CAACd,MAAM,2BAAOe,KAAK,iBAAiBnD,SAAS,YAAYoD,QAAS7C,UAG1EpD,IAAiBoE,EAAAA,EAAAA,KAAA,OAAKI,UAAU,sBAAqBH,UACtDE,EAAAA,EAAAA,MAAA,OAAKC,UAAU,YAAWH,SAAA,EACxBD,EAAAA,EAAAA,KAAA,SAAOI,UAAU,uBAAsBH,SAAC,OACxCD,EAAAA,EAAAA,KAAC2B,EAAAA,EAAM,CAACd,MAAM,2BAAOe,KAAK,iBAAiBnD,SAAS,YAAYoD,QAjE9D8B,MACZtB,EAAAA,EAAAA,GAAc,CACV5F,QAAS,uIACT6F,OAAQ,uCACRV,KAAM,6BACNgC,aAAc,SACdC,YAAa,eACbC,YAAa,eACbvB,OAAQA,IAAMvD,eA4DZgB,EAAAA,EAAAA,KAAA,OAAKI,UAAU,sBAAqBH,UAClCE,EAAAA,EAAAA,MAAA,OAAKC,UAAU,YAAWH,SAAA,EACxBD,EAAAA,EAAAA,KAAA,SAAOI,UAAU,uBAAsBH,SAAC,0CACxCD,EAAAA,EAAAA,KAAA,YAAUtC,MAAOnB,EAAUwH,IAAI,MAAMC,MAAO,CAAEC,MAAO,qBAOrE,OACI9D,EAAAA,EAAAA,MAAA,OAAKC,UAAU,OAAMH,SAAA,EACnBD,EAAAA,EAAAA,KAACkE,EAAAA,EAAK,CAACC,IAAKnI,KACZgE,EAAAA,EAAAA,KAACoE,EAAAA,EAAa,IACblE,GACDC,EAAAA,EAAAA,MAACkE,EAAAA,EAAS,CAAC3G,MAAOxB,EAAUoI,QAAQ,KAAIrE,SAAA,EACtCD,EAAAA,EAAAA,KAACuE,EAAAA,EAAM,CAACC,MAAM,gBAAgBlC,OAAO,kBACrCtC,EAAAA,EAAAA,KAACuE,EAAAA,EAAM,CAACnI,SAAS,UAAUoI,MAAM,iBAAiBlC,OAAO,kBACzDtC,EAAAA,EAAAA,KAACuE,EAAAA,EAAM,CAACE,KAhGcC,IAExB1E,EAAAA,EAAAA,KAAC2B,EAAAA,EAAM,CACLC,KAAK,cACLxB,UAAU,kBACVyB,QAASA,IAzEI6C,KACjB,GAAsB,GAAnBxI,EAASmC,OAAY,CAAC,IAADsG,EACT,QAAbA,EAAA3I,EAAMuC,eAAO,IAAAoG,GAAbA,EAAenG,KAAK,CAAEC,SAAU,QAASC,QAAS,eAAMC,OAAQ,oDAClE,KAAK,CACH,MAAM0E,EAAUnH,EAAS8E,QAAQjE,GAAMA,EAAEoB,KAAOuG,EAAQvG,KACxDhC,EAAYkH,EACd,GAmEmBuB,CAAUF,KA2FSpC,OAAO,uB,yDC3SlC,SAAShG,IACtB,MAAOF,EAAUyI,IAAetJ,EAAAA,EAAAA,UAA8B,KACvDc,EAASyI,IAAcvJ,EAAAA,EAAAA,WAAS,GAUvC,OARAyB,EAAAA,EAAAA,YAAU,KAERe,EAAAA,EAAIC,IAAyB,2BAC1BX,MAAKS,GAAO+G,EAAY/G,EAAIM,QAC5Bd,OAAMC,GAAOZ,QAAQa,MAAM,aAAcD,KACzCwH,SAAQ,IAAMD,GAAW,OACzB,IAEE,CAAE1I,WAAUC,UACrB,C,kCC9BA,MAKA,GALmB,I,QAAI2I,IACpBC,QAAQC,wCACRC,yBACAC,O", "sources": ["components/Page/ReceiptsDetailPage.tsx", "hooks/useDataType.ts", "services/signalr.ts"], "sourcesContent": ["import { But<PERSON> } from 'primereact/button';\r\nimport { Column } from 'primereact/column';\r\nimport { ConfirmDialog, confirmDialog } from 'primereact/confirmdialog';\r\nimport { DataTable } from 'primereact/datatable';\r\nimport { Dropdown } from 'primereact/dropdown';\r\nimport { InputNumber } from 'primereact/inputnumber';\r\nimport { Toast } from 'primereact/toast';\r\nimport React, { useEffect, useRef, useState } from 'react';\r\nimport { useLocation } from \"react-router-dom\";\r\nimport api from \"../../services/api\";\r\nimport connection from \"../../services/signalr\";\r\nimport useDataType from \"../../hooks/useDataType\";\r\n\r\ninterface Receipt {\r\n  id?: number;\r\n  treatmentItem: string;\r\n  treatmentMoney: number;\r\n  treatmentId: number;\r\n  patientId: number;\r\n  orderNo?: string;\r\n}\r\n\r\nconst ReceiptsDetailPage: React.FC = () => {\r\n  const location = useLocation();\r\n  const treatment = location.state?.treatment;\r\n  \r\n  const [treatmentItem, setTreatmentItem] = useState('');\r\n  const [treatmentMoney, setTreatmentMoney] = useState<number>(0);\r\n  const [orderNo, setorderNo] = useState<string>('');\r\n  const [isFileCreate, setisFileCreate] = useState<boolean>(false);\r\n  const [DisableCopyBtn, setDisableCopyBtn] = useState(false);\r\n  const toast = useRef<Toast>(null);\r\n  const [receipts, setReceipts] = useState<Receipt[]>([]);\r\n  const { dataType, loading } = useDataType();\r\n  const [progress, setProgress] = useState(0);\r\n  const [message, setMessage] = useState(\"\");\r\n\r\n  // Use message to avoid unused variable warning\r\n  console.log('Progress:', progress, 'Message:', message);\r\n\r\n  const treatmentItemsUsed = receipts.map(r => r.treatmentItem);\r\n\r\n  useEffect(() => {\r\n    setisFileCreate(treatment.receiptUrl? true : false )\r\n\r\n    loadReceipts();\r\n\r\n    connection\r\n    .start()\r\n    .then(() => {\r\n      console.log(\"已連線至 SignalR\");\r\n      //console.log(\"連線 ID\", connection.connectionId);\r\n    })\r\n    .catch(err => console.error(\"SignalR 連線失敗:\", err));\r\n\r\n    connection.on(\"ReportProgress\", (value) => {\r\n      setProgress(value);\r\n    });\r\n\r\n    connection.on(\"ReportFinished\", (msg) => {\r\n      setMessage(msg);\r\n    });\r\n\r\n    return () => {\r\n      connection.stop();\r\n    };\r\n\r\n  }, []);\r\n\r\n  const loadReceipts = async () => {\r\n\r\n    try {\r\n      const res = await api.get('/api/receipt/', {\r\n        params: {\r\n          Id: treatment.id\r\n        }\r\n      });\r\n      setReceipts(res.data);\r\n      if (res.data.length > 0) {\r\n        setorderNo(res.data[0].orderNo); // 綁定 orderNo\r\n        setDisableCopyBtn(true);\r\n      }\r\n    } catch (err) {\r\n      toast.current?.show({ severity: 'error', summary: '錯誤', detail: '讀取收據資料失敗' });\r\n    }\r\n  };\r\n\r\n\r\n  const getNo = () => {\r\n    const min = 10000; \r\n    const max = 99999; \r\n\r\n    return Math.floor(Math.random() * (max - min + 1)) + min;\r\n  };\r\n\r\n    const getOptions = (groupId: number) => {\r\n    return dataType.find(group => group.groupId === groupId)?.dataTypes.map(item => ({\r\n      label: item.name,\r\n      value: item.name\r\n    })) || [];\r\n  };\r\n\r\n  const addRow = () => {\r\n    if (!treatmentItem || !treatmentMoney) return alert('請填寫完整欄位');\r\n    if (receipts.length >= 4) return alert('最多只能新增 4 筆資料');\r\n    if (treatmentItemsUsed.includes(treatmentItem)) return alert('項目名稱不可重複');\r\n\r\n    const newRow: Receipt = {\r\n      id: getNo(),\r\n      treatmentItem,\r\n      treatmentMoney,\r\n      treatmentId: treatment.id,\r\n      patientId: treatment.patientId,\r\n    };\r\n    setReceipts([...receipts, newRow]);\r\n    setTreatmentItem('');\r\n    setTreatmentMoney(0);\r\n  };\r\n\r\n  const copyLatestRecord = () => {\r\n    confirmDialog({\r\n      message: '是否複製上一筆收據紀錄',\r\n      header: '複製確認',\r\n      icon: 'pi pi-exclamation-triangle',\r\n      accept: async () => {\r\n        try {\r\n          const response = await api.get(`/api/Receipt/GetLatestRecord/${treatment.patientId}`, {\r\n            method: 'GET',\r\n            headers: {\r\n              'Content-Type': 'application/json',\r\n            },\r\n          });\r\n\r\n          if (response) {\r\n            const latestRecords = await response.data;\r\n            // 清空現有的收據記錄\r\n            setReceipts([]);\r\n            // 複製上一筆記錄，但生成新的 ID\r\n            const copiedRecords = latestRecords.map((record: Receipt) => ({\r\n              ...record,\r\n              id: getNo(),\r\n              treatmentId: treatment.id,\r\n              orderNo: undefined // 清空 orderNo，讓它成為新記錄\r\n            }));\r\n            setReceipts(copiedRecords);\r\n            toast.current?.show({ severity: \"success\", summary: \"複製成功\", detail: \"已複製上一筆收據紀錄\" });\r\n          } \r\n        } catch (error:any) {\r\n          toast.current?.show({ severity: \"error\", summary: \"複製失敗\", detail: error.details });\r\n        }\r\n      }\r\n    });\r\n  };\r\n\r\n  const deleteRow = (rowData: Receipt) => {\r\n    if(receipts.length == 1){\r\n      toast.current?.show({ severity: 'error', summary: '錯誤', detail: \"資料請勿少於一筆\" });\r\n    }else{\r\n      const updated = receipts.filter((r) => r.id !== rowData.id);\r\n      setReceipts(updated);\r\n    }\r\n    \r\n  };\r\n\r\n  const saveToServer = async () => {\r\n    if(receipts.length == 0){\r\n      toast.current?.show({ severity: 'error', summary: '錯誤', detail: \"資料請勿少於一筆\" });\r\n      return \r\n    }\r\n\r\n    try {\r\n      if (!orderNo) {\r\n        // 新增模式\r\n        const res = await api.post('/api/receipt/Insert', receipts);\r\n        toast.current?.show({ severity: 'success', summary: '成功', detail: res.data.msg });\r\n        setorderNo(res.data.orderNo); // 取得新的 orderNo\r\n        setDisableCopyBtn(true);\r\n      } else {\r\n        // 更新模式\r\n        const updated = receipts.map(r => ({\r\n          ...r,\r\n          orderNo\r\n        }));\r\n        await api.put('/api/receipt/Update', updated);\r\n        toast.current?.show({ severity: 'success', summary: '成功', detail: '更新完成' });\r\n      }\r\n    } catch (err: any) {\r\n      const detail = err?.response?.data || '儲存失敗';\r\n      toast.current?.show({ severity: 'error', summary: '錯誤', detail });\r\n    }\r\n  };\r\n\r\n  const exportToPDF = async () => {\r\n    if(receipts.length == 0){\r\n      toast.current?.show({ severity: 'error', summary: '錯誤', detail: \"資料請勿少於一筆\" });\r\n      return \r\n    }\r\n\r\n    try {\r\n      const response = await api.get(\"/api/receipt/ExportReceiptsPdf\", {\r\n        params: { \r\n          TreatmentId: treatment.id,\r\n          orderNo: orderNo,\r\n          connectionId: connection.connectionId\r\n        },\r\n        responseType: 'blob'  // blob 格式取得資料\r\n      });\r\n      \r\n      toast.current?.show({ severity: \"success\", summary: \"成功\", detail: \"收據製作成功\"});\r\n\r\n      // 產生blob url\r\n      const file = new Blob([response.data], { type: 'application/pdf' });\r\n      const fileURL = URL.createObjectURL(file);\r\n      setisFileCreate(true)\r\n      // 在新分頁開啟PDF\r\n      window.open(fileURL);\r\n    } \r\n    catch (error:any) {\r\n        toast.current?.show({ severity: \"error\", summary: \"錯誤\", detail: error.message });\r\n    }\r\n  };\r\n\r\n  const actionBodyTemplate = (rowData: Receipt) => {\r\n    return (\r\n      <Button\r\n        icon=\"pi pi-trash\"\r\n        className=\"p-button-danger\"\r\n        onClick={() => deleteRow(rowData)}\r\n      />\r\n    );\r\n  };\r\n\r\n  const confirm = () => {\r\n      confirmDialog({\r\n          message: '收據開立後就無法修改內容，確定要開立收據嗎？',\r\n          header: '收據開立確認',\r\n          icon: 'pi pi-exclamation-triangle',\r\n          defaultFocus: 'accept',\r\n          acceptLabel: '確定',\r\n          rejectLabel: '取消',\r\n          accept: () => exportToPDF(),\r\n      });\r\n  };\r\n\r\n  if (loading) return <p>Loading...</p>;\r\n\r\n  const toolbar = (\r\n        <div className=\"card flex flex-wrap p-fluid\">\r\n          <div className=\"col-6 md:col-2\">\r\n            <div className=\"flex-auto\">\r\n              <label className=\"font-bold block mb-2\">治療項目</label>\r\n              <Dropdown\r\n                value={treatmentItem}\r\n                options={getOptions(9).filter(o => !treatmentItemsUsed.includes(o.value))}\r\n                onChange={(e) => setTreatmentItem(e.value)}\r\n                placeholder=\"請選擇\"\r\n              />\r\n            </div>\r\n          </div>\r\n          <div className=\"col-5 md:col-2\">\r\n            <div className=\"flex-auto\">\r\n              <label htmlFor=\"mile\" className=\"font-bold block mb-2\">金額</label>\r\n              <InputNumber \r\n                value={treatmentMoney}\r\n                onValueChange={(e) => setTreatmentMoney(Number(e.target.value))}\r\n                \r\n              />\r\n            </div>\r\n          </div>\r\n          <div className=\" flex flex-wrap col-11 md:col-5\">\r\n            <div className=\"flex col-6 md:col-2\">\r\n              <div className=\"flex-auto\">\r\n                <label className=\"font-bold block mb-2\"> </label>\r\n                <Button label=\"新增\" icon=\"pi pi-plus\" onClick={addRow} disabled={receipts.length >= 5} />\r\n              </div>\r\n            </div>\r\n            <div className=\"flex col-5 md:col-2\">\r\n              <div className=\"flex-auto\">\r\n                <label className=\"font-bold block mb-2\"> </label>\r\n                <Button label=\"複製\" icon=\"pi pi-copy\" severity=\"info\" onClick={copyLatestRecord} disabled={DisableCopyBtn} />\r\n              </div>\r\n            </div>\r\n            <div className=\"flex col-6 md:col-2\">\r\n              <div className=\"flex-auto\">\r\n                <label className=\"font-bold block mb-2\"> </label>\r\n                <Button label=\"儲存\" icon=\"pi pi-save\" severity=\"success\" onClick={saveToServer} />\r\n              </div>\r\n            </div>\r\n            { isFileCreate && (<div className=\"flex col-6 md:col-3\">\r\n              <div className=\"flex-auto\">\r\n                <label className=\"font-bold block mb-2\"> </label>\r\n                <Button label=\"檢視收據\" icon=\"pi pi-file-pdf\" severity=\"secondary\" onClick={exportToPDF} /> \r\n              </div>\r\n            </div>)}\r\n            { !isFileCreate && (<div className=\"flex col-5 md:col-3\">\r\n              <div className=\"flex-auto\">\r\n                <label className=\"font-bold block mb-2\"> </label>\r\n                <Button label=\"開立收據\" icon=\"pi pi-file-pdf\" severity=\"secondary\" onClick={confirm} /> \r\n              </div>\r\n            </div>) }\r\n            <div className=\"flex col-5 md:col-2\">\r\n              <div className=\"flex-auto\">\r\n                <label className=\"font-bold block mb-2\">報表產生進度</label>\r\n                <progress value={progress} max=\"100\" style={{ width: '100%' }} />\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n  );\r\n\r\nreturn (\r\n    <div className=\"card\">\r\n      <Toast ref={toast} />\r\n      <ConfirmDialog />\r\n      {toolbar}\r\n      <DataTable value={receipts} dataKey=\"id\">\r\n        <Column field=\"treatmentItem\" header=\"項目\" />\r\n        <Column dataType=\"numeric\" field=\"treatmentMoney\" header=\"金額\" />\r\n        <Column body={actionBodyTemplate} header=\"刪除\" />\r\n      </DataTable>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default ReceiptsDetailPage;", "import { useEffect, useState } from \"react\";\r\nimport api from '../services/api';\r\n\r\n// 定義 MenuItem 中每筆物件的型別\r\ninterface DataTypeItem {\r\n  itemId: number;\r\n  number: string;\r\n  name: string;\r\n  isEnabled: boolean;\r\n}\r\n\r\n// 定義 MenuGroupItem 中每筆物件型別\r\ninterface DataTypeGroupItem {\r\n  groupId: number;\r\n  groupName: string;\r\n  isEnabled: boolean;\r\n  dataTypes: DataTypeItem[];\r\n}\r\n\r\nexport default function useDataType() {\r\n  const [dataType, setdataType] = useState<DataTypeGroupItem[]>([]);\r\n  const [loading, setLoading] = useState(true);\r\n\r\n  useEffect(() => {\r\n\r\n    api.get<DataTypeGroupItem[]>(\"/api/system/GetDataType\")\r\n      .then(res => setdataType(res.data))\r\n      .catch(err => console.error(\"API Error:\", err))\r\n      .finally(() => setLoading(false));\r\n    }, []);\r\n\r\n  return { dataType, loading };\r\n}", "import * as signalR from \"@microsoft/signalr\";\r\n\r\nconst connection = new signalR.HubConnectionBuilder()\r\n  .withUrl(process.env.REACT_APP_API_URL + \"/reportHub\")\r\n  .withAutomaticReconnect()\r\n  .build();\r\n\r\nexport default connection;"], "names": ["ReceiptsDetailPage", "_location$state", "treatment", "useLocation", "state", "treatmentItem", "setTreatmentItem", "useState", "treatmentMoney", "setTreatmentMoney", "orderNo", "setorderNo", "isFileCreate", "setisFileCreate", "DisableCopyBtn", "setDisableCopyBtn", "toast", "useRef", "receipts", "setReceipts", "dataType", "loading", "useDataType", "progress", "setProgress", "message", "setMessage", "console", "log", "treatmentItemsUsed", "map", "r", "useEffect", "receiptUrl", "loadReceipts", "connection", "start", "then", "catch", "err", "error", "on", "value", "msg", "stop", "async", "res", "api", "get", "params", "Id", "id", "data", "length", "_toast$current", "current", "show", "severity", "summary", "detail", "getNo", "Math", "floor", "random", "exportToPDF", "_toast$current9", "_toast$current0", "response", "TreatmentId", "connectionId", "responseType", "file", "Blob", "type", "fileURL", "URL", "createObjectURL", "window", "open", "_toast$current1", "_jsx", "children", "toolbar", "_jsxs", "className", "Dropdown", "options", "groupId", "_dataType$find", "find", "group", "dataTypes", "item", "label", "name", "getOptions", "filter", "o", "includes", "onChange", "e", "placeholder", "htmlFor", "InputNumber", "onValueChange", "Number", "target", "<PERSON><PERSON>", "icon", "onClick", "addRow", "alert", "newRow", "treatmentId", "patientId", "disabled", "copyLatestRecord", "confirmDialog", "header", "accept", "concat", "method", "headers", "_toast$current2", "latestRecords", "copiedRecords", "record", "_objectSpread", "undefined", "_toast$current3", "details", "_toast$current5", "_toast$current7", "updated", "put", "_toast$current6", "post", "_err$response", "_toast$current8", "confirm", "defaultFocus", "acceptLabel", "<PERSON><PERSON><PERSON><PERSON>", "max", "style", "width", "Toast", "ref", "ConfirmDialog", "DataTable", "dataKey", "Column", "field", "body", "rowData", "_toast$current4", "deleteRow", "setdataType", "setLoading", "finally", "signalR", "withUrl", "process", "withAutomaticReconnect", "build"], "sourceRoot": ""}
{"ast": null, "code": "import{useState,useEffect,useCallback}from'react';import{PermissionApi}from'../services/apiService';// 導入新的 PermissionApi\nimport{log}from'../utils/logger';export const usePermissions=()=>{const[permissions,setPermissions]=useState([]);// 權限現在是一個字串陣列\nconst[loading,setLoading]=useState(true);const fetchPermissions=useCallback(async()=>{setLoading(true);try{const userPermissions=await PermissionApi.getCurrentUserPermissions();setPermissions(userPermissions||[]);log.info('Permissions loaded',userPermissions);}catch(error){log.error(\"Failed to fetch permissions\",error);setPermissions([]);}finally{setLoading(false);}},[]);useEffect(()=>{fetchPermissions();},[fetchPermissions]);// 檢查用戶是否擁有特定權限\nconst hasPermission=useCallback(permissionCode=>{if(loading){return false;// 載入中不允許訪問\n}return permissions.includes(permissionCode);},[permissions,loading]);return{permissions,hasPermission,loading,refreshPermissions:fetchPermissions};};", "map": {"version": 3, "names": ["useState", "useEffect", "useCallback", "PermissionApi", "log", "usePermissions", "permissions", "setPermissions", "loading", "setLoading", "fetchPermissions", "userPermissions", "getCurrentUserPermissions", "info", "error", "hasPermission", "permissionCode", "includes", "refreshPermissions"], "sources": ["C:/Users/<USER>/Desktop/demo-git/demo-react/my-fullstack-app/frontend/src/hooks/usePermissions.ts"], "sourcesContent": ["import { useState, useEffect, useCallback } from 'react';\nimport { PermissionApi } from '../services/apiService'; // 導入新的 PermissionApi\nimport { log } from '../utils/logger';\n\nexport const usePermissions = () => {\n    const [permissions, setPermissions] = useState<string[]>([]); // 權限現在是一個字串陣列\n    const [loading, setLoading] = useState(true);\n\n    const fetchPermissions = useCallback(async () => {\n        setLoading(true);\n        try {\n            const userPermissions = await PermissionApi.getCurrentUserPermissions();\n            setPermissions(userPermissions || []);\n            log.info('Permissions loaded', userPermissions);\n        } catch (error: any) {\n            log.error(\"Failed to fetch permissions\", error);\n            setPermissions([]);\n        } finally {\n            setLoading(false);\n        }\n    }, []);\n\n    useEffect(() => {\n        fetchPermissions();\n    }, [fetchPermissions]);\n\n    // 檢查用戶是否擁有特定權限\n    const hasPermission = useCallback(\n        (permissionCode: string): boolean => {\n            if (loading) {\n                return false; // 載入中不允許訪問\n            }\n            return permissions.includes(permissionCode);\n        },\n        [permissions, loading]\n    );\n\n    return { permissions, hasPermission, loading, refreshPermissions: fetchPermissions };\n};\n"], "mappings": "AAAA,OAASA,QAAQ,CAAEC,SAAS,CAAEC,WAAW,KAAQ,OAAO,CACxD,OAASC,aAAa,KAAQ,wBAAwB,CAAE;AACxD,OAASC,GAAG,KAAQ,iBAAiB,CAErC,MAAO,MAAM,CAAAC,cAAc,CAAGA,CAAA,GAAM,CAChC,KAAM,CAACC,WAAW,CAAEC,cAAc,CAAC,CAAGP,QAAQ,CAAW,EAAE,CAAC,CAAE;AAC9D,KAAM,CAACQ,OAAO,CAAEC,UAAU,CAAC,CAAGT,QAAQ,CAAC,IAAI,CAAC,CAE5C,KAAM,CAAAU,gBAAgB,CAAGR,WAAW,CAAC,SAAY,CAC7CO,UAAU,CAAC,IAAI,CAAC,CAChB,GAAI,CACA,KAAM,CAAAE,eAAe,CAAG,KAAM,CAAAR,aAAa,CAACS,yBAAyB,CAAC,CAAC,CACvEL,cAAc,CAACI,eAAe,EAAI,EAAE,CAAC,CACrCP,GAAG,CAACS,IAAI,CAAC,oBAAoB,CAAEF,eAAe,CAAC,CACnD,CAAE,MAAOG,KAAU,CAAE,CACjBV,GAAG,CAACU,KAAK,CAAC,6BAA6B,CAAEA,KAAK,CAAC,CAC/CP,cAAc,CAAC,EAAE,CAAC,CACtB,CAAC,OAAS,CACNE,UAAU,CAAC,KAAK,CAAC,CACrB,CACJ,CAAC,CAAE,EAAE,CAAC,CAENR,SAAS,CAAC,IAAM,CACZS,gBAAgB,CAAC,CAAC,CACtB,CAAC,CAAE,CAACA,gBAAgB,CAAC,CAAC,CAEtB;AACA,KAAM,CAAAK,aAAa,CAAGb,WAAW,CAC5Bc,cAAsB,EAAc,CACjC,GAAIR,OAAO,CAAE,CACT,MAAO,MAAK,CAAE;AAClB,CACA,MAAO,CAAAF,WAAW,CAACW,QAAQ,CAACD,cAAc,CAAC,CAC/C,CAAC,CACD,CAACV,WAAW,CAAEE,OAAO,CACzB,CAAC,CAED,MAAO,CAAEF,WAAW,CAAES,aAAa,CAAEP,OAAO,CAAEU,kBAAkB,CAAER,gBAAiB,CAAC,CACxF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
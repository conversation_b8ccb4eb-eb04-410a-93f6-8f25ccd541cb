{"ast": null, "code": "const formatRelativeLocale = {\n  lastWeek: \"'geçen hafta' eeee 'saat' p\",\n  yesterday: \"'dün saat' p\",\n  today: \"'bugün saat' p\",\n  tomorrow: \"'yarın saat' p\",\n  nextWeek: \"eeee 'saat' p\",\n  other: \"P\"\n};\nexport const formatRelative = (token, _date, _baseDate, _options) => formatRelativeLocale[token];", "map": {"version": 3, "names": ["formatRelativeLocale", "lastWeek", "yesterday", "today", "tomorrow", "nextWeek", "other", "formatRelative", "token", "_date", "_baseDate", "_options"], "sources": ["C:/Users/<USER>/Desktop/demo-git/demo-react/my-fullstack-app/frontend/node_modules/date-fns/locale/tr/_lib/formatRelative.js"], "sourcesContent": ["const formatRelativeLocale = {\n  lastWeek: \"'geçen hafta' eeee 'saat' p\",\n  yesterday: \"'dün saat' p\",\n  today: \"'bugün saat' p\",\n  tomorrow: \"'yarın saat' p\",\n  nextWeek: \"eeee 'saat' p\",\n  other: \"P\",\n};\n\nexport const formatRelative = (token, _date, _baseDate, _options) =>\n  formatRelativeLocale[token];\n"], "mappings": "AAAA,MAAMA,oBAAoB,GAAG;EAC3BC,QAAQ,EAAE,6BAA6B;EACvCC,SAAS,EAAE,cAAc;EACzBC,KAAK,EAAE,gBAAgB;EACvBC,QAAQ,EAAE,gBAAgB;EAC1BC,QAAQ,EAAE,eAAe;EACzBC,KAAK,EAAE;AACT,CAAC;AAED,OAAO,MAAMC,cAAc,GAAGA,CAACC,KAAK,EAAEC,KAAK,EAAEC,SAAS,EAAEC,QAAQ,KAC9DX,oBAAoB,CAACQ,KAAK,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
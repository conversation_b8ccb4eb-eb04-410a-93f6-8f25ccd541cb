/// <reference types="react-scripts" />

declare namespace NodeJS {
  interface ProcessEnv {
    readonly NODE_ENV: 'development' | 'production' | 'test';
    readonly PUBLIC_URL: string;
    readonly REACT_APP_API_URL: string;
    readonly REACT_APP_ENVIRONMENT: 'development' | 'production' | 'test';
    readonly REACT_APP_DEBUG: string;
    readonly REACT_APP_LOG_LEVEL: 'debug' | 'info' | 'warn' | 'error';
  }
}

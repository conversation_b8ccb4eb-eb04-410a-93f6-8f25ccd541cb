{"ast": null, "code": "import { buildLocalizeFn } from \"../../_lib/buildLocalizeFn.js\";\nconst eraValues = {\n  narrow: [\"ie.\", \"isz.\"],\n  abbreviated: [\"i. e.\", \"i. sz.\"],\n  wide: [\"<PERSON><PERSON><PERSON>\", \"id<PERSON><PERSON><PERSON>mításunk szerint\"]\n};\nconst quarterValues = {\n  narrow: [\"1.\", \"2.\", \"3.\", \"4.\"],\n  abbreviated: [\"1. n.év\", \"2. n.év\", \"3. n.év\", \"4. n.év\"],\n  wide: [\"1. negyedév\", \"2. negyedév\", \"3. negyedév\", \"4. negyedév\"]\n};\nconst formattingQuarterValues = {\n  narrow: [\"I.\", \"II.\", \"III.\", \"IV.\"],\n  abbreviated: [\"I. n.év\", \"II. n.év\", \"III. n.év\", \"IV. n.év\"],\n  wide: [\"I. negyedév\", \"II. negyedév\", \"III. negyedév\", \"IV. negyedév\"]\n};\nconst monthValues = {\n  narrow: [\"J\", \"F\", \"M\", \"Á\", \"M\", \"J\", \"J\", \"A\", \"Sz\", \"O\", \"N\", \"D\"],\n  abbreviated: [\"jan.\", \"febr.\", \"m<PERSON>rc.\", \"ápr.\", \"m<PERSON>j.\", \"jún.\", \"júl.\", \"aug.\", \"szept.\", \"okt.\", \"nov.\", \"dec.\"],\n  wide: [\"janu<PERSON>r\", \"febru<PERSON>r\", \"m<PERSON>rcius\", \"április\", \"május\", \"június\", \"július\", \"augusztus\", \"szeptember\", \"október\", \"november\", \"december\"]\n};\nconst dayValues = {\n  narrow: [\"V\", \"H\", \"K\", \"Sz\", \"Cs\", \"P\", \"Sz\"],\n  short: [\"V\", \"H\", \"K\", \"Sze\", \"Cs\", \"P\", \"Szo\"],\n  abbreviated: [\"V\", \"H\", \"K\", \"Sze\", \"Cs\", \"P\", \"Szo\"],\n  wide: [\"vasárnap\", \"hétfő\", \"kedd\", \"szerda\", \"csütörtök\", \"péntek\", \"szombat\"]\n};\nconst dayPeriodValues = {\n  narrow: {\n    am: \"de.\",\n    pm: \"du.\",\n    midnight: \"éjfél\",\n    noon: \"dél\",\n    morning: \"reggel\",\n    afternoon: \"du.\",\n    evening: \"este\",\n    night: \"éjjel\"\n  },\n  abbreviated: {\n    am: \"de.\",\n    pm: \"du.\",\n    midnight: \"éjfél\",\n    noon: \"dél\",\n    morning: \"reggel\",\n    afternoon: \"du.\",\n    evening: \"este\",\n    night: \"éjjel\"\n  },\n  wide: {\n    am: \"de.\",\n    pm: \"du.\",\n    midnight: \"éjfél\",\n    noon: \"dél\",\n    morning: \"reggel\",\n    afternoon: \"délután\",\n    evening: \"este\",\n    night: \"éjjel\"\n  }\n};\nconst ordinalNumber = (dirtyNumber, _options) => {\n  const number = Number(dirtyNumber);\n  return number + \".\";\n};\nexport const localize = {\n  ordinalNumber,\n  era: buildLocalizeFn({\n    values: eraValues,\n    defaultWidth: \"wide\"\n  }),\n  quarter: buildLocalizeFn({\n    values: quarterValues,\n    defaultWidth: \"wide\",\n    argumentCallback: quarter => quarter - 1,\n    formattingValues: formattingQuarterValues,\n    defaultFormattingWidth: \"wide\"\n  }),\n  month: buildLocalizeFn({\n    values: monthValues,\n    defaultWidth: \"wide\"\n  }),\n  day: buildLocalizeFn({\n    values: dayValues,\n    defaultWidth: \"wide\"\n  }),\n  dayPeriod: buildLocalizeFn({\n    values: dayPeriodValues,\n    defaultWidth: \"wide\"\n  })\n};", "map": {"version": 3, "names": ["buildLocalizeFn", "<PERSON><PERSON><PERSON><PERSON>", "narrow", "abbreviated", "wide", "quarterValues", "formattingQuarterValues", "month<PERSON><PERSON><PERSON>", "dayV<PERSON><PERSON>", "short", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "am", "pm", "midnight", "noon", "morning", "afternoon", "evening", "night", "ordinalNumber", "dirtyNumber", "_options", "number", "Number", "localize", "era", "values", "defaultWidth", "quarter", "argument<PERSON>allback", "formattingValues", "defaultFormattingWidth", "month", "day", "<PERSON><PERSON><PERSON><PERSON>"], "sources": ["C:/Users/<USER>/Desktop/demo-git/demo-react/my-fullstack-app/frontend/node_modules/date-fns/locale/hu/_lib/localize.js"], "sourcesContent": ["import { buildLocalizeFn } from \"../../_lib/buildLocalizeFn.js\";\n\nconst eraValues = {\n  narrow: [\"ie.\", \"isz.\"],\n  abbreviated: [\"i. e.\", \"i. sz.\"],\n  wide: [\"<PERSON><PERSON><PERSON>\", \"id<PERSON><PERSON><PERSON>mításunk szerint\"],\n};\n\nconst quarterValues = {\n  narrow: [\"1.\", \"2.\", \"3.\", \"4.\"],\n  abbreviated: [\"1. n.év\", \"2. n.év\", \"3. n.év\", \"4. n.év\"],\n  wide: [\"1. negyedév\", \"2. negyedév\", \"3. negyedév\", \"4. negyedév\"],\n};\n\nconst formattingQuarterValues = {\n  narrow: [\"I.\", \"II.\", \"III.\", \"IV.\"],\n  abbreviated: [\"I. n.év\", \"II. n.év\", \"III. n.év\", \"IV. n.év\"],\n  wide: [\"I. negyedév\", \"II. negyedév\", \"III. negyedév\", \"IV. negyedév\"],\n};\n\nconst monthValues = {\n  narrow: [\"J\", \"F\", \"M\", \"Á\", \"M\", \"J\", \"J\", \"A\", \"<PERSON>z\", \"<PERSON>\", \"N\", \"<PERSON>\"],\n\n  abbreviated: [\n    \"jan.\",\n    \"febr.\",\n    \"m<PERSON>rc.\",\n    \"ápr.\",\n    \"m<PERSON>j.\",\n    \"jún.\",\n    \"júl.\",\n    \"aug.\",\n    \"szept.\",\n    \"okt.\",\n    \"nov.\",\n    \"dec.\",\n  ],\n\n  wide: [\n    \"janu<PERSON>r\",\n    \"febru<PERSON>r\",\n    \"március\",\n    \"április\",\n    \"május\",\n    \"június\",\n    \"július\",\n    \"augusztus\",\n    \"szeptember\",\n    \"október\",\n    \"november\",\n    \"december\",\n  ],\n};\n\nconst dayValues = {\n  narrow: [\"V\", \"H\", \"K\", \"Sz\", \"Cs\", \"P\", \"Sz\"],\n  short: [\"V\", \"H\", \"K\", \"Sze\", \"Cs\", \"P\", \"Szo\"],\n  abbreviated: [\"V\", \"H\", \"K\", \"Sze\", \"Cs\", \"P\", \"Szo\"],\n  wide: [\n    \"vasárnap\",\n    \"hétfő\",\n    \"kedd\",\n    \"szerda\",\n    \"csütörtök\",\n    \"péntek\",\n    \"szombat\",\n  ],\n};\n\nconst dayPeriodValues = {\n  narrow: {\n    am: \"de.\",\n    pm: \"du.\",\n    midnight: \"éjfél\",\n    noon: \"dél\",\n    morning: \"reggel\",\n    afternoon: \"du.\",\n    evening: \"este\",\n    night: \"éjjel\",\n  },\n  abbreviated: {\n    am: \"de.\",\n    pm: \"du.\",\n    midnight: \"éjfél\",\n    noon: \"dél\",\n    morning: \"reggel\",\n    afternoon: \"du.\",\n    evening: \"este\",\n    night: \"éjjel\",\n  },\n  wide: {\n    am: \"de.\",\n    pm: \"du.\",\n    midnight: \"éjfél\",\n    noon: \"dél\",\n    morning: \"reggel\",\n    afternoon: \"délután\",\n    evening: \"este\",\n    night: \"éjjel\",\n  },\n};\n\nconst ordinalNumber = (dirtyNumber, _options) => {\n  const number = Number(dirtyNumber);\n  return number + \".\";\n};\n\nexport const localize = {\n  ordinalNumber,\n\n  era: buildLocalizeFn({\n    values: eraValues,\n    defaultWidth: \"wide\",\n  }),\n\n  quarter: buildLocalizeFn({\n    values: quarterValues,\n    defaultWidth: \"wide\",\n    argumentCallback: (quarter) => quarter - 1,\n    formattingValues: formattingQuarterValues,\n    defaultFormattingWidth: \"wide\",\n  }),\n\n  month: buildLocalizeFn({\n    values: monthValues,\n    defaultWidth: \"wide\",\n  }),\n\n  day: buildLocalizeFn({\n    values: dayValues,\n    defaultWidth: \"wide\",\n  }),\n\n  dayPeriod: buildLocalizeFn({\n    values: dayPeriodValues,\n    defaultWidth: \"wide\",\n  }),\n};\n"], "mappings": "AAAA,SAASA,eAAe,QAAQ,+BAA+B;AAE/D,MAAMC,SAAS,GAAG;EAChBC,MAAM,EAAE,CAAC,KAAK,EAAE,MAAM,CAAC;EACvBC,WAAW,EAAE,CAAC,OAAO,EAAE,QAAQ,CAAC;EAChCC,IAAI,EAAE,CAAC,gBAAgB,EAAE,wBAAwB;AACnD,CAAC;AAED,MAAMC,aAAa,GAAG;EACpBH,MAAM,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;EAChCC,WAAW,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,CAAC;EACzDC,IAAI,EAAE,CAAC,aAAa,EAAE,aAAa,EAAE,aAAa,EAAE,aAAa;AACnE,CAAC;AAED,MAAME,uBAAuB,GAAG;EAC9BJ,MAAM,EAAE,CAAC,IAAI,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK,CAAC;EACpCC,WAAW,EAAE,CAAC,SAAS,EAAE,UAAU,EAAE,WAAW,EAAE,UAAU,CAAC;EAC7DC,IAAI,EAAE,CAAC,aAAa,EAAE,cAAc,EAAE,eAAe,EAAE,cAAc;AACvE,CAAC;AAED,MAAMG,WAAW,GAAG;EAClBL,MAAM,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,IAAI,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EAErEC,WAAW,EAAE,CACX,MAAM,EACN,OAAO,EACP,OAAO,EACP,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,QAAQ,EACR,MAAM,EACN,MAAM,EACN,MAAM,CACP;EAEDC,IAAI,EAAE,CACJ,QAAQ,EACR,SAAS,EACT,SAAS,EACT,SAAS,EACT,OAAO,EACP,QAAQ,EACR,QAAQ,EACR,WAAW,EACX,YAAY,EACZ,SAAS,EACT,UAAU,EACV,UAAU;AAEd,CAAC;AAED,MAAMI,SAAS,GAAG;EAChBN,MAAM,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,EAAE,IAAI,CAAC;EAC9CO,KAAK,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG,EAAE,KAAK,CAAC;EAC/CN,WAAW,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG,EAAE,KAAK,CAAC;EACrDC,IAAI,EAAE,CACJ,UAAU,EACV,OAAO,EACP,MAAM,EACN,QAAQ,EACR,WAAW,EACX,QAAQ,EACR,SAAS;AAEb,CAAC;AAED,MAAMM,eAAe,GAAG;EACtBR,MAAM,EAAE;IACNS,EAAE,EAAE,KAAK;IACTC,EAAE,EAAE,KAAK;IACTC,QAAQ,EAAE,OAAO;IACjBC,IAAI,EAAE,KAAK;IACXC,OAAO,EAAE,QAAQ;IACjBC,SAAS,EAAE,KAAK;IAChBC,OAAO,EAAE,MAAM;IACfC,KAAK,EAAE;EACT,CAAC;EACDf,WAAW,EAAE;IACXQ,EAAE,EAAE,KAAK;IACTC,EAAE,EAAE,KAAK;IACTC,QAAQ,EAAE,OAAO;IACjBC,IAAI,EAAE,KAAK;IACXC,OAAO,EAAE,QAAQ;IACjBC,SAAS,EAAE,KAAK;IAChBC,OAAO,EAAE,MAAM;IACfC,KAAK,EAAE;EACT,CAAC;EACDd,IAAI,EAAE;IACJO,EAAE,EAAE,KAAK;IACTC,EAAE,EAAE,KAAK;IACTC,QAAQ,EAAE,OAAO;IACjBC,IAAI,EAAE,KAAK;IACXC,OAAO,EAAE,QAAQ;IACjBC,SAAS,EAAE,SAAS;IACpBC,OAAO,EAAE,MAAM;IACfC,KAAK,EAAE;EACT;AACF,CAAC;AAED,MAAMC,aAAa,GAAGA,CAACC,WAAW,EAAEC,QAAQ,KAAK;EAC/C,MAAMC,MAAM,GAAGC,MAAM,CAACH,WAAW,CAAC;EAClC,OAAOE,MAAM,GAAG,GAAG;AACrB,CAAC;AAED,OAAO,MAAME,QAAQ,GAAG;EACtBL,aAAa;EAEbM,GAAG,EAAEzB,eAAe,CAAC;IACnB0B,MAAM,EAAEzB,SAAS;IACjB0B,YAAY,EAAE;EAChB,CAAC,CAAC;EAEFC,OAAO,EAAE5B,eAAe,CAAC;IACvB0B,MAAM,EAAErB,aAAa;IACrBsB,YAAY,EAAE,MAAM;IACpBE,gBAAgB,EAAGD,OAAO,IAAKA,OAAO,GAAG,CAAC;IAC1CE,gBAAgB,EAAExB,uBAAuB;IACzCyB,sBAAsB,EAAE;EAC1B,CAAC,CAAC;EAEFC,KAAK,EAAEhC,eAAe,CAAC;IACrB0B,MAAM,EAAEnB,WAAW;IACnBoB,YAAY,EAAE;EAChB,CAAC,CAAC;EAEFM,GAAG,EAAEjC,eAAe,CAAC;IACnB0B,MAAM,EAAElB,SAAS;IACjBmB,YAAY,EAAE;EAChB,CAAC,CAAC;EAEFO,SAAS,EAAElC,eAAe,CAAC;IACzB0B,MAAM,EAAEhB,eAAe;IACvBiB,YAAY,EAAE;EAChB,CAAC;AACH,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "import _objectSpread from\"C:/Users/<USER>/Desktop/demo-git/demo-react/my-fullstack-app/frontend/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";import React,{lazy,Suspense}from'react';import LoadingSpinner from'../components/Common/LoadingSpinner';import{ROUTES}from'../constants/routes';// Lazy load components for better performance\nimport{jsx as _jsx}from\"react/jsx-runtime\";const DoctorsPage=/*#__PURE__*/lazy(()=>import('../components/Page/DoctorsPage'));const DoctorDetailPage=/*#__PURE__*/lazy(()=>import('../components/Page/DoctorDetailPage'));const TreatmentsPage=/*#__PURE__*/lazy(()=>import('../components/Page/TreatmentsPage'));const TreatmentsDetailPage=/*#__PURE__*/lazy(()=>import('../components/Page/TreatmentsDetailPage'));const PatientsPage=/*#__PURE__*/lazy(()=>import('../components/Page/PatientsPage'));const PatientsDetailPage=/*#__PURE__*/lazy(()=>import('../components/Page/PatientsDetailPage'));const SchedulesPage=/*#__PURE__*/lazy(()=>import('../components/Page/SchedulesPage'));const ReceiptsPage=/*#__PURE__*/lazy(()=>import('../components/Page/ReceiptsPage'));const ReceiptsDetailPage=/*#__PURE__*/lazy(()=>import('../components/Page/ReceiptsDetailPage'));const UsersPage=/*#__PURE__*/lazy(()=>import('../components/Page/UsersPage'));const BackupPage=/*#__PURE__*/lazy(()=>import('../components/Page/BackupPage'));const ReportManagementPage=/*#__PURE__*/lazy(()=>import('../components/Page/ReportManagementPage'));const ImageManagementPage=/*#__PURE__*/lazy(()=>import('../components/Page/ImageManagementPage'));const LoginLogsPage=/*#__PURE__*/lazy(()=>import('../components/Page/LoginLogsPage'));const IpBlocksPage=/*#__PURE__*/lazy(()=>import('../components/Page/IpBlocksPage'));const DebugPage=/*#__PURE__*/lazy(()=>import('../components/Page/DebugPage'));const UpdatePasswordPage=/*#__PURE__*/lazy(()=>import('../components/Page/UpdatePasswordPage'));const PermissionPage=/*#__PURE__*/lazy(()=>import('../components/Page/PermissionPage'));// Higher-order component to wrap lazy components with Suspense\nconst withSuspense=Component=>{return props=>/*#__PURE__*/_jsx(Suspense,{fallback:/*#__PURE__*/_jsx(LoadingSpinner,{message:\"Loading page...\"}),children:/*#__PURE__*/_jsx(Component,_objectSpread({},props))});};export const componentMap={[ROUTES.DOCTORS]:withSuspense(DoctorsPage),[ROUTES.DOCTOR_DETAIL]:withSuspense(DoctorDetailPage),[ROUTES.TREATMENTS]:withSuspense(TreatmentsPage),[ROUTES.TREATMENT_DETAIL]:withSuspense(TreatmentsDetailPage),[ROUTES.PATIENTS]:withSuspense(PatientsPage),[ROUTES.PATIENT_DETAIL]:withSuspense(PatientsDetailPage),[ROUTES.SCHEDULES]:withSuspense(SchedulesPage),[ROUTES.RECEIPTS]:withSuspense(ReceiptsPage),[ROUTES.RECEIPT_DETAIL]:withSuspense(ReceiptsDetailPage),[ROUTES.USERS]:withSuspense(UsersPage),[ROUTES.BACKUP_MANAGEMENT]:withSuspense(BackupPage),[ROUTES.REPORT_MANAGEMENT]:withSuspense(ReportManagementPage),[ROUTES.IMAGE_MANAGEMENT]:withSuspense(ImageManagementPage),[ROUTES.LOGIN_LOGS]:withSuspense(LoginLogsPage),[ROUTES.IP_BLOCKS]:withSuspense(IpBlocksPage),[ROUTES.DEBUG]:withSuspense(DebugPage),[ROUTES.UPDATE_PASSWORD]:withSuspense(UpdatePasswordPage),[ROUTES.PERMISSION_MANAGEMENT]:withSuspense(PermissionPage)};", "map": {"version": 3, "names": ["React", "lazy", "Suspense", "LoadingSpinner", "ROUTES", "jsx", "_jsx", "DoctorsPage", "DoctorDetailPage", "TreatmentsPage", "TreatmentsDetailPage", "PatientsPage", "PatientsDetailPage", "SchedulesPage", "ReceiptsPage", "ReceiptsDetailPage", "UsersPage", "BackupPage", "ReportManagementPage", "ImageManagementPage", "LoginLogsPage", "IpBlocksPage", "DebugPage", "UpdatePasswordPage", "PermissionPage", "withSuspense", "Component", "props", "fallback", "message", "children", "_objectSpread", "componentMap", "DOCTORS", "DOCTOR_DETAIL", "TREATMENTS", "TREATMENT_DETAIL", "PATIENTS", "PATIENT_DETAIL", "SCHEDULES", "RECEIPTS", "RECEIPT_DETAIL", "USERS", "BACKUP_MANAGEMENT", "REPORT_MANAGEMENT", "IMAGE_MANAGEMENT", "LOGIN_LOGS", "IP_BLOCKS", "DEBUG", "UPDATE_PASSWORD", "PERMISSION_MANAGEMENT"], "sources": ["C:/Users/<USER>/Desktop/demo-git/demo-react/my-fullstack-app/frontend/src/routes/componentMap.tsx"], "sourcesContent": ["\nimport React, { lazy, Suspense } from 'react';\nimport LoadingSpinner from '../components/Common/LoadingSpinner';\nimport { ROUTES } from '../constants/routes';\n\n// Lazy load components for better performance\nconst DoctorsPage = lazy(() => import('../components/Page/DoctorsPage'));\nconst DoctorDetailPage = lazy(() => import('../components/Page/DoctorDetailPage'));\nconst TreatmentsPage = lazy(() => import('../components/Page/TreatmentsPage'));\nconst TreatmentsDetailPage = lazy(() => import('../components/Page/TreatmentsDetailPage'));\nconst PatientsPage = lazy(() => import('../components/Page/PatientsPage'));\nconst PatientsDetailPage = lazy(() => import('../components/Page/PatientsDetailPage'));\nconst SchedulesPage = lazy(() => import('../components/Page/SchedulesPage'));\nconst ReceiptsPage = lazy(() => import('../components/Page/ReceiptsPage'));\nconst ReceiptsDetailPage = lazy(() => import('../components/Page/ReceiptsDetailPage'));\nconst UsersPage = lazy(() => import('../components/Page/UsersPage'));\nconst BackupPage = lazy(() => import('../components/Page/BackupPage'));\nconst ReportManagementPage = lazy(() => import('../components/Page/ReportManagementPage'));\nconst ImageManagementPage = lazy(() => import('../components/Page/ImageManagementPage'));\nconst LoginLogsPage = lazy(() => import('../components/Page/LoginLogsPage'));\nconst IpBlocksPage = lazy(() => import('../components/Page/IpBlocksPage'));\nconst DebugPage = lazy(() => import('../components/Page/DebugPage'));\nconst UpdatePasswordPage = lazy(() => import('../components/Page/UpdatePasswordPage'));\nconst PermissionPage = lazy(() => import('../components/Page/PermissionPage'));\n\n// Higher-order component to wrap lazy components with Suspense\nconst withSuspense = (Component: React.LazyExoticComponent<React.ComponentType<any>>) => {\n  return (props: any) => (\n    <Suspense fallback={<LoadingSpinner message=\"Loading page...\" />}>\n      <Component {...props} />\n    </Suspense>\n  );\n};\n\nexport const componentMap: { [key: string]: React.ComponentType<any> } = {\n  [ROUTES.DOCTORS]: withSuspense(DoctorsPage),\n  [ROUTES.DOCTOR_DETAIL]: withSuspense(DoctorDetailPage),\n  [ROUTES.TREATMENTS]: withSuspense(TreatmentsPage),\n  [ROUTES.TREATMENT_DETAIL]: withSuspense(TreatmentsDetailPage),\n  [ROUTES.PATIENTS]: withSuspense(PatientsPage),\n  [ROUTES.PATIENT_DETAIL]: withSuspense(PatientsDetailPage),\n  [ROUTES.SCHEDULES]: withSuspense(SchedulesPage),\n  [ROUTES.RECEIPTS]: withSuspense(ReceiptsPage),\n  [ROUTES.RECEIPT_DETAIL]: withSuspense(ReceiptsDetailPage),\n  [ROUTES.USERS]: withSuspense(UsersPage),\n  [ROUTES.BACKUP_MANAGEMENT]: withSuspense(BackupPage),\n  [ROUTES.REPORT_MANAGEMENT]: withSuspense(ReportManagementPage),\n  [ROUTES.IMAGE_MANAGEMENT]: withSuspense(ImageManagementPage),\n  [ROUTES.LOGIN_LOGS]: withSuspense(LoginLogsPage),\n  [ROUTES.IP_BLOCKS]: withSuspense(IpBlocksPage),\n  [ROUTES.DEBUG]: withSuspense(DebugPage),\n  [ROUTES.UPDATE_PASSWORD]: withSuspense(UpdatePasswordPage),\n  [ROUTES.PERMISSION_MANAGEMENT]: withSuspense(PermissionPage),\n};"], "mappings": "wJACA,MAAO,CAAAA,KAAK,EAAIC,IAAI,CAAEC,QAAQ,KAAQ,OAAO,CAC7C,MAAO,CAAAC,cAAc,KAAM,qCAAqC,CAChE,OAASC,MAAM,KAAQ,qBAAqB,CAE5C;AAAA,OAAAC,GAAA,IAAAC,IAAA,yBACA,KAAM,CAAAC,WAAW,cAAGN,IAAI,CAAC,IAAM,MAAM,CAAC,gCAAgC,CAAC,CAAC,CACxE,KAAM,CAAAO,gBAAgB,cAAGP,IAAI,CAAC,IAAM,MAAM,CAAC,qCAAqC,CAAC,CAAC,CAClF,KAAM,CAAAQ,cAAc,cAAGR,IAAI,CAAC,IAAM,MAAM,CAAC,mCAAmC,CAAC,CAAC,CAC9E,KAAM,CAAAS,oBAAoB,cAAGT,IAAI,CAAC,IAAM,MAAM,CAAC,yCAAyC,CAAC,CAAC,CAC1F,KAAM,CAAAU,YAAY,cAAGV,IAAI,CAAC,IAAM,MAAM,CAAC,iCAAiC,CAAC,CAAC,CAC1E,KAAM,CAAAW,kBAAkB,cAAGX,IAAI,CAAC,IAAM,MAAM,CAAC,uCAAuC,CAAC,CAAC,CACtF,KAAM,CAAAY,aAAa,cAAGZ,IAAI,CAAC,IAAM,MAAM,CAAC,kCAAkC,CAAC,CAAC,CAC5E,KAAM,CAAAa,YAAY,cAAGb,IAAI,CAAC,IAAM,MAAM,CAAC,iCAAiC,CAAC,CAAC,CAC1E,KAAM,CAAAc,kBAAkB,cAAGd,IAAI,CAAC,IAAM,MAAM,CAAC,uCAAuC,CAAC,CAAC,CACtF,KAAM,CAAAe,SAAS,cAAGf,IAAI,CAAC,IAAM,MAAM,CAAC,8BAA8B,CAAC,CAAC,CACpE,KAAM,CAAAgB,UAAU,cAAGhB,IAAI,CAAC,IAAM,MAAM,CAAC,+BAA+B,CAAC,CAAC,CACtE,KAAM,CAAAiB,oBAAoB,cAAGjB,IAAI,CAAC,IAAM,MAAM,CAAC,yCAAyC,CAAC,CAAC,CAC1F,KAAM,CAAAkB,mBAAmB,cAAGlB,IAAI,CAAC,IAAM,MAAM,CAAC,wCAAwC,CAAC,CAAC,CACxF,KAAM,CAAAmB,aAAa,cAAGnB,IAAI,CAAC,IAAM,MAAM,CAAC,kCAAkC,CAAC,CAAC,CAC5E,KAAM,CAAAoB,YAAY,cAAGpB,IAAI,CAAC,IAAM,MAAM,CAAC,iCAAiC,CAAC,CAAC,CAC1E,KAAM,CAAAqB,SAAS,cAAGrB,IAAI,CAAC,IAAM,MAAM,CAAC,8BAA8B,CAAC,CAAC,CACpE,KAAM,CAAAsB,kBAAkB,cAAGtB,IAAI,CAAC,IAAM,MAAM,CAAC,uCAAuC,CAAC,CAAC,CACtF,KAAM,CAAAuB,cAAc,cAAGvB,IAAI,CAAC,IAAM,MAAM,CAAC,mCAAmC,CAAC,CAAC,CAE9E;AACA,KAAM,CAAAwB,YAAY,CAAIC,SAA8D,EAAK,CACvF,MAAQ,CAAAC,KAAU,eAChBrB,IAAA,CAACJ,QAAQ,EAAC0B,QAAQ,cAAEtB,IAAA,CAACH,cAAc,EAAC0B,OAAO,CAAC,iBAAiB,CAAE,CAAE,CAAAC,QAAA,cAC/DxB,IAAA,CAACoB,SAAS,CAAAK,aAAA,IAAKJ,KAAK,CAAG,CAAC,CAChB,CACX,CACH,CAAC,CAED,MAAO,MAAM,CAAAK,YAAyD,CAAG,CACvE,CAAC5B,MAAM,CAAC6B,OAAO,EAAGR,YAAY,CAAClB,WAAW,CAAC,CAC3C,CAACH,MAAM,CAAC8B,aAAa,EAAGT,YAAY,CAACjB,gBAAgB,CAAC,CACtD,CAACJ,MAAM,CAAC+B,UAAU,EAAGV,YAAY,CAAChB,cAAc,CAAC,CACjD,CAACL,MAAM,CAACgC,gBAAgB,EAAGX,YAAY,CAACf,oBAAoB,CAAC,CAC7D,CAACN,MAAM,CAACiC,QAAQ,EAAGZ,YAAY,CAACd,YAAY,CAAC,CAC7C,CAACP,MAAM,CAACkC,cAAc,EAAGb,YAAY,CAACb,kBAAkB,CAAC,CACzD,CAACR,MAAM,CAACmC,SAAS,EAAGd,YAAY,CAACZ,aAAa,CAAC,CAC/C,CAACT,MAAM,CAACoC,QAAQ,EAAGf,YAAY,CAACX,YAAY,CAAC,CAC7C,CAACV,MAAM,CAACqC,cAAc,EAAGhB,YAAY,CAACV,kBAAkB,CAAC,CACzD,CAACX,MAAM,CAACsC,KAAK,EAAGjB,YAAY,CAACT,SAAS,CAAC,CACvC,CAACZ,MAAM,CAACuC,iBAAiB,EAAGlB,YAAY,CAACR,UAAU,CAAC,CACpD,CAACb,MAAM,CAACwC,iBAAiB,EAAGnB,YAAY,CAACP,oBAAoB,CAAC,CAC9D,CAACd,MAAM,CAACyC,gBAAgB,EAAGpB,YAAY,CAACN,mBAAmB,CAAC,CAC5D,CAACf,MAAM,CAAC0C,UAAU,EAAGrB,YAAY,CAACL,aAAa,CAAC,CAChD,CAAChB,MAAM,CAAC2C,SAAS,EAAGtB,YAAY,CAACJ,YAAY,CAAC,CAC9C,CAACjB,MAAM,CAAC4C,KAAK,EAAGvB,YAAY,CAACH,SAAS,CAAC,CACvC,CAAClB,MAAM,CAAC6C,eAAe,EAAGxB,YAAY,CAACF,kBAAkB,CAAC,CAC1D,CAACnB,MAAM,CAAC8C,qBAAqB,EAAGzB,YAAY,CAACD,cAAc,CAC7D,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"name": "primereact", "version": "10.9.6", "private": false, "author": "PrimeTek Informatics", "description": "PrimeReact is an open source UI library for React featuring a rich set of 90+ components, a theme designer, various theme alternatives such as Material, Bootstrap, Tailwind, premium templates and professional support. In addition, it integrates with PrimeBlock, which has 370+ ready to use UI blocks to build spectacular applications in no time.", "homepage": "https://www.primereact.org", "repository": {"type": "git", "url": "https://github.com/primefaces/primereact.git"}, "license": "MIT", "bugs": {"url": "https://github.com/primefaces/primereact/issues"}, "keywords": ["primereact", "react", "hooks", "next", "nextjs", "ui-kit", "ui library", "component library", "material", "material design", "bootstrap", "tailwind theme", "dark theme", "react components", "responsive components"], "unpkg": "primereact.all.min.js", "jsdelivr": "primereact.all.min.js", "main": "primereact.all.min.js", "module": "primereact.all.esm.min.js", "web-types": "web-types.json", "peerDependencies": {"@types/react": "^17.0.0 || ^18.0.0 || ^19.0.0", "react": "^17.0.0 || ^18.0.0 || ^19.0.0", "react-dom": "^17.0.0 || ^18.0.0 || ^19.0.0"}, "peerDependenciesMeta": {"@types/react": {"optional": true}}, "dependencies": {"@types/react-transition-group": "^4.4.1", "react-transition-group": "^4.4.1"}, "sideEffects": ["**/*.css"], "engines": {"node": ">=14.0.0"}}
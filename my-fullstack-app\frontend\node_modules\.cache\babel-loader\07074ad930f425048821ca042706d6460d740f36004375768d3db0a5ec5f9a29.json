{"ast": null, "code": "import { buildMatchFn } from \"../../_lib/buildMatchFn.js\";\nimport { buildMatchPatternFn } from \"../../_lib/buildMatchPatternFn.js\";\nconst matchOrdinalNumberPattern = /^(\\d+)/i;\nconst parseOrdinalNumberPattern = /\\d+/i;\nconst matchEraPatterns = {\n  narrow: /^(tcn|scn)/i,\n  abbreviated: /^(trước CN|sau CN)/i,\n  wide: /^(trước Công Nguyên|sau Công Nguyên)/i\n};\nconst parseEraPatterns = {\n  any: [/^t/i, /^s/i]\n};\nconst matchQuarterPatterns = {\n  narrow: /^([1234]|i{1,3}v?)/i,\n  abbreviated: /^q([1234]|i{1,3}v?)/i,\n  wide: /^quý ([1234]|i{1,3}v?)/i\n};\nconst parseQuarterPatterns = {\n  any: [/(1|i)$/i, /(2|ii)$/i, /(3|iii)$/i, /(4|iv)$/i]\n};\nconst matchMonthPatterns = {\n  // month number may contain leading 0, 'thg' prefix may have space, underscore or empty before number\n  // note the order of '1' since it is a sub-string of '10', so must be lower priority\n  narrow: /^(0?[2-9]|10|11|12|0?1)/i,\n  // note the order of 'thg 1' since it is sub-string of 'thg 10', so must be lower priority\n  abbreviated: /^thg[ _]?(0?[1-9](?!\\d)|10|11|12)/i,\n  // note the order of 'Mười' since it is sub-string of Mười Một, so must be lower priority\n  wide: /^tháng ?(Một|Hai|Ba|Tư|Năm|Sáu|Bảy|Tám|Chín|Mười|Mười ?Một|Mười ?Hai|0?[1-9](?!\\d)|10|11|12)/i\n};\nconst parseMonthPatterns = {\n  narrow: [/0?1$/i, /0?2/i, /3/, /4/, /5/, /6/, /7/, /8/, /9/, /10/, /11/, /12/],\n  abbreviated: [/^thg[ _]?0?1(?!\\d)/i, /^thg[ _]?0?2/i, /^thg[ _]?0?3/i, /^thg[ _]?0?4/i, /^thg[ _]?0?5/i, /^thg[ _]?0?6/i, /^thg[ _]?0?7/i, /^thg[ _]?0?8/i, /^thg[ _]?0?9/i, /^thg[ _]?10/i, /^thg[ _]?11/i, /^thg[ _]?12/i],\n  wide: [/^tháng ?(Một|0?1(?!\\d))/i, /^tháng ?(Hai|0?2)/i, /^tháng ?(Ba|0?3)/i, /^tháng ?(Tư|0?4)/i, /^tháng ?(Năm|0?5)/i, /^tháng ?(Sáu|0?6)/i, /^tháng ?(Bảy|0?7)/i, /^tháng ?(Tám|0?8)/i, /^tháng ?(Chín|0?9)/i, /^tháng ?(Mười|10)/i, /^tháng ?(Mười ?Một|11)/i, /^tháng ?(Mười ?Hai|12)/i]\n};\nconst matchDayPatterns = {\n  narrow: /^(CN|T2|T3|T4|T5|T6|T7)/i,\n  short: /^(CN|Th ?2|Th ?3|Th ?4|Th ?5|Th ?6|Th ?7)/i,\n  abbreviated: /^(CN|Th ?2|Th ?3|Th ?4|Th ?5|Th ?6|Th ?7)/i,\n  wide: /^(Chủ ?Nhật|Chúa ?Nhật|thứ ?Hai|thứ ?Ba|thứ ?Tư|thứ ?Năm|thứ ?Sáu|thứ ?Bảy)/i\n};\nconst parseDayPatterns = {\n  narrow: [/CN/i, /2/i, /3/i, /4/i, /5/i, /6/i, /7/i],\n  short: [/CN/i, /2/i, /3/i, /4/i, /5/i, /6/i, /7/i],\n  abbreviated: [/CN/i, /2/i, /3/i, /4/i, /5/i, /6/i, /7/i],\n  wide: [/(Chủ|Chúa) ?Nhật/i, /Hai/i, /Ba/i, /Tư/i, /Năm/i, /Sáu/i, /Bảy/i]\n};\nconst matchDayPeriodPatterns = {\n  narrow: /^(a|p|nửa đêm|trưa|(giờ) (sáng|chiều|tối|đêm))/i,\n  abbreviated: /^(am|pm|nửa đêm|trưa|(giờ) (sáng|chiều|tối|đêm))/i,\n  wide: /^(ch[^i]*|sa|nửa đêm|trưa|(giờ) (sáng|chiều|tối|đêm))/i\n};\nconst parseDayPeriodPatterns = {\n  any: {\n    am: /^(a|sa)/i,\n    pm: /^(p|ch[^i]*)/i,\n    midnight: /nửa đêm/i,\n    noon: /trưa/i,\n    morning: /sáng/i,\n    afternoon: /chiều/i,\n    evening: /tối/i,\n    night: /^đêm/i\n  }\n};\nexport const match = {\n  ordinalNumber: buildMatchPatternFn({\n    matchPattern: matchOrdinalNumberPattern,\n    parsePattern: parseOrdinalNumberPattern,\n    valueCallback: value => parseInt(value, 10)\n  }),\n  era: buildMatchFn({\n    matchPatterns: matchEraPatterns,\n    defaultMatchWidth: \"wide\",\n    parsePatterns: parseEraPatterns,\n    defaultParseWidth: \"any\"\n  }),\n  quarter: buildMatchFn({\n    matchPatterns: matchQuarterPatterns,\n    defaultMatchWidth: \"wide\",\n    parsePatterns: parseQuarterPatterns,\n    defaultParseWidth: \"any\",\n    valueCallback: index => index + 1\n  }),\n  month: buildMatchFn({\n    matchPatterns: matchMonthPatterns,\n    defaultMatchWidth: \"wide\",\n    parsePatterns: parseMonthPatterns,\n    defaultParseWidth: \"wide\"\n  }),\n  day: buildMatchFn({\n    matchPatterns: matchDayPatterns,\n    defaultMatchWidth: \"wide\",\n    parsePatterns: parseDayPatterns,\n    defaultParseWidth: \"wide\"\n  }),\n  dayPeriod: buildMatchFn({\n    matchPatterns: matchDayPeriodPatterns,\n    defaultMatchWidth: \"wide\",\n    parsePatterns: parseDayPeriodPatterns,\n    defaultParseWidth: \"any\"\n  })\n};", "map": {"version": 3, "names": ["buildMatchFn", "buildMatchPatternFn", "matchOrdinalNumberPattern", "parseOrdinalNumberPattern", "matchEraPatterns", "narrow", "abbreviated", "wide", "parseEraPatterns", "any", "matchQuarterPatterns", "parseQuarterPatterns", "matchMonthPatterns", "parseMonthPatterns", "matchDayPatterns", "short", "parseDayPatterns", "matchDayPeriodPatterns", "parseDayPeriodPatterns", "am", "pm", "midnight", "noon", "morning", "afternoon", "evening", "night", "match", "ordinalNumber", "matchPattern", "parsePattern", "valueCallback", "value", "parseInt", "era", "matchPatterns", "defaultMatchWidth", "parsePatterns", "defaultParseWidth", "quarter", "index", "month", "day", "<PERSON><PERSON><PERSON><PERSON>"], "sources": ["C:/Users/<USER>/Desktop/demo-git/demo-react/my-fullstack-app/frontend/node_modules/date-fns/locale/vi/_lib/match.js"], "sourcesContent": ["import { buildMatchFn } from \"../../_lib/buildMatchFn.js\";\nimport { buildMatchPatternFn } from \"../../_lib/buildMatchPatternFn.js\";\n\nconst matchOrdinalNumberPattern = /^(\\d+)/i;\nconst parseOrdinalNumberPattern = /\\d+/i;\n\nconst matchEraPatterns = {\n  narrow: /^(tcn|scn)/i,\n  abbreviated: /^(trước CN|sau CN)/i,\n  wide: /^(trước Công Nguyên|sau Công Nguyên)/i,\n};\nconst parseEraPatterns = {\n  any: [/^t/i, /^s/i],\n};\n\nconst matchQuarterPatterns = {\n  narrow: /^([1234]|i{1,3}v?)/i,\n  abbreviated: /^q([1234]|i{1,3}v?)/i,\n  wide: /^quý ([1234]|i{1,3}v?)/i,\n};\nconst parseQuarterPatterns = {\n  any: [/(1|i)$/i, /(2|ii)$/i, /(3|iii)$/i, /(4|iv)$/i],\n};\n\nconst matchMonthPatterns = {\n  // month number may contain leading 0, 'thg' prefix may have space, underscore or empty before number\n  // note the order of '1' since it is a sub-string of '10', so must be lower priority\n  narrow: /^(0?[2-9]|10|11|12|0?1)/i,\n  // note the order of 'thg 1' since it is sub-string of 'thg 10', so must be lower priority\n  abbreviated: /^thg[ _]?(0?[1-9](?!\\d)|10|11|12)/i,\n  // note the order of 'Mười' since it is sub-string of Mười Một, so must be lower priority\n  wide: /^tháng ?(Một|Hai|Ba|Tư|Năm|Sáu|Bảy|Tám|Chín|Mười|Mười ?Một|Mười ?Hai|0?[1-9](?!\\d)|10|11|12)/i,\n};\nconst parseMonthPatterns = {\n  narrow: [\n    /0?1$/i,\n    /0?2/i,\n    /3/,\n    /4/,\n    /5/,\n    /6/,\n    /7/,\n    /8/,\n    /9/,\n    /10/,\n    /11/,\n    /12/,\n  ],\n\n  abbreviated: [\n    /^thg[ _]?0?1(?!\\d)/i,\n    /^thg[ _]?0?2/i,\n    /^thg[ _]?0?3/i,\n    /^thg[ _]?0?4/i,\n    /^thg[ _]?0?5/i,\n    /^thg[ _]?0?6/i,\n    /^thg[ _]?0?7/i,\n    /^thg[ _]?0?8/i,\n    /^thg[ _]?0?9/i,\n    /^thg[ _]?10/i,\n    /^thg[ _]?11/i,\n    /^thg[ _]?12/i,\n  ],\n\n  wide: [\n    /^tháng ?(Một|0?1(?!\\d))/i,\n    /^tháng ?(Hai|0?2)/i,\n    /^tháng ?(Ba|0?3)/i,\n    /^tháng ?(Tư|0?4)/i,\n    /^tháng ?(Năm|0?5)/i,\n    /^tháng ?(Sáu|0?6)/i,\n    /^tháng ?(Bảy|0?7)/i,\n    /^tháng ?(Tám|0?8)/i,\n    /^tháng ?(Chín|0?9)/i,\n    /^tháng ?(Mười|10)/i,\n    /^tháng ?(Mười ?Một|11)/i,\n    /^tháng ?(Mười ?Hai|12)/i,\n  ],\n};\n\nconst matchDayPatterns = {\n  narrow: /^(CN|T2|T3|T4|T5|T6|T7)/i,\n  short: /^(CN|Th ?2|Th ?3|Th ?4|Th ?5|Th ?6|Th ?7)/i,\n  abbreviated: /^(CN|Th ?2|Th ?3|Th ?4|Th ?5|Th ?6|Th ?7)/i,\n  wide: /^(Chủ ?Nhật|Chúa ?Nhật|thứ ?Hai|thứ ?Ba|thứ ?Tư|thứ ?Năm|thứ ?Sáu|thứ ?Bảy)/i,\n};\nconst parseDayPatterns = {\n  narrow: [/CN/i, /2/i, /3/i, /4/i, /5/i, /6/i, /7/i],\n  short: [/CN/i, /2/i, /3/i, /4/i, /5/i, /6/i, /7/i],\n  abbreviated: [/CN/i, /2/i, /3/i, /4/i, /5/i, /6/i, /7/i],\n  wide: [/(Chủ|Chúa) ?Nhật/i, /Hai/i, /Ba/i, /Tư/i, /Năm/i, /Sáu/i, /Bảy/i],\n};\n\nconst matchDayPeriodPatterns = {\n  narrow: /^(a|p|nửa đêm|trưa|(giờ) (sáng|chiều|tối|đêm))/i,\n  abbreviated: /^(am|pm|nửa đêm|trưa|(giờ) (sáng|chiều|tối|đêm))/i,\n  wide: /^(ch[^i]*|sa|nửa đêm|trưa|(giờ) (sáng|chiều|tối|đêm))/i,\n};\nconst parseDayPeriodPatterns = {\n  any: {\n    am: /^(a|sa)/i,\n    pm: /^(p|ch[^i]*)/i,\n    midnight: /nửa đêm/i,\n    noon: /trưa/i,\n    morning: /sáng/i,\n    afternoon: /chiều/i,\n    evening: /tối/i,\n    night: /^đêm/i,\n  },\n};\n\nexport const match = {\n  ordinalNumber: buildMatchPatternFn({\n    matchPattern: matchOrdinalNumberPattern,\n    parsePattern: parseOrdinalNumberPattern,\n    valueCallback: (value) => parseInt(value, 10),\n  }),\n\n  era: buildMatchFn({\n    matchPatterns: matchEraPatterns,\n    defaultMatchWidth: \"wide\",\n    parsePatterns: parseEraPatterns,\n    defaultParseWidth: \"any\",\n  }),\n\n  quarter: buildMatchFn({\n    matchPatterns: matchQuarterPatterns,\n    defaultMatchWidth: \"wide\",\n    parsePatterns: parseQuarterPatterns,\n    defaultParseWidth: \"any\",\n    valueCallback: (index) => index + 1,\n  }),\n\n  month: buildMatchFn({\n    matchPatterns: matchMonthPatterns,\n    defaultMatchWidth: \"wide\",\n    parsePatterns: parseMonthPatterns,\n    defaultParseWidth: \"wide\",\n  }),\n\n  day: buildMatchFn({\n    matchPatterns: matchDayPatterns,\n    defaultMatchWidth: \"wide\",\n    parsePatterns: parseDayPatterns,\n    defaultParseWidth: \"wide\",\n  }),\n\n  dayPeriod: buildMatchFn({\n    matchPatterns: matchDayPeriodPatterns,\n    defaultMatchWidth: \"wide\",\n    parsePatterns: parseDayPeriodPatterns,\n    defaultParseWidth: \"any\",\n  }),\n};\n"], "mappings": "AAAA,SAASA,YAAY,QAAQ,4BAA4B;AACzD,SAASC,mBAAmB,QAAQ,mCAAmC;AAEvE,MAAMC,yBAAyB,GAAG,SAAS;AAC3C,MAAMC,yBAAyB,GAAG,MAAM;AAExC,MAAMC,gBAAgB,GAAG;EACvBC,MAAM,EAAE,aAAa;EACrBC,WAAW,EAAE,qBAAqB;EAClCC,IAAI,EAAE;AACR,CAAC;AACD,MAAMC,gBAAgB,GAAG;EACvBC,GAAG,EAAE,CAAC,KAAK,EAAE,KAAK;AACpB,CAAC;AAED,MAAMC,oBAAoB,GAAG;EAC3BL,MAAM,EAAE,qBAAqB;EAC7BC,WAAW,EAAE,sBAAsB;EACnCC,IAAI,EAAE;AACR,CAAC;AACD,MAAMI,oBAAoB,GAAG;EAC3BF,GAAG,EAAE,CAAC,SAAS,EAAE,UAAU,EAAE,WAAW,EAAE,UAAU;AACtD,CAAC;AAED,MAAMG,kBAAkB,GAAG;EACzB;EACA;EACAP,MAAM,EAAE,0BAA0B;EAClC;EACAC,WAAW,EAAE,oCAAoC;EACjD;EACAC,IAAI,EAAE;AACR,CAAC;AACD,MAAMM,kBAAkB,GAAG;EACzBR,MAAM,EAAE,CACN,OAAO,EACP,MAAM,EACN,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,IAAI,EACJ,IAAI,EACJ,IAAI,CACL;EAEDC,WAAW,EAAE,CACX,qBAAqB,EACrB,eAAe,EACf,eAAe,EACf,eAAe,EACf,eAAe,EACf,eAAe,EACf,eAAe,EACf,eAAe,EACf,eAAe,EACf,cAAc,EACd,cAAc,EACd,cAAc,CACf;EAEDC,IAAI,EAAE,CACJ,0BAA0B,EAC1B,oBAAoB,EACpB,mBAAmB,EACnB,mBAAmB,EACnB,oBAAoB,EACpB,oBAAoB,EACpB,oBAAoB,EACpB,oBAAoB,EACpB,qBAAqB,EACrB,oBAAoB,EACpB,yBAAyB,EACzB,yBAAyB;AAE7B,CAAC;AAED,MAAMO,gBAAgB,GAAG;EACvBT,MAAM,EAAE,0BAA0B;EAClCU,KAAK,EAAE,4CAA4C;EACnDT,WAAW,EAAE,4CAA4C;EACzDC,IAAI,EAAE;AACR,CAAC;AACD,MAAMS,gBAAgB,GAAG;EACvBX,MAAM,EAAE,CAAC,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;EACnDU,KAAK,EAAE,CAAC,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;EAClDT,WAAW,EAAE,CAAC,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;EACxDC,IAAI,EAAE,CAAC,mBAAmB,EAAE,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM;AAC1E,CAAC;AAED,MAAMU,sBAAsB,GAAG;EAC7BZ,MAAM,EAAE,iDAAiD;EACzDC,WAAW,EAAE,mDAAmD;EAChEC,IAAI,EAAE;AACR,CAAC;AACD,MAAMW,sBAAsB,GAAG;EAC7BT,GAAG,EAAE;IACHU,EAAE,EAAE,UAAU;IACdC,EAAE,EAAE,eAAe;IACnBC,QAAQ,EAAE,UAAU;IACpBC,IAAI,EAAE,OAAO;IACbC,OAAO,EAAE,OAAO;IAChBC,SAAS,EAAE,QAAQ;IACnBC,OAAO,EAAE,MAAM;IACfC,KAAK,EAAE;EACT;AACF,CAAC;AAED,OAAO,MAAMC,KAAK,GAAG;EACnBC,aAAa,EAAE3B,mBAAmB,CAAC;IACjC4B,YAAY,EAAE3B,yBAAyB;IACvC4B,YAAY,EAAE3B,yBAAyB;IACvC4B,aAAa,EAAGC,KAAK,IAAKC,QAAQ,CAACD,KAAK,EAAE,EAAE;EAC9C,CAAC,CAAC;EAEFE,GAAG,EAAElC,YAAY,CAAC;IAChBmC,aAAa,EAAE/B,gBAAgB;IAC/BgC,iBAAiB,EAAE,MAAM;IACzBC,aAAa,EAAE7B,gBAAgB;IAC/B8B,iBAAiB,EAAE;EACrB,CAAC,CAAC;EAEFC,OAAO,EAAEvC,YAAY,CAAC;IACpBmC,aAAa,EAAEzB,oBAAoB;IACnC0B,iBAAiB,EAAE,MAAM;IACzBC,aAAa,EAAE1B,oBAAoB;IACnC2B,iBAAiB,EAAE,KAAK;IACxBP,aAAa,EAAGS,KAAK,IAAKA,KAAK,GAAG;EACpC,CAAC,CAAC;EAEFC,KAAK,EAAEzC,YAAY,CAAC;IAClBmC,aAAa,EAAEvB,kBAAkB;IACjCwB,iBAAiB,EAAE,MAAM;IACzBC,aAAa,EAAExB,kBAAkB;IACjCyB,iBAAiB,EAAE;EACrB,CAAC,CAAC;EAEFI,GAAG,EAAE1C,YAAY,CAAC;IAChBmC,aAAa,EAAErB,gBAAgB;IAC/BsB,iBAAiB,EAAE,MAAM;IACzBC,aAAa,EAAErB,gBAAgB;IAC/BsB,iBAAiB,EAAE;EACrB,CAAC,CAAC;EAEFK,SAAS,EAAE3C,YAAY,CAAC;IACtBmC,aAAa,EAAElB,sBAAsB;IACrCmB,iBAAiB,EAAE,MAAM;IACzBC,aAAa,EAAEnB,sBAAsB;IACrCoB,iBAAiB,EAAE;EACrB,CAAC;AACH,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
# 角色菜單權限系統實施指南

## 概述
本系統實現了基於用戶角色的動態菜單顯示功能，不同角色的用戶將看到不同的菜單項目。

## 實施步驟

### 1. 數據庫設置
執行以下SQL腳本來創建角色菜單關聯表並初始化數據：

```bash
# 在MySQL中執行
mysql -u your_username -p your_database < backend/Scripts/CreateRoleMenuTable.sql
```

### 2. 後端代碼更新
已完成的更新包括：

- **新增模型**: `RoleMenu.cs` - 角色菜單關聯表
- **更新DbContext**: 添加了RoleMenu實體和關聯配置
- **修改SystemController**: GetMenus方法現在根據用戶角色返回對應菜單
- **新增RoleMenuController**: 提供角色菜單權限管理API

### 3. 前端代碼更新
已完成的更新包括：

- **新增頁面**: `RoleMenuManagementPage.tsx` - 角色菜單權限管理界面
- **更新路由**: 添加了新的路由映射

### 4. 功能特性

#### 4.1 動態菜單顯示
- 用戶登入後，系統根據其角色動態載入對應的菜單
- 支持多角色用戶（顯示所有角色的菜單聯集）
- 使用Redis緩存提高性能，按角色組合緩存菜單

#### 4.2 角色菜單管理
- 管理員可以通過Web界面配置每個角色的菜單權限
- 支持按菜單群組批量選擇
- 支持單個菜單項目的精細控制

#### 4.3 向後兼容
- 如果沒有配置RoleMenu關聯，系統會顯示所有菜單（保持原有行為）
- 現有用戶不會受到影響

## 默認角色權限配置

### Admin (管理員)
- 擁有所有菜單的訪問權限
- 可以管理其他角色的菜單權限

### Manager (經理)
- 治療相關：所有菜單
- 病患管理：所有菜單  
- 系統管理：備份監控、報表管理

### User (一般用戶)
- 治療相關：所有菜單
- 病患管理：所有菜單（除權限管理外）

## API端點

### 角色菜單管理
- `GET /api/RoleMenu/GetRoles` - 獲取所有角色
- `GET /api/RoleMenu/GetRoleMenus/{roleId}` - 獲取指定角色的菜單權限
- `POST /api/RoleMenu/UpdateRoleMenus` - 更新角色菜單權限

### 系統菜單
- `GET /api/System/GetMenus` - 獲取當前用戶可訪問的菜單（已修改為角色相關）

## 使用方法

### 1. 訪問角色菜單管理
1. 以Admin角色登入系統
2. 導航到角色菜單管理頁面
3. 選擇要配置的角色
4. 勾選該角色可訪問的菜單項目
5. 保存設置

### 2. 測試不同角色
1. 創建不同角色的用戶帳號
2. 使用不同帳號登入
3. 驗證菜單顯示是否符合預期

## 緩存策略
- 菜單數據按角色組合進行緩存
- 緩存鍵格式：`MenuList:Roles:{roleId1,roleId2,...}`
- 緩存時間：1小時
- 管理員可通過"重置緩存"功能清理所有菜單緩存

## 注意事項

1. **權限控制層級**：
   - 菜單顯示控制：前端根據角色顯示菜單
   - API訪問控制：後端Controller仍需要`[Authorize(Roles = "...")]`屬性

2. **角色變更**：
   - 用戶角色變更後需要重新登入才能看到新的菜單
   - 或者可以實施實時權限更新機制

3. **菜單項目管理**：
   - 新增菜單項目後，需要在角色菜單管理中為相應角色分配權限
   - 建議為新菜單項目設置默認的角色權限

## 擴展建議

1. **細粒度權限**：可以擴展為功能級權限控制（如：只讀、編輯、刪除）
2. **動態權限**：實施實時權限更新，無需重新登入
3. **權限繼承**：實施角色層級和權限繼承機制
4. **審計日誌**：記錄權限變更的操作日誌

## 故障排除

### 問題：用戶看不到任何菜單
**解決方案**：
1. 檢查用戶是否有分配角色
2. 檢查角色是否有分配菜單權限
3. 檢查RoleMenu表中是否有對應記錄

### 問題：菜單更新不生效
**解決方案**：
1. 清理Redis緩存
2. 用戶重新登入
3. 檢查角色菜單配置是否正確保存

### 問題：所有用戶都看到所有菜單
**解決方案**：
1. 檢查RoleMenu表是否有數據
2. 確認CreateRoleMenuTable.sql是否正確執行
3. 檢查GetMenus API是否正確實施角色過濾邏輯
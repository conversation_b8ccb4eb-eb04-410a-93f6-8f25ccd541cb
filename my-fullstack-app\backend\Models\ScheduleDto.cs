using System;
using System.ComponentModel.DataAnnotations;

namespace MyApi.Models
{
    // 重複類型枚舉
    public enum RepeatType
    {
        None = 0,
        Daily = 1,
        Weekly = 2,
        Monthly = 3
    }

    // 用於創建和更新 Schedule 的 DTO
    public class ScheduleCreateDto
    {
        [Required]
        public int DoctorId { get; set; }

        [Required]
        public int PatientId { get; set; }

        [Required]
        public DateTime StartDateTime { get; set; }

        [Required]
        public DateTime EndDateTime { get; set; }

        [StringLength(500)]
        public string? Description { get; set; }

        [StringLength(50)]
        public string? BackgroundColor { get; set; } = "#3788d8";

        [StringLength(50)]
        public string? BorderColor { get; set; }

        // 重複設定
        public RepeatType RepeatType { get; set; } = RepeatType.None;

        public int RepeatCount { get; set; } = 1; // 重複次數，默認1次（不重複）
    }

    public class ScheduleUpdateDto : ScheduleCreateDto
    {
        [Required]
        public int Id { get; set; }
    }

    // 用於返回給前端的 DTO
    public class ScheduleResponseDto
    {
        public int Id { get; set; }
        public string Title { get; set; } = string.Empty;
        public DateTime StartDateTime { get; set; }
        public DateTime EndDateTime { get; set; }
        public int DoctorId { get; set; }
        public string DoctorName { get; set; } = string.Empty;
        public int PatientId { get; set; }
        public string PatientName { get; set; } = string.Empty;
        public string? Description { get; set; }
        public string? BackgroundColor { get; set; }
        public string? BorderColor { get; set; }
        public bool IsActive { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime UpdatedAt { get; set; }
    }

    // 用於 FullCalendar 的格式
    public class CalendarEventDto
    {
        public string Id { get; set; } = string.Empty;
        public string Title { get; set; } = string.Empty;
        public string Start { get; set; } = string.Empty;
        public string End { get; set; } = string.Empty;
        public string? BackgroundColor { get; set; }
        public string? BorderColor { get; set; }
        public int DoctorId { get; set; }
        public string DoctorName { get; set; } = string.Empty;
        public int PatientId { get; set; }
        public string PatientName { get; set; } = string.Empty;
        public int? TreatmentId { get; set; }
        public string? Description { get; set; }
    }

    // 查詢參數 DTO
    public class ScheduleQueryDto
    {
        public int? DoctorId { get; set; }
        public int? PatientId { get; set; }
        public DateTime? StartDate { get; set; }
        public DateTime? EndDate { get; set; }
        public bool? IsActive { get; set; } = true;
    }

    // 更新 TreatmentId 的 DTO
    public class UpdateTreatmentIdDto
    {
        public int? TreatmentId { get; set; }
    }
}

import { Browser<PERSON>outer, Route, Routes } from "react-router-dom";
import { ROUTES } from './constants/routes';
import { AuthProvider } from './contexts/AuthContext';
import { componentMap } from './routes/componentMap';

import PasswordCheckRoute from './components/Auth/PasswordCheckRoute';
import ProtectedRoute from './components/Auth/ProtectedRoute';
import Layout from './components/Layout/Layout';
import HomePage from './components/Page/HomePage';
import LoginPage from './components/Page/LoginPage';
import UpdatePasswordPage from './components/Page/UpdatePasswordPage';
import ErrorPage from './components/Page/ErrorPage';
import ErrorFallback from './components/Common/ErrorFallback';
import ErrorBoundary from './components/Common/ErrorBoundary';

function AppContent() {
  return (
    <div className="App">
      <Routes>
        <Route path={ROUTES.LOGIN} element={<LoginPage />} />
        <Route path="/error" element={<ErrorPage />} />
        <Route path={ROUTES.UPDATE_PASSWORD} element={
          <ProtectedRoute>
            <UpdatePasswordPage />
          </ProtectedRoute>
        } />
        <Route path={ROUTES.HOME} element={
          <ProtectedRoute>
            <PasswordCheckRoute>
              <Layout>
                <HomePage />
              </Layout>
            </PasswordCheckRoute>
          </ProtectedRoute>
        } />
        {Object.entries(componentMap).map(([path, Component]) => (
          <Route
            key={path}
            path={path}
            element={
              <ProtectedRoute>
                <PasswordCheckRoute>
                  <Layout>
                    <Component />
                  </Layout>
                </PasswordCheckRoute>
              </ProtectedRoute>
            }
          />
        ))}
      </Routes>
    </div>
  );
}

export default function App() {
  return (
    <ErrorBoundary fallback={ErrorFallback}>
      <AuthProvider>
        <BrowserRouter>
          <AppContent />
        </BrowserRouter>
      </AuthProvider>
    </ErrorBoundary>
  );
}

{"ast": null, "code": "function declension(scheme, count) {\n  // scheme for count=1 exists\n  if (scheme.one !== undefined && count === 1) {\n    return scheme.one;\n  }\n  const rem10 = count % 10;\n  const rem100 = count % 100;\n\n  // 1, 21, 31, ...\n  if (rem10 === 1 && rem100 !== 11) {\n    return scheme.singularNominative.replace(\"{{count}}\", String(count));\n\n    // 2, 3, 4, 22, 23, 24, 32 ...\n  } else if (rem10 >= 2 && rem10 <= 4 && (rem100 < 10 || rem100 > 20)) {\n    return scheme.singularGenitive.replace(\"{{count}}\", String(count));\n\n    // 5, 6, 7, 8, 9, 10, 11, ...\n  } else {\n    return scheme.pluralGenitive.replace(\"{{count}}\", String(count));\n  }\n}\nfunction buildLocalizeTokenFn(scheme) {\n  return (count, options) => {\n    if (options && options.addSuffix) {\n      if (options.comparison && options.comparison > 0) {\n        if (scheme.future) {\n          return declension(scheme.future, count);\n        } else {\n          return \"праз \" + declension(scheme.regular, count);\n        }\n      } else {\n        if (scheme.past) {\n          return declension(scheme.past, count);\n        } else {\n          return declension(scheme.regular, count) + \" таму\";\n        }\n      }\n    } else {\n      return declension(scheme.regular, count);\n    }\n  };\n}\nconst halfAMinute = (_, options) => {\n  if (options && options.addSuffix) {\n    if (options.comparison && options.comparison > 0) {\n      return \"праз паўхвіліны\";\n    } else {\n      return \"паўхвіліны таму\";\n    }\n  }\n  return \"паўхвіліны\";\n};\nconst formatDistanceLocale = {\n  lessThanXSeconds: buildLocalizeTokenFn({\n    regular: {\n      one: \"менш за секунду\",\n      singularNominative: \"менш за {{count}} секунду\",\n      singularGenitive: \"менш за {{count}} секунды\",\n      pluralGenitive: \"менш за {{count}} секунд\"\n    },\n    future: {\n      one: \"менш, чым праз секунду\",\n      singularNominative: \"менш, чым праз {{count}} секунду\",\n      singularGenitive: \"менш, чым праз {{count}} секунды\",\n      pluralGenitive: \"менш, чым праз {{count}} секунд\"\n    }\n  }),\n  xSeconds: buildLocalizeTokenFn({\n    regular: {\n      singularNominative: \"{{count}} секунда\",\n      singularGenitive: \"{{count}} секунды\",\n      pluralGenitive: \"{{count}} секунд\"\n    },\n    past: {\n      singularNominative: \"{{count}} секунду таму\",\n      singularGenitive: \"{{count}} секунды таму\",\n      pluralGenitive: \"{{count}} секунд таму\"\n    },\n    future: {\n      singularNominative: \"праз {{count}} секунду\",\n      singularGenitive: \"праз {{count}} секунды\",\n      pluralGenitive: \"праз {{count}} секунд\"\n    }\n  }),\n  halfAMinute: halfAMinute,\n  lessThanXMinutes: buildLocalizeTokenFn({\n    regular: {\n      one: \"менш за хвіліну\",\n      singularNominative: \"менш за {{count}} хвіліну\",\n      singularGenitive: \"менш за {{count}} хвіліны\",\n      pluralGenitive: \"менш за {{count}} хвілін\"\n    },\n    future: {\n      one: \"менш, чым праз хвіліну\",\n      singularNominative: \"менш, чым праз {{count}} хвіліну\",\n      singularGenitive: \"менш, чым праз {{count}} хвіліны\",\n      pluralGenitive: \"менш, чым праз {{count}} хвілін\"\n    }\n  }),\n  xMinutes: buildLocalizeTokenFn({\n    regular: {\n      singularNominative: \"{{count}} хвіліна\",\n      singularGenitive: \"{{count}} хвіліны\",\n      pluralGenitive: \"{{count}} хвілін\"\n    },\n    past: {\n      singularNominative: \"{{count}} хвіліну таму\",\n      singularGenitive: \"{{count}} хвіліны таму\",\n      pluralGenitive: \"{{count}} хвілін таму\"\n    },\n    future: {\n      singularNominative: \"праз {{count}} хвіліну\",\n      singularGenitive: \"праз {{count}} хвіліны\",\n      pluralGenitive: \"праз {{count}} хвілін\"\n    }\n  }),\n  aboutXHours: buildLocalizeTokenFn({\n    regular: {\n      singularNominative: \"каля {{count}} гадзіны\",\n      singularGenitive: \"каля {{count}} гадзін\",\n      pluralGenitive: \"каля {{count}} гадзін\"\n    },\n    future: {\n      singularNominative: \"прыблізна праз {{count}} гадзіну\",\n      singularGenitive: \"прыблізна праз {{count}} гадзіны\",\n      pluralGenitive: \"прыблізна праз {{count}} гадзін\"\n    }\n  }),\n  xHours: buildLocalizeTokenFn({\n    regular: {\n      singularNominative: \"{{count}} гадзіна\",\n      singularGenitive: \"{{count}} гадзіны\",\n      pluralGenitive: \"{{count}} гадзін\"\n    },\n    past: {\n      singularNominative: \"{{count}} гадзіну таму\",\n      singularGenitive: \"{{count}} гадзіны таму\",\n      pluralGenitive: \"{{count}} гадзін таму\"\n    },\n    future: {\n      singularNominative: \"праз {{count}} гадзіну\",\n      singularGenitive: \"праз {{count}} гадзіны\",\n      pluralGenitive: \"праз {{count}} гадзін\"\n    }\n  }),\n  xDays: buildLocalizeTokenFn({\n    regular: {\n      singularNominative: \"{{count}} дзень\",\n      singularGenitive: \"{{count}} дні\",\n      pluralGenitive: \"{{count}} дзён\"\n    }\n  }),\n  aboutXWeeks: buildLocalizeTokenFn({\n    regular: {\n      singularNominative: \"каля {{count}} тыдні\",\n      singularGenitive: \"каля {{count}} тыдняў\",\n      pluralGenitive: \"каля {{count}} тыдняў\"\n    },\n    future: {\n      singularNominative: \"прыблізна праз {{count}} тыдзень\",\n      singularGenitive: \"прыблізна праз {{count}} тыдні\",\n      pluralGenitive: \"прыблізна праз {{count}} тыдняў\"\n    }\n  }),\n  xWeeks: buildLocalizeTokenFn({\n    regular: {\n      singularNominative: \"{{count}} тыдзень\",\n      singularGenitive: \"{{count}} тыдні\",\n      pluralGenitive: \"{{count}} тыдняў\"\n    }\n  }),\n  aboutXMonths: buildLocalizeTokenFn({\n    regular: {\n      singularNominative: \"каля {{count}} месяца\",\n      singularGenitive: \"каля {{count}} месяцаў\",\n      pluralGenitive: \"каля {{count}} месяцаў\"\n    },\n    future: {\n      singularNominative: \"прыблізна праз {{count}} месяц\",\n      singularGenitive: \"прыблізна праз {{count}} месяцы\",\n      pluralGenitive: \"прыблізна праз {{count}} месяцаў\"\n    }\n  }),\n  xMonths: buildLocalizeTokenFn({\n    regular: {\n      singularNominative: \"{{count}} месяц\",\n      singularGenitive: \"{{count}} месяцы\",\n      pluralGenitive: \"{{count}} месяцаў\"\n    }\n  }),\n  aboutXYears: buildLocalizeTokenFn({\n    regular: {\n      singularNominative: \"каля {{count}} года\",\n      singularGenitive: \"каля {{count}} гадоў\",\n      pluralGenitive: \"каля {{count}} гадоў\"\n    },\n    future: {\n      singularNominative: \"прыблізна праз {{count}} год\",\n      singularGenitive: \"прыблізна праз {{count}} гады\",\n      pluralGenitive: \"прыблізна праз {{count}} гадоў\"\n    }\n  }),\n  xYears: buildLocalizeTokenFn({\n    regular: {\n      singularNominative: \"{{count}} год\",\n      singularGenitive: \"{{count}} гады\",\n      pluralGenitive: \"{{count}} гадоў\"\n    }\n  }),\n  overXYears: buildLocalizeTokenFn({\n    regular: {\n      singularNominative: \"больш за {{count}} год\",\n      singularGenitive: \"больш за {{count}} гады\",\n      pluralGenitive: \"больш за {{count}} гадоў\"\n    },\n    future: {\n      singularNominative: \"больш, чым праз {{count}} год\",\n      singularGenitive: \"больш, чым праз {{count}} гады\",\n      pluralGenitive: \"больш, чым праз {{count}} гадоў\"\n    }\n  }),\n  almostXYears: buildLocalizeTokenFn({\n    regular: {\n      singularNominative: \"амаль {{count}} год\",\n      singularGenitive: \"амаль {{count}} гады\",\n      pluralGenitive: \"амаль {{count}} гадоў\"\n    },\n    future: {\n      singularNominative: \"амаль праз {{count}} год\",\n      singularGenitive: \"амаль праз {{count}} гады\",\n      pluralGenitive: \"амаль праз {{count}} гадоў\"\n    }\n  })\n};\nexport const formatDistance = (token, count, options) => {\n  options = options || {};\n  return formatDistanceLocale[token](count, options);\n};", "map": {"version": 3, "names": ["declension", "scheme", "count", "one", "undefined", "rem10", "rem100", "singularNominative", "replace", "String", "singularGenitive", "pluralGenitive", "buildLocalizeTokenFn", "options", "addSuffix", "comparison", "future", "regular", "past", "halfAMinute", "_", "formatDistanceLocale", "lessThanXSeconds", "xSeconds", "lessThanXMinutes", "xMinutes", "aboutXHours", "xHours", "xDays", "aboutXWeeks", "xWeeks", "aboutXMonths", "xMonths", "aboutXYears", "xYears", "overXYears", "almostXYears", "formatDistance", "token"], "sources": ["C:/Users/<USER>/Desktop/demo-git/demo-react/my-fullstack-app/frontend/node_modules/date-fns/locale/be-tarask/_lib/formatDistance.js"], "sourcesContent": ["function declension(scheme, count) {\n  // scheme for count=1 exists\n  if (scheme.one !== undefined && count === 1) {\n    return scheme.one;\n  }\n\n  const rem10 = count % 10;\n  const rem100 = count % 100;\n\n  // 1, 21, 31, ...\n  if (rem10 === 1 && rem100 !== 11) {\n    return scheme.singularNominative.replace(\"{{count}}\", String(count));\n\n    // 2, 3, 4, 22, 23, 24, 32 ...\n  } else if (rem10 >= 2 && rem10 <= 4 && (rem100 < 10 || rem100 > 20)) {\n    return scheme.singularGenitive.replace(\"{{count}}\", String(count));\n\n    // 5, 6, 7, 8, 9, 10, 11, ...\n  } else {\n    return scheme.pluralGenitive.replace(\"{{count}}\", String(count));\n  }\n}\n\nfunction buildLocalizeTokenFn(scheme) {\n  return (count, options) => {\n    if (options && options.addSuffix) {\n      if (options.comparison && options.comparison > 0) {\n        if (scheme.future) {\n          return declension(scheme.future, count);\n        } else {\n          return \"праз \" + declension(scheme.regular, count);\n        }\n      } else {\n        if (scheme.past) {\n          return declension(scheme.past, count);\n        } else {\n          return declension(scheme.regular, count) + \" таму\";\n        }\n      }\n    } else {\n      return declension(scheme.regular, count);\n    }\n  };\n}\n\nconst halfAMinute = (_, options) => {\n  if (options && options.addSuffix) {\n    if (options.comparison && options.comparison > 0) {\n      return \"праз паўхвіліны\";\n    } else {\n      return \"паўхвіліны таму\";\n    }\n  }\n\n  return \"паўхвіліны\";\n};\n\nconst formatDistanceLocale = {\n  lessThanXSeconds: buildLocalizeTokenFn({\n    regular: {\n      one: \"менш за секунду\",\n      singularNominative: \"менш за {{count}} секунду\",\n      singularGenitive: \"менш за {{count}} секунды\",\n      pluralGenitive: \"менш за {{count}} секунд\",\n    },\n    future: {\n      one: \"менш, чым праз секунду\",\n      singularNominative: \"менш, чым праз {{count}} секунду\",\n      singularGenitive: \"менш, чым праз {{count}} секунды\",\n      pluralGenitive: \"менш, чым праз {{count}} секунд\",\n    },\n  }),\n\n  xSeconds: buildLocalizeTokenFn({\n    regular: {\n      singularNominative: \"{{count}} секунда\",\n      singularGenitive: \"{{count}} секунды\",\n      pluralGenitive: \"{{count}} секунд\",\n    },\n    past: {\n      singularNominative: \"{{count}} секунду таму\",\n      singularGenitive: \"{{count}} секунды таму\",\n      pluralGenitive: \"{{count}} секунд таму\",\n    },\n    future: {\n      singularNominative: \"праз {{count}} секунду\",\n      singularGenitive: \"праз {{count}} секунды\",\n      pluralGenitive: \"праз {{count}} секунд\",\n    },\n  }),\n\n  halfAMinute: halfAMinute,\n\n  lessThanXMinutes: buildLocalizeTokenFn({\n    regular: {\n      one: \"менш за хвіліну\",\n      singularNominative: \"менш за {{count}} хвіліну\",\n      singularGenitive: \"менш за {{count}} хвіліны\",\n      pluralGenitive: \"менш за {{count}} хвілін\",\n    },\n    future: {\n      one: \"менш, чым праз хвіліну\",\n      singularNominative: \"менш, чым праз {{count}} хвіліну\",\n      singularGenitive: \"менш, чым праз {{count}} хвіліны\",\n      pluralGenitive: \"менш, чым праз {{count}} хвілін\",\n    },\n  }),\n\n  xMinutes: buildLocalizeTokenFn({\n    regular: {\n      singularNominative: \"{{count}} хвіліна\",\n      singularGenitive: \"{{count}} хвіліны\",\n      pluralGenitive: \"{{count}} хвілін\",\n    },\n    past: {\n      singularNominative: \"{{count}} хвіліну таму\",\n      singularGenitive: \"{{count}} хвіліны таму\",\n      pluralGenitive: \"{{count}} хвілін таму\",\n    },\n    future: {\n      singularNominative: \"праз {{count}} хвіліну\",\n      singularGenitive: \"праз {{count}} хвіліны\",\n      pluralGenitive: \"праз {{count}} хвілін\",\n    },\n  }),\n\n  aboutXHours: buildLocalizeTokenFn({\n    regular: {\n      singularNominative: \"каля {{count}} гадзіны\",\n      singularGenitive: \"каля {{count}} гадзін\",\n      pluralGenitive: \"каля {{count}} гадзін\",\n    },\n    future: {\n      singularNominative: \"прыблізна праз {{count}} гадзіну\",\n      singularGenitive: \"прыблізна праз {{count}} гадзіны\",\n      pluralGenitive: \"прыблізна праз {{count}} гадзін\",\n    },\n  }),\n\n  xHours: buildLocalizeTokenFn({\n    regular: {\n      singularNominative: \"{{count}} гадзіна\",\n      singularGenitive: \"{{count}} гадзіны\",\n      pluralGenitive: \"{{count}} гадзін\",\n    },\n    past: {\n      singularNominative: \"{{count}} гадзіну таму\",\n      singularGenitive: \"{{count}} гадзіны таму\",\n      pluralGenitive: \"{{count}} гадзін таму\",\n    },\n    future: {\n      singularNominative: \"праз {{count}} гадзіну\",\n      singularGenitive: \"праз {{count}} гадзіны\",\n      pluralGenitive: \"праз {{count}} гадзін\",\n    },\n  }),\n\n  xDays: buildLocalizeTokenFn({\n    regular: {\n      singularNominative: \"{{count}} дзень\",\n      singularGenitive: \"{{count}} дні\",\n      pluralGenitive: \"{{count}} дзён\",\n    },\n  }),\n\n  aboutXWeeks: buildLocalizeTokenFn({\n    regular: {\n      singularNominative: \"каля {{count}} тыдні\",\n      singularGenitive: \"каля {{count}} тыдняў\",\n      pluralGenitive: \"каля {{count}} тыдняў\",\n    },\n    future: {\n      singularNominative: \"прыблізна праз {{count}} тыдзень\",\n      singularGenitive: \"прыблізна праз {{count}} тыдні\",\n      pluralGenitive: \"прыблізна праз {{count}} тыдняў\",\n    },\n  }),\n\n  xWeeks: buildLocalizeTokenFn({\n    regular: {\n      singularNominative: \"{{count}} тыдзень\",\n      singularGenitive: \"{{count}} тыдні\",\n      pluralGenitive: \"{{count}} тыдняў\",\n    },\n  }),\n\n  aboutXMonths: buildLocalizeTokenFn({\n    regular: {\n      singularNominative: \"каля {{count}} месяца\",\n      singularGenitive: \"каля {{count}} месяцаў\",\n      pluralGenitive: \"каля {{count}} месяцаў\",\n    },\n    future: {\n      singularNominative: \"прыблізна праз {{count}} месяц\",\n      singularGenitive: \"прыблізна праз {{count}} месяцы\",\n      pluralGenitive: \"прыблізна праз {{count}} месяцаў\",\n    },\n  }),\n\n  xMonths: buildLocalizeTokenFn({\n    regular: {\n      singularNominative: \"{{count}} месяц\",\n      singularGenitive: \"{{count}} месяцы\",\n      pluralGenitive: \"{{count}} месяцаў\",\n    },\n  }),\n\n  aboutXYears: buildLocalizeTokenFn({\n    regular: {\n      singularNominative: \"каля {{count}} года\",\n      singularGenitive: \"каля {{count}} гадоў\",\n      pluralGenitive: \"каля {{count}} гадоў\",\n    },\n    future: {\n      singularNominative: \"прыблізна праз {{count}} год\",\n      singularGenitive: \"прыблізна праз {{count}} гады\",\n      pluralGenitive: \"прыблізна праз {{count}} гадоў\",\n    },\n  }),\n\n  xYears: buildLocalizeTokenFn({\n    regular: {\n      singularNominative: \"{{count}} год\",\n      singularGenitive: \"{{count}} гады\",\n      pluralGenitive: \"{{count}} гадоў\",\n    },\n  }),\n\n  overXYears: buildLocalizeTokenFn({\n    regular: {\n      singularNominative: \"больш за {{count}} год\",\n      singularGenitive: \"больш за {{count}} гады\",\n      pluralGenitive: \"больш за {{count}} гадоў\",\n    },\n    future: {\n      singularNominative: \"больш, чым праз {{count}} год\",\n      singularGenitive: \"больш, чым праз {{count}} гады\",\n      pluralGenitive: \"больш, чым праз {{count}} гадоў\",\n    },\n  }),\n\n  almostXYears: buildLocalizeTokenFn({\n    regular: {\n      singularNominative: \"амаль {{count}} год\",\n      singularGenitive: \"амаль {{count}} гады\",\n      pluralGenitive: \"амаль {{count}} гадоў\",\n    },\n    future: {\n      singularNominative: \"амаль праз {{count}} год\",\n      singularGenitive: \"амаль праз {{count}} гады\",\n      pluralGenitive: \"амаль праз {{count}} гадоў\",\n    },\n  }),\n};\n\nexport const formatDistance = (token, count, options) => {\n  options = options || {};\n  return formatDistanceLocale[token](count, options);\n};\n"], "mappings": "AAAA,SAASA,UAAUA,CAACC,MAAM,EAAEC,KAAK,EAAE;EACjC;EACA,IAAID,MAAM,CAACE,GAAG,KAAKC,SAAS,IAAIF,KAAK,KAAK,CAAC,EAAE;IAC3C,OAAOD,MAAM,CAACE,GAAG;EACnB;EAEA,MAAME,KAAK,GAAGH,KAAK,GAAG,EAAE;EACxB,MAAMI,MAAM,GAAGJ,KAAK,GAAG,GAAG;;EAE1B;EACA,IAAIG,KAAK,KAAK,CAAC,IAAIC,MAAM,KAAK,EAAE,EAAE;IAChC,OAAOL,MAAM,CAACM,kBAAkB,CAACC,OAAO,CAAC,WAAW,EAAEC,MAAM,CAACP,KAAK,CAAC,CAAC;;IAEpE;EACF,CAAC,MAAM,IAAIG,KAAK,IAAI,CAAC,IAAIA,KAAK,IAAI,CAAC,KAAKC,MAAM,GAAG,EAAE,IAAIA,MAAM,GAAG,EAAE,CAAC,EAAE;IACnE,OAAOL,MAAM,CAACS,gBAAgB,CAACF,OAAO,CAAC,WAAW,EAAEC,MAAM,CAACP,KAAK,CAAC,CAAC;;IAElE;EACF,CAAC,MAAM;IACL,OAAOD,MAAM,CAACU,cAAc,CAACH,OAAO,CAAC,WAAW,EAAEC,MAAM,CAACP,KAAK,CAAC,CAAC;EAClE;AACF;AAEA,SAASU,oBAAoBA,CAACX,MAAM,EAAE;EACpC,OAAO,CAACC,KAAK,EAAEW,OAAO,KAAK;IACzB,IAAIA,OAAO,IAAIA,OAAO,CAACC,SAAS,EAAE;MAChC,IAAID,OAAO,CAACE,UAAU,IAAIF,OAAO,CAACE,UAAU,GAAG,CAAC,EAAE;QAChD,IAAId,MAAM,CAACe,MAAM,EAAE;UACjB,OAAOhB,UAAU,CAACC,MAAM,CAACe,MAAM,EAAEd,KAAK,CAAC;QACzC,CAAC,MAAM;UACL,OAAO,OAAO,GAAGF,UAAU,CAACC,MAAM,CAACgB,OAAO,EAAEf,KAAK,CAAC;QACpD;MACF,CAAC,MAAM;QACL,IAAID,MAAM,CAACiB,IAAI,EAAE;UACf,OAAOlB,UAAU,CAACC,MAAM,CAACiB,IAAI,EAAEhB,KAAK,CAAC;QACvC,CAAC,MAAM;UACL,OAAOF,UAAU,CAACC,MAAM,CAACgB,OAAO,EAAEf,KAAK,CAAC,GAAG,OAAO;QACpD;MACF;IACF,CAAC,MAAM;MACL,OAAOF,UAAU,CAACC,MAAM,CAACgB,OAAO,EAAEf,KAAK,CAAC;IAC1C;EACF,CAAC;AACH;AAEA,MAAMiB,WAAW,GAAGA,CAACC,CAAC,EAAEP,OAAO,KAAK;EAClC,IAAIA,OAAO,IAAIA,OAAO,CAACC,SAAS,EAAE;IAChC,IAAID,OAAO,CAACE,UAAU,IAAIF,OAAO,CAACE,UAAU,GAAG,CAAC,EAAE;MAChD,OAAO,iBAAiB;IAC1B,CAAC,MAAM;MACL,OAAO,iBAAiB;IAC1B;EACF;EAEA,OAAO,YAAY;AACrB,CAAC;AAED,MAAMM,oBAAoB,GAAG;EAC3BC,gBAAgB,EAAEV,oBAAoB,CAAC;IACrCK,OAAO,EAAE;MACPd,GAAG,EAAE,iBAAiB;MACtBI,kBAAkB,EAAE,2BAA2B;MAC/CG,gBAAgB,EAAE,2BAA2B;MAC7CC,cAAc,EAAE;IAClB,CAAC;IACDK,MAAM,EAAE;MACNb,GAAG,EAAE,wBAAwB;MAC7BI,kBAAkB,EAAE,kCAAkC;MACtDG,gBAAgB,EAAE,kCAAkC;MACpDC,cAAc,EAAE;IAClB;EACF,CAAC,CAAC;EAEFY,QAAQ,EAAEX,oBAAoB,CAAC;IAC7BK,OAAO,EAAE;MACPV,kBAAkB,EAAE,mBAAmB;MACvCG,gBAAgB,EAAE,mBAAmB;MACrCC,cAAc,EAAE;IAClB,CAAC;IACDO,IAAI,EAAE;MACJX,kBAAkB,EAAE,wBAAwB;MAC5CG,gBAAgB,EAAE,wBAAwB;MAC1CC,cAAc,EAAE;IAClB,CAAC;IACDK,MAAM,EAAE;MACNT,kBAAkB,EAAE,wBAAwB;MAC5CG,gBAAgB,EAAE,wBAAwB;MAC1CC,cAAc,EAAE;IAClB;EACF,CAAC,CAAC;EAEFQ,WAAW,EAAEA,WAAW;EAExBK,gBAAgB,EAAEZ,oBAAoB,CAAC;IACrCK,OAAO,EAAE;MACPd,GAAG,EAAE,iBAAiB;MACtBI,kBAAkB,EAAE,2BAA2B;MAC/CG,gBAAgB,EAAE,2BAA2B;MAC7CC,cAAc,EAAE;IAClB,CAAC;IACDK,MAAM,EAAE;MACNb,GAAG,EAAE,wBAAwB;MAC7BI,kBAAkB,EAAE,kCAAkC;MACtDG,gBAAgB,EAAE,kCAAkC;MACpDC,cAAc,EAAE;IAClB;EACF,CAAC,CAAC;EAEFc,QAAQ,EAAEb,oBAAoB,CAAC;IAC7BK,OAAO,EAAE;MACPV,kBAAkB,EAAE,mBAAmB;MACvCG,gBAAgB,EAAE,mBAAmB;MACrCC,cAAc,EAAE;IAClB,CAAC;IACDO,IAAI,EAAE;MACJX,kBAAkB,EAAE,wBAAwB;MAC5CG,gBAAgB,EAAE,wBAAwB;MAC1CC,cAAc,EAAE;IAClB,CAAC;IACDK,MAAM,EAAE;MACNT,kBAAkB,EAAE,wBAAwB;MAC5CG,gBAAgB,EAAE,wBAAwB;MAC1CC,cAAc,EAAE;IAClB;EACF,CAAC,CAAC;EAEFe,WAAW,EAAEd,oBAAoB,CAAC;IAChCK,OAAO,EAAE;MACPV,kBAAkB,EAAE,wBAAwB;MAC5CG,gBAAgB,EAAE,uBAAuB;MACzCC,cAAc,EAAE;IAClB,CAAC;IACDK,MAAM,EAAE;MACNT,kBAAkB,EAAE,kCAAkC;MACtDG,gBAAgB,EAAE,kCAAkC;MACpDC,cAAc,EAAE;IAClB;EACF,CAAC,CAAC;EAEFgB,MAAM,EAAEf,oBAAoB,CAAC;IAC3BK,OAAO,EAAE;MACPV,kBAAkB,EAAE,mBAAmB;MACvCG,gBAAgB,EAAE,mBAAmB;MACrCC,cAAc,EAAE;IAClB,CAAC;IACDO,IAAI,EAAE;MACJX,kBAAkB,EAAE,wBAAwB;MAC5CG,gBAAgB,EAAE,wBAAwB;MAC1CC,cAAc,EAAE;IAClB,CAAC;IACDK,MAAM,EAAE;MACNT,kBAAkB,EAAE,wBAAwB;MAC5CG,gBAAgB,EAAE,wBAAwB;MAC1CC,cAAc,EAAE;IAClB;EACF,CAAC,CAAC;EAEFiB,KAAK,EAAEhB,oBAAoB,CAAC;IAC1BK,OAAO,EAAE;MACPV,kBAAkB,EAAE,iBAAiB;MACrCG,gBAAgB,EAAE,eAAe;MACjCC,cAAc,EAAE;IAClB;EACF,CAAC,CAAC;EAEFkB,WAAW,EAAEjB,oBAAoB,CAAC;IAChCK,OAAO,EAAE;MACPV,kBAAkB,EAAE,sBAAsB;MAC1CG,gBAAgB,EAAE,uBAAuB;MACzCC,cAAc,EAAE;IAClB,CAAC;IACDK,MAAM,EAAE;MACNT,kBAAkB,EAAE,kCAAkC;MACtDG,gBAAgB,EAAE,gCAAgC;MAClDC,cAAc,EAAE;IAClB;EACF,CAAC,CAAC;EAEFmB,MAAM,EAAElB,oBAAoB,CAAC;IAC3BK,OAAO,EAAE;MACPV,kBAAkB,EAAE,mBAAmB;MACvCG,gBAAgB,EAAE,iBAAiB;MACnCC,cAAc,EAAE;IAClB;EACF,CAAC,CAAC;EAEFoB,YAAY,EAAEnB,oBAAoB,CAAC;IACjCK,OAAO,EAAE;MACPV,kBAAkB,EAAE,uBAAuB;MAC3CG,gBAAgB,EAAE,wBAAwB;MAC1CC,cAAc,EAAE;IAClB,CAAC;IACDK,MAAM,EAAE;MACNT,kBAAkB,EAAE,gCAAgC;MACpDG,gBAAgB,EAAE,iCAAiC;MACnDC,cAAc,EAAE;IAClB;EACF,CAAC,CAAC;EAEFqB,OAAO,EAAEpB,oBAAoB,CAAC;IAC5BK,OAAO,EAAE;MACPV,kBAAkB,EAAE,iBAAiB;MACrCG,gBAAgB,EAAE,kBAAkB;MACpCC,cAAc,EAAE;IAClB;EACF,CAAC,CAAC;EAEFsB,WAAW,EAAErB,oBAAoB,CAAC;IAChCK,OAAO,EAAE;MACPV,kBAAkB,EAAE,qBAAqB;MACzCG,gBAAgB,EAAE,sBAAsB;MACxCC,cAAc,EAAE;IAClB,CAAC;IACDK,MAAM,EAAE;MACNT,kBAAkB,EAAE,8BAA8B;MAClDG,gBAAgB,EAAE,+BAA+B;MACjDC,cAAc,EAAE;IAClB;EACF,CAAC,CAAC;EAEFuB,MAAM,EAAEtB,oBAAoB,CAAC;IAC3BK,OAAO,EAAE;MACPV,kBAAkB,EAAE,eAAe;MACnCG,gBAAgB,EAAE,gBAAgB;MAClCC,cAAc,EAAE;IAClB;EACF,CAAC,CAAC;EAEFwB,UAAU,EAAEvB,oBAAoB,CAAC;IAC/BK,OAAO,EAAE;MACPV,kBAAkB,EAAE,wBAAwB;MAC5CG,gBAAgB,EAAE,yBAAyB;MAC3CC,cAAc,EAAE;IAClB,CAAC;IACDK,MAAM,EAAE;MACNT,kBAAkB,EAAE,+BAA+B;MACnDG,gBAAgB,EAAE,gCAAgC;MAClDC,cAAc,EAAE;IAClB;EACF,CAAC,CAAC;EAEFyB,YAAY,EAAExB,oBAAoB,CAAC;IACjCK,OAAO,EAAE;MACPV,kBAAkB,EAAE,qBAAqB;MACzCG,gBAAgB,EAAE,sBAAsB;MACxCC,cAAc,EAAE;IAClB,CAAC;IACDK,MAAM,EAAE;MACNT,kBAAkB,EAAE,0BAA0B;MAC9CG,gBAAgB,EAAE,2BAA2B;MAC7CC,cAAc,EAAE;IAClB;EACF,CAAC;AACH,CAAC;AAED,OAAO,MAAM0B,cAAc,GAAGA,CAACC,KAAK,EAAEpC,KAAK,EAAEW,OAAO,KAAK;EACvDA,OAAO,GAAGA,OAAO,IAAI,CAAC,CAAC;EACvB,OAAOQ,oBAAoB,CAACiB,KAAK,CAAC,CAACpC,KAAK,EAAEW,OAAO,CAAC;AACpD,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
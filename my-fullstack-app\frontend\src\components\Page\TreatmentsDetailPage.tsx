import { <PERSON><PERSON> } from "primereact/button";
import { Calendar } from "primereact/calendar";
import { Checkbox } from "primereact/checkbox";
import { Column } from 'primereact/column';
import { ConfirmDialog, confirmDialog } from 'primereact/confirmdialog';
import { DataTable } from 'primereact/datatable';
import { Dropdown } from 'primereact/dropdown';
import { FileUpload, FileUploadHandlerEvent } from "primereact/fileupload";
import { Image } from 'primereact/image';
import { InputNumber } from 'primereact/inputnumber';
import { InputTextarea } from "primereact/inputtextarea";
import { Stepper } from 'primereact/stepper';
import { StepperPanel } from 'primereact/stepperpanel';
import { Toast } from "primereact/toast";
import React, { useEffect, useRef, useState } from "react";
import { useLocation, useNavigate } from "react-router-dom";
import useDataType from "../../hooks/useDataType";
import api from "../../services/api";
import imagepath from "../../services/imagepath";

interface DiscomfortArea {
  id?: number;
  frontAndBack: string;
  discomfortArea: string;
  discomfortSituation: string;
  discomfortDegree: number;
}

interface Treatment {
  orderNo: string;
  step: number;
  discomfortPeriod: string;
  possibleCauses: string;
  treatmentHistory: string;
  howToKnowOur: string;
  hospitalFormUrl: string;
  treatmentConsentFormUrl: string;
  subjective: string;
  objective: string;
  assessment: string;
  plan: string;
  patientId: number;
  hospitalFormRecordDate?: Date | null;
  discomfortAreas: DiscomfortArea[];
}

const TreatmentsDetailPage: React.FC = () => {
  const location = useLocation();
  const patient = location.state?.patient;
  const treatment = location.state?.treatment;
  const toast = useRef<Toast>(null);
  const stepperRef = useRef<Stepper>(null);
  const [currentStep, setCurrentStep] = useState(0);
  const navigate = useNavigate();
  const { dataType, loading } = useDataType();
  const [formData, setFormData] = useState<Treatment>({
    orderNo: "",
    step: 0,
    discomfortPeriod: "",
    possibleCauses: "",
    treatmentHistory: "",
    howToKnowOur: "",
    hospitalFormUrl: "",
    treatmentConsentFormUrl: "",
    subjective: "",
    objective: "",
    assessment: "",
    plan: "",
    patientId: patient?.id || 0,
    hospitalFormRecordDate: null,
    discomfortAreas: []
  });

  // 新增不適區域的狀態
  const [currentDiscomfortArea, setCurrentDiscomfortArea] = useState<DiscomfortArea>({
    frontAndBack: "",
    discomfortArea: "",
    discomfortSituation: "",
    discomfortDegree: 0
  });

  useEffect(() => {
      if (treatment) {
          setFormData({
            ...treatment,
            hospitalFormRecordDate: treatment.hospitalFormRecordDate ? new Date(treatment.hospitalFormRecordDate) : null,
            });

          if(treatment.step === 20){
            setCurrentStep(1)
          }
          if(treatment.step === 30){
            setCurrentStep(2)
          }
      }
    }, [treatment]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
          const { name, value } = e.target;
          setFormData((prev) => ({ ...prev, [name]: value }));
    };

  const handleCheckboxChange = (name: keyof Treatment, value: string, checked: boolean) => {
    setFormData(prev => {
      const current = (prev[name] || "") as string;
      const currentArr = current ? current.split(",") : [];

      const updatedArr = checked
        ? currentArr.includes(value)
          ? currentArr
          : [...currentArr, value]
        : currentArr.filter(item => item !== value);

    return { ...prev, [name]: updatedArr.join(",") };
    });
  };

  const handleDateChange = (value: Date | null | undefined) => {
    setFormData(prev => ({ ...prev, hospitalFormRecordDate: value || null }));
  };

  // 處理不適區域的函數
  const handleDiscomfortAreaChange = (field: keyof DiscomfortArea, value: any) => {
    setCurrentDiscomfortArea(prev => ({ ...prev, [field]: value }));
  };

  const addDiscomfortArea = () => {
    if (!currentDiscomfortArea.frontAndBack || !currentDiscomfortArea.discomfortArea || !currentDiscomfortArea.discomfortSituation) {
      toast.current?.show({ severity: "error", summary: "新增失敗", detail: "請填寫完整的不適區域資訊" });
      return;
    }

    if (formData.discomfortAreas.length >= 5) {
      toast.current?.show({ severity: "error", summary: "新增失敗", detail: "不適區域最多只能新增 5 筆資料" });
      return;
    }

    setFormData(prev => ({
      ...prev,
      discomfortAreas: [...prev.discomfortAreas, { ...currentDiscomfortArea }]
    }));

    // 清空當前輸入
    setCurrentDiscomfortArea({
      frontAndBack: "",
      discomfortArea: "",
      discomfortSituation: "",
      discomfortDegree: 0
    });

    toast.current?.show({ severity: "success", summary: "新增成功", detail: "不適區域已新增" });
  };

  const removeDiscomfortArea = (index: number) => {
    setFormData(prev => ({
      ...prev,
      discomfortAreas: prev.discomfortAreas.filter((_, i) => i !== index)
    }));
    toast.current?.show({ severity: "success", summary: "刪除成功", detail: "不適區域已刪除" });
  };

  const copyLatestRecord = () => {
    
    confirmDialog({
      message: '是否複製上一筆診療紀錄',
      header: '複製確認',
      icon: 'pi pi-exclamation-triangle',
      accept: async () => {
        try {
          const response = await api.get(`/api/Treatment/GetLatestRecord/${patient?.id}`, {
            method: 'GET',
            headers: {
              'Content-Type': 'application/json',
            },
          });

          if (response) {
            const latestRecord = await response.data;
            setFormData(prev => ({
              ...prev,
              discomfortPeriod: latestRecord.discomfortPeriod || "",
              possibleCauses: latestRecord.possibleCauses || "",
              treatmentHistory: latestRecord.treatmentHistory || "",
              howToKnowOur: latestRecord.howToKnowOur || "",
              subjective: latestRecord.subjective || "",
              objective: latestRecord.objective || "",
              assessment: latestRecord.assessment || "",
              plan: latestRecord.plan || "",
              discomfortAreas: latestRecord.discomfortAreas || []
            }));
            toast.current?.show({ severity: "success", summary: "複製成功", detail: "已複製上一筆診療紀錄" });
            
          } 
        } catch (error:any) {
          toast.current?.show({ severity: "error", summary: "複製失敗", detail: error.details });
        }
      }
    });
  };

  const handleSubmit = async (step:number) => {

    var detail = "治療資料已更新";

    if(step === 40){
      if(formData.hospitalFormUrl === '' && formData.treatmentConsentFormUrl === ''){
        toast.current?.show({ severity: "error", summary: "結案失敗", detail: "請上傳治療同意書或醫院診斷書"})
        return
      }
      if(formData.hospitalFormUrl !== '' && !formData.hospitalFormRecordDate){
        toast.current?.show({ severity: "error", summary: "結案失敗", detail: "請填寫醫院診斷書開立時間"})
        return
      }
    }

    if (formData.orderNo === "") {
        // 新增模式
        await api.post("/api/treatment/Insert", formData)
        .then((res) => {
          setFormData(
            (prev) => ({...prev,orderNo: res.data.orderNo})
          ); 
          toast.current?.show({ severity: "success", summary: "成功", detail: res.data.msg }) } )
        .catch((err) => toast.current?.show({ severity: "error", summary: "新增失敗", detail: err.details}) );     
        
    }
    else{
      // 編輯模式
      formData.step = formData.step > step ? formData.step : step;
      setFormData(
            (prev) => ({...prev,step: formData.step})
          ); 
      await api.put("/api/treatment/Update", formData)
        .then(() => toast.current?.show({ severity: "success", summary: "成功", detail: detail }) )
        .catch((err) => toast.current?.show({ severity: "error", summary: "更新失敗", detail: err.details}) );  
    }

    if(step === 40){
      setTimeout(() => navigate("/treatments"), 1500); // 送出後導回列表頁
    }
  };

  // 上傳
  const handleCustomUpload = async (event: FileUploadHandlerEvent, type: 'hospital' | 'consent') => {
    const file = event.files?.[0];
    if (!file) return;

    if (!formData.orderNo) {
      toast.current?.show({
        severity: "error",
        summary: "上傳失敗",
        detail: "請先儲存治療記錄，取得單號後再上傳檔案。",
      });
      return;
    }

    const formDataToSend = new FormData();
    formDataToSend.append("file", file);
    formDataToSend.append("orderNo", formData.orderNo);

    try {
      const response = await api.post("/api/system/UploadFile", formDataToSend, {
        headers: {
          "Content-Type": "multipart/form-data",
        },
      });

      const { fileName } = response.data;

      // 更新 formData
      const updatedFormData = {
        ...formData,
        [type === 'hospital' ? 'hospitalFormUrl' : 'treatmentConsentFormUrl']: fileName,
      };

      // 更新 UI
      setFormData(updatedFormData);

      // 直接呼叫更新 API
      await api.put("/api/treatment/Update", updatedFormData);

      toast.current?.show({
        severity: "success",
        summary: "成功",
        detail: "檔案已上傳並更新記錄。",
      });

    } catch (error: any) {
      const errorMessage = error.response?.data?.message || error.message || "檔案上傳失敗";
      toast.current?.show({
        severity: "error",
        summary: "上傳失敗",
        detail: errorMessage,
      });
    }
  };

  const getOptions = (groupId: number) => {
    return dataType.find(group => group.groupId === groupId)?.dataTypes.map(item => ({
      label: item.name,
      value: item.name
    })) || [];
  };

  const checkStep = (step:number) => {
      if (step === 20 && formData.orderNo === ""){
        toast.current?.show({ severity: "error", summary: "錯誤", detail: "初次開案請存檔" })
      }else{
        stepperRef.current?.nextCallback()
      }
    };

  if (loading) return <p>Loading...</p>;

  return (
    <div className="p-4">
      <Toast ref={toast} />
      <ConfirmDialog />

      <div className="card flex justify-content-center">
        <Stepper ref={stepperRef} activeStep={currentStep} style={{ flexBasis: '100%' }}>
            <StepperPanel header="症狀描述">
              <div className="flex flex-column">
                <div className="grid formgrid p-fluid gap-3 justify-content-center">

                  <div className="col-5 md:col-5" hidden>
                    <label>patientId</label>
                    <div className="flex flex-wrap gap-3">
                      <InputTextarea  name="patientId" rows={1} value={formData.patientId?.toString()} onChange={handleChange} />
                    </div>
                  </div>
                  <div className="col-5 md:col-5" hidden>
                    <label>orderNo</label>
                    <div className="flex flex-wrap gap-3">
                      <InputTextarea  name="orderNo" rows={1} value={formData.orderNo?.toString()} onChange={handleChange} />
                    </div>
                  </div>


                  {/* 不適區域新增區域 */}
                  <div className="col-12 md:col-6">
                    <h4>新增不適區域</h4>
                    <div className="grid formgrid p-fluid">
                      <div className="col-12 flex flex-wrap justify-content-between">
                        <div className="col-12 md:col-4 flex justify-content-center">
                        <Image src="/images/image-body.jpg" alt="Image" width="250" />
                        </div>
                        <div className="col-12 md:col-8 flex justify-content-center align-items-center" >
                        <Image src="/images/NumericalRaringAcale.png" alt="Image" imageStyle={{ width: "100%", maxWidth: "550px", height: "auto" }} />
                        </div>
                      </div>
                      <div className="col-12 flex flex-wrap">
                        <div className="col-6 md:col-4">
                          <label className="font-bold block pt-2 mb-2">前側/後側</label>
                          <Dropdown
                            value={currentDiscomfortArea.frontAndBack}
                            onChange={(e) => handleDiscomfortAreaChange('frontAndBack', e.value)}
                            options={getOptions(1)}
                            optionLabel="label"
                            optionValue="value"
                            placeholder="選擇前側/後側"
                          />
                        </div>
                        <div className="col-6 md:col-4">
                          <label className="font-bold block pt-2 mb-2">不適區域</label>
                          <Dropdown
                            value={currentDiscomfortArea.discomfortArea}
                            onChange={(e) => handleDiscomfortAreaChange('discomfortArea', e.value)}
                            options={getOptions(2)}
                            optionLabel="label"
                            optionValue="value"
                            placeholder="選擇不適區域"
                          />
                        </div>
                        <div className="col-6 md:col-4">
                          <label className="font-bold block pt-2 mb-2">不適情況</label>
                          <Dropdown
                            value={currentDiscomfortArea.discomfortSituation}
                            onChange={(e) => handleDiscomfortAreaChange('discomfortSituation', e.value)}
                            options={getOptions(3)}
                            optionLabel="label"
                            optionValue="value"
                            placeholder="選擇不適情況"
                          />
                        </div>
                      </div>
                      <div className="col-12 flex">
                        <div className="col-6 md:col-4">
                          <label className="font-bold block pt-2 mb-2">疼痛指數</label>
                          <InputNumber
                            value={currentDiscomfortArea.discomfortDegree}
                            onValueChange={(e) => handleDiscomfortAreaChange('discomfortDegree', e.value || 0)}
                            min={0}
                            max={10}
                            placeholder="0-10"
                          />
                        </div>
                        <div className="col-4 md:col-2">
                          <label className="font-bold block pt-2 mb-2">&nbsp;</label>
                          <Button
                            label="新增"
                            icon="pi pi-plus"
                            onClick={addDiscomfortArea}
                            disabled={formData.discomfortAreas.length >= 5}
                          />
                        </div>
                      </div>
                      
                    </div>
                  </div>

                  {/* 不適區域列表 */}
                  <div className="col-12 md:col-5">
                    <h4>不適區域列表 ({formData.discomfortAreas.length}/5)</h4>
                    <DataTable value={formData.discomfortAreas} emptyMessage="尚無不適區域資料">
                      <Column field="frontAndBack" header="前側/後側" />
                      <Column field="discomfortArea" header="不適區域" />
                      <Column field="discomfortSituation" header="不適情況" />
                      <Column field="discomfortDegree" header="疼痛指數" />
                      <Column
                        header="操作"
                        body={(_, options) => (
                          <Button
                            icon="pi pi-trash"
                            className="p-button-rounded p-button-danger p-button-text"
                            onClick={() => removeDiscomfortArea(options.rowIndex)}
                          />
                        )}
                      />
                    </DataTable>
                  </div>

                  <div className="col-12 md:col-6">
                    <label className="font-bold block mb-2">可能引發原因(可複選)</label>
                    <div className="flex flex-wrap gap-3 pb-2">
                    {getOptions(5).map(option => (
                      <div key={option.value} className="flex align-items-center">
                        <Checkbox
                          inputId={`front-${option.value}`}
                          value={option.value}
                          onChange={(e) => handleCheckboxChange("possibleCauses", option.value, e.checked?? false)}
                          checked={formData.possibleCauses.split(",").includes(option.value)}
                        />
                        <label htmlFor={`front-${option.value}`} className="ml-2">{option.label}</label>
                      </div>
                    ))}
                    </div>
                    <InputTextarea  name="possibleCauses" rows={1} value={formData.possibleCauses} onChange={handleChange} />
                  </div>

                  <div className="col-12 md:col-5">
                    <label className="font-bold block mb-2">不適時間</label>
                    <div className="flex flex-wrap gap-3 pb-2">
                    {getOptions(4).map(option => (
                      <div key={option.value} className="flex align-items-center">
                        <Checkbox
                          inputId={`front-${option.value}`}
                          value={option.value}
                          onChange={() => setFormData(prev => ({ ...prev, discomfortPeriod: option.value }))}
                          checked={formData.discomfortPeriod === option.value}
                        />
                        <label htmlFor={`front-${option.value}`} className="ml-2">{option.label}</label>
                      </div>
                    ))}
                    </div>
                  </div>

                  <div className="col-12 md:col-6">
                    <label className="font-bold block mb-2">曾接受過相關處置</label>
                    <div className="flex flex-wrap gap-3 pb-2">
                      {getOptions(6).map(option => (
                        <div key={option.value} className="flex align-items-center">
                          <Checkbox
                            inputId={`front-${option.value}`}
                            value={option.value}
                            onChange={(e) => handleCheckboxChange("treatmentHistory", option.value, e.checked?? false)}
                            checked={formData.treatmentHistory.split(",").includes(option.value)}
                          />
                          <label htmlFor={`front-${option.value}`} className="ml-2">{option.label}</label>
                        </div>
                      ))}
                    </div>
                    <InputTextarea  name="treatmentHistory" rows={1} value={formData.treatmentHistory} onChange={handleChange} />
                  </div>

                  <div className="col-12 md:col-5">
                    <label className="font-bold block mb-2">如何知道我們院所</label>
                    <div className="flex flex-wrap gap-3 pb-2">
                    {getOptions(7).map(option => (
                      <div key={option.value} className="flex align-items-center">
                        <Checkbox
                          inputId={`front-${option.value}`}
                          value={option.value}
                          onChange={() => setFormData(prev => ({ ...prev, howToKnowOur: option.value }))}
                          checked={formData.howToKnowOur === option.value}
                        />
                        <label htmlFor={`front-${option.value}`} className="ml-2">{option.label}</label>
                      </div>
                    ))}
                    </div>
                    <InputTextarea  name="howToKnowOur" rows={1} value={formData.howToKnowOur} onChange={handleChange} />
                  </div>
                </div>  
              </div>
              <div className="flex pt-4 justify-content-between">
                  <Button label="複製" severity="info" icon="pi pi-copy" iconPos="left" onClick={copyLatestRecord} />
                  <div className="flex gap-2">
                    <Button label="存檔" severity="success" icon="pi pi-upload" onClick={() => handleSubmit(10)} />
                  </div>
                  <Button label="下一步" icon="pi pi-arrow-right" iconPos="right" onClick={() => checkStep(20)} />
              </div>
            </StepperPanel>

            <StepperPanel header="治療師診療">
              <div className="flex flex-column">
                <div className="grid formgrid p-fluid gap-3 justify-content-center">
                  <div className="col-12 md:col-5">
                    <label className="font-bold block mb-2">主觀症狀 (S)</label>
                    <InputTextarea name="subjective" rows={6} value={formData.subjective} onChange={handleChange} />
                  </div>

                  <div className="col-12 md:col-5">
                    <label className="font-bold block mb-2">客觀檢查 (O)</label>
                    <InputTextarea name="objective" rows={6} value={formData.objective} onChange={handleChange} />
                  </div>

                  <div className="col-12 md:col-5">
                    <label className="font-bold block mb-2">專業判斷 (A)</label>
                    <InputTextarea name="assessment" rows={6} value={formData.assessment} onChange={handleChange} />
                  </div>

                  <div className="col-12 md:col-5">
                    <label className="font-bold block mb-2">治療計畫 (P)</label>
                    <InputTextarea name="plan" rows={6} value={formData.plan} onChange={handleChange} />
                  </div>
                </div>
              </div>
              <div className="flex pt-4 justify-content-between">
                  <Button label="上一步" severity="secondary" icon="pi pi-arrow-left" onClick={() => stepperRef.current?.prevCallback()} />
                  <Button label="存檔" severity="success" icon="pi pi-upload" onClick={() => handleSubmit(20)} />
                  <Button label="下一步" icon="pi pi-arrow-right" iconPos="right" onClick={() => checkStep(30)} />
              </div>
            </StepperPanel>

            <StepperPanel header="檔案上傳">
              <div className="flex flex-column ">
                <div className="grid formgrid p-fluid gap-3 justify-content-center">
                  <div className="col-12 md:col-5">
                    <label className="font-bold block mb-2">治療同意書</label>
                    <div className="flex flex-column h-15rem">
                        <div className="border-2 border-dashed surface-border border-round surface-ground flex-auto flex justify-content-center align-items-center font-medium">
                          {formData.treatmentConsentFormUrl ? (
                            <Image src={imagepath+formData.treatmentConsentFormUrl} indicatorIcon={<i className="pi pi-search"></i>} alt="Image" width="100%" height="230rem" preview />
                          ) : (
                            <FileUpload
                              mode="basic"
                              name="TreatmentConsentFormUrl"
                              customUpload
                              uploadHandler={(e) => handleCustomUpload(e, 'consent')}
                              accept="image/*"
                              maxFileSize={1000000}
                              chooseLabel="選擇檔案"
                            />
                          )}
                        </div>
                    </div>
                  </div>
                    <div>

                    </div>
                  <div className="col-12 md:col-5">
                    <div className="col-12 md:col-5"></div>
                    <label className="font-bold block mb-2">醫院診斷書</label>
                    <div className="flex flex-column h-15rem">
                        <div className="border-2 border-dashed surface-border border-round surface-ground flex-auto flex justify-content-center align-items-center font-medium">
                          {formData.hospitalFormUrl ? (
                            <Image  src={imagepath+formData.hospitalFormUrl} indicatorIcon={<i className="pi pi-search"></i>} alt="Image" width="100%" height="230rem" preview />
                          ) : (
                            <FileUpload
                              mode="basic"
                              name="HospitalFormUrl"
                              customUpload
                              uploadHandler={(e) => handleCustomUpload(e, 'hospital')}
                              accept="image/*"
                              maxFileSize={1000000}
                              chooseLabel="選擇檔案"
                            />
                          )}
                        </div>
                    </div>
                  </div>
                </div>
                <div className="grid formgrid p-fluid gap-3 pt-2 justify-content-center">
                  <div className="col-12 md:col-5"></div>
                  <div className="col-12 md:col-5 flex justify-content-end">
                    <div className="col-10 md:col-4">
                    <label className="font-bold block mb-2">醫院診斷書開立時間</label>
                    <Calendar
                      value={formData.hospitalFormRecordDate}
                      onChange={(e) => handleDateChange(e.value)}
                      showIcon
                      dateFormat="yy/mm/dd"
                      placeholder="選擇日期"
                    />
                    </div>
                  </div>
                </div>
              </div>
              <div className="flex pt-4 justify-content-between">
                  <Button label="上一步" severity="secondary" icon="pi pi-arrow-left" onClick={() => stepperRef.current?.prevCallback()} />
                  <Button label="結案" severity="success" icon="pi pi-check" iconPos="right" onClick={() => handleSubmit(40)} />
              </div>
            </StepperPanel>
        </Stepper>
    </div>


    </div>
    
  );
};

export default TreatmentsDetailPage;
{"ast": null, "code": "const formatRelativeLocale = {\n  lastWeek: \"eeee 'שעבר בשעה' p\",\n  yesterday: \"'אתמול בשעה' p\",\n  today: \"'היום בשעה' p\",\n  tomorrow: \"'מחר בשעה' p\",\n  nextWeek: \"eeee 'בשעה' p\",\n  other: \"P\"\n};\nexport const formatRelative = (token, _date, _baseDate, _options) => formatRelativeLocale[token];", "map": {"version": 3, "names": ["formatRelativeLocale", "lastWeek", "yesterday", "today", "tomorrow", "nextWeek", "other", "formatRelative", "token", "_date", "_baseDate", "_options"], "sources": ["C:/Users/<USER>/Desktop/demo-git/demo-react/my-fullstack-app/frontend/node_modules/date-fns/locale/he/_lib/formatRelative.js"], "sourcesContent": ["const formatRelativeLocale = {\n  lastWeek: \"eeee 'שעבר בשעה' p\",\n  yesterday: \"'אתמול בשעה' p\",\n  today: \"'היום בשעה' p\",\n  tomorrow: \"'מחר בשעה' p\",\n  nextWeek: \"eeee 'בשעה' p\",\n  other: \"P\",\n};\n\nexport const formatRelative = (token, _date, _baseDate, _options) =>\n  formatRelativeLocale[token];\n"], "mappings": "AAAA,MAAMA,oBAAoB,GAAG;EAC3BC,QAAQ,EAAE,oBAAoB;EAC9BC,SAAS,EAAE,gBAAgB;EAC3BC,KAAK,EAAE,eAAe;EACtBC,QAAQ,EAAE,cAAc;EACxBC,QAAQ,EAAE,eAAe;EACzBC,KAAK,EAAE;AACT,CAAC;AAED,OAAO,MAAMC,cAAc,GAAGA,CAACC,KAAK,EAAEC,KAAK,EAAEC,SAAS,EAAEC,QAAQ,KAC9DX,oBAAoB,CAACQ,KAAK,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "import{But<PERSON>}from\"primereact/button\";import{FileUpload}from\"primereact/fileupload\";import{Image}from'primereact/image';import{InputText}from\"primereact/inputtext\";import{Toast}from\"primereact/toast\";import{useRef,useState}from\"react\";import api from\"../../services/api\";import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";export default function DebugPage(){const toast=useRef(null);const[previewUrl,setPreviewUrl]=useState(null);const[value,setValue]=useState('');// 上傳\nconst handleCustomUpload=async event=>{var _event$files;const file=(_event$files=event.files)===null||_event$files===void 0?void 0:_event$files[0];if(!file)return;const formData=new FormData();formData.append(\"file\",file);// 注意：這裡的 key 要和後端接收的參數一致\ntry{var _toast$current;const response=await api.post(\"/api/system/UploadFile\",formData,{headers:{\"Content-Type\":\"multipart/form-data\"}});console.log(response.data);(_toast$current=toast.current)===null||_toast$current===void 0?void 0:_toast$current.show({severity:\"success\",summary:\"成功\",detail:\"檔案已上傳\"});}catch(error){var _toast$current2;(_toast$current2=toast.current)===null||_toast$current2===void 0?void 0:_toast$current2.show({severity:\"error\",summary:\"錯誤\",detail:\"上傳失敗\"});}};// 下載\nconst handleDownload=async fileName=>{try{var _toast$current3;const response=await api.get(\"/api/system/DownloadFile\",{params:{filename:fileName},responseType:\"blob\"});const url=window.URL.createObjectURL(new Blob([response.data]));const link=document.createElement(\"a\");link.href=url;link.setAttribute(\"download\",fileName);document.body.appendChild(link);link.click();document.body.removeChild(link);(_toast$current3=toast.current)===null||_toast$current3===void 0?void 0:_toast$current3.show({severity:\"success\",summary:\"下載完成\",detail:fileName});}catch(error){var _toast$current4;(_toast$current4=toast.current)===null||_toast$current4===void 0?void 0:_toast$current4.show({severity:\"error\",summary:\"下載失敗\",detail:String(error)});}};// 預覽\nconst handlePreview=async fileName=>{try{const response=await api.get(\"/api/system/DownloadFile\",{params:{filename:fileName},responseType:\"blob\"});const url=window.URL.createObjectURL(new Blob([response.data]));setPreviewUrl(url);}catch(error){var _toast$current5;(_toast$current5=toast.current)===null||_toast$current5===void 0?void 0:_toast$current5.show({severity:\"error\",summary:\"預覽失敗\",detail:String(error)});}};return/*#__PURE__*/_jsxs(\"div\",{className:\"card flex justify-content-center\",children:[/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(Toast,{ref:toast}),/*#__PURE__*/_jsx(FileUpload,{mode:\"basic\",customUpload:true,uploadHandler:handleCustomUpload,accept:\"image/*\",maxFileSize:1000000,chooseLabel:\"\\u9078\\u64C7\\u6A94\\u6848\"})]}),/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex gap-2\",children:[/*#__PURE__*/_jsx(InputText,{value:value,onChange:e=>setValue(e.target.value)}),/*#__PURE__*/_jsx(Button,{label:\"\\u4E0B\\u8F09\",icon:\"pi pi-download\",onClick:()=>handleDownload(value),severity:\"success\"}),/*#__PURE__*/_jsx(Button,{label:\"\\u9810\\u89BD\",icon:\"pi pi-eye\",onClick:()=>handlePreview(value),severity:\"info\"})]})}),/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsx(\"div\",{children:previewUrl&&/*#__PURE__*/_jsx(Image,{src:previewUrl,alt:\"\\u9810\\u89BD\\u5716\\u7247\",width:\"250\",preview:true})})})]});}", "map": {"version": 3, "names": ["<PERSON><PERSON>", "FileUpload", "Image", "InputText", "Toast", "useRef", "useState", "api", "jsx", "_jsx", "jsxs", "_jsxs", "DebugPage", "toast", "previewUrl", "setPreviewUrl", "value", "setValue", "handleCustomUpload", "event", "_event$files", "file", "files", "formData", "FormData", "append", "_toast$current", "response", "post", "headers", "console", "log", "data", "current", "show", "severity", "summary", "detail", "error", "_toast$current2", "handleDownload", "fileName", "_toast$current3", "get", "params", "filename", "responseType", "url", "window", "URL", "createObjectURL", "Blob", "link", "document", "createElement", "href", "setAttribute", "body", "append<PERSON><PERSON><PERSON>", "click", "<PERSON><PERSON><PERSON><PERSON>", "_toast$current4", "String", "handlePreview", "_toast$current5", "className", "children", "ref", "mode", "customUpload", "uploadHandler", "accept", "maxFileSize", "<PERSON><PERSON><PERSON><PERSON>", "onChange", "e", "target", "label", "icon", "onClick", "src", "alt", "width", "preview"], "sources": ["C:/Users/<USER>/Desktop/demo-git/demo-react/my-fullstack-app/frontend/src/components/Page/DebugPage.tsx"], "sourcesContent": ["import { <PERSON><PERSON> } from \"primereact/button\";\r\nimport { FileUpload, FileUploadHandlerEvent } from \"primereact/fileupload\";\r\nimport { Image } from 'primereact/image';\r\nimport { InputText } from \"primereact/inputtext\";\r\nimport { Toast } from \"primereact/toast\";\r\nimport { useRef, useState } from \"react\";\r\nimport api from \"../../services/api\";\r\n\r\nexport default function DebugPage() {\r\n    const toast = useRef<Toast>(null);\r\n    const [previewUrl, setPreviewUrl] = useState<string | null>(null);\r\n    const [value, setValue] = useState('');\r\n\r\n    // 上傳\r\n    const handleCustomUpload = async (event: FileUploadHandlerEvent) => {\r\n        const file = event.files?.[0];\r\n        if (!file) return;\r\n\r\n        const formData = new FormData();\r\n        formData.append(\"file\", file); // 注意：這裡的 key 要和後端接收的參數一致\r\n\r\n        try {\r\n            const response = await api.post(\"/api/system/UploadFile\", formData, {\r\n                headers: {\r\n                    \"Content-Type\": \"multipart/form-data\",\r\n                },\r\n            });\r\n\r\n            console.log(response.data);\r\n\r\n            toast.current?.show({\r\n                severity: \"success\",\r\n                summary: \"成功\",\r\n                detail: \"檔案已上傳\",\r\n            });\r\n        } catch (error) {\r\n            toast.current?.show({\r\n                severity: \"error\",\r\n                summary: \"錯誤\",\r\n                detail: \"上傳失敗\",\r\n            });\r\n        }\r\n    };\r\n\r\n    // 下載\r\n    const handleDownload = async (fileName: string) => {\r\n        try {\r\n            const response = await api.get(`/api/system/DownloadFile`, {\r\n                params: { filename: fileName },\r\n                responseType: \"blob\",\r\n            });\r\n\r\n            const url = window.URL.createObjectURL(new Blob([response.data]));\r\n            const link = document.createElement(\"a\");\r\n            link.href = url;\r\n            link.setAttribute(\"download\", fileName);\r\n            document.body.appendChild(link);\r\n            link.click();\r\n            document.body.removeChild(link);\r\n\r\n            toast.current?.show({\r\n                severity: \"success\",\r\n                summary: \"下載完成\",\r\n                detail: fileName,\r\n            });\r\n        } catch (error) {\r\n            toast.current?.show({\r\n                severity: \"error\",\r\n                summary: \"下載失敗\",\r\n                detail: String(error),\r\n            });\r\n        }\r\n    };\r\n\r\n    // 預覽\r\n    const handlePreview = async (fileName: string) => {\r\n        try {\r\n            const response = await api.get(`/api/system/DownloadFile`, {\r\n                params: { filename: fileName },\r\n                responseType: \"blob\",\r\n            });\r\n\r\n            const url = window.URL.createObjectURL(new Blob([response.data]));\r\n            setPreviewUrl(url);\r\n        } catch (error) {\r\n            toast.current?.show({\r\n                severity: \"error\",\r\n                summary: \"預覽失敗\",\r\n                detail: String(error),\r\n            });\r\n        }\r\n    };\r\n\r\n    return (\r\n        <div className=\"card flex justify-content-center\">\r\n            <div>\r\n            <Toast ref={toast} />\r\n            <FileUpload\r\n                mode=\"basic\"\r\n                customUpload\r\n                uploadHandler={handleCustomUpload}\r\n                accept=\"image/*\"\r\n                maxFileSize={1000000}\r\n                chooseLabel=\"選擇檔案\"\r\n            />\r\n            </div>\r\n            <div>\r\n                <div className=\"flex gap-2\">\r\n                    <InputText value={value} onChange={(e) => setValue(e.target.value)} />\r\n                    <Button label=\"下載\" icon=\"pi pi-download\" onClick={() =>handleDownload(value)} severity=\"success\" />\r\n                    <Button label=\"預覽\" icon=\"pi pi-eye\" onClick={() =>handlePreview(value)} severity=\"info\" />\r\n                </div>\r\n            </div>\r\n            <div>\r\n                <div>\r\n                    {previewUrl && (\r\n                        <Image src={previewUrl} alt=\"預覽圖片\" width=\"250\" preview />\r\n                    )}\r\n                </div>\r\n            </div>\r\n        </div>\r\n        \r\n    );\r\n}"], "mappings": "AAAA,OAASA,MAAM,KAAQ,mBAAmB,CAC1C,OAASC,UAAU,KAAgC,uBAAuB,CAC1E,OAASC,KAAK,KAAQ,kBAAkB,CACxC,OAASC,SAAS,KAAQ,sBAAsB,CAChD,OAASC,KAAK,KAAQ,kBAAkB,CACxC,OAASC,MAAM,CAAEC,QAAQ,KAAQ,OAAO,CACxC,MAAO,CAAAC,GAAG,KAAM,oBAAoB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAErC,cAAe,SAAS,CAAAC,SAASA,CAAA,CAAG,CAChC,KAAM,CAAAC,KAAK,CAAGR,MAAM,CAAQ,IAAI,CAAC,CACjC,KAAM,CAACS,UAAU,CAAEC,aAAa,CAAC,CAAGT,QAAQ,CAAgB,IAAI,CAAC,CACjE,KAAM,CAACU,KAAK,CAAEC,QAAQ,CAAC,CAAGX,QAAQ,CAAC,EAAE,CAAC,CAEtC;AACA,KAAM,CAAAY,kBAAkB,CAAG,KAAO,CAAAC,KAA6B,EAAK,KAAAC,YAAA,CAChE,KAAM,CAAAC,IAAI,EAAAD,YAAA,CAAGD,KAAK,CAACG,KAAK,UAAAF,YAAA,iBAAXA,YAAA,CAAc,CAAC,CAAC,CAC7B,GAAI,CAACC,IAAI,CAAE,OAEX,KAAM,CAAAE,QAAQ,CAAG,GAAI,CAAAC,QAAQ,CAAC,CAAC,CAC/BD,QAAQ,CAACE,MAAM,CAAC,MAAM,CAAEJ,IAAI,CAAC,CAAE;AAE/B,GAAI,KAAAK,cAAA,CACA,KAAM,CAAAC,QAAQ,CAAG,KAAM,CAAApB,GAAG,CAACqB,IAAI,CAAC,wBAAwB,CAAEL,QAAQ,CAAE,CAChEM,OAAO,CAAE,CACL,cAAc,CAAE,qBACpB,CACJ,CAAC,CAAC,CAEFC,OAAO,CAACC,GAAG,CAACJ,QAAQ,CAACK,IAAI,CAAC,CAE1B,CAAAN,cAAA,CAAAb,KAAK,CAACoB,OAAO,UAAAP,cAAA,iBAAbA,cAAA,CAAeQ,IAAI,CAAC,CAChBC,QAAQ,CAAE,SAAS,CACnBC,OAAO,CAAE,IAAI,CACbC,MAAM,CAAE,OACZ,CAAC,CAAC,CACN,CAAE,MAAOC,KAAK,CAAE,KAAAC,eAAA,CACZ,CAAAA,eAAA,CAAA1B,KAAK,CAACoB,OAAO,UAAAM,eAAA,iBAAbA,eAAA,CAAeL,IAAI,CAAC,CAChBC,QAAQ,CAAE,OAAO,CACjBC,OAAO,CAAE,IAAI,CACbC,MAAM,CAAE,MACZ,CAAC,CAAC,CACN,CACJ,CAAC,CAED;AACA,KAAM,CAAAG,cAAc,CAAG,KAAO,CAAAC,QAAgB,EAAK,CAC/C,GAAI,KAAAC,eAAA,CACA,KAAM,CAAAf,QAAQ,CAAG,KAAM,CAAApB,GAAG,CAACoC,GAAG,4BAA6B,CACvDC,MAAM,CAAE,CAAEC,QAAQ,CAAEJ,QAAS,CAAC,CAC9BK,YAAY,CAAE,MAClB,CAAC,CAAC,CAEF,KAAM,CAAAC,GAAG,CAAGC,MAAM,CAACC,GAAG,CAACC,eAAe,CAAC,GAAI,CAAAC,IAAI,CAAC,CAACxB,QAAQ,CAACK,IAAI,CAAC,CAAC,CAAC,CACjE,KAAM,CAAAoB,IAAI,CAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC,CACxCF,IAAI,CAACG,IAAI,CAAGR,GAAG,CACfK,IAAI,CAACI,YAAY,CAAC,UAAU,CAAEf,QAAQ,CAAC,CACvCY,QAAQ,CAACI,IAAI,CAACC,WAAW,CAACN,IAAI,CAAC,CAC/BA,IAAI,CAACO,KAAK,CAAC,CAAC,CACZN,QAAQ,CAACI,IAAI,CAACG,WAAW,CAACR,IAAI,CAAC,CAE/B,CAAAV,eAAA,CAAA7B,KAAK,CAACoB,OAAO,UAAAS,eAAA,iBAAbA,eAAA,CAAeR,IAAI,CAAC,CAChBC,QAAQ,CAAE,SAAS,CACnBC,OAAO,CAAE,MAAM,CACfC,MAAM,CAAEI,QACZ,CAAC,CAAC,CACN,CAAE,MAAOH,KAAK,CAAE,KAAAuB,eAAA,CACZ,CAAAA,eAAA,CAAAhD,KAAK,CAACoB,OAAO,UAAA4B,eAAA,iBAAbA,eAAA,CAAe3B,IAAI,CAAC,CAChBC,QAAQ,CAAE,OAAO,CACjBC,OAAO,CAAE,MAAM,CACfC,MAAM,CAAEyB,MAAM,CAACxB,KAAK,CACxB,CAAC,CAAC,CACN,CACJ,CAAC,CAED;AACA,KAAM,CAAAyB,aAAa,CAAG,KAAO,CAAAtB,QAAgB,EAAK,CAC9C,GAAI,CACA,KAAM,CAAAd,QAAQ,CAAG,KAAM,CAAApB,GAAG,CAACoC,GAAG,4BAA6B,CACvDC,MAAM,CAAE,CAAEC,QAAQ,CAAEJ,QAAS,CAAC,CAC9BK,YAAY,CAAE,MAClB,CAAC,CAAC,CAEF,KAAM,CAAAC,GAAG,CAAGC,MAAM,CAACC,GAAG,CAACC,eAAe,CAAC,GAAI,CAAAC,IAAI,CAAC,CAACxB,QAAQ,CAACK,IAAI,CAAC,CAAC,CAAC,CACjEjB,aAAa,CAACgC,GAAG,CAAC,CACtB,CAAE,MAAOT,KAAK,CAAE,KAAA0B,eAAA,CACZ,CAAAA,eAAA,CAAAnD,KAAK,CAACoB,OAAO,UAAA+B,eAAA,iBAAbA,eAAA,CAAe9B,IAAI,CAAC,CAChBC,QAAQ,CAAE,OAAO,CACjBC,OAAO,CAAE,MAAM,CACfC,MAAM,CAAEyB,MAAM,CAACxB,KAAK,CACxB,CAAC,CAAC,CACN,CACJ,CAAC,CAED,mBACI3B,KAAA,QAAKsD,SAAS,CAAC,kCAAkC,CAAAC,QAAA,eAC7CvD,KAAA,QAAAuD,QAAA,eACAzD,IAAA,CAACL,KAAK,EAAC+D,GAAG,CAAEtD,KAAM,CAAE,CAAC,cACrBJ,IAAA,CAACR,UAAU,EACPmE,IAAI,CAAC,OAAO,CACZC,YAAY,MACZC,aAAa,CAAEpD,kBAAmB,CAClCqD,MAAM,CAAC,SAAS,CAChBC,WAAW,CAAE,OAAQ,CACrBC,WAAW,CAAC,0BAAM,CACrB,CAAC,EACG,CAAC,cACNhE,IAAA,QAAAyD,QAAA,cACIvD,KAAA,QAAKsD,SAAS,CAAC,YAAY,CAAAC,QAAA,eACvBzD,IAAA,CAACN,SAAS,EAACa,KAAK,CAAEA,KAAM,CAAC0D,QAAQ,CAAGC,CAAC,EAAK1D,QAAQ,CAAC0D,CAAC,CAACC,MAAM,CAAC5D,KAAK,CAAE,CAAE,CAAC,cACtEP,IAAA,CAACT,MAAM,EAAC6E,KAAK,CAAC,cAAI,CAACC,IAAI,CAAC,gBAAgB,CAACC,OAAO,CAAEA,CAAA,GAAKvC,cAAc,CAACxB,KAAK,CAAE,CAACmB,QAAQ,CAAC,SAAS,CAAE,CAAC,cACnG1B,IAAA,CAACT,MAAM,EAAC6E,KAAK,CAAC,cAAI,CAACC,IAAI,CAAC,WAAW,CAACC,OAAO,CAAEA,CAAA,GAAKhB,aAAa,CAAC/C,KAAK,CAAE,CAACmB,QAAQ,CAAC,MAAM,CAAE,CAAC,EACzF,CAAC,CACL,CAAC,cACN1B,IAAA,QAAAyD,QAAA,cACIzD,IAAA,QAAAyD,QAAA,CACKpD,UAAU,eACPL,IAAA,CAACP,KAAK,EAAC8E,GAAG,CAAElE,UAAW,CAACmE,GAAG,CAAC,0BAAM,CAACC,KAAK,CAAC,KAAK,CAACC,OAAO,MAAE,CAC3D,CACA,CAAC,CACL,CAAC,EACL,CAAC,CAGd", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
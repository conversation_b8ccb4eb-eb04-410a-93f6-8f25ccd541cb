{"ast": null, "code": "const formatRelativeLocale = {\n  lastWeek: \"'গত' eeee 'সময়' p\",\n  yesterday: \"'গতকাল' 'সময়' p\",\n  today: \"'আজ' 'সময়' p\",\n  tomorrow: \"'আগামীকাল' 'সময়' p\",\n  nextWeek: \"eeee 'সময়' p\",\n  other: \"P\"\n};\nexport const formatRelative = (token, _date, _baseDate, _options) => formatRelativeLocale[token];", "map": {"version": 3, "names": ["formatRelativeLocale", "lastWeek", "yesterday", "today", "tomorrow", "nextWeek", "other", "formatRelative", "token", "_date", "_baseDate", "_options"], "sources": ["C:/Users/<USER>/Desktop/demo-git/demo-react/my-fullstack-app/frontend/node_modules/date-fns/locale/bn/_lib/formatRelative.js"], "sourcesContent": ["const formatRelativeLocale = {\n  lastWeek: \"'গত' eeee 'সময়' p\",\n  yesterday: \"'গতকাল' 'সময়' p\",\n  today: \"'আজ' 'সময়' p\",\n  tomorrow: \"'আগামীকাল' 'সময়' p\",\n  nextWeek: \"eeee 'সময়' p\",\n  other: \"P\",\n};\n\nexport const formatRelative = (token, _date, _baseDate, _options) =>\n  formatRelativeLocale[token];\n"], "mappings": "AAAA,MAAMA,oBAAoB,GAAG;EAC3BC,QAAQ,EAAE,mBAAmB;EAC7BC,SAAS,EAAE,iBAAiB;EAC5BC,KAAK,EAAE,cAAc;EACrBC,QAAQ,EAAE,oBAAoB;EAC9BC,QAAQ,EAAE,cAAc;EACxBC,KAAK,EAAE;AACT,CAAC;AAED,OAAO,MAAMC,cAAc,GAAGA,CAACC,KAAK,EAAEC,KAAK,EAAEC,SAAS,EAAEC,QAAQ,KAC9DX,oBAAoB,CAACQ,KAAK,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace MyApi.Models
{
    [Table("IpBlock")]
    public class IpBlock
    {
        [Key]
        public int Id { get; set; }

        [Required]
        [StringLength(45)]
        public string IPAddress { get; set; } = string.Empty;

        [Required]
        public DateTime CreatedAt { get; set; } = DateTime.Now;

        [Required]
        public DateTime UpdatedAt { get; set; } = DateTime.Now;

        [Required]
        public DateTime ExpiredAt { get; set; }
    }

    public class IpBlockDto
    {
        public int Id { get; set; }
        public string IPAddress { get; set; } = string.Empty;
        public DateTime CreatedAt { get; set; }
        public DateTime UpdatedAt { get; set; }
        public DateTime ExpiredAt { get; set; }
    }

    public class GetIpBlockRequest
    {
        public string? IPAddress { get; set; }
        public DateTime? CreatedAtStart { get; set; }
        public DateTime? CreatedAtEnd { get; set; }
        public int Page { get; set; } = 1;
        public int PageSize { get; set; } = 20;
    }

    public class AddIpBlockRequest
    {
        [Required]
        public string IPAddress { get; set; } = string.Empty;
        public DateTime? ExpiredAt { get; set; }
    }
}

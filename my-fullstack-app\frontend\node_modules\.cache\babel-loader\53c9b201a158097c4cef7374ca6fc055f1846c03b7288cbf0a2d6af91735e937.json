{"ast": null, "code": "import { buildLocalizeFn } from \"../../_lib/buildLocalizeFn.js\";\nconst eraValues = {\n  narrow: [\"пр.н.е.\", \"н.е.\"],\n  abbreviated: [\"преди н. е.\", \"н. е.\"],\n  wide: [\"преди новата ера\", \"новата ера\"]\n};\nconst quarterValues = {\n  narrow: [\"1\", \"2\", \"3\", \"4\"],\n  abbreviated: [\"1-во тримес.\", \"2-ро тримес.\", \"3-то тримес.\", \"4-то тримес.\"],\n  wide: [\"1-во тримесечие\", \"2-ро тримесечие\", \"3-то тримесечие\", \"4-то тримесечие\"]\n};\nconst monthValues = {\n  abbreviated: [\"яну\", \"фев\", \"мар\", \"апр\", \"май\", \"юни\", \"юли\", \"авг\", \"сеп\", \"окт\", \"ное\", \"дек\"],\n  wide: [\"януари\", \"февруари\", \"март\", \"април\", \"май\", \"юни\", \"юли\", \"август\", \"септември\", \"октомври\", \"ноември\", \"декември\"]\n};\nconst dayValues = {\n  narrow: [\"Н\", \"П\", \"В\", \"С\", \"Ч\", \"П\", \"С\"],\n  short: [\"нд\", \"пн\", \"вт\", \"ср\", \"чт\", \"пт\", \"сб\"],\n  abbreviated: [\"нед\", \"пон\", \"вто\", \"сря\", \"чет\", \"пет\", \"съб\"],\n  wide: [\"неделя\", \"понеделник\", \"вторник\", \"сряда\", \"четвъртък\", \"петък\", \"събота\"]\n};\nconst dayPeriodValues = {\n  wide: {\n    am: \"преди обяд\",\n    pm: \"след обяд\",\n    midnight: \"в полунощ\",\n    noon: \"на обяд\",\n    morning: \"сутринта\",\n    afternoon: \"следобед\",\n    evening: \"вечерта\",\n    night: \"през нощта\"\n  }\n};\nfunction isFeminine(unit) {\n  return unit === \"year\" || unit === \"week\" || unit === \"minute\" || unit === \"second\";\n}\nfunction isNeuter(unit) {\n  return unit === \"quarter\";\n}\nfunction numberWithSuffix(number, unit, masculine, feminine, neuter) {\n  const suffix = isNeuter(unit) ? neuter : isFeminine(unit) ? feminine : masculine;\n  return number + \"-\" + suffix;\n}\nconst ordinalNumber = (dirtyNumber, options) => {\n  const number = Number(dirtyNumber);\n  const unit = options === null || options === void 0 ? void 0 : options.unit;\n  if (number === 0) {\n    return numberWithSuffix(0, unit, \"ев\", \"ева\", \"ево\");\n  } else if (number % 1000 === 0) {\n    return numberWithSuffix(number, unit, \"ен\", \"на\", \"но\");\n  } else if (number % 100 === 0) {\n    return numberWithSuffix(number, unit, \"тен\", \"тна\", \"тно\");\n  }\n  const rem100 = number % 100;\n  if (rem100 > 20 || rem100 < 10) {\n    switch (rem100 % 10) {\n      case 1:\n        return numberWithSuffix(number, unit, \"ви\", \"ва\", \"во\");\n      case 2:\n        return numberWithSuffix(number, unit, \"ри\", \"ра\", \"ро\");\n      case 7:\n      case 8:\n        return numberWithSuffix(number, unit, \"ми\", \"ма\", \"мо\");\n    }\n  }\n  return numberWithSuffix(number, unit, \"ти\", \"та\", \"то\");\n};\nexport const localize = {\n  ordinalNumber,\n  era: buildLocalizeFn({\n    values: eraValues,\n    defaultWidth: \"wide\"\n  }),\n  quarter: buildLocalizeFn({\n    values: quarterValues,\n    defaultWidth: \"wide\",\n    argumentCallback: quarter => quarter - 1\n  }),\n  month: buildLocalizeFn({\n    values: monthValues,\n    defaultWidth: \"wide\"\n  }),\n  day: buildLocalizeFn({\n    values: dayValues,\n    defaultWidth: \"wide\"\n  }),\n  dayPeriod: buildLocalizeFn({\n    values: dayPeriodValues,\n    defaultWidth: \"wide\"\n  })\n};", "map": {"version": 3, "names": ["buildLocalizeFn", "<PERSON><PERSON><PERSON><PERSON>", "narrow", "abbreviated", "wide", "quarterValues", "month<PERSON><PERSON><PERSON>", "dayV<PERSON><PERSON>", "short", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "am", "pm", "midnight", "noon", "morning", "afternoon", "evening", "night", "isFeminine", "unit", "isNeuter", "numberWithSuffix", "number", "masculine", "feminine", "neuter", "suffix", "ordinalNumber", "dirtyNumber", "options", "Number", "rem100", "localize", "era", "values", "defaultWidth", "quarter", "argument<PERSON>allback", "month", "day", "<PERSON><PERSON><PERSON><PERSON>"], "sources": ["C:/Users/<USER>/Desktop/demo-git/demo-react/my-fullstack-app/frontend/node_modules/date-fns/locale/bg/_lib/localize.js"], "sourcesContent": ["import { buildLocalizeFn } from \"../../_lib/buildLocalizeFn.js\";\n\nconst eraValues = {\n  narrow: [\"пр.н.е.\", \"н.е.\"],\n  abbreviated: [\"преди н. е.\", \"н. е.\"],\n  wide: [\"преди новата ера\", \"новата ера\"],\n};\n\nconst quarterValues = {\n  narrow: [\"1\", \"2\", \"3\", \"4\"],\n  abbreviated: [\"1-во тримес.\", \"2-ро тримес.\", \"3-то тримес.\", \"4-то тримес.\"],\n\n  wide: [\n    \"1-во тримесечие\",\n    \"2-ро тримесечие\",\n    \"3-то тримесечие\",\n    \"4-то тримесечие\",\n  ],\n};\n\nconst monthValues = {\n  abbreviated: [\n    \"яну\",\n    \"фев\",\n    \"мар\",\n    \"апр\",\n    \"май\",\n    \"юни\",\n    \"юли\",\n    \"авг\",\n    \"сеп\",\n    \"окт\",\n    \"ное\",\n    \"дек\",\n  ],\n\n  wide: [\n    \"януари\",\n    \"февруари\",\n    \"март\",\n    \"април\",\n    \"май\",\n    \"юни\",\n    \"юли\",\n    \"август\",\n    \"септември\",\n    \"октомври\",\n    \"ноември\",\n    \"декември\",\n  ],\n};\n\nconst dayValues = {\n  narrow: [\"Н\", \"П\", \"В\", \"С\", \"Ч\", \"П\", \"С\"],\n  short: [\"нд\", \"пн\", \"вт\", \"ср\", \"чт\", \"пт\", \"сб\"],\n  abbreviated: [\"нед\", \"пон\", \"вто\", \"сря\", \"чет\", \"пет\", \"съб\"],\n  wide: [\n    \"неделя\",\n    \"понеделник\",\n    \"вторник\",\n    \"сряда\",\n    \"четвъртък\",\n    \"петък\",\n    \"събота\",\n  ],\n};\n\nconst dayPeriodValues = {\n  wide: {\n    am: \"преди обяд\",\n    pm: \"след обяд\",\n    midnight: \"в полунощ\",\n    noon: \"на обяд\",\n    morning: \"сутринта\",\n    afternoon: \"следобед\",\n    evening: \"вечерта\",\n    night: \"през нощта\",\n  },\n};\n\nfunction isFeminine(unit) {\n  return (\n    unit === \"year\" || unit === \"week\" || unit === \"minute\" || unit === \"second\"\n  );\n}\n\nfunction isNeuter(unit) {\n  return unit === \"quarter\";\n}\n\nfunction numberWithSuffix(number, unit, masculine, feminine, neuter) {\n  const suffix = isNeuter(unit)\n    ? neuter\n    : isFeminine(unit)\n      ? feminine\n      : masculine;\n  return number + \"-\" + suffix;\n}\n\nconst ordinalNumber = (dirtyNumber, options) => {\n  const number = Number(dirtyNumber);\n  const unit = options?.unit;\n\n  if (number === 0) {\n    return numberWithSuffix(0, unit, \"ев\", \"ева\", \"ево\");\n  } else if (number % 1000 === 0) {\n    return numberWithSuffix(number, unit, \"ен\", \"на\", \"но\");\n  } else if (number % 100 === 0) {\n    return numberWithSuffix(number, unit, \"тен\", \"тна\", \"тно\");\n  }\n\n  const rem100 = number % 100;\n  if (rem100 > 20 || rem100 < 10) {\n    switch (rem100 % 10) {\n      case 1:\n        return numberWithSuffix(number, unit, \"ви\", \"ва\", \"во\");\n      case 2:\n        return numberWithSuffix(number, unit, \"ри\", \"ра\", \"ро\");\n      case 7:\n      case 8:\n        return numberWithSuffix(number, unit, \"ми\", \"ма\", \"мо\");\n    }\n  }\n\n  return numberWithSuffix(number, unit, \"ти\", \"та\", \"то\");\n};\n\nexport const localize = {\n  ordinalNumber,\n\n  era: buildLocalizeFn({\n    values: eraValues,\n    defaultWidth: \"wide\",\n  }),\n\n  quarter: buildLocalizeFn({\n    values: quarterValues,\n    defaultWidth: \"wide\",\n    argumentCallback: (quarter) => quarter - 1,\n  }),\n\n  month: buildLocalizeFn({\n    values: monthValues,\n    defaultWidth: \"wide\",\n  }),\n\n  day: buildLocalizeFn({\n    values: dayValues,\n    defaultWidth: \"wide\",\n  }),\n\n  dayPeriod: buildLocalizeFn({\n    values: dayPeriodValues,\n    defaultWidth: \"wide\",\n  }),\n};\n"], "mappings": "AAAA,SAASA,eAAe,QAAQ,+BAA+B;AAE/D,MAAMC,SAAS,GAAG;EAChBC,MAAM,EAAE,CAAC,SAAS,EAAE,MAAM,CAAC;EAC3BC,WAAW,EAAE,CAAC,aAAa,EAAE,OAAO,CAAC;EACrCC,IAAI,EAAE,CAAC,kBAAkB,EAAE,YAAY;AACzC,CAAC;AAED,MAAMC,aAAa,GAAG;EACpBH,MAAM,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EAC5BC,WAAW,EAAE,CAAC,cAAc,EAAE,cAAc,EAAE,cAAc,EAAE,cAAc,CAAC;EAE7EC,IAAI,EAAE,CACJ,iBAAiB,EACjB,iBAAiB,EACjB,iBAAiB,EACjB,iBAAiB;AAErB,CAAC;AAED,MAAME,WAAW,GAAG;EAClBH,WAAW,EAAE,CACX,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,CACN;EAEDC,IAAI,EAAE,CACJ,QAAQ,EACR,UAAU,EACV,MAAM,EACN,OAAO,EACP,KAAK,EACL,KAAK,EACL,KAAK,EACL,QAAQ,EACR,WAAW,EACX,UAAU,EACV,SAAS,EACT,UAAU;AAEd,CAAC;AAED,MAAMG,SAAS,GAAG;EAChBL,MAAM,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EAC3CM,KAAK,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;EACjDL,WAAW,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC;EAC9DC,IAAI,EAAE,CACJ,QAAQ,EACR,YAAY,EACZ,SAAS,EACT,OAAO,EACP,WAAW,EACX,OAAO,EACP,QAAQ;AAEZ,CAAC;AAED,MAAMK,eAAe,GAAG;EACtBL,IAAI,EAAE;IACJM,EAAE,EAAE,YAAY;IAChBC,EAAE,EAAE,WAAW;IACfC,QAAQ,EAAE,WAAW;IACrBC,IAAI,EAAE,SAAS;IACfC,OAAO,EAAE,UAAU;IACnBC,SAAS,EAAE,UAAU;IACrBC,OAAO,EAAE,SAAS;IAClBC,KAAK,EAAE;EACT;AACF,CAAC;AAED,SAASC,UAAUA,CAACC,IAAI,EAAE;EACxB,OACEA,IAAI,KAAK,MAAM,IAAIA,IAAI,KAAK,MAAM,IAAIA,IAAI,KAAK,QAAQ,IAAIA,IAAI,KAAK,QAAQ;AAEhF;AAEA,SAASC,QAAQA,CAACD,IAAI,EAAE;EACtB,OAAOA,IAAI,KAAK,SAAS;AAC3B;AAEA,SAASE,gBAAgBA,CAACC,MAAM,EAAEH,IAAI,EAAEI,SAAS,EAAEC,QAAQ,EAAEC,MAAM,EAAE;EACnE,MAAMC,MAAM,GAAGN,QAAQ,CAACD,IAAI,CAAC,GACzBM,MAAM,GACNP,UAAU,CAACC,IAAI,CAAC,GACdK,QAAQ,GACRD,SAAS;EACf,OAAOD,MAAM,GAAG,GAAG,GAAGI,MAAM;AAC9B;AAEA,MAAMC,aAAa,GAAGA,CAACC,WAAW,EAAEC,OAAO,KAAK;EAC9C,MAAMP,MAAM,GAAGQ,MAAM,CAACF,WAAW,CAAC;EAClC,MAAMT,IAAI,GAAGU,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEV,IAAI;EAE1B,IAAIG,MAAM,KAAK,CAAC,EAAE;IAChB,OAAOD,gBAAgB,CAAC,CAAC,EAAEF,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,CAAC;EACtD,CAAC,MAAM,IAAIG,MAAM,GAAG,IAAI,KAAK,CAAC,EAAE;IAC9B,OAAOD,gBAAgB,CAACC,MAAM,EAAEH,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;EACzD,CAAC,MAAM,IAAIG,MAAM,GAAG,GAAG,KAAK,CAAC,EAAE;IAC7B,OAAOD,gBAAgB,CAACC,MAAM,EAAEH,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC;EAC5D;EAEA,MAAMY,MAAM,GAAGT,MAAM,GAAG,GAAG;EAC3B,IAAIS,MAAM,GAAG,EAAE,IAAIA,MAAM,GAAG,EAAE,EAAE;IAC9B,QAAQA,MAAM,GAAG,EAAE;MACjB,KAAK,CAAC;QACJ,OAAOV,gBAAgB,CAACC,MAAM,EAAEH,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;MACzD,KAAK,CAAC;QACJ,OAAOE,gBAAgB,CAACC,MAAM,EAAEH,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;MACzD,KAAK,CAAC;MACN,KAAK,CAAC;QACJ,OAAOE,gBAAgB,CAACC,MAAM,EAAEH,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;IAC3D;EACF;EAEA,OAAOE,gBAAgB,CAACC,MAAM,EAAEH,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;AACzD,CAAC;AAED,OAAO,MAAMa,QAAQ,GAAG;EACtBL,aAAa;EAEbM,GAAG,EAAEjC,eAAe,CAAC;IACnBkC,MAAM,EAAEjC,SAAS;IACjBkC,YAAY,EAAE;EAChB,CAAC,CAAC;EAEFC,OAAO,EAAEpC,eAAe,CAAC;IACvBkC,MAAM,EAAE7B,aAAa;IACrB8B,YAAY,EAAE,MAAM;IACpBE,gBAAgB,EAAGD,OAAO,IAAKA,OAAO,GAAG;EAC3C,CAAC,CAAC;EAEFE,KAAK,EAAEtC,eAAe,CAAC;IACrBkC,MAAM,EAAE5B,WAAW;IACnB6B,YAAY,EAAE;EAChB,CAAC,CAAC;EAEFI,GAAG,EAAEvC,eAAe,CAAC;IACnBkC,MAAM,EAAE3B,SAAS;IACjB4B,YAAY,EAAE;EAChB,CAAC,CAAC;EAEFK,SAAS,EAAExC,eAAe,CAAC;IACzBkC,MAAM,EAAEzB,eAAe;IACvB0B,YAAY,EAAE;EAChB,CAAC;AACH,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "function isPluralType(val) {\n  return val.one !== undefined;\n}\nconst formatDistanceLocale = {\n  lessThanXSeconds: {\n    present: {\n      one: \"manj kot {{count}} sekunda\",\n      two: \"manj kot {{count}} sekundi\",\n      few: \"manj kot {{count}} sekunde\",\n      other: \"manj kot {{count}} sekund\"\n    },\n    past: {\n      one: \"manj kot {{count}} sekundo\",\n      two: \"manj kot {{count}} sekundama\",\n      few: \"manj kot {{count}} sekundami\",\n      other: \"manj kot {{count}} sekundami\"\n    },\n    future: {\n      one: \"manj kot {{count}} sekundo\",\n      two: \"manj kot {{count}} sekundi\",\n      few: \"manj kot {{count}} sekunde\",\n      other: \"manj kot {{count}} sekund\"\n    }\n  },\n  xSeconds: {\n    present: {\n      one: \"{{count}} sekunda\",\n      two: \"{{count}} sekundi\",\n      few: \"{{count}} sekunde\",\n      other: \"{{count}} sekund\"\n    },\n    past: {\n      one: \"{{count}} sekundo\",\n      two: \"{{count}} sekundama\",\n      few: \"{{count}} sekundami\",\n      other: \"{{count}} sekundami\"\n    },\n    future: {\n      one: \"{{count}} sekundo\",\n      two: \"{{count}} sekundi\",\n      few: \"{{count}} sekunde\",\n      other: \"{{count}} sekund\"\n    }\n  },\n  halfAMinute: \"pol minute\",\n  lessThanXMinutes: {\n    present: {\n      one: \"manj kot {{count}} minuta\",\n      two: \"manj kot {{count}} minuti\",\n      few: \"manj kot {{count}} minute\",\n      other: \"manj kot {{count}} minut\"\n    },\n    past: {\n      one: \"manj kot {{count}} minuto\",\n      two: \"manj kot {{count}} minutama\",\n      few: \"manj kot {{count}} minutami\",\n      other: \"manj kot {{count}} minutami\"\n    },\n    future: {\n      one: \"manj kot {{count}} minuto\",\n      two: \"manj kot {{count}} minuti\",\n      few: \"manj kot {{count}} minute\",\n      other: \"manj kot {{count}} minut\"\n    }\n  },\n  xMinutes: {\n    present: {\n      one: \"{{count}} minuta\",\n      two: \"{{count}} minuti\",\n      few: \"{{count}} minute\",\n      other: \"{{count}} minut\"\n    },\n    past: {\n      one: \"{{count}} minuto\",\n      two: \"{{count}} minutama\",\n      few: \"{{count}} minutami\",\n      other: \"{{count}} minutami\"\n    },\n    future: {\n      one: \"{{count}} minuto\",\n      two: \"{{count}} minuti\",\n      few: \"{{count}} minute\",\n      other: \"{{count}} minut\"\n    }\n  },\n  aboutXHours: {\n    present: {\n      one: \"približno {{count}} ura\",\n      two: \"približno {{count}} uri\",\n      few: \"približno {{count}} ure\",\n      other: \"približno {{count}} ur\"\n    },\n    past: {\n      one: \"približno {{count}} uro\",\n      two: \"približno {{count}} urama\",\n      few: \"približno {{count}} urami\",\n      other: \"približno {{count}} urami\"\n    },\n    future: {\n      one: \"približno {{count}} uro\",\n      two: \"približno {{count}} uri\",\n      few: \"približno {{count}} ure\",\n      other: \"približno {{count}} ur\"\n    }\n  },\n  xHours: {\n    present: {\n      one: \"{{count}} ura\",\n      two: \"{{count}} uri\",\n      few: \"{{count}} ure\",\n      other: \"{{count}} ur\"\n    },\n    past: {\n      one: \"{{count}} uro\",\n      two: \"{{count}} urama\",\n      few: \"{{count}} urami\",\n      other: \"{{count}} urami\"\n    },\n    future: {\n      one: \"{{count}} uro\",\n      two: \"{{count}} uri\",\n      few: \"{{count}} ure\",\n      other: \"{{count}} ur\"\n    }\n  },\n  xDays: {\n    present: {\n      one: \"{{count}} dan\",\n      two: \"{{count}} dni\",\n      few: \"{{count}} dni\",\n      other: \"{{count}} dni\"\n    },\n    past: {\n      one: \"{{count}} dnem\",\n      two: \"{{count}} dnevoma\",\n      few: \"{{count}} dnevi\",\n      other: \"{{count}} dnevi\"\n    },\n    future: {\n      one: \"{{count}} dan\",\n      two: \"{{count}} dni\",\n      few: \"{{count}} dni\",\n      other: \"{{count}} dni\"\n    }\n  },\n  // no tenses for weeks?\n  aboutXWeeks: {\n    one: \"približno {{count}} teden\",\n    two: \"približno {{count}} tedna\",\n    few: \"približno {{count}} tedne\",\n    other: \"približno {{count}} tednov\"\n  },\n  // no tenses for weeks?\n  xWeeks: {\n    one: \"{{count}} teden\",\n    two: \"{{count}} tedna\",\n    few: \"{{count}} tedne\",\n    other: \"{{count}} tednov\"\n  },\n  aboutXMonths: {\n    present: {\n      one: \"približno {{count}} mesec\",\n      two: \"približno {{count}} meseca\",\n      few: \"približno {{count}} mesece\",\n      other: \"približno {{count}} mesecev\"\n    },\n    past: {\n      one: \"približno {{count}} mesecem\",\n      two: \"približno {{count}} mesecema\",\n      few: \"približno {{count}} meseci\",\n      other: \"približno {{count}} meseci\"\n    },\n    future: {\n      one: \"približno {{count}} mesec\",\n      two: \"približno {{count}} meseca\",\n      few: \"približno {{count}} mesece\",\n      other: \"približno {{count}} mesecev\"\n    }\n  },\n  xMonths: {\n    present: {\n      one: \"{{count}} mesec\",\n      two: \"{{count}} meseca\",\n      few: \"{{count}} meseci\",\n      other: \"{{count}} mesecev\"\n    },\n    past: {\n      one: \"{{count}} mesecem\",\n      two: \"{{count}} mesecema\",\n      few: \"{{count}} meseci\",\n      other: \"{{count}} meseci\"\n    },\n    future: {\n      one: \"{{count}} mesec\",\n      two: \"{{count}} meseca\",\n      few: \"{{count}} mesece\",\n      other: \"{{count}} mesecev\"\n    }\n  },\n  aboutXYears: {\n    present: {\n      one: \"približno {{count}} leto\",\n      two: \"približno {{count}} leti\",\n      few: \"približno {{count}} leta\",\n      other: \"približno {{count}} let\"\n    },\n    past: {\n      one: \"približno {{count}} letom\",\n      two: \"približno {{count}} letoma\",\n      few: \"približno {{count}} leti\",\n      other: \"približno {{count}} leti\"\n    },\n    future: {\n      one: \"približno {{count}} leto\",\n      two: \"približno {{count}} leti\",\n      few: \"približno {{count}} leta\",\n      other: \"približno {{count}} let\"\n    }\n  },\n  xYears: {\n    present: {\n      one: \"{{count}} leto\",\n      two: \"{{count}} leti\",\n      few: \"{{count}} leta\",\n      other: \"{{count}} let\"\n    },\n    past: {\n      one: \"{{count}} letom\",\n      two: \"{{count}} letoma\",\n      few: \"{{count}} leti\",\n      other: \"{{count}} leti\"\n    },\n    future: {\n      one: \"{{count}} leto\",\n      two: \"{{count}} leti\",\n      few: \"{{count}} leta\",\n      other: \"{{count}} let\"\n    }\n  },\n  overXYears: {\n    present: {\n      one: \"več kot {{count}} leto\",\n      two: \"več kot {{count}} leti\",\n      few: \"več kot {{count}} leta\",\n      other: \"več kot {{count}} let\"\n    },\n    past: {\n      one: \"več kot {{count}} letom\",\n      two: \"več kot {{count}} letoma\",\n      few: \"več kot {{count}} leti\",\n      other: \"več kot {{count}} leti\"\n    },\n    future: {\n      one: \"več kot {{count}} leto\",\n      two: \"več kot {{count}} leti\",\n      few: \"več kot {{count}} leta\",\n      other: \"več kot {{count}} let\"\n    }\n  },\n  almostXYears: {\n    present: {\n      one: \"skoraj {{count}} leto\",\n      two: \"skoraj {{count}} leti\",\n      few: \"skoraj {{count}} leta\",\n      other: \"skoraj {{count}} let\"\n    },\n    past: {\n      one: \"skoraj {{count}} letom\",\n      two: \"skoraj {{count}} letoma\",\n      few: \"skoraj {{count}} leti\",\n      other: \"skoraj {{count}} leti\"\n    },\n    future: {\n      one: \"skoraj {{count}} leto\",\n      two: \"skoraj {{count}} leti\",\n      few: \"skoraj {{count}} leta\",\n      other: \"skoraj {{count}} let\"\n    }\n  }\n};\nfunction getFormFromCount(count) {\n  switch (count % 100) {\n    case 1:\n      return \"one\";\n    case 2:\n      return \"two\";\n    case 3:\n    case 4:\n      return \"few\";\n    default:\n      return \"other\";\n  }\n}\nexport const formatDistance = (token, count, options) => {\n  let result = \"\";\n  let tense = \"present\";\n  if (options !== null && options !== void 0 && options.addSuffix) {\n    if (options.comparison && options.comparison > 0) {\n      tense = \"future\";\n      result = \"čez \";\n    } else {\n      tense = \"past\";\n      result = \"pred \";\n    }\n  }\n  const tokenValue = formatDistanceLocale[token];\n  if (typeof tokenValue === \"string\") {\n    result += tokenValue;\n  } else {\n    const form = getFormFromCount(count);\n    if (isPluralType(tokenValue)) {\n      result += tokenValue[form].replace(\"{{count}}\", String(count));\n    } else {\n      result += tokenValue[tense][form].replace(\"{{count}}\", String(count));\n    }\n  }\n  return result;\n};", "map": {"version": 3, "names": ["isPluralType", "val", "one", "undefined", "formatDistanceLocale", "lessThanXSeconds", "present", "two", "few", "other", "past", "future", "xSeconds", "halfAMinute", "lessThanXMinutes", "xMinutes", "aboutXHours", "xHours", "xDays", "aboutXWeeks", "xWeeks", "aboutXMonths", "xMonths", "aboutXYears", "xYears", "overXYears", "almostXYears", "getFormFromCount", "count", "formatDistance", "token", "options", "result", "tense", "addSuffix", "comparison", "tokenValue", "form", "replace", "String"], "sources": ["C:/Users/<USER>/Desktop/demo-git/demo-react/my-fullstack-app/frontend/node_modules/date-fns/locale/sl/_lib/formatDistance.js"], "sourcesContent": ["function isPluralType(val) {\n  return val.one !== undefined;\n}\n\nconst formatDistanceLocale = {\n  lessThanXSeconds: {\n    present: {\n      one: \"manj kot {{count}} sekunda\",\n      two: \"manj kot {{count}} sekundi\",\n      few: \"manj kot {{count}} sekunde\",\n      other: \"manj kot {{count}} sekund\",\n    },\n    past: {\n      one: \"manj kot {{count}} sekundo\",\n      two: \"manj kot {{count}} sekundama\",\n      few: \"manj kot {{count}} sekundami\",\n      other: \"manj kot {{count}} sekundami\",\n    },\n    future: {\n      one: \"manj kot {{count}} sekundo\",\n      two: \"manj kot {{count}} sekundi\",\n      few: \"manj kot {{count}} sekunde\",\n      other: \"manj kot {{count}} sekund\",\n    },\n  },\n\n  xSeconds: {\n    present: {\n      one: \"{{count}} sekunda\",\n      two: \"{{count}} sekundi\",\n      few: \"{{count}} sekunde\",\n      other: \"{{count}} sekund\",\n    },\n    past: {\n      one: \"{{count}} sekundo\",\n      two: \"{{count}} sekundama\",\n      few: \"{{count}} sekundami\",\n      other: \"{{count}} sekundami\",\n    },\n    future: {\n      one: \"{{count}} sekundo\",\n      two: \"{{count}} sekundi\",\n      few: \"{{count}} sekunde\",\n      other: \"{{count}} sekund\",\n    },\n  },\n\n  halfAMinute: \"pol minute\",\n\n  lessThanXMinutes: {\n    present: {\n      one: \"manj kot {{count}} minuta\",\n      two: \"manj kot {{count}} minuti\",\n      few: \"manj kot {{count}} minute\",\n      other: \"manj kot {{count}} minut\",\n    },\n    past: {\n      one: \"manj kot {{count}} minuto\",\n      two: \"manj kot {{count}} minutama\",\n      few: \"manj kot {{count}} minutami\",\n      other: \"manj kot {{count}} minutami\",\n    },\n    future: {\n      one: \"manj kot {{count}} minuto\",\n      two: \"manj kot {{count}} minuti\",\n      few: \"manj kot {{count}} minute\",\n      other: \"manj kot {{count}} minut\",\n    },\n  },\n\n  xMinutes: {\n    present: {\n      one: \"{{count}} minuta\",\n      two: \"{{count}} minuti\",\n      few: \"{{count}} minute\",\n      other: \"{{count}} minut\",\n    },\n    past: {\n      one: \"{{count}} minuto\",\n      two: \"{{count}} minutama\",\n      few: \"{{count}} minutami\",\n      other: \"{{count}} minutami\",\n    },\n    future: {\n      one: \"{{count}} minuto\",\n      two: \"{{count}} minuti\",\n      few: \"{{count}} minute\",\n      other: \"{{count}} minut\",\n    },\n  },\n\n  aboutXHours: {\n    present: {\n      one: \"približno {{count}} ura\",\n      two: \"približno {{count}} uri\",\n      few: \"približno {{count}} ure\",\n      other: \"približno {{count}} ur\",\n    },\n    past: {\n      one: \"približno {{count}} uro\",\n      two: \"približno {{count}} urama\",\n      few: \"približno {{count}} urami\",\n      other: \"približno {{count}} urami\",\n    },\n    future: {\n      one: \"približno {{count}} uro\",\n      two: \"približno {{count}} uri\",\n      few: \"približno {{count}} ure\",\n      other: \"približno {{count}} ur\",\n    },\n  },\n\n  xHours: {\n    present: {\n      one: \"{{count}} ura\",\n      two: \"{{count}} uri\",\n      few: \"{{count}} ure\",\n      other: \"{{count}} ur\",\n    },\n    past: {\n      one: \"{{count}} uro\",\n      two: \"{{count}} urama\",\n      few: \"{{count}} urami\",\n      other: \"{{count}} urami\",\n    },\n    future: {\n      one: \"{{count}} uro\",\n      two: \"{{count}} uri\",\n      few: \"{{count}} ure\",\n      other: \"{{count}} ur\",\n    },\n  },\n\n  xDays: {\n    present: {\n      one: \"{{count}} dan\",\n      two: \"{{count}} dni\",\n      few: \"{{count}} dni\",\n      other: \"{{count}} dni\",\n    },\n    past: {\n      one: \"{{count}} dnem\",\n      two: \"{{count}} dnevoma\",\n      few: \"{{count}} dnevi\",\n      other: \"{{count}} dnevi\",\n    },\n    future: {\n      one: \"{{count}} dan\",\n      two: \"{{count}} dni\",\n      few: \"{{count}} dni\",\n      other: \"{{count}} dni\",\n    },\n  },\n\n  // no tenses for weeks?\n  aboutXWeeks: {\n    one: \"približno {{count}} teden\",\n    two: \"približno {{count}} tedna\",\n    few: \"približno {{count}} tedne\",\n    other: \"približno {{count}} tednov\",\n  },\n\n  // no tenses for weeks?\n  xWeeks: {\n    one: \"{{count}} teden\",\n    two: \"{{count}} tedna\",\n    few: \"{{count}} tedne\",\n    other: \"{{count}} tednov\",\n  },\n\n  aboutXMonths: {\n    present: {\n      one: \"približno {{count}} mesec\",\n      two: \"približno {{count}} meseca\",\n      few: \"približno {{count}} mesece\",\n      other: \"približno {{count}} mesecev\",\n    },\n    past: {\n      one: \"približno {{count}} mesecem\",\n      two: \"približno {{count}} mesecema\",\n      few: \"približno {{count}} meseci\",\n      other: \"približno {{count}} meseci\",\n    },\n    future: {\n      one: \"približno {{count}} mesec\",\n      two: \"približno {{count}} meseca\",\n      few: \"približno {{count}} mesece\",\n      other: \"približno {{count}} mesecev\",\n    },\n  },\n\n  xMonths: {\n    present: {\n      one: \"{{count}} mesec\",\n      two: \"{{count}} meseca\",\n      few: \"{{count}} meseci\",\n      other: \"{{count}} mesecev\",\n    },\n    past: {\n      one: \"{{count}} mesecem\",\n      two: \"{{count}} mesecema\",\n      few: \"{{count}} meseci\",\n      other: \"{{count}} meseci\",\n    },\n    future: {\n      one: \"{{count}} mesec\",\n      two: \"{{count}} meseca\",\n      few: \"{{count}} mesece\",\n      other: \"{{count}} mesecev\",\n    },\n  },\n\n  aboutXYears: {\n    present: {\n      one: \"približno {{count}} leto\",\n      two: \"približno {{count}} leti\",\n      few: \"približno {{count}} leta\",\n      other: \"približno {{count}} let\",\n    },\n    past: {\n      one: \"približno {{count}} letom\",\n      two: \"približno {{count}} letoma\",\n      few: \"približno {{count}} leti\",\n      other: \"približno {{count}} leti\",\n    },\n    future: {\n      one: \"približno {{count}} leto\",\n      two: \"približno {{count}} leti\",\n      few: \"približno {{count}} leta\",\n      other: \"približno {{count}} let\",\n    },\n  },\n\n  xYears: {\n    present: {\n      one: \"{{count}} leto\",\n      two: \"{{count}} leti\",\n      few: \"{{count}} leta\",\n      other: \"{{count}} let\",\n    },\n    past: {\n      one: \"{{count}} letom\",\n      two: \"{{count}} letoma\",\n      few: \"{{count}} leti\",\n      other: \"{{count}} leti\",\n    },\n    future: {\n      one: \"{{count}} leto\",\n      two: \"{{count}} leti\",\n      few: \"{{count}} leta\",\n      other: \"{{count}} let\",\n    },\n  },\n\n  overXYears: {\n    present: {\n      one: \"več kot {{count}} leto\",\n      two: \"več kot {{count}} leti\",\n      few: \"več kot {{count}} leta\",\n      other: \"več kot {{count}} let\",\n    },\n    past: {\n      one: \"več kot {{count}} letom\",\n      two: \"več kot {{count}} letoma\",\n      few: \"več kot {{count}} leti\",\n      other: \"več kot {{count}} leti\",\n    },\n    future: {\n      one: \"več kot {{count}} leto\",\n      two: \"več kot {{count}} leti\",\n      few: \"več kot {{count}} leta\",\n      other: \"več kot {{count}} let\",\n    },\n  },\n\n  almostXYears: {\n    present: {\n      one: \"skoraj {{count}} leto\",\n      two: \"skoraj {{count}} leti\",\n      few: \"skoraj {{count}} leta\",\n      other: \"skoraj {{count}} let\",\n    },\n    past: {\n      one: \"skoraj {{count}} letom\",\n      two: \"skoraj {{count}} letoma\",\n      few: \"skoraj {{count}} leti\",\n      other: \"skoraj {{count}} leti\",\n    },\n    future: {\n      one: \"skoraj {{count}} leto\",\n      two: \"skoraj {{count}} leti\",\n      few: \"skoraj {{count}} leta\",\n      other: \"skoraj {{count}} let\",\n    },\n  },\n};\n\nfunction getFormFromCount(count) {\n  switch (count % 100) {\n    case 1:\n      return \"one\";\n    case 2:\n      return \"two\";\n    case 3:\n    case 4:\n      return \"few\";\n    default:\n      return \"other\";\n  }\n}\n\nexport const formatDistance = (token, count, options) => {\n  let result = \"\";\n  let tense = \"present\";\n\n  if (options?.addSuffix) {\n    if (options.comparison && options.comparison > 0) {\n      tense = \"future\";\n      result = \"čez \";\n    } else {\n      tense = \"past\";\n      result = \"pred \";\n    }\n  }\n\n  const tokenValue = formatDistanceLocale[token];\n\n  if (typeof tokenValue === \"string\") {\n    result += tokenValue;\n  } else {\n    const form = getFormFromCount(count);\n    if (isPluralType(tokenValue)) {\n      result += tokenValue[form].replace(\"{{count}}\", String(count));\n    } else {\n      result += tokenValue[tense][form].replace(\"{{count}}\", String(count));\n    }\n  }\n\n  return result;\n};\n"], "mappings": "AAAA,SAASA,YAAYA,CAACC,GAAG,EAAE;EACzB,OAAOA,GAAG,CAACC,GAAG,KAAKC,SAAS;AAC9B;AAEA,MAAMC,oBAAoB,GAAG;EAC3BC,gBAAgB,EAAE;IAChBC,OAAO,EAAE;MACPJ,GAAG,EAAE,4BAA4B;MACjCK,GAAG,EAAE,4BAA4B;MACjCC,GAAG,EAAE,4BAA4B;MACjCC,KAAK,EAAE;IACT,CAAC;IACDC,IAAI,EAAE;MACJR,GAAG,EAAE,4BAA4B;MACjCK,GAAG,EAAE,8BAA8B;MACnCC,GAAG,EAAE,8BAA8B;MACnCC,KAAK,EAAE;IACT,CAAC;IACDE,MAAM,EAAE;MACNT,GAAG,EAAE,4BAA4B;MACjCK,GAAG,EAAE,4BAA4B;MACjCC,GAAG,EAAE,4BAA4B;MACjCC,KAAK,EAAE;IACT;EACF,CAAC;EAEDG,QAAQ,EAAE;IACRN,OAAO,EAAE;MACPJ,GAAG,EAAE,mBAAmB;MACxBK,GAAG,EAAE,mBAAmB;MACxBC,GAAG,EAAE,mBAAmB;MACxBC,KAAK,EAAE;IACT,CAAC;IACDC,IAAI,EAAE;MACJR,GAAG,EAAE,mBAAmB;MACxBK,GAAG,EAAE,qBAAqB;MAC1BC,GAAG,EAAE,qBAAqB;MAC1BC,KAAK,EAAE;IACT,CAAC;IACDE,MAAM,EAAE;MACNT,GAAG,EAAE,mBAAmB;MACxBK,GAAG,EAAE,mBAAmB;MACxBC,GAAG,EAAE,mBAAmB;MACxBC,KAAK,EAAE;IACT;EACF,CAAC;EAEDI,WAAW,EAAE,YAAY;EAEzBC,gBAAgB,EAAE;IAChBR,OAAO,EAAE;MACPJ,GAAG,EAAE,2BAA2B;MAChCK,GAAG,EAAE,2BAA2B;MAChCC,GAAG,EAAE,2BAA2B;MAChCC,KAAK,EAAE;IACT,CAAC;IACDC,IAAI,EAAE;MACJR,GAAG,EAAE,2BAA2B;MAChCK,GAAG,EAAE,6BAA6B;MAClCC,GAAG,EAAE,6BAA6B;MAClCC,KAAK,EAAE;IACT,CAAC;IACDE,MAAM,EAAE;MACNT,GAAG,EAAE,2BAA2B;MAChCK,GAAG,EAAE,2BAA2B;MAChCC,GAAG,EAAE,2BAA2B;MAChCC,KAAK,EAAE;IACT;EACF,CAAC;EAEDM,QAAQ,EAAE;IACRT,OAAO,EAAE;MACPJ,GAAG,EAAE,kBAAkB;MACvBK,GAAG,EAAE,kBAAkB;MACvBC,GAAG,EAAE,kBAAkB;MACvBC,KAAK,EAAE;IACT,CAAC;IACDC,IAAI,EAAE;MACJR,GAAG,EAAE,kBAAkB;MACvBK,GAAG,EAAE,oBAAoB;MACzBC,GAAG,EAAE,oBAAoB;MACzBC,KAAK,EAAE;IACT,CAAC;IACDE,MAAM,EAAE;MACNT,GAAG,EAAE,kBAAkB;MACvBK,GAAG,EAAE,kBAAkB;MACvBC,GAAG,EAAE,kBAAkB;MACvBC,KAAK,EAAE;IACT;EACF,CAAC;EAEDO,WAAW,EAAE;IACXV,OAAO,EAAE;MACPJ,GAAG,EAAE,yBAAyB;MAC9BK,GAAG,EAAE,yBAAyB;MAC9BC,GAAG,EAAE,yBAAyB;MAC9BC,KAAK,EAAE;IACT,CAAC;IACDC,IAAI,EAAE;MACJR,GAAG,EAAE,yBAAyB;MAC9BK,GAAG,EAAE,2BAA2B;MAChCC,GAAG,EAAE,2BAA2B;MAChCC,KAAK,EAAE;IACT,CAAC;IACDE,MAAM,EAAE;MACNT,GAAG,EAAE,yBAAyB;MAC9BK,GAAG,EAAE,yBAAyB;MAC9BC,GAAG,EAAE,yBAAyB;MAC9BC,KAAK,EAAE;IACT;EACF,CAAC;EAEDQ,MAAM,EAAE;IACNX,OAAO,EAAE;MACPJ,GAAG,EAAE,eAAe;MACpBK,GAAG,EAAE,eAAe;MACpBC,GAAG,EAAE,eAAe;MACpBC,KAAK,EAAE;IACT,CAAC;IACDC,IAAI,EAAE;MACJR,GAAG,EAAE,eAAe;MACpBK,GAAG,EAAE,iBAAiB;MACtBC,GAAG,EAAE,iBAAiB;MACtBC,KAAK,EAAE;IACT,CAAC;IACDE,MAAM,EAAE;MACNT,GAAG,EAAE,eAAe;MACpBK,GAAG,EAAE,eAAe;MACpBC,GAAG,EAAE,eAAe;MACpBC,KAAK,EAAE;IACT;EACF,CAAC;EAEDS,KAAK,EAAE;IACLZ,OAAO,EAAE;MACPJ,GAAG,EAAE,eAAe;MACpBK,GAAG,EAAE,eAAe;MACpBC,GAAG,EAAE,eAAe;MACpBC,KAAK,EAAE;IACT,CAAC;IACDC,IAAI,EAAE;MACJR,GAAG,EAAE,gBAAgB;MACrBK,GAAG,EAAE,mBAAmB;MACxBC,GAAG,EAAE,iBAAiB;MACtBC,KAAK,EAAE;IACT,CAAC;IACDE,MAAM,EAAE;MACNT,GAAG,EAAE,eAAe;MACpBK,GAAG,EAAE,eAAe;MACpBC,GAAG,EAAE,eAAe;MACpBC,KAAK,EAAE;IACT;EACF,CAAC;EAED;EACAU,WAAW,EAAE;IACXjB,GAAG,EAAE,2BAA2B;IAChCK,GAAG,EAAE,2BAA2B;IAChCC,GAAG,EAAE,2BAA2B;IAChCC,KAAK,EAAE;EACT,CAAC;EAED;EACAW,MAAM,EAAE;IACNlB,GAAG,EAAE,iBAAiB;IACtBK,GAAG,EAAE,iBAAiB;IACtBC,GAAG,EAAE,iBAAiB;IACtBC,KAAK,EAAE;EACT,CAAC;EAEDY,YAAY,EAAE;IACZf,OAAO,EAAE;MACPJ,GAAG,EAAE,2BAA2B;MAChCK,GAAG,EAAE,4BAA4B;MACjCC,GAAG,EAAE,4BAA4B;MACjCC,KAAK,EAAE;IACT,CAAC;IACDC,IAAI,EAAE;MACJR,GAAG,EAAE,6BAA6B;MAClCK,GAAG,EAAE,8BAA8B;MACnCC,GAAG,EAAE,4BAA4B;MACjCC,KAAK,EAAE;IACT,CAAC;IACDE,MAAM,EAAE;MACNT,GAAG,EAAE,2BAA2B;MAChCK,GAAG,EAAE,4BAA4B;MACjCC,GAAG,EAAE,4BAA4B;MACjCC,KAAK,EAAE;IACT;EACF,CAAC;EAEDa,OAAO,EAAE;IACPhB,OAAO,EAAE;MACPJ,GAAG,EAAE,iBAAiB;MACtBK,GAAG,EAAE,kBAAkB;MACvBC,GAAG,EAAE,kBAAkB;MACvBC,KAAK,EAAE;IACT,CAAC;IACDC,IAAI,EAAE;MACJR,GAAG,EAAE,mBAAmB;MACxBK,GAAG,EAAE,oBAAoB;MACzBC,GAAG,EAAE,kBAAkB;MACvBC,KAAK,EAAE;IACT,CAAC;IACDE,MAAM,EAAE;MACNT,GAAG,EAAE,iBAAiB;MACtBK,GAAG,EAAE,kBAAkB;MACvBC,GAAG,EAAE,kBAAkB;MACvBC,KAAK,EAAE;IACT;EACF,CAAC;EAEDc,WAAW,EAAE;IACXjB,OAAO,EAAE;MACPJ,GAAG,EAAE,0BAA0B;MAC/BK,GAAG,EAAE,0BAA0B;MAC/BC,GAAG,EAAE,0BAA0B;MAC/BC,KAAK,EAAE;IACT,CAAC;IACDC,IAAI,EAAE;MACJR,GAAG,EAAE,2BAA2B;MAChCK,GAAG,EAAE,4BAA4B;MACjCC,GAAG,EAAE,0BAA0B;MAC/BC,KAAK,EAAE;IACT,CAAC;IACDE,MAAM,EAAE;MACNT,GAAG,EAAE,0BAA0B;MAC/BK,GAAG,EAAE,0BAA0B;MAC/BC,GAAG,EAAE,0BAA0B;MAC/BC,KAAK,EAAE;IACT;EACF,CAAC;EAEDe,MAAM,EAAE;IACNlB,OAAO,EAAE;MACPJ,GAAG,EAAE,gBAAgB;MACrBK,GAAG,EAAE,gBAAgB;MACrBC,GAAG,EAAE,gBAAgB;MACrBC,KAAK,EAAE;IACT,CAAC;IACDC,IAAI,EAAE;MACJR,GAAG,EAAE,iBAAiB;MACtBK,GAAG,EAAE,kBAAkB;MACvBC,GAAG,EAAE,gBAAgB;MACrBC,KAAK,EAAE;IACT,CAAC;IACDE,MAAM,EAAE;MACNT,GAAG,EAAE,gBAAgB;MACrBK,GAAG,EAAE,gBAAgB;MACrBC,GAAG,EAAE,gBAAgB;MACrBC,KAAK,EAAE;IACT;EACF,CAAC;EAEDgB,UAAU,EAAE;IACVnB,OAAO,EAAE;MACPJ,GAAG,EAAE,wBAAwB;MAC7BK,GAAG,EAAE,wBAAwB;MAC7BC,GAAG,EAAE,wBAAwB;MAC7BC,KAAK,EAAE;IACT,CAAC;IACDC,IAAI,EAAE;MACJR,GAAG,EAAE,yBAAyB;MAC9BK,GAAG,EAAE,0BAA0B;MAC/BC,GAAG,EAAE,wBAAwB;MAC7BC,KAAK,EAAE;IACT,CAAC;IACDE,MAAM,EAAE;MACNT,GAAG,EAAE,wBAAwB;MAC7BK,GAAG,EAAE,wBAAwB;MAC7BC,GAAG,EAAE,wBAAwB;MAC7BC,KAAK,EAAE;IACT;EACF,CAAC;EAEDiB,YAAY,EAAE;IACZpB,OAAO,EAAE;MACPJ,GAAG,EAAE,uBAAuB;MAC5BK,GAAG,EAAE,uBAAuB;MAC5BC,GAAG,EAAE,uBAAuB;MAC5BC,KAAK,EAAE;IACT,CAAC;IACDC,IAAI,EAAE;MACJR,GAAG,EAAE,wBAAwB;MAC7BK,GAAG,EAAE,yBAAyB;MAC9BC,GAAG,EAAE,uBAAuB;MAC5BC,KAAK,EAAE;IACT,CAAC;IACDE,MAAM,EAAE;MACNT,GAAG,EAAE,uBAAuB;MAC5BK,GAAG,EAAE,uBAAuB;MAC5BC,GAAG,EAAE,uBAAuB;MAC5BC,KAAK,EAAE;IACT;EACF;AACF,CAAC;AAED,SAASkB,gBAAgBA,CAACC,KAAK,EAAE;EAC/B,QAAQA,KAAK,GAAG,GAAG;IACjB,KAAK,CAAC;MACJ,OAAO,KAAK;IACd,KAAK,CAAC;MACJ,OAAO,KAAK;IACd,KAAK,CAAC;IACN,KAAK,CAAC;MACJ,OAAO,KAAK;IACd;MACE,OAAO,OAAO;EAClB;AACF;AAEA,OAAO,MAAMC,cAAc,GAAGA,CAACC,KAAK,EAAEF,KAAK,EAAEG,OAAO,KAAK;EACvD,IAAIC,MAAM,GAAG,EAAE;EACf,IAAIC,KAAK,GAAG,SAAS;EAErB,IAAIF,OAAO,aAAPA,OAAO,eAAPA,OAAO,CAAEG,SAAS,EAAE;IACtB,IAAIH,OAAO,CAACI,UAAU,IAAIJ,OAAO,CAACI,UAAU,GAAG,CAAC,EAAE;MAChDF,KAAK,GAAG,QAAQ;MAChBD,MAAM,GAAG,MAAM;IACjB,CAAC,MAAM;MACLC,KAAK,GAAG,MAAM;MACdD,MAAM,GAAG,OAAO;IAClB;EACF;EAEA,MAAMI,UAAU,GAAGhC,oBAAoB,CAAC0B,KAAK,CAAC;EAE9C,IAAI,OAAOM,UAAU,KAAK,QAAQ,EAAE;IAClCJ,MAAM,IAAII,UAAU;EACtB,CAAC,MAAM;IACL,MAAMC,IAAI,GAAGV,gBAAgB,CAACC,KAAK,CAAC;IACpC,IAAI5B,YAAY,CAACoC,UAAU,CAAC,EAAE;MAC5BJ,MAAM,IAAII,UAAU,CAACC,IAAI,CAAC,CAACC,OAAO,CAAC,WAAW,EAAEC,MAAM,CAACX,KAAK,CAAC,CAAC;IAChE,CAAC,MAAM;MACLI,MAAM,IAAII,UAAU,CAACH,KAAK,CAAC,CAACI,IAAI,CAAC,CAACC,OAAO,CAAC,WAAW,EAAEC,MAAM,CAACX,KAAK,CAAC,CAAC;IACvE;EACF;EAEA,OAAOI,MAAM;AACf,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
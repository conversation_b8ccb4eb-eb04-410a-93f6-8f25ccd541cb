<Project Sdk="Microsoft.NET.Sdk.Web">
  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <!--<PublishTrimmed>true</PublishTrimmed>-->
    <TrimMode>link</TrimMode>
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|AnyCPU'" />

  <ItemGroup>
    <Compile Remove="Helpers\RedisHelpser.cs" />
  </ItemGroup>

  <ItemGroup>
    <!-- Core dependencies -->
    <PackageReference Include="Google.Apis.Auth" Version="1.70.0" />
    <PackageReference Include="Google.Apis.Calendar.v3" Version="1.69.0.3746" />
    <PackageReference Include="Google.Apis.Gmail.v1" Version="1.70.0.3819" />
    <PackageReference Include="Google.Apis.Oauth2.v2" Version="1.68.0.1869" />
    <PackageReference Include="MimeKit" Version="4.3.0" />
    <PackageReference Include="Microsoft.AspNetCore.ResponseCompression" Version="2.3.0" />
    <PackageReference Include="Microsoft.AspNetCore.SignalR.Protocols.MessagePack" Version="9.0.7" />
    <PackageReference Include="Microsoft.EntityFrameworkCore" Version="8.0.8" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="8.0.8">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="Pomelo.EntityFrameworkCore.MySql" Version="8.0.2" />

    <!-- Authentication -->
    <PackageReference Include="BCrypt.Net-Next" Version="4.0.3" />
    <PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="8.0.17" />
    <PackageReference Include="System.IdentityModel.Tokens.Jwt" Version="8.12.0" />

    <!-- API Documentation (Development only) -->
    <PackageReference Include="Swashbuckle.AspNetCore" Version="8.1.4" Condition="'$(Configuration)' == 'Debug'" />

    <!-- Optional services (conditionally loaded) -->
    <PackageReference Include="StackExchange.Redis" Version="2.8.37" />
    <PackageReference Include="Microsoft.AspNetCore.SignalR" Version="1.2.0" />

    <!-- PDF Generation (only when needed) -->
    <PackageReference Include="QuestPDF" Version="2022.12.15" />
    <PackageReference Include="SkiaSharp.NativeAssets.Linux.NoDependencies" Version="2.88.3" />
    <PackageReference Include="HarfBuzzSharp.NativeAssets.Linux" Version="2.8.2" />
	
	<!-- Swagger -->
	<PackageReference Include="Swashbuckle.AspNetCore" Version="6.5.0" />
  </ItemGroup>

  <ItemGroup>
    <Folder Include="Migrations\" />
  </ItemGroup>
</Project>

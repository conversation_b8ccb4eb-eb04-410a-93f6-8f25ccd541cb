{"ast": null, "code": "import _objectSpread from \"C:/Users/<USER>/Desktop/demo-git/demo-react/my-fullstack-app/frontend/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";\nimport { format } from '../format/index.js';\nimport { toZonedTime } from '../toZonedTime/index.js';\n/**\n * @name formatInTimeZone\n * @category Time Zone Helpers\n * @summary Gets the offset in milliseconds between the time zone and Universal Coordinated Time (UTC)\n *\n * @param date the date representing the local time / real UTC time\n * @param timeZone the time zone this date should be formatted for; can be an offset or IANA time zone\n * @param formatStr the string of tokens\n * @param options the object with options. See [Options]{@link https://date-fns.org/docs/Options}\n * @param {0|1|2} [options.additionalDigits=2] - passed to `toDate`. See [toDate]{@link\n *   https://date-fns.org/docs/toDate}\n * @param {0|1|2|3|4|5|6} [options.weekStartsOn=0] - the index of the first day of the week (0 - Sunday)\n * @param {Number} [options.firstWeekContainsDate=1] - the day of January, which is\n * @param {Locale} [options.locale=defaultLocale] - the locale object. See\n *   [Locale]{@link https://date-fns.org/docs/Locale}\n * @param {Boolean} [options.awareOfUnicodeTokens=false] - if true, allows usage of Unicode tokens causes confusion:\n *   - Some of the day of year tokens (`D`, `DD`) that are confused with the day of month tokens (`d`, `dd`).\n *   - Some of the local week-numbering year tokens (`YY`, `YYYY`) that are confused with the calendar year tokens\n *   (`yy`, `yyyy`). See: https://git.io/fxCyr\n * @param {String} [options.timeZone=''] - used to specify the IANA time zone offset of a date String.\n */\nexport function formatInTimeZone(date, timeZone, formatStr, options) {\n  options = _objectSpread(_objectSpread({}, options), {}, {\n    timeZone,\n    originalDate: date\n  });\n  return format(toZonedTime(date, timeZone, {\n    timeZone: options.timeZone\n  }), formatStr, options);\n}", "map": {"version": 3, "names": ["format", "toZonedTime", "formatInTimeZone", "date", "timeZone", "formatStr", "options", "_objectSpread", "originalDate"], "sources": ["C:/Users/<USER>/Desktop/demo-git/demo-react/my-fullstack-app/frontend/node_modules/date-fns-tz/dist/esm/formatInTimeZone/index.js"], "sourcesContent": ["import { format } from '../format/index.js';\nimport { toZonedTime } from '../toZonedTime/index.js';\n/**\n * @name formatInTimeZone\n * @category Time Zone Helpers\n * @summary Gets the offset in milliseconds between the time zone and Universal Coordinated Time (UTC)\n *\n * @param date the date representing the local time / real UTC time\n * @param timeZone the time zone this date should be formatted for; can be an offset or IANA time zone\n * @param formatStr the string of tokens\n * @param options the object with options. See [Options]{@link https://date-fns.org/docs/Options}\n * @param {0|1|2} [options.additionalDigits=2] - passed to `toDate`. See [toDate]{@link\n *   https://date-fns.org/docs/toDate}\n * @param {0|1|2|3|4|5|6} [options.weekStartsOn=0] - the index of the first day of the week (0 - Sunday)\n * @param {Number} [options.firstWeekContainsDate=1] - the day of January, which is\n * @param {Locale} [options.locale=defaultLocale] - the locale object. See\n *   [Locale]{@link https://date-fns.org/docs/Locale}\n * @param {Boolean} [options.awareOfUnicodeTokens=false] - if true, allows usage of Unicode tokens causes confusion:\n *   - Some of the day of year tokens (`D`, `DD`) that are confused with the day of month tokens (`d`, `dd`).\n *   - Some of the local week-numbering year tokens (`YY`, `YYYY`) that are confused with the calendar year tokens\n *   (`yy`, `yyyy`). See: https://git.io/fxCyr\n * @param {String} [options.timeZone=''] - used to specify the IANA time zone offset of a date String.\n */\nexport function formatInTimeZone(date, timeZone, formatStr, options) {\n    options = {\n        ...options,\n        timeZone,\n        originalDate: date,\n    };\n    return format(toZonedTime(date, timeZone, { timeZone: options.timeZone }), formatStr, options);\n}\n"], "mappings": ";AAAA,SAASA,MAAM,QAAQ,oBAAoB;AAC3C,SAASC,WAAW,QAAQ,yBAAyB;AACrD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,gBAAgBA,CAACC,IAAI,EAAEC,QAAQ,EAAEC,SAAS,EAAEC,OAAO,EAAE;EACjEA,OAAO,GAAAC,aAAA,CAAAA,aAAA,KACAD,OAAO;IACVF,QAAQ;IACRI,YAAY,EAAEL;EAAI,EACrB;EACD,OAAOH,MAAM,CAACC,WAAW,CAACE,IAAI,EAAEC,QAAQ,EAAE;IAAEA,QAAQ,EAAEE,OAAO,CAACF;EAAS,CAAC,CAAC,EAAEC,SAAS,EAAEC,OAAO,CAAC;AAClG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
/**
 * Application route constants
 */

export const ROUTES = {
  // Auth routes
  LOGIN: '/login',
  UPDATE_PASSWORD: '/update-password',
  
  // Main routes
  HOME: '/',
  
  // Doctor routes
  DOCTORS: '/doctors',
  DOCTOR_DETAIL: '/doctordetail',
  
  // Treatment routes
  TREATMENTS: '/treatments',
  TREATMENT_DETAIL: '/treatmentsdetail',
  
  // Patient routes
  PATIENTS: '/patients',
  PATIENT_DETAIL: '/patientsdetail',
  
  // Schedule routes
  SCHEDULES: '/schedules',
  
  // Receipt routes
  RECEIPTS: '/receipts',
  RECEIPT_DETAIL: '/receiptsdetail',
  
  // User routes
  USERS: '/userrole-management',

  // Backup routes
  BACKUP_MANAGEMENT: '/backup-management',

  // Report routes
  REPORT_MANAGEMENT: '/report-management',

  // Image routes
  IMAGE_MANAGEMENT: '/image-management',

  // Login Security routes
  LOGIN_LOGS: '/login-logs',
  IP_BLOCKS: '/ip-blocks',

  // Debug routes
  DEBUG: '/debug',
} as const;

export type RouteKey = keyof typeof ROUTES;
export type RoutePath = typeof ROUTES[RouteKey];

/**
 * Route metadata for navigation and permissions
 */
export interface RouteMetadata {
  path: RoutePath;
  title: string;
  requiresAuth: boolean;
  permissions?: string[];
  icon?: string;
}

export const ROUTE_METADATA: Record<RoutePath, RouteMetadata> = {
  [ROUTES.LOGIN]: {
    path: ROUTES.LOGIN,
    title: 'Login',
    requiresAuth: false,
  },
  [ROUTES.UPDATE_PASSWORD]: {
    path: ROUTES.UPDATE_PASSWORD,
    title: 'Update Password',
    requiresAuth: true,
  },
  [ROUTES.HOME]: {
    path: ROUTES.HOME,
    title: 'Home',
    requiresAuth: true,
    icon: 'pi pi-home',
  },
  [ROUTES.DOCTORS]: {
    path: ROUTES.DOCTORS,
    title: 'Doctors',
    requiresAuth: true,
    icon: 'pi pi-users',
  },
  [ROUTES.DOCTOR_DETAIL]: {
    path: ROUTES.DOCTOR_DETAIL,
    title: 'Doctor Detail',
    requiresAuth: true,
  },
  [ROUTES.TREATMENTS]: {
    path: ROUTES.TREATMENTS,
    title: 'Treatments',
    requiresAuth: true,
    icon: 'pi pi-heart',
  },
  [ROUTES.TREATMENT_DETAIL]: {
    path: ROUTES.TREATMENT_DETAIL,
    title: 'Treatment Detail',
    requiresAuth: true,
  },
  [ROUTES.PATIENTS]: {
    path: ROUTES.PATIENTS,
    title: 'Patients',
    requiresAuth: true,
    icon: 'pi pi-user',
  },
  [ROUTES.PATIENT_DETAIL]: {
    path: ROUTES.PATIENT_DETAIL,
    title: 'Patient Detail',
    requiresAuth: true,
  },
  [ROUTES.SCHEDULES]: {
    path: ROUTES.SCHEDULES,
    title: 'Schedules',
    requiresAuth: true,
    icon: 'pi pi-calendar',
  },
  [ROUTES.RECEIPTS]: {
    path: ROUTES.RECEIPTS,
    title: 'Receipts',
    requiresAuth: true,
    icon: 'pi pi-file',
  },
  [ROUTES.RECEIPT_DETAIL]: {
    path: ROUTES.RECEIPT_DETAIL,
    title: 'Receipt Detail',
    requiresAuth: true,
  },
  [ROUTES.USERS]: {
    path: ROUTES.USERS,
    title: 'User Role Management',
    requiresAuth: true,
    icon: 'pi pi-users',
  },
  [ROUTES.BACKUP_MANAGEMENT]: {
    path: ROUTES.BACKUP_MANAGEMENT,
    title: 'Backup Management',
    requiresAuth: true,
    permissions: ['Admin'],
    icon: 'pi pi-database',
  },
  [ROUTES.REPORT_MANAGEMENT]: {
    path: ROUTES.REPORT_MANAGEMENT,
    title: 'Report Management',
    requiresAuth: true,
    permissions: ['Admin', 'Manager'],
    icon: 'pi pi-file-pdf',
  },
  [ROUTES.IMAGE_MANAGEMENT]: {
    path: ROUTES.IMAGE_MANAGEMENT,
    title: 'Image Management',
    requiresAuth: true,
    permissions: ['Admin', 'Manager'],
    icon: 'pi pi-images',
  },
  [ROUTES.LOGIN_LOGS]: {
    path: ROUTES.LOGIN_LOGS,
    title: 'Login Logs',
    requiresAuth: true,
    permissions: ['Admin', 'Manager'],
    icon: 'pi pi-history',
  },
  [ROUTES.IP_BLOCKS]: {
    path: ROUTES.IP_BLOCKS,
    title: 'IP Blocks',
    requiresAuth: true,
    permissions: ['Admin'],
    icon: 'pi pi-ban',
  },
  [ROUTES.DEBUG]: {
    path: ROUTES.DEBUG,
    title: 'Debug',
    requiresAuth: true,
    icon: 'pi pi-cog',
  },
};

/**
 * Get route metadata by path
 */
export const getRouteMetadata = (path: string): RouteMetadata | undefined => {
  return ROUTE_METADATA[path as RoutePath];
};

/**
 * Check if route requires authentication
 */
export const isProtectedRoute = (path: string): boolean => {
  const metadata = getRouteMetadata(path);
  return metadata?.requiresAuth ?? true;
};

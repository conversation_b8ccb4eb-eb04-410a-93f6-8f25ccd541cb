{"ast": null, "code": "import { buildLocalizeFn } from \"../../_lib/buildLocalizeFn.js\";\nconst eraValues = {\n  narrow: [\"BC\", \"AC\"],\n  abbreviated: [\"紀元前\", \"西暦\"],\n  wide: [\"紀元前\", \"西暦\"]\n};\nconst quarterValues = {\n  narrow: [\"1\", \"2\", \"3\", \"4\"],\n  abbreviated: [\"Q1\", \"Q2\", \"Q3\", \"Q4\"],\n  wide: [\"第1四半期\", \"第2四半期\", \"第3四半期\", \"第4四半期\"]\n};\nconst monthValues = {\n  narrow: [\"1\", \"2\", \"3\", \"4\", \"5\", \"6\", \"7\", \"8\", \"9\", \"10\", \"11\", \"12\"],\n  abbreviated: [\"1月\", \"2月\", \"3月\", \"4月\", \"5月\", \"6月\", \"7月\", \"8月\", \"9月\", \"10月\", \"11月\", \"12月\"],\n  wide: [\"1月\", \"2月\", \"3月\", \"4月\", \"5月\", \"6月\", \"7月\", \"8月\", \"9月\", \"10月\", \"11月\", \"12月\"]\n};\nconst dayValues = {\n  narrow: [\"日\", \"月\", \"火\", \"水\", \"木\", \"金\", \"土\"],\n  short: [\"日\", \"月\", \"火\", \"水\", \"木\", \"金\", \"土\"],\n  abbreviated: [\"日\", \"月\", \"火\", \"水\", \"木\", \"金\", \"土\"],\n  wide: [\"日曜日\", \"月曜日\", \"火曜日\", \"水曜日\", \"木曜日\", \"金曜日\", \"土曜日\"]\n};\nconst dayPeriodValues = {\n  narrow: {\n    am: \"午前\",\n    pm: \"午後\",\n    midnight: \"深夜\",\n    noon: \"正午\",\n    morning: \"朝\",\n    afternoon: \"午後\",\n    evening: \"夜\",\n    night: \"深夜\"\n  },\n  abbreviated: {\n    am: \"午前\",\n    pm: \"午後\",\n    midnight: \"深夜\",\n    noon: \"正午\",\n    morning: \"朝\",\n    afternoon: \"午後\",\n    evening: \"夜\",\n    night: \"深夜\"\n  },\n  wide: {\n    am: \"午前\",\n    pm: \"午後\",\n    midnight: \"深夜\",\n    noon: \"正午\",\n    morning: \"朝\",\n    afternoon: \"午後\",\n    evening: \"夜\",\n    night: \"深夜\"\n  }\n};\nconst formattingDayPeriodValues = {\n  narrow: {\n    am: \"午前\",\n    pm: \"午後\",\n    midnight: \"深夜\",\n    noon: \"正午\",\n    morning: \"朝\",\n    afternoon: \"午後\",\n    evening: \"夜\",\n    night: \"深夜\"\n  },\n  abbreviated: {\n    am: \"午前\",\n    pm: \"午後\",\n    midnight: \"深夜\",\n    noon: \"正午\",\n    morning: \"朝\",\n    afternoon: \"午後\",\n    evening: \"夜\",\n    night: \"深夜\"\n  },\n  wide: {\n    am: \"午前\",\n    pm: \"午後\",\n    midnight: \"深夜\",\n    noon: \"正午\",\n    morning: \"朝\",\n    afternoon: \"午後\",\n    evening: \"夜\",\n    night: \"深夜\"\n  }\n};\nconst ordinalNumber = (dirtyNumber, options) => {\n  const number = Number(dirtyNumber);\n  const unit = String(options === null || options === void 0 ? void 0 : options.unit);\n  switch (unit) {\n    case \"year\":\n      return \"\".concat(number, \"\\u5E74\");\n    case \"quarter\":\n      return \"\\u7B2C\".concat(number, \"\\u56DB\\u534A\\u671F\");\n    case \"month\":\n      return \"\".concat(number, \"\\u6708\");\n    case \"week\":\n      return \"\\u7B2C\".concat(number, \"\\u9031\");\n    case \"date\":\n      return \"\".concat(number, \"\\u65E5\");\n    case \"hour\":\n      return \"\".concat(number, \"\\u6642\");\n    case \"minute\":\n      return \"\".concat(number, \"\\u5206\");\n    case \"second\":\n      return \"\".concat(number, \"\\u79D2\");\n    default:\n      return \"\".concat(number);\n  }\n};\nexport const localize = {\n  ordinalNumber: ordinalNumber,\n  era: buildLocalizeFn({\n    values: eraValues,\n    defaultWidth: \"wide\"\n  }),\n  quarter: buildLocalizeFn({\n    values: quarterValues,\n    defaultWidth: \"wide\",\n    argumentCallback: quarter => Number(quarter) - 1\n  }),\n  month: buildLocalizeFn({\n    values: monthValues,\n    defaultWidth: \"wide\"\n  }),\n  day: buildLocalizeFn({\n    values: dayValues,\n    defaultWidth: \"wide\"\n  }),\n  dayPeriod: buildLocalizeFn({\n    values: dayPeriodValues,\n    defaultWidth: \"wide\",\n    formattingValues: formattingDayPeriodValues,\n    defaultFormattingWidth: \"wide\"\n  })\n};", "map": {"version": 3, "names": ["buildLocalizeFn", "<PERSON><PERSON><PERSON><PERSON>", "narrow", "abbreviated", "wide", "quarterValues", "month<PERSON><PERSON><PERSON>", "dayV<PERSON><PERSON>", "short", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "am", "pm", "midnight", "noon", "morning", "afternoon", "evening", "night", "formattingDayPeriodValues", "ordinalNumber", "dirtyNumber", "options", "number", "Number", "unit", "String", "concat", "localize", "era", "values", "defaultWidth", "quarter", "argument<PERSON>allback", "month", "day", "<PERSON><PERSON><PERSON><PERSON>", "formattingValues", "defaultFormattingWidth"], "sources": ["C:/Users/<USER>/Desktop/demo-git/demo-react/my-fullstack-app/frontend/node_modules/date-fns/locale/ja/_lib/localize.js"], "sourcesContent": ["import { buildLocalizeFn } from \"../../_lib/buildLocalizeFn.js\";\n\nconst eraValues = {\n  narrow: [\"BC\", \"AC\"],\n  abbreviated: [\"紀元前\", \"西暦\"],\n  wide: [\"紀元前\", \"西暦\"],\n};\n\nconst quarterValues = {\n  narrow: [\"1\", \"2\", \"3\", \"4\"],\n  abbreviated: [\"Q1\", \"Q2\", \"Q3\", \"Q4\"],\n  wide: [\"第1四半期\", \"第2四半期\", \"第3四半期\", \"第4四半期\"],\n};\n\nconst monthValues = {\n  narrow: [\"1\", \"2\", \"3\", \"4\", \"5\", \"6\", \"7\", \"8\", \"9\", \"10\", \"11\", \"12\"],\n\n  abbreviated: [\n    \"1月\",\n    \"2月\",\n    \"3月\",\n    \"4月\",\n    \"5月\",\n    \"6月\",\n    \"7月\",\n    \"8月\",\n    \"9月\",\n    \"10月\",\n    \"11月\",\n    \"12月\",\n  ],\n\n  wide: [\n    \"1月\",\n    \"2月\",\n    \"3月\",\n    \"4月\",\n    \"5月\",\n    \"6月\",\n    \"7月\",\n    \"8月\",\n    \"9月\",\n    \"10月\",\n    \"11月\",\n    \"12月\",\n  ],\n};\n\nconst dayValues = {\n  narrow: [\"日\", \"月\", \"火\", \"水\", \"木\", \"金\", \"土\"],\n  short: [\"日\", \"月\", \"火\", \"水\", \"木\", \"金\", \"土\"],\n  abbreviated: [\"日\", \"月\", \"火\", \"水\", \"木\", \"金\", \"土\"],\n  wide: [\"日曜日\", \"月曜日\", \"火曜日\", \"水曜日\", \"木曜日\", \"金曜日\", \"土曜日\"],\n};\n\nconst dayPeriodValues = {\n  narrow: {\n    am: \"午前\",\n    pm: \"午後\",\n    midnight: \"深夜\",\n    noon: \"正午\",\n    morning: \"朝\",\n    afternoon: \"午後\",\n    evening: \"夜\",\n    night: \"深夜\",\n  },\n  abbreviated: {\n    am: \"午前\",\n    pm: \"午後\",\n    midnight: \"深夜\",\n    noon: \"正午\",\n    morning: \"朝\",\n    afternoon: \"午後\",\n    evening: \"夜\",\n    night: \"深夜\",\n  },\n  wide: {\n    am: \"午前\",\n    pm: \"午後\",\n    midnight: \"深夜\",\n    noon: \"正午\",\n    morning: \"朝\",\n    afternoon: \"午後\",\n    evening: \"夜\",\n    night: \"深夜\",\n  },\n};\nconst formattingDayPeriodValues = {\n  narrow: {\n    am: \"午前\",\n    pm: \"午後\",\n    midnight: \"深夜\",\n    noon: \"正午\",\n    morning: \"朝\",\n    afternoon: \"午後\",\n    evening: \"夜\",\n    night: \"深夜\",\n  },\n  abbreviated: {\n    am: \"午前\",\n    pm: \"午後\",\n    midnight: \"深夜\",\n    noon: \"正午\",\n    morning: \"朝\",\n    afternoon: \"午後\",\n    evening: \"夜\",\n    night: \"深夜\",\n  },\n  wide: {\n    am: \"午前\",\n    pm: \"午後\",\n    midnight: \"深夜\",\n    noon: \"正午\",\n    morning: \"朝\",\n    afternoon: \"午後\",\n    evening: \"夜\",\n    night: \"深夜\",\n  },\n};\n\nconst ordinalNumber = (dirtyNumber, options) => {\n  const number = Number(dirtyNumber);\n  const unit = String(options?.unit);\n\n  switch (unit) {\n    case \"year\":\n      return `${number}年`;\n    case \"quarter\":\n      return `第${number}四半期`;\n    case \"month\":\n      return `${number}月`;\n    case \"week\":\n      return `第${number}週`;\n    case \"date\":\n      return `${number}日`;\n    case \"hour\":\n      return `${number}時`;\n    case \"minute\":\n      return `${number}分`;\n    case \"second\":\n      return `${number}秒`;\n    default:\n      return `${number}`;\n  }\n};\n\nexport const localize = {\n  ordinalNumber: ordinalNumber,\n\n  era: buildLocalizeFn({\n    values: eraValues,\n    defaultWidth: \"wide\",\n  }),\n\n  quarter: buildLocalizeFn({\n    values: quarterValues,\n    defaultWidth: \"wide\",\n    argumentCallback: (quarter) => Number(quarter) - 1,\n  }),\n\n  month: buildLocalizeFn({\n    values: monthValues,\n    defaultWidth: \"wide\",\n  }),\n\n  day: buildLocalizeFn({\n    values: dayValues,\n    defaultWidth: \"wide\",\n  }),\n\n  dayPeriod: buildLocalizeFn({\n    values: dayPeriodValues,\n    defaultWidth: \"wide\",\n    formattingValues: formattingDayPeriodValues,\n    defaultFormattingWidth: \"wide\",\n  }),\n};\n"], "mappings": "AAAA,SAASA,eAAe,QAAQ,+BAA+B;AAE/D,MAAMC,SAAS,GAAG;EAChBC,MAAM,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC;EACpBC,WAAW,EAAE,CAAC,KAAK,EAAE,IAAI,CAAC;EAC1BC,IAAI,EAAE,CAAC,KAAK,EAAE,IAAI;AACpB,CAAC;AAED,MAAMC,aAAa,GAAG;EACpBH,MAAM,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EAC5BC,WAAW,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;EACrCC,IAAI,EAAE,CAAC,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO;AAC3C,CAAC;AAED,MAAME,WAAW,GAAG;EAClBJ,MAAM,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;EAEvEC,WAAW,EAAE,CACX,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,KAAK,EACL,KAAK,EACL,KAAK,CACN;EAEDC,IAAI,EAAE,CACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,KAAK,EACL,KAAK,EACL,KAAK;AAET,CAAC;AAED,MAAMG,SAAS,GAAG;EAChBL,MAAM,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EAC3CM,KAAK,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EAC1CL,WAAW,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EAChDC,IAAI,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK;AACxD,CAAC;AAED,MAAMK,eAAe,GAAG;EACtBP,MAAM,EAAE;IACNQ,EAAE,EAAE,IAAI;IACRC,EAAE,EAAE,IAAI;IACRC,QAAQ,EAAE,IAAI;IACdC,IAAI,EAAE,IAAI;IACVC,OAAO,EAAE,GAAG;IACZC,SAAS,EAAE,IAAI;IACfC,OAAO,EAAE,GAAG;IACZC,KAAK,EAAE;EACT,CAAC;EACDd,WAAW,EAAE;IACXO,EAAE,EAAE,IAAI;IACRC,EAAE,EAAE,IAAI;IACRC,QAAQ,EAAE,IAAI;IACdC,IAAI,EAAE,IAAI;IACVC,OAAO,EAAE,GAAG;IACZC,SAAS,EAAE,IAAI;IACfC,OAAO,EAAE,GAAG;IACZC,KAAK,EAAE;EACT,CAAC;EACDb,IAAI,EAAE;IACJM,EAAE,EAAE,IAAI;IACRC,EAAE,EAAE,IAAI;IACRC,QAAQ,EAAE,IAAI;IACdC,IAAI,EAAE,IAAI;IACVC,OAAO,EAAE,GAAG;IACZC,SAAS,EAAE,IAAI;IACfC,OAAO,EAAE,GAAG;IACZC,KAAK,EAAE;EACT;AACF,CAAC;AACD,MAAMC,yBAAyB,GAAG;EAChChB,MAAM,EAAE;IACNQ,EAAE,EAAE,IAAI;IACRC,EAAE,EAAE,IAAI;IACRC,QAAQ,EAAE,IAAI;IACdC,IAAI,EAAE,IAAI;IACVC,OAAO,EAAE,GAAG;IACZC,SAAS,EAAE,IAAI;IACfC,OAAO,EAAE,GAAG;IACZC,KAAK,EAAE;EACT,CAAC;EACDd,WAAW,EAAE;IACXO,EAAE,EAAE,IAAI;IACRC,EAAE,EAAE,IAAI;IACRC,QAAQ,EAAE,IAAI;IACdC,IAAI,EAAE,IAAI;IACVC,OAAO,EAAE,GAAG;IACZC,SAAS,EAAE,IAAI;IACfC,OAAO,EAAE,GAAG;IACZC,KAAK,EAAE;EACT,CAAC;EACDb,IAAI,EAAE;IACJM,EAAE,EAAE,IAAI;IACRC,EAAE,EAAE,IAAI;IACRC,QAAQ,EAAE,IAAI;IACdC,IAAI,EAAE,IAAI;IACVC,OAAO,EAAE,GAAG;IACZC,SAAS,EAAE,IAAI;IACfC,OAAO,EAAE,GAAG;IACZC,KAAK,EAAE;EACT;AACF,CAAC;AAED,MAAME,aAAa,GAAGA,CAACC,WAAW,EAAEC,OAAO,KAAK;EAC9C,MAAMC,MAAM,GAAGC,MAAM,CAACH,WAAW,CAAC;EAClC,MAAMI,IAAI,GAAGC,MAAM,CAACJ,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEG,IAAI,CAAC;EAElC,QAAQA,IAAI;IACV,KAAK,MAAM;MACT,UAAAE,MAAA,CAAUJ,MAAM;IAClB,KAAK,SAAS;MACZ,gBAAAI,MAAA,CAAWJ,MAAM;IACnB,KAAK,OAAO;MACV,UAAAI,MAAA,CAAUJ,MAAM;IAClB,KAAK,MAAM;MACT,gBAAAI,MAAA,CAAWJ,MAAM;IACnB,KAAK,MAAM;MACT,UAAAI,MAAA,CAAUJ,MAAM;IAClB,KAAK,MAAM;MACT,UAAAI,MAAA,CAAUJ,MAAM;IAClB,KAAK,QAAQ;MACX,UAAAI,MAAA,CAAUJ,MAAM;IAClB,KAAK,QAAQ;MACX,UAAAI,MAAA,CAAUJ,MAAM;IAClB;MACE,UAAAI,MAAA,CAAUJ,MAAM;EACpB;AACF,CAAC;AAED,OAAO,MAAMK,QAAQ,GAAG;EACtBR,aAAa,EAAEA,aAAa;EAE5BS,GAAG,EAAE5B,eAAe,CAAC;IACnB6B,MAAM,EAAE5B,SAAS;IACjB6B,YAAY,EAAE;EAChB,CAAC,CAAC;EAEFC,OAAO,EAAE/B,eAAe,CAAC;IACvB6B,MAAM,EAAExB,aAAa;IACrByB,YAAY,EAAE,MAAM;IACpBE,gBAAgB,EAAGD,OAAO,IAAKR,MAAM,CAACQ,OAAO,CAAC,GAAG;EACnD,CAAC,CAAC;EAEFE,KAAK,EAAEjC,eAAe,CAAC;IACrB6B,MAAM,EAAEvB,WAAW;IACnBwB,YAAY,EAAE;EAChB,CAAC,CAAC;EAEFI,GAAG,EAAElC,eAAe,CAAC;IACnB6B,MAAM,EAAEtB,SAAS;IACjBuB,YAAY,EAAE;EAChB,CAAC,CAAC;EAEFK,SAAS,EAAEnC,eAAe,CAAC;IACzB6B,MAAM,EAAEpB,eAAe;IACvBqB,YAAY,EAAE,MAAM;IACpBM,gBAAgB,EAAElB,yBAAyB;IAC3CmB,sBAAsB,EAAE;EAC1B,CAAC;AACH,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
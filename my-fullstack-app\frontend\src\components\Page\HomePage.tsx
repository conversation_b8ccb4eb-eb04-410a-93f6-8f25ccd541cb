import { zhTW } from 'date-fns/locale';
import { formatUtcToTaipei } from '../../utils/dateUtils';
import dayGridPlugin from '@fullcalendar/daygrid';
import FullCalendar from '@fullcalendar/react';
import timeGridPlugin from '@fullcalendar/timegrid';
import { Chart } from 'primereact/chart';
import React, { useEffect, useState, useRef } from 'react';
import { useNavigate } from 'react-router-dom';
import api from '../../services/api';
import { log } from '../../utils/logger';
import { Toast } from 'primereact/toast';
import { Dialog } from 'primereact/dialog';
import { Button } from 'primereact/button';
import { ROUTES } from '../../constants/routes';

interface CalendarEvent {
  id?: string;
  title: string;
  start: string;
  end: string;
  backgroundColor?: string;
  doctorId?: number;
  patientId?: number;
  treatmentId?: number;
  doctorName?: string;
  patientName?: string;
  description?: string;
}

interface SatisticEvent {
  lable?: string[];
  schedules?: number[];
  treatments?: number[];
}

const HomePage: React.FC = () => {
  const navigate = useNavigate();
  const toast = useRef<Toast>(null);
  const [basicData, setBasicData] = useState({});
  const [lineData, setLineData] = useState({});
  const [chartOptions, setChartOptions] = useState({});
  const [todayEvents, setTodayEvents] = useState<CalendarEvent[]>([]);
  const [showEventDialog, setShowEventDialog] = useState(false);
  const [selectedEvent, setSelectedEvent] = useState<CalendarEvent | null>(null);

  useEffect(() => {
    const loadData = async () => {
      // 載入圖表數據
      const documentStyle = getComputedStyle(document.documentElement);
      const textColor = documentStyle.getPropertyValue('--text-color');
      const textColorSecondary = documentStyle.getPropertyValue('--text-color-secondary');
      const surfaceBorder = documentStyle.getPropertyValue('--surface-border');

      // Chart Options
      const options = {
        maintainAspectRatio: false,
        aspectRatio: 0.6,
        plugins: {
          legend: {
            labels: {
              fontColor: textColor
            }
          }
        },
        scales: {
          x: {
            ticks: {
              color: textColorSecondary
            },
            grid: {
              color: surfaceBorder
            }
          },
          y: {
            ticks: {
              color: textColorSecondary
            },
            grid: {
              color: surfaceBorder
            }
          }
        }
      };

      // 載入月統計數據
      try {
        log.api('載入月統計數據...');
        const response = await api.get('/api/system/GetMonthSatistics');
        log.api('月統計數據', response.data);
        
        // 解析 JSON 字符串（因為後端返回的是序列化的 JSON）
        const MonthData = typeof response.data === 'string'
          ? JSON.parse(response.data)
          : response.data;

        // 轉換數據格式
        const formattedEvents: SatisticEvent = {
          lable: MonthData.lable || MonthData.labels || [],
          schedules: MonthData.schedules || [],
          treatments: MonthData.treatments || []
        };

        log.ui('月統計原始數據', MonthData);
        log.ui('月統計格式化數據', formattedEvents);

        // Basic Chart Data (Bar Chart) - 直接使用解析後的數據
        const basicChartData = {
          labels: formattedEvents.lable,
          datasets: [
            {
              label: '預約人數',
              backgroundColor: documentStyle.getPropertyValue('--blue-500'),
              borderColor: documentStyle.getPropertyValue('--blue-500'),
              data: formattedEvents.schedules
            },
            {
              label: '治療次數',
              backgroundColor: documentStyle.getPropertyValue('--pink-500'),
              borderColor: documentStyle.getPropertyValue('--pink-500'),
              data: formattedEvents.treatments
            }
          ]
        };

        log.ui('月統計圖表數據', basicChartData);
        setBasicData(basicChartData);
      } catch (error) {
        log.error('載入月統計失敗', error);
        // 如果 API 失敗，顯示示例數據
        toast.current?.show({
          severity: 'error',
          summary: '錯誤',
          detail: '載入月統計失敗',
        });
      }

      // 載入周統計數據
      try {
        log.api('載入周統計數據...');
        const response = await api.get('/api/system/GetWeekSatistics');
        log.api('周統計數據', response.data);
        
        // 解析 JSON 字符串（因為後端返回的是序列化的 JSON）
        const WeekData = typeof response.data === 'string'
          ? JSON.parse(response.data)
          : response.data;

        // 轉換數據格式
        const formattedEvents: SatisticEvent = {
          lable: WeekData.lable.map((label:string) => `${label} 號`) || WeekData.labels.map((label:string) => `${label} 號`) || [],
          schedules: WeekData.schedules || [],
          treatments: WeekData.treatments || []
        };

        log.ui('周統計原始數據', WeekData);
        log.ui('周統計格式化數據', formattedEvents);

        // Line Chart Data - 直接使用解析後的數據
        const lineChartData = {
          labels: formattedEvents.lable,
          datasets: [
            {
              label: '預約數量',
              data: formattedEvents.schedules,
              fill: false,
              backgroundColor: documentStyle.getPropertyValue('--blue-600'),
              borderColor: documentStyle.getPropertyValue('--blue-600'),
              tension: 0.4
            },
            {
              label: '完成治療',
              data: formattedEvents.treatments,
              fill: false,
              backgroundColor: documentStyle.getPropertyValue('--pink-600'),
              borderColor: documentStyle.getPropertyValue('--pink-600'),
              tension: 0.4
            }
          ]
        };

        log.ui('周統計圖表數據', lineChartData);
        setLineData(lineChartData);
      } catch (error) {
        log.error('載入周統計失敗', error);
        // 如果 API 失敗，顯示示例數據
        toast.current?.show({
          severity: 'error',
          summary: '錯誤',
          detail: '載入周統計失敗',
        });
      }

      setChartOptions(options);

      // 載入當日行程數據
      try {
        log.api('載入當日行程數據...');
        const response = await api.get('/api/schedule/GetTodaySchedules');
        log.api('當日行程數據', response.data);

        // 解析 JSON 字符串（因為後端返回的是序列化的 JSON）
        const todaySchedulesData = typeof response.data === 'string'
          ? JSON.parse(response.data)
          : response.data;

        // 轉換數據格式
        const formattedEvents: CalendarEvent[] = todaySchedulesData.map((event: any) => ({
          id: event.Id || event.id,
          title: event.Title || event.title,
          start: event.Start || event.start,
          end: event.End || event.end,
          backgroundColor: event.BackgroundColor || event.backgroundColor || '#3788d8',
          doctorId: event.DoctorId || event.doctorId,
          patientId: event.PatientId || event.patientId,
          treatmentId: event.TreatmentId || event.treatmentId,
          doctorName: event.DoctorName || event.doctorName,
          patientName: event.PatientName || event.patientName,
          description: event.Description || event.description
        }));

        log.ui('今日行程', formattedEvents);
        setTodayEvents(formattedEvents);

      } catch (error) {
        log.error('載入當日行程失敗', error);
        // 如果 API 失敗，顯示示例數據
        toast.current?.show({
          severity: 'error',
          summary: '錯誤',
          detail: '載入當日行程失敗',
        });
      }

    };
    
    loadData();
  }, []);

  // 處理事件點擊
  const handleEventClick = (clickInfo: any) => {
    const eventId = clickInfo.event.id;
    const event = todayEvents.find(e => e.id === eventId);
    if (event) {
      setSelectedEvent(event);
      setShowEventDialog(true);
    }
  };

  // 處理開案
  const handleOpenCase = async () => {
    if (!selectedEvent) return;

    try {
      let treatmentId = selectedEvent.treatmentId;

      if (!treatmentId) {
        // 如果沒有 TreatmentId，創建新的治療案件
        log.api('創建新的治療案件');
        const createResponse = await api.post('/api/treatment/Insert', {
          PatientId: selectedEvent.patientId,
          DoctorId: selectedEvent.doctorId,
          // 其他必要的初始數據
        });

        treatmentId = createResponse.data.treatmentId;
        log.api('新治療案件創建成功', { treatmentId });

        // 更新 Schedule 的 TreatmentId
        await api.patch(`/api/schedule/${selectedEvent.id}/treatment`, {
          TreatmentId: treatmentId
        });
        log.api('Schedule TreatmentId 更新成功');
      }

      // 獲取完整的治療數據
      log.api('獲取治療數據', { treatmentId });

      const treatmentResponse = await api.get('/api/treatment', {
        params: {
          Id: treatmentId
        }
      });

      const treatmentData = treatmentResponse.data;

      // 跳轉到 TreatmentsDetailPage
      if (treatmentId) {
        navigate(`${ROUTES.TREATMENT_DETAIL}?id=${treatmentId}`, {
          state: { treatment: treatmentData, patient: { id: treatmentData.patientId } }
        });
      }

    } catch (error) {
      log.error('開案失敗', error);
      toast.current?.show({
        severity: 'error',
        summary: '開案失敗',
        detail: '無法開啟治療案件，請稍後再試',
        life: 3000
      });
    }
  };

  return (
    <div className="home-page">
      <Toast ref={toast} />
      <div className="grid justify-content-center">

        {/* 當日行程表 */}
        <div className="col-12 md:col-4 pl-4 pr-4">
          <div className="card">
            <h3 className='flex flex-wrap '>
              <div className='col-12 md:col-6 flex justify-content-center'>今日預約療程</div>
              <div className='col-12 md:col-6 flex justify-content-center'>({formatUtcToTaipei(new Date(), 'yyyy-MM-dd EEEE', { locale: zhTW })})
              </div>
            </h3>
            <div className="calendar-container">
              <FullCalendar
                plugins={[dayGridPlugin, timeGridPlugin]}
                height="auto"
                initialView="timeGridDay"
                locale="zh-tw"
                headerToolbar={{
                  left: '',
                  center: '',
                  right: '',
                }}
                dayHeaderFormat={{
                  day: 'numeric', // 顯示日期數字 (例如: 13)
                  weekday: 'short' // 顯示短格式的星期 (例如: 日, 一, 二)
                }}
                slotLabelFormat={{
                  hour: '2-digit', // 小時顯示為兩位數 (例如: 09)
                  minute: 'numeric', // 分鐘顯示為兩位數 (例如: 00)
                  hour12: false // 禁用 12 小時制，啟用 24 小時制
                }}
                allDaySlot={false}
                slotMinTime="09:00:00"
                slotMaxTime="22:00:00"
                events={todayEvents}
                eventDisplay="block"
                dayMaxEvents={false}
                businessHours={{
                  daysOfWeek: [1, 2, 3, 4, 5, 6],
                  startTime: '10:00',
                  endTime: '18:00',
                }}
                eventClick={handleEventClick} // 啟用點擊事件
                selectable={false} // 禁用選擇
                editable={false} // 禁用編輯
              />
            </div>
          </div>
        </div>

        
        <div className="col-12 md:col-8 pl-4 pr-4 flex flex-wrap">

          {/* Basic Chart */}
          <div className="col-12 md:col-6">
            <div className="card">
              <h3 className='flex justify-content-center'>月度統計(每小時更新)</h3>
              <Chart type="bar" 
              data={basicData} 
              options={chartOptions} />
            </div>
          </div>
          {/* Line Chart */}
          <div className="col-12 md:col-6">
            <div className="card">
              <h3 className='flex justify-content-center'>每週預約趨勢(每小時更新)</h3>
              <Chart type="line" 
              data={lineData} 
              options={chartOptions} />
            </div>
          </div>

        </div>
      </div>

      {/* 事件詳細視窗 */}
      <Dialog
        header="預約詳情"
        visible={showEventDialog}
        style={{ width: '450px' }}
        onHide={() => setShowEventDialog(false)}
        footer={
          <div className="flex justify-content-end gap-2">
            <Button
              label="取消"
              icon="pi pi-times"
              onClick={() => setShowEventDialog(false)}
              className="p-button-text"
            />
            <Button
              label="開案"
              icon="pi pi-folder-open"
              onClick={handleOpenCase}
              className="p-button-primary"
            />
          </div>
        }
      >
        {selectedEvent && (
          <div className="grid">
            <div className="col-12">
              <div className="field">
                <label className="font-bold">治療師:</label>
                <p className="m-0">{selectedEvent.doctorName}</p>
              </div>
            </div>
            <div className="col-12">
              <div className="field">
                <label className="font-bold">病患:</label>
                <p className="m-0">{selectedEvent.patientName}</p>
              </div>
            </div>
            <div className="col-6">
              <div className="field">
                <label className="font-bold">開始時間:</label>
                <p className="m-0">{formatUtcToTaipei(selectedEvent.start, 'HH:mm')}</p>
              </div>
            </div>
            <div className="col-6">
              <div className="field">
                <label className="font-bold">結束時間:</label>
                <p className="m-0">{formatUtcToTaipei(selectedEvent.end, 'HH:mm')}</p>
              </div>
            </div>
            {selectedEvent.description && (
              <div className="col-12">
                <div className="field">
                  <label className="font-bold">備註:</label>
                  <p className="m-0">{selectedEvent.description}</p>
                </div>
              </div>
            )}
            {selectedEvent.treatmentId && (
              <div className="col-12">
                <div className="field">
                  <label className="font-bold">治療案件編號:</label>
                  <p className="m-0">{selectedEvent.treatmentId}</p>
                </div>
              </div>
            )}
          </div>
        )}
      </Dialog>

      <Toast ref={toast} />
    </div>
  );
};

export default HomePage;
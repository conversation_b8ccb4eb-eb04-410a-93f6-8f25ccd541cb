# HTTP 到 HTTPS 的重定向，並處理 Certbot 驗證
server {
    listen 80;
    server_name nbhphysical.idv.tw www.nbhphysical.idv.tw;

    location /.well-known/acme-challenge/ {
        root /var/lib/letsencrypt;
        allow all;
    }

    location / {
        return 301 https://$host$request_uri;
    }
}

# HTTPS 設定
server {
    listen 443 ssl;
    server_name nbhphysical.idv.tw www.nbhphysical.idv.tw;

    ssl_certificate /etc/letsencrypt/live/nbhphysical.idv.tw/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/nbhphysical.idv.tw/privkey.pem;

    # 推薦的 SSL 設定，增強安全性
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 10m;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers "ECDHE-ECDSA-AES128-GCM-SHA256:ECDHE-RSA-AES128-GCM-SHA256:ECDHE-ECDSA-AES256-GCM-SHA384:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES128-GCM-SHA256:DHE-RSA-AES256-GCM-SHA384:ECDHE-ECDSA-CHACHA20-POLY1305:ECDHE-RSA-CHACHA20-POLY1305:DHE-RSA-CHACHA20-POLY1305";
    ssl_prefer_server_ciphers on;
    ssl_stapling on;
    ssl_stapling_verify on;
    resolver ******* ******* valid=300s;
    resolver_timeout 5s;
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
	
	# 代理 SignalR/WebSocket
	location /reportHub { 
        proxy_pass http://backend:5000; # 代理到您的後端服務 (假設在 5000 埠)

        # 這是 WebSocket/SignalR 代理的關鍵頭部
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme; # 確保後端知道原始請求是 HTTPS

        # 可選：提高緩衝區大小以避免大型消息問題
        proxy_buffering off;
        proxy_read_timeout 86400s; # 增加讀取超時時間，用於持久連線
        proxy_send_timeout 86400s; # 增加發送超時時間
    }
	
	# 提供靜態圖片檔案
    location /uploads/ { # 定義公開 URL 路徑，例如 /uploads/
        alias /app/uploads/; # 將請求映射到 Nginx 容器內部的實際路徑

        # 可選：設定檔案快取、到期時間等，以提高效能
        expires 30d;
        add_header Cache-Control "public";
        
        # 允許跨域請求，如果你的前端和後端域名不同（雖然這裡同域名但有時會需要）
        # add_header Access-Control-Allow-Origin "*"; 
    }
	
    location /api {
        proxy_pass http://backend:5000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
		proxy_read_timeout 300s; # 增加讀取超時時間，例如 5 分鐘
		proxy_send_timeout 300s;
    }

    location / {
        proxy_pass http://frontend:80;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
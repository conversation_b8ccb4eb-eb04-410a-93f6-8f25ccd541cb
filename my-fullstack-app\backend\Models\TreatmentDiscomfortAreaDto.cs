using System.ComponentModel.DataAnnotations;

namespace MyApi.Models
{
    public class TreatmentDiscomfortAreaDto
    {
        public int? Id { get; set; }
        
        [Required]
        public string FrontAndBack { get; set; } = string.Empty;
        
        [Required]
        public string DiscomfortArea { get; set; } = string.Empty;
        
        [Required]
        public string DiscomfortSituation { get; set; } = string.Empty;
        
        [Range(0, 10)]
        public int DiscomfortDegree { get; set; }
    }
}

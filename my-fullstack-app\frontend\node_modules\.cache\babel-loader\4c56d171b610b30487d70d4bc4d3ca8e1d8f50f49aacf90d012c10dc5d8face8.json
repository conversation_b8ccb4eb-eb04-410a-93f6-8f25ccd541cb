{"ast": null, "code": "import { isSameWeek } from \"../../../isSameWeek.js\";\nimport { toDate } from \"../../../toDate.js\";\nconst accusativeWeekdays = [\"нядзелю\", \"панядзелак\", \"аўторак\", \"сераду\", \"чацьвер\", \"пятніцу\", \"суботу\"];\nfunction lastWeek(day) {\n  const weekday = accusativeWeekdays[day];\n  switch (day) {\n    case 0:\n    case 3:\n    case 5:\n    case 6:\n      return \"'у мінулую \" + weekday + \" а' p\";\n    case 1:\n    case 2:\n    case 4:\n      return \"'у мінулы \" + weekday + \" а' p\";\n  }\n}\nfunction thisWeek(day) {\n  const weekday = accusativeWeekdays[day];\n  return \"'у \" + weekday + \" а' p\";\n}\nfunction nextWeek(day) {\n  const weekday = accusativeWeekdays[day];\n  switch (day) {\n    case 0:\n    case 3:\n    case 5:\n    case 6:\n      return \"'у наступную \" + weekday + \" а' p\";\n    case 1:\n    case 2:\n    case 4:\n      return \"'у наступны \" + weekday + \" а' p\";\n  }\n}\nconst lastWeekFormat = (dirtyDate, baseDate, options) => {\n  const date = toDate(dirtyDate);\n  const day = date.getDay();\n  if (isSameWeek(date, baseDate, options)) {\n    return thisWeek(day);\n  } else {\n    return lastWeek(day);\n  }\n};\nconst nextWeekFormat = (dirtyDate, baseDate, options) => {\n  const date = toDate(dirtyDate);\n  const day = date.getDay();\n  if (isSameWeek(date, baseDate, options)) {\n    return thisWeek(day);\n  } else {\n    return nextWeek(day);\n  }\n};\nconst formatRelativeLocale = {\n  lastWeek: lastWeekFormat,\n  yesterday: \"'учора а' p\",\n  today: \"'сёньня а' p\",\n  tomorrow: \"'заўтра а' p\",\n  nextWeek: nextWeekFormat,\n  other: \"P\"\n};\nexport const formatRelative = (token, date, baseDate, options) => {\n  const format = formatRelativeLocale[token];\n  if (typeof format === \"function\") {\n    return format(date, baseDate, options);\n  }\n  return format;\n};", "map": {"version": 3, "names": ["isSameWeek", "toDate", "accusativeWeekdays", "lastWeek", "day", "weekday", "thisWeek", "nextWeek", "lastWeekFormat", "dirtyDate", "baseDate", "options", "date", "getDay", "nextWeekFormat", "formatRelativeLocale", "yesterday", "today", "tomorrow", "other", "formatRelative", "token", "format"], "sources": ["C:/Users/<USER>/Desktop/demo-git/demo-react/my-fullstack-app/frontend/node_modules/date-fns/locale/be-tarask/_lib/formatRelative.js"], "sourcesContent": ["import { isSameWeek } from \"../../../isSameWeek.js\";\nimport { toDate } from \"../../../toDate.js\";\n\nconst accusativeWeekdays = [\n  \"нядзелю\",\n  \"панядзелак\",\n  \"аўторак\",\n  \"сераду\",\n  \"чацьвер\",\n  \"пятніцу\",\n  \"суботу\",\n];\n\nfunction lastWeek(day) {\n  const weekday = accusativeWeekdays[day];\n\n  switch (day) {\n    case 0:\n    case 3:\n    case 5:\n    case 6:\n      return \"'у мінулую \" + weekday + \" а' p\";\n    case 1:\n    case 2:\n    case 4:\n      return \"'у мінулы \" + weekday + \" а' p\";\n  }\n}\n\nfunction thisWeek(day) {\n  const weekday = accusativeWeekdays[day];\n\n  return \"'у \" + weekday + \" а' p\";\n}\n\nfunction nextWeek(day) {\n  const weekday = accusativeWeekdays[day];\n\n  switch (day) {\n    case 0:\n    case 3:\n    case 5:\n    case 6:\n      return \"'у наступную \" + weekday + \" а' p\";\n    case 1:\n    case 2:\n    case 4:\n      return \"'у наступны \" + weekday + \" а' p\";\n  }\n}\n\nconst lastWeekFormat = (dirtyDate, baseDate, options) => {\n  const date = toDate(dirtyDate);\n  const day = date.getDay();\n  if (isSameWeek(date, baseDate, options)) {\n    return thisWeek(day);\n  } else {\n    return lastWeek(day);\n  }\n};\n\nconst nextWeekFormat = (dirtyDate, baseDate, options) => {\n  const date = toDate(dirtyDate);\n  const day = date.getDay();\n  if (isSameWeek(date, baseDate, options)) {\n    return thisWeek(day);\n  } else {\n    return nextWeek(day);\n  }\n};\n\nconst formatRelativeLocale = {\n  lastWeek: lastWeekFormat,\n  yesterday: \"'учора а' p\",\n  today: \"'сёньня а' p\",\n  tomorrow: \"'заўтра а' p\",\n  nextWeek: nextWeekFormat,\n  other: \"P\",\n};\n\nexport const formatRelative = (token, date, baseDate, options) => {\n  const format = formatRelativeLocale[token];\n\n  if (typeof format === \"function\") {\n    return format(date, baseDate, options);\n  }\n\n  return format;\n};\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,wBAAwB;AACnD,SAASC,MAAM,QAAQ,oBAAoB;AAE3C,MAAMC,kBAAkB,GAAG,CACzB,SAAS,EACT,YAAY,EACZ,SAAS,EACT,QAAQ,EACR,SAAS,EACT,SAAS,EACT,QAAQ,CACT;AAED,SAASC,QAAQA,CAACC,GAAG,EAAE;EACrB,MAAMC,OAAO,GAAGH,kBAAkB,CAACE,GAAG,CAAC;EAEvC,QAAQA,GAAG;IACT,KAAK,CAAC;IACN,KAAK,CAAC;IACN,KAAK,CAAC;IACN,KAAK,CAAC;MACJ,OAAO,aAAa,GAAGC,OAAO,GAAG,OAAO;IAC1C,KAAK,CAAC;IACN,KAAK,CAAC;IACN,KAAK,CAAC;MACJ,OAAO,YAAY,GAAGA,OAAO,GAAG,OAAO;EAC3C;AACF;AAEA,SAASC,QAAQA,CAACF,GAAG,EAAE;EACrB,MAAMC,OAAO,GAAGH,kBAAkB,CAACE,GAAG,CAAC;EAEvC,OAAO,KAAK,GAAGC,OAAO,GAAG,OAAO;AAClC;AAEA,SAASE,QAAQA,CAACH,GAAG,EAAE;EACrB,MAAMC,OAAO,GAAGH,kBAAkB,CAACE,GAAG,CAAC;EAEvC,QAAQA,GAAG;IACT,KAAK,CAAC;IACN,KAAK,CAAC;IACN,KAAK,CAAC;IACN,KAAK,CAAC;MACJ,OAAO,eAAe,GAAGC,OAAO,GAAG,OAAO;IAC5C,KAAK,CAAC;IACN,KAAK,CAAC;IACN,KAAK,CAAC;MACJ,OAAO,cAAc,GAAGA,OAAO,GAAG,OAAO;EAC7C;AACF;AAEA,MAAMG,cAAc,GAAGA,CAACC,SAAS,EAAEC,QAAQ,EAAEC,OAAO,KAAK;EACvD,MAAMC,IAAI,GAAGX,MAAM,CAACQ,SAAS,CAAC;EAC9B,MAAML,GAAG,GAAGQ,IAAI,CAACC,MAAM,CAAC,CAAC;EACzB,IAAIb,UAAU,CAACY,IAAI,EAAEF,QAAQ,EAAEC,OAAO,CAAC,EAAE;IACvC,OAAOL,QAAQ,CAACF,GAAG,CAAC;EACtB,CAAC,MAAM;IACL,OAAOD,QAAQ,CAACC,GAAG,CAAC;EACtB;AACF,CAAC;AAED,MAAMU,cAAc,GAAGA,CAACL,SAAS,EAAEC,QAAQ,EAAEC,OAAO,KAAK;EACvD,MAAMC,IAAI,GAAGX,MAAM,CAACQ,SAAS,CAAC;EAC9B,MAAML,GAAG,GAAGQ,IAAI,CAACC,MAAM,CAAC,CAAC;EACzB,IAAIb,UAAU,CAACY,IAAI,EAAEF,QAAQ,EAAEC,OAAO,CAAC,EAAE;IACvC,OAAOL,QAAQ,CAACF,GAAG,CAAC;EACtB,CAAC,MAAM;IACL,OAAOG,QAAQ,CAACH,GAAG,CAAC;EACtB;AACF,CAAC;AAED,MAAMW,oBAAoB,GAAG;EAC3BZ,QAAQ,EAAEK,cAAc;EACxBQ,SAAS,EAAE,aAAa;EACxBC,KAAK,EAAE,cAAc;EACrBC,QAAQ,EAAE,cAAc;EACxBX,QAAQ,EAAEO,cAAc;EACxBK,KAAK,EAAE;AACT,CAAC;AAED,OAAO,MAAMC,cAAc,GAAGA,CAACC,KAAK,EAAET,IAAI,EAAEF,QAAQ,EAAEC,OAAO,KAAK;EAChE,MAAMW,MAAM,GAAGP,oBAAoB,CAACM,KAAK,CAAC;EAE1C,IAAI,OAAOC,MAAM,KAAK,UAAU,EAAE;IAChC,OAAOA,MAAM,CAACV,IAAI,EAAEF,QAAQ,EAAEC,OAAO,CAAC;EACxC;EAEA,OAAOW,MAAM;AACf,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "import { buildMatchFn } from \"../../_lib/buildMatchFn.js\";\nimport { buildMatchPatternFn } from \"../../_lib/buildMatchPatternFn.js\";\nconst matchOrdinalNumberPattern = /^(\\d+)(\\.)/i;\nconst parseOrdinalNumberPattern = /\\d+/i;\nconst matchEraPatterns = {\n  narrow: /^(e|j)/i,\n  abbreviated: /^(eaa.|jaa.)/i,\n  wide: /^(ennen ajanlaskun alkua|jälkeen ajanlaskun alun)/i\n};\nconst parseEraPatterns = {\n  any: [/^e/i, /^j/i]\n};\nconst matchQuarterPatterns = {\n  narrow: /^[1234]/i,\n  abbreviated: /^q[1234]/i,\n  wide: /^[1234]\\.? kvartaali/i\n};\nconst parseQuarterPatterns = {\n  any: [/1/i, /2/i, /3/i, /4/i]\n};\nconst matchMonthPatterns = {\n  narrow: /^[thmkeslj]/i,\n  abbreviated: /^(tammi|helmi|maalis|huhti|touko|kesä|heinä|elo|syys|loka|marras|joulu)/i,\n  wide: /^(tammikuu|helmikuu|maaliskuu|huhtikuu|toukokuu|kesäkuu|heinäkuu|elokuu|syyskuu|lokakuu|marraskuu|joulukuu)(ta)?/i\n};\nconst parseMonthPatterns = {\n  narrow: [/^t/i, /^h/i, /^m/i, /^h/i, /^t/i, /^k/i, /^h/i, /^e/i, /^s/i, /^l/i, /^m/i, /^j/i],\n  any: [/^ta/i, /^hel/i, /^maa/i, /^hu/i, /^to/i, /^k/i, /^hei/i, /^e/i, /^s/i, /^l/i, /^mar/i, /^j/i]\n};\nconst matchDayPatterns = {\n  narrow: /^[smtkpl]/i,\n  short: /^(su|ma|ti|ke|to|pe|la)/i,\n  abbreviated: /^(sunn.|maan.|tiis.|kesk.|torst.|perj.|la)/i,\n  wide: /^(sunnuntai|maanantai|tiistai|keskiviikko|torstai|perjantai|lauantai)(na)?/i\n};\nconst parseDayPatterns = {\n  narrow: [/^s/i, /^m/i, /^t/i, /^k/i, /^t/i, /^p/i, /^l/i],\n  any: [/^s/i, /^m/i, /^ti/i, /^k/i, /^to/i, /^p/i, /^l/i]\n};\nconst matchDayPeriodPatterns = {\n  narrow: /^(ap|ip|keskiyö|keskipäivä|aamupäivällä|iltapäivällä|illalla|yöllä)/i,\n  any: /^(ap|ip|keskiyöllä|keskipäivällä|aamupäivällä|iltapäivällä|illalla|yöllä)/i\n};\nconst parseDayPeriodPatterns = {\n  any: {\n    am: /^ap/i,\n    pm: /^ip/i,\n    midnight: /^keskiyö/i,\n    noon: /^keskipäivä/i,\n    morning: /aamupäivällä/i,\n    afternoon: /iltapäivällä/i,\n    evening: /illalla/i,\n    night: /yöllä/i\n  }\n};\nexport const match = {\n  ordinalNumber: buildMatchPatternFn({\n    matchPattern: matchOrdinalNumberPattern,\n    parsePattern: parseOrdinalNumberPattern,\n    valueCallback: value => parseInt(value, 10)\n  }),\n  era: buildMatchFn({\n    matchPatterns: matchEraPatterns,\n    defaultMatchWidth: \"wide\",\n    parsePatterns: parseEraPatterns,\n    defaultParseWidth: \"any\"\n  }),\n  quarter: buildMatchFn({\n    matchPatterns: matchQuarterPatterns,\n    defaultMatchWidth: \"wide\",\n    parsePatterns: parseQuarterPatterns,\n    defaultParseWidth: \"any\",\n    valueCallback: index => index + 1\n  }),\n  month: buildMatchFn({\n    matchPatterns: matchMonthPatterns,\n    defaultMatchWidth: \"wide\",\n    parsePatterns: parseMonthPatterns,\n    defaultParseWidth: \"any\"\n  }),\n  day: buildMatchFn({\n    matchPatterns: matchDayPatterns,\n    defaultMatchWidth: \"wide\",\n    parsePatterns: parseDayPatterns,\n    defaultParseWidth: \"any\"\n  }),\n  dayPeriod: buildMatchFn({\n    matchPatterns: matchDayPeriodPatterns,\n    defaultMatchWidth: \"any\",\n    parsePatterns: parseDayPeriodPatterns,\n    defaultParseWidth: \"any\"\n  })\n};", "map": {"version": 3, "names": ["buildMatchFn", "buildMatchPatternFn", "matchOrdinalNumberPattern", "parseOrdinalNumberPattern", "matchEraPatterns", "narrow", "abbreviated", "wide", "parseEraPatterns", "any", "matchQuarterPatterns", "parseQuarterPatterns", "matchMonthPatterns", "parseMonthPatterns", "matchDayPatterns", "short", "parseDayPatterns", "matchDayPeriodPatterns", "parseDayPeriodPatterns", "am", "pm", "midnight", "noon", "morning", "afternoon", "evening", "night", "match", "ordinalNumber", "matchPattern", "parsePattern", "valueCallback", "value", "parseInt", "era", "matchPatterns", "defaultMatchWidth", "parsePatterns", "defaultParseWidth", "quarter", "index", "month", "day", "<PERSON><PERSON><PERSON><PERSON>"], "sources": ["C:/Users/<USER>/Desktop/demo-git/demo-react/my-fullstack-app/frontend/node_modules/date-fns/locale/fi/_lib/match.js"], "sourcesContent": ["import { buildMatchFn } from \"../../_lib/buildMatchFn.js\";\nimport { buildMatchPatternFn } from \"../../_lib/buildMatchPatternFn.js\";\n\nconst matchOrdinalNumberPattern = /^(\\d+)(\\.)/i;\nconst parseOrdinalNumberPattern = /\\d+/i;\n\nconst matchEraPatterns = {\n  narrow: /^(e|j)/i,\n  abbreviated: /^(eaa.|jaa.)/i,\n  wide: /^(ennen ajanlaskun alkua|jälkeen ajanlaskun alun)/i,\n};\nconst parseEraPatterns = {\n  any: [/^e/i, /^j/i],\n};\n\nconst matchQuarterPatterns = {\n  narrow: /^[1234]/i,\n  abbreviated: /^q[1234]/i,\n  wide: /^[1234]\\.? kvartaali/i,\n};\nconst parseQuarterPatterns = {\n  any: [/1/i, /2/i, /3/i, /4/i],\n};\n\nconst matchMonthPatterns = {\n  narrow: /^[thmkeslj]/i,\n  abbreviated:\n    /^(tammi|helmi|maalis|huhti|touko|kesä|heinä|elo|syys|loka|marras|joulu)/i,\n  wide: /^(tammikuu|helmikuu|maaliskuu|huhtikuu|toukokuu|kesäkuu|heinäkuu|elokuu|syyskuu|lokakuu|marraskuu|joulukuu)(ta)?/i,\n};\nconst parseMonthPatterns = {\n  narrow: [\n    /^t/i,\n    /^h/i,\n    /^m/i,\n    /^h/i,\n    /^t/i,\n    /^k/i,\n    /^h/i,\n    /^e/i,\n    /^s/i,\n    /^l/i,\n    /^m/i,\n    /^j/i,\n  ],\n\n  any: [\n    /^ta/i,\n    /^hel/i,\n    /^maa/i,\n    /^hu/i,\n    /^to/i,\n    /^k/i,\n    /^hei/i,\n    /^e/i,\n    /^s/i,\n    /^l/i,\n    /^mar/i,\n    /^j/i,\n  ],\n};\n\nconst matchDayPatterns = {\n  narrow: /^[smtkpl]/i,\n  short: /^(su|ma|ti|ke|to|pe|la)/i,\n  abbreviated: /^(sunn.|maan.|tiis.|kesk.|torst.|perj.|la)/i,\n  wide: /^(sunnuntai|maanantai|tiistai|keskiviikko|torstai|perjantai|lauantai)(na)?/i,\n};\nconst parseDayPatterns = {\n  narrow: [/^s/i, /^m/i, /^t/i, /^k/i, /^t/i, /^p/i, /^l/i],\n  any: [/^s/i, /^m/i, /^ti/i, /^k/i, /^to/i, /^p/i, /^l/i],\n};\n\nconst matchDayPeriodPatterns = {\n  narrow:\n    /^(ap|ip|keskiyö|keskipäivä|aamupäivällä|iltapäivällä|illalla|yöllä)/i,\n  any: /^(ap|ip|keskiyöllä|keskipäivällä|aamupäivällä|iltapäivällä|illalla|yöllä)/i,\n};\nconst parseDayPeriodPatterns = {\n  any: {\n    am: /^ap/i,\n    pm: /^ip/i,\n    midnight: /^keskiyö/i,\n    noon: /^keskipäivä/i,\n    morning: /aamupäivällä/i,\n    afternoon: /iltapäivällä/i,\n    evening: /illalla/i,\n    night: /yöllä/i,\n  },\n};\n\nexport const match = {\n  ordinalNumber: buildMatchPatternFn({\n    matchPattern: matchOrdinalNumberPattern,\n    parsePattern: parseOrdinalNumberPattern,\n    valueCallback: (value) => parseInt(value, 10),\n  }),\n\n  era: buildMatchFn({\n    matchPatterns: matchEraPatterns,\n    defaultMatchWidth: \"wide\",\n    parsePatterns: parseEraPatterns,\n    defaultParseWidth: \"any\",\n  }),\n\n  quarter: buildMatchFn({\n    matchPatterns: matchQuarterPatterns,\n    defaultMatchWidth: \"wide\",\n    parsePatterns: parseQuarterPatterns,\n    defaultParseWidth: \"any\",\n    valueCallback: (index) => index + 1,\n  }),\n\n  month: buildMatchFn({\n    matchPatterns: matchMonthPatterns,\n    defaultMatchWidth: \"wide\",\n    parsePatterns: parseMonthPatterns,\n    defaultParseWidth: \"any\",\n  }),\n\n  day: buildMatchFn({\n    matchPatterns: matchDayPatterns,\n    defaultMatchWidth: \"wide\",\n    parsePatterns: parseDayPatterns,\n    defaultParseWidth: \"any\",\n  }),\n\n  dayPeriod: buildMatchFn({\n    matchPatterns: matchDayPeriodPatterns,\n    defaultMatchWidth: \"any\",\n    parsePatterns: parseDayPeriodPatterns,\n    defaultParseWidth: \"any\",\n  }),\n};\n"], "mappings": "AAAA,SAASA,YAAY,QAAQ,4BAA4B;AACzD,SAASC,mBAAmB,QAAQ,mCAAmC;AAEvE,MAAMC,yBAAyB,GAAG,aAAa;AAC/C,MAAMC,yBAAyB,GAAG,MAAM;AAExC,MAAMC,gBAAgB,GAAG;EACvBC,MAAM,EAAE,SAAS;EACjBC,WAAW,EAAE,eAAe;EAC5BC,IAAI,EAAE;AACR,CAAC;AACD,MAAMC,gBAAgB,GAAG;EACvBC,GAAG,EAAE,CAAC,KAAK,EAAE,KAAK;AACpB,CAAC;AAED,MAAMC,oBAAoB,GAAG;EAC3BL,MAAM,EAAE,UAAU;EAClBC,WAAW,EAAE,WAAW;EACxBC,IAAI,EAAE;AACR,CAAC;AACD,MAAMI,oBAAoB,GAAG;EAC3BF,GAAG,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI;AAC9B,CAAC;AAED,MAAMG,kBAAkB,GAAG;EACzBP,MAAM,EAAE,cAAc;EACtBC,WAAW,EACT,0EAA0E;EAC5EC,IAAI,EAAE;AACR,CAAC;AACD,MAAMM,kBAAkB,GAAG;EACzBR,MAAM,EAAE,CACN,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,CACN;EAEDI,GAAG,EAAE,CACH,MAAM,EACN,OAAO,EACP,OAAO,EACP,MAAM,EACN,MAAM,EACN,KAAK,EACL,OAAO,EACP,KAAK,EACL,KAAK,EACL,KAAK,EACL,OAAO,EACP,KAAK;AAET,CAAC;AAED,MAAMK,gBAAgB,GAAG;EACvBT,MAAM,EAAE,YAAY;EACpBU,KAAK,EAAE,0BAA0B;EACjCT,WAAW,EAAE,6CAA6C;EAC1DC,IAAI,EAAE;AACR,CAAC;AACD,MAAMS,gBAAgB,GAAG;EACvBX,MAAM,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC;EACzDI,GAAG,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,KAAK;AACzD,CAAC;AAED,MAAMQ,sBAAsB,GAAG;EAC7BZ,MAAM,EACJ,sEAAsE;EACxEI,GAAG,EAAE;AACP,CAAC;AACD,MAAMS,sBAAsB,GAAG;EAC7BT,GAAG,EAAE;IACHU,EAAE,EAAE,MAAM;IACVC,EAAE,EAAE,MAAM;IACVC,QAAQ,EAAE,WAAW;IACrBC,IAAI,EAAE,cAAc;IACpBC,OAAO,EAAE,eAAe;IACxBC,SAAS,EAAE,eAAe;IAC1BC,OAAO,EAAE,UAAU;IACnBC,KAAK,EAAE;EACT;AACF,CAAC;AAED,OAAO,MAAMC,KAAK,GAAG;EACnBC,aAAa,EAAE3B,mBAAmB,CAAC;IACjC4B,YAAY,EAAE3B,yBAAyB;IACvC4B,YAAY,EAAE3B,yBAAyB;IACvC4B,aAAa,EAAGC,KAAK,IAAKC,QAAQ,CAACD,KAAK,EAAE,EAAE;EAC9C,CAAC,CAAC;EAEFE,GAAG,EAAElC,YAAY,CAAC;IAChBmC,aAAa,EAAE/B,gBAAgB;IAC/BgC,iBAAiB,EAAE,MAAM;IACzBC,aAAa,EAAE7B,gBAAgB;IAC/B8B,iBAAiB,EAAE;EACrB,CAAC,CAAC;EAEFC,OAAO,EAAEvC,YAAY,CAAC;IACpBmC,aAAa,EAAEzB,oBAAoB;IACnC0B,iBAAiB,EAAE,MAAM;IACzBC,aAAa,EAAE1B,oBAAoB;IACnC2B,iBAAiB,EAAE,KAAK;IACxBP,aAAa,EAAGS,KAAK,IAAKA,KAAK,GAAG;EACpC,CAAC,CAAC;EAEFC,KAAK,EAAEzC,YAAY,CAAC;IAClBmC,aAAa,EAAEvB,kBAAkB;IACjCwB,iBAAiB,EAAE,MAAM;IACzBC,aAAa,EAAExB,kBAAkB;IACjCyB,iBAAiB,EAAE;EACrB,CAAC,CAAC;EAEFI,GAAG,EAAE1C,YAAY,CAAC;IAChBmC,aAAa,EAAErB,gBAAgB;IAC/BsB,iBAAiB,EAAE,MAAM;IACzBC,aAAa,EAAErB,gBAAgB;IAC/BsB,iBAAiB,EAAE;EACrB,CAAC,CAAC;EAEFK,SAAS,EAAE3C,YAAY,CAAC;IACtBmC,aAAa,EAAElB,sBAAsB;IACrCmB,iBAAiB,EAAE,KAAK;IACxBC,aAAa,EAAEnB,sBAAsB;IACrCoB,iBAAiB,EAAE;EACrB,CAAC;AACH,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
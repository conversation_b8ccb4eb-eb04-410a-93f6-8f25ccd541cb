{"version": 3, "file": "static/js/658.1d599276.chunk.js", "mappings": "2IAmBe,SAASA,IACtB,MAAOC,EAAUC,IAAeC,EAAAA,EAAAA,UAA8B,KACvDC,EAASC,IAAcF,EAAAA,EAAAA,WAAS,GAUvC,OARAG,EAAAA,EAAAA,YAAU,KAERC,EAAAA,EAAIC,IAAyB,2BAC1BC,MAAKC,GAAOR,EAAYQ,EAAIC,QAC5BC,OAAMC,GAAOC,QAAQC,MAAM,aAAcF,KACzCG,SAAQ,IAAMX,GAAW,OACzB,IAEE,CAAEJ,WAAUG,UACrB,C,+OC/BA,MAEA,EAFkBa,sC,aC6ClB,MAgkBA,EAhkBuCC,KAAO,IAADC,EAAAC,EAAAC,EAAAC,EAC3C,MAAMC,GAAWC,EAAAA,EAAAA,MACXC,EAAwB,QAAjBN,EAAGI,EAASG,aAAK,IAAAP,OAAA,EAAdA,EAAgBM,QAC1BE,EAA0B,QAAjBP,EAAGG,EAASG,aAAK,IAAAN,OAAA,EAAdA,EAAgBO,UAC5BC,GAAQC,EAAAA,EAAAA,QAAc,MACtBC,GAAaD,EAAAA,EAAAA,QAAgB,OAC5BE,EAAaC,IAAkB7B,EAAAA,EAAAA,UAAS,GACzC8B,GAAWC,EAAAA,EAAAA,OACX,SAAEjC,EAAQ,QAAEG,IAAYJ,EAAAA,EAAAA,MACvBmC,EAAUC,IAAejC,EAAAA,EAAAA,UAAoB,CAClDkC,QAAS,GACTC,KAAM,EACNC,iBAAkB,GAClBC,eAAgB,GAChBC,iBAAkB,GAClBC,aAAc,GACdC,gBAAiB,GACjBC,wBAAyB,GACzBC,WAAY,GACZC,UAAW,GACXC,WAAY,GACZC,KAAM,GACNC,WAAkB,OAAPxB,QAAO,IAAPA,OAAO,EAAPA,EAASyB,KAAM,EAC1BC,uBAAwB,KACxBC,gBAAiB,MAIZC,EAAuBC,IAA4BnD,EAAAA,EAAAA,UAAyB,CACjFoD,aAAc,GACdC,eAAgB,GAChBC,oBAAqB,GACrBC,iBAAkB,KAGpBpD,EAAAA,EAAAA,YAAU,KACFqB,IACAS,GAAWuB,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAC,CAAC,EACRhC,GAAS,IACZwB,uBAAwBxB,EAAUwB,uBAAyB,IAAIS,KAAKjC,EAAUwB,wBAA0B,QAGpF,KAAnBxB,EAAUW,MACXN,EAAe,GAEK,KAAnBL,EAAUW,MACXN,EAAe,MAGpB,CAACL,IAEN,MAAMkC,EAAgBC,IACd,MAAM,KAAEC,EAAI,MAAEC,GAAUF,EAAEG,OAC1B7B,GAAa8B,IAAIP,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GAAWO,GAAI,IAAE,CAACH,GAAOC,OAG5CG,EAAuBA,CAACJ,EAAuBC,EAAeI,KAClEhC,GAAY8B,IACV,MAAMG,EAAWH,EAAKH,IAAS,GACzBO,EAAaD,EAAUA,EAAQE,MAAM,KAAO,GAE5CC,EAAaJ,EACfE,EAAWG,SAAST,GAClBM,EACA,IAAIA,EAAYN,GAClBM,EAAWI,QAAOC,GAAQA,IAASX,IAEzC,OAAAL,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GAAYO,GAAI,IAAE,CAACH,GAAOS,EAAWI,KAAK,WAStCC,EAA6BA,CAACC,EAA6Bd,KAC/DV,GAAyBY,IAAIP,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GAAUO,GAAI,IAAE,CAACY,GAAQd,OA6ElDe,EAAeC,UAInB,GAAY,KAAT1C,EAAY,CACkE,IAAD2C,EAIPC,EAJvE,GAAgC,KAA7B/C,EAASQ,iBAA+D,KAArCR,EAASS,wBAE7C,YADa,QAAbqC,EAAArD,EAAMyC,eAAO,IAAAY,GAAbA,EAAeE,KAAK,CAAEC,SAAU,QAASC,QAAS,2BAAQC,OAAQ,0FAGpE,GAAgC,KAA7BnD,EAASQ,kBAA2BR,EAASgB,uBAE9C,YADa,QAAb+B,EAAAtD,EAAMyC,eAAO,IAAAa,GAAbA,EAAeC,KAAK,CAAEC,SAAU,QAASC,QAAS,2BAAQC,OAAQ,6EAGtE,CAEyB,KAArBnD,EAASE,cAEH9B,EAAAA,EAAIgF,KAAK,wBAAyBpD,GACvC1B,MAAMC,IAAS,IAAD8E,EACbpD,GACG8B,IAAIP,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GAAUO,GAAI,IAAC7B,QAAS3B,EAAIC,KAAK0B,YAE3B,QAAbmD,EAAA5D,EAAMyC,eAAO,IAAAmB,GAAbA,EAAeL,KAAK,CAAEC,SAAU,UAAWC,QAAS,eAAMC,OAAQ5E,EAAIC,KAAK8E,SAC5E7E,OAAOC,IAAG,IAAA6E,EAAA,OAAkB,QAAlBA,EAAK9D,EAAMyC,eAAO,IAAAqB,OAAA,EAAbA,EAAeP,KAAK,CAAEC,SAAU,QAASC,QAAS,2BAAQC,OAAQzE,EAAI8E,cAKxFxD,EAASG,KAAOH,EAASG,KAAOA,EAAOH,EAASG,KAAOA,EACvDF,GACO8B,IAAIP,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GAAUO,GAAI,IAAC5B,KAAMH,EAASG,eAEnC/B,EAAAA,EAAIqF,IAAI,wBAAyBzD,GACpC1B,MAAK,SAAAoF,EAAA,OAAmB,QAAnBA,EAAMjE,EAAMyC,eAAO,IAAAwB,OAAA,EAAbA,EAAeV,KAAK,CAAEC,SAAU,UAAWC,QAAS,eAAMC,OA/B7D,kDAgCR1E,OAAOC,IAAG,IAAAiF,EAAA,OAAkB,QAAlBA,EAAKlE,EAAMyC,eAAO,IAAAyB,OAAA,EAAbA,EAAeX,KAAK,CAAEC,SAAU,QAASC,QAAS,2BAAQC,OAAQzE,EAAI8E,cAG9E,KAATrD,GACDyD,YAAW,IAAM9D,EAAS,gBAAgB,OAKxC+D,EAAqBhB,MAAOiB,EAA+BC,KAAkC,IAADC,EAChG,MAAMC,EAAkB,QAAdD,EAAGF,EAAMI,aAAK,IAAAF,OAAA,EAAXA,EAAc,GAC3B,IAAKC,EAAM,OAEa,IAADE,EAAvB,IAAKnE,EAASE,QAMZ,YALa,QAAbiE,EAAA1E,EAAMyC,eAAO,IAAAiC,GAAbA,EAAenB,KAAK,CAClBC,SAAU,QACVC,QAAS,2BACTC,OAAQ,8HAKZ,MAAMiB,EAAiB,IAAIC,SAC3BD,EAAeE,OAAO,OAAQL,GAC9BG,EAAeE,OAAO,UAAWtE,EAASE,SAE1C,IAAK,IAADqE,EACF,MAAMC,QAAiBpG,EAAAA,EAAIgF,KAAK,yBAA0BgB,EAAgB,CACxEK,QAAS,CACP,eAAgB,0BAId,SAAEC,GAAaF,EAAShG,KAGxBmG,GAAenD,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GAChBxB,GAAQ,IACX,CAAU,aAAT+D,EAAsB,kBAAoB,2BAA4BW,IAIzEzE,EAAY0E,SAGNvG,EAAAA,EAAIqF,IAAI,wBAAyBkB,GAE1B,QAAbJ,EAAA9E,EAAMyC,eAAO,IAAAqC,GAAbA,EAAevB,KAAK,CAClBC,SAAU,UACVC,QAAS,eACTC,OAAQ,sEAGZ,CAAE,MAAOvE,GAAa,IAADgG,EAAAC,EAAAC,EACnB,MAAMC,GAA6B,QAAdH,EAAAhG,EAAM4F,gBAAQ,IAAAI,GAAM,QAANC,EAAdD,EAAgBpG,YAAI,IAAAqG,OAAN,EAAdA,EAAsBG,UAAWpG,EAAMoG,SAAW,uCAC1D,QAAbF,EAAArF,EAAMyC,eAAO,IAAA4C,GAAbA,EAAe9B,KAAK,CAClBC,SAAU,QACVC,QAAS,2BACTC,OAAQ4B,GAEZ,GAGIE,EAAcC,IAAqB,IAADC,EACtC,OAAwD,QAAjDA,EAAArH,EAASsH,MAAKC,GAASA,EAAMH,UAAYA,WAAQ,IAAAC,OAAA,EAAjDA,EAAmDG,UAAUC,KAAI/C,IAAI,CAC1EgD,MAAOhD,EAAKZ,KACZC,MAAOW,EAAKZ,WACP,IAGH6D,EAAatF,IAC6B,IAADuF,EAEtCC,EAFQ,KAATxF,GAAoC,KAArBH,EAASE,QACb,QAAbwF,EAAAjG,EAAMyC,eAAO,IAAAwD,GAAbA,EAAe1C,KAAK,CAAEC,SAAU,QAASC,QAAS,eAAMC,OAAQ,+CAE9C,QAAlBwC,EAAAhG,EAAWuC,eAAO,IAAAyD,GAAlBA,EAAoBC,gBAI1B,OAAI3H,GAAgB4H,EAAAA,EAAAA,KAAA,KAAAC,SAAG,gBAGrBC,EAAAA,EAAAA,MAAA,OAAKC,UAAU,MAAKF,SAAA,EAClBD,EAAAA,EAAAA,KAACI,EAAAA,EAAK,CAACC,IAAKzG,KACZoG,EAAAA,EAAAA,KAACM,EAAAA,EAAa,KAEdN,EAAAA,EAAAA,KAAA,OAAKG,UAAU,mCAAkCF,UAC/CC,EAAAA,EAAAA,MAACK,EAAAA,EAAO,CAACF,IAAKvG,EAAY0G,WAAYzG,EAAa0G,MAAO,CAAEC,UAAW,QAAST,SAAA,EAC5EC,EAAAA,EAAAA,MAACS,EAAAA,EAAY,CAACC,OAAO,2BAAMX,SAAA,EACzBD,EAAAA,EAAAA,KAAA,OAAKG,UAAU,mBAAkBF,UAC/BC,EAAAA,EAAAA,MAAA,OAAKC,UAAU,qDAAoDF,SAAA,EAEjEC,EAAAA,EAAAA,MAAA,OAAKC,UAAU,iBAAiBU,QAAM,EAAAZ,SAAA,EACpCD,EAAAA,EAAAA,KAAA,SAAAC,SAAO,eACPD,EAAAA,EAAAA,KAAA,OAAKG,UAAU,uBAAsBF,UACnCD,EAAAA,EAAAA,KAACc,EAAAA,EAAa,CAAE/E,KAAK,YAAYgF,KAAM,EAAG/E,MAAyB,QAApB3C,EAAEc,EAASc,iBAAS,IAAA5B,OAAA,EAAlBA,EAAoB2H,WAAYC,SAAUpF,UAG/FqE,EAAAA,EAAAA,MAAA,OAAKC,UAAU,iBAAiBU,QAAM,EAAAZ,SAAA,EACpCD,EAAAA,EAAAA,KAAA,SAAAC,SAAO,aACPD,EAAAA,EAAAA,KAAA,OAAKG,UAAU,uBAAsBF,UACnCD,EAAAA,EAAAA,KAACc,EAAAA,EAAa,CAAE/E,KAAK,UAAUgF,KAAM,EAAG/E,MAAuB,QAAlB1C,EAAEa,EAASE,eAAO,IAAAf,OAAA,EAAhBA,EAAkB0H,WAAYC,SAAUpF,UAM3FqE,EAAAA,EAAAA,MAAA,OAAKC,UAAU,kBAAiBF,SAAA,EAC9BD,EAAAA,EAAAA,KAAA,MAAAC,SAAI,0CACJC,EAAAA,EAAAA,MAAA,OAAKC,UAAU,wBAAuBF,SAAA,EACpCC,EAAAA,EAAAA,MAAA,OAAKC,UAAU,gDAA+CF,SAAA,EAC5DD,EAAAA,EAAAA,KAAA,OAAKG,UAAU,8CAA6CF,UAC5DD,EAAAA,EAAAA,KAACkB,EAAAA,EAAK,CAACC,IAAI,yBAAyBC,IAAI,QAAQC,MAAM,WAEtDrB,EAAAA,EAAAA,KAAA,OAAKG,UAAU,iEAAgEF,UAC/ED,EAAAA,EAAAA,KAACkB,EAAAA,EAAK,CAACC,IAAI,mCAAmCC,IAAI,QAAQE,WAAY,CAAED,MAAO,OAAQE,SAAU,QAASC,OAAQ,gBAGpHtB,EAAAA,EAAAA,MAAA,OAAKC,UAAU,wBAAuBF,SAAA,EACpCC,EAAAA,EAAAA,MAAA,OAAKC,UAAU,iBAAgBF,SAAA,EAC7BD,EAAAA,EAAAA,KAAA,SAAOG,UAAU,4BAA2BF,SAAC,+BAC7CD,EAAAA,EAAAA,KAACyB,EAAAA,EAAQ,CACPzF,MAAOX,EAAsBE,aAC7B0F,SAAWnF,GAAMe,EAA2B,eAAgBf,EAAEE,OAC9D0F,QAAStC,EAAW,GACpBuC,YAAY,QACZC,YAAY,QACZC,YAAY,8CAGhB3B,EAAAA,EAAAA,MAAA,OAAKC,UAAU,iBAAgBF,SAAA,EAC7BD,EAAAA,EAAAA,KAAA,SAAOG,UAAU,4BAA2BF,SAAC,8BAC7CD,EAAAA,EAAAA,KAACyB,EAAAA,EAAQ,CACPzF,MAAOX,EAAsBG,eAC7ByF,SAAWnF,GAAMe,EAA2B,iBAAkBf,EAAEE,OAChE0F,QAAStC,EAAW,GACpBuC,YAAY,QACZC,YAAY,QACZC,YAAY,6CAGhB3B,EAAAA,EAAAA,MAAA,OAAKC,UAAU,iBAAgBF,SAAA,EAC7BD,EAAAA,EAAAA,KAAA,SAAOG,UAAU,4BAA2BF,SAAC,8BAC7CD,EAAAA,EAAAA,KAACyB,EAAAA,EAAQ,CACPzF,MAAOX,EAAsBI,oBAC7BwF,SAAWnF,GAAMe,EAA2B,sBAAuBf,EAAEE,OACrE0F,QAAStC,EAAW,GACpBuC,YAAY,QACZC,YAAY,QACZC,YAAY,gDAIlB3B,EAAAA,EAAAA,MAAA,OAAKC,UAAU,cAAaF,SAAA,EAC1BC,EAAAA,EAAAA,MAAA,OAAKC,UAAU,iBAAgBF,SAAA,EAC7BD,EAAAA,EAAAA,KAAA,SAAOG,UAAU,4BAA2BF,SAAC,8BAC7CD,EAAAA,EAAAA,KAAC8B,EAAAA,EAAW,CACV9F,MAAOX,EAAsBK,iBAC7BqG,cAAgBjG,GAAMe,EAA2B,mBAAoBf,EAAEE,OAAS,GAChFgG,IAAK,EACLC,IAAK,GACLJ,YAAY,aAGhB3B,EAAAA,EAAAA,MAAA,OAAKC,UAAU,iBAAgBF,SAAA,EAC7BD,EAAAA,EAAAA,KAAA,SAAOG,UAAU,4BAA2BF,SAAC,UAC7CD,EAAAA,EAAAA,KAACkC,EAAAA,EAAM,CACLvC,MAAM,eACNwC,KAAK,aACLC,QApRAC,KAAO,IAADC,EAMYC,EALsFC,EAA3HnH,EAAsBE,cAAiBF,EAAsBG,gBAAmBH,EAAsBI,oBAKvGtB,EAASiB,gBAAgBqH,QAAU,EACxB,QAAbF,EAAA3I,EAAMyC,eAAO,IAAAkG,GAAbA,EAAepF,KAAK,CAAEC,SAAU,QAASC,QAAS,2BAAQC,OAAQ,uFAIpElD,GAAY8B,IAAIP,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACXO,GAAI,IACPd,gBAAiB,IAAIc,EAAKd,iBAAeO,EAAAA,EAAAA,GAAA,GAAON,QAIlDC,EAAyB,CACvBC,aAAc,GACdC,eAAgB,GAChBC,oBAAqB,GACrBC,iBAAkB,IAGP,QAAb4G,EAAA1I,EAAMyC,eAAO,IAAAiG,GAAbA,EAAenF,KAAK,CAAEC,SAAU,UAAWC,QAAS,2BAAQC,OAAQ,gDAtBrD,QAAbkF,EAAA5I,EAAMyC,eAAO,IAAAmG,GAAbA,EAAerF,KAAK,CAAEC,SAAU,QAASC,QAAS,2BAAQC,OAAQ,8EAmR5CoF,SAAUvI,EAASiB,gBAAgBqH,QAAU,iBASvDvC,EAAAA,EAAAA,MAAA,OAAKC,UAAU,kBAAiBF,SAAA,EAC9BC,EAAAA,EAAAA,MAAA,MAAAD,SAAA,CAAI,yCAAS9F,EAASiB,gBAAgBqH,OAAO,UAC7CvC,EAAAA,EAAAA,MAACyC,EAAAA,EAAS,CAAC3G,MAAO7B,EAASiB,gBAAiBwH,aAAa,mDAAU3C,SAAA,EACjED,EAAAA,EAAAA,KAAC6C,EAAAA,EAAM,CAAC/F,MAAM,eAAe8D,OAAO,+BACpCZ,EAAAA,EAAAA,KAAC6C,EAAAA,EAAM,CAAC/F,MAAM,iBAAiB8D,OAAO,8BACtCZ,EAAAA,EAAAA,KAAC6C,EAAAA,EAAM,CAAC/F,MAAM,sBAAsB8D,OAAO,8BAC3CZ,EAAAA,EAAAA,KAAC6C,EAAAA,EAAM,CAAC/F,MAAM,mBAAmB8D,OAAO,8BACxCZ,EAAAA,EAAAA,KAAC6C,EAAAA,EAAM,CACLjC,OAAO,eACPkC,KAAMA,CAACC,EAAGrB,KACR1B,EAAAA,EAAAA,KAACkC,EAAAA,EAAM,CACLC,KAAK,cACLhC,UAAU,iDACViC,QAASA,IAhRLY,KAAmB,IAADC,EAC9C7I,GAAY8B,IAAIP,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACXO,GAAI,IACPd,gBAAiBc,EAAKd,gBAAgBsB,QAAO,CAACqG,EAAGG,IAAMA,IAAMF,QAElD,QAAbC,EAAArJ,EAAMyC,eAAO,IAAA4G,GAAbA,EAAe9F,KAAK,CAAEC,SAAU,UAAWC,QAAS,2BAAQC,OAAQ,gDA2Q7B6F,CAAqBzB,EAAQ0B,qBAOtDlD,EAAAA,EAAAA,MAAA,OAAKC,UAAU,kBAAiBF,SAAA,EAC9BD,EAAAA,EAAAA,KAAA,SAAOG,UAAU,uBAAsBF,SAAC,8DACxCD,EAAAA,EAAAA,KAAA,OAAKG,UAAU,4BAA2BF,SACzCb,EAAW,GAAGM,KAAI2D,IACjBnD,EAAAA,EAAAA,MAAA,OAAwBC,UAAU,0BAAyBF,SAAA,EACzDD,EAAAA,EAAAA,KAACsD,EAAAA,EAAQ,CACPC,QAAO,SAAAC,OAAWH,EAAOrH,OACzBA,MAAOqH,EAAOrH,MACdiF,SAAWnF,IAAC,IAAA2H,EAAA,OAAKtH,EAAqB,iBAAkBkH,EAAOrH,MAAgB,QAAXyH,EAAE3H,EAAEM,eAAO,IAAAqH,GAAAA,IAC/ErH,QAASjC,EAASK,eAAe+B,MAAM,KAAKE,SAAS4G,EAAOrH,UAE9DgE,EAAAA,EAAAA,KAAA,SAAO0D,QAAO,SAAAF,OAAWH,EAAOrH,OAASmE,UAAU,OAAMF,SAAEoD,EAAO1D,UAP1D0D,EAAOrH,YAWnBgE,EAAAA,EAAAA,KAACc,EAAAA,EAAa,CAAE/E,KAAK,iBAAiBgF,KAAM,EAAG/E,MAAO7B,EAASK,eAAgByG,SAAUpF,QAG3FqE,EAAAA,EAAAA,MAAA,OAAKC,UAAU,kBAAiBF,SAAA,EAC9BD,EAAAA,EAAAA,KAAA,SAAOG,UAAU,uBAAsBF,SAAC,8BACxCD,EAAAA,EAAAA,KAAA,OAAKG,UAAU,4BAA2BF,SACzCb,EAAW,GAAGM,KAAI2D,IACjBnD,EAAAA,EAAAA,MAAA,OAAwBC,UAAU,0BAAyBF,SAAA,EACzDD,EAAAA,EAAAA,KAACsD,EAAAA,EAAQ,CACPC,QAAO,SAAAC,OAAWH,EAAOrH,OACzBA,MAAOqH,EAAOrH,MACdiF,SAAUA,IAAM7G,GAAY8B,IAAIP,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GAAUO,GAAI,IAAE3B,iBAAkB8I,EAAOrH,UACzEI,QAASjC,EAASI,mBAAqB8I,EAAOrH,SAEhDgE,EAAAA,EAAAA,KAAA,SAAO0D,QAAO,SAAAF,OAAWH,EAAOrH,OAASmE,UAAU,OAAMF,SAAEoD,EAAO1D,UAP1D0D,EAAOrH,eAarBkE,EAAAA,EAAAA,MAAA,OAAKC,UAAU,kBAAiBF,SAAA,EAC9BD,EAAAA,EAAAA,KAAA,SAAOG,UAAU,uBAAsBF,SAAC,sDACxCD,EAAAA,EAAAA,KAAA,OAAKG,UAAU,4BAA2BF,SACvCb,EAAW,GAAGM,KAAI2D,IACjBnD,EAAAA,EAAAA,MAAA,OAAwBC,UAAU,0BAAyBF,SAAA,EACzDD,EAAAA,EAAAA,KAACsD,EAAAA,EAAQ,CACPC,QAAO,SAAAC,OAAWH,EAAOrH,OACzBA,MAAOqH,EAAOrH,MACdiF,SAAWnF,IAAC,IAAA6H,EAAA,OAAKxH,EAAqB,mBAAoBkH,EAAOrH,MAAgB,QAAX2H,EAAE7H,EAAEM,eAAO,IAAAuH,GAAAA,IACjFvH,QAASjC,EAASM,iBAAiB8B,MAAM,KAAKE,SAAS4G,EAAOrH,UAEhEgE,EAAAA,EAAAA,KAAA,SAAO0D,QAAO,SAAAF,OAAWH,EAAOrH,OAASmE,UAAU,OAAMF,SAAEoD,EAAO1D,UAP1D0D,EAAOrH,YAWrBgE,EAAAA,EAAAA,KAACc,EAAAA,EAAa,CAAE/E,KAAK,mBAAmBgF,KAAM,EAAG/E,MAAO7B,EAASM,iBAAkBwG,SAAUpF,QAG/FqE,EAAAA,EAAAA,MAAA,OAAKC,UAAU,kBAAiBF,SAAA,EAC9BD,EAAAA,EAAAA,KAAA,SAAOG,UAAU,uBAAsBF,SAAC,sDACxCD,EAAAA,EAAAA,KAAA,OAAKG,UAAU,4BAA2BF,SACzCb,EAAW,GAAGM,KAAI2D,IACjBnD,EAAAA,EAAAA,MAAA,OAAwBC,UAAU,0BAAyBF,SAAA,EACzDD,EAAAA,EAAAA,KAACsD,EAAAA,EAAQ,CACPC,QAAO,SAAAC,OAAWH,EAAOrH,OACzBA,MAAOqH,EAAOrH,MACdiF,SAAUA,IAAM7G,GAAY8B,IAAIP,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GAAUO,GAAI,IAAExB,aAAc2I,EAAOrH,UACrEI,QAASjC,EAASO,eAAiB2I,EAAOrH,SAE5CgE,EAAAA,EAAAA,KAAA,SAAO0D,QAAO,SAAAF,OAAWH,EAAOrH,OAASmE,UAAU,OAAMF,SAAEoD,EAAO1D,UAP1D0D,EAAOrH,YAWnBgE,EAAAA,EAAAA,KAACc,EAAAA,EAAa,CAAE/E,KAAK,eAAegF,KAAM,EAAG/E,MAAO7B,EAASO,aAAcuG,SAAUpF,aAI3FqE,EAAAA,EAAAA,MAAA,OAAKC,UAAU,oCAAmCF,SAAA,EAC9CD,EAAAA,EAAAA,KAACkC,EAAAA,EAAM,CAACvC,MAAM,eAAKvC,SAAS,OAAO+E,KAAK,aAAayB,QAAQ,OAAOxB,QAxV3DyB,MAEvBC,EAAAA,EAAAA,GAAc,CACZ3E,QAAS,qEACTyB,OAAQ,2BACRuB,KAAM,6BACN4B,OAAQ/G,UACN,IACE,MAAM2B,QAAiBpG,EAAAA,EAAIC,IAAI,kCAADgL,OAA0C,OAAP/J,QAAO,IAAPA,OAAO,EAAPA,EAASyB,IAAM,CAC9E8I,OAAQ,MACRpF,QAAS,CACP,eAAgB,sBAIpB,GAAID,EAAU,CAAC,IAADsF,EACZ,MAAMC,QAAqBvF,EAAShG,KACpCyB,GAAY8B,IAAIP,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACXO,GAAI,IACP3B,iBAAkB2J,EAAa3J,kBAAoB,GACnDC,eAAgB0J,EAAa1J,gBAAkB,GAC/CC,iBAAkByJ,EAAazJ,kBAAoB,GACnDC,aAAcwJ,EAAaxJ,cAAgB,GAC3CG,WAAYqJ,EAAarJ,YAAc,GACvCC,UAAWoJ,EAAapJ,WAAa,GACrCC,WAAYmJ,EAAanJ,YAAc,GACvCC,KAAMkJ,EAAalJ,MAAQ,GAC3BI,gBAAiB8I,EAAa9I,iBAAmB,OAEtC,QAAb6I,EAAArK,EAAMyC,eAAO,IAAA4H,GAAbA,EAAe9G,KAAK,CAAEC,SAAU,UAAWC,QAAS,2BAAQC,OAAQ,gEAEtE,CACF,CAAE,MAAOvE,GAAY,IAADoL,EACL,QAAbA,EAAAvK,EAAMyC,eAAO,IAAA8H,GAAbA,EAAehH,KAAK,CAAEC,SAAU,QAASC,QAAS,2BAAQC,OAAQvE,EAAM4E,SAC1E,SAuTUqC,EAAAA,EAAAA,KAAA,OAAKG,UAAU,aAAYF,UACzBD,EAAAA,EAAAA,KAACkC,EAAAA,EAAM,CAACvC,MAAM,eAAKvC,SAAS,UAAU+E,KAAK,eAAeC,QAASA,IAAMrF,EAAa,SAExFiD,EAAAA,EAAAA,KAACkC,EAAAA,EAAM,CAACvC,MAAM,qBAAMwC,KAAK,oBAAoByB,QAAQ,QAAQxB,QAASA,IAAMxC,EAAU,aAI5FM,EAAAA,EAAAA,MAACS,EAAAA,EAAY,CAACC,OAAO,iCAAOX,SAAA,EAC1BD,EAAAA,EAAAA,KAAA,OAAKG,UAAU,mBAAkBF,UAC/BC,EAAAA,EAAAA,MAAA,OAAKC,UAAU,qDAAoDF,SAAA,EACjEC,EAAAA,EAAAA,MAAA,OAAKC,UAAU,kBAAiBF,SAAA,EAC9BD,EAAAA,EAAAA,KAAA,SAAOG,UAAU,uBAAsBF,SAAC,kCACxCD,EAAAA,EAAAA,KAACc,EAAAA,EAAa,CAAC/E,KAAK,aAAagF,KAAM,EAAG/E,MAAO7B,EAASU,WAAYoG,SAAUpF,QAGlFqE,EAAAA,EAAAA,MAAA,OAAKC,UAAU,kBAAiBF,SAAA,EAC9BD,EAAAA,EAAAA,KAAA,SAAOG,UAAU,uBAAsBF,SAAC,kCACxCD,EAAAA,EAAAA,KAACc,EAAAA,EAAa,CAAC/E,KAAK,YAAYgF,KAAM,EAAG/E,MAAO7B,EAASW,UAAWmG,SAAUpF,QAGhFqE,EAAAA,EAAAA,MAAA,OAAKC,UAAU,kBAAiBF,SAAA,EAC9BD,EAAAA,EAAAA,KAAA,SAAOG,UAAU,uBAAsBF,SAAC,kCACxCD,EAAAA,EAAAA,KAACc,EAAAA,EAAa,CAAC/E,KAAK,aAAagF,KAAM,EAAG/E,MAAO7B,EAASY,WAAYkG,SAAUpF,QAGlFqE,EAAAA,EAAAA,MAAA,OAAKC,UAAU,kBAAiBF,SAAA,EAC9BD,EAAAA,EAAAA,KAAA,SAAOG,UAAU,uBAAsBF,SAAC,kCACxCD,EAAAA,EAAAA,KAACc,EAAAA,EAAa,CAAC/E,KAAK,OAAOgF,KAAM,EAAG/E,MAAO7B,EAASa,KAAMiG,SAAUpF,aAI1EqE,EAAAA,EAAAA,MAAA,OAAKC,UAAU,oCAAmCF,SAAA,EAC9CD,EAAAA,EAAAA,KAACkC,EAAAA,EAAM,CAACvC,MAAM,qBAAMvC,SAAS,YAAY+E,KAAK,mBAAmBC,QAASA,KAAA,IAAAgC,EAAA,OAAwB,QAAxBA,EAAMtK,EAAWuC,eAAO,IAAA+H,OAAA,EAAlBA,EAAoBC,mBACpGrE,EAAAA,EAAAA,KAACkC,EAAAA,EAAM,CAACvC,MAAM,eAAKvC,SAAS,UAAU+E,KAAK,eAAeC,QAASA,IAAMrF,EAAa,OACtFiD,EAAAA,EAAAA,KAACkC,EAAAA,EAAM,CAACvC,MAAM,qBAAMwC,KAAK,oBAAoByB,QAAQ,QAAQxB,QAASA,IAAMxC,EAAU,aAI5FM,EAAAA,EAAAA,MAACS,EAAAA,EAAY,CAACC,OAAO,2BAAMX,SAAA,EACzBC,EAAAA,EAAAA,MAAA,OAAKC,UAAU,oBAAmBF,SAAA,EAChCC,EAAAA,EAAAA,MAAA,OAAKC,UAAU,qDAAoDF,SAAA,EACjEC,EAAAA,EAAAA,MAAA,OAAKC,UAAU,kBAAiBF,SAAA,EAC9BD,EAAAA,EAAAA,KAAA,SAAOG,UAAU,uBAAsBF,SAAC,oCACxCD,EAAAA,EAAAA,KAAA,OAAKG,UAAU,2BAA0BF,UACrCD,EAAAA,EAAAA,KAAA,OAAKG,UAAU,yIAAwIF,SACpJ9F,EAASS,yBACRoF,EAAAA,EAAAA,KAACkB,EAAAA,EAAK,CAACC,IAAKmD,EAAUnK,EAASS,wBAAyB2J,eAAevE,EAAAA,EAAAA,KAAA,KAAGG,UAAU,iBAAqBiB,IAAI,QAAQC,MAAM,OAAOG,OAAO,SAASgD,SAAO,KAEzJxE,EAAAA,EAAAA,KAACyE,EAAAA,EAAU,CACTC,KAAK,QACL3I,KAAK,0BACL4I,cAAY,EACZC,cAAgB9I,GAAMkC,EAAmBlC,EAAG,WAC5CiI,OAAO,UACPc,YAAa,IACbC,YAAY,qCAMtB9E,EAAAA,EAAAA,KAAA,WAGFE,EAAAA,EAAAA,MAAA,OAAKC,UAAU,kBAAiBF,SAAA,EAC9BD,EAAAA,EAAAA,KAAA,OAAKG,UAAU,qBACfH,EAAAA,EAAAA,KAAA,SAAOG,UAAU,uBAAsBF,SAAC,oCACxCD,EAAAA,EAAAA,KAAA,OAAKG,UAAU,2BAA0BF,UACrCD,EAAAA,EAAAA,KAAA,OAAKG,UAAU,yIAAwIF,SACpJ9F,EAASQ,iBACRqF,EAAAA,EAAAA,KAACkB,EAAAA,EAAK,CAAEC,IAAKmD,EAAUnK,EAASQ,gBAAiB4J,eAAevE,EAAAA,EAAAA,KAAA,KAAGG,UAAU,iBAAqBiB,IAAI,QAAQC,MAAM,OAAOG,OAAO,SAASgD,SAAO,KAElJxE,EAAAA,EAAAA,KAACyE,EAAAA,EAAU,CACTC,KAAK,QACL3I,KAAK,kBACL4I,cAAY,EACZC,cAAgB9I,GAAMkC,EAAmBlC,EAAG,YAC5CiI,OAAO,UACPc,YAAa,IACbC,YAAY,wCAO1B5E,EAAAA,EAAAA,MAAA,OAAKC,UAAU,0DAAyDF,SAAA,EACtED,EAAAA,EAAAA,KAAA,OAAKG,UAAU,qBACfH,EAAAA,EAAAA,KAAA,OAAKG,UAAU,2CAA0CF,UACvDC,EAAAA,EAAAA,MAAA,OAAKC,UAAU,kBAAiBF,SAAA,EAChCD,EAAAA,EAAAA,KAAA,SAAOG,UAAU,uBAAsBF,SAAC,4DACxCD,EAAAA,EAAAA,KAAC+E,EAAAA,EAAQ,CACP/I,MAAO7B,EAASgB,uBAChB8F,SAAWnF,IAAMkJ,OAleXhJ,EAke4BF,EAAEE,WAjetD5B,GAAY8B,IAAIP,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GAAUO,GAAI,IAAEf,uBAAwBa,GAAS,SADzCA,OAmeNiJ,UAAQ,EACRC,WAAW,WACXrD,YAAY,yCAMpB3B,EAAAA,EAAAA,MAAA,OAAKC,UAAU,oCAAmCF,SAAA,EAC9CD,EAAAA,EAAAA,KAACkC,EAAAA,EAAM,CAACvC,MAAM,qBAAMvC,SAAS,YAAY+E,KAAK,mBAAmBC,QAASA,KAAA,IAAA+C,EAAA,OAAwB,QAAxBA,EAAMrL,EAAWuC,eAAO,IAAA8I,OAAA,EAAlBA,EAAoBd,mBACpGrE,EAAAA,EAAAA,KAACkC,EAAAA,EAAM,CAACvC,MAAM,eAAKvC,SAAS,UAAU+E,KAAK,cAAcyB,QAAQ,QAAQxB,QAASA,IAAMrF,EAAa,oB", "sources": ["hooks/useDataType.ts", "services/imagepath.js", "components/Page/TreatmentsDetailPage.tsx"], "sourcesContent": ["import { useEffect, useState } from \"react\";\r\nimport api from '../services/api';\r\n\r\n// 定義 MenuItem 中每筆物件的型別\r\ninterface DataTypeItem {\r\n  itemId: number;\r\n  number: string;\r\n  name: string;\r\n  isEnabled: boolean;\r\n}\r\n\r\n// 定義 MenuGroupItem 中每筆物件型別\r\ninterface DataTypeGroupItem {\r\n  groupId: number;\r\n  groupName: string;\r\n  isEnabled: boolean;\r\n  dataTypes: DataTypeItem[];\r\n}\r\n\r\nexport default function useDataType() {\r\n  const [dataType, setdataType] = useState<DataTypeGroupItem[]>([]);\r\n  const [loading, setLoading] = useState(true);\r\n\r\n  useEffect(() => {\r\n\r\n    api.get<DataTypeGroupItem[]>(\"/api/system/GetDataType\")\r\n      .then(res => setdataType(res.data))\r\n      .catch(err => console.error(\"API Error:\", err))\r\n      .finally(() => setLoading(false));\r\n    }, []);\r\n\r\n  return { dataType, loading };\r\n}", "\r\nconst imagepath = process.env.REACT_APP_API_URL + \"/uploads/\"; \r\n\r\nexport default imagepath;", "import { <PERSON><PERSON> } from \"primereact/button\";\r\nimport { Calendar } from \"primereact/calendar\";\r\nimport { Checkbox } from \"primereact/checkbox\";\r\nimport { Column } from 'primereact/column';\r\nimport { ConfirmDialog, confirmDialog } from 'primereact/confirmdialog';\r\nimport { DataTable } from 'primereact/datatable';\r\nimport { Dropdown } from 'primereact/dropdown';\r\nimport { FileUpload, FileUploadHandlerEvent } from \"primereact/fileupload\";\r\nimport { Image } from 'primereact/image';\r\nimport { InputNumber } from 'primereact/inputnumber';\r\nimport { InputTextarea } from \"primereact/inputtextarea\";\r\nimport { Stepper } from 'primereact/stepper';\r\nimport { StepperPanel } from 'primereact/stepperpanel';\r\nimport { Toast } from \"primereact/toast\";\r\nimport React, { useEffect, useRef, useState } from \"react\";\r\nimport { useLocation, useNavigate } from \"react-router-dom\";\r\nimport useDataType from \"../../hooks/useDataType\";\r\nimport api from \"../../services/api\";\r\nimport imagepath from \"../../services/imagepath\";\r\n\r\ninterface DiscomfortArea {\r\n  id?: number;\r\n  frontAndBack: string;\r\n  discomfortArea: string;\r\n  discomfortSituation: string;\r\n  discomfortDegree: number;\r\n}\r\n\r\ninterface Treatment {\r\n  orderNo: string;\r\n  step: number;\r\n  discomfortPeriod: string;\r\n  possibleCauses: string;\r\n  treatmentHistory: string;\r\n  howToKnowOur: string;\r\n  hospitalFormUrl: string;\r\n  treatmentConsentFormUrl: string;\r\n  subjective: string;\r\n  objective: string;\r\n  assessment: string;\r\n  plan: string;\r\n  patientId: number;\r\n  hospitalFormRecordDate?: Date | null;\r\n  discomfortAreas: DiscomfortArea[];\r\n}\r\n\r\nconst TreatmentsDetailPage: React.FC = () => {\r\n  const location = useLocation();\r\n  const patient = location.state?.patient;\r\n  const treatment = location.state?.treatment;\r\n  const toast = useRef<Toast>(null);\r\n  const stepperRef = useRef<Stepper>(null);\r\n  const [currentStep, setCurrentStep] = useState(0);\r\n  const navigate = useNavigate();\r\n  const { dataType, loading } = useDataType();\r\n  const [formData, setFormData] = useState<Treatment>({\r\n    orderNo: \"\",\r\n    step: 0,\r\n    discomfortPeriod: \"\",\r\n    possibleCauses: \"\",\r\n    treatmentHistory: \"\",\r\n    howToKnowOur: \"\",\r\n    hospitalFormUrl: \"\",\r\n    treatmentConsentFormUrl: \"\",\r\n    subjective: \"\",\r\n    objective: \"\",\r\n    assessment: \"\",\r\n    plan: \"\",\r\n    patientId: patient?.id || 0,\r\n    hospitalFormRecordDate: null,\r\n    discomfortAreas: []\r\n  });\r\n\r\n  // 新增不適區域的狀態\r\n  const [currentDiscomfortArea, setCurrentDiscomfortArea] = useState<DiscomfortArea>({\r\n    frontAndBack: \"\",\r\n    discomfortArea: \"\",\r\n    discomfortSituation: \"\",\r\n    discomfortDegree: 0\r\n  });\r\n\r\n  useEffect(() => {\r\n      if (treatment) {\r\n          setFormData({\r\n            ...treatment,\r\n            hospitalFormRecordDate: treatment.hospitalFormRecordDate ? new Date(treatment.hospitalFormRecordDate) : null,\r\n            });\r\n\r\n          if(treatment.step === 20){\r\n            setCurrentStep(1)\r\n          }\r\n          if(treatment.step === 30){\r\n            setCurrentStep(2)\r\n          }\r\n      }\r\n    }, [treatment]);\r\n\r\n  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {\r\n          const { name, value } = e.target;\r\n          setFormData((prev) => ({ ...prev, [name]: value }));\r\n    };\r\n\r\n  const handleCheckboxChange = (name: keyof Treatment, value: string, checked: boolean) => {\r\n    setFormData(prev => {\r\n      const current = (prev[name] || \"\") as string;\r\n      const currentArr = current ? current.split(\",\") : [];\r\n\r\n      const updatedArr = checked\r\n        ? currentArr.includes(value)\r\n          ? currentArr\r\n          : [...currentArr, value]\r\n        : currentArr.filter(item => item !== value);\r\n\r\n    return { ...prev, [name]: updatedArr.join(\",\") };\r\n    });\r\n  };\r\n\r\n  const handleDateChange = (value: Date | null | undefined) => {\r\n    setFormData(prev => ({ ...prev, hospitalFormRecordDate: value || null }));\r\n  };\r\n\r\n  // 處理不適區域的函數\r\n  const handleDiscomfortAreaChange = (field: keyof DiscomfortArea, value: any) => {\r\n    setCurrentDiscomfortArea(prev => ({ ...prev, [field]: value }));\r\n  };\r\n\r\n  const addDiscomfortArea = () => {\r\n    if (!currentDiscomfortArea.frontAndBack || !currentDiscomfortArea.discomfortArea || !currentDiscomfortArea.discomfortSituation) {\r\n      toast.current?.show({ severity: \"error\", summary: \"新增失敗\", detail: \"請填寫完整的不適區域資訊\" });\r\n      return;\r\n    }\r\n\r\n    if (formData.discomfortAreas.length >= 5) {\r\n      toast.current?.show({ severity: \"error\", summary: \"新增失敗\", detail: \"不適區域最多只能新增 5 筆資料\" });\r\n      return;\r\n    }\r\n\r\n    setFormData(prev => ({\r\n      ...prev,\r\n      discomfortAreas: [...prev.discomfortAreas, { ...currentDiscomfortArea }]\r\n    }));\r\n\r\n    // 清空當前輸入\r\n    setCurrentDiscomfortArea({\r\n      frontAndBack: \"\",\r\n      discomfortArea: \"\",\r\n      discomfortSituation: \"\",\r\n      discomfortDegree: 0\r\n    });\r\n\r\n    toast.current?.show({ severity: \"success\", summary: \"新增成功\", detail: \"不適區域已新增\" });\r\n  };\r\n\r\n  const removeDiscomfortArea = (index: number) => {\r\n    setFormData(prev => ({\r\n      ...prev,\r\n      discomfortAreas: prev.discomfortAreas.filter((_, i) => i !== index)\r\n    }));\r\n    toast.current?.show({ severity: \"success\", summary: \"刪除成功\", detail: \"不適區域已刪除\" });\r\n  };\r\n\r\n  const copyLatestRecord = () => {\r\n    \r\n    confirmDialog({\r\n      message: '是否複製上一筆診療紀錄',\r\n      header: '複製確認',\r\n      icon: 'pi pi-exclamation-triangle',\r\n      accept: async () => {\r\n        try {\r\n          const response = await api.get(`/api/Treatment/GetLatestRecord/${patient?.id}`, {\r\n            method: 'GET',\r\n            headers: {\r\n              'Content-Type': 'application/json',\r\n            },\r\n          });\r\n\r\n          if (response) {\r\n            const latestRecord = await response.data;\r\n            setFormData(prev => ({\r\n              ...prev,\r\n              discomfortPeriod: latestRecord.discomfortPeriod || \"\",\r\n              possibleCauses: latestRecord.possibleCauses || \"\",\r\n              treatmentHistory: latestRecord.treatmentHistory || \"\",\r\n              howToKnowOur: latestRecord.howToKnowOur || \"\",\r\n              subjective: latestRecord.subjective || \"\",\r\n              objective: latestRecord.objective || \"\",\r\n              assessment: latestRecord.assessment || \"\",\r\n              plan: latestRecord.plan || \"\",\r\n              discomfortAreas: latestRecord.discomfortAreas || []\r\n            }));\r\n            toast.current?.show({ severity: \"success\", summary: \"複製成功\", detail: \"已複製上一筆診療紀錄\" });\r\n            \r\n          } \r\n        } catch (error:any) {\r\n          toast.current?.show({ severity: \"error\", summary: \"複製失敗\", detail: error.details });\r\n        }\r\n      }\r\n    });\r\n  };\r\n\r\n  const handleSubmit = async (step:number) => {\r\n\r\n    var detail = \"治療資料已更新\";\r\n\r\n    if(step === 40){\r\n      if(formData.hospitalFormUrl === '' && formData.treatmentConsentFormUrl === ''){\r\n        toast.current?.show({ severity: \"error\", summary: \"結案失敗\", detail: \"請上傳治療同意書或醫院診斷書\"})\r\n        return\r\n      }\r\n      if(formData.hospitalFormUrl !== '' && !formData.hospitalFormRecordDate){\r\n        toast.current?.show({ severity: \"error\", summary: \"結案失敗\", detail: \"請填寫醫院診斷書開立時間\"})\r\n        return\r\n      }\r\n    }\r\n\r\n    if (formData.orderNo === \"\") {\r\n        // 新增模式\r\n        await api.post(\"/api/treatment/Insert\", formData)\r\n        .then((res) => {\r\n          setFormData(\r\n            (prev) => ({...prev,orderNo: res.data.orderNo})\r\n          ); \r\n          toast.current?.show({ severity: \"success\", summary: \"成功\", detail: res.data.msg }) } )\r\n        .catch((err) => toast.current?.show({ severity: \"error\", summary: \"新增失敗\", detail: err.details}) );     \r\n        \r\n    }\r\n    else{\r\n      // 編輯模式\r\n      formData.step = formData.step > step ? formData.step : step;\r\n      setFormData(\r\n            (prev) => ({...prev,step: formData.step})\r\n          ); \r\n      await api.put(\"/api/treatment/Update\", formData)\r\n        .then(() => toast.current?.show({ severity: \"success\", summary: \"成功\", detail: detail }) )\r\n        .catch((err) => toast.current?.show({ severity: \"error\", summary: \"更新失敗\", detail: err.details}) );  \r\n    }\r\n\r\n    if(step === 40){\r\n      setTimeout(() => navigate(\"/treatments\"), 1500); // 送出後導回列表頁\r\n    }\r\n  };\r\n\r\n  // 上傳\r\n  const handleCustomUpload = async (event: FileUploadHandlerEvent, type: 'hospital' | 'consent') => {\r\n    const file = event.files?.[0];\r\n    if (!file) return;\r\n\r\n    if (!formData.orderNo) {\r\n      toast.current?.show({\r\n        severity: \"error\",\r\n        summary: \"上傳失敗\",\r\n        detail: \"請先儲存治療記錄，取得單號後再上傳檔案。\",\r\n      });\r\n      return;\r\n    }\r\n\r\n    const formDataToSend = new FormData();\r\n    formDataToSend.append(\"file\", file);\r\n    formDataToSend.append(\"orderNo\", formData.orderNo);\r\n\r\n    try {\r\n      const response = await api.post(\"/api/system/UploadFile\", formDataToSend, {\r\n        headers: {\r\n          \"Content-Type\": \"multipart/form-data\",\r\n        },\r\n      });\r\n\r\n      const { fileName } = response.data;\r\n\r\n      // 更新 formData\r\n      const updatedFormData = {\r\n        ...formData,\r\n        [type === 'hospital' ? 'hospitalFormUrl' : 'treatmentConsentFormUrl']: fileName,\r\n      };\r\n\r\n      // 更新 UI\r\n      setFormData(updatedFormData);\r\n\r\n      // 直接呼叫更新 API\r\n      await api.put(\"/api/treatment/Update\", updatedFormData);\r\n\r\n      toast.current?.show({\r\n        severity: \"success\",\r\n        summary: \"成功\",\r\n        detail: \"檔案已上傳並更新記錄。\",\r\n      });\r\n\r\n    } catch (error: any) {\r\n      const errorMessage = error.response?.data?.message || error.message || \"檔案上傳失敗\";\r\n      toast.current?.show({\r\n        severity: \"error\",\r\n        summary: \"上傳失敗\",\r\n        detail: errorMessage,\r\n      });\r\n    }\r\n  };\r\n\r\n  const getOptions = (groupId: number) => {\r\n    return dataType.find(group => group.groupId === groupId)?.dataTypes.map(item => ({\r\n      label: item.name,\r\n      value: item.name\r\n    })) || [];\r\n  };\r\n\r\n  const checkStep = (step:number) => {\r\n      if (step === 20 && formData.orderNo === \"\"){\r\n        toast.current?.show({ severity: \"error\", summary: \"錯誤\", detail: \"初次開案請存檔\" })\r\n      }else{\r\n        stepperRef.current?.nextCallback()\r\n      }\r\n    };\r\n\r\n  if (loading) return <p>Loading...</p>;\r\n\r\n  return (\r\n    <div className=\"p-4\">\r\n      <Toast ref={toast} />\r\n      <ConfirmDialog />\r\n\r\n      <div className=\"card flex justify-content-center\">\r\n        <Stepper ref={stepperRef} activeStep={currentStep} style={{ flexBasis: '100%' }}>\r\n            <StepperPanel header=\"症狀描述\">\r\n              <div className=\"flex flex-column\">\r\n                <div className=\"grid formgrid p-fluid gap-3 justify-content-center\">\r\n\r\n                  <div className=\"col-5 md:col-5\" hidden>\r\n                    <label>patientId</label>\r\n                    <div className=\"flex flex-wrap gap-3\">\r\n                      <InputTextarea  name=\"patientId\" rows={1} value={formData.patientId?.toString()} onChange={handleChange} />\r\n                    </div>\r\n                  </div>\r\n                  <div className=\"col-5 md:col-5\" hidden>\r\n                    <label>orderNo</label>\r\n                    <div className=\"flex flex-wrap gap-3\">\r\n                      <InputTextarea  name=\"orderNo\" rows={1} value={formData.orderNo?.toString()} onChange={handleChange} />\r\n                    </div>\r\n                  </div>\r\n\r\n\r\n                  {/* 不適區域新增區域 */}\r\n                  <div className=\"col-12 md:col-6\">\r\n                    <h4>新增不適區域</h4>\r\n                    <div className=\"grid formgrid p-fluid\">\r\n                      <div className=\"col-12 flex flex-wrap justify-content-between\">\r\n                        <div className=\"col-12 md:col-4 flex justify-content-center\">\r\n                        <Image src=\"/images/image-body.jpg\" alt=\"Image\" width=\"250\" />\r\n                        </div>\r\n                        <div className=\"col-12 md:col-8 flex justify-content-center align-items-center\" >\r\n                        <Image src=\"/images/NumericalRaringAcale.png\" alt=\"Image\" imageStyle={{ width: \"100%\", maxWidth: \"550px\", height: \"auto\" }} />\r\n                        </div>\r\n                      </div>\r\n                      <div className=\"col-12 flex flex-wrap\">\r\n                        <div className=\"col-6 md:col-4\">\r\n                          <label className=\"font-bold block pt-2 mb-2\">前側/後側</label>\r\n                          <Dropdown\r\n                            value={currentDiscomfortArea.frontAndBack}\r\n                            onChange={(e) => handleDiscomfortAreaChange('frontAndBack', e.value)}\r\n                            options={getOptions(1)}\r\n                            optionLabel=\"label\"\r\n                            optionValue=\"value\"\r\n                            placeholder=\"選擇前側/後側\"\r\n                          />\r\n                        </div>\r\n                        <div className=\"col-6 md:col-4\">\r\n                          <label className=\"font-bold block pt-2 mb-2\">不適區域</label>\r\n                          <Dropdown\r\n                            value={currentDiscomfortArea.discomfortArea}\r\n                            onChange={(e) => handleDiscomfortAreaChange('discomfortArea', e.value)}\r\n                            options={getOptions(2)}\r\n                            optionLabel=\"label\"\r\n                            optionValue=\"value\"\r\n                            placeholder=\"選擇不適區域\"\r\n                          />\r\n                        </div>\r\n                        <div className=\"col-6 md:col-4\">\r\n                          <label className=\"font-bold block pt-2 mb-2\">不適情況</label>\r\n                          <Dropdown\r\n                            value={currentDiscomfortArea.discomfortSituation}\r\n                            onChange={(e) => handleDiscomfortAreaChange('discomfortSituation', e.value)}\r\n                            options={getOptions(3)}\r\n                            optionLabel=\"label\"\r\n                            optionValue=\"value\"\r\n                            placeholder=\"選擇不適情況\"\r\n                          />\r\n                        </div>\r\n                      </div>\r\n                      <div className=\"col-12 flex\">\r\n                        <div className=\"col-6 md:col-4\">\r\n                          <label className=\"font-bold block pt-2 mb-2\">疼痛指數</label>\r\n                          <InputNumber\r\n                            value={currentDiscomfortArea.discomfortDegree}\r\n                            onValueChange={(e) => handleDiscomfortAreaChange('discomfortDegree', e.value || 0)}\r\n                            min={0}\r\n                            max={10}\r\n                            placeholder=\"0-10\"\r\n                          />\r\n                        </div>\r\n                        <div className=\"col-4 md:col-2\">\r\n                          <label className=\"font-bold block pt-2 mb-2\">&nbsp;</label>\r\n                          <Button\r\n                            label=\"新增\"\r\n                            icon=\"pi pi-plus\"\r\n                            onClick={addDiscomfortArea}\r\n                            disabled={formData.discomfortAreas.length >= 5}\r\n                          />\r\n                        </div>\r\n                      </div>\r\n                      \r\n                    </div>\r\n                  </div>\r\n\r\n                  {/* 不適區域列表 */}\r\n                  <div className=\"col-12 md:col-5\">\r\n                    <h4>不適區域列表 ({formData.discomfortAreas.length}/5)</h4>\r\n                    <DataTable value={formData.discomfortAreas} emptyMessage=\"尚無不適區域資料\">\r\n                      <Column field=\"frontAndBack\" header=\"前側/後側\" />\r\n                      <Column field=\"discomfortArea\" header=\"不適區域\" />\r\n                      <Column field=\"discomfortSituation\" header=\"不適情況\" />\r\n                      <Column field=\"discomfortDegree\" header=\"疼痛指數\" />\r\n                      <Column\r\n                        header=\"操作\"\r\n                        body={(_, options) => (\r\n                          <Button\r\n                            icon=\"pi pi-trash\"\r\n                            className=\"p-button-rounded p-button-danger p-button-text\"\r\n                            onClick={() => removeDiscomfortArea(options.rowIndex)}\r\n                          />\r\n                        )}\r\n                      />\r\n                    </DataTable>\r\n                  </div>\r\n\r\n                  <div className=\"col-12 md:col-6\">\r\n                    <label className=\"font-bold block mb-2\">可能引發原因(可複選)</label>\r\n                    <div className=\"flex flex-wrap gap-3 pb-2\">\r\n                    {getOptions(5).map(option => (\r\n                      <div key={option.value} className=\"flex align-items-center\">\r\n                        <Checkbox\r\n                          inputId={`front-${option.value}`}\r\n                          value={option.value}\r\n                          onChange={(e) => handleCheckboxChange(\"possibleCauses\", option.value, e.checked?? false)}\r\n                          checked={formData.possibleCauses.split(\",\").includes(option.value)}\r\n                        />\r\n                        <label htmlFor={`front-${option.value}`} className=\"ml-2\">{option.label}</label>\r\n                      </div>\r\n                    ))}\r\n                    </div>\r\n                    <InputTextarea  name=\"possibleCauses\" rows={1} value={formData.possibleCauses} onChange={handleChange} />\r\n                  </div>\r\n\r\n                  <div className=\"col-12 md:col-5\">\r\n                    <label className=\"font-bold block mb-2\">不適時間</label>\r\n                    <div className=\"flex flex-wrap gap-3 pb-2\">\r\n                    {getOptions(4).map(option => (\r\n                      <div key={option.value} className=\"flex align-items-center\">\r\n                        <Checkbox\r\n                          inputId={`front-${option.value}`}\r\n                          value={option.value}\r\n                          onChange={() => setFormData(prev => ({ ...prev, discomfortPeriod: option.value }))}\r\n                          checked={formData.discomfortPeriod === option.value}\r\n                        />\r\n                        <label htmlFor={`front-${option.value}`} className=\"ml-2\">{option.label}</label>\r\n                      </div>\r\n                    ))}\r\n                    </div>\r\n                  </div>\r\n\r\n                  <div className=\"col-12 md:col-6\">\r\n                    <label className=\"font-bold block mb-2\">曾接受過相關處置</label>\r\n                    <div className=\"flex flex-wrap gap-3 pb-2\">\r\n                      {getOptions(6).map(option => (\r\n                        <div key={option.value} className=\"flex align-items-center\">\r\n                          <Checkbox\r\n                            inputId={`front-${option.value}`}\r\n                            value={option.value}\r\n                            onChange={(e) => handleCheckboxChange(\"treatmentHistory\", option.value, e.checked?? false)}\r\n                            checked={formData.treatmentHistory.split(\",\").includes(option.value)}\r\n                          />\r\n                          <label htmlFor={`front-${option.value}`} className=\"ml-2\">{option.label}</label>\r\n                        </div>\r\n                      ))}\r\n                    </div>\r\n                    <InputTextarea  name=\"treatmentHistory\" rows={1} value={formData.treatmentHistory} onChange={handleChange} />\r\n                  </div>\r\n\r\n                  <div className=\"col-12 md:col-5\">\r\n                    <label className=\"font-bold block mb-2\">如何知道我們院所</label>\r\n                    <div className=\"flex flex-wrap gap-3 pb-2\">\r\n                    {getOptions(7).map(option => (\r\n                      <div key={option.value} className=\"flex align-items-center\">\r\n                        <Checkbox\r\n                          inputId={`front-${option.value}`}\r\n                          value={option.value}\r\n                          onChange={() => setFormData(prev => ({ ...prev, howToKnowOur: option.value }))}\r\n                          checked={formData.howToKnowOur === option.value}\r\n                        />\r\n                        <label htmlFor={`front-${option.value}`} className=\"ml-2\">{option.label}</label>\r\n                      </div>\r\n                    ))}\r\n                    </div>\r\n                    <InputTextarea  name=\"howToKnowOur\" rows={1} value={formData.howToKnowOur} onChange={handleChange} />\r\n                  </div>\r\n                </div>  \r\n              </div>\r\n              <div className=\"flex pt-4 justify-content-between\">\r\n                  <Button label=\"複製\" severity=\"info\" icon=\"pi pi-copy\" iconPos=\"left\" onClick={copyLatestRecord} />\r\n                  <div className=\"flex gap-2\">\r\n                    <Button label=\"存檔\" severity=\"success\" icon=\"pi pi-upload\" onClick={() => handleSubmit(10)} />\r\n                  </div>\r\n                  <Button label=\"下一步\" icon=\"pi pi-arrow-right\" iconPos=\"right\" onClick={() => checkStep(20)} />\r\n              </div>\r\n            </StepperPanel>\r\n\r\n            <StepperPanel header=\"治療師診療\">\r\n              <div className=\"flex flex-column\">\r\n                <div className=\"grid formgrid p-fluid gap-3 justify-content-center\">\r\n                  <div className=\"col-12 md:col-5\">\r\n                    <label className=\"font-bold block mb-2\">主觀症狀 (S)</label>\r\n                    <InputTextarea name=\"subjective\" rows={6} value={formData.subjective} onChange={handleChange} />\r\n                  </div>\r\n\r\n                  <div className=\"col-12 md:col-5\">\r\n                    <label className=\"font-bold block mb-2\">客觀檢查 (O)</label>\r\n                    <InputTextarea name=\"objective\" rows={6} value={formData.objective} onChange={handleChange} />\r\n                  </div>\r\n\r\n                  <div className=\"col-12 md:col-5\">\r\n                    <label className=\"font-bold block mb-2\">專業判斷 (A)</label>\r\n                    <InputTextarea name=\"assessment\" rows={6} value={formData.assessment} onChange={handleChange} />\r\n                  </div>\r\n\r\n                  <div className=\"col-12 md:col-5\">\r\n                    <label className=\"font-bold block mb-2\">治療計畫 (P)</label>\r\n                    <InputTextarea name=\"plan\" rows={6} value={formData.plan} onChange={handleChange} />\r\n                  </div>\r\n                </div>\r\n              </div>\r\n              <div className=\"flex pt-4 justify-content-between\">\r\n                  <Button label=\"上一步\" severity=\"secondary\" icon=\"pi pi-arrow-left\" onClick={() => stepperRef.current?.prevCallback()} />\r\n                  <Button label=\"存檔\" severity=\"success\" icon=\"pi pi-upload\" onClick={() => handleSubmit(20)} />\r\n                  <Button label=\"下一步\" icon=\"pi pi-arrow-right\" iconPos=\"right\" onClick={() => checkStep(30)} />\r\n              </div>\r\n            </StepperPanel>\r\n\r\n            <StepperPanel header=\"檔案上傳\">\r\n              <div className=\"flex flex-column \">\r\n                <div className=\"grid formgrid p-fluid gap-3 justify-content-center\">\r\n                  <div className=\"col-12 md:col-5\">\r\n                    <label className=\"font-bold block mb-2\">治療同意書</label>\r\n                    <div className=\"flex flex-column h-15rem\">\r\n                        <div className=\"border-2 border-dashed surface-border border-round surface-ground flex-auto flex justify-content-center align-items-center font-medium\">\r\n                          {formData.treatmentConsentFormUrl ? (\r\n                            <Image src={imagepath+formData.treatmentConsentFormUrl} indicatorIcon={<i className=\"pi pi-search\"></i>} alt=\"Image\" width=\"100%\" height=\"230rem\" preview />\r\n                          ) : (\r\n                            <FileUpload\r\n                              mode=\"basic\"\r\n                              name=\"TreatmentConsentFormUrl\"\r\n                              customUpload\r\n                              uploadHandler={(e) => handleCustomUpload(e, 'consent')}\r\n                              accept=\"image/*\"\r\n                              maxFileSize={1000000}\r\n                              chooseLabel=\"選擇檔案\"\r\n                            />\r\n                          )}\r\n                        </div>\r\n                    </div>\r\n                  </div>\r\n                    <div>\r\n\r\n                    </div>\r\n                  <div className=\"col-12 md:col-5\">\r\n                    <div className=\"col-12 md:col-5\"></div>\r\n                    <label className=\"font-bold block mb-2\">醫院診斷書</label>\r\n                    <div className=\"flex flex-column h-15rem\">\r\n                        <div className=\"border-2 border-dashed surface-border border-round surface-ground flex-auto flex justify-content-center align-items-center font-medium\">\r\n                          {formData.hospitalFormUrl ? (\r\n                            <Image  src={imagepath+formData.hospitalFormUrl} indicatorIcon={<i className=\"pi pi-search\"></i>} alt=\"Image\" width=\"100%\" height=\"230rem\" preview />\r\n                          ) : (\r\n                            <FileUpload\r\n                              mode=\"basic\"\r\n                              name=\"HospitalFormUrl\"\r\n                              customUpload\r\n                              uploadHandler={(e) => handleCustomUpload(e, 'hospital')}\r\n                              accept=\"image/*\"\r\n                              maxFileSize={1000000}\r\n                              chooseLabel=\"選擇檔案\"\r\n                            />\r\n                          )}\r\n                        </div>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n                <div className=\"grid formgrid p-fluid gap-3 pt-2 justify-content-center\">\r\n                  <div className=\"col-12 md:col-5\"></div>\r\n                  <div className=\"col-12 md:col-5 flex justify-content-end\">\r\n                    <div className=\"col-10 md:col-4\">\r\n                    <label className=\"font-bold block mb-2\">醫院診斷書開立時間</label>\r\n                    <Calendar\r\n                      value={formData.hospitalFormRecordDate}\r\n                      onChange={(e) => handleDateChange(e.value)}\r\n                      showIcon\r\n                      dateFormat=\"yy/mm/dd\"\r\n                      placeholder=\"選擇日期\"\r\n                    />\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n              <div className=\"flex pt-4 justify-content-between\">\r\n                  <Button label=\"上一步\" severity=\"secondary\" icon=\"pi pi-arrow-left\" onClick={() => stepperRef.current?.prevCallback()} />\r\n                  <Button label=\"結案\" severity=\"success\" icon=\"pi pi-check\" iconPos=\"right\" onClick={() => handleSubmit(40)} />\r\n              </div>\r\n            </StepperPanel>\r\n        </Stepper>\r\n    </div>\r\n\r\n\r\n    </div>\r\n    \r\n  );\r\n};\r\n\r\nexport default TreatmentsDetailPage;"], "names": ["useDataType", "dataType", "setdataType", "useState", "loading", "setLoading", "useEffect", "api", "get", "then", "res", "data", "catch", "err", "console", "error", "finally", "process", "TreatmentsDetailPage", "_location$state", "_location$state2", "_formData$patientId", "_formData$orderNo", "location", "useLocation", "patient", "state", "treatment", "toast", "useRef", "stepper<PERSON><PERSON>", "currentStep", "setCurrentStep", "navigate", "useNavigate", "formData", "setFormData", "orderNo", "step", "discomfort<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "treatmentHistory", "howToKnowOur", "hospitalFormUrl", "treatmentConsentFormUrl", "subjective", "objective", "assessment", "plan", "patientId", "id", "hospitalFormRecordDate", "<PERSON><PERSON><PERSON><PERSON>", "currentDiscomfortArea", "setCurrentDiscomfortArea", "frontAndBack", "discomfortArea", "discomfortSituation", "discomfortDegree", "_objectSpread", "Date", "handleChange", "e", "name", "value", "target", "prev", "handleCheckboxChange", "checked", "current", "currentArr", "split", "updatedArr", "includes", "filter", "item", "join", "handleDiscomfortAreaChange", "field", "handleSubmit", "async", "_toast$current7", "_toast$current8", "show", "severity", "summary", "detail", "post", "_toast$current9", "msg", "_toast$current0", "details", "put", "_toast$current1", "_toast$current10", "setTimeout", "handleCustomUpload", "event", "type", "_event$files", "file", "files", "_toast$current11", "formDataToSend", "FormData", "append", "_toast$current12", "response", "headers", "fileName", "updatedFormData", "_error$response", "_error$response$data", "_toast$current13", "errorMessage", "message", "getOptions", "groupId", "_dataType$find", "find", "group", "dataTypes", "map", "label", "checkStep", "_toast$current14", "_stepperRef$current", "nextCallback", "_jsx", "children", "_jsxs", "className", "Toast", "ref", "ConfirmDialog", "Stepper", "activeStep", "style", "flexBasis", "StepperPanel", "header", "hidden", "InputTextarea", "rows", "toString", "onChange", "Image", "src", "alt", "width", "imageStyle", "max<PERSON><PERSON><PERSON>", "height", "Dropdown", "options", "optionLabel", "optionValue", "placeholder", "InputNumber", "onValueChange", "min", "max", "<PERSON><PERSON>", "icon", "onClick", "addDiscomfortArea", "_toast$current3", "_toast$current2", "_toast$current", "length", "disabled", "DataTable", "emptyMessage", "Column", "body", "_", "index", "_toast$current4", "i", "removeDiscomfortArea", "rowIndex", "option", "Checkbox", "inputId", "concat", "_e$checked", "htmlFor", "_e$checked2", "iconPos", "copyLatestRecord", "confirmDialog", "accept", "method", "_toast$current5", "latestRecord", "_toast$current6", "_stepperRef$current2", "prevCallback", "imagepath", "indicatorIcon", "preview", "FileUpload", "mode", "customUpload", "uploadHandler", "maxFileSize", "<PERSON><PERSON><PERSON><PERSON>", "Calendar", "handleDateChange", "showIcon", "dateFormat", "_stepperRef$current3"], "sourceRoot": ""}
{"ast": null, "code": "const formatRelativeLocale = {\n  lastWeek: \"eeee 'lalu pukul' p\",\n  yesterday: \"'<PERSON><PERSON><PERSON> pukul' p\",\n  today: \"'Hari ini pukul' p\",\n  tomorrow: \"'Besok pukul' p\",\n  nextWeek: \"eeee 'pukul' p\",\n  other: \"P\"\n};\nexport const formatRelative = (token, _date, _baseDate, _options) => formatRelativeLocale[token];", "map": {"version": 3, "names": ["formatRelativeLocale", "lastWeek", "yesterday", "today", "tomorrow", "nextWeek", "other", "formatRelative", "token", "_date", "_baseDate", "_options"], "sources": ["C:/Users/<USER>/Desktop/demo-git/demo-react/my-fullstack-app/frontend/node_modules/date-fns/locale/id/_lib/formatRelative.js"], "sourcesContent": ["const formatRelativeLocale = {\n  lastWeek: \"eeee 'lalu pukul' p\",\n  yesterday: \"'<PERSON><PERSON><PERSON> pukul' p\",\n  today: \"'Hari ini pukul' p\",\n  tomorrow: \"'Besok pukul' p\",\n  nextWeek: \"eeee 'pukul' p\",\n  other: \"P\",\n};\n\nexport const formatRelative = (token, _date, _baseDate, _options) =>\n  formatRelativeLocale[token];\n"], "mappings": "AAAA,MAAMA,oBAAoB,GAAG;EAC3BC,QAAQ,EAAE,qBAAqB;EAC/BC,SAAS,EAAE,mBAAmB;EAC9BC,KAAK,EAAE,oBAAoB;EAC3BC,QAAQ,EAAE,iBAAiB;EAC3BC,QAAQ,EAAE,gBAAgB;EAC1BC,KAAK,EAAE;AACT,CAAC;AAED,OAAO,MAAMC,cAAc,GAAGA,CAACC,KAAK,EAAEC,KAAK,EAAEC,SAAS,EAAEC,QAAQ,KAC9DX,oBAAoB,CAACQ,KAAK,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
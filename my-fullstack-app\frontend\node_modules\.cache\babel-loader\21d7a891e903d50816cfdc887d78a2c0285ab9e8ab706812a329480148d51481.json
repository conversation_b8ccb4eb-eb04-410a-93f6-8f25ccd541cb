{"ast": null, "code": "const translations = {\n  xseconds_other: \"sekundė_sekundžių_sekundes\",\n  xminutes_one: \"minutė_minutės_minutę\",\n  xminutes_other: \"minutės_minučių_minutes\",\n  xhours_one: \"valanda_valandos_valandą\",\n  xhours_other: \"valandos_valandų_valandas\",\n  xdays_one: \"diena_dienos_dieną\",\n  xdays_other: \"dienos_dienų_dienas\",\n  xweeks_one: \"savaitė_savaitės_savaitę\",\n  xweeks_other: \"savaitės_savaičių_savaites\",\n  xmonths_one: \"mėnuo_mėnesio_mėnesį\",\n  xmonths_other: \"mėnesiai_mėnesių_mėnesius\",\n  xyears_one: \"metai_metų_metus\",\n  xyears_other: \"metai_metų_metus\",\n  about: \"apie\",\n  over: \"daugiau nei\",\n  almost: \"beveik\",\n  lessthan: \"mažiau nei\"\n};\nconst translateSeconds = (_number, addSuffix, _key, isFuture) => {\n  if (!addSuffix) {\n    return \"kelios sekund<PERSON>\";\n  } else {\n    return isFuture ? \"keli<PERSON> sekundži<PERSON>\" : \"kelias sekundes\";\n  }\n};\nconst translateSingular = (_number, addSuffix, key, isFuture) => {\n  return !addSuffix ? forms(key)[0] : isFuture ? forms(key)[1] : forms(key)[2];\n};\nconst translate = (number, addSuffix, key, isFuture) => {\n  const result = number + \" \";\n  if (number === 1) {\n    return result + translateSingular(number, addSuffix, key, isFuture);\n  } else if (!addSuffix) {\n    return result + (special(number) ? forms(key)[1] : forms(key)[0]);\n  } else {\n    if (isFuture) {\n      return result + forms(key)[1];\n    } else {\n      return result + (special(number) ? forms(key)[1] : forms(key)[2]);\n    }\n  }\n};\nfunction special(number) {\n  return number % 10 === 0 || number > 10 && number < 20;\n}\nfunction forms(key) {\n  return translations[key].split(\"_\");\n}\nconst formatDistanceLocale = {\n  lessThanXSeconds: {\n    one: translateSeconds,\n    other: translate\n  },\n  xSeconds: {\n    one: translateSeconds,\n    other: translate\n  },\n  halfAMinute: \"pusė minutės\",\n  lessThanXMinutes: {\n    one: translateSingular,\n    other: translate\n  },\n  xMinutes: {\n    one: translateSingular,\n    other: translate\n  },\n  aboutXHours: {\n    one: translateSingular,\n    other: translate\n  },\n  xHours: {\n    one: translateSingular,\n    other: translate\n  },\n  xDays: {\n    one: translateSingular,\n    other: translate\n  },\n  aboutXWeeks: {\n    one: translateSingular,\n    other: translate\n  },\n  xWeeks: {\n    one: translateSingular,\n    other: translate\n  },\n  aboutXMonths: {\n    one: translateSingular,\n    other: translate\n  },\n  xMonths: {\n    one: translateSingular,\n    other: translate\n  },\n  aboutXYears: {\n    one: translateSingular,\n    other: translate\n  },\n  xYears: {\n    one: translateSingular,\n    other: translate\n  },\n  overXYears: {\n    one: translateSingular,\n    other: translate\n  },\n  almostXYears: {\n    one: translateSingular,\n    other: translate\n  }\n};\nexport const formatDistance = (token, count, options) => {\n  const adverb = token.match(/about|over|almost|lessthan/i);\n  const unit = adverb ? token.replace(adverb[0], \"\") : token;\n  const isFuture = (options === null || options === void 0 ? void 0 : options.comparison) !== undefined && options.comparison > 0;\n  let result;\n  const tokenValue = formatDistanceLocale[token];\n  if (typeof tokenValue === \"string\") {\n    result = tokenValue;\n  } else if (count === 1) {\n    result = tokenValue.one(count, (options === null || options === void 0 ? void 0 : options.addSuffix) === true, unit.toLowerCase() + \"_one\", isFuture);\n  } else {\n    result = tokenValue.other(count, (options === null || options === void 0 ? void 0 : options.addSuffix) === true, unit.toLowerCase() + \"_other\", isFuture);\n  }\n  if (adverb) {\n    const key = adverb[0].toLowerCase();\n    result = translations[key] + \" \" + result;\n  }\n  if (options !== null && options !== void 0 && options.addSuffix) {\n    if (options.comparison && options.comparison > 0) {\n      return \"po \" + result;\n    } else {\n      return \"prieš \" + result;\n    }\n  }\n  return result;\n};", "map": {"version": 3, "names": ["translations", "xseconds_other", "xminutes_one", "xminutes_other", "xhours_one", "xhours_other", "xdays_one", "xdays_other", "xweeks_one", "xweeks_other", "xmonths_one", "xmonths_other", "xyears_one", "xyears_other", "about", "over", "almost", "lessthan", "translateSeconds", "_number", "addSuffix", "_key", "isFuture", "translateSingular", "key", "forms", "translate", "number", "result", "special", "split", "formatDistanceLocale", "lessThanXSeconds", "one", "other", "xSeconds", "halfAMinute", "lessThanXMinutes", "xMinutes", "aboutXHours", "xHours", "xDays", "aboutXWeeks", "xWeeks", "aboutXMonths", "xMonths", "aboutXYears", "xYears", "overXYears", "almostXYears", "formatDistance", "token", "count", "options", "adverb", "match", "unit", "replace", "comparison", "undefined", "tokenValue", "toLowerCase"], "sources": ["C:/Users/<USER>/Desktop/demo-git/demo-react/my-fullstack-app/frontend/node_modules/date-fns/locale/lt/_lib/formatDistance.js"], "sourcesContent": ["const translations = {\n  xseconds_other: \"sekundė_sekundžių_sekundes\",\n  xminutes_one: \"minutė_minutės_minutę\",\n  xminutes_other: \"minutės_minučių_minutes\",\n  xhours_one: \"valanda_valandos_valandą\",\n  xhours_other: \"valandos_valandų_valandas\",\n  xdays_one: \"diena_dienos_dieną\",\n  xdays_other: \"dienos_dienų_dienas\",\n  xweeks_one: \"savaitė_savaitės_savaitę\",\n  xweeks_other: \"savaitės_savaičių_savaites\",\n  xmonths_one: \"mėnuo_mėnesio_mėnesį\",\n  xmonths_other: \"mėnesiai_mėnesių_mėnesius\",\n  xyears_one: \"metai_metų_metus\",\n  xyears_other: \"metai_metų_metus\",\n  about: \"apie\",\n  over: \"daugiau nei\",\n  almost: \"beveik\",\n  lessthan: \"mažia<PERSON> nei\",\n};\n\nconst translateSeconds = (_number, addSuffix, _key, isFuture) => {\n  if (!addSuffix) {\n    return \"kelios sekund<PERSON>\";\n  } else {\n    return isFuture ? \"keli<PERSON> sekundžių\" : \"kelias sekundes\";\n  }\n};\n\nconst translateSingular = (_number, addSuffix, key, isFuture) => {\n  return !addSuffix ? forms(key)[0] : isFuture ? forms(key)[1] : forms(key)[2];\n};\n\nconst translate = (number, addSuffix, key, isFuture) => {\n  const result = number + \" \";\n  if (number === 1) {\n    return result + translateSingular(number, addSuffix, key, isFuture);\n  } else if (!addSuffix) {\n    return result + (special(number) ? forms(key)[1] : forms(key)[0]);\n  } else {\n    if (isFuture) {\n      return result + forms(key)[1];\n    } else {\n      return result + (special(number) ? forms(key)[1] : forms(key)[2]);\n    }\n  }\n};\n\nfunction special(number) {\n  return number % 10 === 0 || (number > 10 && number < 20);\n}\n\nfunction forms(key) {\n  return translations[key].split(\"_\");\n}\n\nconst formatDistanceLocale = {\n  lessThanXSeconds: {\n    one: translateSeconds,\n    other: translate,\n  },\n\n  xSeconds: {\n    one: translateSeconds,\n    other: translate,\n  },\n\n  halfAMinute: \"pusė minutės\",\n\n  lessThanXMinutes: {\n    one: translateSingular,\n    other: translate,\n  },\n\n  xMinutes: {\n    one: translateSingular,\n    other: translate,\n  },\n\n  aboutXHours: {\n    one: translateSingular,\n    other: translate,\n  },\n\n  xHours: {\n    one: translateSingular,\n    other: translate,\n  },\n\n  xDays: {\n    one: translateSingular,\n    other: translate,\n  },\n\n  aboutXWeeks: {\n    one: translateSingular,\n    other: translate,\n  },\n\n  xWeeks: {\n    one: translateSingular,\n    other: translate,\n  },\n\n  aboutXMonths: {\n    one: translateSingular,\n    other: translate,\n  },\n\n  xMonths: {\n    one: translateSingular,\n    other: translate,\n  },\n\n  aboutXYears: {\n    one: translateSingular,\n    other: translate,\n  },\n\n  xYears: {\n    one: translateSingular,\n    other: translate,\n  },\n\n  overXYears: {\n    one: translateSingular,\n    other: translate,\n  },\n\n  almostXYears: {\n    one: translateSingular,\n    other: translate,\n  },\n};\n\nexport const formatDistance = (token, count, options) => {\n  const adverb = token.match(/about|over|almost|lessthan/i);\n  const unit = adverb ? token.replace(adverb[0], \"\") : token;\n\n  const isFuture = options?.comparison !== undefined && options.comparison > 0;\n\n  let result;\n\n  const tokenValue = formatDistanceLocale[token];\n  if (typeof tokenValue === \"string\") {\n    result = tokenValue;\n  } else if (count === 1) {\n    result = tokenValue.one(\n      count,\n      options?.addSuffix === true,\n      unit.toLowerCase() + \"_one\",\n      isFuture,\n    );\n  } else {\n    result = tokenValue.other(\n      count,\n      options?.addSuffix === true,\n      unit.toLowerCase() + \"_other\",\n      isFuture,\n    );\n  }\n\n  if (adverb) {\n    const key = adverb[0].toLowerCase();\n    result = translations[key] + \" \" + result;\n  }\n\n  if (options?.addSuffix) {\n    if (options.comparison && options.comparison > 0) {\n      return \"po \" + result;\n    } else {\n      return \"prieš \" + result;\n    }\n  }\n\n  return result;\n};\n"], "mappings": "AAAA,MAAMA,YAAY,GAAG;EACnBC,cAAc,EAAE,4BAA4B;EAC5CC,YAAY,EAAE,uBAAuB;EACrCC,cAAc,EAAE,yBAAyB;EACzCC,UAAU,EAAE,0BAA0B;EACtCC,YAAY,EAAE,2BAA2B;EACzCC,SAAS,EAAE,oBAAoB;EAC/BC,WAAW,EAAE,qBAAqB;EAClCC,UAAU,EAAE,0BAA0B;EACtCC,YAAY,EAAE,4BAA4B;EAC1CC,WAAW,EAAE,sBAAsB;EACnCC,aAAa,EAAE,2BAA2B;EAC1CC,UAAU,EAAE,kBAAkB;EAC9BC,YAAY,EAAE,kBAAkB;EAChCC,KAAK,EAAE,MAAM;EACbC,IAAI,EAAE,aAAa;EACnBC,MAAM,EAAE,QAAQ;EAChBC,QAAQ,EAAE;AACZ,CAAC;AAED,MAAMC,gBAAgB,GAAGA,CAACC,OAAO,EAAEC,SAAS,EAAEC,IAAI,EAAEC,QAAQ,KAAK;EAC/D,IAAI,CAACF,SAAS,EAAE;IACd,OAAO,iBAAiB;EAC1B,CAAC,MAAM;IACL,OAAOE,QAAQ,GAAG,iBAAiB,GAAG,iBAAiB;EACzD;AACF,CAAC;AAED,MAAMC,iBAAiB,GAAGA,CAACJ,OAAO,EAAEC,SAAS,EAAEI,GAAG,EAAEF,QAAQ,KAAK;EAC/D,OAAO,CAACF,SAAS,GAAGK,KAAK,CAACD,GAAG,CAAC,CAAC,CAAC,CAAC,GAAGF,QAAQ,GAAGG,KAAK,CAACD,GAAG,CAAC,CAAC,CAAC,CAAC,GAAGC,KAAK,CAACD,GAAG,CAAC,CAAC,CAAC,CAAC;AAC9E,CAAC;AAED,MAAME,SAAS,GAAGA,CAACC,MAAM,EAAEP,SAAS,EAAEI,GAAG,EAAEF,QAAQ,KAAK;EACtD,MAAMM,MAAM,GAAGD,MAAM,GAAG,GAAG;EAC3B,IAAIA,MAAM,KAAK,CAAC,EAAE;IAChB,OAAOC,MAAM,GAAGL,iBAAiB,CAACI,MAAM,EAAEP,SAAS,EAAEI,GAAG,EAAEF,QAAQ,CAAC;EACrE,CAAC,MAAM,IAAI,CAACF,SAAS,EAAE;IACrB,OAAOQ,MAAM,IAAIC,OAAO,CAACF,MAAM,CAAC,GAAGF,KAAK,CAACD,GAAG,CAAC,CAAC,CAAC,CAAC,GAAGC,KAAK,CAACD,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;EACnE,CAAC,MAAM;IACL,IAAIF,QAAQ,EAAE;MACZ,OAAOM,MAAM,GAAGH,KAAK,CAACD,GAAG,CAAC,CAAC,CAAC,CAAC;IAC/B,CAAC,MAAM;MACL,OAAOI,MAAM,IAAIC,OAAO,CAACF,MAAM,CAAC,GAAGF,KAAK,CAACD,GAAG,CAAC,CAAC,CAAC,CAAC,GAAGC,KAAK,CAACD,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;IACnE;EACF;AACF,CAAC;AAED,SAASK,OAAOA,CAACF,MAAM,EAAE;EACvB,OAAOA,MAAM,GAAG,EAAE,KAAK,CAAC,IAAKA,MAAM,GAAG,EAAE,IAAIA,MAAM,GAAG,EAAG;AAC1D;AAEA,SAASF,KAAKA,CAACD,GAAG,EAAE;EAClB,OAAOxB,YAAY,CAACwB,GAAG,CAAC,CAACM,KAAK,CAAC,GAAG,CAAC;AACrC;AAEA,MAAMC,oBAAoB,GAAG;EAC3BC,gBAAgB,EAAE;IAChBC,GAAG,EAAEf,gBAAgB;IACrBgB,KAAK,EAAER;EACT,CAAC;EAEDS,QAAQ,EAAE;IACRF,GAAG,EAAEf,gBAAgB;IACrBgB,KAAK,EAAER;EACT,CAAC;EAEDU,WAAW,EAAE,cAAc;EAE3BC,gBAAgB,EAAE;IAChBJ,GAAG,EAAEV,iBAAiB;IACtBW,KAAK,EAAER;EACT,CAAC;EAEDY,QAAQ,EAAE;IACRL,GAAG,EAAEV,iBAAiB;IACtBW,KAAK,EAAER;EACT,CAAC;EAEDa,WAAW,EAAE;IACXN,GAAG,EAAEV,iBAAiB;IACtBW,KAAK,EAAER;EACT,CAAC;EAEDc,MAAM,EAAE;IACNP,GAAG,EAAEV,iBAAiB;IACtBW,KAAK,EAAER;EACT,CAAC;EAEDe,KAAK,EAAE;IACLR,GAAG,EAAEV,iBAAiB;IACtBW,KAAK,EAAER;EACT,CAAC;EAEDgB,WAAW,EAAE;IACXT,GAAG,EAAEV,iBAAiB;IACtBW,KAAK,EAAER;EACT,CAAC;EAEDiB,MAAM,EAAE;IACNV,GAAG,EAAEV,iBAAiB;IACtBW,KAAK,EAAER;EACT,CAAC;EAEDkB,YAAY,EAAE;IACZX,GAAG,EAAEV,iBAAiB;IACtBW,KAAK,EAAER;EACT,CAAC;EAEDmB,OAAO,EAAE;IACPZ,GAAG,EAAEV,iBAAiB;IACtBW,KAAK,EAAER;EACT,CAAC;EAEDoB,WAAW,EAAE;IACXb,GAAG,EAAEV,iBAAiB;IACtBW,KAAK,EAAER;EACT,CAAC;EAEDqB,MAAM,EAAE;IACNd,GAAG,EAAEV,iBAAiB;IACtBW,KAAK,EAAER;EACT,CAAC;EAEDsB,UAAU,EAAE;IACVf,GAAG,EAAEV,iBAAiB;IACtBW,KAAK,EAAER;EACT,CAAC;EAEDuB,YAAY,EAAE;IACZhB,GAAG,EAAEV,iBAAiB;IACtBW,KAAK,EAAER;EACT;AACF,CAAC;AAED,OAAO,MAAMwB,cAAc,GAAGA,CAACC,KAAK,EAAEC,KAAK,EAAEC,OAAO,KAAK;EACvD,MAAMC,MAAM,GAAGH,KAAK,CAACI,KAAK,CAAC,6BAA6B,CAAC;EACzD,MAAMC,IAAI,GAAGF,MAAM,GAAGH,KAAK,CAACM,OAAO,CAACH,MAAM,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,GAAGH,KAAK;EAE1D,MAAM7B,QAAQ,GAAG,CAAA+B,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEK,UAAU,MAAKC,SAAS,IAAIN,OAAO,CAACK,UAAU,GAAG,CAAC;EAE5E,IAAI9B,MAAM;EAEV,MAAMgC,UAAU,GAAG7B,oBAAoB,CAACoB,KAAK,CAAC;EAC9C,IAAI,OAAOS,UAAU,KAAK,QAAQ,EAAE;IAClChC,MAAM,GAAGgC,UAAU;EACrB,CAAC,MAAM,IAAIR,KAAK,KAAK,CAAC,EAAE;IACtBxB,MAAM,GAAGgC,UAAU,CAAC3B,GAAG,CACrBmB,KAAK,EACL,CAAAC,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEjC,SAAS,MAAK,IAAI,EAC3BoC,IAAI,CAACK,WAAW,CAAC,CAAC,GAAG,MAAM,EAC3BvC,QACF,CAAC;EACH,CAAC,MAAM;IACLM,MAAM,GAAGgC,UAAU,CAAC1B,KAAK,CACvBkB,KAAK,EACL,CAAAC,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEjC,SAAS,MAAK,IAAI,EAC3BoC,IAAI,CAACK,WAAW,CAAC,CAAC,GAAG,QAAQ,EAC7BvC,QACF,CAAC;EACH;EAEA,IAAIgC,MAAM,EAAE;IACV,MAAM9B,GAAG,GAAG8B,MAAM,CAAC,CAAC,CAAC,CAACO,WAAW,CAAC,CAAC;IACnCjC,MAAM,GAAG5B,YAAY,CAACwB,GAAG,CAAC,GAAG,GAAG,GAAGI,MAAM;EAC3C;EAEA,IAAIyB,OAAO,aAAPA,OAAO,eAAPA,OAAO,CAAEjC,SAAS,EAAE;IACtB,IAAIiC,OAAO,CAACK,UAAU,IAAIL,OAAO,CAACK,UAAU,GAAG,CAAC,EAAE;MAChD,OAAO,KAAK,GAAG9B,MAAM;IACvB,CAAC,MAAM;MACL,OAAO,QAAQ,GAAGA,MAAM;IAC1B;EACF;EAEA,OAAOA,MAAM;AACf,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
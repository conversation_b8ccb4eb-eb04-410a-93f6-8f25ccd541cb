{"ast": null, "code": "var _s = $RefreshSig$();\nimport { useState, useEffect, useCallback } from 'react';\nimport api from '../services/api';\nexport const usePermissions = () => {\n  _s();\n  const [permissions, setPermissions] = useState({});\n  const [loading, setLoading] = useState(true);\n  const fetchPermissions = useCallback(async () => {\n    try {\n      const response = await api.get('/api/RoleMenu/GetUserPermissions');\n      setPermissions(response.data || {});\n    } catch (error) {\n      console.error(\"Failed to fetch permissions\", error);\n      setPermissions({});\n    } finally {\n      setLoading(false);\n    }\n  }, []);\n  useEffect(() => {\n    fetchPermissions();\n  }, [fetchPermissions]);\n  const hasPermission = useCallback((menuPath, action) => {\n    if (loading) return false; // Don't allow access while loading\n    const menuPermissions = permissions[menuPath];\n    if (!menuPermissions) return false;\n    return menuPermissions[action] === true;\n  }, [permissions, loading]);\n  return {\n    permissions,\n    hasPermission,\n    loading,\n    refreshPermissions: fetchPermissions\n  };\n};\n_s(usePermissions, \"EtCB2WX8HJ/xtHjEI/WHR50zztc=\");", "map": {"version": 3, "names": ["useState", "useEffect", "useCallback", "api", "usePermissions", "_s", "permissions", "setPermissions", "loading", "setLoading", "fetchPermissions", "response", "get", "data", "error", "console", "hasPermission", "menuPath", "action", "menuPermissions", "refreshPermissions"], "sources": ["C:/Users/<USER>/Desktop/demo-git/demo-react/my-fullstack-app/frontend/src/hooks/usePermissions.ts"], "sourcesContent": ["\nimport { useState, useEffect, useCallback } from 'react';\nimport api from '../services/api';\n\nexport const usePermissions = () => {\n    const [permissions, setPermissions] = useState({});\n    const [loading, setLoading] = useState(true);\n\n    const fetchPermissions = useCallback(async () => {\n        try {\n            const response = await api.get('/api/RoleMenu/GetUserPermissions');\n            setPermissions(response.data || {});\n        } catch (error) {\n            console.error(\"Failed to fetch permissions\", error);\n            setPermissions({});\n        } finally {\n            setLoading(false);\n        }\n    }, []);\n\n    useEffect(() => {\n        fetchPermissions();\n    }, [fetchPermissions]);\n\n    const hasPermission = useCallback((menuPath, action) => {\n        if (loading) return false; // Don't allow access while loading\n        const menuPermissions = permissions[menuPath];\n        if (!menuPermissions) return false;\n        return menuPermissions[action] === true;\n    }, [permissions, loading]);\n\n    return { permissions, hasPermission, loading, refreshPermissions: fetchPermissions };\n};\n"], "mappings": ";AACA,SAASA,QAAQ,EAAEC,SAAS,EAAEC,WAAW,QAAQ,OAAO;AACxD,OAAOC,GAAG,MAAM,iBAAiB;AAEjC,OAAO,MAAMC,cAAc,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAChC,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGP,QAAQ,CAAC,CAAC,CAAC,CAAC;EAClD,MAAM,CAACQ,OAAO,EAAEC,UAAU,CAAC,GAAGT,QAAQ,CAAC,IAAI,CAAC;EAE5C,MAAMU,gBAAgB,GAAGR,WAAW,CAAC,YAAY;IAC7C,IAAI;MACA,MAAMS,QAAQ,GAAG,MAAMR,GAAG,CAACS,GAAG,CAAC,kCAAkC,CAAC;MAClEL,cAAc,CAACI,QAAQ,CAACE,IAAI,IAAI,CAAC,CAAC,CAAC;IACvC,CAAC,CAAC,OAAOC,KAAK,EAAE;MACZC,OAAO,CAACD,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;MACnDP,cAAc,CAAC,CAAC,CAAC,CAAC;IACtB,CAAC,SAAS;MACNE,UAAU,CAAC,KAAK,CAAC;IACrB;EACJ,CAAC,EAAE,EAAE,CAAC;EAENR,SAAS,CAAC,MAAM;IACZS,gBAAgB,CAAC,CAAC;EACtB,CAAC,EAAE,CAACA,gBAAgB,CAAC,CAAC;EAEtB,MAAMM,aAAa,GAAGd,WAAW,CAAC,CAACe,QAAQ,EAAEC,MAAM,KAAK;IACpD,IAAIV,OAAO,EAAE,OAAO,KAAK,CAAC,CAAC;IAC3B,MAAMW,eAAe,GAAGb,WAAW,CAACW,QAAQ,CAAC;IAC7C,IAAI,CAACE,eAAe,EAAE,OAAO,KAAK;IAClC,OAAOA,eAAe,CAACD,MAAM,CAAC,KAAK,IAAI;EAC3C,CAAC,EAAE,CAACZ,WAAW,EAAEE,OAAO,CAAC,CAAC;EAE1B,OAAO;IAAEF,WAAW;IAAEU,aAAa;IAAER,OAAO;IAAEY,kBAAkB,EAAEV;EAAiB,CAAC;AACxF,CAAC;AAACL,EAAA,CA5BWD,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
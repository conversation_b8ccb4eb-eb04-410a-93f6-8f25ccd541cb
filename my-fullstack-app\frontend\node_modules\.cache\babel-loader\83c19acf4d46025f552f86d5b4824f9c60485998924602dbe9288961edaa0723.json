{"ast": null, "code": "import { buildFormatLongFn } from \"../../_lib/buildFormatLongFn.js\";\nconst dateFormats = {\n  // thứ Sáu, ngày 25 tháng 08 năm 2017\n  full: \"EEEE, 'ngày' d MMMM 'năm' y\",\n  // ngày 25 tháng 08 năm 2017\n  long: \"'ngày' d MMMM 'năm' y\",\n  // 25 thg 08 năm 2017\n  medium: \"d MMM 'năm' y\",\n  // 25/08/2017\n  short: \"dd/MM/y\"\n};\nconst timeFormats = {\n  full: \"HH:mm:ss zzzz\",\n  long: \"HH:mm:ss z\",\n  medium: \"HH:mm:ss\",\n  short: \"HH:mm\"\n};\nconst dateTimeFormats = {\n  // thứ Sáu, ngày 25 tháng 08 năm 2017 23:25:59\n  full: \"{{date}} {{time}}\",\n  // ngày 25 tháng 08 năm 2017 23:25\n  long: \"{{date}} {{time}}\",\n  medium: \"{{date}} {{time}}\",\n  short: \"{{date}} {{time}}\"\n};\nexport const formatLong = {\n  date: buildFormatLongFn({\n    formats: dateFormats,\n    defaultWidth: \"full\"\n  }),\n  time: buildFormatLongFn({\n    formats: timeFormats,\n    defaultWidth: \"full\"\n  }),\n  dateTime: buildFormatLongFn({\n    formats: dateTimeFormats,\n    defaultWidth: \"full\"\n  })\n};", "map": {"version": 3, "names": ["buildFormatLongFn", "dateFormats", "full", "long", "medium", "short", "timeFormats", "dateTimeFormats", "formatLong", "date", "formats", "defaultWidth", "time", "dateTime"], "sources": ["C:/Users/<USER>/Desktop/demo-git/demo-react/my-fullstack-app/frontend/node_modules/date-fns/locale/vi/_lib/formatLong.js"], "sourcesContent": ["import { buildFormatLongFn } from \"../../_lib/buildFormatLongFn.js\";\n\nconst dateFormats = {\n  // thứ <PERSON>á<PERSON>, ngày 25 tháng 08 năm 2017\n  full: \"EEEE, 'ngày' d MMMM 'năm' y\",\n  // ngày 25 tháng 08 năm 2017\n  long: \"'ngày' d MMMM 'năm' y\",\n  // 25 thg 08 năm 2017\n  medium: \"d MMM 'năm' y\",\n  // 25/08/2017\n  short: \"dd/MM/y\",\n};\n\nconst timeFormats = {\n  full: \"HH:mm:ss zzzz\",\n  long: \"HH:mm:ss z\",\n  medium: \"HH:mm:ss\",\n  short: \"HH:mm\",\n};\n\nconst dateTimeFormats = {\n  // thứ Sáu, ngày 25 tháng 08 năm 2017 23:25:59\n  full: \"{{date}} {{time}}\",\n  // ngày 25 tháng 08 năm 2017 23:25\n  long: \"{{date}} {{time}}\",\n  medium: \"{{date}} {{time}}\",\n  short: \"{{date}} {{time}}\",\n};\n\nexport const formatLong = {\n  date: buildFormatLongFn({\n    formats: dateFormats,\n    defaultWidth: \"full\",\n  }),\n\n  time: buildFormatLongFn({\n    formats: timeFormats,\n    defaultWidth: \"full\",\n  }),\n\n  dateTime: buildFormatLongFn({\n    formats: dateTimeFormats,\n    defaultWidth: \"full\",\n  }),\n};\n"], "mappings": "AAAA,SAASA,iBAAiB,QAAQ,iCAAiC;AAEnE,MAAMC,WAAW,GAAG;EAClB;EACAC,IAAI,EAAE,6BAA6B;EACnC;EACAC,IAAI,EAAE,uBAAuB;EAC7B;EACAC,MAAM,EAAE,eAAe;EACvB;EACAC,KAAK,EAAE;AACT,CAAC;AAED,MAAMC,WAAW,GAAG;EAClBJ,IAAI,EAAE,eAAe;EACrBC,IAAI,EAAE,YAAY;EAClBC,MAAM,EAAE,UAAU;EAClBC,KAAK,EAAE;AACT,CAAC;AAED,MAAME,eAAe,GAAG;EACtB;EACAL,IAAI,EAAE,mBAAmB;EACzB;EACAC,IAAI,EAAE,mBAAmB;EACzBC,MAAM,EAAE,mBAAmB;EAC3BC,KAAK,EAAE;AACT,CAAC;AAED,OAAO,MAAMG,UAAU,GAAG;EACxBC,IAAI,EAAET,iBAAiB,CAAC;IACtBU,OAAO,EAAET,WAAW;IACpBU,YAAY,EAAE;EAChB,CAAC,CAAC;EAEFC,IAAI,EAAEZ,iBAAiB,CAAC;IACtBU,OAAO,EAAEJ,WAAW;IACpBK,YAAY,EAAE;EAChB,CAAC,CAAC;EAEFE,QAAQ,EAAEb,iBAAiB,CAAC;IAC1BU,OAAO,EAAEH,eAAe;IACxBI,YAAY,EAAE;EAChB,CAAC;AACH,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
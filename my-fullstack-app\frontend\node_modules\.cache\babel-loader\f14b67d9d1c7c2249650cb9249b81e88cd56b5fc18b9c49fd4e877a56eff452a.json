{"ast": null, "code": "/**\n * Use instead of `new Date(Date.UTC(...))` to support years below 100 which doesn't work\n * otherwise due to the nature of the\n * [`Date` constructor](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Date#interpretation_of_two-digit_years.\n *\n * For `Date.UTC(...)`, use `newDateUTC(...).getTime()`.\n */\nexport function newDateUTC(fullYear, month, day, hour, minute, second, millisecond) {\n  const utcDate = new Date(0);\n  utcDate.setUTCFullYear(fullYear, month, day);\n  utcDate.setUTCHours(hour, minute, second, millisecond);\n  return utcDate;\n}", "map": {"version": 3, "names": ["newDateUTC", "fullYear", "month", "day", "hour", "minute", "second", "millisecond", "utcDate", "Date", "setUTCFullYear", "setUTCHours"], "sources": ["C:/Users/<USER>/Desktop/demo-git/demo-react/my-fullstack-app/frontend/node_modules/date-fns-tz/dist/esm/_lib/newDateUTC/index.js"], "sourcesContent": ["/**\n * Use instead of `new Date(Date.UTC(...))` to support years below 100 which doesn't work\n * otherwise due to the nature of the\n * [`Date` constructor](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Date#interpretation_of_two-digit_years.\n *\n * For `Date.UTC(...)`, use `newDateUTC(...).getTime()`.\n */\nexport function newDateUTC(fullYear, month, day, hour, minute, second, millisecond) {\n    const utcDate = new Date(0);\n    utcDate.setUTCFullYear(fullYear, month, day);\n    utcDate.setUTCHours(hour, minute, second, millisecond);\n    return utcDate;\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASA,UAAUA,CAACC,QAAQ,EAAEC,KAAK,EAAEC,GAAG,EAAEC,IAAI,EAAEC,MAAM,EAAEC,MAAM,EAAEC,WAAW,EAAE;EAChF,MAAMC,OAAO,GAAG,IAAIC,IAAI,CAAC,CAAC,CAAC;EAC3BD,OAAO,CAACE,cAAc,CAACT,QAAQ,EAAEC,KAAK,EAAEC,GAAG,CAAC;EAC5CK,OAAO,CAACG,WAAW,CAACP,IAAI,EAAEC,MAAM,EAAEC,MAAM,EAAEC,WAAW,CAAC;EACtD,OAAOC,OAAO;AAClB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
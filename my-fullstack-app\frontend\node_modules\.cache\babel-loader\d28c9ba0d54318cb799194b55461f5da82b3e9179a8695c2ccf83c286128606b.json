{"ast": null, "code": "import { buildLocalizeFn } from \"../../_lib/buildLocalizeFn.js\";\nconst eraValues = {\n  narrow: [\"BC\", \"AC\"],\n  abbreviated: [\"きげんぜん\", \"せいれき\"],\n  wide: [\"きげんぜん\", \"せいれき\"]\n};\nconst quarterValues = {\n  narrow: [\"1\", \"2\", \"3\", \"4\"],\n  abbreviated: [\"Q1\", \"Q2\", \"Q3\", \"Q4\"],\n  wide: [\"だい1しはんき\", \"だい2しはんき\", \"だい3しはんき\", \"だい4しはんき\"]\n};\nconst monthValues = {\n  narrow: [\"1\", \"2\", \"3\", \"4\", \"5\", \"6\", \"7\", \"8\", \"9\", \"10\", \"11\", \"12\"],\n  abbreviated: [\"1がつ\", \"2がつ\", \"3がつ\", \"4がつ\", \"5がつ\", \"6がつ\", \"7がつ\", \"8がつ\", \"9がつ\", \"10がつ\", \"11がつ\", \"12がつ\"],\n  wide: [\"1がつ\", \"2がつ\", \"3がつ\", \"4がつ\", \"5がつ\", \"6がつ\", \"7がつ\", \"8がつ\", \"9がつ\", \"10がつ\", \"11がつ\", \"12がつ\"]\n};\nconst dayValues = {\n  narrow: [\"にち\", \"げつ\", \"か\", \"すい\", \"もく\", \"きん\", \"ど\"],\n  short: [\"にち\", \"げつ\", \"か\", \"すい\", \"もく\", \"きん\", \"ど\"],\n  abbreviated: [\"にち\", \"げつ\", \"か\", \"すい\", \"もく\", \"きん\", \"ど\"],\n  wide: [\"にちようび\", \"げつようび\", \"かようび\", \"すいようび\", \"もくようび\", \"きんようび\", \"どようび\"]\n};\nconst dayPeriodValues = {\n  narrow: {\n    am: \"ごぜん\",\n    pm: \"ごご\",\n    midnight: \"しんや\",\n    noon: \"しょうご\",\n    morning: \"あさ\",\n    afternoon: \"ごご\",\n    evening: \"よる\",\n    night: \"しんや\"\n  },\n  abbreviated: {\n    am: \"ごぜん\",\n    pm: \"ごご\",\n    midnight: \"しんや\",\n    noon: \"しょうご\",\n    morning: \"あさ\",\n    afternoon: \"ごご\",\n    evening: \"よる\",\n    night: \"しんや\"\n  },\n  wide: {\n    am: \"ごぜん\",\n    pm: \"ごご\",\n    midnight: \"しんや\",\n    noon: \"しょうご\",\n    morning: \"あさ\",\n    afternoon: \"ごご\",\n    evening: \"よる\",\n    night: \"しんや\"\n  }\n};\nconst formattingDayPeriodValues = {\n  narrow: {\n    am: \"ごぜん\",\n    pm: \"ごご\",\n    midnight: \"しんや\",\n    noon: \"しょうご\",\n    morning: \"あさ\",\n    afternoon: \"ごご\",\n    evening: \"よる\",\n    night: \"しんや\"\n  },\n  abbreviated: {\n    am: \"ごぜん\",\n    pm: \"ごご\",\n    midnight: \"しんや\",\n    noon: \"しょうご\",\n    morning: \"あさ\",\n    afternoon: \"ごご\",\n    evening: \"よる\",\n    night: \"しんや\"\n  },\n  wide: {\n    am: \"ごぜん\",\n    pm: \"ごご\",\n    midnight: \"しんや\",\n    noon: \"しょうご\",\n    morning: \"あさ\",\n    afternoon: \"ごご\",\n    evening: \"よる\",\n    night: \"しんや\"\n  }\n};\nconst ordinalNumber = (dirtyNumber, options) => {\n  const number = Number(dirtyNumber);\n  const unit = String(options === null || options === void 0 ? void 0 : options.unit);\n  switch (unit) {\n    case \"year\":\n      return \"\".concat(number, \"\\u306D\\u3093\");\n    case \"quarter\":\n      return \"\\u3060\\u3044\".concat(number, \"\\u3057\\u306F\\u3093\\u304D\");\n    case \"month\":\n      return \"\".concat(number, \"\\u304C\\u3064\");\n    case \"week\":\n      return \"\\u3060\\u3044\".concat(number, \"\\u3057\\u3085\\u3046\");\n    case \"date\":\n      return \"\".concat(number, \"\\u306B\\u3061\");\n    case \"hour\":\n      return \"\".concat(number, \"\\u3058\");\n    case \"minute\":\n      return \"\".concat(number, \"\\u3075\\u3093\");\n    case \"second\":\n      return \"\".concat(number, \"\\u3073\\u3087\\u3046\");\n    default:\n      return \"\".concat(number);\n  }\n};\nexport const localize = {\n  ordinalNumber: ordinalNumber,\n  era: buildLocalizeFn({\n    values: eraValues,\n    defaultWidth: \"wide\"\n  }),\n  quarter: buildLocalizeFn({\n    values: quarterValues,\n    defaultWidth: \"wide\",\n    argumentCallback: quarter => Number(quarter) - 1\n  }),\n  month: buildLocalizeFn({\n    values: monthValues,\n    defaultWidth: \"wide\"\n  }),\n  day: buildLocalizeFn({\n    values: dayValues,\n    defaultWidth: \"wide\"\n  }),\n  dayPeriod: buildLocalizeFn({\n    values: dayPeriodValues,\n    defaultWidth: \"wide\",\n    formattingValues: formattingDayPeriodValues,\n    defaultFormattingWidth: \"wide\"\n  })\n};", "map": {"version": 3, "names": ["buildLocalizeFn", "<PERSON><PERSON><PERSON><PERSON>", "narrow", "abbreviated", "wide", "quarterValues", "month<PERSON><PERSON><PERSON>", "dayV<PERSON><PERSON>", "short", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "am", "pm", "midnight", "noon", "morning", "afternoon", "evening", "night", "formattingDayPeriodValues", "ordinalNumber", "dirtyNumber", "options", "number", "Number", "unit", "String", "concat", "localize", "era", "values", "defaultWidth", "quarter", "argument<PERSON>allback", "month", "day", "<PERSON><PERSON><PERSON><PERSON>", "formattingValues", "defaultFormattingWidth"], "sources": ["C:/Users/<USER>/Desktop/demo-git/demo-react/my-fullstack-app/frontend/node_modules/date-fns/locale/ja-<PERSON>ra/_lib/localize.js"], "sourcesContent": ["import { buildLocalizeFn } from \"../../_lib/buildLocalizeFn.js\";\n\nconst eraValues = {\n  narrow: [\"BC\", \"AC\"],\n  abbreviated: [\"きげんぜん\", \"せいれき\"],\n  wide: [\"きげんぜん\", \"せいれき\"],\n};\n\nconst quarterValues = {\n  narrow: [\"1\", \"2\", \"3\", \"4\"],\n  abbreviated: [\"Q1\", \"Q2\", \"Q3\", \"Q4\"],\n  wide: [\"だい1しはんき\", \"だい2しはんき\", \"だい3しはんき\", \"だい4しはんき\"],\n};\n\nconst monthValues = {\n  narrow: [\"1\", \"2\", \"3\", \"4\", \"5\", \"6\", \"7\", \"8\", \"9\", \"10\", \"11\", \"12\"],\n\n  abbreviated: [\n    \"1がつ\",\n    \"2がつ\",\n    \"3がつ\",\n    \"4がつ\",\n    \"5がつ\",\n    \"6がつ\",\n    \"7がつ\",\n    \"8がつ\",\n    \"9がつ\",\n    \"10がつ\",\n    \"11がつ\",\n    \"12がつ\",\n  ],\n\n  wide: [\n    \"1がつ\",\n    \"2がつ\",\n    \"3がつ\",\n    \"4がつ\",\n    \"5がつ\",\n    \"6がつ\",\n    \"7がつ\",\n    \"8がつ\",\n    \"9がつ\",\n    \"10がつ\",\n    \"11がつ\",\n    \"12がつ\",\n  ],\n};\n\nconst dayValues = {\n  narrow: [\"にち\", \"げつ\", \"か\", \"すい\", \"もく\", \"きん\", \"ど\"],\n  short: [\"にち\", \"げつ\", \"か\", \"すい\", \"もく\", \"きん\", \"ど\"],\n  abbreviated: [\"にち\", \"げつ\", \"か\", \"すい\", \"もく\", \"きん\", \"ど\"],\n  wide: [\n    \"にちようび\",\n    \"げつようび\",\n    \"かようび\",\n    \"すいようび\",\n    \"もくようび\",\n    \"きんようび\",\n    \"どようび\",\n  ],\n};\n\nconst dayPeriodValues = {\n  narrow: {\n    am: \"ごぜん\",\n    pm: \"ごご\",\n    midnight: \"しんや\",\n    noon: \"しょうご\",\n    morning: \"あさ\",\n    afternoon: \"ごご\",\n    evening: \"よる\",\n    night: \"しんや\",\n  },\n  abbreviated: {\n    am: \"ごぜん\",\n    pm: \"ごご\",\n    midnight: \"しんや\",\n    noon: \"しょうご\",\n    morning: \"あさ\",\n    afternoon: \"ごご\",\n    evening: \"よる\",\n    night: \"しんや\",\n  },\n  wide: {\n    am: \"ごぜん\",\n    pm: \"ごご\",\n    midnight: \"しんや\",\n    noon: \"しょうご\",\n    morning: \"あさ\",\n    afternoon: \"ごご\",\n    evening: \"よる\",\n    night: \"しんや\",\n  },\n};\nconst formattingDayPeriodValues = {\n  narrow: {\n    am: \"ごぜん\",\n    pm: \"ごご\",\n    midnight: \"しんや\",\n    noon: \"しょうご\",\n    morning: \"あさ\",\n    afternoon: \"ごご\",\n    evening: \"よる\",\n    night: \"しんや\",\n  },\n  abbreviated: {\n    am: \"ごぜん\",\n    pm: \"ごご\",\n    midnight: \"しんや\",\n    noon: \"しょうご\",\n    morning: \"あさ\",\n    afternoon: \"ごご\",\n    evening: \"よる\",\n    night: \"しんや\",\n  },\n  wide: {\n    am: \"ごぜん\",\n    pm: \"ごご\",\n    midnight: \"しんや\",\n    noon: \"しょうご\",\n    morning: \"あさ\",\n    afternoon: \"ごご\",\n    evening: \"よる\",\n    night: \"しんや\",\n  },\n};\n\nconst ordinalNumber = (dirtyNumber, options) => {\n  const number = Number(dirtyNumber);\n  const unit = String(options?.unit);\n\n  switch (unit) {\n    case \"year\":\n      return `${number}ねん`;\n    case \"quarter\":\n      return `だい${number}しはんき`;\n    case \"month\":\n      return `${number}がつ`;\n    case \"week\":\n      return `だい${number}しゅう`;\n    case \"date\":\n      return `${number}にち`;\n    case \"hour\":\n      return `${number}じ`;\n    case \"minute\":\n      return `${number}ふん`;\n    case \"second\":\n      return `${number}びょう`;\n    default:\n      return `${number}`;\n  }\n};\n\nexport const localize = {\n  ordinalNumber: ordinalNumber,\n\n  era: buildLocalizeFn({\n    values: eraValues,\n    defaultWidth: \"wide\",\n  }),\n\n  quarter: buildLocalizeFn({\n    values: quarterValues,\n    defaultWidth: \"wide\",\n    argumentCallback: (quarter) => Number(quarter) - 1,\n  }),\n\n  month: buildLocalizeFn({\n    values: monthValues,\n    defaultWidth: \"wide\",\n  }),\n\n  day: buildLocalizeFn({\n    values: dayValues,\n    defaultWidth: \"wide\",\n  }),\n\n  dayPeriod: buildLocalizeFn({\n    values: dayPeriodValues,\n    defaultWidth: \"wide\",\n    formattingValues: formattingDayPeriodValues,\n    defaultFormattingWidth: \"wide\",\n  }),\n};\n"], "mappings": "AAAA,SAASA,eAAe,QAAQ,+BAA+B;AAE/D,MAAMC,SAAS,GAAG;EAChBC,MAAM,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC;EACpBC,WAAW,EAAE,CAAC,OAAO,EAAE,MAAM,CAAC;EAC9BC,IAAI,EAAE,CAAC,OAAO,EAAE,MAAM;AACxB,CAAC;AAED,MAAMC,aAAa,GAAG;EACpBH,MAAM,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EAC5BC,WAAW,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;EACrCC,IAAI,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS;AACnD,CAAC;AAED,MAAME,WAAW,GAAG;EAClBJ,MAAM,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;EAEvEC,WAAW,EAAE,CACX,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,MAAM,EACN,MAAM,EACN,MAAM,CACP;EAEDC,IAAI,EAAE,CACJ,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,MAAM,EACN,MAAM,EACN,MAAM;AAEV,CAAC;AAED,MAAMG,SAAS,GAAG;EAChBL,MAAM,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,CAAC;EAChDM,KAAK,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,CAAC;EAC/CL,WAAW,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,CAAC;EACrDC,IAAI,EAAE,CACJ,OAAO,EACP,OAAO,EACP,MAAM,EACN,OAAO,EACP,OAAO,EACP,OAAO,EACP,MAAM;AAEV,CAAC;AAED,MAAMK,eAAe,GAAG;EACtBP,MAAM,EAAE;IACNQ,EAAE,EAAE,KAAK;IACTC,EAAE,EAAE,IAAI;IACRC,QAAQ,EAAE,KAAK;IACfC,IAAI,EAAE,MAAM;IACZC,OAAO,EAAE,IAAI;IACbC,SAAS,EAAE,IAAI;IACfC,OAAO,EAAE,IAAI;IACbC,KAAK,EAAE;EACT,CAAC;EACDd,WAAW,EAAE;IACXO,EAAE,EAAE,KAAK;IACTC,EAAE,EAAE,IAAI;IACRC,QAAQ,EAAE,KAAK;IACfC,IAAI,EAAE,MAAM;IACZC,OAAO,EAAE,IAAI;IACbC,SAAS,EAAE,IAAI;IACfC,OAAO,EAAE,IAAI;IACbC,KAAK,EAAE;EACT,CAAC;EACDb,IAAI,EAAE;IACJM,EAAE,EAAE,KAAK;IACTC,EAAE,EAAE,IAAI;IACRC,QAAQ,EAAE,KAAK;IACfC,IAAI,EAAE,MAAM;IACZC,OAAO,EAAE,IAAI;IACbC,SAAS,EAAE,IAAI;IACfC,OAAO,EAAE,IAAI;IACbC,KAAK,EAAE;EACT;AACF,CAAC;AACD,MAAMC,yBAAyB,GAAG;EAChChB,MAAM,EAAE;IACNQ,EAAE,EAAE,KAAK;IACTC,EAAE,EAAE,IAAI;IACRC,QAAQ,EAAE,KAAK;IACfC,IAAI,EAAE,MAAM;IACZC,OAAO,EAAE,IAAI;IACbC,SAAS,EAAE,IAAI;IACfC,OAAO,EAAE,IAAI;IACbC,KAAK,EAAE;EACT,CAAC;EACDd,WAAW,EAAE;IACXO,EAAE,EAAE,KAAK;IACTC,EAAE,EAAE,IAAI;IACRC,QAAQ,EAAE,KAAK;IACfC,IAAI,EAAE,MAAM;IACZC,OAAO,EAAE,IAAI;IACbC,SAAS,EAAE,IAAI;IACfC,OAAO,EAAE,IAAI;IACbC,KAAK,EAAE;EACT,CAAC;EACDb,IAAI,EAAE;IACJM,EAAE,EAAE,KAAK;IACTC,EAAE,EAAE,IAAI;IACRC,QAAQ,EAAE,KAAK;IACfC,IAAI,EAAE,MAAM;IACZC,OAAO,EAAE,IAAI;IACbC,SAAS,EAAE,IAAI;IACfC,OAAO,EAAE,IAAI;IACbC,KAAK,EAAE;EACT;AACF,CAAC;AAED,MAAME,aAAa,GAAGA,CAACC,WAAW,EAAEC,OAAO,KAAK;EAC9C,MAAMC,MAAM,GAAGC,MAAM,CAACH,WAAW,CAAC;EAClC,MAAMI,IAAI,GAAGC,MAAM,CAACJ,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEG,IAAI,CAAC;EAElC,QAAQA,IAAI;IACV,KAAK,MAAM;MACT,UAAAE,MAAA,CAAUJ,MAAM;IAClB,KAAK,SAAS;MACZ,sBAAAI,MAAA,CAAYJ,MAAM;IACpB,KAAK,OAAO;MACV,UAAAI,MAAA,CAAUJ,MAAM;IAClB,KAAK,MAAM;MACT,sBAAAI,MAAA,CAAYJ,MAAM;IACpB,KAAK,MAAM;MACT,UAAAI,MAAA,CAAUJ,MAAM;IAClB,KAAK,MAAM;MACT,UAAAI,MAAA,CAAUJ,MAAM;IAClB,KAAK,QAAQ;MACX,UAAAI,MAAA,CAAUJ,MAAM;IAClB,KAAK,QAAQ;MACX,UAAAI,MAAA,CAAUJ,MAAM;IAClB;MACE,UAAAI,MAAA,CAAUJ,MAAM;EACpB;AACF,CAAC;AAED,OAAO,MAAMK,QAAQ,GAAG;EACtBR,aAAa,EAAEA,aAAa;EAE5BS,GAAG,EAAE5B,eAAe,CAAC;IACnB6B,MAAM,EAAE5B,SAAS;IACjB6B,YAAY,EAAE;EAChB,CAAC,CAAC;EAEFC,OAAO,EAAE/B,eAAe,CAAC;IACvB6B,MAAM,EAAExB,aAAa;IACrByB,YAAY,EAAE,MAAM;IACpBE,gBAAgB,EAAGD,OAAO,IAAKR,MAAM,CAACQ,OAAO,CAAC,GAAG;EACnD,CAAC,CAAC;EAEFE,KAAK,EAAEjC,eAAe,CAAC;IACrB6B,MAAM,EAAEvB,WAAW;IACnBwB,YAAY,EAAE;EAChB,CAAC,CAAC;EAEFI,GAAG,EAAElC,eAAe,CAAC;IACnB6B,MAAM,EAAEtB,SAAS;IACjBuB,YAAY,EAAE;EAChB,CAAC,CAAC;EAEFK,SAAS,EAAEnC,eAAe,CAAC;IACzB6B,MAAM,EAAEpB,eAAe;IACvBqB,YAAY,EAAE,MAAM;IACpBM,gBAAgB,EAAElB,yBAAyB;IAC3CmB,sBAAsB,EAAE;EAC1B,CAAC;AACH,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "import { buildLocalizeFn } from \"../../_lib/buildLocalizeFn.js\";\nconst eraValues = {\n  narrow: [\"v.Chr.\", \"n.Chr.\"],\n  abbreviated: [\"v.Chr.\", \"n.Chr.\"],\n  wide: [\"viru Christus\", \"no Christ<PERSON>\"]\n};\nconst quarterValues = {\n  narrow: [\"1\", \"2\", \"3\", \"4\"],\n  abbreviated: [\"Q1\", \"Q2\", \"Q3\", \"Q4\"],\n  wide: [\"1. Quartal\", \"2. Quartal\", \"3. Quartal\", \"4. Quartal\"]\n};\nconst monthValues = {\n  narrow: [\"J\", \"F\", \"M\", \"A\", \"M\", \"J\", \"J\", \"A\", \"S\", \"O\", \"N\", \"D\"],\n  abbreviated: [\"Jan\", \"Feb\", \"Mäe\", \"Abr\", \"Mee\", \"Jun\", \"Jul\", \"Aug\", \"Sep\", \"Okt\", \"Nov\", \"De<PERSON>\"],\n  wide: [\"<PERSON><PERSON><PERSON>\", \"<PERSON><PERSON><PERSON>\", \"<PERSON><PERSON><PERSON><PERSON>\", \"<PERSON><PERSON><PERSON><PERSON><PERSON>\", \"<PERSON><PERSON>\", \"<PERSON><PERSON>\", \"<PERSON><PERSON>\", \"August\", \"September\", \"<PERSON><PERSON><PERSON>\", \"November\", \"Dezember\"]\n};\nconst dayValues = {\n  narrow: [\"S\", \"M\", \"D\", \"M\", \"D\", \"F\", \"S\"],\n  short: [\"So\", \"Mé\", \"Dë\", \"Më\", \"Do\", \"Fr\", \"Sa\"],\n  abbreviated: [\"So.\", \"Mé.\", \"Dë.\", \"Më.\", \"Do.\", \"Fr.\", \"Sa.\"],\n  wide: [\"Sonndeg\", \"Méindeg\", \"Dënschdeg\", \"Mëttwoch\", \"Donneschdeg\", \"Freideg\", \"Samschdeg\"]\n};\nconst dayPeriodValues = {\n  narrow: {\n    am: \"mo.\",\n    pm: \"nomë.\",\n    midnight: \"Mëtternuecht\",\n    noon: \"Mëtteg\",\n    morning: \"Moien\",\n    afternoon: \"Nomëtteg\",\n    evening: \"Owend\",\n    night: \"Nuecht\"\n  },\n  abbreviated: {\n    am: \"moies\",\n    pm: \"nomëttes\",\n    midnight: \"Mëtternuecht\",\n    noon: \"Mëtteg\",\n    morning: \"Moien\",\n    afternoon: \"Nomëtteg\",\n    evening: \"Owend\",\n    night: \"Nuecht\"\n  },\n  wide: {\n    am: \"moies\",\n    pm: \"nomëttes\",\n    midnight: \"Mëtternuecht\",\n    noon: \"Mëtteg\",\n    morning: \"Moien\",\n    afternoon: \"Nomëtteg\",\n    evening: \"Owend\",\n    night: \"Nuecht\"\n  }\n};\nconst formattingDayPeriodValues = {\n  narrow: {\n    am: \"mo.\",\n    pm: \"nom.\",\n    midnight: \"Mëtternuecht\",\n    noon: \"mëttes\",\n    morning: \"moies\",\n    afternoon: \"nomëttes\",\n    evening: \"owes\",\n    night: \"nuets\"\n  },\n  abbreviated: {\n    am: \"moies\",\n    pm: \"nomëttes\",\n    midnight: \"Mëtternuecht\",\n    noon: \"mëttes\",\n    morning: \"moies\",\n    afternoon: \"nomëttes\",\n    evening: \"owes\",\n    night: \"nuets\"\n  },\n  wide: {\n    am: \"moies\",\n    pm: \"nomëttes\",\n    midnight: \"Mëtternuecht\",\n    noon: \"mëttes\",\n    morning: \"moies\",\n    afternoon: \"nomëttes\",\n    evening: \"owes\",\n    night: \"nuets\"\n  }\n};\nconst ordinalNumber = (dirtyNumber, _options) => {\n  const number = Number(dirtyNumber);\n  return number + \".\";\n};\nexport const localize = {\n  ordinalNumber,\n  era: buildLocalizeFn({\n    values: eraValues,\n    defaultWidth: \"wide\"\n  }),\n  quarter: buildLocalizeFn({\n    values: quarterValues,\n    defaultWidth: \"wide\",\n    argumentCallback: quarter => quarter - 1\n  }),\n  month: buildLocalizeFn({\n    values: monthValues,\n    defaultWidth: \"wide\"\n  }),\n  day: buildLocalizeFn({\n    values: dayValues,\n    defaultWidth: \"wide\"\n  }),\n  dayPeriod: buildLocalizeFn({\n    values: dayPeriodValues,\n    defaultWidth: \"wide\",\n    formattingValues: formattingDayPeriodValues,\n    defaultFormattingWidth: \"wide\"\n  })\n};", "map": {"version": 3, "names": ["buildLocalizeFn", "<PERSON><PERSON><PERSON><PERSON>", "narrow", "abbreviated", "wide", "quarterValues", "month<PERSON><PERSON><PERSON>", "dayV<PERSON><PERSON>", "short", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "am", "pm", "midnight", "noon", "morning", "afternoon", "evening", "night", "formattingDayPeriodValues", "ordinalNumber", "dirtyNumber", "_options", "number", "Number", "localize", "era", "values", "defaultWidth", "quarter", "argument<PERSON>allback", "month", "day", "<PERSON><PERSON><PERSON><PERSON>", "formattingValues", "defaultFormattingWidth"], "sources": ["C:/Users/<USER>/Desktop/demo-git/demo-react/my-fullstack-app/frontend/node_modules/date-fns/locale/lb/_lib/localize.js"], "sourcesContent": ["import { buildLocalizeFn } from \"../../_lib/buildLocalizeFn.js\";\n\nconst eraValues = {\n  narrow: [\"v.Chr.\", \"n.Chr.\"],\n  abbreviated: [\"v.Chr.\", \"n.Chr.\"],\n  wide: [\"viru Christus\", \"no Christ<PERSON>\"],\n};\n\nconst quarterValues = {\n  narrow: [\"1\", \"2\", \"3\", \"4\"],\n  abbreviated: [\"Q1\", \"Q2\", \"Q3\", \"Q4\"],\n  wide: [\"1. Quartal\", \"2. Quartal\", \"3. Quartal\", \"4. Quartal\"],\n};\n\nconst monthValues = {\n  narrow: [\"J\", \"F\", \"M\", \"A\", \"M\", \"J\", \"J\", \"A\", \"S\", \"O\", \"N\", \"D\"],\n  abbreviated: [\n    \"Jan\",\n    \"Feb\",\n    \"Mäe\",\n    \"Abr\",\n    \"Mee\",\n    \"<PERSON>\",\n    \"Jul\",\n    \"Aug\",\n    \"Sep\",\n    \"Okt\",\n    \"Nov\",\n    \"Dez\",\n  ],\n\n  wide: [\n    \"<PERSON><PERSON><PERSON>\",\n    \"<PERSON><PERSON><PERSON>\",\n    \"<PERSON><PERSON><PERSON><PERSON>\",\n    \"<PERSON>b<PERSON><PERSON><PERSON>\",\n    \"<PERSON><PERSON>\",\n    \"<PERSON><PERSON>\",\n    \"<PERSON><PERSON>\",\n    \"August\",\n    \"September\",\n    \"<PERSON><PERSON><PERSON>\",\n    \"November\",\n    \"Dezember\",\n  ],\n};\n\nconst dayValues = {\n  narrow: [\"S\", \"M\", \"D\", \"M\", \"D\", \"F\", \"S\"],\n  short: [\"So\", \"Mé\", \"Dë\", \"Më\", \"Do\", \"Fr\", \"Sa\"],\n  abbreviated: [\"So.\", \"Mé.\", \"Dë.\", \"Më.\", \"Do.\", \"Fr.\", \"Sa.\"],\n  wide: [\n    \"Sonndeg\",\n    \"Méindeg\",\n    \"Dënschdeg\",\n    \"Mëttwoch\",\n    \"Donneschdeg\",\n    \"Freideg\",\n    \"Samschdeg\",\n  ],\n};\n\nconst dayPeriodValues = {\n  narrow: {\n    am: \"mo.\",\n    pm: \"nomë.\",\n    midnight: \"Mëtternuecht\",\n    noon: \"Mëtteg\",\n    morning: \"Moien\",\n    afternoon: \"Nomëtteg\",\n    evening: \"Owend\",\n    night: \"Nuecht\",\n  },\n  abbreviated: {\n    am: \"moies\",\n    pm: \"nomëttes\",\n    midnight: \"Mëtternuecht\",\n    noon: \"Mëtteg\",\n    morning: \"Moien\",\n    afternoon: \"Nomëtteg\",\n    evening: \"Owend\",\n    night: \"Nuecht\",\n  },\n  wide: {\n    am: \"moies\",\n    pm: \"nomëttes\",\n    midnight: \"Mëtternuecht\",\n    noon: \"Mëtteg\",\n    morning: \"Moien\",\n    afternoon: \"Nomëtteg\",\n    evening: \"Owend\",\n    night: \"Nuecht\",\n  },\n};\n\nconst formattingDayPeriodValues = {\n  narrow: {\n    am: \"mo.\",\n    pm: \"nom.\",\n    midnight: \"Mëtternuecht\",\n    noon: \"mëttes\",\n    morning: \"moies\",\n    afternoon: \"nomëttes\",\n    evening: \"owes\",\n    night: \"nuets\",\n  },\n  abbreviated: {\n    am: \"moies\",\n    pm: \"nomëttes\",\n    midnight: \"Mëtternuecht\",\n    noon: \"mëttes\",\n    morning: \"moies\",\n    afternoon: \"nomëttes\",\n    evening: \"owes\",\n    night: \"nuets\",\n  },\n  wide: {\n    am: \"moies\",\n    pm: \"nomëttes\",\n    midnight: \"Mëtternuecht\",\n    noon: \"mëttes\",\n    morning: \"moies\",\n    afternoon: \"nomëttes\",\n    evening: \"owes\",\n    night: \"nuets\",\n  },\n};\n\nconst ordinalNumber = (dirtyNumber, _options) => {\n  const number = Number(dirtyNumber);\n  return number + \".\";\n};\n\nexport const localize = {\n  ordinalNumber,\n\n  era: buildLocalizeFn({\n    values: eraValues,\n    defaultWidth: \"wide\",\n  }),\n\n  quarter: buildLocalizeFn({\n    values: quarterValues,\n    defaultWidth: \"wide\",\n    argumentCallback: (quarter) => quarter - 1,\n  }),\n\n  month: buildLocalizeFn({\n    values: monthValues,\n    defaultWidth: \"wide\",\n  }),\n\n  day: buildLocalizeFn({\n    values: dayValues,\n    defaultWidth: \"wide\",\n  }),\n\n  dayPeriod: buildLocalizeFn({\n    values: dayPeriodValues,\n    defaultWidth: \"wide\",\n    formattingValues: formattingDayPeriodValues,\n    defaultFormattingWidth: \"wide\",\n  }),\n};\n"], "mappings": "AAAA,SAASA,eAAe,QAAQ,+BAA+B;AAE/D,MAAMC,SAAS,GAAG;EAChBC,MAAM,EAAE,CAAC,QAAQ,EAAE,QAAQ,CAAC;EAC5BC,WAAW,EAAE,CAAC,QAAQ,EAAE,QAAQ,CAAC;EACjCC,IAAI,EAAE,CAAC,eAAe,EAAE,aAAa;AACvC,CAAC;AAED,MAAMC,aAAa,GAAG;EACpBH,MAAM,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EAC5BC,WAAW,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;EACrCC,IAAI,EAAE,CAAC,YAAY,EAAE,YAAY,EAAE,YAAY,EAAE,YAAY;AAC/D,CAAC;AAED,MAAME,WAAW,GAAG;EAClBJ,MAAM,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EACpEC,WAAW,EAAE,CACX,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,CACN;EAEDC,IAAI,EAAE,CACJ,QAAQ,EACR,SAAS,EACT,OAAO,EACP,QAAQ,EACR,KAAK,EACL,MAAM,EACN,MAAM,EACN,QAAQ,EACR,WAAW,EACX,SAAS,EACT,UAAU,EACV,UAAU;AAEd,CAAC;AAED,MAAMG,SAAS,GAAG;EAChBL,MAAM,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EAC3CM,KAAK,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;EACjDL,WAAW,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC;EAC9DC,IAAI,EAAE,CACJ,SAAS,EACT,SAAS,EACT,WAAW,EACX,UAAU,EACV,aAAa,EACb,SAAS,EACT,WAAW;AAEf,CAAC;AAED,MAAMK,eAAe,GAAG;EACtBP,MAAM,EAAE;IACNQ,EAAE,EAAE,KAAK;IACTC,EAAE,EAAE,OAAO;IACXC,QAAQ,EAAE,cAAc;IACxBC,IAAI,EAAE,QAAQ;IACdC,OAAO,EAAE,OAAO;IAChBC,SAAS,EAAE,UAAU;IACrBC,OAAO,EAAE,OAAO;IAChBC,KAAK,EAAE;EACT,CAAC;EACDd,WAAW,EAAE;IACXO,EAAE,EAAE,OAAO;IACXC,EAAE,EAAE,UAAU;IACdC,QAAQ,EAAE,cAAc;IACxBC,IAAI,EAAE,QAAQ;IACdC,OAAO,EAAE,OAAO;IAChBC,SAAS,EAAE,UAAU;IACrBC,OAAO,EAAE,OAAO;IAChBC,KAAK,EAAE;EACT,CAAC;EACDb,IAAI,EAAE;IACJM,EAAE,EAAE,OAAO;IACXC,EAAE,EAAE,UAAU;IACdC,QAAQ,EAAE,cAAc;IACxBC,IAAI,EAAE,QAAQ;IACdC,OAAO,EAAE,OAAO;IAChBC,SAAS,EAAE,UAAU;IACrBC,OAAO,EAAE,OAAO;IAChBC,KAAK,EAAE;EACT;AACF,CAAC;AAED,MAAMC,yBAAyB,GAAG;EAChChB,MAAM,EAAE;IACNQ,EAAE,EAAE,KAAK;IACTC,EAAE,EAAE,MAAM;IACVC,QAAQ,EAAE,cAAc;IACxBC,IAAI,EAAE,QAAQ;IACdC,OAAO,EAAE,OAAO;IAChBC,SAAS,EAAE,UAAU;IACrBC,OAAO,EAAE,MAAM;IACfC,KAAK,EAAE;EACT,CAAC;EACDd,WAAW,EAAE;IACXO,EAAE,EAAE,OAAO;IACXC,EAAE,EAAE,UAAU;IACdC,QAAQ,EAAE,cAAc;IACxBC,IAAI,EAAE,QAAQ;IACdC,OAAO,EAAE,OAAO;IAChBC,SAAS,EAAE,UAAU;IACrBC,OAAO,EAAE,MAAM;IACfC,KAAK,EAAE;EACT,CAAC;EACDb,IAAI,EAAE;IACJM,EAAE,EAAE,OAAO;IACXC,EAAE,EAAE,UAAU;IACdC,QAAQ,EAAE,cAAc;IACxBC,IAAI,EAAE,QAAQ;IACdC,OAAO,EAAE,OAAO;IAChBC,SAAS,EAAE,UAAU;IACrBC,OAAO,EAAE,MAAM;IACfC,KAAK,EAAE;EACT;AACF,CAAC;AAED,MAAME,aAAa,GAAGA,CAACC,WAAW,EAAEC,QAAQ,KAAK;EAC/C,MAAMC,MAAM,GAAGC,MAAM,CAACH,WAAW,CAAC;EAClC,OAAOE,MAAM,GAAG,GAAG;AACrB,CAAC;AAED,OAAO,MAAME,QAAQ,GAAG;EACtBL,aAAa;EAEbM,GAAG,EAAEzB,eAAe,CAAC;IACnB0B,MAAM,EAAEzB,SAAS;IACjB0B,YAAY,EAAE;EAChB,CAAC,CAAC;EAEFC,OAAO,EAAE5B,eAAe,CAAC;IACvB0B,MAAM,EAAErB,aAAa;IACrBsB,YAAY,EAAE,MAAM;IACpBE,gBAAgB,EAAGD,OAAO,IAAKA,OAAO,GAAG;EAC3C,CAAC,CAAC;EAEFE,KAAK,EAAE9B,eAAe,CAAC;IACrB0B,MAAM,EAAEpB,WAAW;IACnBqB,YAAY,EAAE;EAChB,CAAC,CAAC;EAEFI,GAAG,EAAE/B,eAAe,CAAC;IACnB0B,MAAM,EAAEnB,SAAS;IACjBoB,YAAY,EAAE;EAChB,CAAC,CAAC;EAEFK,SAAS,EAAEhC,eAAe,CAAC;IACzB0B,MAAM,EAAEjB,eAAe;IACvBkB,YAAY,EAAE,MAAM;IACpBM,gBAAgB,EAAEf,yBAAyB;IAC3CgB,sBAAsB,EAAE;EAC1B,CAAC;AACH,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
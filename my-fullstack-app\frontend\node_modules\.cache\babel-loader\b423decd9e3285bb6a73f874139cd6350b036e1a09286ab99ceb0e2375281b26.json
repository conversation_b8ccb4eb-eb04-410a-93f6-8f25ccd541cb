{"ast": null, "code": "import { tzIntlTimeZoneName } from '../../_lib/tzIntlTimeZoneName/index.js';\nimport { tzParseTimezone } from '../../_lib/tzParseTimezone/index.js';\nconst MILLISECONDS_IN_MINUTE = 60 * 1000;\nexport const formatters = {\n  // Timezone (ISO-8601. If offset is 0, output is always `'Z'`)\n  X: function (date, token, options) {\n    const timezoneOffset = getTimeZoneOffset(options.timeZone, date);\n    if (timezoneOffset === 0) {\n      return 'Z';\n    }\n    switch (token) {\n      // Hours and optional minutes\n      case 'X':\n        return formatTimezoneWithOptionalMinutes(timezoneOffset);\n      // Hours, minutes and optional seconds without `:` delimeter\n      // Note: neither ISO-8601 nor JavaScript supports seconds in timezone offsets\n      // so this token always has the same output as `XX`\n      case 'XXXX':\n      case 'XX':\n        // Hours and minutes without `:` delimeter\n        return formatTimezone(timezoneOffset);\n      // Hours, minutes and optional seconds with `:` delimeter\n      // Note: neither ISO-8601 nor JavaScript supports seconds in timezone offsets\n      // so this token always has the same output as `XXX`\n      case 'XXXXX':\n      case 'XXX': // Hours and minutes with `:` delimeter\n      default:\n        return formatTimezone(timezoneOffset, ':');\n    }\n  },\n  // Timezone (ISO-8601. If offset is 0, output is `'+00:00'` or equivalent)\n  x: function (date, token, options) {\n    const timezoneOffset = getTimeZoneOffset(options.timeZone, date);\n    switch (token) {\n      // Hours and optional minutes\n      case 'x':\n        return formatTimezoneWithOptionalMinutes(timezoneOffset);\n      // Hours, minutes and optional seconds without `:` delimeter\n      // Note: neither ISO-8601 nor JavaScript supports seconds in timezone offsets\n      // so this token always has the same output as `xx`\n      case 'xxxx':\n      case 'xx':\n        // Hours and minutes without `:` delimeter\n        return formatTimezone(timezoneOffset);\n      // Hours, minutes and optional seconds with `:` delimeter\n      // Note: neither ISO-8601 nor JavaScript supports seconds in timezone offsets\n      // so this token always has the same output as `xxx`\n      case 'xxxxx':\n      case 'xxx': // Hours and minutes with `:` delimeter\n      default:\n        return formatTimezone(timezoneOffset, ':');\n    }\n  },\n  // Timezone (GMT)\n  O: function (date, token, options) {\n    const timezoneOffset = getTimeZoneOffset(options.timeZone, date);\n    switch (token) {\n      // Short\n      case 'O':\n      case 'OO':\n      case 'OOO':\n        return 'GMT' + formatTimezoneShort(timezoneOffset, ':');\n      // Long\n      case 'OOOO':\n      default:\n        return 'GMT' + formatTimezone(timezoneOffset, ':');\n    }\n  },\n  // Timezone (specific non-location)\n  z: function (date, token, options) {\n    switch (token) {\n      // Short\n      case 'z':\n      case 'zz':\n      case 'zzz':\n        return tzIntlTimeZoneName('short', date, options);\n      // Long\n      case 'zzzz':\n      default:\n        return tzIntlTimeZoneName('long', date, options);\n    }\n  }\n};\nfunction getTimeZoneOffset(timeZone, originalDate) {\n  var _originalDate$getTime;\n  const timeZoneOffset = timeZone ? tzParseTimezone(timeZone, originalDate, true) / MILLISECONDS_IN_MINUTE : (_originalDate$getTime = originalDate === null || originalDate === void 0 ? void 0 : originalDate.getTimezoneOffset()) !== null && _originalDate$getTime !== void 0 ? _originalDate$getTime : 0;\n  if (Number.isNaN(timeZoneOffset)) {\n    throw new RangeError('Invalid time zone specified: ' + timeZone);\n  }\n  return timeZoneOffset;\n}\nfunction addLeadingZeros(number, targetLength) {\n  const sign = number < 0 ? '-' : '';\n  let output = Math.abs(number).toString();\n  while (output.length < targetLength) {\n    output = '0' + output;\n  }\n  return sign + output;\n}\nfunction formatTimezone(offset) {\n  let delimiter = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : '';\n  const sign = offset > 0 ? '-' : '+';\n  const absOffset = Math.abs(offset);\n  const hours = addLeadingZeros(Math.floor(absOffset / 60), 2);\n  const minutes = addLeadingZeros(Math.floor(absOffset % 60), 2);\n  return sign + hours + delimiter + minutes;\n}\nfunction formatTimezoneWithOptionalMinutes(offset, delimiter) {\n  if (offset % 60 === 0) {\n    const sign = offset > 0 ? '-' : '+';\n    return sign + addLeadingZeros(Math.abs(offset) / 60, 2);\n  }\n  return formatTimezone(offset, delimiter);\n}\nfunction formatTimezoneShort(offset) {\n  let delimiter = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : '';\n  const sign = offset > 0 ? '-' : '+';\n  const absOffset = Math.abs(offset);\n  const hours = Math.floor(absOffset / 60);\n  const minutes = absOffset % 60;\n  if (minutes === 0) {\n    return sign + String(hours);\n  }\n  return sign + String(hours) + delimiter + addLeadingZeros(minutes, 2);\n}", "map": {"version": 3, "names": ["tzIntlTimeZoneName", "tzParseTimezone", "MILLISECONDS_IN_MINUTE", "formatters", "X", "date", "token", "options", "timezoneOffset", "getTimeZoneOffset", "timeZone", "formatTimezoneWithOptionalMinutes", "formatTimezone", "x", "O", "formatTimezoneShort", "z", "originalDate", "_originalDate$getTime", "timeZoneOffset", "getTimezoneOffset", "Number", "isNaN", "RangeError", "addLeadingZeros", "number", "targetLength", "sign", "output", "Math", "abs", "toString", "length", "offset", "delimiter", "arguments", "undefined", "absOffset", "hours", "floor", "minutes", "String"], "sources": ["C:/Users/<USER>/Desktop/demo-git/demo-react/my-fullstack-app/frontend/node_modules/date-fns-tz/dist/esm/format/formatters/index.js"], "sourcesContent": ["import { tzIntlTimeZoneName } from '../../_lib/tzIntlTimeZoneName/index.js';\nimport { tzParseTimezone } from '../../_lib/tzParseTimezone/index.js';\nconst MILLISECONDS_IN_MINUTE = 60 * 1000;\nexport const formatters = {\n    // Timezone (ISO-8601. If offset is 0, output is always `'Z'`)\n    X: function (date, token, options) {\n        const timezoneOffset = getTimeZoneOffset(options.timeZone, date);\n        if (timezoneOffset === 0) {\n            return 'Z';\n        }\n        switch (token) {\n            // Hours and optional minutes\n            case 'X':\n                return formatTimezoneWithOptionalMinutes(timezoneOffset);\n            // Hours, minutes and optional seconds without `:` delimeter\n            // Note: neither ISO-8601 nor JavaScript supports seconds in timezone offsets\n            // so this token always has the same output as `XX`\n            case 'XXXX':\n            case 'XX': // Hours and minutes without `:` delimeter\n                return formatTimezone(timezoneOffset);\n            // Hours, minutes and optional seconds with `:` delimeter\n            // Note: neither ISO-8601 nor JavaScript supports seconds in timezone offsets\n            // so this token always has the same output as `XXX`\n            case 'XXXXX':\n            case 'XXX': // Hours and minutes with `:` delimeter\n            default:\n                return formatTimezone(timezoneOffset, ':');\n        }\n    },\n    // Timezone (ISO-8601. If offset is 0, output is `'+00:00'` or equivalent)\n    x: function (date, token, options) {\n        const timezoneOffset = getTimeZoneOffset(options.timeZone, date);\n        switch (token) {\n            // Hours and optional minutes\n            case 'x':\n                return formatTimezoneWithOptionalMinutes(timezoneOffset);\n            // Hours, minutes and optional seconds without `:` delimeter\n            // Note: neither ISO-8601 nor JavaScript supports seconds in timezone offsets\n            // so this token always has the same output as `xx`\n            case 'xxxx':\n            case 'xx': // Hours and minutes without `:` delimeter\n                return formatTimezone(timezoneOffset);\n            // Hours, minutes and optional seconds with `:` delimeter\n            // Note: neither ISO-8601 nor JavaScript supports seconds in timezone offsets\n            // so this token always has the same output as `xxx`\n            case 'xxxxx':\n            case 'xxx': // Hours and minutes with `:` delimeter\n            default:\n                return formatTimezone(timezoneOffset, ':');\n        }\n    },\n    // Timezone (GMT)\n    O: function (date, token, options) {\n        const timezoneOffset = getTimeZoneOffset(options.timeZone, date);\n        switch (token) {\n            // Short\n            case 'O':\n            case 'OO':\n            case 'OOO':\n                return 'GMT' + formatTimezoneShort(timezoneOffset, ':');\n            // Long\n            case 'OOOO':\n            default:\n                return 'GMT' + formatTimezone(timezoneOffset, ':');\n        }\n    },\n    // Timezone (specific non-location)\n    z: function (date, token, options) {\n        switch (token) {\n            // Short\n            case 'z':\n            case 'zz':\n            case 'zzz':\n                return tzIntlTimeZoneName('short', date, options);\n            // Long\n            case 'zzzz':\n            default:\n                return tzIntlTimeZoneName('long', date, options);\n        }\n    },\n};\nfunction getTimeZoneOffset(timeZone, originalDate) {\n    const timeZoneOffset = timeZone\n        ? tzParseTimezone(timeZone, originalDate, true) / MILLISECONDS_IN_MINUTE\n        : originalDate?.getTimezoneOffset() ?? 0;\n    if (Number.isNaN(timeZoneOffset)) {\n        throw new RangeError('Invalid time zone specified: ' + timeZone);\n    }\n    return timeZoneOffset;\n}\nfunction addLeadingZeros(number, targetLength) {\n    const sign = number < 0 ? '-' : '';\n    let output = Math.abs(number).toString();\n    while (output.length < targetLength) {\n        output = '0' + output;\n    }\n    return sign + output;\n}\nfunction formatTimezone(offset, delimiter = '') {\n    const sign = offset > 0 ? '-' : '+';\n    const absOffset = Math.abs(offset);\n    const hours = addLeadingZeros(Math.floor(absOffset / 60), 2);\n    const minutes = addLeadingZeros(Math.floor(absOffset % 60), 2);\n    return sign + hours + delimiter + minutes;\n}\nfunction formatTimezoneWithOptionalMinutes(offset, delimiter) {\n    if (offset % 60 === 0) {\n        const sign = offset > 0 ? '-' : '+';\n        return sign + addLeadingZeros(Math.abs(offset) / 60, 2);\n    }\n    return formatTimezone(offset, delimiter);\n}\nfunction formatTimezoneShort(offset, delimiter = '') {\n    const sign = offset > 0 ? '-' : '+';\n    const absOffset = Math.abs(offset);\n    const hours = Math.floor(absOffset / 60);\n    const minutes = absOffset % 60;\n    if (minutes === 0) {\n        return sign + String(hours);\n    }\n    return sign + String(hours) + delimiter + addLeadingZeros(minutes, 2);\n}\n"], "mappings": "AAAA,SAASA,kBAAkB,QAAQ,wCAAwC;AAC3E,SAASC,eAAe,QAAQ,qCAAqC;AACrE,MAAMC,sBAAsB,GAAG,EAAE,GAAG,IAAI;AACxC,OAAO,MAAMC,UAAU,GAAG;EACtB;EACAC,CAAC,EAAE,SAAAA,CAAUC,IAAI,EAAEC,KAAK,EAAEC,OAAO,EAAE;IAC/B,MAAMC,cAAc,GAAGC,iBAAiB,CAACF,OAAO,CAACG,QAAQ,EAAEL,IAAI,CAAC;IAChE,IAAIG,cAAc,KAAK,CAAC,EAAE;MACtB,OAAO,GAAG;IACd;IACA,QAAQF,KAAK;MACT;MACA,KAAK,GAAG;QACJ,OAAOK,iCAAiC,CAACH,cAAc,CAAC;MAC5D;MACA;MACA;MACA,KAAK,MAAM;MACX,KAAK,IAAI;QAAE;QACP,OAAOI,cAAc,CAACJ,cAAc,CAAC;MACzC;MACA;MACA;MACA,KAAK,OAAO;MACZ,KAAK,KAAK,CAAC,CAAC;MACZ;QACI,OAAOI,cAAc,CAACJ,cAAc,EAAE,GAAG,CAAC;IAClD;EACJ,CAAC;EACD;EACAK,CAAC,EAAE,SAAAA,CAAUR,IAAI,EAAEC,KAAK,EAAEC,OAAO,EAAE;IAC/B,MAAMC,cAAc,GAAGC,iBAAiB,CAACF,OAAO,CAACG,QAAQ,EAAEL,IAAI,CAAC;IAChE,QAAQC,KAAK;MACT;MACA,KAAK,GAAG;QACJ,OAAOK,iCAAiC,CAACH,cAAc,CAAC;MAC5D;MACA;MACA;MACA,KAAK,MAAM;MACX,KAAK,IAAI;QAAE;QACP,OAAOI,cAAc,CAACJ,cAAc,CAAC;MACzC;MACA;MACA;MACA,KAAK,OAAO;MACZ,KAAK,KAAK,CAAC,CAAC;MACZ;QACI,OAAOI,cAAc,CAACJ,cAAc,EAAE,GAAG,CAAC;IAClD;EACJ,CAAC;EACD;EACAM,CAAC,EAAE,SAAAA,CAAUT,IAAI,EAAEC,KAAK,EAAEC,OAAO,EAAE;IAC/B,MAAMC,cAAc,GAAGC,iBAAiB,CAACF,OAAO,CAACG,QAAQ,EAAEL,IAAI,CAAC;IAChE,QAAQC,KAAK;MACT;MACA,KAAK,GAAG;MACR,KAAK,IAAI;MACT,KAAK,KAAK;QACN,OAAO,KAAK,GAAGS,mBAAmB,CAACP,cAAc,EAAE,GAAG,CAAC;MAC3D;MACA,KAAK,MAAM;MACX;QACI,OAAO,KAAK,GAAGI,cAAc,CAACJ,cAAc,EAAE,GAAG,CAAC;IAC1D;EACJ,CAAC;EACD;EACAQ,CAAC,EAAE,SAAAA,CAAUX,IAAI,EAAEC,KAAK,EAAEC,OAAO,EAAE;IAC/B,QAAQD,KAAK;MACT;MACA,KAAK,GAAG;MACR,KAAK,IAAI;MACT,KAAK,KAAK;QACN,OAAON,kBAAkB,CAAC,OAAO,EAAEK,IAAI,EAAEE,OAAO,CAAC;MACrD;MACA,KAAK,MAAM;MACX;QACI,OAAOP,kBAAkB,CAAC,MAAM,EAAEK,IAAI,EAAEE,OAAO,CAAC;IACxD;EACJ;AACJ,CAAC;AACD,SAASE,iBAAiBA,CAACC,QAAQ,EAAEO,YAAY,EAAE;EAAA,IAAAC,qBAAA;EAC/C,MAAMC,cAAc,GAAGT,QAAQ,GACzBT,eAAe,CAACS,QAAQ,EAAEO,YAAY,EAAE,IAAI,CAAC,GAAGf,sBAAsB,IAAAgB,qBAAA,GACtED,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAEG,iBAAiB,CAAC,CAAC,cAAAF,qBAAA,cAAAA,qBAAA,GAAI,CAAC;EAC5C,IAAIG,MAAM,CAACC,KAAK,CAACH,cAAc,CAAC,EAAE;IAC9B,MAAM,IAAII,UAAU,CAAC,+BAA+B,GAAGb,QAAQ,CAAC;EACpE;EACA,OAAOS,cAAc;AACzB;AACA,SAASK,eAAeA,CAACC,MAAM,EAAEC,YAAY,EAAE;EAC3C,MAAMC,IAAI,GAAGF,MAAM,GAAG,CAAC,GAAG,GAAG,GAAG,EAAE;EAClC,IAAIG,MAAM,GAAGC,IAAI,CAACC,GAAG,CAACL,MAAM,CAAC,CAACM,QAAQ,CAAC,CAAC;EACxC,OAAOH,MAAM,CAACI,MAAM,GAAGN,YAAY,EAAE;IACjCE,MAAM,GAAG,GAAG,GAAGA,MAAM;EACzB;EACA,OAAOD,IAAI,GAAGC,MAAM;AACxB;AACA,SAAShB,cAAcA,CAACqB,MAAM,EAAkB;EAAA,IAAhBC,SAAS,GAAAC,SAAA,CAAAH,MAAA,QAAAG,SAAA,QAAAC,SAAA,GAAAD,SAAA,MAAG,EAAE;EAC1C,MAAMR,IAAI,GAAGM,MAAM,GAAG,CAAC,GAAG,GAAG,GAAG,GAAG;EACnC,MAAMI,SAAS,GAAGR,IAAI,CAACC,GAAG,CAACG,MAAM,CAAC;EAClC,MAAMK,KAAK,GAAGd,eAAe,CAACK,IAAI,CAACU,KAAK,CAACF,SAAS,GAAG,EAAE,CAAC,EAAE,CAAC,CAAC;EAC5D,MAAMG,OAAO,GAAGhB,eAAe,CAACK,IAAI,CAACU,KAAK,CAACF,SAAS,GAAG,EAAE,CAAC,EAAE,CAAC,CAAC;EAC9D,OAAOV,IAAI,GAAGW,KAAK,GAAGJ,SAAS,GAAGM,OAAO;AAC7C;AACA,SAAS7B,iCAAiCA,CAACsB,MAAM,EAAEC,SAAS,EAAE;EAC1D,IAAID,MAAM,GAAG,EAAE,KAAK,CAAC,EAAE;IACnB,MAAMN,IAAI,GAAGM,MAAM,GAAG,CAAC,GAAG,GAAG,GAAG,GAAG;IACnC,OAAON,IAAI,GAAGH,eAAe,CAACK,IAAI,CAACC,GAAG,CAACG,MAAM,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC;EAC3D;EACA,OAAOrB,cAAc,CAACqB,MAAM,EAAEC,SAAS,CAAC;AAC5C;AACA,SAASnB,mBAAmBA,CAACkB,MAAM,EAAkB;EAAA,IAAhBC,SAAS,GAAAC,SAAA,CAAAH,MAAA,QAAAG,SAAA,QAAAC,SAAA,GAAAD,SAAA,MAAG,EAAE;EAC/C,MAAMR,IAAI,GAAGM,MAAM,GAAG,CAAC,GAAG,GAAG,GAAG,GAAG;EACnC,MAAMI,SAAS,GAAGR,IAAI,CAACC,GAAG,CAACG,MAAM,CAAC;EAClC,MAAMK,KAAK,GAAGT,IAAI,CAACU,KAAK,CAACF,SAAS,GAAG,EAAE,CAAC;EACxC,MAAMG,OAAO,GAAGH,SAAS,GAAG,EAAE;EAC9B,IAAIG,OAAO,KAAK,CAAC,EAAE;IACf,OAAOb,IAAI,GAAGc,MAAM,CAACH,KAAK,CAAC;EAC/B;EACA,OAAOX,IAAI,GAAGc,MAAM,CAACH,KAAK,CAAC,GAAGJ,SAAS,GAAGV,eAAe,CAACgB,OAAO,EAAE,CAAC,CAAC;AACzE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "import { buildLocalizeFn } from \"../../_lib/buildLocalizeFn.js\";\n\n// Reference: https://www.unicode.org/cldr/charts/32/summary/kn.html\n\nconst eraValues = {\n  narrow: [\"ಕ್ರಿ.ಪೂ\", \"ಕ್ರಿ.ಶ\"],\n  abbreviated: [\"ಕ್ರಿ.ಪೂ\", \"ಕ್ರಿ.ಶ\"],\n  // CLDR #1618, #1620\n  wide: [\"ಕ್ರಿಸ್ತ ಪೂರ್ವ\", \"ಕ್ರಿಸ್ತ ಶಕ\"] // CLDR #1614, #1616\n};\nconst quarterValues = {\n  narrow: [\"1\", \"2\", \"3\", \"4\"],\n  abbreviated: [\"ತ್ರೈ 1\", \"ತ್ರೈ 2\", \"ತ್ರೈ 3\", \"ತ್ರೈ 4\"],\n  // CLDR #1630 - #1638\n  wide: [\"1ನೇ ತ್ರೈಮಾಸಿಕ\", \"2ನೇ ತ್ರೈಮಾಸಿಕ\", \"3ನೇ ತ್ರೈಮಾಸಿಕ\", \"4ನೇ ತ್ರೈಮಾಸಿಕ\"]\n  // CLDR #1622 - #1629\n};\n\n// CLDR #1646 - #1717\nconst monthValues = {\n  narrow: [\"ಜ\", \"ಫೆ\", \"ಮಾ\", \"ಏ\", \"ಮೇ\", \"ಜೂ\", \"ಜು\", \"ಆ\", \"ಸೆ\", \"ಅ\", \"ನ\", \"ಡಿ\"],\n  abbreviated: [\"ಜನ\", \"ಫೆಬ್ರ\", \"ಮಾರ್ಚ್\", \"ಏಪ್ರಿ\", \"ಮೇ\", \"ಜೂನ್\", \"ಜುಲೈ\", \"ಆಗ\", \"ಸೆಪ್ಟೆಂ\", \"ಅಕ್ಟೋ\", \"ನವೆಂ\", \"ಡಿಸೆಂ\"],\n  wide: [\"ಜನವರಿ\", \"ಫೆಬ್ರವರಿ\", \"ಮಾರ್ಚ್\", \"ಏಪ್ರಿಲ್\", \"ಮೇ\", \"ಜೂನ್\", \"ಜುಲೈ\", \"ಆಗಸ್ಟ್\", \"ಸೆಪ್ಟೆಂಬರ್\", \"ಅಕ್ಟೋಬರ್\", \"ನವೆಂಬರ್\", \"ಡಿಸೆಂಬರ್\"]\n};\n\n// CLDR #1718 - #1773\nconst dayValues = {\n  narrow: [\"ಭಾ\", \"ಸೋ\", \"ಮಂ\", \"ಬು\", \"ಗು\", \"ಶು\", \"ಶ\"],\n  short: [\"ಭಾನು\", \"ಸೋಮ\", \"ಮಂಗಳ\", \"ಬುಧ\", \"ಗುರು\", \"ಶುಕ್ರ\", \"ಶನಿ\"],\n  abbreviated: [\"ಭಾನು\", \"ಸೋಮ\", \"ಮಂಗಳ\", \"ಬುಧ\", \"ಗುರು\", \"ಶುಕ್ರ\", \"ಶನಿ\"],\n  wide: [\"ಭಾನುವಾರ\", \"ಸೋಮವಾರ\", \"ಮಂಗಳವಾರ\", \"ಬುಧವಾರ\", \"ಗುರುವಾರ\", \"ಶುಕ್ರವಾರ\", \"ಶನಿವಾರ\"]\n};\n\n// CLDR #1774 - #1815\nconst dayPeriodValues = {\n  narrow: {\n    am: \"ಪೂರ್ವಾಹ್ನ\",\n    pm: \"ಅಪರಾಹ್ನ\",\n    midnight: \"ಮಧ್ಯರಾತ್ರಿ\",\n    noon: \"ಮಧ್ಯಾಹ್ನ\",\n    morning: \"ಬೆಳಗ್ಗೆ\",\n    afternoon: \"ಮಧ್ಯಾಹ್ನ\",\n    evening: \"ಸಂಜೆ\",\n    night: \"ರಾತ್ರಿ\"\n  },\n  abbreviated: {\n    am: \"ಪೂರ್ವಾಹ್ನ\",\n    pm: \"ಅಪರಾಹ್ನ\",\n    midnight: \"ಮಧ್ಯರಾತ್ರಿ\",\n    noon: \"ಮಧ್ಯಾನ್ಹ\",\n    morning: \"ಬೆಳಗ್ಗೆ\",\n    afternoon: \"ಮಧ್ಯಾನ್ಹ\",\n    evening: \"ಸಂಜೆ\",\n    night: \"ರಾತ್ರಿ\"\n  },\n  wide: {\n    am: \"ಪೂರ್ವಾಹ್ನ\",\n    pm: \"ಅಪರಾಹ್ನ\",\n    midnight: \"ಮಧ್ಯರಾತ್ರಿ\",\n    noon: \"ಮಧ್ಯಾನ್ಹ\",\n    morning: \"ಬೆಳಗ್ಗೆ\",\n    afternoon: \"ಮಧ್ಯಾನ್ಹ\",\n    evening: \"ಸಂಜೆ\",\n    night: \"ರಾತ್ರಿ\"\n  }\n};\nconst formattingDayPeriodValues = {\n  narrow: {\n    am: \"ಪೂ\",\n    pm: \"ಅ\",\n    midnight: \"ಮಧ್ಯರಾತ್ರಿ\",\n    noon: \"ಮಧ್ಯಾನ್ಹ\",\n    morning: \"ಬೆಳಗ್ಗೆ\",\n    afternoon: \"ಮಧ್ಯಾನ್ಹ\",\n    evening: \"ಸಂಜೆ\",\n    night: \"ರಾತ್ರಿ\"\n  },\n  abbreviated: {\n    am: \"ಪೂರ್ವಾಹ್ನ\",\n    pm: \"ಅಪರಾಹ್ನ\",\n    midnight: \"ಮಧ್ಯ ರಾತ್ರಿ\",\n    noon: \"ಮಧ್ಯಾನ್ಹ\",\n    morning: \"ಬೆಳಗ್ಗೆ\",\n    afternoon: \"ಮಧ್ಯಾನ್ಹ\",\n    evening: \"ಸಂಜೆ\",\n    night: \"ರಾತ್ರಿ\"\n  },\n  wide: {\n    am: \"ಪೂರ್ವಾಹ್ನ\",\n    pm: \"ಅಪರಾಹ್ನ\",\n    midnight: \"ಮಧ್ಯ ರಾತ್ರಿ\",\n    noon: \"ಮಧ್ಯಾನ್ಹ\",\n    morning: \"ಬೆಳಗ್ಗೆ\",\n    afternoon: \"ಮಧ್ಯಾನ್ಹ\",\n    evening: \"ಸಂಜೆ\",\n    night: \"ರಾತ್ರಿ\"\n  }\n};\nconst ordinalNumber = (dirtyNumber, _options) => {\n  const number = Number(dirtyNumber);\n  return number + \"ನೇ\";\n};\nexport const localize = {\n  ordinalNumber,\n  era: buildLocalizeFn({\n    values: eraValues,\n    defaultWidth: \"wide\"\n  }),\n  quarter: buildLocalizeFn({\n    values: quarterValues,\n    defaultWidth: \"wide\",\n    argumentCallback: quarter => quarter - 1\n  }),\n  month: buildLocalizeFn({\n    values: monthValues,\n    defaultWidth: \"wide\"\n  }),\n  day: buildLocalizeFn({\n    values: dayValues,\n    defaultWidth: \"wide\"\n  }),\n  dayPeriod: buildLocalizeFn({\n    values: dayPeriodValues,\n    defaultWidth: \"wide\",\n    formattingValues: formattingDayPeriodValues,\n    defaultFormattingWidth: \"wide\"\n  })\n};", "map": {"version": 3, "names": ["buildLocalizeFn", "<PERSON><PERSON><PERSON><PERSON>", "narrow", "abbreviated", "wide", "quarterValues", "month<PERSON><PERSON><PERSON>", "dayV<PERSON><PERSON>", "short", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "am", "pm", "midnight", "noon", "morning", "afternoon", "evening", "night", "formattingDayPeriodValues", "ordinalNumber", "dirtyNumber", "_options", "number", "Number", "localize", "era", "values", "defaultWidth", "quarter", "argument<PERSON>allback", "month", "day", "<PERSON><PERSON><PERSON><PERSON>", "formattingValues", "defaultFormattingWidth"], "sources": ["C:/Users/<USER>/Desktop/demo-git/demo-react/my-fullstack-app/frontend/node_modules/date-fns/locale/kn/_lib/localize.js"], "sourcesContent": ["import { buildLocalizeFn } from \"../../_lib/buildLocalizeFn.js\";\n\n// Reference: https://www.unicode.org/cldr/charts/32/summary/kn.html\n\nconst eraValues = {\n  narrow: [\"ಕ್ರಿ.ಪೂ\", \"ಕ್ರಿ.ಶ\"],\n  abbreviated: [\"ಕ್ರಿ.ಪೂ\", \"ಕ್ರಿ.ಶ\"], // CLDR #1618, #1620\n  wide: [\"ಕ್ರಿಸ್ತ ಪೂರ್ವ\", \"ಕ್ರಿಸ್ತ ಶಕ\"], // CLDR #1614, #1616\n};\n\nconst quarterValues = {\n  narrow: [\"1\", \"2\", \"3\", \"4\"],\n  abbreviated: [\"ತ್ರೈ 1\", \"ತ್ರೈ 2\", \"ತ್ರೈ 3\", \"ತ್ರೈ 4\"], // CLDR #1630 - #1638\n  wide: [\"1ನೇ ತ್ರೈಮಾಸಿಕ\", \"2ನೇ ತ್ರೈಮಾಸಿಕ\", \"3ನೇ ತ್ರೈಮಾಸಿಕ\", \"4ನೇ ತ್ರೈಮಾಸಿಕ\"],\n  // CLDR #1622 - #1629\n};\n\n// CLDR #1646 - #1717\nconst monthValues = {\n  narrow: [\"ಜ\", \"ಫೆ\", \"ಮಾ\", \"ಏ\", \"ಮೇ\", \"ಜೂ\", \"ಜು\", \"ಆ\", \"ಸೆ\", \"ಅ\", \"ನ\", \"ಡಿ\"],\n\n  abbreviated: [\n    \"ಜನ\",\n    \"ಫೆಬ್ರ\",\n    \"ಮಾರ್ಚ್\",\n    \"ಏಪ್ರಿ\",\n    \"ಮೇ\",\n    \"ಜೂನ್\",\n    \"ಜುಲೈ\",\n    \"ಆಗ\",\n    \"ಸೆಪ್ಟೆಂ\",\n    \"ಅಕ್ಟೋ\",\n    \"ನವೆಂ\",\n    \"ಡಿಸೆಂ\",\n  ],\n\n  wide: [\n    \"ಜನವರಿ\",\n    \"ಫೆಬ್ರವರಿ\",\n    \"ಮಾರ್ಚ್\",\n    \"ಏಪ್ರಿಲ್\",\n    \"ಮೇ\",\n    \"ಜೂನ್\",\n    \"ಜುಲೈ\",\n    \"ಆಗಸ್ಟ್\",\n    \"ಸೆಪ್ಟೆಂಬರ್\",\n    \"ಅಕ್ಟೋಬರ್\",\n    \"ನವೆಂಬರ್\",\n    \"ಡಿಸೆಂಬರ್\",\n  ],\n};\n\n// CLDR #1718 - #1773\nconst dayValues = {\n  narrow: [\"ಭಾ\", \"ಸೋ\", \"ಮಂ\", \"ಬು\", \"ಗು\", \"ಶು\", \"ಶ\"],\n  short: [\"ಭಾನು\", \"ಸೋಮ\", \"ಮಂಗಳ\", \"ಬುಧ\", \"ಗುರು\", \"ಶುಕ್ರ\", \"ಶನಿ\"],\n  abbreviated: [\"ಭಾನು\", \"ಸೋಮ\", \"ಮಂಗಳ\", \"ಬುಧ\", \"ಗುರು\", \"ಶುಕ್ರ\", \"ಶನಿ\"],\n  wide: [\n    \"ಭಾನುವಾರ\",\n    \"ಸೋಮವಾರ\",\n    \"ಮಂಗಳವಾರ\",\n    \"ಬುಧವಾರ\",\n    \"ಗುರುವಾರ\",\n    \"ಶುಕ್ರವಾರ\",\n    \"ಶನಿವಾರ\",\n  ],\n};\n\n// CLDR #1774 - #1815\nconst dayPeriodValues = {\n  narrow: {\n    am: \"ಪೂರ್ವಾಹ್ನ\",\n    pm: \"ಅಪರಾಹ್ನ\",\n    midnight: \"ಮಧ್ಯರಾತ್ರಿ\",\n    noon: \"ಮಧ್ಯಾಹ್ನ\",\n    morning: \"ಬೆಳಗ್ಗೆ\",\n    afternoon: \"ಮಧ್ಯಾಹ್ನ\",\n    evening: \"ಸಂಜೆ\",\n    night: \"ರಾತ್ರಿ\",\n  },\n  abbreviated: {\n    am: \"ಪೂರ್ವಾಹ್ನ\",\n    pm: \"ಅಪರಾಹ್ನ\",\n    midnight: \"ಮಧ್ಯರಾತ್ರಿ\",\n    noon: \"ಮಧ್ಯಾನ್ಹ\",\n    morning: \"ಬೆಳಗ್ಗೆ\",\n    afternoon: \"ಮಧ್ಯಾನ್ಹ\",\n    evening: \"ಸಂಜೆ\",\n    night: \"ರಾತ್ರಿ\",\n  },\n  wide: {\n    am: \"ಪೂರ್ವಾಹ್ನ\",\n    pm: \"ಅಪರಾಹ್ನ\",\n    midnight: \"ಮಧ್ಯರಾತ್ರಿ\",\n    noon: \"ಮಧ್ಯಾನ್ಹ\",\n    morning: \"ಬೆಳಗ್ಗೆ\",\n    afternoon: \"ಮಧ್ಯಾನ್ಹ\",\n    evening: \"ಸಂಜೆ\",\n    night: \"ರಾತ್ರಿ\",\n  },\n};\n\nconst formattingDayPeriodValues = {\n  narrow: {\n    am: \"ಪೂ\",\n    pm: \"ಅ\",\n    midnight: \"ಮಧ್ಯರಾತ್ರಿ\",\n    noon: \"ಮಧ್ಯಾನ್ಹ\",\n    morning: \"ಬೆಳಗ್ಗೆ\",\n    afternoon: \"ಮಧ್ಯಾನ್ಹ\",\n    evening: \"ಸಂಜೆ\",\n    night: \"ರಾತ್ರಿ\",\n  },\n  abbreviated: {\n    am: \"ಪೂರ್ವಾಹ್ನ\",\n    pm: \"ಅಪರಾಹ್ನ\",\n    midnight: \"ಮಧ್ಯ ರಾತ್ರಿ\",\n    noon: \"ಮಧ್ಯಾನ್ಹ\",\n    morning: \"ಬೆಳಗ್ಗೆ\",\n    afternoon: \"ಮಧ್ಯಾನ್ಹ\",\n    evening: \"ಸಂಜೆ\",\n    night: \"ರಾತ್ರಿ\",\n  },\n  wide: {\n    am: \"ಪೂರ್ವಾಹ್ನ\",\n    pm: \"ಅಪರಾಹ್ನ\",\n    midnight: \"ಮಧ್ಯ ರಾತ್ರಿ\",\n    noon: \"ಮಧ್ಯಾನ್ಹ\",\n    morning: \"ಬೆಳಗ್ಗೆ\",\n    afternoon: \"ಮಧ್ಯಾನ್ಹ\",\n    evening: \"ಸಂಜೆ\",\n    night: \"ರಾತ್ರಿ\",\n  },\n};\n\nconst ordinalNumber = (dirtyNumber, _options) => {\n  const number = Number(dirtyNumber);\n  return number + \"ನೇ\";\n};\n\nexport const localize = {\n  ordinalNumber,\n\n  era: buildLocalizeFn({\n    values: eraValues,\n    defaultWidth: \"wide\",\n  }),\n\n  quarter: buildLocalizeFn({\n    values: quarterValues,\n    defaultWidth: \"wide\",\n    argumentCallback: (quarter) => quarter - 1,\n  }),\n\n  month: buildLocalizeFn({\n    values: monthValues,\n    defaultWidth: \"wide\",\n  }),\n\n  day: buildLocalizeFn({\n    values: dayValues,\n    defaultWidth: \"wide\",\n  }),\n\n  dayPeriod: buildLocalizeFn({\n    values: dayPeriodValues,\n    defaultWidth: \"wide\",\n    formattingValues: formattingDayPeriodValues,\n    defaultFormattingWidth: \"wide\",\n  }),\n};\n"], "mappings": "AAAA,SAASA,eAAe,QAAQ,+BAA+B;;AAE/D;;AAEA,MAAMC,SAAS,GAAG;EAChBC,MAAM,EAAE,CAAC,SAAS,EAAE,QAAQ,CAAC;EAC7BC,WAAW,EAAE,CAAC,SAAS,EAAE,QAAQ,CAAC;EAAE;EACpCC,IAAI,EAAE,CAAC,eAAe,EAAE,YAAY,CAAC,CAAE;AACzC,CAAC;AAED,MAAMC,aAAa,GAAG;EACpBH,MAAM,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EAC5BC,WAAW,EAAE,CAAC,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,CAAC;EAAE;EACvDC,IAAI,EAAE,CAAC,eAAe,EAAE,eAAe,EAAE,eAAe,EAAE,eAAe;EACzE;AACF,CAAC;;AAED;AACA,MAAME,WAAW,GAAG;EAClBJ,MAAM,EAAE,CAAC,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,EAAE,IAAI,EAAE,GAAG,EAAE,GAAG,EAAE,IAAI,CAAC;EAE3EC,WAAW,EAAE,CACX,IAAI,EACJ,OAAO,EACP,QAAQ,EACR,OAAO,EACP,IAAI,EACJ,MAAM,EACN,MAAM,EACN,IAAI,EACJ,SAAS,EACT,OAAO,EACP,MAAM,EACN,OAAO,CACR;EAEDC,IAAI,EAAE,CACJ,OAAO,EACP,UAAU,EACV,QAAQ,EACR,SAAS,EACT,IAAI,EACJ,MAAM,EACN,MAAM,EACN,QAAQ,EACR,YAAY,EACZ,UAAU,EACV,SAAS,EACT,UAAU;AAEd,CAAC;;AAED;AACA,MAAMG,SAAS,GAAG;EAChBL,MAAM,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,CAAC;EACjDM,KAAK,EAAE,CAAC,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,OAAO,EAAE,KAAK,CAAC;EAC7DL,WAAW,EAAE,CAAC,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,OAAO,EAAE,KAAK,CAAC;EACnEC,IAAI,EAAE,CACJ,SAAS,EACT,QAAQ,EACR,SAAS,EACT,QAAQ,EACR,SAAS,EACT,UAAU,EACV,QAAQ;AAEZ,CAAC;;AAED;AACA,MAAMK,eAAe,GAAG;EACtBP,MAAM,EAAE;IACNQ,EAAE,EAAE,WAAW;IACfC,EAAE,EAAE,SAAS;IACbC,QAAQ,EAAE,YAAY;IACtBC,IAAI,EAAE,UAAU;IAChBC,OAAO,EAAE,SAAS;IAClBC,SAAS,EAAE,UAAU;IACrBC,OAAO,EAAE,MAAM;IACfC,KAAK,EAAE;EACT,CAAC;EACDd,WAAW,EAAE;IACXO,EAAE,EAAE,WAAW;IACfC,EAAE,EAAE,SAAS;IACbC,QAAQ,EAAE,YAAY;IACtBC,IAAI,EAAE,UAAU;IAChBC,OAAO,EAAE,SAAS;IAClBC,SAAS,EAAE,UAAU;IACrBC,OAAO,EAAE,MAAM;IACfC,KAAK,EAAE;EACT,CAAC;EACDb,IAAI,EAAE;IACJM,EAAE,EAAE,WAAW;IACfC,EAAE,EAAE,SAAS;IACbC,QAAQ,EAAE,YAAY;IACtBC,IAAI,EAAE,UAAU;IAChBC,OAAO,EAAE,SAAS;IAClBC,SAAS,EAAE,UAAU;IACrBC,OAAO,EAAE,MAAM;IACfC,KAAK,EAAE;EACT;AACF,CAAC;AAED,MAAMC,yBAAyB,GAAG;EAChChB,MAAM,EAAE;IACNQ,EAAE,EAAE,IAAI;IACRC,EAAE,EAAE,GAAG;IACPC,QAAQ,EAAE,YAAY;IACtBC,IAAI,EAAE,UAAU;IAChBC,OAAO,EAAE,SAAS;IAClBC,SAAS,EAAE,UAAU;IACrBC,OAAO,EAAE,MAAM;IACfC,KAAK,EAAE;EACT,CAAC;EACDd,WAAW,EAAE;IACXO,EAAE,EAAE,WAAW;IACfC,EAAE,EAAE,SAAS;IACbC,QAAQ,EAAE,aAAa;IACvBC,IAAI,EAAE,UAAU;IAChBC,OAAO,EAAE,SAAS;IAClBC,SAAS,EAAE,UAAU;IACrBC,OAAO,EAAE,MAAM;IACfC,KAAK,EAAE;EACT,CAAC;EACDb,IAAI,EAAE;IACJM,EAAE,EAAE,WAAW;IACfC,EAAE,EAAE,SAAS;IACbC,QAAQ,EAAE,aAAa;IACvBC,IAAI,EAAE,UAAU;IAChBC,OAAO,EAAE,SAAS;IAClBC,SAAS,EAAE,UAAU;IACrBC,OAAO,EAAE,MAAM;IACfC,KAAK,EAAE;EACT;AACF,CAAC;AAED,MAAME,aAAa,GAAGA,CAACC,WAAW,EAAEC,QAAQ,KAAK;EAC/C,MAAMC,MAAM,GAAGC,MAAM,CAACH,WAAW,CAAC;EAClC,OAAOE,MAAM,GAAG,IAAI;AACtB,CAAC;AAED,OAAO,MAAME,QAAQ,GAAG;EACtBL,aAAa;EAEbM,GAAG,EAAEzB,eAAe,CAAC;IACnB0B,MAAM,EAAEzB,SAAS;IACjB0B,YAAY,EAAE;EAChB,CAAC,CAAC;EAEFC,OAAO,EAAE5B,eAAe,CAAC;IACvB0B,MAAM,EAAErB,aAAa;IACrBsB,YAAY,EAAE,MAAM;IACpBE,gBAAgB,EAAGD,OAAO,IAAKA,OAAO,GAAG;EAC3C,CAAC,CAAC;EAEFE,KAAK,EAAE9B,eAAe,CAAC;IACrB0B,MAAM,EAAEpB,WAAW;IACnBqB,YAAY,EAAE;EAChB,CAAC,CAAC;EAEFI,GAAG,EAAE/B,eAAe,CAAC;IACnB0B,MAAM,EAAEnB,SAAS;IACjBoB,YAAY,EAAE;EAChB,CAAC,CAAC;EAEFK,SAAS,EAAEhC,eAAe,CAAC;IACzB0B,MAAM,EAAEjB,eAAe;IACvBkB,YAAY,EAAE,MAAM;IACpBM,gBAAgB,EAAEf,yBAAyB;IAC3CgB,sBAAsB,EAAE;EAC1B,CAAC;AACH,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
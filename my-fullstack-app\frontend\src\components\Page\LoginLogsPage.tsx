import React, { useState, useRef, useEffect } from 'react';
import { Button } from 'primereact/button';
import { Column } from 'primereact/column';
import { DataTable } from 'primereact/datatable';
import { Toast } from 'primereact/toast';
import { Card } from 'primereact/card';
import { ProgressSpinner } from 'primereact/progressspinner';
import { Tag } from 'primereact/tag';
import { InputText } from 'primereact/inputtext';
import { Dropdown } from 'primereact/dropdown';
import { Calendar } from 'primereact/calendar';
import { formatUtcToTaipei } from "../../utils/dateUtils";
import api from '../../services/api';
import { log } from '../../utils/logger';

interface LoginLog {
  id: number;
  username: string;
  createdAt: string;
  ipAddress: string;
  device: string;
  browser: string;
  status: string;
}

const LoginLogsPage: React.FC = () => {
  const toast = useRef<Toast>(null);
  const [logs, setLogs] = useState<LoginLog[]>([]);
  const [loading, setLoading] = useState(true);
  const [totalRecords, setTotalRecords] = useState(0);
  const [first, setFirst] = useState(0);
  const [rows, setRows] = useState(20);

  // 搜尋條件
  const [filters, setFilters] = useState({
    ipAddress: '',
    device: '',
    status: '',
    createdAtStart: null as Date | null,
    createdAtEnd: null as Date | null
  });

  const statusOptions = [
    { label: '全部', value: '' },
    { label: '登入成功', value: 'Login Success' },
    { label: '登出成功', value: 'Logout Success' },
    { label: '登入失敗', value: 'Login Failed' },
    { label: '登出失敗', value: 'Logout Failed' }
    
  ];

  // 載入登入紀錄
  const loadLogs = async (page = 1, pageSize = 20) => {
    try {
      setLoading(true);
      log.api('載入登入紀錄');

      const params: any = {
        page,
        pageSize
      };

      if (filters.ipAddress) params.ipAddress = filters.ipAddress;
      if (filters.device) params.device = filters.device;
      if (filters.status) params.status = filters.status;
      if (filters.createdAtStart) params.createdAtStart = filters.createdAtStart.toISOString();
      if (filters.createdAtEnd) params.createdAtEnd = filters.createdAtEnd.toISOString();

      const response = await api.get('/api/auth/GetUserLoginLog', { params });
      
      setLogs(response.data.data);
      setTotalRecords(response.data.totalCount);
      
      log.api('登入紀錄載入成功', { count: response.data.data.length });
      
    } catch (error: any) {
      log.error('載入登入紀錄失敗', error);
      toast.current?.show({
        severity: 'error',
        summary: '載入失敗',
        detail: '無法載入登入紀錄',
        life: 5000
      });
    } finally {
      setLoading(false);
    }
  };

  // 搜尋
  const handleSearch = () => {
    setFirst(0);
    loadLogs(1, rows);
  };

  // 重置搜尋
  const handleReset = () => {
    setFilters({
      ipAddress: '',
      device: '',
      status: '',
      createdAtStart: null,
      createdAtEnd: null
    });
    setFirst(0);
    loadLogs(1, rows);
  };

  // 分頁變更
  const onPageChange = (event: any) => {
    setFirst(event.first);
    setRows(event.rows);
    const page = Math.floor(event.first / event.rows) + 1;
    loadLogs(page, event.rows);
  };

  // 格式化日期
  const formatDate = (dateString: string): string => {
    if (!dateString) return '';
    try {
      return formatUtcToTaipei(dateString, 'yyyy/MM/dd HH:mm:ss');
    } catch (error) {
      console.error('Error formatting date:', error);
      return dateString;
    }
  };

  // 狀態標籤模板
  const statusBodyTemplate = (rowData: LoginLog) => {
    const severity = rowData.status.includes('Success') ? 'success' : 'danger';
    const labelfirst = rowData.status.includes('Login') ? '登入' : '登出';
    const labelsecond = severity === "success" ? '成功' : '失敗';
    return <Tag value={labelfirst + labelsecond} severity={severity} />;
  };

  // 日期模板
  const dateBodyTemplate = (rowData: LoginLog) => {
    return formatDate(rowData.createdAt);
  };

  // 分頁器左側
  const paginatorLeft = (
    <Button
      type="button"
      icon="pi pi-refresh"
      text
      onClick={() => loadLogs(Math.floor(first / rows) + 1, rows)}
      disabled={loading}
    />
  );

  const paginatorRight = <div></div>;

  useEffect(() => {
    loadLogs();
  }, []);

  if (loading && logs.length === 0) {
    return (
      <div className="flex justify-content-center align-items-center" style={{ height: '400px' }}>
        <ProgressSpinner />
      </div>
    );
  }

  return (
    <div>
      <Toast ref={toast} />
      
      <Card title="登入紀錄" className="mb-4">
        <p className="text-600 line-height-3 m-0">
          查看系統用戶的登入紀錄，包括成功和失敗的登入嘗試。可以根據 IP 位址、裝置、狀態和時間範圍進行篩選。
        </p>
      </Card>

      {/* 搜尋條件 */}
      <Card className="mb-4">
        <div className="grid">
          <div className="col-6 md:col-3">
            <InputText
              id="ipAddress"
              value={filters.ipAddress}
              onChange={(e) => setFilters({ ...filters, ipAddress: e.target.value })}
              placeholder="輸入 IP 位址"
              className="w-full"
            />
          </div>
          
          <div className="col-6 md:col-3">
            <InputText
              id="device"
              value={filters.device}
              onChange={(e) => setFilters({ ...filters, device: e.target.value })}
              placeholder="輸入裝置名稱"
              className="w-full"
            />
          </div>

          <div className="col-6 md:col-3">
            <Calendar
              value={filters.createdAtStart}
              onChange={(e) => setFilters({ ...filters, createdAtStart: e.value as Date })}
              placeholder="選擇開始日期"
              className="w-full"
              showIcon
              dateFormat="yy/mm/dd"
            />
          </div>
          
          <div className="col-6 md:col-3">
            <Calendar
              value={filters.createdAtEnd}
              onChange={(e) => setFilters({ ...filters, createdAtEnd: e.value as Date })}
              placeholder="選擇結束日期"
              className="w-full"
              showIcon
              dateFormat="yy/mm/dd"
            />
          </div>

          <div className="col-12 md:col-3">
            <Dropdown
              id="status"
              value={filters.status}
              options={statusOptions}
              onChange={(e) => setFilters({ ...filters, status: e.value })}
              placeholder="選擇狀態"
              className="w-full"
            />
          </div>

          <div className="col-12 md:col-4">
            <div className="flex gap-2">
              <Button
                label="搜尋"
                icon="pi pi-search"
                onClick={handleSearch}
              />
              <Button
                label="重置"
                icon="pi pi-refresh"
                onClick={handleReset}
                className="p-button-secondary"
              />
            </div>
          </div>
        </div>
      </Card>

      {/* 資料表 */}
      <Card>
        <DataTable
          value={logs}
          paginator
          lazy
          first={first}
          rows={rows}
          totalRecords={totalRecords}
          onPage={onPageChange}
          rowsPerPageOptions={[10, 20, 50]}
          emptyMessage="沒有找到登入紀錄"
          tableStyle={{ minWidth: '50rem' }}
          paginatorLeft={paginatorLeft}
          paginatorRight={paginatorRight}
          loading={loading}
        >
          <Column field="username" header="用戶名稱" sortable style={{ width: '15%' }} />
          <Column field="ipAddress" header="IP 位址" sortable style={{ width: '15%' }} />
          <Column field="device" header="裝置" style={{ width: '15%' }} />
          <Column field="browser" header="瀏覽器" style={{ width: '15%' }} />
          <Column field="status" header="狀態" body={statusBodyTemplate} style={{ width: '10%' }} />
          <Column field="createdAt" header="登入時間" body={dateBodyTemplate} sortable style={{ width: '20%' }} />
        </DataTable>
      </Card>
    </div>
  );
};

export default LoginLogsPage;

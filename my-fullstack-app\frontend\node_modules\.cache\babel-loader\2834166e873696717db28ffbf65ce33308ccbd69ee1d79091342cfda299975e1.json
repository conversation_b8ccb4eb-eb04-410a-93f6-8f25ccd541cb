{"ast": null, "code": "import _objectSpread from\"C:/Users/<USER>/Desktop/demo-git/demo-react/my-fullstack-app/frontend/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";import{Button}from\"primereact/button\";import{Calendar}from\"primereact/calendar\";import{Checkbox}from\"primereact/checkbox\";import{Column}from'primereact/column';import{ConfirmDialog,confirmDialog}from'primereact/confirmdialog';import{DataTable}from'primereact/datatable';import{Dropdown}from'primereact/dropdown';import{FileUpload}from\"primereact/fileupload\";import{Image}from'primereact/image';import{InputNumber}from'primereact/inputnumber';import{InputTextarea}from\"primereact/inputtextarea\";import{Stepper}from'primereact/stepper';import{StepperPanel}from'primereact/stepperpanel';import{Toast}from\"primereact/toast\";import React,{useEffect,useRef,useState}from\"react\";import{useLocation,useNavigate}from\"react-router-dom\";import useDataType from\"../../hooks/useDataType\";import api from\"../../services/api\";import imagepath from\"../../services/imagepath\";import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const TreatmentsDetailPage=()=>{var _location$state,_location$state2,_formData$patientId,_formData$orderNo;const location=useLocation();const patient=(_location$state=location.state)===null||_location$state===void 0?void 0:_location$state.patient;const treatment=(_location$state2=location.state)===null||_location$state2===void 0?void 0:_location$state2.treatment;const toast=useRef(null);const stepperRef=useRef(null);const[currentStep,setCurrentStep]=useState(0);const navigate=useNavigate();const{dataType,loading}=useDataType();const[formData,setFormData]=useState({orderNo:\"\",step:0,discomfortPeriod:\"\",possibleCauses:\"\",treatmentHistory:\"\",howToKnowOur:\"\",hospitalFormUrl:\"\",treatmentConsentFormUrl:\"\",subjective:\"\",objective:\"\",assessment:\"\",plan:\"\",patientId:(patient===null||patient===void 0?void 0:patient.id)||0,hospitalFormRecordDate:null,discomfortAreas:[]});// 新增不適區域的狀態\nconst[currentDiscomfortArea,setCurrentDiscomfortArea]=useState({frontAndBack:\"\",discomfortArea:\"\",discomfortSituation:\"\",discomfortDegree:0});useEffect(()=>{if(treatment){setFormData(_objectSpread(_objectSpread({},treatment),{},{hospitalFormRecordDate:treatment.hospitalFormRecordDate?new Date(treatment.hospitalFormRecordDate):null}));if(treatment.step===20){setCurrentStep(1);}if(treatment.step===30){setCurrentStep(2);}}},[treatment]);const handleChange=e=>{const{name,value}=e.target;setFormData(prev=>_objectSpread(_objectSpread({},prev),{},{[name]:value}));};const handleCheckboxChange=(name,value,checked)=>{setFormData(prev=>{const current=prev[name]||\"\";const currentArr=current?current.split(\",\"):[];const updatedArr=checked?currentArr.includes(value)?currentArr:[...currentArr,value]:currentArr.filter(item=>item!==value);return _objectSpread(_objectSpread({},prev),{},{[name]:updatedArr.join(\",\")});});};const handleDateChange=value=>{setFormData(prev=>_objectSpread(_objectSpread({},prev),{},{hospitalFormRecordDate:value||null}));};// 處理不適區域的函數\nconst handleDiscomfortAreaChange=(field,value)=>{setCurrentDiscomfortArea(prev=>_objectSpread(_objectSpread({},prev),{},{[field]:value}));};const addDiscomfortArea=()=>{var _toast$current3;if(!currentDiscomfortArea.frontAndBack||!currentDiscomfortArea.discomfortArea||!currentDiscomfortArea.discomfortSituation){var _toast$current;(_toast$current=toast.current)===null||_toast$current===void 0?void 0:_toast$current.show({severity:\"error\",summary:\"新增失敗\",detail:\"請填寫完整的不適區域資訊\"});return;}if(formData.discomfortAreas.length>=5){var _toast$current2;(_toast$current2=toast.current)===null||_toast$current2===void 0?void 0:_toast$current2.show({severity:\"error\",summary:\"新增失敗\",detail:\"不適區域最多只能新增 5 筆資料\"});return;}setFormData(prev=>_objectSpread(_objectSpread({},prev),{},{discomfortAreas:[...prev.discomfortAreas,_objectSpread({},currentDiscomfortArea)]}));// 清空當前輸入\nsetCurrentDiscomfortArea({frontAndBack:\"\",discomfortArea:\"\",discomfortSituation:\"\",discomfortDegree:0});(_toast$current3=toast.current)===null||_toast$current3===void 0?void 0:_toast$current3.show({severity:\"success\",summary:\"新增成功\",detail:\"不適區域已新增\"});};const removeDiscomfortArea=index=>{var _toast$current4;setFormData(prev=>_objectSpread(_objectSpread({},prev),{},{discomfortAreas:prev.discomfortAreas.filter((_,i)=>i!==index)}));(_toast$current4=toast.current)===null||_toast$current4===void 0?void 0:_toast$current4.show({severity:\"success\",summary:\"刪除成功\",detail:\"不適區域已刪除\"});};const copyLatestRecord=()=>{confirmDialog({message:'是否複製上一筆診療紀錄',header:'複製確認',icon:'pi pi-exclamation-triangle',accept:async()=>{try{const response=await api.get(\"/api/Treatment/GetLatestRecord/\".concat(patient===null||patient===void 0?void 0:patient.id),{method:'GET',headers:{'Content-Type':'application/json'}});if(response){var _toast$current5;const latestRecord=await response.data;setFormData(prev=>_objectSpread(_objectSpread({},prev),{},{discomfortPeriod:latestRecord.discomfortPeriod||\"\",possibleCauses:latestRecord.possibleCauses||\"\",treatmentHistory:latestRecord.treatmentHistory||\"\",howToKnowOur:latestRecord.howToKnowOur||\"\",subjective:latestRecord.subjective||\"\",objective:latestRecord.objective||\"\",assessment:latestRecord.assessment||\"\",plan:latestRecord.plan||\"\",discomfortAreas:latestRecord.discomfortAreas||[]}));(_toast$current5=toast.current)===null||_toast$current5===void 0?void 0:_toast$current5.show({severity:\"success\",summary:\"複製成功\",detail:\"已複製上一筆診療紀錄\"});}}catch(error){var _toast$current6;(_toast$current6=toast.current)===null||_toast$current6===void 0?void 0:_toast$current6.show({severity:\"error\",summary:\"複製失敗\",detail:error.details});}}});};const handleSubmit=async step=>{var detail=\"治療資料已更新\";if(step===40){if(formData.hospitalFormUrl===''&&formData.treatmentConsentFormUrl===''){var _toast$current7;(_toast$current7=toast.current)===null||_toast$current7===void 0?void 0:_toast$current7.show({severity:\"error\",summary:\"結案失敗\",detail:\"請上傳治療同意書或醫院診斷書\"});return;}if(formData.hospitalFormUrl!==''&&!formData.hospitalFormRecordDate){var _toast$current8;(_toast$current8=toast.current)===null||_toast$current8===void 0?void 0:_toast$current8.show({severity:\"error\",summary:\"結案失敗\",detail:\"請填寫醫院診斷書開立時間\"});return;}}if(formData.orderNo===\"\"){// 新增模式\nawait api.post(\"/api/treatment/Insert\",formData).then(res=>{var _toast$current9;setFormData(prev=>_objectSpread(_objectSpread({},prev),{},{orderNo:res.data.orderNo}));(_toast$current9=toast.current)===null||_toast$current9===void 0?void 0:_toast$current9.show({severity:\"success\",summary:\"成功\",detail:res.data.msg});}).catch(err=>{var _toast$current0;return(_toast$current0=toast.current)===null||_toast$current0===void 0?void 0:_toast$current0.show({severity:\"error\",summary:\"新增失敗\",detail:err.details});});}else{// 編輯模式\nformData.step=formData.step>step?formData.step:step;setFormData(prev=>_objectSpread(_objectSpread({},prev),{},{step:formData.step}));await api.put(\"/api/treatment/Update\",formData).then(()=>{var _toast$current1;return(_toast$current1=toast.current)===null||_toast$current1===void 0?void 0:_toast$current1.show({severity:\"success\",summary:\"成功\",detail:detail});}).catch(err=>{var _toast$current10;return(_toast$current10=toast.current)===null||_toast$current10===void 0?void 0:_toast$current10.show({severity:\"error\",summary:\"更新失敗\",detail:err.details});});}if(step===40){setTimeout(()=>navigate(\"/treatments\"),1500);// 送出後導回列表頁\n}};// 上傳\nconst handleCustomUpload=async(event,type)=>{var _event$files;const file=(_event$files=event.files)===null||_event$files===void 0?void 0:_event$files[0];if(!file)return;if(!formData.orderNo){var _toast$current11;(_toast$current11=toast.current)===null||_toast$current11===void 0?void 0:_toast$current11.show({severity:\"error\",summary:\"上傳失敗\",detail:\"請先儲存治療記錄，取得單號後再上傳檔案。\"});return;}const formDataToSend=new FormData();formDataToSend.append(\"file\",file);formDataToSend.append(\"orderNo\",formData.orderNo);try{var _toast$current12;const response=await api.post(\"/api/system/UploadFile\",formDataToSend,{headers:{\"Content-Type\":\"multipart/form-data\"}});const{fileName}=response.data;// 更新 formData\nconst updatedFormData=_objectSpread(_objectSpread({},formData),{},{[type==='hospital'?'hospitalFormUrl':'treatmentConsentFormUrl']:fileName});// 更新 UI\nsetFormData(updatedFormData);// 直接呼叫更新 API\nawait api.put(\"/api/treatment/Update\",updatedFormData);(_toast$current12=toast.current)===null||_toast$current12===void 0?void 0:_toast$current12.show({severity:\"success\",summary:\"成功\",detail:\"檔案已上傳並更新記錄。\"});}catch(error){var _error$response,_error$response$data,_toast$current13;const errorMessage=((_error$response=error.response)===null||_error$response===void 0?void 0:(_error$response$data=_error$response.data)===null||_error$response$data===void 0?void 0:_error$response$data.message)||error.message||\"檔案上傳失敗\";(_toast$current13=toast.current)===null||_toast$current13===void 0?void 0:_toast$current13.show({severity:\"error\",summary:\"上傳失敗\",detail:errorMessage});}};const getOptions=groupId=>{var _dataType$find;return((_dataType$find=dataType.find(group=>group.groupId===groupId))===null||_dataType$find===void 0?void 0:_dataType$find.dataTypes.map(item=>({label:item.name,value:item.name})))||[];};const checkStep=step=>{if(step===20&&formData.orderNo===\"\"){var _toast$current14;(_toast$current14=toast.current)===null||_toast$current14===void 0?void 0:_toast$current14.show({severity:\"error\",summary:\"錯誤\",detail:\"初次開案請存檔\"});}else{var _stepperRef$current;(_stepperRef$current=stepperRef.current)===null||_stepperRef$current===void 0?void 0:_stepperRef$current.nextCallback();}};if(loading)return/*#__PURE__*/_jsx(\"p\",{children:\"Loading...\"});return/*#__PURE__*/_jsxs(\"div\",{className:\"p-4\",children:[/*#__PURE__*/_jsx(Toast,{ref:toast}),/*#__PURE__*/_jsx(ConfirmDialog,{}),/*#__PURE__*/_jsx(\"div\",{className:\"card flex justify-content-center\",children:/*#__PURE__*/_jsxs(Stepper,{ref:stepperRef,activeStep:currentStep,style:{flexBasis:'100%'},children:[/*#__PURE__*/_jsxs(StepperPanel,{header:\"\\u75C7\\u72C0\\u63CF\\u8FF0\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"flex flex-column\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"grid formgrid p-fluid gap-3 justify-content-center\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"col-5 md:col-5\",hidden:true,children:[/*#__PURE__*/_jsx(\"label\",{children:\"patientId\"}),/*#__PURE__*/_jsx(\"div\",{className:\"flex flex-wrap gap-3\",children:/*#__PURE__*/_jsx(InputTextarea,{name:\"patientId\",rows:1,value:(_formData$patientId=formData.patientId)===null||_formData$patientId===void 0?void 0:_formData$patientId.toString(),onChange:handleChange})})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"col-5 md:col-5\",hidden:true,children:[/*#__PURE__*/_jsx(\"label\",{children:\"orderNo\"}),/*#__PURE__*/_jsx(\"div\",{className:\"flex flex-wrap gap-3\",children:/*#__PURE__*/_jsx(InputTextarea,{name:\"orderNo\",rows:1,value:(_formData$orderNo=formData.orderNo)===null||_formData$orderNo===void 0?void 0:_formData$orderNo.toString(),onChange:handleChange})})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"col-12 md:col-6\",children:[/*#__PURE__*/_jsx(\"h4\",{children:\"\\u65B0\\u589E\\u4E0D\\u9069\\u5340\\u57DF\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"grid formgrid p-fluid\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"col-12 flex flex-wrap justify-content-between\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"col-12 md:col-4 flex justify-content-center\",children:/*#__PURE__*/_jsx(Image,{src:\"/images/image-body.jpg\",alt:\"Image\",width:\"250\"})}),/*#__PURE__*/_jsx(\"div\",{className:\"col-12 md:col-8 flex justify-content-center align-items-center\",children:/*#__PURE__*/_jsx(Image,{src:\"/images/NumericalRaringAcale.png\",alt:\"Image\",imageStyle:{width:\"100%\",maxWidth:\"550px\",height:\"auto\"}})})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"col-12 flex flex-wrap\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"col-6 md:col-4\",children:[/*#__PURE__*/_jsx(\"label\",{className:\"font-bold block pt-2 mb-2\",children:\"\\u524D\\u5074/\\u5F8C\\u5074\"}),/*#__PURE__*/_jsx(Dropdown,{value:currentDiscomfortArea.frontAndBack,onChange:e=>handleDiscomfortAreaChange('frontAndBack',e.value),options:getOptions(1),optionLabel:\"label\",optionValue:\"value\",placeholder:\"\\u9078\\u64C7\\u524D\\u5074/\\u5F8C\\u5074\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"col-6 md:col-4\",children:[/*#__PURE__*/_jsx(\"label\",{className:\"font-bold block pt-2 mb-2\",children:\"\\u4E0D\\u9069\\u5340\\u57DF\"}),/*#__PURE__*/_jsx(Dropdown,{value:currentDiscomfortArea.discomfortArea,onChange:e=>handleDiscomfortAreaChange('discomfortArea',e.value),options:getOptions(2),optionLabel:\"label\",optionValue:\"value\",placeholder:\"\\u9078\\u64C7\\u4E0D\\u9069\\u5340\\u57DF\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"col-6 md:col-4\",children:[/*#__PURE__*/_jsx(\"label\",{className:\"font-bold block pt-2 mb-2\",children:\"\\u4E0D\\u9069\\u60C5\\u6CC1\"}),/*#__PURE__*/_jsx(Dropdown,{value:currentDiscomfortArea.discomfortSituation,onChange:e=>handleDiscomfortAreaChange('discomfortSituation',e.value),options:getOptions(3),optionLabel:\"label\",optionValue:\"value\",placeholder:\"\\u9078\\u64C7\\u4E0D\\u9069\\u60C5\\u6CC1\"})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"col-12 flex\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"col-6 md:col-4\",children:[/*#__PURE__*/_jsx(\"label\",{className:\"font-bold block pt-2 mb-2\",children:\"\\u75BC\\u75DB\\u6307\\u6578\"}),/*#__PURE__*/_jsx(InputNumber,{value:currentDiscomfortArea.discomfortDegree,onValueChange:e=>handleDiscomfortAreaChange('discomfortDegree',e.value||0),min:0,max:10,placeholder:\"0-10\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"col-4 md:col-2\",children:[/*#__PURE__*/_jsx(\"label\",{className:\"font-bold block pt-2 mb-2\",children:\"\\xA0\"}),/*#__PURE__*/_jsx(Button,{label:\"\\u65B0\\u589E\",icon:\"pi pi-plus\",onClick:addDiscomfortArea,disabled:formData.discomfortAreas.length>=5})]})]})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"col-12 md:col-5\",children:[/*#__PURE__*/_jsxs(\"h4\",{children:[\"\\u4E0D\\u9069\\u5340\\u57DF\\u5217\\u8868 (\",formData.discomfortAreas.length,\"/5)\"]}),/*#__PURE__*/_jsxs(DataTable,{value:formData.discomfortAreas,emptyMessage:\"\\u5C1A\\u7121\\u4E0D\\u9069\\u5340\\u57DF\\u8CC7\\u6599\",children:[/*#__PURE__*/_jsx(Column,{field:\"frontAndBack\",header:\"\\u524D\\u5074/\\u5F8C\\u5074\"}),/*#__PURE__*/_jsx(Column,{field:\"discomfortArea\",header:\"\\u4E0D\\u9069\\u5340\\u57DF\"}),/*#__PURE__*/_jsx(Column,{field:\"discomfortSituation\",header:\"\\u4E0D\\u9069\\u60C5\\u6CC1\"}),/*#__PURE__*/_jsx(Column,{field:\"discomfortDegree\",header:\"\\u75BC\\u75DB\\u6307\\u6578\"}),/*#__PURE__*/_jsx(Column,{header:\"\\u64CD\\u4F5C\",body:(_,options)=>/*#__PURE__*/_jsx(Button,{icon:\"pi pi-trash\",className:\"p-button-rounded p-button-danger p-button-text\",onClick:()=>removeDiscomfortArea(options.rowIndex)})})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"col-12 md:col-6\",children:[/*#__PURE__*/_jsx(\"label\",{className:\"font-bold block mb-2\",children:\"\\u53EF\\u80FD\\u5F15\\u767C\\u539F\\u56E0(\\u53EF\\u8907\\u9078)\"}),/*#__PURE__*/_jsx(\"div\",{className:\"flex flex-wrap gap-3 pb-2\",children:getOptions(5).map(option=>/*#__PURE__*/_jsxs(\"div\",{className:\"flex align-items-center\",children:[/*#__PURE__*/_jsx(Checkbox,{inputId:\"front-\".concat(option.value),value:option.value,onChange:e=>{var _e$checked;return handleCheckboxChange(\"possibleCauses\",option.value,(_e$checked=e.checked)!==null&&_e$checked!==void 0?_e$checked:false);},checked:formData.possibleCauses.split(\",\").includes(option.value)}),/*#__PURE__*/_jsx(\"label\",{htmlFor:\"front-\".concat(option.value),className:\"ml-2\",children:option.label})]},option.value))}),/*#__PURE__*/_jsx(InputTextarea,{name:\"possibleCauses\",rows:1,value:formData.possibleCauses,onChange:handleChange})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"col-12 md:col-5\",children:[/*#__PURE__*/_jsx(\"label\",{className:\"font-bold block mb-2\",children:\"\\u4E0D\\u9069\\u6642\\u9593\"}),/*#__PURE__*/_jsx(\"div\",{className:\"flex flex-wrap gap-3 pb-2\",children:getOptions(4).map(option=>/*#__PURE__*/_jsxs(\"div\",{className:\"flex align-items-center\",children:[/*#__PURE__*/_jsx(Checkbox,{inputId:\"front-\".concat(option.value),value:option.value,onChange:()=>setFormData(prev=>_objectSpread(_objectSpread({},prev),{},{discomfortPeriod:option.value})),checked:formData.discomfortPeriod===option.value}),/*#__PURE__*/_jsx(\"label\",{htmlFor:\"front-\".concat(option.value),className:\"ml-2\",children:option.label})]},option.value))})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"col-12 md:col-6\",children:[/*#__PURE__*/_jsx(\"label\",{className:\"font-bold block mb-2\",children:\"\\u66FE\\u63A5\\u53D7\\u904E\\u76F8\\u95DC\\u8655\\u7F6E\"}),/*#__PURE__*/_jsx(\"div\",{className:\"flex flex-wrap gap-3 pb-2\",children:getOptions(6).map(option=>/*#__PURE__*/_jsxs(\"div\",{className:\"flex align-items-center\",children:[/*#__PURE__*/_jsx(Checkbox,{inputId:\"front-\".concat(option.value),value:option.value,onChange:e=>{var _e$checked2;return handleCheckboxChange(\"treatmentHistory\",option.value,(_e$checked2=e.checked)!==null&&_e$checked2!==void 0?_e$checked2:false);},checked:formData.treatmentHistory.split(\",\").includes(option.value)}),/*#__PURE__*/_jsx(\"label\",{htmlFor:\"front-\".concat(option.value),className:\"ml-2\",children:option.label})]},option.value))}),/*#__PURE__*/_jsx(InputTextarea,{name:\"treatmentHistory\",rows:1,value:formData.treatmentHistory,onChange:handleChange})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"col-12 md:col-5\",children:[/*#__PURE__*/_jsx(\"label\",{className:\"font-bold block mb-2\",children:\"\\u5982\\u4F55\\u77E5\\u9053\\u6211\\u5011\\u9662\\u6240\"}),/*#__PURE__*/_jsx(\"div\",{className:\"flex flex-wrap gap-3 pb-2\",children:getOptions(7).map(option=>/*#__PURE__*/_jsxs(\"div\",{className:\"flex align-items-center\",children:[/*#__PURE__*/_jsx(Checkbox,{inputId:\"front-\".concat(option.value),value:option.value,onChange:()=>setFormData(prev=>_objectSpread(_objectSpread({},prev),{},{howToKnowOur:option.value})),checked:formData.howToKnowOur===option.value}),/*#__PURE__*/_jsx(\"label\",{htmlFor:\"front-\".concat(option.value),className:\"ml-2\",children:option.label})]},option.value))}),/*#__PURE__*/_jsx(InputTextarea,{name:\"howToKnowOur\",rows:1,value:formData.howToKnowOur,onChange:handleChange})]})]})}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex pt-4 justify-content-between\",children:[/*#__PURE__*/_jsx(Button,{label:\"\\u8907\\u88FD\",severity:\"info\",icon:\"pi pi-copy\",iconPos:\"left\",onClick:copyLatestRecord}),/*#__PURE__*/_jsx(\"div\",{className:\"flex gap-2\",children:/*#__PURE__*/_jsx(Button,{label:\"\\u5B58\\u6A94\",severity:\"success\",icon:\"pi pi-upload\",onClick:()=>handleSubmit(10)})}),/*#__PURE__*/_jsx(Button,{label:\"\\u4E0B\\u4E00\\u6B65\",icon:\"pi pi-arrow-right\",iconPos:\"right\",onClick:()=>checkStep(20)})]})]}),/*#__PURE__*/_jsxs(StepperPanel,{header:\"\\u6CBB\\u7642\\u5E2B\\u8A3A\\u7642\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"flex flex-column\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"grid formgrid p-fluid gap-3 justify-content-center\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"col-12 md:col-5\",children:[/*#__PURE__*/_jsx(\"label\",{className:\"font-bold block mb-2\",children:\"\\u4E3B\\u89C0\\u75C7\\u72C0 (S)\"}),/*#__PURE__*/_jsx(InputTextarea,{name:\"subjective\",rows:6,value:formData.subjective,onChange:handleChange})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"col-12 md:col-5\",children:[/*#__PURE__*/_jsx(\"label\",{className:\"font-bold block mb-2\",children:\"\\u5BA2\\u89C0\\u6AA2\\u67E5 (O)\"}),/*#__PURE__*/_jsx(InputTextarea,{name:\"objective\",rows:6,value:formData.objective,onChange:handleChange})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"col-12 md:col-5\",children:[/*#__PURE__*/_jsx(\"label\",{className:\"font-bold block mb-2\",children:\"\\u5C08\\u696D\\u5224\\u65B7 (A)\"}),/*#__PURE__*/_jsx(InputTextarea,{name:\"assessment\",rows:6,value:formData.assessment,onChange:handleChange})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"col-12 md:col-5\",children:[/*#__PURE__*/_jsx(\"label\",{className:\"font-bold block mb-2\",children:\"\\u6CBB\\u7642\\u8A08\\u756B (P)\"}),/*#__PURE__*/_jsx(InputTextarea,{name:\"plan\",rows:6,value:formData.plan,onChange:handleChange})]})]})}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex pt-4 justify-content-between\",children:[/*#__PURE__*/_jsx(Button,{label:\"\\u4E0A\\u4E00\\u6B65\",severity:\"secondary\",icon:\"pi pi-arrow-left\",onClick:()=>{var _stepperRef$current2;return(_stepperRef$current2=stepperRef.current)===null||_stepperRef$current2===void 0?void 0:_stepperRef$current2.prevCallback();}}),/*#__PURE__*/_jsx(Button,{label:\"\\u5B58\\u6A94\",severity:\"success\",icon:\"pi pi-upload\",onClick:()=>handleSubmit(20)}),/*#__PURE__*/_jsx(Button,{label:\"\\u4E0B\\u4E00\\u6B65\",icon:\"pi pi-arrow-right\",iconPos:\"right\",onClick:()=>checkStep(30)})]})]}),/*#__PURE__*/_jsxs(StepperPanel,{header:\"\\u6A94\\u6848\\u4E0A\\u50B3\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-column \",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"grid formgrid p-fluid gap-3 justify-content-center\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"col-12 md:col-5\",children:[/*#__PURE__*/_jsx(\"label\",{className:\"font-bold block mb-2\",children:\"\\u6CBB\\u7642\\u540C\\u610F\\u66F8\"}),/*#__PURE__*/_jsx(\"div\",{className:\"flex flex-column h-15rem\",children:/*#__PURE__*/_jsx(\"div\",{className:\"border-2 border-dashed surface-border border-round surface-ground flex-auto flex justify-content-center align-items-center font-medium\",children:formData.treatmentConsentFormUrl?/*#__PURE__*/_jsx(Image,{src:imagepath+formData.treatmentConsentFormUrl,indicatorIcon:/*#__PURE__*/_jsx(\"i\",{className:\"pi pi-search\"}),alt:\"Image\",width:\"100%\",height:\"230rem\",preview:true}):/*#__PURE__*/_jsx(FileUpload,{mode:\"basic\",name:\"TreatmentConsentFormUrl\",customUpload:true,uploadHandler:e=>handleCustomUpload(e,'consent'),accept:\"image/*\",maxFileSize:1000000,chooseLabel:\"\\u9078\\u64C7\\u6A94\\u6848\"})})})]}),/*#__PURE__*/_jsx(\"div\",{}),/*#__PURE__*/_jsxs(\"div\",{className:\"col-12 md:col-5\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"col-12 md:col-5\"}),/*#__PURE__*/_jsx(\"label\",{className:\"font-bold block mb-2\",children:\"\\u91AB\\u9662\\u8A3A\\u65B7\\u66F8\"}),/*#__PURE__*/_jsx(\"div\",{className:\"flex flex-column h-15rem\",children:/*#__PURE__*/_jsx(\"div\",{className:\"border-2 border-dashed surface-border border-round surface-ground flex-auto flex justify-content-center align-items-center font-medium\",children:formData.hospitalFormUrl?/*#__PURE__*/_jsx(Image,{src:imagepath+formData.hospitalFormUrl,indicatorIcon:/*#__PURE__*/_jsx(\"i\",{className:\"pi pi-search\"}),alt:\"Image\",width:\"100%\",height:\"230rem\",preview:true}):/*#__PURE__*/_jsx(FileUpload,{mode:\"basic\",name:\"HospitalFormUrl\",customUpload:true,uploadHandler:e=>handleCustomUpload(e,'hospital'),accept:\"image/*\",maxFileSize:1000000,chooseLabel:\"\\u9078\\u64C7\\u6A94\\u6848\"})})})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"grid formgrid p-fluid gap-3 pt-2 justify-content-center\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"col-12 md:col-5\"}),/*#__PURE__*/_jsx(\"div\",{className:\"col-12 md:col-5 flex justify-content-end\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"col-10 md:col-4\",children:[/*#__PURE__*/_jsx(\"label\",{className:\"font-bold block mb-2\",children:\"\\u91AB\\u9662\\u8A3A\\u65B7\\u66F8\\u958B\\u7ACB\\u6642\\u9593\"}),/*#__PURE__*/_jsx(Calendar,{value:formData.hospitalFormRecordDate,onChange:e=>handleDateChange(e.value),showIcon:true,dateFormat:\"yy/mm/dd\",placeholder:\"\\u9078\\u64C7\\u65E5\\u671F\"})]})})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex pt-4 justify-content-between\",children:[/*#__PURE__*/_jsx(Button,{label:\"\\u4E0A\\u4E00\\u6B65\",severity:\"secondary\",icon:\"pi pi-arrow-left\",onClick:()=>{var _stepperRef$current3;return(_stepperRef$current3=stepperRef.current)===null||_stepperRef$current3===void 0?void 0:_stepperRef$current3.prevCallback();}}),/*#__PURE__*/_jsx(Button,{label:\"\\u7D50\\u6848\",severity:\"success\",icon:\"pi pi-check\",iconPos:\"right\",onClick:()=>handleSubmit(40)})]})]})]})})]});};export default TreatmentsDetailPage;", "map": {"version": 3, "names": ["<PERSON><PERSON>", "Calendar", "Checkbox", "Column", "ConfirmDialog", "confirmDialog", "DataTable", "Dropdown", "FileUpload", "Image", "InputNumber", "InputTextarea", "Stepper", "StepperPanel", "Toast", "React", "useEffect", "useRef", "useState", "useLocation", "useNavigate", "useDataType", "api", "imagepath", "jsx", "_jsx", "jsxs", "_jsxs", "TreatmentsDetailPage", "_location$state", "_location$state2", "_formData$patientId", "_formData$orderNo", "location", "patient", "state", "treatment", "toast", "stepper<PERSON><PERSON>", "currentStep", "setCurrentStep", "navigate", "dataType", "loading", "formData", "setFormData", "orderNo", "step", "discomfort<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "treatmentHistory", "howToKnowOur", "hospitalFormUrl", "treatmentConsentFormUrl", "subjective", "objective", "assessment", "plan", "patientId", "id", "hospitalFormRecordDate", "<PERSON><PERSON><PERSON><PERSON>", "currentDiscomfortArea", "setCurrentDiscomfortArea", "frontAndBack", "discomfortArea", "discomfortSituation", "discomfortDegree", "_objectSpread", "Date", "handleChange", "e", "name", "value", "target", "prev", "handleCheckboxChange", "checked", "current", "currentArr", "split", "updatedArr", "includes", "filter", "item", "join", "handleDateChange", "handleDiscomfortAreaChange", "field", "addDiscomfortArea", "_toast$current3", "_toast$current", "show", "severity", "summary", "detail", "length", "_toast$current2", "removeDiscomfortArea", "index", "_toast$current4", "_", "i", "copyLatestRecord", "message", "header", "icon", "accept", "response", "get", "concat", "method", "headers", "_toast$current5", "latestRecord", "data", "error", "_toast$current6", "details", "handleSubmit", "_toast$current7", "_toast$current8", "post", "then", "res", "_toast$current9", "msg", "catch", "err", "_toast$current0", "put", "_toast$current1", "_toast$current10", "setTimeout", "handleCustomUpload", "event", "type", "_event$files", "file", "files", "_toast$current11", "formDataToSend", "FormData", "append", "_toast$current12", "fileName", "updatedFormData", "_error$response", "_error$response$data", "_toast$current13", "errorMessage", "getOptions", "groupId", "_dataType$find", "find", "group", "dataTypes", "map", "label", "checkStep", "_toast$current14", "_stepperRef$current", "nextCallback", "children", "className", "ref", "activeStep", "style", "flexBasis", "hidden", "rows", "toString", "onChange", "src", "alt", "width", "imageStyle", "max<PERSON><PERSON><PERSON>", "height", "options", "optionLabel", "optionValue", "placeholder", "onValueChange", "min", "max", "onClick", "disabled", "emptyMessage", "body", "rowIndex", "option", "inputId", "_e$checked", "htmlFor", "_e$checked2", "iconPos", "_stepperRef$current2", "prevCallback", "indicatorIcon", "preview", "mode", "customUpload", "uploadHandler", "maxFileSize", "<PERSON><PERSON><PERSON><PERSON>", "showIcon", "dateFormat", "_stepperRef$current3"], "sources": ["C:/Users/<USER>/Desktop/demo-git/demo-react/my-fullstack-app/frontend/src/components/Page/TreatmentsDetailPage.tsx"], "sourcesContent": ["import { <PERSON><PERSON> } from \"primereact/button\";\r\nimport { Calendar } from \"primereact/calendar\";\r\nimport { Checkbox } from \"primereact/checkbox\";\r\nimport { Column } from 'primereact/column';\r\nimport { ConfirmDialog, confirmDialog } from 'primereact/confirmdialog';\r\nimport { DataTable } from 'primereact/datatable';\r\nimport { Dropdown } from 'primereact/dropdown';\r\nimport { FileUpload, FileUploadHandlerEvent } from \"primereact/fileupload\";\r\nimport { Image } from 'primereact/image';\r\nimport { InputNumber } from 'primereact/inputnumber';\r\nimport { InputTextarea } from \"primereact/inputtextarea\";\r\nimport { Stepper } from 'primereact/stepper';\r\nimport { StepperPanel } from 'primereact/stepperpanel';\r\nimport { Toast } from \"primereact/toast\";\r\nimport React, { useEffect, useRef, useState } from \"react\";\r\nimport { useLocation, useNavigate } from \"react-router-dom\";\r\nimport useDataType from \"../../hooks/useDataType\";\r\nimport api from \"../../services/api\";\r\nimport imagepath from \"../../services/imagepath\";\r\n\r\ninterface DiscomfortArea {\r\n  id?: number;\r\n  frontAndBack: string;\r\n  discomfortArea: string;\r\n  discomfortSituation: string;\r\n  discomfortDegree: number;\r\n}\r\n\r\ninterface Treatment {\r\n  orderNo: string;\r\n  step: number;\r\n  discomfortPeriod: string;\r\n  possibleCauses: string;\r\n  treatmentHistory: string;\r\n  howToKnowOur: string;\r\n  hospitalFormUrl: string;\r\n  treatmentConsentFormUrl: string;\r\n  subjective: string;\r\n  objective: string;\r\n  assessment: string;\r\n  plan: string;\r\n  patientId: number;\r\n  hospitalFormRecordDate?: Date | null;\r\n  discomfortAreas: DiscomfortArea[];\r\n}\r\n\r\nconst TreatmentsDetailPage: React.FC = () => {\r\n  const location = useLocation();\r\n  const patient = location.state?.patient;\r\n  const treatment = location.state?.treatment;\r\n  const toast = useRef<Toast>(null);\r\n  const stepperRef = useRef<Stepper>(null);\r\n  const [currentStep, setCurrentStep] = useState(0);\r\n  const navigate = useNavigate();\r\n  const { dataType, loading } = useDataType();\r\n  const [formData, setFormData] = useState<Treatment>({\r\n    orderNo: \"\",\r\n    step: 0,\r\n    discomfortPeriod: \"\",\r\n    possibleCauses: \"\",\r\n    treatmentHistory: \"\",\r\n    howToKnowOur: \"\",\r\n    hospitalFormUrl: \"\",\r\n    treatmentConsentFormUrl: \"\",\r\n    subjective: \"\",\r\n    objective: \"\",\r\n    assessment: \"\",\r\n    plan: \"\",\r\n    patientId: patient?.id || 0,\r\n    hospitalFormRecordDate: null,\r\n    discomfortAreas: []\r\n  });\r\n\r\n  // 新增不適區域的狀態\r\n  const [currentDiscomfortArea, setCurrentDiscomfortArea] = useState<DiscomfortArea>({\r\n    frontAndBack: \"\",\r\n    discomfortArea: \"\",\r\n    discomfortSituation: \"\",\r\n    discomfortDegree: 0\r\n  });\r\n\r\n  useEffect(() => {\r\n      if (treatment) {\r\n          setFormData({\r\n            ...treatment,\r\n            hospitalFormRecordDate: treatment.hospitalFormRecordDate ? new Date(treatment.hospitalFormRecordDate) : null,\r\n            });\r\n\r\n          if(treatment.step === 20){\r\n            setCurrentStep(1)\r\n          }\r\n          if(treatment.step === 30){\r\n            setCurrentStep(2)\r\n          }\r\n      }\r\n    }, [treatment]);\r\n\r\n  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {\r\n          const { name, value } = e.target;\r\n          setFormData((prev) => ({ ...prev, [name]: value }));\r\n    };\r\n\r\n  const handleCheckboxChange = (name: keyof Treatment, value: string, checked: boolean) => {\r\n    setFormData(prev => {\r\n      const current = (prev[name] || \"\") as string;\r\n      const currentArr = current ? current.split(\",\") : [];\r\n\r\n      const updatedArr = checked\r\n        ? currentArr.includes(value)\r\n          ? currentArr\r\n          : [...currentArr, value]\r\n        : currentArr.filter(item => item !== value);\r\n\r\n    return { ...prev, [name]: updatedArr.join(\",\") };\r\n    });\r\n  };\r\n\r\n  const handleDateChange = (value: Date | null | undefined) => {\r\n    setFormData(prev => ({ ...prev, hospitalFormRecordDate: value || null }));\r\n  };\r\n\r\n  // 處理不適區域的函數\r\n  const handleDiscomfortAreaChange = (field: keyof DiscomfortArea, value: any) => {\r\n    setCurrentDiscomfortArea(prev => ({ ...prev, [field]: value }));\r\n  };\r\n\r\n  const addDiscomfortArea = () => {\r\n    if (!currentDiscomfortArea.frontAndBack || !currentDiscomfortArea.discomfortArea || !currentDiscomfortArea.discomfortSituation) {\r\n      toast.current?.show({ severity: \"error\", summary: \"新增失敗\", detail: \"請填寫完整的不適區域資訊\" });\r\n      return;\r\n    }\r\n\r\n    if (formData.discomfortAreas.length >= 5) {\r\n      toast.current?.show({ severity: \"error\", summary: \"新增失敗\", detail: \"不適區域最多只能新增 5 筆資料\" });\r\n      return;\r\n    }\r\n\r\n    setFormData(prev => ({\r\n      ...prev,\r\n      discomfortAreas: [...prev.discomfortAreas, { ...currentDiscomfortArea }]\r\n    }));\r\n\r\n    // 清空當前輸入\r\n    setCurrentDiscomfortArea({\r\n      frontAndBack: \"\",\r\n      discomfortArea: \"\",\r\n      discomfortSituation: \"\",\r\n      discomfortDegree: 0\r\n    });\r\n\r\n    toast.current?.show({ severity: \"success\", summary: \"新增成功\", detail: \"不適區域已新增\" });\r\n  };\r\n\r\n  const removeDiscomfortArea = (index: number) => {\r\n    setFormData(prev => ({\r\n      ...prev,\r\n      discomfortAreas: prev.discomfortAreas.filter((_, i) => i !== index)\r\n    }));\r\n    toast.current?.show({ severity: \"success\", summary: \"刪除成功\", detail: \"不適區域已刪除\" });\r\n  };\r\n\r\n  const copyLatestRecord = () => {\r\n    \r\n    confirmDialog({\r\n      message: '是否複製上一筆診療紀錄',\r\n      header: '複製確認',\r\n      icon: 'pi pi-exclamation-triangle',\r\n      accept: async () => {\r\n        try {\r\n          const response = await api.get(`/api/Treatment/GetLatestRecord/${patient?.id}`, {\r\n            method: 'GET',\r\n            headers: {\r\n              'Content-Type': 'application/json',\r\n            },\r\n          });\r\n\r\n          if (response) {\r\n            const latestRecord = await response.data;\r\n            setFormData(prev => ({\r\n              ...prev,\r\n              discomfortPeriod: latestRecord.discomfortPeriod || \"\",\r\n              possibleCauses: latestRecord.possibleCauses || \"\",\r\n              treatmentHistory: latestRecord.treatmentHistory || \"\",\r\n              howToKnowOur: latestRecord.howToKnowOur || \"\",\r\n              subjective: latestRecord.subjective || \"\",\r\n              objective: latestRecord.objective || \"\",\r\n              assessment: latestRecord.assessment || \"\",\r\n              plan: latestRecord.plan || \"\",\r\n              discomfortAreas: latestRecord.discomfortAreas || []\r\n            }));\r\n            toast.current?.show({ severity: \"success\", summary: \"複製成功\", detail: \"已複製上一筆診療紀錄\" });\r\n            \r\n          } \r\n        } catch (error:any) {\r\n          toast.current?.show({ severity: \"error\", summary: \"複製失敗\", detail: error.details });\r\n        }\r\n      }\r\n    });\r\n  };\r\n\r\n  const handleSubmit = async (step:number) => {\r\n\r\n    var detail = \"治療資料已更新\";\r\n\r\n    if(step === 40){\r\n      if(formData.hospitalFormUrl === '' && formData.treatmentConsentFormUrl === ''){\r\n        toast.current?.show({ severity: \"error\", summary: \"結案失敗\", detail: \"請上傳治療同意書或醫院診斷書\"})\r\n        return\r\n      }\r\n      if(formData.hospitalFormUrl !== '' && !formData.hospitalFormRecordDate){\r\n        toast.current?.show({ severity: \"error\", summary: \"結案失敗\", detail: \"請填寫醫院診斷書開立時間\"})\r\n        return\r\n      }\r\n    }\r\n\r\n    if (formData.orderNo === \"\") {\r\n        // 新增模式\r\n        await api.post(\"/api/treatment/Insert\", formData)\r\n        .then((res) => {\r\n          setFormData(\r\n            (prev) => ({...prev,orderNo: res.data.orderNo})\r\n          ); \r\n          toast.current?.show({ severity: \"success\", summary: \"成功\", detail: res.data.msg }) } )\r\n        .catch((err) => toast.current?.show({ severity: \"error\", summary: \"新增失敗\", detail: err.details}) );     \r\n        \r\n    }\r\n    else{\r\n      // 編輯模式\r\n      formData.step = formData.step > step ? formData.step : step;\r\n      setFormData(\r\n            (prev) => ({...prev,step: formData.step})\r\n          ); \r\n      await api.put(\"/api/treatment/Update\", formData)\r\n        .then(() => toast.current?.show({ severity: \"success\", summary: \"成功\", detail: detail }) )\r\n        .catch((err) => toast.current?.show({ severity: \"error\", summary: \"更新失敗\", detail: err.details}) );  \r\n    }\r\n\r\n    if(step === 40){\r\n      setTimeout(() => navigate(\"/treatments\"), 1500); // 送出後導回列表頁\r\n    }\r\n  };\r\n\r\n  // 上傳\r\n  const handleCustomUpload = async (event: FileUploadHandlerEvent, type: 'hospital' | 'consent') => {\r\n    const file = event.files?.[0];\r\n    if (!file) return;\r\n\r\n    if (!formData.orderNo) {\r\n      toast.current?.show({\r\n        severity: \"error\",\r\n        summary: \"上傳失敗\",\r\n        detail: \"請先儲存治療記錄，取得單號後再上傳檔案。\",\r\n      });\r\n      return;\r\n    }\r\n\r\n    const formDataToSend = new FormData();\r\n    formDataToSend.append(\"file\", file);\r\n    formDataToSend.append(\"orderNo\", formData.orderNo);\r\n\r\n    try {\r\n      const response = await api.post(\"/api/system/UploadFile\", formDataToSend, {\r\n        headers: {\r\n          \"Content-Type\": \"multipart/form-data\",\r\n        },\r\n      });\r\n\r\n      const { fileName } = response.data;\r\n\r\n      // 更新 formData\r\n      const updatedFormData = {\r\n        ...formData,\r\n        [type === 'hospital' ? 'hospitalFormUrl' : 'treatmentConsentFormUrl']: fileName,\r\n      };\r\n\r\n      // 更新 UI\r\n      setFormData(updatedFormData);\r\n\r\n      // 直接呼叫更新 API\r\n      await api.put(\"/api/treatment/Update\", updatedFormData);\r\n\r\n      toast.current?.show({\r\n        severity: \"success\",\r\n        summary: \"成功\",\r\n        detail: \"檔案已上傳並更新記錄。\",\r\n      });\r\n\r\n    } catch (error: any) {\r\n      const errorMessage = error.response?.data?.message || error.message || \"檔案上傳失敗\";\r\n      toast.current?.show({\r\n        severity: \"error\",\r\n        summary: \"上傳失敗\",\r\n        detail: errorMessage,\r\n      });\r\n    }\r\n  };\r\n\r\n  const getOptions = (groupId: number) => {\r\n    return dataType.find(group => group.groupId === groupId)?.dataTypes.map(item => ({\r\n      label: item.name,\r\n      value: item.name\r\n    })) || [];\r\n  };\r\n\r\n  const checkStep = (step:number) => {\r\n      if (step === 20 && formData.orderNo === \"\"){\r\n        toast.current?.show({ severity: \"error\", summary: \"錯誤\", detail: \"初次開案請存檔\" })\r\n      }else{\r\n        stepperRef.current?.nextCallback()\r\n      }\r\n    };\r\n\r\n  if (loading) return <p>Loading...</p>;\r\n\r\n  return (\r\n    <div className=\"p-4\">\r\n      <Toast ref={toast} />\r\n      <ConfirmDialog />\r\n\r\n      <div className=\"card flex justify-content-center\">\r\n        <Stepper ref={stepperRef} activeStep={currentStep} style={{ flexBasis: '100%' }}>\r\n            <StepperPanel header=\"症狀描述\">\r\n              <div className=\"flex flex-column\">\r\n                <div className=\"grid formgrid p-fluid gap-3 justify-content-center\">\r\n\r\n                  <div className=\"col-5 md:col-5\" hidden>\r\n                    <label>patientId</label>\r\n                    <div className=\"flex flex-wrap gap-3\">\r\n                      <InputTextarea  name=\"patientId\" rows={1} value={formData.patientId?.toString()} onChange={handleChange} />\r\n                    </div>\r\n                  </div>\r\n                  <div className=\"col-5 md:col-5\" hidden>\r\n                    <label>orderNo</label>\r\n                    <div className=\"flex flex-wrap gap-3\">\r\n                      <InputTextarea  name=\"orderNo\" rows={1} value={formData.orderNo?.toString()} onChange={handleChange} />\r\n                    </div>\r\n                  </div>\r\n\r\n\r\n                  {/* 不適區域新增區域 */}\r\n                  <div className=\"col-12 md:col-6\">\r\n                    <h4>新增不適區域</h4>\r\n                    <div className=\"grid formgrid p-fluid\">\r\n                      <div className=\"col-12 flex flex-wrap justify-content-between\">\r\n                        <div className=\"col-12 md:col-4 flex justify-content-center\">\r\n                        <Image src=\"/images/image-body.jpg\" alt=\"Image\" width=\"250\" />\r\n                        </div>\r\n                        <div className=\"col-12 md:col-8 flex justify-content-center align-items-center\" >\r\n                        <Image src=\"/images/NumericalRaringAcale.png\" alt=\"Image\" imageStyle={{ width: \"100%\", maxWidth: \"550px\", height: \"auto\" }} />\r\n                        </div>\r\n                      </div>\r\n                      <div className=\"col-12 flex flex-wrap\">\r\n                        <div className=\"col-6 md:col-4\">\r\n                          <label className=\"font-bold block pt-2 mb-2\">前側/後側</label>\r\n                          <Dropdown\r\n                            value={currentDiscomfortArea.frontAndBack}\r\n                            onChange={(e) => handleDiscomfortAreaChange('frontAndBack', e.value)}\r\n                            options={getOptions(1)}\r\n                            optionLabel=\"label\"\r\n                            optionValue=\"value\"\r\n                            placeholder=\"選擇前側/後側\"\r\n                          />\r\n                        </div>\r\n                        <div className=\"col-6 md:col-4\">\r\n                          <label className=\"font-bold block pt-2 mb-2\">不適區域</label>\r\n                          <Dropdown\r\n                            value={currentDiscomfortArea.discomfortArea}\r\n                            onChange={(e) => handleDiscomfortAreaChange('discomfortArea', e.value)}\r\n                            options={getOptions(2)}\r\n                            optionLabel=\"label\"\r\n                            optionValue=\"value\"\r\n                            placeholder=\"選擇不適區域\"\r\n                          />\r\n                        </div>\r\n                        <div className=\"col-6 md:col-4\">\r\n                          <label className=\"font-bold block pt-2 mb-2\">不適情況</label>\r\n                          <Dropdown\r\n                            value={currentDiscomfortArea.discomfortSituation}\r\n                            onChange={(e) => handleDiscomfortAreaChange('discomfortSituation', e.value)}\r\n                            options={getOptions(3)}\r\n                            optionLabel=\"label\"\r\n                            optionValue=\"value\"\r\n                            placeholder=\"選擇不適情況\"\r\n                          />\r\n                        </div>\r\n                      </div>\r\n                      <div className=\"col-12 flex\">\r\n                        <div className=\"col-6 md:col-4\">\r\n                          <label className=\"font-bold block pt-2 mb-2\">疼痛指數</label>\r\n                          <InputNumber\r\n                            value={currentDiscomfortArea.discomfortDegree}\r\n                            onValueChange={(e) => handleDiscomfortAreaChange('discomfortDegree', e.value || 0)}\r\n                            min={0}\r\n                            max={10}\r\n                            placeholder=\"0-10\"\r\n                          />\r\n                        </div>\r\n                        <div className=\"col-4 md:col-2\">\r\n                          <label className=\"font-bold block pt-2 mb-2\">&nbsp;</label>\r\n                          <Button\r\n                            label=\"新增\"\r\n                            icon=\"pi pi-plus\"\r\n                            onClick={addDiscomfortArea}\r\n                            disabled={formData.discomfortAreas.length >= 5}\r\n                          />\r\n                        </div>\r\n                      </div>\r\n                      \r\n                    </div>\r\n                  </div>\r\n\r\n                  {/* 不適區域列表 */}\r\n                  <div className=\"col-12 md:col-5\">\r\n                    <h4>不適區域列表 ({formData.discomfortAreas.length}/5)</h4>\r\n                    <DataTable value={formData.discomfortAreas} emptyMessage=\"尚無不適區域資料\">\r\n                      <Column field=\"frontAndBack\" header=\"前側/後側\" />\r\n                      <Column field=\"discomfortArea\" header=\"不適區域\" />\r\n                      <Column field=\"discomfortSituation\" header=\"不適情況\" />\r\n                      <Column field=\"discomfortDegree\" header=\"疼痛指數\" />\r\n                      <Column\r\n                        header=\"操作\"\r\n                        body={(_, options) => (\r\n                          <Button\r\n                            icon=\"pi pi-trash\"\r\n                            className=\"p-button-rounded p-button-danger p-button-text\"\r\n                            onClick={() => removeDiscomfortArea(options.rowIndex)}\r\n                          />\r\n                        )}\r\n                      />\r\n                    </DataTable>\r\n                  </div>\r\n\r\n                  <div className=\"col-12 md:col-6\">\r\n                    <label className=\"font-bold block mb-2\">可能引發原因(可複選)</label>\r\n                    <div className=\"flex flex-wrap gap-3 pb-2\">\r\n                    {getOptions(5).map(option => (\r\n                      <div key={option.value} className=\"flex align-items-center\">\r\n                        <Checkbox\r\n                          inputId={`front-${option.value}`}\r\n                          value={option.value}\r\n                          onChange={(e) => handleCheckboxChange(\"possibleCauses\", option.value, e.checked?? false)}\r\n                          checked={formData.possibleCauses.split(\",\").includes(option.value)}\r\n                        />\r\n                        <label htmlFor={`front-${option.value}`} className=\"ml-2\">{option.label}</label>\r\n                      </div>\r\n                    ))}\r\n                    </div>\r\n                    <InputTextarea  name=\"possibleCauses\" rows={1} value={formData.possibleCauses} onChange={handleChange} />\r\n                  </div>\r\n\r\n                  <div className=\"col-12 md:col-5\">\r\n                    <label className=\"font-bold block mb-2\">不適時間</label>\r\n                    <div className=\"flex flex-wrap gap-3 pb-2\">\r\n                    {getOptions(4).map(option => (\r\n                      <div key={option.value} className=\"flex align-items-center\">\r\n                        <Checkbox\r\n                          inputId={`front-${option.value}`}\r\n                          value={option.value}\r\n                          onChange={() => setFormData(prev => ({ ...prev, discomfortPeriod: option.value }))}\r\n                          checked={formData.discomfortPeriod === option.value}\r\n                        />\r\n                        <label htmlFor={`front-${option.value}`} className=\"ml-2\">{option.label}</label>\r\n                      </div>\r\n                    ))}\r\n                    </div>\r\n                  </div>\r\n\r\n                  <div className=\"col-12 md:col-6\">\r\n                    <label className=\"font-bold block mb-2\">曾接受過相關處置</label>\r\n                    <div className=\"flex flex-wrap gap-3 pb-2\">\r\n                      {getOptions(6).map(option => (\r\n                        <div key={option.value} className=\"flex align-items-center\">\r\n                          <Checkbox\r\n                            inputId={`front-${option.value}`}\r\n                            value={option.value}\r\n                            onChange={(e) => handleCheckboxChange(\"treatmentHistory\", option.value, e.checked?? false)}\r\n                            checked={formData.treatmentHistory.split(\",\").includes(option.value)}\r\n                          />\r\n                          <label htmlFor={`front-${option.value}`} className=\"ml-2\">{option.label}</label>\r\n                        </div>\r\n                      ))}\r\n                    </div>\r\n                    <InputTextarea  name=\"treatmentHistory\" rows={1} value={formData.treatmentHistory} onChange={handleChange} />\r\n                  </div>\r\n\r\n                  <div className=\"col-12 md:col-5\">\r\n                    <label className=\"font-bold block mb-2\">如何知道我們院所</label>\r\n                    <div className=\"flex flex-wrap gap-3 pb-2\">\r\n                    {getOptions(7).map(option => (\r\n                      <div key={option.value} className=\"flex align-items-center\">\r\n                        <Checkbox\r\n                          inputId={`front-${option.value}`}\r\n                          value={option.value}\r\n                          onChange={() => setFormData(prev => ({ ...prev, howToKnowOur: option.value }))}\r\n                          checked={formData.howToKnowOur === option.value}\r\n                        />\r\n                        <label htmlFor={`front-${option.value}`} className=\"ml-2\">{option.label}</label>\r\n                      </div>\r\n                    ))}\r\n                    </div>\r\n                    <InputTextarea  name=\"howToKnowOur\" rows={1} value={formData.howToKnowOur} onChange={handleChange} />\r\n                  </div>\r\n                </div>  \r\n              </div>\r\n              <div className=\"flex pt-4 justify-content-between\">\r\n                  <Button label=\"複製\" severity=\"info\" icon=\"pi pi-copy\" iconPos=\"left\" onClick={copyLatestRecord} />\r\n                  <div className=\"flex gap-2\">\r\n                    <Button label=\"存檔\" severity=\"success\" icon=\"pi pi-upload\" onClick={() => handleSubmit(10)} />\r\n                  </div>\r\n                  <Button label=\"下一步\" icon=\"pi pi-arrow-right\" iconPos=\"right\" onClick={() => checkStep(20)} />\r\n              </div>\r\n            </StepperPanel>\r\n\r\n            <StepperPanel header=\"治療師診療\">\r\n              <div className=\"flex flex-column\">\r\n                <div className=\"grid formgrid p-fluid gap-3 justify-content-center\">\r\n                  <div className=\"col-12 md:col-5\">\r\n                    <label className=\"font-bold block mb-2\">主觀症狀 (S)</label>\r\n                    <InputTextarea name=\"subjective\" rows={6} value={formData.subjective} onChange={handleChange} />\r\n                  </div>\r\n\r\n                  <div className=\"col-12 md:col-5\">\r\n                    <label className=\"font-bold block mb-2\">客觀檢查 (O)</label>\r\n                    <InputTextarea name=\"objective\" rows={6} value={formData.objective} onChange={handleChange} />\r\n                  </div>\r\n\r\n                  <div className=\"col-12 md:col-5\">\r\n                    <label className=\"font-bold block mb-2\">專業判斷 (A)</label>\r\n                    <InputTextarea name=\"assessment\" rows={6} value={formData.assessment} onChange={handleChange} />\r\n                  </div>\r\n\r\n                  <div className=\"col-12 md:col-5\">\r\n                    <label className=\"font-bold block mb-2\">治療計畫 (P)</label>\r\n                    <InputTextarea name=\"plan\" rows={6} value={formData.plan} onChange={handleChange} />\r\n                  </div>\r\n                </div>\r\n              </div>\r\n              <div className=\"flex pt-4 justify-content-between\">\r\n                  <Button label=\"上一步\" severity=\"secondary\" icon=\"pi pi-arrow-left\" onClick={() => stepperRef.current?.prevCallback()} />\r\n                  <Button label=\"存檔\" severity=\"success\" icon=\"pi pi-upload\" onClick={() => handleSubmit(20)} />\r\n                  <Button label=\"下一步\" icon=\"pi pi-arrow-right\" iconPos=\"right\" onClick={() => checkStep(30)} />\r\n              </div>\r\n            </StepperPanel>\r\n\r\n            <StepperPanel header=\"檔案上傳\">\r\n              <div className=\"flex flex-column \">\r\n                <div className=\"grid formgrid p-fluid gap-3 justify-content-center\">\r\n                  <div className=\"col-12 md:col-5\">\r\n                    <label className=\"font-bold block mb-2\">治療同意書</label>\r\n                    <div className=\"flex flex-column h-15rem\">\r\n                        <div className=\"border-2 border-dashed surface-border border-round surface-ground flex-auto flex justify-content-center align-items-center font-medium\">\r\n                          {formData.treatmentConsentFormUrl ? (\r\n                            <Image src={imagepath+formData.treatmentConsentFormUrl} indicatorIcon={<i className=\"pi pi-search\"></i>} alt=\"Image\" width=\"100%\" height=\"230rem\" preview />\r\n                          ) : (\r\n                            <FileUpload\r\n                              mode=\"basic\"\r\n                              name=\"TreatmentConsentFormUrl\"\r\n                              customUpload\r\n                              uploadHandler={(e) => handleCustomUpload(e, 'consent')}\r\n                              accept=\"image/*\"\r\n                              maxFileSize={1000000}\r\n                              chooseLabel=\"選擇檔案\"\r\n                            />\r\n                          )}\r\n                        </div>\r\n                    </div>\r\n                  </div>\r\n                    <div>\r\n\r\n                    </div>\r\n                  <div className=\"col-12 md:col-5\">\r\n                    <div className=\"col-12 md:col-5\"></div>\r\n                    <label className=\"font-bold block mb-2\">醫院診斷書</label>\r\n                    <div className=\"flex flex-column h-15rem\">\r\n                        <div className=\"border-2 border-dashed surface-border border-round surface-ground flex-auto flex justify-content-center align-items-center font-medium\">\r\n                          {formData.hospitalFormUrl ? (\r\n                            <Image  src={imagepath+formData.hospitalFormUrl} indicatorIcon={<i className=\"pi pi-search\"></i>} alt=\"Image\" width=\"100%\" height=\"230rem\" preview />\r\n                          ) : (\r\n                            <FileUpload\r\n                              mode=\"basic\"\r\n                              name=\"HospitalFormUrl\"\r\n                              customUpload\r\n                              uploadHandler={(e) => handleCustomUpload(e, 'hospital')}\r\n                              accept=\"image/*\"\r\n                              maxFileSize={1000000}\r\n                              chooseLabel=\"選擇檔案\"\r\n                            />\r\n                          )}\r\n                        </div>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n                <div className=\"grid formgrid p-fluid gap-3 pt-2 justify-content-center\">\r\n                  <div className=\"col-12 md:col-5\"></div>\r\n                  <div className=\"col-12 md:col-5 flex justify-content-end\">\r\n                    <div className=\"col-10 md:col-4\">\r\n                    <label className=\"font-bold block mb-2\">醫院診斷書開立時間</label>\r\n                    <Calendar\r\n                      value={formData.hospitalFormRecordDate}\r\n                      onChange={(e) => handleDateChange(e.value)}\r\n                      showIcon\r\n                      dateFormat=\"yy/mm/dd\"\r\n                      placeholder=\"選擇日期\"\r\n                    />\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n              <div className=\"flex pt-4 justify-content-between\">\r\n                  <Button label=\"上一步\" severity=\"secondary\" icon=\"pi pi-arrow-left\" onClick={() => stepperRef.current?.prevCallback()} />\r\n                  <Button label=\"結案\" severity=\"success\" icon=\"pi pi-check\" iconPos=\"right\" onClick={() => handleSubmit(40)} />\r\n              </div>\r\n            </StepperPanel>\r\n        </Stepper>\r\n    </div>\r\n\r\n\r\n    </div>\r\n    \r\n  );\r\n};\r\n\r\nexport default TreatmentsDetailPage;"], "mappings": "wJAAA,OAASA,MAAM,KAAQ,mBAAmB,CAC1C,OAASC,QAAQ,KAAQ,qBAAqB,CAC9C,OAASC,QAAQ,KAAQ,qBAAqB,CAC9C,OAASC,MAAM,KAAQ,mBAAmB,CAC1C,OAASC,aAAa,CAAEC,aAAa,KAAQ,0BAA0B,CACvE,OAASC,SAAS,KAAQ,sBAAsB,CAChD,OAASC,QAAQ,KAAQ,qBAAqB,CAC9C,OAASC,UAAU,KAAgC,uBAAuB,CAC1E,OAASC,KAAK,KAAQ,kBAAkB,CACxC,OAASC,WAAW,KAAQ,wBAAwB,CACpD,OAASC,aAAa,KAAQ,0BAA0B,CACxD,OAASC,OAAO,KAAQ,oBAAoB,CAC5C,OAASC,YAAY,KAAQ,yBAAyB,CACtD,OAASC,KAAK,KAAQ,kBAAkB,CACxC,MAAO,CAAAC,KAAK,EAAIC,SAAS,CAAEC,MAAM,CAAEC,QAAQ,KAAQ,OAAO,CAC1D,OAASC,WAAW,CAAEC,WAAW,KAAQ,kBAAkB,CAC3D,MAAO,CAAAC,WAAW,KAAM,yBAAyB,CACjD,MAAO,CAAAC,GAAG,KAAM,oBAAoB,CACpC,MAAO,CAAAC,SAAS,KAAM,0BAA0B,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBA4BjD,KAAM,CAAAC,oBAA8B,CAAGA,CAAA,GAAM,KAAAC,eAAA,CAAAC,gBAAA,CAAAC,mBAAA,CAAAC,iBAAA,CAC3C,KAAM,CAAAC,QAAQ,CAAGd,WAAW,CAAC,CAAC,CAC9B,KAAM,CAAAe,OAAO,EAAAL,eAAA,CAAGI,QAAQ,CAACE,KAAK,UAAAN,eAAA,iBAAdA,eAAA,CAAgBK,OAAO,CACvC,KAAM,CAAAE,SAAS,EAAAN,gBAAA,CAAGG,QAAQ,CAACE,KAAK,UAAAL,gBAAA,iBAAdA,gBAAA,CAAgBM,SAAS,CAC3C,KAAM,CAAAC,KAAK,CAAGpB,MAAM,CAAQ,IAAI,CAAC,CACjC,KAAM,CAAAqB,UAAU,CAAGrB,MAAM,CAAU,IAAI,CAAC,CACxC,KAAM,CAACsB,WAAW,CAAEC,cAAc,CAAC,CAAGtB,QAAQ,CAAC,CAAC,CAAC,CACjD,KAAM,CAAAuB,QAAQ,CAAGrB,WAAW,CAAC,CAAC,CAC9B,KAAM,CAAEsB,QAAQ,CAAEC,OAAQ,CAAC,CAAGtB,WAAW,CAAC,CAAC,CAC3C,KAAM,CAACuB,QAAQ,CAAEC,WAAW,CAAC,CAAG3B,QAAQ,CAAY,CAClD4B,OAAO,CAAE,EAAE,CACXC,IAAI,CAAE,CAAC,CACPC,gBAAgB,CAAE,EAAE,CACpBC,cAAc,CAAE,EAAE,CAClBC,gBAAgB,CAAE,EAAE,CACpBC,YAAY,CAAE,EAAE,CAChBC,eAAe,CAAE,EAAE,CACnBC,uBAAuB,CAAE,EAAE,CAC3BC,UAAU,CAAE,EAAE,CACdC,SAAS,CAAE,EAAE,CACbC,UAAU,CAAE,EAAE,CACdC,IAAI,CAAE,EAAE,CACRC,SAAS,CAAE,CAAAxB,OAAO,SAAPA,OAAO,iBAAPA,OAAO,CAAEyB,EAAE,GAAI,CAAC,CAC3BC,sBAAsB,CAAE,IAAI,CAC5BC,eAAe,CAAE,EACnB,CAAC,CAAC,CAEF;AACA,KAAM,CAACC,qBAAqB,CAAEC,wBAAwB,CAAC,CAAG7C,QAAQ,CAAiB,CACjF8C,YAAY,CAAE,EAAE,CAChBC,cAAc,CAAE,EAAE,CAClBC,mBAAmB,CAAE,EAAE,CACvBC,gBAAgB,CAAE,CACpB,CAAC,CAAC,CAEFnD,SAAS,CAAC,IAAM,CACZ,GAAIoB,SAAS,CAAE,CACXS,WAAW,CAAAuB,aAAA,CAAAA,aAAA,IACNhC,SAAS,MACZwB,sBAAsB,CAAExB,SAAS,CAACwB,sBAAsB,CAAG,GAAI,CAAAS,IAAI,CAACjC,SAAS,CAACwB,sBAAsB,CAAC,CAAG,IAAI,EAC3G,CAAC,CAEJ,GAAGxB,SAAS,CAACW,IAAI,GAAK,EAAE,CAAC,CACvBP,cAAc,CAAC,CAAC,CAAC,CACnB,CACA,GAAGJ,SAAS,CAACW,IAAI,GAAK,EAAE,CAAC,CACvBP,cAAc,CAAC,CAAC,CAAC,CACnB,CACJ,CACF,CAAC,CAAE,CAACJ,SAAS,CAAC,CAAC,CAEjB,KAAM,CAAAkC,YAAY,CAAIC,CAA4D,EAAK,CAC/E,KAAM,CAAEC,IAAI,CAAEC,KAAM,CAAC,CAAGF,CAAC,CAACG,MAAM,CAChC7B,WAAW,CAAE8B,IAAI,EAAAP,aAAA,CAAAA,aAAA,IAAWO,IAAI,MAAE,CAACH,IAAI,EAAGC,KAAK,EAAG,CAAC,CACzD,CAAC,CAEH,KAAM,CAAAG,oBAAoB,CAAGA,CAACJ,IAAqB,CAAEC,KAAa,CAAEI,OAAgB,GAAK,CACvFhC,WAAW,CAAC8B,IAAI,EAAI,CAClB,KAAM,CAAAG,OAAO,CAAIH,IAAI,CAACH,IAAI,CAAC,EAAI,EAAa,CAC5C,KAAM,CAAAO,UAAU,CAAGD,OAAO,CAAGA,OAAO,CAACE,KAAK,CAAC,GAAG,CAAC,CAAG,EAAE,CAEpD,KAAM,CAAAC,UAAU,CAAGJ,OAAO,CACtBE,UAAU,CAACG,QAAQ,CAACT,KAAK,CAAC,CACxBM,UAAU,CACV,CAAC,GAAGA,UAAU,CAAEN,KAAK,CAAC,CACxBM,UAAU,CAACI,MAAM,CAACC,IAAI,EAAIA,IAAI,GAAKX,KAAK,CAAC,CAE/C,OAAAL,aAAA,CAAAA,aAAA,IAAYO,IAAI,MAAE,CAACH,IAAI,EAAGS,UAAU,CAACI,IAAI,CAAC,GAAG,CAAC,GAC9C,CAAC,CAAC,CACJ,CAAC,CAED,KAAM,CAAAC,gBAAgB,CAAIb,KAA8B,EAAK,CAC3D5B,WAAW,CAAC8B,IAAI,EAAAP,aAAA,CAAAA,aAAA,IAAUO,IAAI,MAAEf,sBAAsB,CAAEa,KAAK,EAAI,IAAI,EAAG,CAAC,CAC3E,CAAC,CAED;AACA,KAAM,CAAAc,0BAA0B,CAAGA,CAACC,KAA2B,CAAEf,KAAU,GAAK,CAC9EV,wBAAwB,CAACY,IAAI,EAAAP,aAAA,CAAAA,aAAA,IAAUO,IAAI,MAAE,CAACa,KAAK,EAAGf,KAAK,EAAG,CAAC,CACjE,CAAC,CAED,KAAM,CAAAgB,iBAAiB,CAAGA,CAAA,GAAM,KAAAC,eAAA,CAC9B,GAAI,CAAC5B,qBAAqB,CAACE,YAAY,EAAI,CAACF,qBAAqB,CAACG,cAAc,EAAI,CAACH,qBAAqB,CAACI,mBAAmB,CAAE,KAAAyB,cAAA,CAC9H,CAAAA,cAAA,CAAAtD,KAAK,CAACyC,OAAO,UAAAa,cAAA,iBAAbA,cAAA,CAAeC,IAAI,CAAC,CAAEC,QAAQ,CAAE,OAAO,CAAEC,OAAO,CAAE,MAAM,CAAEC,MAAM,CAAE,cAAe,CAAC,CAAC,CACnF,OACF,CAEA,GAAInD,QAAQ,CAACiB,eAAe,CAACmC,MAAM,EAAI,CAAC,CAAE,KAAAC,eAAA,CACxC,CAAAA,eAAA,CAAA5D,KAAK,CAACyC,OAAO,UAAAmB,eAAA,iBAAbA,eAAA,CAAeL,IAAI,CAAC,CAAEC,QAAQ,CAAE,OAAO,CAAEC,OAAO,CAAE,MAAM,CAAEC,MAAM,CAAE,kBAAmB,CAAC,CAAC,CACvF,OACF,CAEAlD,WAAW,CAAC8B,IAAI,EAAAP,aAAA,CAAAA,aAAA,IACXO,IAAI,MACPd,eAAe,CAAE,CAAC,GAAGc,IAAI,CAACd,eAAe,CAAAO,aAAA,IAAON,qBAAqB,EAAG,EACxE,CAAC,CAEH;AACAC,wBAAwB,CAAC,CACvBC,YAAY,CAAE,EAAE,CAChBC,cAAc,CAAE,EAAE,CAClBC,mBAAmB,CAAE,EAAE,CACvBC,gBAAgB,CAAE,CACpB,CAAC,CAAC,CAEF,CAAAuB,eAAA,CAAArD,KAAK,CAACyC,OAAO,UAAAY,eAAA,iBAAbA,eAAA,CAAeE,IAAI,CAAC,CAAEC,QAAQ,CAAE,SAAS,CAAEC,OAAO,CAAE,MAAM,CAAEC,MAAM,CAAE,SAAU,CAAC,CAAC,CAClF,CAAC,CAED,KAAM,CAAAG,oBAAoB,CAAIC,KAAa,EAAK,KAAAC,eAAA,CAC9CvD,WAAW,CAAC8B,IAAI,EAAAP,aAAA,CAAAA,aAAA,IACXO,IAAI,MACPd,eAAe,CAAEc,IAAI,CAACd,eAAe,CAACsB,MAAM,CAAC,CAACkB,CAAC,CAAEC,CAAC,GAAKA,CAAC,GAAKH,KAAK,CAAC,EACnE,CAAC,CACH,CAAAC,eAAA,CAAA/D,KAAK,CAACyC,OAAO,UAAAsB,eAAA,iBAAbA,eAAA,CAAeR,IAAI,CAAC,CAAEC,QAAQ,CAAE,SAAS,CAAEC,OAAO,CAAE,MAAM,CAAEC,MAAM,CAAE,SAAU,CAAC,CAAC,CAClF,CAAC,CAED,KAAM,CAAAQ,gBAAgB,CAAGA,CAAA,GAAM,CAE7BlG,aAAa,CAAC,CACZmG,OAAO,CAAE,aAAa,CACtBC,MAAM,CAAE,MAAM,CACdC,IAAI,CAAE,4BAA4B,CAClCC,MAAM,CAAE,KAAAA,CAAA,GAAY,CAClB,GAAI,CACF,KAAM,CAAAC,QAAQ,CAAG,KAAM,CAAAtF,GAAG,CAACuF,GAAG,mCAAAC,MAAA,CAAmC5E,OAAO,SAAPA,OAAO,iBAAPA,OAAO,CAAEyB,EAAE,EAAI,CAC9EoD,MAAM,CAAE,KAAK,CACbC,OAAO,CAAE,CACP,cAAc,CAAE,kBAClB,CACF,CAAC,CAAC,CAEF,GAAIJ,QAAQ,CAAE,KAAAK,eAAA,CACZ,KAAM,CAAAC,YAAY,CAAG,KAAM,CAAAN,QAAQ,CAACO,IAAI,CACxCtE,WAAW,CAAC8B,IAAI,EAAAP,aAAA,CAAAA,aAAA,IACXO,IAAI,MACP3B,gBAAgB,CAAEkE,YAAY,CAAClE,gBAAgB,EAAI,EAAE,CACrDC,cAAc,CAAEiE,YAAY,CAACjE,cAAc,EAAI,EAAE,CACjDC,gBAAgB,CAAEgE,YAAY,CAAChE,gBAAgB,EAAI,EAAE,CACrDC,YAAY,CAAE+D,YAAY,CAAC/D,YAAY,EAAI,EAAE,CAC7CG,UAAU,CAAE4D,YAAY,CAAC5D,UAAU,EAAI,EAAE,CACzCC,SAAS,CAAE2D,YAAY,CAAC3D,SAAS,EAAI,EAAE,CACvCC,UAAU,CAAE0D,YAAY,CAAC1D,UAAU,EAAI,EAAE,CACzCC,IAAI,CAAEyD,YAAY,CAACzD,IAAI,EAAI,EAAE,CAC7BI,eAAe,CAAEqD,YAAY,CAACrD,eAAe,EAAI,EAAE,EACnD,CAAC,CACH,CAAAoD,eAAA,CAAA5E,KAAK,CAACyC,OAAO,UAAAmC,eAAA,iBAAbA,eAAA,CAAerB,IAAI,CAAC,CAAEC,QAAQ,CAAE,SAAS,CAAEC,OAAO,CAAE,MAAM,CAAEC,MAAM,CAAE,YAAa,CAAC,CAAC,CAErF,CACF,CAAE,MAAOqB,KAAS,CAAE,KAAAC,eAAA,CAClB,CAAAA,eAAA,CAAAhF,KAAK,CAACyC,OAAO,UAAAuC,eAAA,iBAAbA,eAAA,CAAezB,IAAI,CAAC,CAAEC,QAAQ,CAAE,OAAO,CAAEC,OAAO,CAAE,MAAM,CAAEC,MAAM,CAAEqB,KAAK,CAACE,OAAQ,CAAC,CAAC,CACpF,CACF,CACF,CAAC,CAAC,CACJ,CAAC,CAED,KAAM,CAAAC,YAAY,CAAG,KAAO,CAAAxE,IAAW,EAAK,CAE1C,GAAI,CAAAgD,MAAM,CAAG,SAAS,CAEtB,GAAGhD,IAAI,GAAK,EAAE,CAAC,CACb,GAAGH,QAAQ,CAACQ,eAAe,GAAK,EAAE,EAAIR,QAAQ,CAACS,uBAAuB,GAAK,EAAE,CAAC,KAAAmE,eAAA,CAC5E,CAAAA,eAAA,CAAAnF,KAAK,CAACyC,OAAO,UAAA0C,eAAA,iBAAbA,eAAA,CAAe5B,IAAI,CAAC,CAAEC,QAAQ,CAAE,OAAO,CAAEC,OAAO,CAAE,MAAM,CAAEC,MAAM,CAAE,gBAAgB,CAAC,CAAC,CACpF,OACF,CACA,GAAGnD,QAAQ,CAACQ,eAAe,GAAK,EAAE,EAAI,CAACR,QAAQ,CAACgB,sBAAsB,CAAC,KAAA6D,eAAA,CACrE,CAAAA,eAAA,CAAApF,KAAK,CAACyC,OAAO,UAAA2C,eAAA,iBAAbA,eAAA,CAAe7B,IAAI,CAAC,CAAEC,QAAQ,CAAE,OAAO,CAAEC,OAAO,CAAE,MAAM,CAAEC,MAAM,CAAE,cAAc,CAAC,CAAC,CAClF,OACF,CACF,CAEA,GAAInD,QAAQ,CAACE,OAAO,GAAK,EAAE,CAAE,CACzB;AACA,KAAM,CAAAxB,GAAG,CAACoG,IAAI,CAAC,uBAAuB,CAAE9E,QAAQ,CAAC,CAChD+E,IAAI,CAAEC,GAAG,EAAK,KAAAC,eAAA,CACbhF,WAAW,CACR8B,IAAI,EAAAP,aAAA,CAAAA,aAAA,IAAUO,IAAI,MAAC7B,OAAO,CAAE8E,GAAG,CAACT,IAAI,CAACrE,OAAO,EAC/C,CAAC,CACD,CAAA+E,eAAA,CAAAxF,KAAK,CAACyC,OAAO,UAAA+C,eAAA,iBAAbA,eAAA,CAAejC,IAAI,CAAC,CAAEC,QAAQ,CAAE,SAAS,CAAEC,OAAO,CAAE,IAAI,CAAEC,MAAM,CAAE6B,GAAG,CAACT,IAAI,CAACW,GAAI,CAAC,CAAC,CAAC,CAAE,CAAC,CACtFC,KAAK,CAAEC,GAAG,OAAAC,eAAA,QAAAA,eAAA,CAAK5F,KAAK,CAACyC,OAAO,UAAAmD,eAAA,iBAAbA,eAAA,CAAerC,IAAI,CAAC,CAAEC,QAAQ,CAAE,OAAO,CAAEC,OAAO,CAAE,MAAM,CAAEC,MAAM,CAAEiC,GAAG,CAACV,OAAO,CAAC,CAAC,EAAC,CAAC,CAErG,CAAC,IACG,CACF;AACA1E,QAAQ,CAACG,IAAI,CAAGH,QAAQ,CAACG,IAAI,CAAGA,IAAI,CAAGH,QAAQ,CAACG,IAAI,CAAGA,IAAI,CAC3DF,WAAW,CACJ8B,IAAI,EAAAP,aAAA,CAAAA,aAAA,IAAUO,IAAI,MAAC5B,IAAI,CAAEH,QAAQ,CAACG,IAAI,EACzC,CAAC,CACL,KAAM,CAAAzB,GAAG,CAAC4G,GAAG,CAAC,uBAAuB,CAAEtF,QAAQ,CAAC,CAC7C+E,IAAI,CAAC,SAAAQ,eAAA,QAAAA,eAAA,CAAM9F,KAAK,CAACyC,OAAO,UAAAqD,eAAA,iBAAbA,eAAA,CAAevC,IAAI,CAAC,CAAEC,QAAQ,CAAE,SAAS,CAAEC,OAAO,CAAE,IAAI,CAAEC,MAAM,CAAEA,MAAO,CAAC,CAAC,EAAC,CAAC,CACxFgC,KAAK,CAAEC,GAAG,OAAAI,gBAAA,QAAAA,gBAAA,CAAK/F,KAAK,CAACyC,OAAO,UAAAsD,gBAAA,iBAAbA,gBAAA,CAAexC,IAAI,CAAC,CAAEC,QAAQ,CAAE,OAAO,CAAEC,OAAO,CAAE,MAAM,CAAEC,MAAM,CAAEiC,GAAG,CAACV,OAAO,CAAC,CAAC,EAAC,CAAC,CACrG,CAEA,GAAGvE,IAAI,GAAK,EAAE,CAAC,CACbsF,UAAU,CAAC,IAAM5F,QAAQ,CAAC,aAAa,CAAC,CAAE,IAAI,CAAC,CAAE;AACnD,CACF,CAAC,CAED;AACA,KAAM,CAAA6F,kBAAkB,CAAG,KAAAA,CAAOC,KAA6B,CAAEC,IAA4B,GAAK,KAAAC,YAAA,CAChG,KAAM,CAAAC,IAAI,EAAAD,YAAA,CAAGF,KAAK,CAACI,KAAK,UAAAF,YAAA,iBAAXA,YAAA,CAAc,CAAC,CAAC,CAC7B,GAAI,CAACC,IAAI,CAAE,OAEX,GAAI,CAAC9F,QAAQ,CAACE,OAAO,CAAE,KAAA8F,gBAAA,CACrB,CAAAA,gBAAA,CAAAvG,KAAK,CAACyC,OAAO,UAAA8D,gBAAA,iBAAbA,gBAAA,CAAehD,IAAI,CAAC,CAClBC,QAAQ,CAAE,OAAO,CACjBC,OAAO,CAAE,MAAM,CACfC,MAAM,CAAE,sBACV,CAAC,CAAC,CACF,OACF,CAEA,KAAM,CAAA8C,cAAc,CAAG,GAAI,CAAAC,QAAQ,CAAC,CAAC,CACrCD,cAAc,CAACE,MAAM,CAAC,MAAM,CAAEL,IAAI,CAAC,CACnCG,cAAc,CAACE,MAAM,CAAC,SAAS,CAAEnG,QAAQ,CAACE,OAAO,CAAC,CAElD,GAAI,KAAAkG,gBAAA,CACF,KAAM,CAAApC,QAAQ,CAAG,KAAM,CAAAtF,GAAG,CAACoG,IAAI,CAAC,wBAAwB,CAAEmB,cAAc,CAAE,CACxE7B,OAAO,CAAE,CACP,cAAc,CAAE,qBAClB,CACF,CAAC,CAAC,CAEF,KAAM,CAAEiC,QAAS,CAAC,CAAGrC,QAAQ,CAACO,IAAI,CAElC;AACA,KAAM,CAAA+B,eAAe,CAAA9E,aAAA,CAAAA,aAAA,IAChBxB,QAAQ,MACX,CAAC4F,IAAI,GAAK,UAAU,CAAG,iBAAiB,CAAG,yBAAyB,EAAGS,QAAQ,EAChF,CAED;AACApG,WAAW,CAACqG,eAAe,CAAC,CAE5B;AACA,KAAM,CAAA5H,GAAG,CAAC4G,GAAG,CAAC,uBAAuB,CAAEgB,eAAe,CAAC,CAEvD,CAAAF,gBAAA,CAAA3G,KAAK,CAACyC,OAAO,UAAAkE,gBAAA,iBAAbA,gBAAA,CAAepD,IAAI,CAAC,CAClBC,QAAQ,CAAE,SAAS,CACnBC,OAAO,CAAE,IAAI,CACbC,MAAM,CAAE,aACV,CAAC,CAAC,CAEJ,CAAE,MAAOqB,KAAU,CAAE,KAAA+B,eAAA,CAAAC,oBAAA,CAAAC,gBAAA,CACnB,KAAM,CAAAC,YAAY,CAAG,EAAAH,eAAA,CAAA/B,KAAK,CAACR,QAAQ,UAAAuC,eAAA,kBAAAC,oBAAA,CAAdD,eAAA,CAAgBhC,IAAI,UAAAiC,oBAAA,iBAApBA,oBAAA,CAAsB5C,OAAO,GAAIY,KAAK,CAACZ,OAAO,EAAI,QAAQ,CAC/E,CAAA6C,gBAAA,CAAAhH,KAAK,CAACyC,OAAO,UAAAuE,gBAAA,iBAAbA,gBAAA,CAAezD,IAAI,CAAC,CAClBC,QAAQ,CAAE,OAAO,CACjBC,OAAO,CAAE,MAAM,CACfC,MAAM,CAAEuD,YACV,CAAC,CAAC,CACJ,CACF,CAAC,CAED,KAAM,CAAAC,UAAU,CAAIC,OAAe,EAAK,KAAAC,cAAA,CACtC,MAAO,EAAAA,cAAA,CAAA/G,QAAQ,CAACgH,IAAI,CAACC,KAAK,EAAIA,KAAK,CAACH,OAAO,GAAKA,OAAO,CAAC,UAAAC,cAAA,iBAAjDA,cAAA,CAAmDG,SAAS,CAACC,GAAG,CAACzE,IAAI,GAAK,CAC/E0E,KAAK,CAAE1E,IAAI,CAACZ,IAAI,CAChBC,KAAK,CAAEW,IAAI,CAACZ,IACd,CAAC,CAAC,CAAC,GAAI,EAAE,CACX,CAAC,CAED,KAAM,CAAAuF,SAAS,CAAIhH,IAAW,EAAK,CAC/B,GAAIA,IAAI,GAAK,EAAE,EAAIH,QAAQ,CAACE,OAAO,GAAK,EAAE,CAAC,KAAAkH,gBAAA,CACzC,CAAAA,gBAAA,CAAA3H,KAAK,CAACyC,OAAO,UAAAkF,gBAAA,iBAAbA,gBAAA,CAAepE,IAAI,CAAC,CAAEC,QAAQ,CAAE,OAAO,CAAEC,OAAO,CAAE,IAAI,CAAEC,MAAM,CAAE,SAAU,CAAC,CAAC,CAC9E,CAAC,IAAI,KAAAkE,mBAAA,CACH,CAAAA,mBAAA,CAAA3H,UAAU,CAACwC,OAAO,UAAAmF,mBAAA,iBAAlBA,mBAAA,CAAoBC,YAAY,CAAC,CAAC,CACpC,CACF,CAAC,CAEH,GAAIvH,OAAO,CAAE,mBAAOlB,IAAA,MAAA0I,QAAA,CAAG,YAAU,CAAG,CAAC,CAErC,mBACExI,KAAA,QAAKyI,SAAS,CAAC,KAAK,CAAAD,QAAA,eAClB1I,IAAA,CAACX,KAAK,EAACuJ,GAAG,CAAEhI,KAAM,CAAE,CAAC,cACrBZ,IAAA,CAACrB,aAAa,GAAE,CAAC,cAEjBqB,IAAA,QAAK2I,SAAS,CAAC,kCAAkC,CAAAD,QAAA,cAC/CxI,KAAA,CAACf,OAAO,EAACyJ,GAAG,CAAE/H,UAAW,CAACgI,UAAU,CAAE/H,WAAY,CAACgI,KAAK,CAAE,CAAEC,SAAS,CAAE,MAAO,CAAE,CAAAL,QAAA,eAC5ExI,KAAA,CAACd,YAAY,EAAC4F,MAAM,CAAC,0BAAM,CAAA0D,QAAA,eACzB1I,IAAA,QAAK2I,SAAS,CAAC,kBAAkB,CAAAD,QAAA,cAC/BxI,KAAA,QAAKyI,SAAS,CAAC,oDAAoD,CAAAD,QAAA,eAEjExI,KAAA,QAAKyI,SAAS,CAAC,gBAAgB,CAACK,MAAM,MAAAN,QAAA,eACpC1I,IAAA,UAAA0I,QAAA,CAAO,WAAS,CAAO,CAAC,cACxB1I,IAAA,QAAK2I,SAAS,CAAC,sBAAsB,CAAAD,QAAA,cACnC1I,IAAA,CAACd,aAAa,EAAE6D,IAAI,CAAC,WAAW,CAACkG,IAAI,CAAE,CAAE,CAACjG,KAAK,EAAA1C,mBAAA,CAAEa,QAAQ,CAACc,SAAS,UAAA3B,mBAAA,iBAAlBA,mBAAA,CAAoB4I,QAAQ,CAAC,CAAE,CAACC,QAAQ,CAAEtG,YAAa,CAAE,CAAC,CACxG,CAAC,EACH,CAAC,cACN3C,KAAA,QAAKyI,SAAS,CAAC,gBAAgB,CAACK,MAAM,MAAAN,QAAA,eACpC1I,IAAA,UAAA0I,QAAA,CAAO,SAAO,CAAO,CAAC,cACtB1I,IAAA,QAAK2I,SAAS,CAAC,sBAAsB,CAAAD,QAAA,cACnC1I,IAAA,CAACd,aAAa,EAAE6D,IAAI,CAAC,SAAS,CAACkG,IAAI,CAAE,CAAE,CAACjG,KAAK,EAAAzC,iBAAA,CAAEY,QAAQ,CAACE,OAAO,UAAAd,iBAAA,iBAAhBA,iBAAA,CAAkB2I,QAAQ,CAAC,CAAE,CAACC,QAAQ,CAAEtG,YAAa,CAAE,CAAC,CACpG,CAAC,EACH,CAAC,cAIN3C,KAAA,QAAKyI,SAAS,CAAC,iBAAiB,CAAAD,QAAA,eAC9B1I,IAAA,OAAA0I,QAAA,CAAI,sCAAM,CAAI,CAAC,cACfxI,KAAA,QAAKyI,SAAS,CAAC,uBAAuB,CAAAD,QAAA,eACpCxI,KAAA,QAAKyI,SAAS,CAAC,+CAA+C,CAAAD,QAAA,eAC5D1I,IAAA,QAAK2I,SAAS,CAAC,6CAA6C,CAAAD,QAAA,cAC5D1I,IAAA,CAAChB,KAAK,EAACoK,GAAG,CAAC,wBAAwB,CAACC,GAAG,CAAC,OAAO,CAACC,KAAK,CAAC,KAAK,CAAE,CAAC,CACzD,CAAC,cACNtJ,IAAA,QAAK2I,SAAS,CAAC,gEAAgE,CAAAD,QAAA,cAC/E1I,IAAA,CAAChB,KAAK,EAACoK,GAAG,CAAC,kCAAkC,CAACC,GAAG,CAAC,OAAO,CAACE,UAAU,CAAE,CAAED,KAAK,CAAE,MAAM,CAAEE,QAAQ,CAAE,OAAO,CAAEC,MAAM,CAAE,MAAO,CAAE,CAAE,CAAC,CACzH,CAAC,EACH,CAAC,cACNvJ,KAAA,QAAKyI,SAAS,CAAC,uBAAuB,CAAAD,QAAA,eACpCxI,KAAA,QAAKyI,SAAS,CAAC,gBAAgB,CAAAD,QAAA,eAC7B1I,IAAA,UAAO2I,SAAS,CAAC,2BAA2B,CAAAD,QAAA,CAAC,2BAAK,CAAO,CAAC,cAC1D1I,IAAA,CAAClB,QAAQ,EACPkE,KAAK,CAAEX,qBAAqB,CAACE,YAAa,CAC1C4G,QAAQ,CAAGrG,CAAC,EAAKgB,0BAA0B,CAAC,cAAc,CAAEhB,CAAC,CAACE,KAAK,CAAE,CACrE0G,OAAO,CAAE5B,UAAU,CAAC,CAAC,CAAE,CACvB6B,WAAW,CAAC,OAAO,CACnBC,WAAW,CAAC,OAAO,CACnBC,WAAW,CAAC,uCAAS,CACtB,CAAC,EACC,CAAC,cACN3J,KAAA,QAAKyI,SAAS,CAAC,gBAAgB,CAAAD,QAAA,eAC7B1I,IAAA,UAAO2I,SAAS,CAAC,2BAA2B,CAAAD,QAAA,CAAC,0BAAI,CAAO,CAAC,cACzD1I,IAAA,CAAClB,QAAQ,EACPkE,KAAK,CAAEX,qBAAqB,CAACG,cAAe,CAC5C2G,QAAQ,CAAGrG,CAAC,EAAKgB,0BAA0B,CAAC,gBAAgB,CAAEhB,CAAC,CAACE,KAAK,CAAE,CACvE0G,OAAO,CAAE5B,UAAU,CAAC,CAAC,CAAE,CACvB6B,WAAW,CAAC,OAAO,CACnBC,WAAW,CAAC,OAAO,CACnBC,WAAW,CAAC,sCAAQ,CACrB,CAAC,EACC,CAAC,cACN3J,KAAA,QAAKyI,SAAS,CAAC,gBAAgB,CAAAD,QAAA,eAC7B1I,IAAA,UAAO2I,SAAS,CAAC,2BAA2B,CAAAD,QAAA,CAAC,0BAAI,CAAO,CAAC,cACzD1I,IAAA,CAAClB,QAAQ,EACPkE,KAAK,CAAEX,qBAAqB,CAACI,mBAAoB,CACjD0G,QAAQ,CAAGrG,CAAC,EAAKgB,0BAA0B,CAAC,qBAAqB,CAAEhB,CAAC,CAACE,KAAK,CAAE,CAC5E0G,OAAO,CAAE5B,UAAU,CAAC,CAAC,CAAE,CACvB6B,WAAW,CAAC,OAAO,CACnBC,WAAW,CAAC,OAAO,CACnBC,WAAW,CAAC,sCAAQ,CACrB,CAAC,EACC,CAAC,EACH,CAAC,cACN3J,KAAA,QAAKyI,SAAS,CAAC,aAAa,CAAAD,QAAA,eAC1BxI,KAAA,QAAKyI,SAAS,CAAC,gBAAgB,CAAAD,QAAA,eAC7B1I,IAAA,UAAO2I,SAAS,CAAC,2BAA2B,CAAAD,QAAA,CAAC,0BAAI,CAAO,CAAC,cACzD1I,IAAA,CAACf,WAAW,EACV+D,KAAK,CAAEX,qBAAqB,CAACK,gBAAiB,CAC9CoH,aAAa,CAAGhH,CAAC,EAAKgB,0BAA0B,CAAC,kBAAkB,CAAEhB,CAAC,CAACE,KAAK,EAAI,CAAC,CAAE,CACnF+G,GAAG,CAAE,CAAE,CACPC,GAAG,CAAE,EAAG,CACRH,WAAW,CAAC,MAAM,CACnB,CAAC,EACC,CAAC,cACN3J,KAAA,QAAKyI,SAAS,CAAC,gBAAgB,CAAAD,QAAA,eAC7B1I,IAAA,UAAO2I,SAAS,CAAC,2BAA2B,CAAAD,QAAA,CAAC,MAAM,CAAO,CAAC,cAC3D1I,IAAA,CAACzB,MAAM,EACL8J,KAAK,CAAC,cAAI,CACVpD,IAAI,CAAC,YAAY,CACjBgF,OAAO,CAAEjG,iBAAkB,CAC3BkG,QAAQ,CAAE/I,QAAQ,CAACiB,eAAe,CAACmC,MAAM,EAAI,CAAE,CAChD,CAAC,EACC,CAAC,EACH,CAAC,EAEH,CAAC,EACH,CAAC,cAGNrE,KAAA,QAAKyI,SAAS,CAAC,iBAAiB,CAAAD,QAAA,eAC9BxI,KAAA,OAAAwI,QAAA,EAAI,wCAAQ,CAACvH,QAAQ,CAACiB,eAAe,CAACmC,MAAM,CAAC,KAAG,EAAI,CAAC,cACrDrE,KAAA,CAACrB,SAAS,EAACmE,KAAK,CAAE7B,QAAQ,CAACiB,eAAgB,CAAC+H,YAAY,CAAC,kDAAU,CAAAzB,QAAA,eACjE1I,IAAA,CAACtB,MAAM,EAACqF,KAAK,CAAC,cAAc,CAACiB,MAAM,CAAC,2BAAO,CAAE,CAAC,cAC9ChF,IAAA,CAACtB,MAAM,EAACqF,KAAK,CAAC,gBAAgB,CAACiB,MAAM,CAAC,0BAAM,CAAE,CAAC,cAC/ChF,IAAA,CAACtB,MAAM,EAACqF,KAAK,CAAC,qBAAqB,CAACiB,MAAM,CAAC,0BAAM,CAAE,CAAC,cACpDhF,IAAA,CAACtB,MAAM,EAACqF,KAAK,CAAC,kBAAkB,CAACiB,MAAM,CAAC,0BAAM,CAAE,CAAC,cACjDhF,IAAA,CAACtB,MAAM,EACLsG,MAAM,CAAC,cAAI,CACXoF,IAAI,CAAEA,CAACxF,CAAC,CAAE8E,OAAO,gBACf1J,IAAA,CAACzB,MAAM,EACL0G,IAAI,CAAC,aAAa,CAClB0D,SAAS,CAAC,gDAAgD,CAC1DsB,OAAO,CAAEA,CAAA,GAAMxF,oBAAoB,CAACiF,OAAO,CAACW,QAAQ,CAAE,CACvD,CACD,CACH,CAAC,EACO,CAAC,EACT,CAAC,cAENnK,KAAA,QAAKyI,SAAS,CAAC,iBAAiB,CAAAD,QAAA,eAC9B1I,IAAA,UAAO2I,SAAS,CAAC,sBAAsB,CAAAD,QAAA,CAAC,0DAAW,CAAO,CAAC,cAC3D1I,IAAA,QAAK2I,SAAS,CAAC,2BAA2B,CAAAD,QAAA,CACzCZ,UAAU,CAAC,CAAC,CAAC,CAACM,GAAG,CAACkC,MAAM,eACvBpK,KAAA,QAAwByI,SAAS,CAAC,yBAAyB,CAAAD,QAAA,eACzD1I,IAAA,CAACvB,QAAQ,EACP8L,OAAO,UAAAlF,MAAA,CAAWiF,MAAM,CAACtH,KAAK,CAAG,CACjCA,KAAK,CAAEsH,MAAM,CAACtH,KAAM,CACpBmG,QAAQ,CAAGrG,CAAC,OAAA0H,UAAA,OAAK,CAAArH,oBAAoB,CAAC,gBAAgB,CAAEmH,MAAM,CAACtH,KAAK,EAAAwH,UAAA,CAAE1H,CAAC,CAACM,OAAO,UAAAoH,UAAA,UAAAA,UAAA,CAAG,KAAK,CAAC,EAAC,CACzFpH,OAAO,CAAEjC,QAAQ,CAACK,cAAc,CAAC+B,KAAK,CAAC,GAAG,CAAC,CAACE,QAAQ,CAAC6G,MAAM,CAACtH,KAAK,CAAE,CACpE,CAAC,cACFhD,IAAA,UAAOyK,OAAO,UAAApF,MAAA,CAAWiF,MAAM,CAACtH,KAAK,CAAG,CAAC2F,SAAS,CAAC,MAAM,CAAAD,QAAA,CAAE4B,MAAM,CAACjC,KAAK,CAAQ,CAAC,GAPxEiC,MAAM,CAACtH,KAQZ,CACN,CAAC,CACG,CAAC,cACNhD,IAAA,CAACd,aAAa,EAAE6D,IAAI,CAAC,gBAAgB,CAACkG,IAAI,CAAE,CAAE,CAACjG,KAAK,CAAE7B,QAAQ,CAACK,cAAe,CAAC2H,QAAQ,CAAEtG,YAAa,CAAE,CAAC,EACtG,CAAC,cAEN3C,KAAA,QAAKyI,SAAS,CAAC,iBAAiB,CAAAD,QAAA,eAC9B1I,IAAA,UAAO2I,SAAS,CAAC,sBAAsB,CAAAD,QAAA,CAAC,0BAAI,CAAO,CAAC,cACpD1I,IAAA,QAAK2I,SAAS,CAAC,2BAA2B,CAAAD,QAAA,CACzCZ,UAAU,CAAC,CAAC,CAAC,CAACM,GAAG,CAACkC,MAAM,eACvBpK,KAAA,QAAwByI,SAAS,CAAC,yBAAyB,CAAAD,QAAA,eACzD1I,IAAA,CAACvB,QAAQ,EACP8L,OAAO,UAAAlF,MAAA,CAAWiF,MAAM,CAACtH,KAAK,CAAG,CACjCA,KAAK,CAAEsH,MAAM,CAACtH,KAAM,CACpBmG,QAAQ,CAAEA,CAAA,GAAM/H,WAAW,CAAC8B,IAAI,EAAAP,aAAA,CAAAA,aAAA,IAAUO,IAAI,MAAE3B,gBAAgB,CAAE+I,MAAM,CAACtH,KAAK,EAAG,CAAE,CACnFI,OAAO,CAAEjC,QAAQ,CAACI,gBAAgB,GAAK+I,MAAM,CAACtH,KAAM,CACrD,CAAC,cACFhD,IAAA,UAAOyK,OAAO,UAAApF,MAAA,CAAWiF,MAAM,CAACtH,KAAK,CAAG,CAAC2F,SAAS,CAAC,MAAM,CAAAD,QAAA,CAAE4B,MAAM,CAACjC,KAAK,CAAQ,CAAC,GAPxEiC,MAAM,CAACtH,KAQZ,CACN,CAAC,CACG,CAAC,EACH,CAAC,cAEN9C,KAAA,QAAKyI,SAAS,CAAC,iBAAiB,CAAAD,QAAA,eAC9B1I,IAAA,UAAO2I,SAAS,CAAC,sBAAsB,CAAAD,QAAA,CAAC,kDAAQ,CAAO,CAAC,cACxD1I,IAAA,QAAK2I,SAAS,CAAC,2BAA2B,CAAAD,QAAA,CACvCZ,UAAU,CAAC,CAAC,CAAC,CAACM,GAAG,CAACkC,MAAM,eACvBpK,KAAA,QAAwByI,SAAS,CAAC,yBAAyB,CAAAD,QAAA,eACzD1I,IAAA,CAACvB,QAAQ,EACP8L,OAAO,UAAAlF,MAAA,CAAWiF,MAAM,CAACtH,KAAK,CAAG,CACjCA,KAAK,CAAEsH,MAAM,CAACtH,KAAM,CACpBmG,QAAQ,CAAGrG,CAAC,OAAA4H,WAAA,OAAK,CAAAvH,oBAAoB,CAAC,kBAAkB,CAAEmH,MAAM,CAACtH,KAAK,EAAA0H,WAAA,CAAE5H,CAAC,CAACM,OAAO,UAAAsH,WAAA,UAAAA,WAAA,CAAG,KAAK,CAAC,EAAC,CAC3FtH,OAAO,CAAEjC,QAAQ,CAACM,gBAAgB,CAAC8B,KAAK,CAAC,GAAG,CAAC,CAACE,QAAQ,CAAC6G,MAAM,CAACtH,KAAK,CAAE,CACtE,CAAC,cACFhD,IAAA,UAAOyK,OAAO,UAAApF,MAAA,CAAWiF,MAAM,CAACtH,KAAK,CAAG,CAAC2F,SAAS,CAAC,MAAM,CAAAD,QAAA,CAAE4B,MAAM,CAACjC,KAAK,CAAQ,CAAC,GAPxEiC,MAAM,CAACtH,KAQZ,CACN,CAAC,CACC,CAAC,cACNhD,IAAA,CAACd,aAAa,EAAE6D,IAAI,CAAC,kBAAkB,CAACkG,IAAI,CAAE,CAAE,CAACjG,KAAK,CAAE7B,QAAQ,CAACM,gBAAiB,CAAC0H,QAAQ,CAAEtG,YAAa,CAAE,CAAC,EAC1G,CAAC,cAEN3C,KAAA,QAAKyI,SAAS,CAAC,iBAAiB,CAAAD,QAAA,eAC9B1I,IAAA,UAAO2I,SAAS,CAAC,sBAAsB,CAAAD,QAAA,CAAC,kDAAQ,CAAO,CAAC,cACxD1I,IAAA,QAAK2I,SAAS,CAAC,2BAA2B,CAAAD,QAAA,CACzCZ,UAAU,CAAC,CAAC,CAAC,CAACM,GAAG,CAACkC,MAAM,eACvBpK,KAAA,QAAwByI,SAAS,CAAC,yBAAyB,CAAAD,QAAA,eACzD1I,IAAA,CAACvB,QAAQ,EACP8L,OAAO,UAAAlF,MAAA,CAAWiF,MAAM,CAACtH,KAAK,CAAG,CACjCA,KAAK,CAAEsH,MAAM,CAACtH,KAAM,CACpBmG,QAAQ,CAAEA,CAAA,GAAM/H,WAAW,CAAC8B,IAAI,EAAAP,aAAA,CAAAA,aAAA,IAAUO,IAAI,MAAExB,YAAY,CAAE4I,MAAM,CAACtH,KAAK,EAAG,CAAE,CAC/EI,OAAO,CAAEjC,QAAQ,CAACO,YAAY,GAAK4I,MAAM,CAACtH,KAAM,CACjD,CAAC,cACFhD,IAAA,UAAOyK,OAAO,UAAApF,MAAA,CAAWiF,MAAM,CAACtH,KAAK,CAAG,CAAC2F,SAAS,CAAC,MAAM,CAAAD,QAAA,CAAE4B,MAAM,CAACjC,KAAK,CAAQ,CAAC,GAPxEiC,MAAM,CAACtH,KAQZ,CACN,CAAC,CACG,CAAC,cACNhD,IAAA,CAACd,aAAa,EAAE6D,IAAI,CAAC,cAAc,CAACkG,IAAI,CAAE,CAAE,CAACjG,KAAK,CAAE7B,QAAQ,CAACO,YAAa,CAACyH,QAAQ,CAAEtG,YAAa,CAAE,CAAC,EAClG,CAAC,EACH,CAAC,CACH,CAAC,cACN3C,KAAA,QAAKyI,SAAS,CAAC,mCAAmC,CAAAD,QAAA,eAC9C1I,IAAA,CAACzB,MAAM,EAAC8J,KAAK,CAAC,cAAI,CAACjE,QAAQ,CAAC,MAAM,CAACa,IAAI,CAAC,YAAY,CAAC0F,OAAO,CAAC,MAAM,CAACV,OAAO,CAAEnF,gBAAiB,CAAE,CAAC,cACjG9E,IAAA,QAAK2I,SAAS,CAAC,YAAY,CAAAD,QAAA,cACzB1I,IAAA,CAACzB,MAAM,EAAC8J,KAAK,CAAC,cAAI,CAACjE,QAAQ,CAAC,SAAS,CAACa,IAAI,CAAC,cAAc,CAACgF,OAAO,CAAEA,CAAA,GAAMnE,YAAY,CAAC,EAAE,CAAE,CAAE,CAAC,CAC1F,CAAC,cACN9F,IAAA,CAACzB,MAAM,EAAC8J,KAAK,CAAC,oBAAK,CAACpD,IAAI,CAAC,mBAAmB,CAAC0F,OAAO,CAAC,OAAO,CAACV,OAAO,CAAEA,CAAA,GAAM3B,SAAS,CAAC,EAAE,CAAE,CAAE,CAAC,EAC5F,CAAC,EACM,CAAC,cAEfpI,KAAA,CAACd,YAAY,EAAC4F,MAAM,CAAC,gCAAO,CAAA0D,QAAA,eAC1B1I,IAAA,QAAK2I,SAAS,CAAC,kBAAkB,CAAAD,QAAA,cAC/BxI,KAAA,QAAKyI,SAAS,CAAC,oDAAoD,CAAAD,QAAA,eACjExI,KAAA,QAAKyI,SAAS,CAAC,iBAAiB,CAAAD,QAAA,eAC9B1I,IAAA,UAAO2I,SAAS,CAAC,sBAAsB,CAAAD,QAAA,CAAC,8BAAQ,CAAO,CAAC,cACxD1I,IAAA,CAACd,aAAa,EAAC6D,IAAI,CAAC,YAAY,CAACkG,IAAI,CAAE,CAAE,CAACjG,KAAK,CAAE7B,QAAQ,CAACU,UAAW,CAACsH,QAAQ,CAAEtG,YAAa,CAAE,CAAC,EAC7F,CAAC,cAEN3C,KAAA,QAAKyI,SAAS,CAAC,iBAAiB,CAAAD,QAAA,eAC9B1I,IAAA,UAAO2I,SAAS,CAAC,sBAAsB,CAAAD,QAAA,CAAC,8BAAQ,CAAO,CAAC,cACxD1I,IAAA,CAACd,aAAa,EAAC6D,IAAI,CAAC,WAAW,CAACkG,IAAI,CAAE,CAAE,CAACjG,KAAK,CAAE7B,QAAQ,CAACW,SAAU,CAACqH,QAAQ,CAAEtG,YAAa,CAAE,CAAC,EAC3F,CAAC,cAEN3C,KAAA,QAAKyI,SAAS,CAAC,iBAAiB,CAAAD,QAAA,eAC9B1I,IAAA,UAAO2I,SAAS,CAAC,sBAAsB,CAAAD,QAAA,CAAC,8BAAQ,CAAO,CAAC,cACxD1I,IAAA,CAACd,aAAa,EAAC6D,IAAI,CAAC,YAAY,CAACkG,IAAI,CAAE,CAAE,CAACjG,KAAK,CAAE7B,QAAQ,CAACY,UAAW,CAACoH,QAAQ,CAAEtG,YAAa,CAAE,CAAC,EAC7F,CAAC,cAEN3C,KAAA,QAAKyI,SAAS,CAAC,iBAAiB,CAAAD,QAAA,eAC9B1I,IAAA,UAAO2I,SAAS,CAAC,sBAAsB,CAAAD,QAAA,CAAC,8BAAQ,CAAO,CAAC,cACxD1I,IAAA,CAACd,aAAa,EAAC6D,IAAI,CAAC,MAAM,CAACkG,IAAI,CAAE,CAAE,CAACjG,KAAK,CAAE7B,QAAQ,CAACa,IAAK,CAACmH,QAAQ,CAAEtG,YAAa,CAAE,CAAC,EACjF,CAAC,EACH,CAAC,CACH,CAAC,cACN3C,KAAA,QAAKyI,SAAS,CAAC,mCAAmC,CAAAD,QAAA,eAC9C1I,IAAA,CAACzB,MAAM,EAAC8J,KAAK,CAAC,oBAAK,CAACjE,QAAQ,CAAC,WAAW,CAACa,IAAI,CAAC,kBAAkB,CAACgF,OAAO,CAAEA,CAAA,QAAAW,oBAAA,QAAAA,oBAAA,CAAM/J,UAAU,CAACwC,OAAO,UAAAuH,oBAAA,iBAAlBA,oBAAA,CAAoBC,YAAY,CAAC,CAAC,EAAC,CAAE,CAAC,cACtH7K,IAAA,CAACzB,MAAM,EAAC8J,KAAK,CAAC,cAAI,CAACjE,QAAQ,CAAC,SAAS,CAACa,IAAI,CAAC,cAAc,CAACgF,OAAO,CAAEA,CAAA,GAAMnE,YAAY,CAAC,EAAE,CAAE,CAAE,CAAC,cAC7F9F,IAAA,CAACzB,MAAM,EAAC8J,KAAK,CAAC,oBAAK,CAACpD,IAAI,CAAC,mBAAmB,CAAC0F,OAAO,CAAC,OAAO,CAACV,OAAO,CAAEA,CAAA,GAAM3B,SAAS,CAAC,EAAE,CAAE,CAAE,CAAC,EAC5F,CAAC,EACM,CAAC,cAEfpI,KAAA,CAACd,YAAY,EAAC4F,MAAM,CAAC,0BAAM,CAAA0D,QAAA,eACzBxI,KAAA,QAAKyI,SAAS,CAAC,mBAAmB,CAAAD,QAAA,eAChCxI,KAAA,QAAKyI,SAAS,CAAC,oDAAoD,CAAAD,QAAA,eACjExI,KAAA,QAAKyI,SAAS,CAAC,iBAAiB,CAAAD,QAAA,eAC9B1I,IAAA,UAAO2I,SAAS,CAAC,sBAAsB,CAAAD,QAAA,CAAC,gCAAK,CAAO,CAAC,cACrD1I,IAAA,QAAK2I,SAAS,CAAC,0BAA0B,CAAAD,QAAA,cACrC1I,IAAA,QAAK2I,SAAS,CAAC,wIAAwI,CAAAD,QAAA,CACpJvH,QAAQ,CAACS,uBAAuB,cAC/B5B,IAAA,CAAChB,KAAK,EAACoK,GAAG,CAAEtJ,SAAS,CAACqB,QAAQ,CAACS,uBAAwB,CAACkJ,aAAa,cAAE9K,IAAA,MAAG2I,SAAS,CAAC,cAAc,CAAI,CAAE,CAACU,GAAG,CAAC,OAAO,CAACC,KAAK,CAAC,MAAM,CAACG,MAAM,CAAC,QAAQ,CAACsB,OAAO,MAAE,CAAC,cAE5J/K,IAAA,CAACjB,UAAU,EACTiM,IAAI,CAAC,OAAO,CACZjI,IAAI,CAAC,yBAAyB,CAC9BkI,YAAY,MACZC,aAAa,CAAGpI,CAAC,EAAK+D,kBAAkB,CAAC/D,CAAC,CAAE,SAAS,CAAE,CACvDoC,MAAM,CAAC,SAAS,CAChBiG,WAAW,CAAE,OAAQ,CACrBC,WAAW,CAAC,0BAAM,CACnB,CACF,CACE,CAAC,CACL,CAAC,EACH,CAAC,cACJpL,IAAA,SAEK,CAAC,cACRE,KAAA,QAAKyI,SAAS,CAAC,iBAAiB,CAAAD,QAAA,eAC9B1I,IAAA,QAAK2I,SAAS,CAAC,iBAAiB,CAAM,CAAC,cACvC3I,IAAA,UAAO2I,SAAS,CAAC,sBAAsB,CAAAD,QAAA,CAAC,gCAAK,CAAO,CAAC,cACrD1I,IAAA,QAAK2I,SAAS,CAAC,0BAA0B,CAAAD,QAAA,cACrC1I,IAAA,QAAK2I,SAAS,CAAC,wIAAwI,CAAAD,QAAA,CACpJvH,QAAQ,CAACQ,eAAe,cACvB3B,IAAA,CAAChB,KAAK,EAAEoK,GAAG,CAAEtJ,SAAS,CAACqB,QAAQ,CAACQ,eAAgB,CAACmJ,aAAa,cAAE9K,IAAA,MAAG2I,SAAS,CAAC,cAAc,CAAI,CAAE,CAACU,GAAG,CAAC,OAAO,CAACC,KAAK,CAAC,MAAM,CAACG,MAAM,CAAC,QAAQ,CAACsB,OAAO,MAAE,CAAC,cAErJ/K,IAAA,CAACjB,UAAU,EACTiM,IAAI,CAAC,OAAO,CACZjI,IAAI,CAAC,iBAAiB,CACtBkI,YAAY,MACZC,aAAa,CAAGpI,CAAC,EAAK+D,kBAAkB,CAAC/D,CAAC,CAAE,UAAU,CAAE,CACxDoC,MAAM,CAAC,SAAS,CAChBiG,WAAW,CAAE,OAAQ,CACrBC,WAAW,CAAC,0BAAM,CACnB,CACF,CACE,CAAC,CACL,CAAC,EACH,CAAC,EACH,CAAC,cACNlL,KAAA,QAAKyI,SAAS,CAAC,yDAAyD,CAAAD,QAAA,eACtE1I,IAAA,QAAK2I,SAAS,CAAC,iBAAiB,CAAM,CAAC,cACvC3I,IAAA,QAAK2I,SAAS,CAAC,0CAA0C,CAAAD,QAAA,cACvDxI,KAAA,QAAKyI,SAAS,CAAC,iBAAiB,CAAAD,QAAA,eAChC1I,IAAA,UAAO2I,SAAS,CAAC,sBAAsB,CAAAD,QAAA,CAAC,wDAAS,CAAO,CAAC,cACzD1I,IAAA,CAACxB,QAAQ,EACPwE,KAAK,CAAE7B,QAAQ,CAACgB,sBAAuB,CACvCgH,QAAQ,CAAGrG,CAAC,EAAKe,gBAAgB,CAACf,CAAC,CAACE,KAAK,CAAE,CAC3CqI,QAAQ,MACRC,UAAU,CAAC,UAAU,CACrBzB,WAAW,CAAC,0BAAM,CACnB,CAAC,EACG,CAAC,CACH,CAAC,EACH,CAAC,EACH,CAAC,cACN3J,KAAA,QAAKyI,SAAS,CAAC,mCAAmC,CAAAD,QAAA,eAC9C1I,IAAA,CAACzB,MAAM,EAAC8J,KAAK,CAAC,oBAAK,CAACjE,QAAQ,CAAC,WAAW,CAACa,IAAI,CAAC,kBAAkB,CAACgF,OAAO,CAAEA,CAAA,QAAAsB,oBAAA,QAAAA,oBAAA,CAAM1K,UAAU,CAACwC,OAAO,UAAAkI,oBAAA,iBAAlBA,oBAAA,CAAoBV,YAAY,CAAC,CAAC,EAAC,CAAE,CAAC,cACtH7K,IAAA,CAACzB,MAAM,EAAC8J,KAAK,CAAC,cAAI,CAACjE,QAAQ,CAAC,SAAS,CAACa,IAAI,CAAC,aAAa,CAAC0F,OAAO,CAAC,OAAO,CAACV,OAAO,CAAEA,CAAA,GAAMnE,YAAY,CAAC,EAAE,CAAE,CAAE,CAAC,EAC3G,CAAC,EACM,CAAC,EACV,CAAC,CACT,CAAC,EAGD,CAAC,CAGV,CAAC,CAED,cAAe,CAAA3F,oBAAoB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
import axios, { AxiosError, <PERSON><PERSON>os<PERSON><PERSON><PERSON>, AxiosRequestConfig, AxiosResponse } from "axios";
import { config } from "../config/env";

/**
 * API Error types
 */
export interface ApiError {
  message: string ;
  status?: number | undefined;
  code?: string | undefined;
  details?: any | undefined;
}

/**
 * API Response wrapper
 */
export interface ApiResponse<T = any> {
  data: T;
  success: boolean;
  message?: string;
  errors?: string[];
}

/**
 * Create API client with enhanced error handling and type safety
 */
class ApiClient {
  private instance: AxiosInstance;

  constructor() {
    this.instance = axios.create({
      baseURL: config.apiUrl,
      timeout: 10000,
      headers: {
        'Content-Type': 'application/json',
      },
    });

    this.setupInterceptors();
  }

  private setupInterceptors(): void {
    // Request interceptor
    this.instance.interceptors.request.use(
      (config) => {
        const token = localStorage.getItem("token");
        if (token && config.headers) {
          config.headers.Authorization = `Bearer ${token}`;
        }
        return config;
      },
      (error) => Promise.reject(this.handleError(error))
    );

    // Response interceptor
    this.instance.interceptors.response.use(
      (response: AxiosResponse) => response,
      (error: AxiosError) => Promise.reject(this.handleError(error))
    );
  }

  private handleError(error: AxiosError): ApiError {
    const apiError: ApiError = {
      message: 'An unexpected error occurred',
      status: error.response?.status,
      code: error.code,
    };

    if (error.response) {
      // Server responded with error status
      const { status, data } = error.response;
      apiError.status = status;
      apiError.message = (data as any)?.message || `HTTP Error ${status}`;
      apiError.details = data;

      // Handle specific status codes
      switch (status) {
        case 400:
          apiError.message = 'Bad request. Please check your input.';
          break;
        case 401:
          apiError.message = 'Unauthorized. Please login again.';
          localStorage.removeItem('token');
          // window.location.href = '/login';
          break;
        case 403:
          apiError.message = 'Access forbidden.';
          break;
        case 404:
          apiError.message = 'Resource not found.';
          break;
        case 500:
          apiError.message = 'Internal server error.';
          break;
      }
    } else if (error.request) {
      // Network error
      apiError.message = 'Network error. Please check your connection.';
    }

    return apiError;
  }

  /**
   * Generic GET request
   */
  async get<T = any>(url: string, config?: AxiosRequestConfig): Promise<T> {
    const response = await this.instance.get<T>(url, config);
    return response.data;
  }

  /**
   * Generic POST request
   */
  async post<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> {
    const response = await this.instance.post<T>(url, data, config);
    return response.data;
  }

  /**
   * Generic PUT request
   */
  async put<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> {
    const response = await this.instance.put<T>(url, data, config);
    return response.data;
  }

  /**
   * Generic DELETE request
   */
  async delete<T = any>(url: string, config?: AxiosRequestConfig): Promise<T> {
    const response = await this.instance.delete<T>(url, config);
    return response.data;
  }

  /**
   * Get the underlying axios instance
   */
  getInstance(): AxiosInstance {
    return this.instance;
  }
}

// Export singleton instance
export const apiClient = new ApiClient();

// Export default for backward compatibility
export default apiClient.getInstance();
{"ast": null, "code": "import { buildLocalizeFn } from \"../../_lib/buildLocalizeFn.js\";\nconst eraValues = {\n  narrow: [\"e.ə\", \"b.e\"],\n  abbreviated: [\"e.ə\", \"b.e\"],\n  wide: [\"eramızdan əvvəl\", \"bizim era\"]\n};\nconst quarterValues = {\n  narrow: [\"1\", \"2\", \"3\", \"4\"],\n  abbreviated: [\"K1\", \"K2\", \"K3\", \"K4\"],\n  wide: [\"1ci kvartal\", \"2ci kvartal\", \"3cü kvartal\", \"4cü kvartal\"]\n};\nconst monthValues = {\n  narrow: [\"Y\", \"F\", \"M\", \"A\", \"M\", \"İ\", \"İ\", \"A\", \"S\", \"O\", \"N\", \"D\"],\n  abbreviated: [\"Yan\", \"Fev\", \"Mar\", \"Apr\", \"May\", \"<PERSON>yun\", \"<PERSON>yul\", \"Avq\", \"Sen\", \"Okt\", \"<PERSON>y\", \"Dek\"],\n  wide: [\"<PERSON><PERSON>\", \"<PERSON><PERSON><PERSON>\", \"<PERSON>\", \"<PERSON><PERSON>\", \"May\", \"<PERSON>yun\", \"<PERSON>yu<PERSON>\", \"Avqust\", \"Sentyabr\", \"Oktyabr\", \"Noyabr\", \"Dekabr\"]\n};\nconst dayValues = {\n  narrow: [\"B.\", \"B.e\", \"Ç.a\", \"Ç.\", \"C.a\", \"C.\", \"Ş.\"],\n  short: [\"B.\", \"B.e\", \"Ç.a\", \"Ç.\", \"C.a\", \"C.\", \"Ş.\"],\n  abbreviated: [\"Baz\", \"Baz.e\", \"Çər.a\", \"Çər\", \"Cüm.a\", \"Cüm\", \"Şə\"],\n  wide: [\"Bazar\", \"Bazar ertəsi\", \"Çərşənbə axşamı\", \"Çərşənbə\", \"Cümə axşamı\", \"Cümə\", \"Şənbə\"]\n};\nconst dayPeriodValues = {\n  narrow: {\n    am: \"am\",\n    pm: \"pm\",\n    midnight: \"gecəyarı\",\n    noon: \"gün\",\n    morning: \"səhər\",\n    afternoon: \"gündüz\",\n    evening: \"axşam\",\n    night: \"gecə\"\n  },\n  abbreviated: {\n    am: \"AM\",\n    pm: \"PM\",\n    midnight: \"gecəyarı\",\n    noon: \"gün\",\n    morning: \"səhər\",\n    afternoon: \"gündüz\",\n    evening: \"axşam\",\n    night: \"gecə\"\n  },\n  wide: {\n    am: \"a.m.\",\n    pm: \"p.m.\",\n    midnight: \"gecəyarı\",\n    noon: \"gün\",\n    morning: \"səhər\",\n    afternoon: \"gündüz\",\n    evening: \"axşam\",\n    night: \"gecə\"\n  }\n};\nconst formattingDayPeriodValues = {\n  narrow: {\n    am: \"a\",\n    pm: \"p\",\n    midnight: \"gecəyarı\",\n    noon: \"gün\",\n    morning: \"səhər\",\n    afternoon: \"gündüz\",\n    evening: \"axşam\",\n    night: \"gecə\"\n  },\n  abbreviated: {\n    am: \"AM\",\n    pm: \"PM\",\n    midnight: \"gecəyarı\",\n    noon: \"gün\",\n    morning: \"səhər\",\n    afternoon: \"gündüz\",\n    evening: \"axşam\",\n    night: \"gecə\"\n  },\n  wide: {\n    am: \"a.m.\",\n    pm: \"p.m.\",\n    midnight: \"gecəyarı\",\n    noon: \"gün\",\n    morning: \"səhər\",\n    afternoon: \"gündüz\",\n    evening: \"axşam\",\n    night: \"gecə\"\n  }\n};\nconst suffixes = {\n  1: \"-inci\",\n  5: \"-inci\",\n  8: \"-inci\",\n  70: \"-inci\",\n  80: \"-inci\",\n  2: \"-nci\",\n  7: \"-nci\",\n  20: \"-nci\",\n  50: \"-nci\",\n  3: \"-üncü\",\n  4: \"-üncü\",\n  100: \"-üncü\",\n  6: \"-ncı\",\n  9: \"-uncu\",\n  10: \"-uncu\",\n  30: \"-uncu\",\n  60: \"-ıncı\",\n  90: \"-ıncı\"\n};\nconst getSuffix = number => {\n  if (number === 0) {\n    // special case for zero\n    return number + \"-ıncı\";\n  }\n  const a = number % 10;\n  const b = number % 100 - a;\n  const c = number >= 100 ? 100 : null;\n  if (suffixes[a]) {\n    return suffixes[a];\n  } else if (suffixes[b]) {\n    return suffixes[b];\n  } else if (c !== null) {\n    return suffixes[c];\n  }\n  return \"\";\n};\nconst ordinalNumber = (dirtyNumber, _options) => {\n  const number = Number(dirtyNumber);\n  const suffix = getSuffix(number);\n  return number + suffix;\n};\nexport const localize = {\n  ordinalNumber,\n  era: buildLocalizeFn({\n    values: eraValues,\n    defaultWidth: \"wide\"\n  }),\n  quarter: buildLocalizeFn({\n    values: quarterValues,\n    defaultWidth: \"wide\",\n    argumentCallback: quarter => quarter - 1\n  }),\n  month: buildLocalizeFn({\n    values: monthValues,\n    defaultWidth: \"wide\"\n  }),\n  day: buildLocalizeFn({\n    values: dayValues,\n    defaultWidth: \"wide\"\n  }),\n  dayPeriod: buildLocalizeFn({\n    values: dayPeriodValues,\n    defaultWidth: \"wide\",\n    formattingValues: formattingDayPeriodValues,\n    defaultFormattingWidth: \"wide\"\n  })\n};", "map": {"version": 3, "names": ["buildLocalizeFn", "<PERSON><PERSON><PERSON><PERSON>", "narrow", "abbreviated", "wide", "quarterValues", "month<PERSON><PERSON><PERSON>", "dayV<PERSON><PERSON>", "short", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "am", "pm", "midnight", "noon", "morning", "afternoon", "evening", "night", "formattingDayPeriodValues", "suffixes", "getSuffix", "number", "a", "b", "c", "ordinalNumber", "dirtyNumber", "_options", "Number", "suffix", "localize", "era", "values", "defaultWidth", "quarter", "argument<PERSON>allback", "month", "day", "<PERSON><PERSON><PERSON><PERSON>", "formattingValues", "defaultFormattingWidth"], "sources": ["C:/Users/<USER>/Desktop/demo-git/demo-react/my-fullstack-app/frontend/node_modules/date-fns/locale/az/_lib/localize.js"], "sourcesContent": ["import { buildLocalizeFn } from \"../../_lib/buildLocalizeFn.js\";\n\nconst eraValues = {\n  narrow: [\"e.ə\", \"b.e\"],\n  abbreviated: [\"e.ə\", \"b.e\"],\n  wide: [\"eramızdan əvvəl\", \"bizim era\"],\n};\n\nconst quarterValues = {\n  narrow: [\"1\", \"2\", \"3\", \"4\"],\n  abbreviated: [\"K1\", \"K2\", \"K3\", \"K4\"],\n  wide: [\"1ci kvartal\", \"2ci kvartal\", \"3cü kvartal\", \"4cü kvartal\"],\n};\nconst monthValues = {\n  narrow: [\"Y\", \"F\", \"M\", \"A\", \"M\", \"İ\", \"İ\", \"A\", \"S\", \"O\", \"N\", \"D\"],\n  abbreviated: [\n    \"Yan\",\n    \"Fev\",\n    \"Mar\",\n    \"Apr\",\n    \"May\",\n    \"İyun\",\n    \"<PERSON>yul\",\n    \"Avq\",\n    \"Sen\",\n    \"Okt\",\n    \"<PERSON><PERSON>\",\n    \"Dek\",\n  ],\n\n  wide: [\n    \"<PERSON><PERSON>\",\n    \"<PERSON><PERSON><PERSON>\",\n    \"<PERSON>\",\n    \"<PERSON><PERSON>\",\n    \"May\",\n    \"<PERSON><PERSON>\",\n    \"<PERSON>yu<PERSON>\",\n    \"Avqust\",\n    \"Sentyabr\",\n    \"Oktyabr\",\n    \"Noyabr\",\n    \"Dekabr\",\n  ],\n};\n\nconst dayValues = {\n  narrow: [\"B.\", \"B.e\", \"Ç.a\", \"Ç.\", \"C.a\", \"C.\", \"Ş.\"],\n  short: [\"B.\", \"B.e\", \"Ç.a\", \"Ç.\", \"C.a\", \"C.\", \"Ş.\"],\n  abbreviated: [\"Baz\", \"Baz.e\", \"Çər.a\", \"Çər\", \"Cüm.a\", \"Cüm\", \"Şə\"],\n  wide: [\n    \"Bazar\",\n    \"Bazar ertəsi\",\n    \"Çərşənbə axşamı\",\n    \"Çərşənbə\",\n    \"Cümə axşamı\",\n    \"Cümə\",\n    \"Şənbə\",\n  ],\n};\n\nconst dayPeriodValues = {\n  narrow: {\n    am: \"am\",\n    pm: \"pm\",\n    midnight: \"gecəyarı\",\n    noon: \"gün\",\n    morning: \"səhər\",\n    afternoon: \"gündüz\",\n    evening: \"axşam\",\n    night: \"gecə\",\n  },\n  abbreviated: {\n    am: \"AM\",\n    pm: \"PM\",\n    midnight: \"gecəyarı\",\n    noon: \"gün\",\n    morning: \"səhər\",\n    afternoon: \"gündüz\",\n    evening: \"axşam\",\n    night: \"gecə\",\n  },\n  wide: {\n    am: \"a.m.\",\n    pm: \"p.m.\",\n    midnight: \"gecəyarı\",\n    noon: \"gün\",\n    morning: \"səhər\",\n    afternoon: \"gündüz\",\n    evening: \"axşam\",\n    night: \"gecə\",\n  },\n};\n\nconst formattingDayPeriodValues = {\n  narrow: {\n    am: \"a\",\n    pm: \"p\",\n    midnight: \"gecəyarı\",\n    noon: \"gün\",\n    morning: \"səhər\",\n    afternoon: \"gündüz\",\n    evening: \"axşam\",\n    night: \"gecə\",\n  },\n  abbreviated: {\n    am: \"AM\",\n    pm: \"PM\",\n    midnight: \"gecəyarı\",\n    noon: \"gün\",\n    morning: \"səhər\",\n    afternoon: \"gündüz\",\n    evening: \"axşam\",\n    night: \"gecə\",\n  },\n  wide: {\n    am: \"a.m.\",\n    pm: \"p.m.\",\n    midnight: \"gecəyarı\",\n    noon: \"gün\",\n    morning: \"səhər\",\n    afternoon: \"gündüz\",\n    evening: \"axşam\",\n    night: \"gecə\",\n  },\n};\n\nconst suffixes = {\n  1: \"-inci\",\n  5: \"-inci\",\n  8: \"-inci\",\n  70: \"-inci\",\n  80: \"-inci\",\n  2: \"-nci\",\n  7: \"-nci\",\n  20: \"-nci\",\n  50: \"-nci\",\n  3: \"-üncü\",\n  4: \"-üncü\",\n  100: \"-üncü\",\n  6: \"-ncı\",\n  9: \"-uncu\",\n  10: \"-uncu\",\n  30: \"-uncu\",\n  60: \"-ıncı\",\n  90: \"-ıncı\",\n};\n\nconst getSuffix = (number) => {\n  if (number === 0) {\n    // special case for zero\n    return number + \"-ıncı\";\n  }\n\n  const a = number % 10;\n  const b = (number % 100) - a;\n  const c = number >= 100 ? 100 : null;\n\n  if (suffixes[a]) {\n    return suffixes[a];\n  } else if (suffixes[b]) {\n    return suffixes[b];\n  } else if (c !== null) {\n    return suffixes[c];\n  }\n  return \"\";\n};\n\nconst ordinalNumber = (dirtyNumber, _options) => {\n  const number = Number(dirtyNumber);\n  const suffix = getSuffix(number);\n\n  return number + suffix;\n};\n\nexport const localize = {\n  ordinalNumber,\n\n  era: buildLocalizeFn({\n    values: eraValues,\n    defaultWidth: \"wide\",\n  }),\n\n  quarter: buildLocalizeFn({\n    values: quarterValues,\n    defaultWidth: \"wide\",\n    argumentCallback: (quarter) => quarter - 1,\n  }),\n\n  month: buildLocalizeFn({\n    values: monthValues,\n    defaultWidth: \"wide\",\n  }),\n\n  day: buildLocalizeFn({\n    values: dayValues,\n    defaultWidth: \"wide\",\n  }),\n\n  dayPeriod: buildLocalizeFn({\n    values: dayPeriodValues,\n    defaultWidth: \"wide\",\n    formattingValues: formattingDayPeriodValues,\n    defaultFormattingWidth: \"wide\",\n  }),\n};\n"], "mappings": "AAAA,SAASA,eAAe,QAAQ,+BAA+B;AAE/D,MAAMC,SAAS,GAAG;EAChBC,MAAM,EAAE,CAAC,KAAK,EAAE,KAAK,CAAC;EACtBC,WAAW,EAAE,CAAC,KAAK,EAAE,KAAK,CAAC;EAC3BC,IAAI,EAAE,CAAC,iBAAiB,EAAE,WAAW;AACvC,CAAC;AAED,MAAMC,aAAa,GAAG;EACpBH,MAAM,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EAC5BC,WAAW,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;EACrCC,IAAI,EAAE,CAAC,aAAa,EAAE,aAAa,EAAE,aAAa,EAAE,aAAa;AACnE,CAAC;AACD,MAAME,WAAW,GAAG;EAClBJ,MAAM,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EACpEC,WAAW,EAAE,CACX,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,MAAM,EACN,MAAM,EACN,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,CACN;EAEDC,IAAI,EAAE,CACJ,QAAQ,EACR,QAAQ,EACR,MAAM,EACN,OAAO,EACP,KAAK,EACL,MAAM,EACN,MAAM,EACN,QAAQ,EACR,UAAU,EACV,SAAS,EACT,QAAQ,EACR,QAAQ;AAEZ,CAAC;AAED,MAAMG,SAAS,GAAG;EAChBL,MAAM,EAAE,CAAC,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,IAAI,CAAC;EACrDM,KAAK,EAAE,CAAC,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,IAAI,CAAC;EACpDL,WAAW,EAAE,CAAC,KAAK,EAAE,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,OAAO,EAAE,KAAK,EAAE,IAAI,CAAC;EACnEC,IAAI,EAAE,CACJ,OAAO,EACP,cAAc,EACd,iBAAiB,EACjB,UAAU,EACV,aAAa,EACb,MAAM,EACN,OAAO;AAEX,CAAC;AAED,MAAMK,eAAe,GAAG;EACtBP,MAAM,EAAE;IACNQ,EAAE,EAAE,IAAI;IACRC,EAAE,EAAE,IAAI;IACRC,QAAQ,EAAE,UAAU;IACpBC,IAAI,EAAE,KAAK;IACXC,OAAO,EAAE,OAAO;IAChBC,SAAS,EAAE,QAAQ;IACnBC,OAAO,EAAE,OAAO;IAChBC,KAAK,EAAE;EACT,CAAC;EACDd,WAAW,EAAE;IACXO,EAAE,EAAE,IAAI;IACRC,EAAE,EAAE,IAAI;IACRC,QAAQ,EAAE,UAAU;IACpBC,IAAI,EAAE,KAAK;IACXC,OAAO,EAAE,OAAO;IAChBC,SAAS,EAAE,QAAQ;IACnBC,OAAO,EAAE,OAAO;IAChBC,KAAK,EAAE;EACT,CAAC;EACDb,IAAI,EAAE;IACJM,EAAE,EAAE,MAAM;IACVC,EAAE,EAAE,MAAM;IACVC,QAAQ,EAAE,UAAU;IACpBC,IAAI,EAAE,KAAK;IACXC,OAAO,EAAE,OAAO;IAChBC,SAAS,EAAE,QAAQ;IACnBC,OAAO,EAAE,OAAO;IAChBC,KAAK,EAAE;EACT;AACF,CAAC;AAED,MAAMC,yBAAyB,GAAG;EAChChB,MAAM,EAAE;IACNQ,EAAE,EAAE,GAAG;IACPC,EAAE,EAAE,GAAG;IACPC,QAAQ,EAAE,UAAU;IACpBC,IAAI,EAAE,KAAK;IACXC,OAAO,EAAE,OAAO;IAChBC,SAAS,EAAE,QAAQ;IACnBC,OAAO,EAAE,OAAO;IAChBC,KAAK,EAAE;EACT,CAAC;EACDd,WAAW,EAAE;IACXO,EAAE,EAAE,IAAI;IACRC,EAAE,EAAE,IAAI;IACRC,QAAQ,EAAE,UAAU;IACpBC,IAAI,EAAE,KAAK;IACXC,OAAO,EAAE,OAAO;IAChBC,SAAS,EAAE,QAAQ;IACnBC,OAAO,EAAE,OAAO;IAChBC,KAAK,EAAE;EACT,CAAC;EACDb,IAAI,EAAE;IACJM,EAAE,EAAE,MAAM;IACVC,EAAE,EAAE,MAAM;IACVC,QAAQ,EAAE,UAAU;IACpBC,IAAI,EAAE,KAAK;IACXC,OAAO,EAAE,OAAO;IAChBC,SAAS,EAAE,QAAQ;IACnBC,OAAO,EAAE,OAAO;IAChBC,KAAK,EAAE;EACT;AACF,CAAC;AAED,MAAME,QAAQ,GAAG;EACf,CAAC,EAAE,OAAO;EACV,CAAC,EAAE,OAAO;EACV,CAAC,EAAE,OAAO;EACV,EAAE,EAAE,OAAO;EACX,EAAE,EAAE,OAAO;EACX,CAAC,EAAE,MAAM;EACT,CAAC,EAAE,MAAM;EACT,EAAE,EAAE,MAAM;EACV,EAAE,EAAE,MAAM;EACV,CAAC,EAAE,OAAO;EACV,CAAC,EAAE,OAAO;EACV,GAAG,EAAE,OAAO;EACZ,CAAC,EAAE,MAAM;EACT,CAAC,EAAE,OAAO;EACV,EAAE,EAAE,OAAO;EACX,EAAE,EAAE,OAAO;EACX,EAAE,EAAE,OAAO;EACX,EAAE,EAAE;AACN,CAAC;AAED,MAAMC,SAAS,GAAIC,MAAM,IAAK;EAC5B,IAAIA,MAAM,KAAK,CAAC,EAAE;IAChB;IACA,OAAOA,MAAM,GAAG,OAAO;EACzB;EAEA,MAAMC,CAAC,GAAGD,MAAM,GAAG,EAAE;EACrB,MAAME,CAAC,GAAIF,MAAM,GAAG,GAAG,GAAIC,CAAC;EAC5B,MAAME,CAAC,GAAGH,MAAM,IAAI,GAAG,GAAG,GAAG,GAAG,IAAI;EAEpC,IAAIF,QAAQ,CAACG,CAAC,CAAC,EAAE;IACf,OAAOH,QAAQ,CAACG,CAAC,CAAC;EACpB,CAAC,MAAM,IAAIH,QAAQ,CAACI,CAAC,CAAC,EAAE;IACtB,OAAOJ,QAAQ,CAACI,CAAC,CAAC;EACpB,CAAC,MAAM,IAAIC,CAAC,KAAK,IAAI,EAAE;IACrB,OAAOL,QAAQ,CAACK,CAAC,CAAC;EACpB;EACA,OAAO,EAAE;AACX,CAAC;AAED,MAAMC,aAAa,GAAGA,CAACC,WAAW,EAAEC,QAAQ,KAAK;EAC/C,MAAMN,MAAM,GAAGO,MAAM,CAACF,WAAW,CAAC;EAClC,MAAMG,MAAM,GAAGT,SAAS,CAACC,MAAM,CAAC;EAEhC,OAAOA,MAAM,GAAGQ,MAAM;AACxB,CAAC;AAED,OAAO,MAAMC,QAAQ,GAAG;EACtBL,aAAa;EAEbM,GAAG,EAAE/B,eAAe,CAAC;IACnBgC,MAAM,EAAE/B,SAAS;IACjBgC,YAAY,EAAE;EAChB,CAAC,CAAC;EAEFC,OAAO,EAAElC,eAAe,CAAC;IACvBgC,MAAM,EAAE3B,aAAa;IACrB4B,YAAY,EAAE,MAAM;IACpBE,gBAAgB,EAAGD,OAAO,IAAKA,OAAO,GAAG;EAC3C,CAAC,CAAC;EAEFE,KAAK,EAAEpC,eAAe,CAAC;IACrBgC,MAAM,EAAE1B,WAAW;IACnB2B,YAAY,EAAE;EAChB,CAAC,CAAC;EAEFI,GAAG,EAAErC,eAAe,CAAC;IACnBgC,MAAM,EAAEzB,SAAS;IACjB0B,YAAY,EAAE;EAChB,CAAC,CAAC;EAEFK,SAAS,EAAEtC,eAAe,CAAC;IACzBgC,MAAM,EAAEvB,eAAe;IACvBwB,YAAY,EAAE,MAAM;IACpBM,gBAAgB,EAAErB,yBAAyB;IAC3CsB,sBAAsB,EAAE;EAC1B,CAAC;AACH,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
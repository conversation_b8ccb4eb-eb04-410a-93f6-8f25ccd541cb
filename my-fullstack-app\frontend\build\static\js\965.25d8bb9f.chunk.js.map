{"version": 3, "file": "static/js/965.25d8bb9f.chunk.js", "mappings": "2NAoBA,MAkJA,EAlJiCA,KAC7B,MAAMC,GAAQC,EAAAA,EAAAA,QAAc,OACrBC,EAAOC,IAAYC,EAAAA,EAAAA,UAAiB,KACpCC,EAAcC,IAAmBF,EAAAA,EAAAA,UAAwB,OACzDG,EAAgBC,IAAqBJ,EAAAA,EAAAA,UAAuB,KAC5DK,EAAiBC,IAAsBN,EAAAA,EAAAA,UAAmB,KAC1DO,EAASC,IAAcR,EAAAA,EAAAA,WAAS,GAGjCS,GAAaC,EAAAA,EAAAA,cAAYC,UAC3B,IACI,MAAMC,QAAiBC,EAAAA,EAAUC,IAAoC,uBACrEf,EAASa,EAASG,KAAIC,IAAC,CAAOC,GAAID,EAAEC,GAAIC,KAAMF,EAAEE,SACpD,CAAE,MAAOC,GAAa,IAADC,EAAAC,EAAAC,EACJ,QAAbF,EAAAxB,EAAM2B,eAAO,IAAAH,GAAbA,EAAeI,KAAK,CAAEC,SAAU,QAASC,QAAS,mDAAYC,QAAsB,QAAdN,EAAAF,EAAMP,gBAAQ,IAAAS,GAAM,QAANC,EAAdD,EAAgBO,YAAI,IAAAN,OAAN,EAAdA,EAAsBO,UAAWV,EAAMU,SACjH,IACD,IAGGC,GAAsBpB,EAAAA,EAAAA,cAAYC,UACpC,IACI,MAAMoB,QAAoBC,EAAAA,GAAcC,oBACxC7B,EAAkB2B,EACtB,CAAE,MAAOZ,GAAa,IAADe,EAAAC,EAAAC,EACJ,QAAbF,EAAAtC,EAAM2B,eAAO,IAAAW,GAAbA,EAAeV,KAAK,CAAEC,SAAU,QAASC,QAAS,mDAAYC,QAAsB,QAAdQ,EAAAhB,EAAMP,gBAAQ,IAAAuB,GAAM,QAANC,EAAdD,EAAgBP,YAAI,IAAAQ,OAAN,EAAdA,EAAsBP,UAAWV,EAAMU,SACjH,IACD,IAGGQ,GAAuB3B,EAAAA,EAAAA,cAAYC,UACrCH,GAAW,GACX,IACI,MAAMuB,QAAoBC,EAAAA,GAAcM,mBAAmBC,GAC3DjC,EAAmByB,EACvB,CAAE,MAAOZ,GAAa,IAADqB,EAAAC,EAAAC,EACJ,QAAbF,EAAA5C,EAAM2B,eAAO,IAAAiB,GAAbA,EAAehB,KAAK,CAAEC,SAAU,QAASC,QAAS,mDAAYC,QAAsB,QAAdc,EAAAtB,EAAMP,gBAAQ,IAAA6B,GAAM,QAANC,EAAdD,EAAgBb,YAAI,IAAAc,OAAN,EAAdA,EAAsBb,UAAWV,EAAMU,UAC7GvB,EAAmB,GACvB,CAAC,QACGE,GAAW,EACf,IACD,KAEHmC,EAAAA,EAAAA,YAAU,KACNlC,IACAqB,MACD,CAACrB,EAAYqB,KAEhBa,EAAAA,EAAAA,YAAU,KACF1C,EACAoC,EAAqBpC,GAErBK,EAAmB,MAExB,CAACL,EAAcoC,IAElB,MA0BMO,EAAqBzC,EAAe0C,QAA2B,CAACC,EAAKC,KACvE,MAAMC,EAAWD,EAAWE,UAAY,qBAKxC,OAJKH,EAAIE,KACLF,EAAIE,GAAY,IAEnBF,EAAIE,GAA2BE,KAAKH,GAC9BD,IACR,CAAC,GAEJ,OACIK,EAAAA,EAAAA,MAAA,OAAKC,UAAU,iBAAgBC,SAAA,EAC3BC,EAAAA,EAAAA,KAACC,EAAAA,EAAK,CAACC,IAAK5D,KACZ0D,EAAAA,EAAAA,KAAA,OAAKF,UAAU,WAAUC,UACrBF,EAAAA,EAAAA,MAACM,EAAAA,EAAI,CAACC,MAAM,2BAAML,SAAA,EACdF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,sBAAqBC,SAAA,EAChCC,EAAAA,EAAAA,KAAA,SAAOK,QAAQ,OAAOP,UAAU,kBAAiBC,SAAC,8BAClDC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,mBAAkBC,UAC7BC,EAAAA,EAAAA,KAACM,EAAAA,EAAQ,CACL3C,GAAG,OACH4C,MAAO5D,EACP6D,QAAShE,EACTiE,SAAWC,GAA2B9D,EAAgB8D,EAAEH,OACxDI,YAAY,OACZC,YAAY,KACZC,YAAY,6CACZC,MAAO,CAAEC,MAAO,eAK3BpE,GAAgBqE,OAAOC,KAAK3B,GAAoB7B,KAAIiC,IACjDG,EAAAA,EAAAA,MAAA,OAAoBC,UAAU,eAAcC,SAAA,EACxCC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,yBAAwBC,SAAEL,KACxCM,EAAAA,EAAAA,KAAA,OAAKF,UAAU,SAAQC,UACjBT,EAAmBI,IAAa,IAAIjC,KAAIgC,IACtCI,EAAAA,EAAAA,MAAA,OAA2BC,UAAU,kBAAiBC,SAAA,EAClDC,EAAAA,EAAAA,KAACkB,EAAAA,EAAQ,CACLC,QAAS1B,EAAW2B,KACpBC,QAAStE,EAAgBuE,SAAS7B,EAAW2B,MAC7CX,SAAWC,IAA2Ba,OAjE9CC,EAiEqE/B,EAAW2B,KAjExDK,IAiEgEf,EAAEW,aAhEtHrE,GAAmB0E,GACXD,EACO,IAAIC,EAAWF,GAEfE,EAAUC,QAAOC,GAAQA,IAASJ,MALtBD,IAACC,EAAwBC,MAmEpB5B,EAAAA,EAAAA,MAAA,SAAOQ,QAASZ,EAAW2B,KAAMtB,UAAU,SAAQC,SAAA,CAC9CN,EAAWoC,KAAK,KAAGpC,EAAW2B,KAAK,SAPlC3B,EAAW2B,YAJvB1B,MAmBdM,EAAAA,EAAAA,KAAA,OAAKF,UAAU,yBAAwBC,UACnCC,EAAAA,EAAAA,KAAC8B,EAAAA,EAAM,CACHC,MAAM,2BACNC,KAAK,cACLC,QAtEE5E,UACtB,GAAKV,EAAL,CACAO,GAAW,GAEX,IAAK,IAADgF,QACMxD,EAAAA,GAAcyD,sBAAsBxF,EAAcI,GAC3C,QAAbmF,EAAA5F,EAAM2B,eAAO,IAAAiE,GAAbA,EAAehE,KAAK,CAAEC,SAAU,UAAWC,QAAS,uCAAUC,OAAQ,KACtEU,EAAqBpC,EACzB,CAAE,MAAOkB,GAAa,IAADuE,EAAAC,EAAAC,EACJ,QAAbF,EAAA9F,EAAM2B,eAAO,IAAAmE,GAAbA,EAAelE,KAAK,CAAEC,SAAU,QAASC,QAAS,uCAAUC,QAAsB,QAAdgE,EAAAxE,EAAMP,gBAAQ,IAAA+E,GAAM,QAANC,EAAdD,EAAgB/D,YAAI,IAAAgE,OAAN,EAAdA,EAAsB/D,UAAWV,EAAMU,SAC/G,CAAC,QACGrB,GAAW,EACf,CAXyB,GAsELqF,UAAW5F,GAAgBM,EAC3BA,QAASA,c,4GCpJrC,SAASuF,IACP,OAAOA,EAAWxB,OAAOyB,OAASzB,OAAOyB,OAAOC,OAAS,SAAUC,GACjE,IAAK,IAAIjC,EAAI,EAAGA,EAAIkC,UAAUC,OAAQnC,IAAK,CACzC,IAAIoC,EAAIF,UAAUlC,GAClB,IAAK,IAAIhD,KAAKoF,GAAG,CAAG,GAAEC,eAAeC,KAAKF,EAAGpF,KAAOiF,EAAEjF,GAAKoF,EAAEpF,GAC/D,CACA,OAAOiF,CACT,EAAGH,EAASS,MAAM,KAAML,UAC1B,CAEA,SAASM,EAAQC,GAGf,OAAOD,EAAU,mBAAqBE,QAAU,iBAAmBA,OAAOC,SAAW,SAAUF,GAC7F,cAAcA,CAChB,EAAI,SAAUA,GACZ,OAAOA,GAAK,mBAAqBC,QAAUD,EAAEG,cAAgBF,QAAUD,IAAMC,OAAOG,UAAY,gBAAkBJ,CACpH,EAAGD,EAAQC,EACb,CAaA,SAASK,EAAcV,GACrB,IAAIW,EAZN,SAAqBX,EAAGpF,GACtB,GAAI,UAAYwF,EAAQJ,KAAOA,EAAG,OAAOA,EACzC,IAAIpC,EAAIoC,EAAEM,OAAOM,aACjB,QAAI,IAAWhD,EAAG,CAChB,IAAI+C,EAAI/C,EAAEsC,KAAKF,EAAGpF,GAAK,WACvB,GAAI,UAAYwF,EAAQO,GAAI,OAAOA,EACnC,MAAM,IAAIE,UAAU,+CACtB,CACA,OAAQ,WAAajG,EAAIkG,OAASC,QAAQf,EAC5C,CAGUY,CAAYZ,EAAG,UACvB,MAAO,UAAYI,EAAQO,GAAKA,EAAIA,EAAI,EAC1C,CAEA,SAASK,EAAgBpD,EAAGhD,EAAGoF,GAC7B,OAAQpF,EAAI8F,EAAc9F,MAAOgD,EAAIM,OAAO+C,eAAerD,EAAGhD,EAAG,CAC/D6C,MAAOuC,EACPkB,YAAY,EACZC,cAAc,EACdC,UAAU,IACPxD,EAAEhD,GAAKoF,EAAGpC,CACjB,CAkCA,SAASyD,EAAkBzG,EAAG0G,IAC3B,MAAQA,GAAKA,EAAI1G,EAAEmF,UAAYuB,EAAI1G,EAAEmF,QACtC,IAAK,IAAInC,EAAI,EAAGiC,EAAI0B,MAAMD,GAAI1D,EAAI0D,EAAG1D,IAAKiC,EAAEjC,GAAKhD,EAAEgD,GACnD,OAAOiC,CACT,CAcA,SAAS2B,EAAe5G,EAAGgD,GACzB,OAnDF,SAAyBhD,GACvB,GAAI2G,MAAME,QAAQ7G,GAAI,OAAOA,CAC/B,CAiDS8G,CAAgB9G,IA/CzB,SAA+BA,EAAG+G,GAChC,IAAI3B,EAAI,MAAQpF,EAAI,KAAO,oBAAsB0F,QAAU1F,EAAE0F,OAAOC,WAAa3F,EAAE,cACnF,GAAI,MAAQoF,EAAG,CACb,IAAIpC,EACFiC,EACAc,EACAiB,EACAN,EAAI,GACJO,GAAI,EACJxB,GAAI,EACN,IACE,GAAIM,GAAKX,EAAIA,EAAEE,KAAKtF,IAAIkH,KAAM,IAAMH,EAAG,CACrC,GAAIzD,OAAO8B,KAAOA,EAAG,OACrB6B,GAAI,CACN,MAAO,OAASA,GAAKjE,EAAI+C,EAAET,KAAKF,IAAI+B,QAAUT,EAAExE,KAAKc,EAAEH,OAAQ6D,EAAEvB,SAAW4B,GAAIE,GAAI,GACtF,CAAE,MAAOjH,GACPyF,GAAI,EAAIR,EAAIjF,CACd,CAAE,QACA,IACE,IAAKiH,GAAK,MAAQ7B,EAAU,SAAM4B,EAAI5B,EAAU,SAAK9B,OAAO0D,KAAOA,GAAI,MACzE,CAAE,QACA,GAAIvB,EAAG,MAAMR,CACf,CACF,CACA,OAAOyB,CACT,CACF,CAqB+BU,CAAsBpH,EAAGgD,IAbxD,SAAqChD,EAAG0G,GACtC,GAAI1G,EAAG,CACL,GAAI,iBAAmBA,EAAG,OAAOyG,EAAkBzG,EAAG0G,GACtD,IAAItB,EAAI,CAAC,EAAEiC,SAAS/B,KAAKtF,GAAGsH,MAAM,GAAI,GACtC,MAAO,WAAalC,GAAKpF,EAAE4F,cAAgBR,EAAIpF,EAAE4F,YAAY1F,MAAO,QAAUkF,GAAK,QAAUA,EAAIuB,MAAMY,KAAKvH,GAAK,cAAgBoF,GAAK,2CAA2CoC,KAAKpC,GAAKqB,EAAkBzG,EAAG0G,QAAK,CACvN,CACF,CAO8De,CAA4BzH,EAAGgD,IAL7F,WACE,MAAM,IAAIiD,UAAU,4IACtB,CAGmGyB,EACnG,CAEA,IAAIC,EAAU,CACZC,IAAK,iBACLC,MAAO,mBACPvD,KAAM,kBACNwD,KAAM,SAAcC,GAClB,IAAIC,EAAQD,EAAKC,MACfrE,EAAUoE,EAAKpE,QACfsE,EAAUF,EAAKE,QACjB,OAAOC,EAAAA,EAAAA,IAAW,yBAA0B,CAC1C,cAAevE,EACf,aAAcqE,EAAMnD,SACpB,YAAamD,EAAMG,QACnB,mBAAoBH,EAAMI,QAA4B,WAAlBJ,EAAMI,QAAuBH,GAAkC,WAAvBA,EAAQI,YAExF,GAEEC,EAAeC,EAAAA,EAAcC,OAAO,CACtCC,aAAc,CACZC,OAAQ,WACRC,WAAW,EACXhF,SAAS,EACTvB,UAAW,KACXyC,UAAU,EACV+D,YAAY,EACZtE,KAAM,KACNrE,GAAI,KACJwD,QAAS,KACToF,SAAU,KACVV,SAAS,EACTC,QAAS,KACTlI,KAAM,KACN6C,SAAU,KACV+F,cAAe,KACfC,YAAa,KACbC,UAAU,EACVC,UAAU,EACV7F,MAAO,KACP8F,SAAU,KACVC,QAAS,KACTC,eAAgB,KAChBC,WAAW,EACXxG,MAAO,KACPR,cAAUiH,GAEZC,IAAK,CACH5B,QAASA,KAIb,SAAS6B,EAAQxG,EAAGhD,GAAK,IAAIoF,EAAI9B,OAAOC,KAAKP,GAAI,GAAIM,OAAOmG,sBAAuB,CAAE,IAAIhE,EAAInC,OAAOmG,sBAAsBzG,GAAIhD,IAAMyF,EAAIA,EAAExB,QAAO,SAAUjE,GAAK,OAAOsD,OAAOoG,yBAAyB1G,EAAGhD,GAAGsG,UAAY,KAAKlB,EAAElD,KAAKqD,MAAMH,EAAGK,EAAI,CAAE,OAAOL,CAAG,CAC9P,SAASuE,EAAc3G,GAAK,IAAK,IAAIhD,EAAI,EAAGA,EAAIkF,UAAUC,OAAQnF,IAAK,CAAE,IAAIoF,EAAI,MAAQF,UAAUlF,GAAKkF,UAAUlF,GAAK,CAAC,EAAGA,EAAI,EAAIwJ,EAAQlG,OAAO8B,IAAI,GAAIwE,SAAQ,SAAU5J,GAAKoG,EAAgBpD,EAAGhD,EAAGoF,EAAEpF,GAAK,IAAKsD,OAAOuG,0BAA4BvG,OAAOwG,iBAAiB9G,EAAGM,OAAOuG,0BAA0BzE,IAAMoE,EAAQlG,OAAO8B,IAAIwE,SAAQ,SAAU5J,GAAKsD,OAAO+C,eAAerD,EAAGhD,EAAGsD,OAAOoG,yBAAyBtE,EAAGpF,GAAK,GAAI,CAAE,OAAOgD,CAAG,CACtb,IAAIQ,EAAwBuG,EAAAA,KAAwBA,EAAAA,YAAiB,SAAUC,EAASxH,GACtF,IAAIyH,GAAaC,EAAAA,EAAAA,MACbjC,EAAU8B,EAAAA,WAAiBI,EAAAA,IAC3BnC,EAAQM,EAAa8B,SAASJ,EAAS/B,GAEzCoC,EAAmBzD,EADCmD,EAAAA,UAAe,GACgB,GACnDO,EAAeD,EAAiB,GAChCE,EAAkBF,EAAiB,GACjCG,EAAwBlC,EAAamC,YAAY,CACjDzC,MAAOA,EACP0C,MAAO,CACLC,QAASL,GAEXrC,QAAS,CACPtE,QAASqE,EAAMrE,UAAYqE,EAAMqB,UACjCxE,SAAUmD,EAAMnD,YAGpB+F,EAAMJ,EAAsBI,IAC5BC,EAAKL,EAAsBK,GAC3BC,EAAaN,EAAsBM,YACrCC,EAAAA,EAAAA,GAAezC,EAAaiB,IAAIyB,OAAQF,EAAY,CAClD5K,KAAM,aAER,IAAI+K,EAAalB,EAAAA,OAAa,MAC1BlB,EAAWkB,EAAAA,OAAa/B,EAAMa,UAC9B9E,EAAY,WACd,OAAOiE,EAAMrE,UAAYqE,EAAMqB,SACjC,EA8CAU,EAAAA,oBAA0BvH,GAAK,WAC7B,MAAO,CACLwF,MAAOA,EACPkD,MAAO,WACL,OAAOC,EAAAA,GAAWD,MAAMrC,EAAStI,QACnC,EACA6K,WAAY,WACV,OAAOH,EAAW1K,OACpB,EACA8K,SAAU,WACR,OAAOxC,EAAStI,OAClB,EAEJ,IACAwJ,EAAAA,WAAgB,WACduB,EAAAA,GAAYC,aAAa1C,EAAUb,EAAMa,SAC3C,GAAG,CAACA,EAAUb,EAAMa,YACpB2C,EAAAA,EAAAA,KAAgB,WACd3C,EAAStI,QAAQoD,QAAUI,GAC7B,GAAG,CAACiE,EAAMrE,QAASqE,EAAMqB,aACzBoC,EAAAA,EAAAA,KAAe,WACTzD,EAAMW,WACRwC,EAAAA,GAAWD,MAAMrC,EAAStI,QAASyH,EAAMW,UAE7C,IACA,IAAIhF,EAAUI,IACV2H,EAAaJ,EAAAA,GAAYK,WAAW3D,EAAMmB,SAC1CyC,EAAatD,EAAauD,cAAc7D,GACxC8D,EAAY7B,EAAW,CACzBhK,GAAI+H,EAAM/H,GACVmC,WAAW8F,EAAAA,EAAAA,IAAWF,EAAM5F,UAAWyI,EAAG,OAAQ,CAChDlH,QAASA,EACTsE,QAASA,KAEX7E,MAAO4E,EAAM5E,MACb,mBAAoBO,EACpB,kBAAmBqE,EAAMnD,SACzBiE,cAAed,EAAMc,cACrBC,YAAaf,EAAMe,aAClB6C,EAAYhB,EAAI,SA8CnB,OAAoBb,EAAAA,cAAoBA,EAAAA,SAAgB,KAAmBA,EAAAA,cAAoB,MAAOjF,EAAS,CAC7GtC,IAAKyI,GACJa,GA/CsB,WACvB,IAAIC,EAAYT,EAAAA,GAAYU,WAAWJ,EAAYT,EAAAA,GAAWc,YAC1DC,EAAajC,EAAWN,EAAc,CACxC1J,GAAI+H,EAAMvE,QACV0I,KAAM,WACN/J,UAAWyI,EAAG,SACd3K,KAAM8H,EAAM9H,KACZgJ,SAAUlB,EAAMkB,SAChBkD,QAAS,SAAiBpJ,GACxB,OA3DS,SAAiBqJ,GAC9B,IAAIC,EACJ/B,GAAgB,GACN,OAAVvC,QAA4B,IAAVA,GAAyD,QAApCsE,EAAiBtE,EAAMoE,eAAwC,IAAnBE,GAA6BA,EAAehH,KAAK0C,EAAOqE,EAC7I,CAuDaE,CAASvJ,EAClB,EACAwJ,OAAQ,SAAgBxJ,GACtB,OAzDQ,SAAgBqJ,GAC5B,IAAII,EACJlC,GAAgB,GACN,OAAVvC,QAA4B,IAAVA,GAAuD,QAAlCyE,EAAgBzE,EAAMwE,cAAsC,IAAlBC,GAA4BA,EAAcnH,KAAK0C,EAAOqE,EACzI,CAqDaK,CAAQ1J,EACjB,EACAD,SAAU,SAAkBC,GAC1B,OApGU,SAAkBqJ,GAChC,IAAIrE,EAAMnD,WAAYmD,EAAMgB,UAGxBhB,EAAMjF,SAAU,CAClB,IAAI4J,EAEA9J,EADWkB,IACQiE,EAAMY,WAAaZ,EAAMqB,UAC5CuD,EAAY,CACdC,cAAeR,EACfxJ,MAAOmF,EAAMnF,MACbc,QAASd,EACTiK,gBAAiB,WACL,OAAVT,QAA4B,IAAVA,GAAoBA,EAAMS,iBAC9C,EACAC,eAAgB,WACJ,OAAVV,QAA4B,IAAVA,GAAoBA,EAAMU,gBAC9C,EACAC,OAAQ,CACNb,KAAM,WACNjM,KAAM8H,EAAM9H,KACZD,GAAI+H,EAAM/H,GACV4C,MAAOmF,EAAMnF,MACbc,QAASd,IAMb,GAHU,OAAVmF,QAA4B,IAAVA,GAA2D,QAAtC2E,EAAkB3E,EAAMjF,gBAA0C,IAApB4J,GAA8BA,EAAgBrH,KAAK0C,EAAO4E,GAG3IP,EAAMY,iBACR,OAEF9B,EAAAA,GAAWD,MAAMrC,EAAStI,QAC5B,CACF,CAkEa2M,CAAUlK,EACnB,EACA6B,SAAUmD,EAAMnD,SAChBmE,SAAUhB,EAAMgB,SAChBC,SAAUjB,EAAMiB,SAChB,eAAgBjB,EAAMG,QACtBxE,QAASA,GACRoI,GAAYnB,EAAI,UACnB,OAAoBb,EAAAA,cAAoB,QAASjF,EAAS,CACxDtC,IAAKqG,GACJqD,GACL,CAqBeiB,GApBQ,WACrB,IAAIC,EAAYnD,EAAW,CACzB7H,UAAWyI,EAAG,SACbD,EAAI,SACHyC,EAAWpD,EAAW,CACxB7H,UAAWyI,EAAG,MAAO,CACnBlH,QAASA,IAEX,mBAAoBA,EACpB,kBAAmBqE,EAAMnD,UACxB+F,EAAI,QACHtG,EAAOX,EAAUqE,EAAM1D,MAAqByF,EAAAA,cAAoBuD,EAAAA,EAAWF,GAAa,KACxFG,EAAeC,EAAAA,GAAUC,WAAWnJ,EAAMqF,EAAc,CAAC,EAAGyD,GAAY,CAC1EpF,MAAOA,EACPrE,QAASA,IAEX,OAAoBoG,EAAAA,cAAoB,MAAOsD,EAAUE,EAC3D,CAGqCG,IAAqBhC,GAA2B3B,EAAAA,cAAoB4D,EAAAA,EAAS7I,EAAS,CACzHkI,OAAQ/B,EACR2C,QAAS5F,EAAMmB,QACf0E,GAAIjD,EAAI,YACP5C,EAAMoB,iBACX,KACA5F,EAASsK,YAAc,U", "sources": ["components/Page/PermissionPage.tsx", "../node_modules/primereact/checkbox/checkbox.esm.js"], "sourcesContent": ["import React, { useState, useEffect, useCallback, useRef } from 'react';\nimport { Dropdown, DropdownChangeEvent } from 'primereact/dropdown';\nimport { Checkbox, CheckboxChangeEvent } from 'primereact/checkbox';\nimport { Button } from 'primereact/button';\nimport { Toast } from 'primereact/toast';\nimport { Card } from 'primereact/card';\nimport { PermissionApi } from '../../services/apiService'; // 導入新的 PermissionApi\nimport { Permission } from '../../types/api'; // 導入 Permission 類型\nimport { apiClient } from '../../services/api'; // 導入 apiClient\n\n// Type Definitions\ninterface Role {\n    id: number;\n    name: string;\n}\n\ninterface GroupedPermissions {\n    [category: string]: Permission[];\n}\n\nconst PermissionPage: React.FC = () => {\n    const toast = useRef<Toast>(null);\n    const [roles, setRoles] = useState<Role[]>([]);\n    const [selectedRole, setSelectedRole] = useState<number | null>(null);\n    const [allPermissions, setAllPermissions] = useState<Permission[]>([]); // 所有可用的細粒度權限\n    const [rolePermissions, setRolePermissions] = useState<string[]>([]); // 當前角色擁有的權限代碼列表\n    const [loading, setLoading] = useState(false);\n\n    // 獲取所有角色\n    const fetchRoles = useCallback(async () => {\n        try {\n            const response = await apiClient.get<{ id: number; name: string }[]>('/api/users/GetRoles'); \n            setRoles(response.map(r => ({ id: r.id, name: r.name })));\n        } catch (error: any) {\n            toast.current?.show({ severity: 'error', summary: '獲取角色列表失敗', detail: error.response?.data?.message || error.message });\n        }\n    }, []);\n\n    // 獲取所有權限定義\n    const fetchAllPermissions = useCallback(async () => {\n        try {\n            const permissions = await PermissionApi.getAllPermissions();\n            setAllPermissions(permissions);\n        } catch (error: any) {\n            toast.current?.show({ severity: 'error', summary: '獲取所有權限失敗', detail: error.response?.data?.message || error.message });\n        }\n    }, []);\n\n    // 獲取指定角色的權限\n    const fetchRolePermissions = useCallback(async (roleId: number) => {\n        setLoading(true);\n        try {\n            const permissions = await PermissionApi.getRolePermissions(roleId);\n            setRolePermissions(permissions);\n        } catch (error: any) {\n            toast.current?.show({ severity: 'error', summary: '獲取角色權限失敗', detail: error.response?.data?.message || error.message });\n            setRolePermissions([]);\n        } finally {\n            setLoading(false);\n        }\n    }, []);\n\n    useEffect(() => {\n        fetchRoles();\n        fetchAllPermissions(); // 頁面載入時獲取所有權限定義\n    }, [fetchRoles, fetchAllPermissions]);\n\n    useEffect(() => {\n        if (selectedRole) {\n            fetchRolePermissions(selectedRole);\n        } else {\n            setRolePermissions([]);\n        }\n    }, [selectedRole, fetchRolePermissions]);\n\n    const handlePermissionChange = (permissionCode: string, isChecked: boolean) => {\n        setRolePermissions(prevCodes => {\n            if (isChecked) {\n                return [...prevCodes, permissionCode];\n            } else {\n                return prevCodes.filter(code => code !== permissionCode);\n            }\n        });\n    };\n    \n    const handleSaveChanges = async () => {\n        if (!selectedRole) return;\n        setLoading(true);\n\n        try {\n            await PermissionApi.updateRolePermissions(selectedRole, rolePermissions);\n            toast.current?.show({ severity: 'success', summary: '權限更新成功', detail: \"\" });\n            fetchRolePermissions(selectedRole); // 重新獲取以確保狀態同步\n        } catch (error: any) {\n            toast.current?.show({ severity: 'error', summary: '權限更新失敗', detail: error.response?.data?.message || error.message });\n        } finally {\n            setLoading(false);\n        }\n    };\n\n    // 將權限按類別分組\n    const groupedPermissions = allPermissions.reduce<GroupedPermissions>((acc, permission) => {\n        const category = permission.Category || '未分類';\n        if (!acc[category]) {\n            acc[category] = [];\n        }\n        (acc[category] as Permission[]).push(permission);\n        return acc;\n    }, {});\n\n    return (\n        <div className=\"p-grid p-fluid\">\n            <Toast ref={toast} />\n            <div className=\"p-col-12\">\n                <Card title=\"權限管理\">\n                    <div className=\"p-field p-grid mb-4\">\n                        <label htmlFor=\"role\" className=\"p-col-12 p-md-2\">選擇角色</label>\n                        <div className=\"p-col-12 p-md-10\">\n                            <Dropdown\n                                id=\"role\"\n                                value={selectedRole}\n                                options={roles}\n                                onChange={(e: DropdownChangeEvent) => setSelectedRole(e.value)}\n                                optionLabel=\"name\"\n                                optionValue=\"id\"\n                                placeholder=\"請選擇一個角色\"\n                                style={{ width: '100%' }}\n                            />\n                        </div>\n                    </div>\n\n                    {selectedRole && Object.keys(groupedPermissions).map(category => (\n                        <div key={category} className=\"p-field mb-4\">\n                            <h5 className=\"text-lg font-bold mb-2\">{category}</h5>\n                            <div className=\"p-grid\">\n                                {(groupedPermissions[category] || []).map(permission => (\n                                    <div key={permission.Code} className=\"p-col-12 p-md-4\">\n                                        <Checkbox\n                                            inputId={permission.Code}\n                                            checked={rolePermissions.includes(permission.Code)}\n                                            onChange={(e: CheckboxChangeEvent) => handlePermissionChange(permission.Code, !!e.checked)}\n                                        />\n                                        <label htmlFor={permission.Code} className=\"p-ml-2\">\n                                            {permission.Name} ({permission.Code})\n                                        </label>\n                                    </div>\n                                ))}\n                            </div>\n                        </div>\n                    ))}\n\n                    <div className=\"p-d-flex p-jc-end mt-4\">\n                        <Button\n                            label=\"儲存變更\"\n                            icon=\"pi pi-check\"\n                            onClick={handleSaveChanges}\n                            disabled={!selectedRole || loading}\n                            loading={loading}\n                        />\n                    </div>\n                </Card>\n            </div>\n        </div>\n    );\n};\n\nexport default PermissionPage;", "'use client';\nimport * as React from 'react';\nimport { PrimeReactContext } from 'primereact/api';\nimport { ComponentBase, useHandleStyle } from 'primereact/componentbase';\nimport { useMergeProps, useUpdateEffect, useMountEffect } from 'primereact/hooks';\nimport { CheckIcon } from 'primereact/icons/check';\nimport { Tooltip } from 'primereact/tooltip';\nimport { classNames, DomHandler, ObjectUtils, IconUtils } from 'primereact/utils';\n\nfunction _extends() {\n  return _extends = Object.assign ? Object.assign.bind() : function (n) {\n    for (var e = 1; e < arguments.length; e++) {\n      var t = arguments[e];\n      for (var r in t) ({}).hasOwnProperty.call(t, r) && (n[r] = t[r]);\n    }\n    return n;\n  }, _extends.apply(null, arguments);\n}\n\nfunction _typeof(o) {\n  \"@babel/helpers - typeof\";\n\n  return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (o) {\n    return typeof o;\n  } : function (o) {\n    return o && \"function\" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? \"symbol\" : typeof o;\n  }, _typeof(o);\n}\n\nfunction toPrimitive(t, r) {\n  if (\"object\" != _typeof(t) || !t) return t;\n  var e = t[Symbol.toPrimitive];\n  if (void 0 !== e) {\n    var i = e.call(t, r || \"default\");\n    if (\"object\" != _typeof(i)) return i;\n    throw new TypeError(\"@@toPrimitive must return a primitive value.\");\n  }\n  return (\"string\" === r ? String : Number)(t);\n}\n\nfunction toPropertyKey(t) {\n  var i = toPrimitive(t, \"string\");\n  return \"symbol\" == _typeof(i) ? i : i + \"\";\n}\n\nfunction _defineProperty(e, r, t) {\n  return (r = toPropertyKey(r)) in e ? Object.defineProperty(e, r, {\n    value: t,\n    enumerable: !0,\n    configurable: !0,\n    writable: !0\n  }) : e[r] = t, e;\n}\n\nfunction _arrayWithHoles(r) {\n  if (Array.isArray(r)) return r;\n}\n\nfunction _iterableToArrayLimit(r, l) {\n  var t = null == r ? null : \"undefined\" != typeof Symbol && r[Symbol.iterator] || r[\"@@iterator\"];\n  if (null != t) {\n    var e,\n      n,\n      i,\n      u,\n      a = [],\n      f = !0,\n      o = !1;\n    try {\n      if (i = (t = t.call(r)).next, 0 === l) {\n        if (Object(t) !== t) return;\n        f = !1;\n      } else for (; !(f = (e = i.call(t)).done) && (a.push(e.value), a.length !== l); f = !0);\n    } catch (r) {\n      o = !0, n = r;\n    } finally {\n      try {\n        if (!f && null != t[\"return\"] && (u = t[\"return\"](), Object(u) !== u)) return;\n      } finally {\n        if (o) throw n;\n      }\n    }\n    return a;\n  }\n}\n\nfunction _arrayLikeToArray(r, a) {\n  (null == a || a > r.length) && (a = r.length);\n  for (var e = 0, n = Array(a); e < a; e++) n[e] = r[e];\n  return n;\n}\n\nfunction _unsupportedIterableToArray(r, a) {\n  if (r) {\n    if (\"string\" == typeof r) return _arrayLikeToArray(r, a);\n    var t = {}.toString.call(r).slice(8, -1);\n    return \"Object\" === t && r.constructor && (t = r.constructor.name), \"Map\" === t || \"Set\" === t ? Array.from(r) : \"Arguments\" === t || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t) ? _arrayLikeToArray(r, a) : void 0;\n  }\n}\n\nfunction _nonIterableRest() {\n  throw new TypeError(\"Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\");\n}\n\nfunction _slicedToArray(r, e) {\n  return _arrayWithHoles(r) || _iterableToArrayLimit(r, e) || _unsupportedIterableToArray(r, e) || _nonIterableRest();\n}\n\nvar classes = {\n  box: 'p-checkbox-box',\n  input: 'p-checkbox-input',\n  icon: 'p-checkbox-icon',\n  root: function root(_ref) {\n    var props = _ref.props,\n      checked = _ref.checked,\n      context = _ref.context;\n    return classNames('p-checkbox p-component', {\n      'p-highlight': checked,\n      'p-disabled': props.disabled,\n      'p-invalid': props.invalid,\n      'p-variant-filled': props.variant ? props.variant === 'filled' : context && context.inputStyle === 'filled'\n    });\n  }\n};\nvar CheckboxBase = ComponentBase.extend({\n  defaultProps: {\n    __TYPE: 'Checkbox',\n    autoFocus: false,\n    checked: false,\n    className: null,\n    disabled: false,\n    falseValue: false,\n    icon: null,\n    id: null,\n    inputId: null,\n    inputRef: null,\n    invalid: false,\n    variant: null,\n    name: null,\n    onChange: null,\n    onContextMenu: null,\n    onMouseDown: null,\n    readOnly: false,\n    required: false,\n    style: null,\n    tabIndex: null,\n    tooltip: null,\n    tooltipOptions: null,\n    trueValue: true,\n    value: null,\n    children: undefined\n  },\n  css: {\n    classes: classes\n  }\n});\n\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nvar Checkbox = /*#__PURE__*/React.memo(/*#__PURE__*/React.forwardRef(function (inProps, ref) {\n  var mergeProps = useMergeProps();\n  var context = React.useContext(PrimeReactContext);\n  var props = CheckboxBase.getProps(inProps, context);\n  var _React$useState = React.useState(false),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    focusedState = _React$useState2[0],\n    setFocusedState = _React$useState2[1];\n  var _CheckboxBase$setMeta = CheckboxBase.setMetaData({\n      props: props,\n      state: {\n        focused: focusedState\n      },\n      context: {\n        checked: props.checked === props.trueValue,\n        disabled: props.disabled\n      }\n    }),\n    ptm = _CheckboxBase$setMeta.ptm,\n    cx = _CheckboxBase$setMeta.cx,\n    isUnstyled = _CheckboxBase$setMeta.isUnstyled;\n  useHandleStyle(CheckboxBase.css.styles, isUnstyled, {\n    name: 'checkbox'\n  });\n  var elementRef = React.useRef(null);\n  var inputRef = React.useRef(props.inputRef);\n  var isChecked = function isChecked() {\n    return props.checked === props.trueValue;\n  };\n  var _onChange = function onChange(event) {\n    if (props.disabled || props.readOnly) {\n      return;\n    }\n    if (props.onChange) {\n      var _props$onChange;\n      var _checked = isChecked();\n      var value = _checked ? props.falseValue : props.trueValue;\n      var eventData = {\n        originalEvent: event,\n        value: props.value,\n        checked: value,\n        stopPropagation: function stopPropagation() {\n          event === null || event === void 0 || event.stopPropagation();\n        },\n        preventDefault: function preventDefault() {\n          event === null || event === void 0 || event.preventDefault();\n        },\n        target: {\n          type: 'checkbox',\n          name: props.name,\n          id: props.id,\n          value: props.value,\n          checked: value\n        }\n      };\n      props === null || props === void 0 || (_props$onChange = props.onChange) === null || _props$onChange === void 0 || _props$onChange.call(props, eventData);\n\n      // do not continue if the user defined click wants to prevent\n      if (event.defaultPrevented) {\n        return;\n      }\n      DomHandler.focus(inputRef.current);\n    }\n  };\n  var _onFocus = function onFocus(event) {\n    var _props$onFocus;\n    setFocusedState(true);\n    props === null || props === void 0 || (_props$onFocus = props.onFocus) === null || _props$onFocus === void 0 || _props$onFocus.call(props, event);\n  };\n  var _onBlur = function onBlur(event) {\n    var _props$onBlur;\n    setFocusedState(false);\n    props === null || props === void 0 || (_props$onBlur = props.onBlur) === null || _props$onBlur === void 0 || _props$onBlur.call(props, event);\n  };\n  React.useImperativeHandle(ref, function () {\n    return {\n      props: props,\n      focus: function focus() {\n        return DomHandler.focus(inputRef.current);\n      },\n      getElement: function getElement() {\n        return elementRef.current;\n      },\n      getInput: function getInput() {\n        return inputRef.current;\n      }\n    };\n  });\n  React.useEffect(function () {\n    ObjectUtils.combinedRefs(inputRef, props.inputRef);\n  }, [inputRef, props.inputRef]);\n  useUpdateEffect(function () {\n    inputRef.current.checked = isChecked();\n  }, [props.checked, props.trueValue]);\n  useMountEffect(function () {\n    if (props.autoFocus) {\n      DomHandler.focus(inputRef.current, props.autoFocus);\n    }\n  });\n  var checked = isChecked();\n  var hasTooltip = ObjectUtils.isNotEmpty(props.tooltip);\n  var otherProps = CheckboxBase.getOtherProps(props);\n  var rootProps = mergeProps({\n    id: props.id,\n    className: classNames(props.className, cx('root', {\n      checked: checked,\n      context: context\n    })),\n    style: props.style,\n    'data-p-highlight': checked,\n    'data-p-disabled': props.disabled,\n    onContextMenu: props.onContextMenu,\n    onMouseDown: props.onMouseDown\n  }, otherProps, ptm('root'));\n  var createInputElement = function createInputElement() {\n    var ariaProps = ObjectUtils.reduceKeys(otherProps, DomHandler.ARIA_PROPS);\n    var inputProps = mergeProps(_objectSpread({\n      id: props.inputId,\n      type: 'checkbox',\n      className: cx('input'),\n      name: props.name,\n      tabIndex: props.tabIndex,\n      onFocus: function onFocus(e) {\n        return _onFocus(e);\n      },\n      onBlur: function onBlur(e) {\n        return _onBlur(e);\n      },\n      onChange: function onChange(e) {\n        return _onChange(e);\n      },\n      disabled: props.disabled,\n      readOnly: props.readOnly,\n      required: props.required,\n      'aria-invalid': props.invalid,\n      checked: checked\n    }, ariaProps), ptm('input'));\n    return /*#__PURE__*/React.createElement(\"input\", _extends({\n      ref: inputRef\n    }, inputProps));\n  };\n  var createBoxElement = function createBoxElement() {\n    var iconProps = mergeProps({\n      className: cx('icon')\n    }, ptm('icon'));\n    var boxProps = mergeProps({\n      className: cx('box', {\n        checked: checked\n      }),\n      'data-p-highlight': checked,\n      'data-p-disabled': props.disabled\n    }, ptm('box'));\n    var icon = checked ? props.icon || /*#__PURE__*/React.createElement(CheckIcon, iconProps) : null;\n    var checkboxIcon = IconUtils.getJSXIcon(icon, _objectSpread({}, iconProps), {\n      props: props,\n      checked: checked\n    });\n    return /*#__PURE__*/React.createElement(\"div\", boxProps, checkboxIcon);\n  };\n  return /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(\"div\", _extends({\n    ref: elementRef\n  }, rootProps), createInputElement(), createBoxElement()), hasTooltip && /*#__PURE__*/React.createElement(Tooltip, _extends({\n    target: elementRef,\n    content: props.tooltip,\n    pt: ptm('tooltip')\n  }, props.tooltipOptions)));\n}));\nCheckbox.displayName = 'Checkbox';\n\nexport { Checkbox };\n"], "names": ["PermissionPage", "toast", "useRef", "roles", "setRoles", "useState", "selectedR<PERSON>", "setSelectedRole", "allPermissions", "setAllPermissions", "rolePermissions", "setRolePermissions", "loading", "setLoading", "fetchRoles", "useCallback", "async", "response", "apiClient", "get", "map", "r", "id", "name", "error", "_toast$current", "_error$response", "_error$response$data", "current", "show", "severity", "summary", "detail", "data", "message", "fetchAllPermissions", "permissions", "PermissionApi", "getAllPermissions", "_toast$current2", "_error$response2", "_error$response2$data", "fetchRolePermissions", "getRolePermissions", "roleId", "_toast$current3", "_error$response3", "_error$response3$data", "useEffect", "groupedPermissions", "reduce", "acc", "permission", "category", "Category", "push", "_jsxs", "className", "children", "_jsx", "Toast", "ref", "Card", "title", "htmlFor", "Dropdown", "value", "options", "onChange", "e", "optionLabel", "optionValue", "placeholder", "style", "width", "Object", "keys", "Checkbox", "inputId", "Code", "checked", "includes", "handlePermissionChange", "permissionCode", "isChecked", "prevCodes", "filter", "code", "Name", "<PERSON><PERSON>", "label", "icon", "onClick", "_toast$current4", "updateRolePermissions", "_toast$current5", "_error$response4", "_error$response4$data", "disabled", "_extends", "assign", "bind", "n", "arguments", "length", "t", "hasOwnProperty", "call", "apply", "_typeof", "o", "Symbol", "iterator", "constructor", "prototype", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "i", "toPrimitive", "TypeError", "String", "Number", "_defineProperty", "defineProperty", "enumerable", "configurable", "writable", "_arrayLikeToArray", "a", "Array", "_slicedToArray", "isArray", "_arrayWithHoles", "l", "u", "f", "next", "done", "_iterableToArrayLimit", "toString", "slice", "from", "test", "_unsupportedIterableToArray", "_nonIterableRest", "classes", "box", "input", "root", "_ref", "props", "context", "classNames", "invalid", "variant", "inputStyle", "CheckboxBase", "ComponentBase", "extend", "defaultProps", "__TYPE", "autoFocus", "falseValue", "inputRef", "onContextMenu", "onMouseDown", "readOnly", "required", "tabIndex", "tooltip", "tooltipOptions", "trueValue", "undefined", "css", "ownKeys", "getOwnPropertySymbols", "getOwnPropertyDescriptor", "_objectSpread", "for<PERSON>ach", "getOwnPropertyDescriptors", "defineProperties", "React", "inProps", "mergeProps", "useMergeProps", "PrimeReactContext", "getProps", "_React$useState2", "focusedState", "setFocusedState", "_CheckboxBase$setMeta", "setMetaData", "state", "focused", "ptm", "cx", "isUnstyled", "useHandleStyle", "styles", "elementRef", "focus", "<PERSON><PERSON><PERSON><PERSON>", "getElement", "getInput", "ObjectUtils", "combinedRefs", "useUpdateEffect", "useMountEffect", "hasTooltip", "isNotEmpty", "otherProps", "getOtherProps", "rootProps", "ariaProps", "reduceKeys", "ARIA_PROPS", "inputProps", "type", "onFocus", "event", "_props$onFocus", "_onFocus", "onBlur", "_props$onBlur", "_onBlur", "_props$onChange", "eventData", "originalEvent", "stopPropagation", "preventDefault", "target", "defaultPrevented", "_onChange", "createInputElement", "iconProps", "boxProps", "CheckIcon", "checkboxIcon", "IconUtils", "getJSXIcon", "createBoxElement", "<PERSON><PERSON><PERSON>", "content", "pt", "displayName"], "sourceRoot": ""}
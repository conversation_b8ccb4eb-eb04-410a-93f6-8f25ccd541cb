{"ast": null, "code": "import { formatDistance } from \"./ug/_lib/formatDistance.js\";\nimport { formatLong } from \"./ug/_lib/formatLong.js\";\nimport { formatRelative } from \"./ug/_lib/formatRelative.js\";\nimport { localize } from \"./ug/_lib/localize.js\";\nimport { match } from \"./ug/_lib/match.js\";\n\n/**\n * @category Locales\n * @summary Uighur locale\n * @language Uighur\n * @iso-639-2 uig\n * <AUTHOR> [@abduwaly](https://github.com/abduwaly)\n */\nexport const ug = {\n  code: \"ug\",\n  formatDistance: formatDistance,\n  formatLong: formatLong,\n  formatRelative: formatRelative,\n  localize: localize,\n  match: match,\n  options: {\n    weekStartsOn: 0 /* Sunday */,\n    firstWeekContainsDate: 1\n  }\n};\n\n// Fallback for modularized imports:\nexport default ug;", "map": {"version": 3, "names": ["formatDistance", "formatLong", "formatRelative", "localize", "match", "ug", "code", "options", "weekStartsOn", "firstWeekContainsDate"], "sources": ["C:/Users/<USER>/Desktop/demo-git/demo-react/my-fullstack-app/frontend/node_modules/date-fns/locale/ug.js"], "sourcesContent": ["import { formatDistance } from \"./ug/_lib/formatDistance.js\";\nimport { formatLong } from \"./ug/_lib/formatLong.js\";\nimport { formatRelative } from \"./ug/_lib/formatRelative.js\";\nimport { localize } from \"./ug/_lib/localize.js\";\nimport { match } from \"./ug/_lib/match.js\";\n\n/**\n * @category Locales\n * @summary Uighur locale\n * @language Uighur\n * @iso-639-2 uig\n * <AUTHOR> [@abduwaly](https://github.com/abduwaly)\n */\nexport const ug = {\n  code: \"ug\",\n  formatDistance: formatDistance,\n  formatLong: formatLong,\n  formatRelative: formatRelative,\n  localize: localize,\n  match: match,\n  options: {\n    weekStartsOn: 0 /* Sunday */,\n    firstWeekContainsDate: 1,\n  },\n};\n\n// Fallback for modularized imports:\nexport default ug;\n"], "mappings": "AAAA,SAASA,cAAc,QAAQ,6BAA6B;AAC5D,SAASC,UAAU,QAAQ,yBAAyB;AACpD,SAASC,cAAc,QAAQ,6BAA6B;AAC5D,SAASC,QAAQ,QAAQ,uBAAuB;AAChD,SAASC,KAAK,QAAQ,oBAAoB;;AAE1C;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMC,EAAE,GAAG;EAChBC,IAAI,EAAE,IAAI;EACVN,cAAc,EAAEA,cAAc;EAC9BC,UAAU,EAAEA,UAAU;EACtBC,cAAc,EAAEA,cAAc;EAC9BC,QAAQ,EAAEA,QAAQ;EAClBC,KAAK,EAAEA,KAAK;EACZG,OAAO,EAAE;IACPC,YAAY,EAAE,CAAC,CAAC;IAChBC,qBAAqB,EAAE;EACzB;AACF,CAAC;;AAED;AACA,eAAeJ,EAAE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
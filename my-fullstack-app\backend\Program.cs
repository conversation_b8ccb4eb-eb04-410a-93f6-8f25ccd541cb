using Microsoft.AspNetCore.Builder;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.EntityFrameworkCore;
using StackExchange.Redis;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Hosting;
using Microsoft.AspNetCore.SignalR;
using System;
using MyApi.Data;
using Microsoft.IdentityModel.Tokens;
using System.Text;
using Microsoft.Extensions.FileProviders;
using System.IO;
using MyApi.Helpers;
using MyApi.Services;
using Microsoft.AspNetCore.ResponseCompression;
using Microsoft.AspNetCore.Http;

var builder = WebApplication.CreateBuilder(args);

// 環境配置
var isDevelopment = builder.Environment.IsDevelopment();

#region 數據庫配置

var connectionString = builder.Configuration.GetConnectionString("DefaultConnection");
var configuration = builder.Configuration.GetConnectionString("Redis");
if (isDevelopment)
{
    connectionString = builder.Configuration.GetConnectionString("DevDefaultConnection");
    configuration = builder.Configuration.GetConnectionString("DevRedis");
    Console.WriteLine("ConnectionString: " + connectionString);
}

builder.Services.AddDbContext<AppDbContext>(options =>
{
    options.UseMySql(connectionString, ServerVersion.AutoDetect(connectionString));

    // 生產環境優化
    if (!isDevelopment)
    {
        options.EnableSensitiveDataLogging(false);
        options.EnableDetailedErrors(false);
    }
});

#endregion

#region Redis 配置

//  Redis 配置
builder.Services.AddSingleton<IConnectionMultiplexer>(sp =>
{
    
    return ConnectionMultiplexer.Connect(configuration);
});

builder.Services.AddSingleton<RedisService>();

#endregion

// 註冊 Google Calendar 服務
builder.Services.AddScoped<MyApi.Services.IGoogleCalendarService, MyApi.Services.GoogleCalendarService>();

// 註冊 Gmail 服務
builder.Services.AddScoped<MyApi.Services.IGmailService, MyApi.Services.GmailService>();

// 註冊 HttpClientFactory
builder.Services.AddHttpClient();

#region CORS 跨域設置

builder.Services.AddCors(options =>
{
    options.AddPolicy("AllowAll", policy =>
    {
        policy.WithOrigins(
            "http://frontend:80", // 使用 Compose 定義的服務名稱
            "http://localhost:3309", // 可保留給開發機本地測試
            "http://127.0.0.1:3309", // 可保留給開發機本地測試
            "http://127.0.0.1:80"
            )
            .AllowAnyHeader()
            .AllowAnyMethod()
            .AllowCredentials(); //SignalR 
    });
});

// API 服務配置
builder.Services.AddControllers()
    .ConfigureApiBehaviorOptions(options =>
    {
        // 自定義模型驗證錯誤響應
        options.SuppressModelStateInvalidFilter = false;
    });

// 僅在開發環境啟用 API 文檔
if (isDevelopment)
{
    builder.Services.AddEndpointsApiExplorer();
    builder.Services.AddSwaggerGen();
}

#endregion

#region JWT 驗證

builder.Services.AddAuthentication("Bearer")
    .AddJwtBearer("Bearer", options =>
    {
        options.TokenValidationParameters = new TokenValidationParameters
        {
            ValidateIssuer = true,
            ValidateAudience = true,
            ValidateLifetime = true,
            ValidateIssuerSigningKey = true,
            ValidIssuer = builder.Configuration["Jwt:Issuer"],
            ValidAudience = builder.Configuration["Jwt:Audience"],
            IssuerSigningKey = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(builder.Configuration["Jwt:Key"]))
        };
    });

#endregion

#region SignalR 通訊

builder.Services.AddSignalR().AddMessagePackProtocol();

#endregion

# region 啟用 Gzip 壓縮

builder.Services.AddResponseCompression(options =>
{
    options.EnableForHttps = true;
    options.Providers.Add<GzipCompressionProvider>();
});

#endregion

var app = builder.Build();

// 中間件管道優化
app.UseCors("AllowAll");

// 僅在開發環境啟用 Swagger
if (isDevelopment)
{
    app.UseSwagger();
    app.UseSwaggerUI();
}

// 生產環境啟用 HTTPS 重定向
if (!isDevelopment)
{
    app.UseHttpsRedirection();
}

app.UseMiddleware<CustomMiddleware>();
app.UseResponseCompression();
app.UseRouting();
app.UseAuthentication();
app.UseAuthorization();

#region 圖片上傳

var uploadsPath = Path.Combine(Directory.GetCurrentDirectory(), "uploads");
if (!Directory.Exists(uploadsPath))
{
    Directory.CreateDirectory(uploadsPath);
}

app.UseStaticFiles(new StaticFileOptions
{
    FileProvider = new PhysicalFileProvider(uploadsPath),
    RequestPath = "/uploads",
    OnPrepareResponse = ctx =>
    {
        ctx.Context.Response.Headers.Append("Cache-Control", "public,max-age=604800"); // 7天
    }
});

#endregion

#region 報表

app.MapHub<ReportHub>("/reportHub");

#endregion

using (var scope = app.Services.CreateScope())
{
    var db = scope.ServiceProvider.GetRequiredService<AppDbContext>();
    db.Database.Migrate(); 
}

app.MapControllers();
app.Run();

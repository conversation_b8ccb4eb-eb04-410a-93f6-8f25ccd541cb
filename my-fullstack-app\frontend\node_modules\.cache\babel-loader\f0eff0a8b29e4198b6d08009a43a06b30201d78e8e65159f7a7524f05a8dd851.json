{"ast": null, "code": "import { formatDistance } from \"./hi/_lib/formatDistance.js\";\nimport { formatLong } from \"./hi/_lib/formatLong.js\";\nimport { formatRelative } from \"./hi/_lib/formatRelative.js\";\nimport { localize } from \"./hi/_lib/localize.js\";\nimport { match } from \"./hi/_lib/match.js\";\n\n/**\n * @category Locales\n * @summary Hindi locale (India).\n * @language Hindi\n * @iso-639-2 hin\n * <AUTHOR> [@mukeshmandiwal](https://github.com/mukeshmandiwal)\n */\nexport const hi = {\n  code: \"hi\",\n  formatDistance: formatDistance,\n  formatLong: formatLong,\n  formatRelative: formatRelative,\n  localize: localize,\n  match: match,\n  options: {\n    weekStartsOn: 0 /* Monday */,\n    firstWeekContainsDate: 4\n  }\n};\n\n// Fallback for modularized imports:\nexport default hi;", "map": {"version": 3, "names": ["formatDistance", "formatLong", "formatRelative", "localize", "match", "hi", "code", "options", "weekStartsOn", "firstWeekContainsDate"], "sources": ["C:/Users/<USER>/Desktop/demo-git/demo-react/my-fullstack-app/frontend/node_modules/date-fns/locale/hi.js"], "sourcesContent": ["import { formatDistance } from \"./hi/_lib/formatDistance.js\";\nimport { formatLong } from \"./hi/_lib/formatLong.js\";\nimport { formatRelative } from \"./hi/_lib/formatRelative.js\";\nimport { localize } from \"./hi/_lib/localize.js\";\nimport { match } from \"./hi/_lib/match.js\";\n\n/**\n * @category Locales\n * @summary Hindi locale (India).\n * @language Hindi\n * @iso-639-2 hin\n * <AUTHOR> [@mukeshmandiwal](https://github.com/mukeshmandiwal)\n */\nexport const hi = {\n  code: \"hi\",\n  formatDistance: formatDistance,\n  formatLong: formatLong,\n  formatRelative: formatRelative,\n  localize: localize,\n  match: match,\n  options: {\n    weekStartsOn: 0 /* Monday */,\n    firstWeekContainsDate: 4,\n  },\n};\n\n// Fallback for modularized imports:\nexport default hi;\n"], "mappings": "AAAA,SAASA,cAAc,QAAQ,6BAA6B;AAC5D,SAASC,UAAU,QAAQ,yBAAyB;AACpD,SAASC,cAAc,QAAQ,6BAA6B;AAC5D,SAASC,QAAQ,QAAQ,uBAAuB;AAChD,SAASC,KAAK,QAAQ,oBAAoB;;AAE1C;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMC,EAAE,GAAG;EAChBC,IAAI,EAAE,IAAI;EACVN,cAAc,EAAEA,cAAc;EAC9BC,UAAU,EAAEA,UAAU;EACtBC,cAAc,EAAEA,cAAc;EAC9BC,QAAQ,EAAEA,QAAQ;EAClBC,KAAK,EAAEA,KAAK;EACZG,OAAO,EAAE;IACPC,YAAY,EAAE,CAAC,CAAC;IAChBC,qBAAqB,EAAE;EACzB;AACF,CAAC;;AAED;AACA,eAAeJ,EAAE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
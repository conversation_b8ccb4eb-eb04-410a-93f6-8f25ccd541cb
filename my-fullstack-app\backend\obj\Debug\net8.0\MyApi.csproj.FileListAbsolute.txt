C:\Users\<USER>\Desktop\demo-git\demo-react\my-fullstack-app\backend\bin\Debug\net8.0\appsettings.json
C:\Users\<USER>\Desktop\demo-git\demo-react\my-fullstack-app\backend\bin\Debug\net8.0\client_secret_448638201562-ntutsm4jsi9o2sbs5dsq0af54hlv5n3g.apps.googleusercontent.com.json
C:\Users\<USER>\Desktop\demo-git\demo-react\my-fullstack-app\backend\bin\Debug\net8.0\libman.json
C:\Users\<USER>\Desktop\demo-git\demo-react\my-fullstack-app\backend\bin\Debug\net8.0\projectphysicalsite-11895cc283aa.json
C:\Users\<USER>\Desktop\demo-git\demo-react\my-fullstack-app\backend\bin\Debug\net8.0\MyApi.staticwebassets.runtime.json
C:\Users\<USER>\Desktop\demo-git\demo-react\my-fullstack-app\backend\bin\Debug\net8.0\MyApi.exe
C:\Users\<USER>\Desktop\demo-git\demo-react\my-fullstack-app\backend\bin\Debug\net8.0\MyApi.deps.json
C:\Users\<USER>\Desktop\demo-git\demo-react\my-fullstack-app\backend\bin\Debug\net8.0\MyApi.runtimeconfig.json
C:\Users\<USER>\Desktop\demo-git\demo-react\my-fullstack-app\backend\bin\Debug\net8.0\MyApi.dll
C:\Users\<USER>\Desktop\demo-git\demo-react\my-fullstack-app\backend\bin\Debug\net8.0\MyApi.pdb
C:\Users\<USER>\Desktop\demo-git\demo-react\my-fullstack-app\backend\bin\Debug\net8.0\BCrypt.Net-Next.dll
C:\Users\<USER>\Desktop\demo-git\demo-react\my-fullstack-app\backend\bin\Debug\net8.0\BouncyCastle.Cryptography.dll
C:\Users\<USER>\Desktop\demo-git\demo-react\my-fullstack-app\backend\bin\Debug\net8.0\Google.Apis.dll
C:\Users\<USER>\Desktop\demo-git\demo-react\my-fullstack-app\backend\bin\Debug\net8.0\Google.Apis.Auth.dll
C:\Users\<USER>\Desktop\demo-git\demo-react\my-fullstack-app\backend\bin\Debug\net8.0\Google.Apis.Calendar.v3.dll
C:\Users\<USER>\Desktop\demo-git\demo-react\my-fullstack-app\backend\bin\Debug\net8.0\Google.Apis.Core.dll
C:\Users\<USER>\Desktop\demo-git\demo-react\my-fullstack-app\backend\bin\Debug\net8.0\Google.Apis.Gmail.v1.dll
C:\Users\<USER>\Desktop\demo-git\demo-react\my-fullstack-app\backend\bin\Debug\net8.0\HarfBuzzSharp.dll
C:\Users\<USER>\Desktop\demo-git\demo-react\my-fullstack-app\backend\bin\Debug\net8.0\Humanizer.dll
C:\Users\<USER>\Desktop\demo-git\demo-react\my-fullstack-app\backend\bin\Debug\net8.0\MessagePack.dll
C:\Users\<USER>\Desktop\demo-git\demo-react\my-fullstack-app\backend\bin\Debug\net8.0\MessagePack.Annotations.dll
C:\Users\<USER>\Desktop\demo-git\demo-react\my-fullstack-app\backend\bin\Debug\net8.0\Microsoft.AspNetCore.Authentication.JwtBearer.dll
C:\Users\<USER>\Desktop\demo-git\demo-react\my-fullstack-app\backend\bin\Debug\net8.0\Microsoft.AspNetCore.Connections.Abstractions.dll
C:\Users\<USER>\Desktop\demo-git\demo-react\my-fullstack-app\backend\bin\Debug\net8.0\Microsoft.AspNetCore.SignalR.Common.dll
C:\Users\<USER>\Desktop\demo-git\demo-react\my-fullstack-app\backend\bin\Debug\net8.0\Microsoft.AspNetCore.SignalR.Protocols.MessagePack.dll
C:\Users\<USER>\Desktop\demo-git\demo-react\my-fullstack-app\backend\bin\Debug\net8.0\Microsoft.Bcl.AsyncInterfaces.dll
C:\Users\<USER>\Desktop\demo-git\demo-react\my-fullstack-app\backend\bin\Debug\net8.0\Microsoft.CodeAnalysis.dll
C:\Users\<USER>\Desktop\demo-git\demo-react\my-fullstack-app\backend\bin\Debug\net8.0\Microsoft.CodeAnalysis.CSharp.dll
C:\Users\<USER>\Desktop\demo-git\demo-react\my-fullstack-app\backend\bin\Debug\net8.0\Microsoft.CodeAnalysis.CSharp.Workspaces.dll
C:\Users\<USER>\Desktop\demo-git\demo-react\my-fullstack-app\backend\bin\Debug\net8.0\Microsoft.CodeAnalysis.Workspaces.dll
C:\Users\<USER>\Desktop\demo-git\demo-react\my-fullstack-app\backend\bin\Debug\net8.0\Microsoft.EntityFrameworkCore.dll
C:\Users\<USER>\Desktop\demo-git\demo-react\my-fullstack-app\backend\bin\Debug\net8.0\Microsoft.EntityFrameworkCore.Abstractions.dll
C:\Users\<USER>\Desktop\demo-git\demo-react\my-fullstack-app\backend\bin\Debug\net8.0\Microsoft.EntityFrameworkCore.Design.dll
C:\Users\<USER>\Desktop\demo-git\demo-react\my-fullstack-app\backend\bin\Debug\net8.0\Microsoft.EntityFrameworkCore.Relational.dll
C:\Users\<USER>\Desktop\demo-git\demo-react\my-fullstack-app\backend\bin\Debug\net8.0\Microsoft.Extensions.DependencyInjection.Abstractions.dll
C:\Users\<USER>\Desktop\demo-git\demo-react\my-fullstack-app\backend\bin\Debug\net8.0\Microsoft.Extensions.DependencyModel.dll
C:\Users\<USER>\Desktop\demo-git\demo-react\my-fullstack-app\backend\bin\Debug\net8.0\Microsoft.Extensions.Diagnostics.Abstractions.dll
C:\Users\<USER>\Desktop\demo-git\demo-react\my-fullstack-app\backend\bin\Debug\net8.0\Microsoft.Extensions.Features.dll
C:\Users\<USER>\Desktop\demo-git\demo-react\my-fullstack-app\backend\bin\Debug\net8.0\Microsoft.Extensions.Hosting.Abstractions.dll
C:\Users\<USER>\Desktop\demo-git\demo-react\my-fullstack-app\backend\bin\Debug\net8.0\Microsoft.Extensions.Logging.Abstractions.dll
C:\Users\<USER>\Desktop\demo-git\demo-react\my-fullstack-app\backend\bin\Debug\net8.0\Microsoft.Extensions.ObjectPool.dll
C:\Users\<USER>\Desktop\demo-git\demo-react\my-fullstack-app\backend\bin\Debug\net8.0\Microsoft.Extensions.Options.dll
C:\Users\<USER>\Desktop\demo-git\demo-react\my-fullstack-app\backend\bin\Debug\net8.0\Microsoft.Extensions.Primitives.dll
C:\Users\<USER>\Desktop\demo-git\demo-react\my-fullstack-app\backend\bin\Debug\net8.0\Microsoft.IdentityModel.Abstractions.dll
C:\Users\<USER>\Desktop\demo-git\demo-react\my-fullstack-app\backend\bin\Debug\net8.0\Microsoft.IdentityModel.JsonWebTokens.dll
C:\Users\<USER>\Desktop\demo-git\demo-react\my-fullstack-app\backend\bin\Debug\net8.0\Microsoft.IdentityModel.Logging.dll
C:\Users\<USER>\Desktop\demo-git\demo-react\my-fullstack-app\backend\bin\Debug\net8.0\Microsoft.IdentityModel.Protocols.dll
C:\Users\<USER>\Desktop\demo-git\demo-react\my-fullstack-app\backend\bin\Debug\net8.0\Microsoft.IdentityModel.Protocols.OpenIdConnect.dll
C:\Users\<USER>\Desktop\demo-git\demo-react\my-fullstack-app\backend\bin\Debug\net8.0\Microsoft.IdentityModel.Tokens.dll
C:\Users\<USER>\Desktop\demo-git\demo-react\my-fullstack-app\backend\bin\Debug\net8.0\Microsoft.NET.StringTools.dll
C:\Users\<USER>\Desktop\demo-git\demo-react\my-fullstack-app\backend\bin\Debug\net8.0\Microsoft.OpenApi.dll
C:\Users\<USER>\Desktop\demo-git\demo-react\my-fullstack-app\backend\bin\Debug\net8.0\MimeKit.dll
C:\Users\<USER>\Desktop\demo-git\demo-react\my-fullstack-app\backend\bin\Debug\net8.0\Mono.TextTemplating.dll
C:\Users\<USER>\Desktop\demo-git\demo-react\my-fullstack-app\backend\bin\Debug\net8.0\MySqlConnector.dll
C:\Users\<USER>\Desktop\demo-git\demo-react\my-fullstack-app\backend\bin\Debug\net8.0\Newtonsoft.Json.dll
C:\Users\<USER>\Desktop\demo-git\demo-react\my-fullstack-app\backend\bin\Debug\net8.0\Pipelines.Sockets.Unofficial.dll
C:\Users\<USER>\Desktop\demo-git\demo-react\my-fullstack-app\backend\bin\Debug\net8.0\Pomelo.EntityFrameworkCore.MySql.dll
C:\Users\<USER>\Desktop\demo-git\demo-react\my-fullstack-app\backend\bin\Debug\net8.0\QuestPDF.dll
C:\Users\<USER>\Desktop\demo-git\demo-react\my-fullstack-app\backend\bin\Debug\net8.0\SkiaSharp.dll
C:\Users\<USER>\Desktop\demo-git\demo-react\my-fullstack-app\backend\bin\Debug\net8.0\SkiaSharp.HarfBuzz.dll
C:\Users\<USER>\Desktop\demo-git\demo-react\my-fullstack-app\backend\bin\Debug\net8.0\StackExchange.Redis.dll
C:\Users\<USER>\Desktop\demo-git\demo-react\my-fullstack-app\backend\bin\Debug\net8.0\Swashbuckle.AspNetCore.Swagger.dll
C:\Users\<USER>\Desktop\demo-git\demo-react\my-fullstack-app\backend\bin\Debug\net8.0\Swashbuckle.AspNetCore.SwaggerGen.dll
C:\Users\<USER>\Desktop\demo-git\demo-react\my-fullstack-app\backend\bin\Debug\net8.0\Swashbuckle.AspNetCore.SwaggerUI.dll
C:\Users\<USER>\Desktop\demo-git\demo-react\my-fullstack-app\backend\bin\Debug\net8.0\System.CodeDom.dll
C:\Users\<USER>\Desktop\demo-git\demo-react\my-fullstack-app\backend\bin\Debug\net8.0\System.Composition.AttributedModel.dll
C:\Users\<USER>\Desktop\demo-git\demo-react\my-fullstack-app\backend\bin\Debug\net8.0\System.Composition.Convention.dll
C:\Users\<USER>\Desktop\demo-git\demo-react\my-fullstack-app\backend\bin\Debug\net8.0\System.Composition.Hosting.dll
C:\Users\<USER>\Desktop\demo-git\demo-react\my-fullstack-app\backend\bin\Debug\net8.0\System.Composition.Runtime.dll
C:\Users\<USER>\Desktop\demo-git\demo-react\my-fullstack-app\backend\bin\Debug\net8.0\System.Composition.TypedParts.dll
C:\Users\<USER>\Desktop\demo-git\demo-react\my-fullstack-app\backend\bin\Debug\net8.0\System.IdentityModel.Tokens.Jwt.dll
C:\Users\<USER>\Desktop\demo-git\demo-react\my-fullstack-app\backend\bin\Debug\net8.0\System.IO.Pipelines.dll
C:\Users\<USER>\Desktop\demo-git\demo-react\my-fullstack-app\backend\bin\Debug\net8.0\System.Management.dll
C:\Users\<USER>\Desktop\demo-git\demo-react\my-fullstack-app\backend\bin\Debug\net8.0\System.Net.WebSockets.WebSocketProtocol.dll
C:\Users\<USER>\Desktop\demo-git\demo-react\my-fullstack-app\backend\bin\Debug\net8.0\System.Text.Encodings.Web.dll
C:\Users\<USER>\Desktop\demo-git\demo-react\my-fullstack-app\backend\bin\Debug\net8.0\System.Text.Json.dll
C:\Users\<USER>\Desktop\demo-git\demo-react\my-fullstack-app\backend\bin\Debug\net8.0\cs\Microsoft.CodeAnalysis.resources.dll
C:\Users\<USER>\Desktop\demo-git\demo-react\my-fullstack-app\backend\bin\Debug\net8.0\de\Microsoft.CodeAnalysis.resources.dll
C:\Users\<USER>\Desktop\demo-git\demo-react\my-fullstack-app\backend\bin\Debug\net8.0\es\Microsoft.CodeAnalysis.resources.dll
C:\Users\<USER>\Desktop\demo-git\demo-react\my-fullstack-app\backend\bin\Debug\net8.0\fr\Microsoft.CodeAnalysis.resources.dll
C:\Users\<USER>\Desktop\demo-git\demo-react\my-fullstack-app\backend\bin\Debug\net8.0\it\Microsoft.CodeAnalysis.resources.dll
C:\Users\<USER>\Desktop\demo-git\demo-react\my-fullstack-app\backend\bin\Debug\net8.0\ja\Microsoft.CodeAnalysis.resources.dll
C:\Users\<USER>\Desktop\demo-git\demo-react\my-fullstack-app\backend\bin\Debug\net8.0\ko\Microsoft.CodeAnalysis.resources.dll
C:\Users\<USER>\Desktop\demo-git\demo-react\my-fullstack-app\backend\bin\Debug\net8.0\pl\Microsoft.CodeAnalysis.resources.dll
C:\Users\<USER>\Desktop\demo-git\demo-react\my-fullstack-app\backend\bin\Debug\net8.0\pt-BR\Microsoft.CodeAnalysis.resources.dll
C:\Users\<USER>\Desktop\demo-git\demo-react\my-fullstack-app\backend\bin\Debug\net8.0\ru\Microsoft.CodeAnalysis.resources.dll
C:\Users\<USER>\Desktop\demo-git\demo-react\my-fullstack-app\backend\bin\Debug\net8.0\tr\Microsoft.CodeAnalysis.resources.dll
C:\Users\<USER>\Desktop\demo-git\demo-react\my-fullstack-app\backend\bin\Debug\net8.0\zh-Hans\Microsoft.CodeAnalysis.resources.dll
C:\Users\<USER>\Desktop\demo-git\demo-react\my-fullstack-app\backend\bin\Debug\net8.0\zh-Hant\Microsoft.CodeAnalysis.resources.dll
C:\Users\<USER>\Desktop\demo-git\demo-react\my-fullstack-app\backend\bin\Debug\net8.0\cs\Microsoft.CodeAnalysis.CSharp.resources.dll
C:\Users\<USER>\Desktop\demo-git\demo-react\my-fullstack-app\backend\bin\Debug\net8.0\de\Microsoft.CodeAnalysis.CSharp.resources.dll
C:\Users\<USER>\Desktop\demo-git\demo-react\my-fullstack-app\backend\bin\Debug\net8.0\es\Microsoft.CodeAnalysis.CSharp.resources.dll
C:\Users\<USER>\Desktop\demo-git\demo-react\my-fullstack-app\backend\bin\Debug\net8.0\fr\Microsoft.CodeAnalysis.CSharp.resources.dll
C:\Users\<USER>\Desktop\demo-git\demo-react\my-fullstack-app\backend\bin\Debug\net8.0\it\Microsoft.CodeAnalysis.CSharp.resources.dll
C:\Users\<USER>\Desktop\demo-git\demo-react\my-fullstack-app\backend\bin\Debug\net8.0\ja\Microsoft.CodeAnalysis.CSharp.resources.dll
C:\Users\<USER>\Desktop\demo-git\demo-react\my-fullstack-app\backend\bin\Debug\net8.0\ko\Microsoft.CodeAnalysis.CSharp.resources.dll
C:\Users\<USER>\Desktop\demo-git\demo-react\my-fullstack-app\backend\bin\Debug\net8.0\pl\Microsoft.CodeAnalysis.CSharp.resources.dll
C:\Users\<USER>\Desktop\demo-git\demo-react\my-fullstack-app\backend\bin\Debug\net8.0\pt-BR\Microsoft.CodeAnalysis.CSharp.resources.dll
C:\Users\<USER>\Desktop\demo-git\demo-react\my-fullstack-app\backend\bin\Debug\net8.0\ru\Microsoft.CodeAnalysis.CSharp.resources.dll
C:\Users\<USER>\Desktop\demo-git\demo-react\my-fullstack-app\backend\bin\Debug\net8.0\tr\Microsoft.CodeAnalysis.CSharp.resources.dll
C:\Users\<USER>\Desktop\demo-git\demo-react\my-fullstack-app\backend\bin\Debug\net8.0\zh-Hans\Microsoft.CodeAnalysis.CSharp.resources.dll
C:\Users\<USER>\Desktop\demo-git\demo-react\my-fullstack-app\backend\bin\Debug\net8.0\zh-Hant\Microsoft.CodeAnalysis.CSharp.resources.dll
C:\Users\<USER>\Desktop\demo-git\demo-react\my-fullstack-app\backend\bin\Debug\net8.0\cs\Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll
C:\Users\<USER>\Desktop\demo-git\demo-react\my-fullstack-app\backend\bin\Debug\net8.0\de\Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll
C:\Users\<USER>\Desktop\demo-git\demo-react\my-fullstack-app\backend\bin\Debug\net8.0\es\Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll
C:\Users\<USER>\Desktop\demo-git\demo-react\my-fullstack-app\backend\bin\Debug\net8.0\fr\Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll
C:\Users\<USER>\Desktop\demo-git\demo-react\my-fullstack-app\backend\bin\Debug\net8.0\it\Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll
C:\Users\<USER>\Desktop\demo-git\demo-react\my-fullstack-app\backend\bin\Debug\net8.0\ja\Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll
C:\Users\<USER>\Desktop\demo-git\demo-react\my-fullstack-app\backend\bin\Debug\net8.0\ko\Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll
C:\Users\<USER>\Desktop\demo-git\demo-react\my-fullstack-app\backend\bin\Debug\net8.0\pl\Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll
C:\Users\<USER>\Desktop\demo-git\demo-react\my-fullstack-app\backend\bin\Debug\net8.0\pt-BR\Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll
C:\Users\<USER>\Desktop\demo-git\demo-react\my-fullstack-app\backend\bin\Debug\net8.0\ru\Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll
C:\Users\<USER>\Desktop\demo-git\demo-react\my-fullstack-app\backend\bin\Debug\net8.0\tr\Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll
C:\Users\<USER>\Desktop\demo-git\demo-react\my-fullstack-app\backend\bin\Debug\net8.0\zh-Hans\Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll
C:\Users\<USER>\Desktop\demo-git\demo-react\my-fullstack-app\backend\bin\Debug\net8.0\zh-Hant\Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll
C:\Users\<USER>\Desktop\demo-git\demo-react\my-fullstack-app\backend\bin\Debug\net8.0\cs\Microsoft.CodeAnalysis.Workspaces.resources.dll
C:\Users\<USER>\Desktop\demo-git\demo-react\my-fullstack-app\backend\bin\Debug\net8.0\de\Microsoft.CodeAnalysis.Workspaces.resources.dll
C:\Users\<USER>\Desktop\demo-git\demo-react\my-fullstack-app\backend\bin\Debug\net8.0\es\Microsoft.CodeAnalysis.Workspaces.resources.dll
C:\Users\<USER>\Desktop\demo-git\demo-react\my-fullstack-app\backend\bin\Debug\net8.0\fr\Microsoft.CodeAnalysis.Workspaces.resources.dll
C:\Users\<USER>\Desktop\demo-git\demo-react\my-fullstack-app\backend\bin\Debug\net8.0\it\Microsoft.CodeAnalysis.Workspaces.resources.dll
C:\Users\<USER>\Desktop\demo-git\demo-react\my-fullstack-app\backend\bin\Debug\net8.0\ja\Microsoft.CodeAnalysis.Workspaces.resources.dll
C:\Users\<USER>\Desktop\demo-git\demo-react\my-fullstack-app\backend\bin\Debug\net8.0\ko\Microsoft.CodeAnalysis.Workspaces.resources.dll
C:\Users\<USER>\Desktop\demo-git\demo-react\my-fullstack-app\backend\bin\Debug\net8.0\pl\Microsoft.CodeAnalysis.Workspaces.resources.dll
C:\Users\<USER>\Desktop\demo-git\demo-react\my-fullstack-app\backend\bin\Debug\net8.0\pt-BR\Microsoft.CodeAnalysis.Workspaces.resources.dll
C:\Users\<USER>\Desktop\demo-git\demo-react\my-fullstack-app\backend\bin\Debug\net8.0\ru\Microsoft.CodeAnalysis.Workspaces.resources.dll
C:\Users\<USER>\Desktop\demo-git\demo-react\my-fullstack-app\backend\bin\Debug\net8.0\tr\Microsoft.CodeAnalysis.Workspaces.resources.dll
C:\Users\<USER>\Desktop\demo-git\demo-react\my-fullstack-app\backend\bin\Debug\net8.0\zh-Hans\Microsoft.CodeAnalysis.Workspaces.resources.dll
C:\Users\<USER>\Desktop\demo-git\demo-react\my-fullstack-app\backend\bin\Debug\net8.0\zh-Hant\Microsoft.CodeAnalysis.Workspaces.resources.dll
C:\Users\<USER>\Desktop\demo-git\demo-react\my-fullstack-app\backend\bin\Debug\net8.0\runtimes\linux-arm\native\libHarfBuzzSharp.so
C:\Users\<USER>\Desktop\demo-git\demo-react\my-fullstack-app\backend\bin\Debug\net8.0\runtimes\linux-arm64\native\libHarfBuzzSharp.so
C:\Users\<USER>\Desktop\demo-git\demo-react\my-fullstack-app\backend\bin\Debug\net8.0\runtimes\linux-musl-x64\native\libHarfBuzzSharp.so
C:\Users\<USER>\Desktop\demo-git\demo-react\my-fullstack-app\backend\bin\Debug\net8.0\runtimes\linux-x64\native\libHarfBuzzSharp.so
C:\Users\<USER>\Desktop\demo-git\demo-react\my-fullstack-app\backend\bin\Debug\net8.0\runtimes\osx\native\libHarfBuzzSharp.dylib
C:\Users\<USER>\Desktop\demo-git\demo-react\my-fullstack-app\backend\bin\Debug\net8.0\runtimes\win-arm64\native\libHarfBuzzSharp.dll
C:\Users\<USER>\Desktop\demo-git\demo-react\my-fullstack-app\backend\bin\Debug\net8.0\runtimes\win-x64\native\libHarfBuzzSharp.dll
C:\Users\<USER>\Desktop\demo-git\demo-react\my-fullstack-app\backend\bin\Debug\net8.0\runtimes\win-x86\native\libHarfBuzzSharp.dll
C:\Users\<USER>\Desktop\demo-git\demo-react\my-fullstack-app\backend\bin\Debug\net8.0\runtimes\linux-arm\native\libSkiaSharp.so
C:\Users\<USER>\Desktop\demo-git\demo-react\my-fullstack-app\backend\bin\Debug\net8.0\runtimes\linux-arm64\native\libSkiaSharp.so
C:\Users\<USER>\Desktop\demo-git\demo-react\my-fullstack-app\backend\bin\Debug\net8.0\runtimes\linux-musl-x64\native\libSkiaSharp.so
C:\Users\<USER>\Desktop\demo-git\demo-react\my-fullstack-app\backend\bin\Debug\net8.0\runtimes\linux-x64\native\libSkiaSharp.so
C:\Users\<USER>\Desktop\demo-git\demo-react\my-fullstack-app\backend\bin\Debug\net8.0\runtimes\osx\native\libSkiaSharp.dylib
C:\Users\<USER>\Desktop\demo-git\demo-react\my-fullstack-app\backend\bin\Debug\net8.0\runtimes\win-arm64\native\libSkiaSharp.dll
C:\Users\<USER>\Desktop\demo-git\demo-react\my-fullstack-app\backend\bin\Debug\net8.0\runtimes\win-x64\native\libSkiaSharp.dll
C:\Users\<USER>\Desktop\demo-git\demo-react\my-fullstack-app\backend\bin\Debug\net8.0\runtimes\win-x86\native\libSkiaSharp.dll
C:\Users\<USER>\Desktop\demo-git\demo-react\my-fullstack-app\backend\bin\Debug\net8.0\runtimes\win\lib\net7.0\System.Management.dll
C:\Users\<USER>\Desktop\demo-git\demo-react\my-fullstack-app\backend\bin\Debug\net8.0\runtimes\browser\lib\net8.0\System.Text.Encodings.Web.dll
C:\Users\<USER>\Desktop\demo-git\demo-react\my-fullstack-app\backend\obj\Debug\net8.0\MyApi.csproj.AssemblyReference.cache
C:\Users\<USER>\Desktop\demo-git\demo-react\my-fullstack-app\backend\obj\Debug\net8.0\MyApi.GeneratedMSBuildEditorConfig.editorconfig
C:\Users\<USER>\Desktop\demo-git\demo-react\my-fullstack-app\backend\obj\Debug\net8.0\MyApi.AssemblyInfoInputs.cache
C:\Users\<USER>\Desktop\demo-git\demo-react\my-fullstack-app\backend\obj\Debug\net8.0\MyApi.AssemblyInfo.cs
C:\Users\<USER>\Desktop\demo-git\demo-react\my-fullstack-app\backend\obj\Debug\net8.0\MyApi.csproj.CoreCompileInputs.cache
C:\Users\<USER>\Desktop\demo-git\demo-react\my-fullstack-app\backend\obj\Debug\net8.0\MyApi.MvcApplicationPartsAssemblyInfo.cs
C:\Users\<USER>\Desktop\demo-git\demo-react\my-fullstack-app\backend\obj\Debug\net8.0\MyApi.MvcApplicationPartsAssemblyInfo.cache
C:\Users\<USER>\Desktop\demo-git\demo-react\my-fullstack-app\backend\obj\Debug\net8.0\MyApi.sourcelink.json
C:\Users\<USER>\Desktop\demo-git\demo-react\my-fullstack-app\backend\obj\Debug\net8.0\staticwebassets.build.json
C:\Users\<USER>\Desktop\demo-git\demo-react\my-fullstack-app\backend\obj\Debug\net8.0\staticwebassets.development.json
C:\Users\<USER>\Desktop\demo-git\demo-react\my-fullstack-app\backend\obj\Debug\net8.0\staticwebassets\msbuild.MyApi.Microsoft.AspNetCore.StaticWebAssets.props
C:\Users\<USER>\Desktop\demo-git\demo-react\my-fullstack-app\backend\obj\Debug\net8.0\staticwebassets\msbuild.build.MyApi.props
C:\Users\<USER>\Desktop\demo-git\demo-react\my-fullstack-app\backend\obj\Debug\net8.0\staticwebassets\msbuild.buildMultiTargeting.MyApi.props
C:\Users\<USER>\Desktop\demo-git\demo-react\my-fullstack-app\backend\obj\Debug\net8.0\staticwebassets\msbuild.buildTransitive.MyApi.props
C:\Users\<USER>\Desktop\demo-git\demo-react\my-fullstack-app\backend\obj\Debug\net8.0\staticwebassets.pack.json
C:\Users\<USER>\Desktop\demo-git\demo-react\my-fullstack-app\backend\obj\Debug\net8.0\scopedcss\bundle\MyApi.styles.css
C:\Users\<USER>\Desktop\demo-git\demo-react\my-fullstack-app\backend\obj\Debug\net8.0\MyApi.csproj.CopyComplete
C:\Users\<USER>\Desktop\demo-git\demo-react\my-fullstack-app\backend\obj\Debug\net8.0\MyApi.dll
C:\Users\<USER>\Desktop\demo-git\demo-react\my-fullstack-app\backend\obj\Debug\net8.0\refint\MyApi.dll
C:\Users\<USER>\Desktop\demo-git\demo-react\my-fullstack-app\backend\obj\Debug\net8.0\MyApi.pdb
C:\Users\<USER>\Desktop\demo-git\demo-react\my-fullstack-app\backend\obj\Debug\net8.0\MyApi.genruntimeconfig.cache
C:\Users\<USER>\Desktop\demo-git\demo-react\my-fullstack-app\backend\obj\Debug\net8.0\ref\MyApi.dll
C:\Users\<USER>\Desktop\demo-git\demo-react\my-fullstack-app\backend\bin\Debug\net8.0\Google.Apis.Oauth2.v2.dll
C:\Users\<USER>\Desktop\demo-git\demo-react\my-fullstack-app\backend\bin\Debug\net8.0\gmail-token.json

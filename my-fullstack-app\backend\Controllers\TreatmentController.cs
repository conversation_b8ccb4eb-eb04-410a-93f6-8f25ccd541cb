﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using MyApi.Models;
using static MyApi.Helpers.Enums;
using System.Collections.Generic;
using System;
using MyApi.Data;
using System.Linq;

namespace MyApi.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class TreatmentController : ControllerBase
    {
        private readonly AppDbContext _context;

        public TreatmentController(AppDbContext context)
        {
            _context = context;
        }

        [Authorize(Roles = "Admin,Manager,User")]
        [HttpGet]
        public IActionResult Get([FromQuery] int Id)
        {
            var treatment = _context.Treatments
                .Include(t => t.DiscomfortAreas.Where(da => !da.IsDelete))
                .Where(t => t.Id == Id)
                .FirstOrDefault();

            if (treatment == null)
            {
                return BadRequest("未找到診療資料");
            }

            var result = new
            {
                treatment.Id,
                treatment.OrderNo,
                treatment.Step,
                treatment.DiscomfortPeriod,
                treatment.PossibleCauses,
                treatment.TreatmentHistory,
                treatment.HowToKnowOur,
                treatment.HospitalFormUrl,
                treatment.TreatmentConsentFormUrl,
                treatment.HospitalFormRecordDate,
                treatment.Subjective,
                treatment.Objective,
                treatment.Assessment,
                treatment.Plan,
                treatment.ReceiptUrl,
                treatment.CreatedAt,
                treatment.UpdatedAt,
                treatment.IsDelete,
                treatment.OperatorUserId,
                treatment.UserId,
                treatment.PatientId,
                DiscomfortAreas = treatment.DiscomfortAreas.Select(da => new
                {
                    da.Id,
                    da.FrontAndBack,
                    da.DiscomfortArea,
                    da.DiscomfortSituation,
                    da.DiscomfortDegree
                }).ToList()
            };

            return Ok(result);
        }

        [Authorize(Roles = "Admin,Manager,User")]
        [HttpGet("GetList")]
        public IActionResult GetList([FromQuery] string patientname, string? nationalId, int? doctortid, TreatmentStep? step, DateTime? starttime, DateTime? endtime)
        {
            var Treatments = (
                   from reatments in _context.Treatments
                   where reatments.IsDelete == false
                   join patient in _context.Patients on reatments.PatientId equals patient.Id
                   join user in _context.Users on reatments.UserId equals user.Id
                   join operatoruser in _context.Users on reatments.OperatorUserId equals operatoruser.Id
                   orderby reatments.CreatedAt descending
                   select new
                   {
                       DoctorId = user.Id,
                       DoctorName = user.Name,

                       PatientId = patient.Id,
                       PatientNationalId = patient.NationalId,
                       PatientName = patient.FullName,
                       PatientGender = patient.Gender,
                       PatientBirthDate = patient.BirthDate,

                       Id = reatments.Id,
                       OrderNo = reatments.OrderNo,
                       Step = reatments.Step,
                       CreatedAt = reatments.CreatedAt,
                       UpdatedAt = reatments.UpdatedAt,
                       ReceiptUrl = reatments.ReceiptUrl,

                       OperatorUserName = operatoruser.Name
                   }
               ).ToList();


            if (!string.IsNullOrEmpty(patientname))
            {
                Treatments = Treatments.Where(p => p.PatientName.Contains(patientname))
                .ToList();
            }

            if (!string.IsNullOrEmpty(nationalId))
            {
                Treatments = Treatments.Where(p => p.PatientNationalId == nationalId).ToList();
            }

            if (doctortid != null)
            {
                Treatments = Treatments.Where(p => p.DoctorId == doctortid).ToList();
            }

            if (step != null)
            {
                Treatments = Treatments.Where(p => p.Step == step).ToList();
            }

            if (starttime != null && endtime != null)
            {
                Treatments = Treatments.Where(p => p.CreatedAt >= starttime && p.CreatedAt <= endtime).ToList();
            }

            return Ok(Treatments);
        }

        [Authorize(Roles = "Admin,Manager,User")]
        [HttpPost("Insert")]
        public IActionResult Insert([FromBody] TreatmentDto data)
        {
            try
            {
                var userId = User.FindFirst("UserId");

                // 檢查 DiscomfortAreas 數量限制
                if (data.DiscomfortAreas.Count > 5)
                {
                    return BadRequest("不適區域最多只能新增 5 筆資料");
                }

                var treatment = new Treatment
                {
                    OrderNo = DateTime.Now.ToString("yyyyMMddhhmmss"),
                    Step = TreatmentStep.Opencase,
                    DiscomfortPeriod = data.DiscomfortPeriod,
                    PossibleCauses = data.PossibleCauses,
                    TreatmentHistory = data.TreatmentHistory,
                    HowToKnowOur = data.HowToKnowOur,
                    HospitalFormUrl = data.HospitalFormUrl,
                    TreatmentConsentFormUrl = data.TreatmentConsentFormUrl,
                    HospitalFormRecordDate = data.HospitalFormRecordDate == null ? null : DateTime.Parse(data.HospitalFormRecordDate.ToString()).ToLocalTime(),
                    Subjective = data.Subjective,
                    Objective = data.Objective,
                    Assessment = data.Assessment,
                    Plan = data.Plan,
                    ReceiptUrl = data.ReceiptUrl,
                    PatientId = data.PatientId,
                    UserId = int.Parse(userId.Value),
                    OperatorUserId = int.Parse(userId.Value),
                    UpdatedAt = DateTime.Now
                };

                _context.Treatments.Add(treatment);
                _context.SaveChanges();

                // 添加 DiscomfortAreas
                foreach (var discomfortArea in data.DiscomfortAreas)
                {
                    var treatmentDiscomfortArea = new TreatmentDiscomfortArea
                    {
                        TreatmentId = treatment.Id,
                        FrontAndBack = discomfortArea.FrontAndBack,
                        DiscomfortArea = discomfortArea.DiscomfortArea,
                        DiscomfortSituation = discomfortArea.DiscomfortSituation,
                        DiscomfortDegree = discomfortArea.DiscomfortDegree
                    };
                    _context.TreatmentDiscomfortAreas.Add(treatmentDiscomfortArea);
                }
                _context.SaveChanges();

                var result = new
                {
                    Msg = "治療案件已新增",
                    OrderNo = treatment.OrderNo,
                    TreatmentId = treatment.Id
                };

                return Ok(result);
            }
            catch (Exception ex)
            {
                return StatusCode(500, $"新增失敗: {ex.Message}");
            }
        }

        [Authorize(Roles = "Admin,Manager,User")]
        [HttpPut("Update")]
        public IActionResult Update([FromBody] TreatmentDto data)
        {
            var userId = User.FindFirst("UserId");

            var treatment = _context.Treatments
                .Where(t => t.OrderNo == data.OrderNo && t.IsDelete == false)
                .FirstOrDefault();

            if (treatment == null)
            {
                return BadRequest("未找到案件資料");
            }

            // 檢查結案時的醫院診斷書開立時間
            if (data.Step == TreatmentStep.CaseClose)
            {
                if (!string.IsNullOrEmpty(data.HospitalFormUrl) && !data.HospitalFormRecordDate.HasValue)
                {
                    return BadRequest("請填寫醫院診斷書開立時間");
                }
            }

            // 檢查 DiscomfortAreas 數量限制
            if (data.DiscomfortAreas.Count > 5)
            {
                return BadRequest("不適區域最多只能新增 5 筆資料");
            }

            // 更新欄位
            treatment.DiscomfortPeriod = data.DiscomfortPeriod;
            treatment.PossibleCauses = data.PossibleCauses;
            treatment.TreatmentHistory = data.TreatmentHistory;
            treatment.HowToKnowOur = data.HowToKnowOur;
            treatment.HospitalFormUrl = data.HospitalFormUrl;
            treatment.TreatmentConsentFormUrl = data.TreatmentConsentFormUrl;
            treatment.HospitalFormRecordDate = data.HospitalFormRecordDate == null ? null : DateTime.Parse(data.HospitalFormRecordDate.ToString()).ToLocalTime();
            treatment.Subjective = data.Subjective;
            treatment.Objective = data.Objective;
            treatment.Assessment = data.Assessment;
            treatment.Plan = data.Plan;
            treatment.Step = data.Step;
            treatment.UpdatedAt = DateTime.Now;
            treatment.OperatorUserId = int.Parse(userId.Value);

            // 更新 DiscomfortAreas
            // 先刪除現有的 DiscomfortAreas
            var existingAreas = _context.TreatmentDiscomfortAreas
                .Where(tda => tda.TreatmentId == treatment.Id)
                .ToList();
            _context.TreatmentDiscomfortAreas.RemoveRange(existingAreas);

            // 添加新的 DiscomfortAreas
            foreach (var discomfortArea in data.DiscomfortAreas)
            {
                var treatmentDiscomfortArea = new TreatmentDiscomfortArea
                {
                    TreatmentId = treatment.Id,
                    FrontAndBack = discomfortArea.FrontAndBack,
                    DiscomfortArea = discomfortArea.DiscomfortArea,
                    DiscomfortSituation = discomfortArea.DiscomfortSituation,
                    DiscomfortDegree = discomfortArea.DiscomfortDegree
                };
                _context.TreatmentDiscomfortAreas.Add(treatmentDiscomfortArea);
            }

            _context.SaveChanges();

            return Ok("治療案件已更新");
        }

        [Authorize(Roles = "Admin,Manager")]
        [HttpGet("Delete")]
        public IActionResult Delete([FromQuery] string OrderNo)
        {
            var userId = User.FindFirst("UserId");

            var treatment = _context.Treatments
                .Where(p => p.OrderNo == OrderNo && p.IsDelete == false)
                .ToList();

            if (treatment.Count == 0)
            {
                return BadRequest("未找到案件資料");
            }

            treatment.First().IsDelete = true;
            treatment.First().OperatorUserId = int.Parse(userId.Value);
            treatment.First().UpdatedAt = DateTime.Now;

            _context.SaveChanges();

            return Ok("治療案件已刪除");
        }

        [Authorize(Roles = "Admin,Manager,User")]
        [HttpGet("GetCaseStatus")]
        public IActionResult GetCaseStatus([FromQuery] string nationalId)
        {
            var treatments = new List<Treatment>();

            treatments = _context.Treatments
                .Include(t => t.User)
                .Include(t => t.Patient)
                .Where(t => t.Step != TreatmentStep.CaseClose &&
                            t.Step != TreatmentStep.CreateReceipt &&
                            t.Patient.NationalId == nationalId && 
                            t.IsDelete == false)
                .OrderByDescending(p => p.CreatedAt)
                .ToList();

            if (treatments.Count > 0)
            {
                return BadRequest("治療案件尚未結束");
            }

            return Ok();
        }

        [Authorize(Roles = "Admin,Manager,User")]
        [HttpGet("GetLatestRecord/{patientId}")]
        public IActionResult GetLatestRecord(int patientId)
        {
            var latestRecord = _context.Treatments
                .Include(t => t.DiscomfortAreas.Where(da => !da.IsDelete))
                .Where(t => t.PatientId == patientId && (t.Step == TreatmentStep.CaseClose || t.Step == TreatmentStep.CreateReceipt))
                .OrderByDescending(t => t.CreatedAt)
                .FirstOrDefault();

            if (latestRecord == null)
            {
                return BadRequest("無上一次診療紀錄");
            }

            // 返回除了指定欄位外的所有資料
            var result = new
            {
                discomfortPeriod = latestRecord.DiscomfortPeriod,
                possibleCauses = latestRecord.PossibleCauses,
                treatmentHistory = latestRecord.TreatmentHistory,
                howToKnowOur = latestRecord.HowToKnowOur,
                subjective = latestRecord.Subjective,
                objective = latestRecord.Objective,
                assessment = latestRecord.Assessment,
                plan = latestRecord.Plan,
                patientId = latestRecord.PatientId,
                discomfortAreas = latestRecord.DiscomfortAreas.Select(da => new
                {
                    frontAndBack = da.FrontAndBack,
                    discomfortArea = da.DiscomfortArea,
                    discomfortSituation = da.DiscomfortSituation,
                    discomfortDegree = da.DiscomfortDegree
                }).ToList()
            };

            return Ok(result);
        }
    }
}

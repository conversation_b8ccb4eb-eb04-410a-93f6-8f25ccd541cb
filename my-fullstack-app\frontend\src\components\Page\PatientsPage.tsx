import { formatUtcToTaipei } from "../../utils/dateUtils";
import { Button } from 'primereact/button';
import { Calendar } from 'primereact/calendar';
import { Column } from 'primereact/column';
import { ConfirmDialog, confirmDialog } from 'primereact/confirmdialog';
import { DataTable } from 'primereact/datatable';
import { InputText } from "primereact/inputtext";
import LoadingSpinner from '../Common/LoadingSpinner';
import { Toast } from "primereact/toast";
import React, { useEffect, useRef, useState } from 'react';
import { useNavigate } from "react-router-dom";
import { ROUTES } from "../../constants/routes";
import usePatient from '../../hooks/usePatient';
import api from "../../services/api";
import { Card } from 'primereact/card';

const PatientsPage: React.FC = () => {
    const navigate = useNavigate();
    const toast = useRef<Toast>(null);
    const [name, setName] = useState('');
    const [starttime, setStarttime] = useState<Date | null | undefined>(undefined);
    const [endtime, setEndtime] = useState<Date | null | undefined>(undefined);
    const [refreshKey, setRefreshKey] = useState(0);
    const [deletedFlag, setDeletedFlag] = useState(false);

    const [searchParams, setSearchParams] = useState({
        name: '',
        starttime: null as Date | null | undefined,
        endtime: null as Date | null | undefined,
        refreshKey: 0,
    });

    const { patients, loading } = usePatient({
        fullName: searchParams.name,
        startTime: searchParams.starttime || null,
        endTime: searchParams.endtime || null,
        refreshKey
    });

    const genderdict: { [key: string]: string } = {
        "1": "男性",
        "2": "女性",
        "3": "其他"
    };

    useEffect(() => {
        if (deletedFlag && !loading) {
            toast.current?.show({ severity: "success", summary: "成功", detail: "病患資料已刪除" });
            setDeletedFlag(false); // 重置
        }
    }, [loading]);

    const handleSearchClick = () => {
        setRefreshKey(refreshKey + 1)
        setSearchParams({ name, starttime, endtime, refreshKey});
    };

    const handleAddClick = () => {
        navigate("/patientsdetail");
    };

    const handleDelete = async (Id:string) => {
        try {
            await api.get("/api/patients/Delete",  {
                    params: { 
                        id: Id
                    }
                }
            );
            setDeletedFlag(true);
            Reload();
        } catch (error:any) {
            var detail =  error.status === 403 ? "您無權限，請通知管理員" : error.response?.data?.message || '刪除失敗';
            toast.current?.show({ severity: "error", summary: "錯誤", detail: detail });
            
        }
    };

    const Reload = () => {
        // 重新觸發 usePatient，等於重新查詢
        setRefreshKey(prev => prev + 1);
    }

    const NewCase = async (id: string) => {
        navigate(ROUTES.TREATMENT_DETAIL, { state: { patient: { id: id} } })   
    }

    const Edit = async (id: string) => {
        try {
            const Response = await api.get('/api/patients/', {
                params: {
                    id: id
                }
            });
    
            const Data = Response.data;
            
            if (Data) {
                navigate(ROUTES.PATIENT_DETAIL, { state: { patient: Data } })
            }
        } catch (error:any) {
            var detail =  error.status === 403 ? "您無權限，請通知管理員" : error.response?.data?.message || '編輯失敗';
            toast.current?.show({ severity: "error", summary: "錯誤", detail: detail });
        }
    }

    const paginatorLeft = (
        <Button
            type="button"
            icon="pi pi-refresh"
            text
            onClick={() => Reload()}
        />
    );
    const paginatorRight = <div></div>;

    const optionBodyTemplate = (rowData: any) => {
        return (
            <div className="flex gap-1">
                    <Button 
                        label="編輯" 
                        type="button" 
                        icon="pi pi-file-edit" 
                        onClick={() => Edit(rowData.id)}
                        size="small" 
                        severity="info" 
                        style={{ fontSize: '1rem', margin: '3px' }} 
                    />
                    <Button 
                        label="開案" 
                        type="button" 
                        icon="pi pi-clipboard" 
                        onClick={() => NewCase(rowData.id)} 
                        size="small" 
                        severity="success" 
                        style={{ fontSize: '1rem', margin: '3px' }}
                    />
                    <Button 
                        label="刪除" 
                        type="button" 
                        icon="pi pi-file-excel" 
                        onClick={()=> confirm(rowData.id)} 
                        size="small" 
                        severity="danger" 
                        style={{  fontSize: '1rem', margin: '3px' }} 
                    />
            </div>
        );
    };

    const confirm = (Id:string) => {
        confirmDialog({
            message: '確定要刪除這筆資料嗎？',
            header: '刪除確認',
            icon: 'pi pi-exclamation-triangle',
            defaultFocus: 'reject',
            acceptClassName: 'p-button-danger',
            acceptLabel: '確定',
            rejectLabel: '取消',
            accept: () => handleDelete(Id),
        });
    };

    const genderBodyTemplate = (rowData: any) => {
        var data = String(rowData.gender)
        const gendar = genderdict[data]
        return (
            <div>
                {gendar}
            </div>
        );
    };

    const formatDate = (value: string) => {
    if (!value) return '';
    return formatUtcToTaipei(value, "yyyy/MM/dd HH:mm:ss");
  };

    const formatAge = (value: string) => {
        if (!value) return "";
        const date = new Date(value);
        const today = new Date();
        let age = today.getFullYear() - date.getFullYear();

        const hasNotHadBirthdayThisYear =
            today.getMonth() < date.getMonth() ||
            (today.getMonth() === date.getMonth() && today.getDate() < date.getDate());

        if (hasNotHadBirthdayThisYear) {
            age--;
        }

        return age;
        
    };

    if (loading) {
        return <LoadingSpinner message="載入病患資料中..." />;
    }

    return (
        <div>
            <Toast ref={toast} />
            <ConfirmDialog />
            <Card title="病患管理" className="mb-4">
                <p className="text-600 line-height-3 m-0">
                    病患管理頁面，可以查詢、新增、編輯、刪除病患資料。
                </p>
            </Card>

            {/* 搜尋條件 */}
            <Card className="mb-4">
                <div className="grid">
                    <div className="col-12 md:col-4">
                        <InputText
                            id="name"
                            type="text"
                            value={name}
                            onChange={(e) => setName(e.target.value)}
                            placeholder="病患姓名"
                            className="w-full"
                        />
                    </div>
                    <div className="col-6 md:col-3">
                        <Calendar 
                            id="starttime" 
                            value={starttime} 
                            onChange={(e) => setStarttime(e.value)} 
                            placeholder="開始時間"
                            className="w-full"
                            dateFormat="yy/mm/dd" 
                            showIcon/>
                    </div>
                    <div className="col-6 md:col-3">
                        <Calendar 
                            id="endtime" 
                            value={endtime} 
                            onChange={(e) => setEndtime(e.value)} 
                            placeholder="結束時間"
                            className="w-full"
                            dateFormat="yy/mm/dd"  
                            showIcon/>
                    </div>
                    <div className="col-12 md:col-4">
                        <div className="flex gap-2">
                            <Button 
                                label="查詢" 
                                icon="pi pi-search" 
                                onClick={handleSearchClick}/>
                            <Button 
                                label="新增" 
                                icon="pi pi-plus" 
                                onClick={handleAddClick} />
                        </div>
                    </div>
                </div>
            </Card>

            {/* 病患列表 */}
            <Card>
                <DataTable
                    value={patients}
                    paginator
                    rows={10}
                    rowsPerPageOptions={[10, 20, 30, 40]}
                    tableStyle={{ minWidth: '50rem' }}
                    emptyMessage="沒有找到病患資料"
                    paginatorLeft={paginatorLeft}
                    paginatorRight={paginatorRight}
                >
                    <Column field="id" header="ID" style={{ width: '5%' }} />
                    <Column field="fullName" header="姓名" style={{ width: '10%' }} />
                    <Column field="gender" header="性別" style={{ width: '5%' }} body={genderBodyTemplate}/>
                    <Column field="birthDate" header="年齡" style={{ width: '5%' }} body={(rowData) => formatAge(rowData.birthDate)}/>
                    <Column field="createdAt" header="新增日期" style={{ width: '10%' }} body={(rowData) => formatDate(rowData.createdAt)} />
                    <Column field="updatedAt" header="更新日期" style={{ width: '10%' }} body={(rowData) => formatDate(rowData.updatedAt)} />
                    <Column field="operatorUserName" header="操作人" style={{ width: '5%' }} />
                    <Column field="option" header="功能" style={{ width: '12%' }} body={optionBodyTemplate} />
                </DataTable>
                  
            </Card>
        </div>

        
    );
};

export default PatientsPage;
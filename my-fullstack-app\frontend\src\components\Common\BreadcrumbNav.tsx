import { BreadCrumb } from 'primereact/breadcrumb';
import { MenuItem } from 'primereact/menuitem';
import React from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import { ROUTES } from '../../constants/routes';

interface BreadcrumbNavProps {
  customItems?: MenuItem[];
  className?: string;
}

const BreadcrumbNav: React.FC<BreadcrumbNavProps> = ({ 
  customItems, 
  className = '' 
}) => {
  const location = useLocation();
  const navigate = useNavigate();

  // 路由到中文名稱的映射
  const routeNameMap: Record<string, string> = {
    [ROUTES.HOME]: '首頁',
    [ROUTES.DOCTORS]: '治療師列表',
    [ROUTES.DOCTOR_DETAIL]: '治療師詳細資料',
    [ROUTES.TREATMENTS]: '診療紀錄',
    [ROUTES.TREATMENT_DETAIL]: '診療詳情',
    [ROUTES.PATIENTS]: '病患列表',
    [ROUTES.PATIENT_DETAIL]: '病患詳情',
    [ROUTES.SCHEDULES]: '排班表',
    [ROUTES.RECEIPTS]: '收據列表',
    [ROUTES.RECEIPT_DETAIL]: '收據詳情',
    [ROUTES.USERS]: '權限管理',
    [ROUTES.DEBUG]: '除錯頁面',
    [ROUTES.BACKUP_MANAGEMENT]: '備份監控',
    [ROUTES.REPORT_MANAGEMENT]: '報表管理',
    [ROUTES.IMAGE_MANAGEMENT]: '圖片管理',
    [ROUTES.LOGIN_LOGS]: '登入紀錄',
    [ROUTES.IP_BLOCKS]: 'IP封鎖',
  };

  // 生成麵包屑項目
  const generateBreadcrumbItems = (): MenuItem[] => {
    if (customItems) {
      return customItems;
    }

    const items: MenuItem[] = [];

    // 首頁項目
    items.push({
      label: '首頁',
      icon: 'pi pi-home',
      command: () => navigate(ROUTES.HOME),
    });

    // 如果不是首頁，添加當前頁面
    if (location.pathname !== ROUTES.HOME) {
      const currentPath = location.pathname;
      let currentRouteName = routeNameMap[currentPath];

      // 檢查是否是詳細頁面
      if (currentPath.includes('detail')) {
        let parentRoute = '';
        let parentRouteName = '';

        // 根據詳情頁面確定父級路由
        if (currentPath === ROUTES.TREATMENT_DETAIL) {
          parentRoute = ROUTES.TREATMENTS;
          parentRouteName = routeNameMap[ROUTES.TREATMENTS] || '診療紀錄';
        } else if (currentPath === ROUTES.PATIENT_DETAIL) {
          parentRoute = ROUTES.PATIENTS;
          parentRouteName = routeNameMap[ROUTES.PATIENTS] || '病患列表';
        } else if (currentPath === ROUTES.RECEIPT_DETAIL) {
          parentRoute = ROUTES.RECEIPTS;
          parentRouteName = routeNameMap[ROUTES.RECEIPTS] || '收據列表';
        } else if (currentPath === ROUTES.DOCTOR_DETAIL) {
          parentRoute = ROUTES.DOCTORS;
          parentRouteName = routeNameMap[ROUTES.DOCTORS] || '治療師管理';
        }

        // 添加父級路由
        if (parentRoute && parentRouteName) {
          items.push({
            label: parentRouteName,
            command: () => navigate(parentRoute),
          });
        }

        // 如果沒有找到當前路由名稱，使用預設名稱
        if (!currentRouteName) {
          if (currentPath === ROUTES.TREATMENT_DETAIL) {
            currentRouteName = '診療詳情';
          } else if (currentPath === ROUTES.PATIENT_DETAIL) {
            currentRouteName = '病患詳情';
          } else if (currentPath === ROUTES.RECEIPT_DETAIL) {
            currentRouteName = '收據詳情';
          } else if (currentPath === ROUTES.DOCTOR_DETAIL) {
            currentRouteName = '治療師詳細資料';
          } else {
            currentRouteName = '詳情頁面';
          }
        }
      }

      // 如果還是沒有找到路由名稱，使用未知頁面
      if (!currentRouteName) {
        currentRouteName = '未知頁面';
      }

      items.push({
        label: currentRouteName,
      });
    }

    return items;
  };

  const breadcrumbItems = generateBreadcrumbItems();

  // 首頁項目
  const home: MenuItem = {
    icon: 'pi pi-home',
    command: () => navigate(ROUTES.HOME),
  };

  return (
    <div className={`breadcrumb-nav ${className}`}>
      <BreadCrumb 
        model={breadcrumbItems.slice(1)} // 移除首頁，因為 home prop 會處理
        home={home}
        className="border-none bg-transparent p-0"
      />
    </div>
  );
};

export default BreadcrumbNav;

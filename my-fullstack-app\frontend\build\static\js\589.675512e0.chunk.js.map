{"version": 3, "file": "static/js/589.675512e0.chunk.js", "mappings": "kNAQe,SAASA,IACpB,MAAMC,GAAQC,EAAAA,EAAAA,QAAc,OACrBC,EAAYC,IAAiBC,EAAAA,EAAAA,UAAwB,OACrDC,EAAOC,IAAYF,EAAAA,EAAAA,UAAS,IAkFnC,OACIG,EAAAA,EAAAA,MAAA,OAAKC,UAAU,mCAAkCC,SAAA,EAC7CF,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACAC,EAAAA,EAAAA,KAACC,EAAAA,EAAK,CAACC,IAAKZ,KACZU,EAAAA,EAAAA,KAACG,EAAAA,EAAU,CACPC,KAAK,QACLC,cAAY,EACZC,cAtFeC,UAA0C,IAADC,EAChE,MAAMC,EAAkB,QAAdD,EAAGE,EAAMC,aAAK,IAAAH,OAAA,EAAXA,EAAc,GAC3B,IAAKC,EAAM,OAEX,MAAMG,EAAW,IAAIC,SACrBD,EAASE,OAAO,OAAQL,GAExB,IAAK,IAADM,EACA,MAAMC,QAAiBC,EAAAA,EAAIC,KAAK,yBAA0BN,EAAU,CAChEO,QAAS,CACL,eAAgB,yBAIxBC,QAAQC,IAAIL,EAASM,MAER,QAAbP,EAAAzB,EAAMiC,eAAO,IAAAR,GAAbA,EAAeS,KAAK,CAChBC,SAAU,UACVC,QAAS,eACTC,OAAQ,kCAEhB,CAAE,MAAOC,GAAQ,IAADC,EACC,QAAbA,EAAAvC,EAAMiC,eAAO,IAAAM,GAAbA,EAAeL,KAAK,CAChBC,SAAU,QACVC,QAAS,eACTC,OAAQ,4BAEhB,GA4DQG,OAAO,UACPC,YAAa,IACbC,YAAY,iCAGhBhC,EAAAA,EAAAA,KAAA,OAAAD,UACIF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,aAAYC,SAAA,EACvBC,EAAAA,EAAAA,KAACiC,EAAAA,EAAS,CAACtC,MAAOA,EAAOuC,SAAWC,GAAMvC,EAASuC,EAAEC,OAAOzC,UAC5DK,EAAAA,EAAAA,KAACqC,EAAAA,EAAM,CAACC,MAAM,eAAKC,KAAK,iBAAiBC,QAASA,IAhE3CjC,WACnB,IAAK,IAADkC,EACA,MAAMzB,QAAiBC,EAAAA,EAAIyB,IAAI,2BAA4B,CACvDC,OAAQ,CAAEC,SAAUC,GACpBC,aAAc,SAGZC,EAAMC,OAAOC,IAAIC,gBAAgB,IAAIC,KAAK,CAACnC,EAASM,QACpD8B,EAAOC,SAASC,cAAc,KACpCF,EAAKG,KAAOR,EACZK,EAAKI,aAAa,WAAYX,GAC9BQ,SAASI,KAAKC,YAAYN,GAC1BA,EAAKO,QACLN,SAASI,KAAKG,YAAYR,GAEb,QAAbX,EAAAnD,EAAMiC,eAAO,IAAAkB,GAAbA,EAAejB,KAAK,CAChBC,SAAU,UACVC,QAAS,2BACTC,OAAQkB,GAEhB,CAAE,MAAOjB,GAAQ,IAADiC,EACC,QAAbA,EAAAvE,EAAMiC,eAAO,IAAAsC,GAAbA,EAAerC,KAAK,CAChBC,SAAU,QACVC,QAAS,2BACTC,OAAQmC,OAAOlC,IAEvB,GAsCmEmC,CAAepE,GAAQ8B,SAAS,aACvFzB,EAAAA,EAAAA,KAACqC,EAAAA,EAAM,CAACC,MAAM,eAAKC,KAAK,YAAYC,QAASA,IAnCvCjC,WAClB,IACI,MAAMS,QAAiBC,EAAAA,EAAIyB,IAAI,2BAA4B,CACvDC,OAAQ,CAAEC,SAAUC,GACpBC,aAAc,SAGZC,EAAMC,OAAOC,IAAIC,gBAAgB,IAAIC,KAAK,CAACnC,EAASM,QAC1D7B,EAAcsD,EAClB,CAAE,MAAOnB,GAAQ,IAADoC,EACC,QAAbA,EAAA1E,EAAMiC,eAAO,IAAAyC,GAAbA,EAAexC,KAAK,CAChBC,SAAU,QACVC,QAAS,2BACTC,OAAQmC,OAAOlC,IAEvB,GAoB8DqC,CAActE,GAAQ8B,SAAS,eAGzFzB,EAAAA,EAAAA,KAAA,OAAAD,UACIC,EAAAA,EAAAA,KAAA,OAAAD,SACKP,IACGQ,EAAAA,EAAAA,KAACkE,EAAAA,EAAK,CAACC,IAAK3E,EAAY4E,IAAI,2BAAOC,MAAM,MAAMC,SAAO,UAO9E,C,0DCvHA,SAASC,IACP,OAAOA,EAAWC,OAAOC,OAASD,OAAOC,OAAOC,OAAS,SAAUC,GACjE,IAAK,IAAIxC,EAAI,EAAGA,EAAIyC,UAAUC,OAAQ1C,IAAK,CACzC,IAAI2C,EAAIF,UAAUzC,GAClB,IAAK,IAAI4C,KAAKD,GAAG,CAAG,GAAEE,eAAeC,KAAKH,EAAGC,KAAOJ,EAAEI,GAAKD,EAAEC,GAC/D,CACA,OAAOJ,CACT,EAAGJ,EAASW,MAAM,KAAMN,UAC1B,CAEA,IAAIO,EAAwBC,EAAAA,KAAwBA,EAAAA,YAAiB,SAAUC,EAASnF,GACtF,IAAIoF,EAAMC,EAAAA,EAASC,OAAOH,GAC1B,OAAoBD,EAAAA,cAAoB,MAAOb,EAAS,CACtDrE,IAAKA,EACLmE,MAAO,KACPoB,OAAQ,KACRC,QAAS,YACTC,KAAM,OACNC,MAAO,8BACNN,GAAmBF,EAAAA,cAAoB,OAAQ,CAChDS,EAAG,0xBACHF,KAAM,iBAEV,KACAR,EAASW,YAAc,U", "sources": ["components/Page/DebugPage.tsx", "../node_modules/primereact/icons/plus/index.esm.js"], "sourcesContent": ["import { <PERSON><PERSON> } from \"primereact/button\";\r\nimport { FileUpload, FileUploadHandlerEvent } from \"primereact/fileupload\";\r\nimport { Image } from 'primereact/image';\r\nimport { InputText } from \"primereact/inputtext\";\r\nimport { Toast } from \"primereact/toast\";\r\nimport { useRef, useState } from \"react\";\r\nimport api from \"../../services/api\";\r\n\r\nexport default function DebugPage() {\r\n    const toast = useRef<Toast>(null);\r\n    const [previewUrl, setPreviewUrl] = useState<string | null>(null);\r\n    const [value, setValue] = useState('');\r\n\r\n    // 上傳\r\n    const handleCustomUpload = async (event: FileUploadHandlerEvent) => {\r\n        const file = event.files?.[0];\r\n        if (!file) return;\r\n\r\n        const formData = new FormData();\r\n        formData.append(\"file\", file); // 注意：這裡的 key 要和後端接收的參數一致\r\n\r\n        try {\r\n            const response = await api.post(\"/api/system/UploadFile\", formData, {\r\n                headers: {\r\n                    \"Content-Type\": \"multipart/form-data\",\r\n                },\r\n            });\r\n\r\n            console.log(response.data);\r\n\r\n            toast.current?.show({\r\n                severity: \"success\",\r\n                summary: \"成功\",\r\n                detail: \"檔案已上傳\",\r\n            });\r\n        } catch (error) {\r\n            toast.current?.show({\r\n                severity: \"error\",\r\n                summary: \"錯誤\",\r\n                detail: \"上傳失敗\",\r\n            });\r\n        }\r\n    };\r\n\r\n    // 下載\r\n    const handleDownload = async (fileName: string) => {\r\n        try {\r\n            const response = await api.get(`/api/system/DownloadFile`, {\r\n                params: { filename: fileName },\r\n                responseType: \"blob\",\r\n            });\r\n\r\n            const url = window.URL.createObjectURL(new Blob([response.data]));\r\n            const link = document.createElement(\"a\");\r\n            link.href = url;\r\n            link.setAttribute(\"download\", fileName);\r\n            document.body.appendChild(link);\r\n            link.click();\r\n            document.body.removeChild(link);\r\n\r\n            toast.current?.show({\r\n                severity: \"success\",\r\n                summary: \"下載完成\",\r\n                detail: fileName,\r\n            });\r\n        } catch (error) {\r\n            toast.current?.show({\r\n                severity: \"error\",\r\n                summary: \"下載失敗\",\r\n                detail: String(error),\r\n            });\r\n        }\r\n    };\r\n\r\n    // 預覽\r\n    const handlePreview = async (fileName: string) => {\r\n        try {\r\n            const response = await api.get(`/api/system/DownloadFile`, {\r\n                params: { filename: fileName },\r\n                responseType: \"blob\",\r\n            });\r\n\r\n            const url = window.URL.createObjectURL(new Blob([response.data]));\r\n            setPreviewUrl(url);\r\n        } catch (error) {\r\n            toast.current?.show({\r\n                severity: \"error\",\r\n                summary: \"預覽失敗\",\r\n                detail: String(error),\r\n            });\r\n        }\r\n    };\r\n\r\n    return (\r\n        <div className=\"card flex justify-content-center\">\r\n            <div>\r\n            <Toast ref={toast} />\r\n            <FileUpload\r\n                mode=\"basic\"\r\n                customUpload\r\n                uploadHandler={handleCustomUpload}\r\n                accept=\"image/*\"\r\n                maxFileSize={1000000}\r\n                chooseLabel=\"選擇檔案\"\r\n            />\r\n            </div>\r\n            <div>\r\n                <div className=\"flex gap-2\">\r\n                    <InputText value={value} onChange={(e) => setValue(e.target.value)} />\r\n                    <Button label=\"下載\" icon=\"pi pi-download\" onClick={() =>handleDownload(value)} severity=\"success\" />\r\n                    <Button label=\"預覽\" icon=\"pi pi-eye\" onClick={() =>handlePreview(value)} severity=\"info\" />\r\n                </div>\r\n            </div>\r\n            <div>\r\n                <div>\r\n                    {previewUrl && (\r\n                        <Image src={previewUrl} alt=\"預覽圖片\" width=\"250\" preview />\r\n                    )}\r\n                </div>\r\n            </div>\r\n        </div>\r\n        \r\n    );\r\n}", "'use client';\nimport * as React from 'react';\nimport { IconBase } from 'primereact/iconbase';\n\nfunction _extends() {\n  return _extends = Object.assign ? Object.assign.bind() : function (n) {\n    for (var e = 1; e < arguments.length; e++) {\n      var t = arguments[e];\n      for (var r in t) ({}).hasOwnProperty.call(t, r) && (n[r] = t[r]);\n    }\n    return n;\n  }, _extends.apply(null, arguments);\n}\n\nvar PlusIcon = /*#__PURE__*/React.memo(/*#__PURE__*/React.forwardRef(function (inProps, ref) {\n  var pti = IconBase.getPTI(inProps);\n  return /*#__PURE__*/React.createElement(\"svg\", _extends({\n    ref: ref,\n    width: \"14\",\n    height: \"14\",\n    viewBox: \"0 0 14 14\",\n    fill: \"none\",\n    xmlns: \"http://www.w3.org/2000/svg\"\n  }, pti), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M7.67742 6.32258V0.677419C7.67742 0.497757 7.60605 0.325452 7.47901 0.198411C7.35197 0.0713707 7.17966 0 7 0C6.82034 0 6.64803 0.0713707 6.52099 0.198411C6.39395 0.325452 6.32258 0.497757 6.32258 0.677419V6.32258H0.677419C0.497757 6.32258 0.325452 6.39395 0.198411 6.52099C0.0713707 6.64803 0 6.82034 0 7C0 7.17966 0.0713707 7.35197 0.198411 7.47901C0.325452 7.60605 0.497757 7.67742 0.677419 7.67742H6.32258V13.3226C6.32492 13.5015 6.39704 13.6725 6.52358 13.799C6.65012 13.9255 6.82106 13.9977 7 14C7.17966 14 7.35197 13.9286 7.47901 13.8016C7.60605 13.6745 7.67742 13.5022 7.67742 13.3226V7.67742H13.3226C13.5022 7.67742 13.6745 7.60605 13.8016 7.47901C13.9286 7.35197 14 7.17966 14 7C13.9977 6.82106 13.9255 6.65012 13.799 6.52358C13.6725 6.39704 13.5015 6.32492 13.3226 6.32258H7.67742Z\",\n    fill: \"currentColor\"\n  }));\n}));\nPlusIcon.displayName = 'PlusIcon';\n\nexport { PlusIcon };\n"], "names": ["DebugPage", "toast", "useRef", "previewUrl", "setPreviewUrl", "useState", "value", "setValue", "_jsxs", "className", "children", "_jsx", "Toast", "ref", "FileUpload", "mode", "customUpload", "uploadHandler", "async", "_event$files", "file", "event", "files", "formData", "FormData", "append", "_toast$current", "response", "api", "post", "headers", "console", "log", "data", "current", "show", "severity", "summary", "detail", "error", "_toast$current2", "accept", "maxFileSize", "<PERSON><PERSON><PERSON><PERSON>", "InputText", "onChange", "e", "target", "<PERSON><PERSON>", "label", "icon", "onClick", "_toast$current3", "get", "params", "filename", "fileName", "responseType", "url", "window", "URL", "createObjectURL", "Blob", "link", "document", "createElement", "href", "setAttribute", "body", "append<PERSON><PERSON><PERSON>", "click", "<PERSON><PERSON><PERSON><PERSON>", "_toast$current4", "String", "handleDownload", "_toast$current5", "handlePreview", "Image", "src", "alt", "width", "preview", "_extends", "Object", "assign", "bind", "n", "arguments", "length", "t", "r", "hasOwnProperty", "call", "apply", "PlusIcon", "React", "inProps", "pti", "IconBase", "getPTI", "height", "viewBox", "fill", "xmlns", "d", "displayName"], "sourceRoot": ""}
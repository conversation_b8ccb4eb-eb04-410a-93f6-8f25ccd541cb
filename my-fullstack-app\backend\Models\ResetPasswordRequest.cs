using System.ComponentModel.DataAnnotations;

namespace MyApi.Models
{
    public class ResetPasswordRequest
    {
        [Required]
        public int UserId { get; set; }
    }

    public class UpdatePasswordRequest
    {
        [Required]
        public int UserId { get; set; }

        [Required]
        public string OldPassword { get; set; } = string.Empty;

        [Required]
        [MinLength(6)]
        public string NewPassword { get; set; } = string.Empty;

        [Required]
        public string ConfirmPassword { get; set; } = string.Empty;
    }
}

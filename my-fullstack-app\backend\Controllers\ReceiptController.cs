﻿using Microsoft.AspNetCore.Mvc;
using MyApi.Data;
using System.Linq;
using MyApi.Models;
using System.IO;
using System;
using QuestPDF.Fluent;
using QuestPDF.Helpers;
using QuestPDF.Infrastructure;
using QuestPDF.Drawing;
using QuestPDF.Elements;
using static MyApi.Helpers.Enums;
using System.Collections.Generic;
using System.Diagnostics;
using Microsoft.AspNetCore.SignalR;
using MyApi.Helpers;
using System.Threading.Tasks;
using Microsoft.Extensions.Configuration;
using Microsoft.EntityFrameworkCore;
using Microsoft.AspNetCore.Hosting;
using Microsoft.Extensions.Hosting;
using Microsoft.AspNetCore.Authorization;
using MyApi.Services;
using Swashbuckle.AspNetCore.SwaggerGen;

namespace MyApi.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class ReceiptController : ControllerBase
    {
        private readonly AppDbContext _context;
        private readonly string _reportPath = Path.Combine(Directory.GetCurrentDirectory(), "wwwroot");
        private readonly IHubContext<ReportHub> _hub;
        private readonly IWebHostEnvironment _env;
        private readonly IGmailService _gmailService;

        public ReceiptController(AppDbContext context, IHubContext<ReportHub> hub, IWebHostEnvironment evn, IGmailService gmailService)
        {
            _context = context;
            _hub = hub;
            _env = evn;
            _gmailService = gmailService;
        }

        [Authorize(Roles = "Admin,Manager,User")]
        [HttpGet]
        public IActionResult Get([FromQuery] int Id)
        {
            var receipts = _context.Receipts
                    .Where(p => p.TreatmentId == Id &&
                                p.IsDelete == false)
                    .ToList();


            return Ok(receipts);
        }

        [Authorize(Roles = "Admin,Manager,User")]
        [HttpGet("GetList")]
        public IActionResult GetList([FromQuery] string patientname, string? nationalId, DateTime? starttime, DateTime? endtime)
        {
            var Receipts = (
                   from receipts in _context.Receipts
                   join patient in _context.Patients on receipts.PatientId equals patient.Id
                   join treatment in _context.Treatments on receipts.TreatmentId equals treatment.Id
                   join operatoruser in _context.Users on receipts.OperatorUserId equals operatoruser.Id
                   where !string.IsNullOrEmpty(treatment.ReceiptUrl)
                   orderby receipts.CreatedAt descending
                   select new
                   {
                       PatientId = patient.Id,
                       PatientNationalId = patient.NationalId,
                       PatientName = patient.FullName,
                       PatientGender = patient.Gender,
                       PatientBirthDate = patient.BirthDate,
                       PatientEmail = patient.Email,

                       Id = treatment.Id,
                       OrderNo = treatment.OrderNo,
                       ReceiptUrl = treatment.ReceiptUrl,

                       ReceiptOrderNo = receipts.OrderNo,
                       ReceiptCreatedAt = receipts.CreatedAt,
                       ReceiptUpdatedAt = receipts.UpdatedAt,
                       ReceiptOperatorUserName = operatoruser.Name,
                   }
               ).ToList();

            if (!string.IsNullOrEmpty(patientname))
            {
                Receipts = Receipts.Where(p => p.PatientName.Contains(patientname))
                .ToList();
            }

            if (!string.IsNullOrEmpty(nationalId))
            {
                Receipts = Receipts.Where(p => p.PatientNationalId == nationalId).ToList();
            }

            if (starttime != null && endtime != null)
            {
                Receipts = Receipts.Where(p => p.ReceiptCreatedAt >= starttime && p.ReceiptUpdatedAt <= endtime).ToList();
            }

            var result = Receipts
            .GroupBy(x => new { x.PatientId, x.ReceiptOrderNo })
            .Select(g => g.First())
            .ToList();

            return Ok(result);
        }

        [Authorize(Roles = "Admin,Manager,User")]
        [HttpPost("Insert")]
        public IActionResult Insert([FromBody] List<Receipt> data)
        {
            try
            {
                var userId = User.FindFirst("UserId");

                var treatments = _context.Treatments
                .Where(p => p.Id == data.First().TreatmentId && 
                            p.IsDelete == false)
                .ToList();

                if (treatments.Count > 0)
                {
                    if (treatments.First().Step != TreatmentStep.CaseClose &&
                        treatments.First().Step != TreatmentStep.CreateReceipt)
                    {
                        return BadRequest("此病患尚未結束療程");
                    }
                    if (!string.IsNullOrEmpty(treatments.First().ReceiptUrl))
                    {
                        return BadRequest("此案件收據已開立");
                    }
                }
                else
                {
                    return BadRequest("未找到案件資料");
                }

                var receipts = _context.Receipts
                    .Where(p => p.TreatmentId == data.First().TreatmentId &&
                                p.IsDelete == false)
                    .ToList();

                if (receipts.Count > 0)
                {
                    return BadRequest("此案件收據已存在");
                }

                var datetimestr = DateTime.Now.ToString("yyyyMMdd");

                var OrderNomber = _context.Receipts
                    .Where(p => p.OrderNo.Contains(datetimestr))
                    .OrderByDescending(p => p.OrderNo)
                    .Select(p => p.OrderNo)
                    .Take(1)
                    .ToList();

                var OrderNo = "";

                if (OrderNomber.Count > 0)
                {
                    OrderNo = (Convert.ToInt64(OrderNomber.FirstOrDefault()) + 1).ToString();
                }
                else
                {
                    OrderNo = datetimestr + "1".PadLeft(3, '0');
                }

                var OperatorUserId = int.Parse(userId.Value);
                var UpdatedAt = DateTime.Now;


                foreach (var item in data)
                {

                    item.OrderNo = OrderNo;
                    item.OperatorUserId = OperatorUserId;
                    item.UpdatedAt = UpdatedAt;
                       
                    _context.Receipts.Add(item);
                }

                treatments.First().Step = TreatmentStep.CreateReceipt;
                treatments.First().OperatorUserId = OperatorUserId;
                treatments.First().UpdatedAt = UpdatedAt;

                _context.SaveChanges();

                var result = new
                {
                    Msg = "收據資料已新增",
                    OrderNo = OrderNo
                };

                return Ok(result);
            }
            catch (Exception ex)
            {
                return StatusCode(500, $"新增失敗: {ex.Message}");
            }
        }

        [Authorize(Roles = "Admin,Manager,User")]
        [HttpPut("Update")]
        public IActionResult Update([FromBody] List<Receipt> data)
        {
            var userId = User.FindFirst("UserId");

            var treatments = _context.Treatments
                .Where(p => p.Id == data.First().TreatmentId &&
                            p.IsDelete == false)
                .ToList();

            if (treatments.Count > 0)
            {
                if (treatments.First().Step != TreatmentStep.CaseClose &&
                    treatments.First().Step != TreatmentStep.CreateReceipt)
                {
                    return BadRequest("此病患尚未結束療程");
                }
                if (!string.IsNullOrEmpty(treatments.First().ReceiptUrl))
                {
                    return BadRequest("此案件收據已開立");
                }
            }
            else
            {
                return BadRequest("未找到案件資料");
            }

            var toDelete = _context.Receipts
                .Where(p => p.TreatmentId == data.First().TreatmentId &&
                            p.OrderNo == data.First().OrderNo)
                .ToList();

            _context.Receipts.RemoveRange(toDelete);


            var OperatorUserId = int.Parse(userId.Value);
            var UpdatedAt = DateTime.Now;

            foreach (var item in data)
            {
                item.Id = 0;
                item.OperatorUserId = OperatorUserId;
                item.UpdatedAt = UpdatedAt;

                _context.Receipts.Add(item);
            }

            _context.SaveChanges();

            return Ok("治療案件已更新");
        }

        public List<string> ReceiptsTitle(int Titlecode)
        {
            List<string> receiptsTitle = new List<string>();

            switch (Titlecode)
            {
                case 1:
                    receiptsTitle.Add("繳款人收執聯");
                    break;
                case 2:
                    receiptsTitle.Add("單位存根聯");
                    break;
                case 3:
                    receiptsTitle.Add("單位扣底聯");
                    break;
                default:
                    receiptsTitle.Add("繳款人收執聯");
                    receiptsTitle.Add("單位存根聯");
                    receiptsTitle.Add("單位扣底聯");
                    break;
            }

            return receiptsTitle;
        }

        [Authorize(Roles = "Admin,Manager,User")]
        [HttpGet("ExportReceiptsPdf")]
        public async Task<IActionResult> ExportReceiptsPdf([FromQuery] int TreatmentId, string OrderNo, string connectionId, [FromServices] IHubContext<ReportHub> hub)
        {

            if (!Directory.Exists(_reportPath))
                Directory.CreateDirectory(_reportPath);

            if (string.IsNullOrEmpty(OrderNo))
                return BadRequest("未找到收據編號，請先進行存擋");

            var receipts = _context.Receipts
                .Where(p => p.TreatmentId == TreatmentId && p.OrderNo == OrderNo)
                .ToList();

            if (receipts.Count == 0)
                return BadRequest("此案件收據無資料");

            var treatments = _context.Treatments
                .Where(p => p.Id == TreatmentId && !p.IsDelete)
                .FirstOrDefault();

            if (treatments == null)
                return BadRequest("未找到案件資料");

            await hub.Clients.Client(connectionId).SendAsync("ReportProgress", 5);

            var patients = _context.Patients
                .Where(p => p.Id == receipts.First().PatientId && !p.IsDelete)
                .FirstOrDefault();

            if (patients == null)
                return BadRequest("未找到病患資料");

            var filePath = string.Empty;

            await hub.Clients.Client(connectionId).SendAsync("ReportProgress", 20);

            Dictionary<string, List<Receipt>> receiptDic = new Dictionary<string, List<Receipt>>();
            Dictionary<string, Treatment> treatmentDic = new Dictionary<string, Treatment>();
            Dictionary<string, Patient> patientDic = new Dictionary<string, Patient>();
            receiptDic.Add(receipts.First().OrderNo, receipts);
            treatmentDic.Add(receipts.First().OrderNo, treatments);
            patientDic.Add(receipts.First().OrderNo, patients);

            if (string.IsNullOrEmpty(treatments.ReceiptUrl))
            {
                var fileNo = $"receipt_{OrderNo}_{DateTime.Now:yyyyMMdd}";
                filePath = Path.Combine(_reportPath, $"{fileNo}.pdf" );

                await hub.Clients.Client(connectionId).SendAsync("ReportProgress", 40);

                // 製作收據 PDF
                ExportReceiptToPdf(receiptDic, patientDic, treatmentDic,ReceiptsTitle(0), filePath);

                await hub.Clients.Client(connectionId).SendAsync("ReportProgress", 60);

                // 壓縮 PDF
                var compressPath = Path.Combine(_reportPath, $"{fileNo}_Compressed.pdf");
                CompressPdf(filePath, compressPath);

                await hub.Clients.Client(connectionId).SendAsync("ReportProgress", 80);

                // 刪除未壓縮 PDF
                System.IO.File.Delete(filePath);

                treatments.ReceiptUrl = Path.GetFileName(compressPath);
                filePath = compressPath;
            }
            else
            {
                filePath = Path.Combine(_reportPath, treatments.ReceiptUrl);

                await hub.Clients.Client(connectionId).SendAsync("ReportProgress", 80);
            }

            _context.SaveChanges();

            await hub.Clients.Client(connectionId).SendAsync("ReportProgress", 100);
            await hub.Clients.Client(connectionId).SendAsync("ReportFinished", "報表已完成");

            var fileBytes = System.IO.File.ReadAllBytes(filePath);
            return File(fileBytes, "application/pdf", "patients_report.pdf");
        }

        public class ExportPdfRequest
        {
            public int[] TreatmentId { get; set; }
            public string[] OrderNo { get; set; }
            public int Titlecode { get; set; }
        }

        [Authorize(Roles = "Admin,Manager,User")]
        [HttpPost("ExportReceiptsLisrPdf")]
        public async Task<IActionResult> ExportReceiptsLisrPdf([FromBody] ExportPdfRequest request, string connectionId, [FromServices] IHubContext<ReportHub> hub)
        {
            Dictionary<string, List<Receipt>> receiptDic = new Dictionary<string, List<Receipt>>();
            Dictionary<string, Treatment> treatmentDic = new Dictionary<string, Treatment>();
            Dictionary<string, Patient> patientDic = new Dictionary<string, Patient>();

            if (!Directory.Exists(_reportPath))
                Directory.CreateDirectory(_reportPath);

            await hub.Clients.Client(connectionId).SendAsync("ReportProgress", 5);

            if (request.OrderNo.Length == 0)
                return BadRequest("未選擇收據編號，請重新再試");

            for(int i = 0; i < request.TreatmentId.Length; i++)
            {
                var receipts = _context.Receipts
               .Where(p => p.TreatmentId == request.TreatmentId[i] && p.OrderNo == request.OrderNo[i])
               .ToList();

                if (receipts.Count == 0)
                    return BadRequest("此案件收據無資料");

                receiptDic.Add(request.OrderNo[i], receipts);

                var treatments = _context.Treatments
                    .Where(p => p.Id == request.TreatmentId[i] && !p.IsDelete)
                    .FirstOrDefault();

                if (treatments == null)
                    return BadRequest("未找到案件資料");

                treatmentDic.Add(request.OrderNo[i], treatments);

                var patients = _context.Patients
                    .Where(p => p.Id == receipts.First().PatientId && !p.IsDelete)
                    .FirstOrDefault();

                if (patients == null)
                    return BadRequest("未找到病患資料");

                patientDic.Add(request.OrderNo[i], patients);
            }

            await hub.Clients.Client(connectionId).SendAsync("ReportProgress", 20);

            var filePath = string.Empty;

            var fileNo = $"receipt_ALL_{DateTime.Now:yyyyMMddhhmmss}";
            filePath = Path.Combine(_reportPath, $"{fileNo}.pdf");

            await hub.Clients.Client(connectionId).SendAsync("ReportProgress", 40);

            // 製作收據 PDF
            ExportReceiptToPdf(receiptDic, patientDic, treatmentDic, ReceiptsTitle(request.Titlecode), filePath);

            await hub.Clients.Client(connectionId).SendAsync("ReportProgress", 60);

            // 壓縮 PDF
            var compressPath = Path.Combine(_reportPath, $"{fileNo}_Compressed.pdf");
            CompressPdf(filePath, compressPath);

            await hub.Clients.Client(connectionId).SendAsync("ReportProgress", 80);

            // 刪除未壓縮 PDF
            System.IO.File.Delete(filePath);

            filePath = compressPath;

            _context.SaveChanges();

            await hub.Clients.Client(connectionId).SendAsync("ReportProgress", 100);
            await hub.Clients.Client(connectionId).SendAsync("ReportFinished", "報表已完成");

            var fileBytes = System.IO.File.ReadAllBytes(filePath);
            return File(fileBytes, "application/pdf", "patients_report.pdf");
        }

        /// <summary>
        /// 壓縮PDF
        /// </summary>
        /// <param name="inputPath"></param>
        /// <param name="outputPath"></param>
        private void CompressPdf(string inputPath, string outputPath)
        {
            var FileName = "gs";

            if (_env.IsDevelopment())
            {
                FileName = @"C:\Program Files\gs\gs10.05.1\bin\gswin64c.exe";
            }

            var psi = new ProcessStartInfo
            {
                FileName = FileName,
                Arguments = $"-sDEVICE=pdfwrite -dCompatibilityLevel=1.4 -dPDFSETTINGS=/ebook " +
                            "-dNOPAUSE -dQUIET -dBATCH " +
                            $"-sOutputFile=\"{outputPath}\" \"{inputPath}\"",
                RedirectStandardOutput = true,
                RedirectStandardError = true,
                UseShellExecute = false,
                CreateNoWindow = true
            };

            using var process = Process.Start(psi);
            process.WaitForExit();
        }

        /// <summary>
        /// 製作收據 PDF
        /// </summary>
        /// <param name="receipts"></param>
        /// <param name="filePath"></param>
        private void ExportReceiptToPdf(Dictionary<string, List<Receipt>> receipts, Dictionary<string, Patient> patients, Dictionary<string, Treatment> treatment, List<string> receiptsTitle, string filePath)
        {

            var FontFamily = "Noto Sans CJK TC";
            if (_env.IsDevelopment())
            {
                FontFamily = "Microsoft JhengHei";
            }

            TextStyle.Default.FontFamily(FontFamily);
            QuestPDF.Settings.CheckIfAllTextGlyphsAreAvailable = false;

            Document.Create(container =>
            {
                foreach(var item in receipts.Keys)
                {
                    container.Page(page =>
                    {
                        page.MarginTop(20);
                        page.MarginBottom(20);
                        page.MarginLeft(50);
                        page.MarginRight(50);
                        page.DefaultTextStyle(x => x.FontSize(12));

                        page.Content().Column(column =>
                        {
                            foreach (var title in receiptsTitle)
                            {
                                // 標題與編號
                                column.Item().Row(row =>
                                {
                                    row.ConstantItem(160).Text("");
                                    row.RelativeItem().Text("厝邊頭家物理治療所收據").FontFamily(FontFamily).Bold().FontSize(14);
                                    row.ConstantItem(140).Text($"編號: {receipts[item].First().OrderNo}").FontFamily(FontFamily);
                                });

                                column.Item().LineHorizontal(2).LineColor(Colors.Black);

                                // 基本資訊
                                column.Item().Row(row =>
                                {
                                    row.RelativeItem().Text($"姓名: {patients[item].FullName}").FontFamily(FontFamily);
                                    row.RelativeItem().Text($"生日: {patients[item].BirthDate?.ToString("yyyy/MM/dd") ?? ""}").FontFamily(FontFamily);
                                    row.RelativeItem().Text($"身分證字號: {patients[item].NationalId}").FontFamily(FontFamily);
                                });

                                column.Item().LineHorizontal(1).LineColor(Colors.Black);

                                column.Item().Row(row =>
                                {
                                    row.RelativeItem().Text("服務日期").FontFamily(FontFamily);
                                    row.RelativeItem().Text("項目").FontFamily(FontFamily);
                                    row.RelativeItem().Text("金額").FontFamily(FontFamily);
                                });

                                column.Item().LineHorizontal(1).LineColor(Colors.Black);

                                column.Item().Row(row =>
                                {
                                    // 左側：服務日期
                                    row.ConstantItem(160).Text(treatment[item].CreatedAt.ToString("yyyy/MM/dd")).FontFamily(FontFamily);

                                    // 右側：項目與金額
                                    row.RelativeItem().Table(table =>
                                    {
                                        table.ColumnsDefinition(columns =>
                                        {
                                            columns.RelativeColumn();
                                            columns.RelativeColumn();
                                        });

                                        foreach (var receipt in receipts[item])
                                        {
                                            table.Cell().Text(receipt.TreatmentItem).FontFamily(FontFamily);
                                            table.Cell().Text("$" + receipt.TreatmentMoney.ToString());
                                        }

                                    });
                                });

                                column.Item().LineHorizontal(2).LineColor(Colors.Black);

                                column.Item().Row(row =>
                                {
                                    // 左側：印章
                                    row.ConstantItem(80).PaddingTop(26).PaddingBottom(26).Text("治療所章：").FontFamily(FontFamily);
                                    //row.ConstantItem(200).PaddingTop(30).PaddingBottom(30).Text("（此處可放圖片章）").FontFamily(FontFamily);
                                    row.ConstantItem(10).Text("");
                                    row.ConstantItem(100).PaddingTop(2).PaddingBottom(2).Image("images/stamp.jpg", ImageScaling.FitArea);
                                    row.ConstantItem(80).Text("");

                                    // 右側：開立欄位
                                    row.RelativeItem().Table(table =>
                                    {
                                        table.ColumnsDefinition(columns =>
                                        {
                                            columns.ConstantColumn(70);
                                            columns.ConstantColumn(130);
                                        });

                                        table.Cell().Text("總額：").FontFamily(FontFamily);
                                        table.Cell().Text("$" + receipts[item].Sum(t => t.TreatmentMoney).ToString("g0"));

                                        table.Cell().PaddingTop(13).PaddingBottom(13).Text("開立日期：").FontFamily(FontFamily);
                                        table.Cell().PaddingTop(13).PaddingBottom(13).Text(receipts[item].First().UpdatedAt.ToString("yyyy/MM/dd"));

                                    });
                                });

                                column.Item().LineHorizontal(2).LineColor(Colors.Black);

                                // 收執聯名稱
                                column.Item().Row(row =>
                                {
                                    row.RelativeItem().AlignCenter().Text(title).FontFamily(FontFamily);
                                });

                                if (title != receiptsTitle.Last())
                                {
                                    column.Item().PaddingTop(10).PaddingBottom(10).LineHorizontal(1).LineColor(Colors.Grey.Medium);
                                }
                            }
                        });
                    });
                }
            })
        .GeneratePdf(filePath);
        }

        [Authorize(Roles = "Admin,Manager,User")]
        [HttpGet("GetLatestRecord/{patientId}")]
        public IActionResult GetLatestRecord(int patientId)
        {
            var latestReceipts = _context.Receipts
                .Where(r => r.PatientId == patientId && r.IsDelete == false)
                .OrderByDescending(r => r.CreatedAt)
                .Take(10) // 取最近的10筆，然後找到最新的一組
                .ToList();

            if (!latestReceipts.Any())
            {
                return BadRequest("無上一次收據紀錄");
            }

            // 找到最新的 OrderNo
            var latestOrderNo = latestReceipts.First().OrderNo;

            // 取得該 OrderNo 的所有收據記錄
            var latestRecordGroup = latestReceipts
                .Where(r => r.OrderNo == latestOrderNo)
                .Select(r => new
                {
                    treatmentItem = r.TreatmentItem,
                    treatmentMoney = r.TreatmentMoney,
                    treatmentId = r.TreatmentId,
                    patientId = r.PatientId
                })
                .ToList();

            return Ok(latestRecordGroup);
        }

        /// <summary>
        /// 發送收據郵件
        /// </summary>
        [Authorize(Roles = "Admin,Manager,User")]
        [HttpPost("SendReceiptEmail")]
        public async Task<IActionResult> SendReceiptEmail([FromBody] SendReceiptEmailRequest request)
        {
            try
            {
                // 驗證輸入參數
                if (string.IsNullOrWhiteSpace(request.Email))
                    return BadRequest("收件人郵箱不能為空");

                if (string.IsNullOrWhiteSpace(request.OrderNo))
                    return BadRequest("收據編號不能為空");

                // 查找收據資料
                var receipts = _context.Receipts
                    .Where(r => r.OrderNo == request.OrderNo && !r.IsDelete)
                    .ToList();

                if (!receipts.Any())
                    return BadRequest("找不到指定的收據");

                // 查找治療資料
                var treatment = _context.Treatments
                    .Where(t => t.Id == receipts.First().TreatmentId && !t.IsDelete)
                    .FirstOrDefault();

                if (treatment == null)
                    return BadRequest("找不到相關的治療資料");

                // 查找病患資料
                var patient = _context.Patients
                    .Where(p => p.Id == receipts.First().PatientId && !p.IsDelete)
                    .FirstOrDefault();

                if (patient == null)
                    return BadRequest("找不到病患資料");

                // 檢查是否有收據 PDF 檔案
                if (string.IsNullOrEmpty(treatment.ReceiptUrl))
                    return BadRequest("收據 PDF 尚未生成，請先生成收據");

                var pdfPath = Path.Combine(_reportPath, treatment.ReceiptUrl);
                if (!System.IO.File.Exists(pdfPath))
                    return BadRequest("收據 PDF 檔案不存在");

                // 發送郵件
                var success = await _gmailService.SendReceiptEmailAsync(
                    request.Email,
                    patient.FullName,
                    request.OrderNo,
                    pdfPath
                );

                if (success)
                {
                    // 記錄發送成功
                    // 可以在這裡添加郵件發送記錄到資料庫
                    return Ok(new { message = "收據郵件發送成功" });
                }
                else
                {
                    return StatusCode(500, new { error = "郵件發送失敗" });
                }
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { error = "發送郵件時發生錯誤", details = ex.Message });
            }
        }

        /// <summary>
        /// 發送收據郵件請求模型
        /// </summary>
        public class SendReceiptEmailRequest
        {
            public string Email { get; set; } = string.Empty;
            public string OrderNo { get; set; } = string.Empty;
        }

    }
}

{"ast": null, "code": "import { formatDistance } from \"./eo/_lib/formatDistance.js\";\nimport { formatLong } from \"./eo/_lib/formatLong.js\";\nimport { formatRelative } from \"./eo/_lib/formatRelative.js\";\nimport { localize } from \"./eo/_lib/localize.js\";\nimport { match } from \"./eo/_lib/match.js\";\n\n/**\n * @category Locales\n * @summary Esperanto locale.\n * @language Esperanto\n * @iso-639-2 epo\n * <AUTHOR>\n */\nexport const eo = {\n  code: \"eo\",\n  formatDistance: formatDistance,\n  formatLong: formatLong,\n  formatRelative: formatRelative,\n  localize: localize,\n  match: match,\n  options: {\n    weekStartsOn: 1 /* Monday */,\n    firstWeekContainsDate: 4\n  }\n};\n\n// Fallback for modularized imports:\nexport default eo;", "map": {"version": 3, "names": ["formatDistance", "formatLong", "formatRelative", "localize", "match", "eo", "code", "options", "weekStartsOn", "firstWeekContainsDate"], "sources": ["C:/Users/<USER>/Desktop/demo-git/demo-react/my-fullstack-app/frontend/node_modules/date-fns/locale/eo.js"], "sourcesContent": ["import { formatDistance } from \"./eo/_lib/formatDistance.js\";\nimport { formatLong } from \"./eo/_lib/formatLong.js\";\nimport { formatRelative } from \"./eo/_lib/formatRelative.js\";\nimport { localize } from \"./eo/_lib/localize.js\";\nimport { match } from \"./eo/_lib/match.js\";\n\n/**\n * @category Locales\n * @summary Esperanto locale.\n * @language Esperanto\n * @iso-639-2 epo\n * <AUTHOR>\n */\nexport const eo = {\n  code: \"eo\",\n  formatDistance: formatDistance,\n  formatLong: formatLong,\n  formatRelative: formatRelative,\n  localize: localize,\n  match: match,\n  options: {\n    weekStartsOn: 1 /* Monday */,\n    firstWeekContainsDate: 4,\n  },\n};\n\n// Fallback for modularized imports:\nexport default eo;\n"], "mappings": "AAAA,SAASA,cAAc,QAAQ,6BAA6B;AAC5D,SAASC,UAAU,QAAQ,yBAAyB;AACpD,SAASC,cAAc,QAAQ,6BAA6B;AAC5D,SAASC,QAAQ,QAAQ,uBAAuB;AAChD,SAASC,KAAK,QAAQ,oBAAoB;;AAE1C;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMC,EAAE,GAAG;EAChBC,IAAI,EAAE,IAAI;EACVN,cAAc,EAAEA,cAAc;EAC9BC,UAAU,EAAEA,UAAU;EACtBC,cAAc,EAAEA,cAAc;EAC9BC,QAAQ,EAAEA,QAAQ;EAClBC,KAAK,EAAEA,KAAK;EACZG,OAAO,EAAE;IACPC,YAAY,EAAE,CAAC,CAAC;IAChBC,qBAAqB,EAAE;EACzB;AACF,CAAC;;AAED;AACA,eAAeJ,EAAE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
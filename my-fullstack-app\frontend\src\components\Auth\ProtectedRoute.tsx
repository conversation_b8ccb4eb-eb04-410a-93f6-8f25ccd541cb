import React, { ReactNode } from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import { ROUTES } from '../../constants/routes';
import { useAuth } from '../../contexts/AuthContext';
import LoadingSpinner from '../Common/LoadingSpinner';

interface ProtectedRouteProps {
  children: ReactNode;
  requiredPermissions?: string[];
}

const ProtectedRoute: React.FC<ProtectedRouteProps> = ({
  children,
  requiredPermissions = [],
}) => {
  const { isAuthenticated, isLoading, user, token } = useAuth();
  const location = useLocation();

  console.log('ProtectedRoute: 檢查認證狀態', {
    isAuthenticated,
    isLoading,
    hasUser: !!user,
    hasToken: !!token,
    currentPath: location.pathname
  });

  if (isLoading) {
    console.log('ProtectedRoute: 載入中...');
    return <LoadingSpinner fullScreen message="檢查認證狀態..." />;
  }

  // 檢查 localStorage 中的 token 作為備用
  const localToken = localStorage.getItem('token');
  if (!isAuthenticated && !localToken) {
    console.log('ProtectedRoute: 未認證，重定向到登入頁面');
    // Redirect to login page with return url
    return (
      <Navigate
        to={ROUTES.LOGIN}
        state={{ from: location }}
        replace
      />
    );
  }

  // Check permissions if required
  if (requiredPermissions.length > 0) {
    // TODO: Implement permission checking logic
    // For now, just check if user exists
    if (!user) {
      return (
        <Navigate
          to={ROUTES.LOGIN}
          state={{ from: location }}
          replace
        />
      );
    }
  }

  return <>{children}</>;
};

export default ProtectedRoute;

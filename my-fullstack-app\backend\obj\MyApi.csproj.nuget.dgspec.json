{"format": 1, "restore": {"C:\\Users\\<USER>\\Desktop\\demo-git\\demo-react\\my-fullstack-app\\backend\\MyApi.csproj": {}}, "projects": {"C:\\Users\\<USER>\\Desktop\\demo-git\\demo-react\\my-fullstack-app\\backend\\MyApi.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Desktop\\demo-git\\demo-react\\my-fullstack-app\\backend\\MyApi.csproj", "projectName": "MyApi", "projectPath": "C:\\Users\\<USER>\\Desktop\\demo-git\\demo-react\\my-fullstack-app\\backend\\MyApi.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Desktop\\demo-git\\demo-react\\my-fullstack-app\\backend\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"BCrypt.Net-Next": {"target": "Package", "version": "[4.0.3, )"}, "Google.Apis.Auth": {"target": "Package", "version": "[1.70.0, )"}, "Google.Apis.Calendar.v3": {"target": "Package", "version": "[1.69.0.3746, )"}, "Google.Apis.Gmail.v1": {"target": "Package", "version": "[1.70.0.3819, )"}, "Google.Apis.Oauth2.v2": {"target": "Package", "version": "[1.68.0.1869, )"}, "HarfBuzzSharp.NativeAssets.Linux": {"target": "Package", "version": "[2.8.2, )"}, "Microsoft.AspNetCore.Authentication.JwtBearer": {"target": "Package", "version": "[8.0.17, )"}, "Microsoft.AspNetCore.ResponseCompression": {"target": "Package", "version": "[2.3.0, )"}, "Microsoft.AspNetCore.SignalR": {"target": "Package", "version": "[1.2.0, )"}, "Microsoft.AspNetCore.SignalR.Protocols.MessagePack": {"target": "Package", "version": "[9.0.7, )"}, "Microsoft.EntityFrameworkCore": {"target": "Package", "version": "[8.0.8, )"}, "Microsoft.EntityFrameworkCore.Tools": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[8.0.8, )"}, "MimeKit": {"target": "Package", "version": "[4.3.0, )"}, "Pomelo.EntityFrameworkCore.MySql": {"target": "Package", "version": "[8.0.2, )"}, "QuestPDF": {"target": "Package", "version": "[2022.12.15, )"}, "SkiaSharp.NativeAssets.Linux.NoDependencies": {"target": "Package", "version": "[2.88.3, )"}, "StackExchange.Redis": {"target": "Package", "version": "[2.8.37, )"}, "Swashbuckle.AspNetCore": {"target": "Package", "version": "[8.1.4, )"}, "System.IdentityModel.Tokens.Jwt": {"target": "Package", "version": "[8.12.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.AspNetCore.App": {"privateAssets": "none"}, "Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.100/PortableRuntimeIdentifierGraph.json"}}}}}
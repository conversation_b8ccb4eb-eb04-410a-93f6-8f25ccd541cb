{"ast": null, "code": "const formatRelativeLocale = {\n  lastWeek: date => {\n    const day = date.getDay();\n    switch (day) {\n      case 0:\n        return \"'prejšnjo nedeljo ob' p\";\n      case 3:\n        return \"'prejšnjo sredo ob' p\";\n      case 6:\n        return \"'prejš<PERSON>jo soboto ob' p\";\n      default:\n        return \"'prejšnji' EEEE 'ob' p\";\n    }\n  },\n  yesterday: \"'včeraj ob' p\",\n  today: \"'danes ob' p\",\n  tomorrow: \"'jutri ob' p\",\n  nextWeek: date => {\n    const day = date.getDay();\n    switch (day) {\n      case 0:\n        return \"'naslednjo nedeljo ob' p\";\n      case 3:\n        return \"'naslednjo sredo ob' p\";\n      case 6:\n        return \"'naslednjo soboto ob' p\";\n      default:\n        return \"'naslednji' EEEE 'ob' p\";\n    }\n  },\n  other: \"P\"\n};\nexport const formatRelative = (token, date, _baseDate, _options) => {\n  const format = formatRelativeLocale[token];\n  if (typeof format === \"function\") {\n    return format(date);\n  }\n  return format;\n};", "map": {"version": 3, "names": ["formatRelativeLocale", "lastWeek", "date", "day", "getDay", "yesterday", "today", "tomorrow", "nextWeek", "other", "formatRelative", "token", "_baseDate", "_options", "format"], "sources": ["C:/Users/<USER>/Desktop/demo-git/demo-react/my-fullstack-app/frontend/node_modules/date-fns/locale/sl/_lib/formatRelative.js"], "sourcesContent": ["const formatRelativeLocale = {\n  lastWeek: (date) => {\n    const day = date.getDay();\n\n    switch (day) {\n      case 0:\n        return \"'prejšnjo nedeljo ob' p\";\n      case 3:\n        return \"'prejšnjo sredo ob' p\";\n      case 6:\n        return \"'prejšnjo soboto ob' p\";\n      default:\n        return \"'prejšnji' EEEE 'ob' p\";\n    }\n  },\n  yesterday: \"'včeraj ob' p\",\n  today: \"'danes ob' p\",\n  tomorrow: \"'jutri ob' p\",\n  nextWeek: (date) => {\n    const day = date.getDay();\n\n    switch (day) {\n      case 0:\n        return \"'naslednjo nedeljo ob' p\";\n      case 3:\n        return \"'naslednjo sredo ob' p\";\n      case 6:\n        return \"'naslednjo soboto ob' p\";\n      default:\n        return \"'naslednji' EEEE 'ob' p\";\n    }\n  },\n  other: \"P\",\n};\n\nexport const formatRelative = (token, date, _baseDate, _options) => {\n  const format = formatRelativeLocale[token];\n\n  if (typeof format === \"function\") {\n    return format(date);\n  }\n\n  return format;\n};\n"], "mappings": "AAAA,MAAMA,oBAAoB,GAAG;EAC3BC,QAAQ,EAAGC,IAAI,IAAK;IAClB,MAAMC,GAAG,GAAGD,IAAI,CAACE,MAAM,CAAC,CAAC;IAEzB,QAAQD,GAAG;MACT,KAAK,CAAC;QACJ,OAAO,yBAAyB;MAClC,KAAK,CAAC;QACJ,OAAO,uBAAuB;MAChC,KAAK,CAAC;QACJ,OAAO,wBAAwB;MACjC;QACE,OAAO,wBAAwB;IACnC;EACF,CAAC;EACDE,SAAS,EAAE,eAAe;EAC1BC,KAAK,EAAE,cAAc;EACrBC,QAAQ,EAAE,cAAc;EACxBC,QAAQ,EAAGN,IAAI,IAAK;IAClB,MAAMC,GAAG,GAAGD,IAAI,CAACE,MAAM,CAAC,CAAC;IAEzB,QAAQD,GAAG;MACT,KAAK,CAAC;QACJ,OAAO,0BAA0B;MACnC,KAAK,CAAC;QACJ,OAAO,wBAAwB;MACjC,KAAK,CAAC;QACJ,OAAO,yBAAyB;MAClC;QACE,OAAO,yBAAyB;IACpC;EACF,CAAC;EACDM,KAAK,EAAE;AACT,CAAC;AAED,OAAO,MAAMC,cAAc,GAAGA,CAACC,KAAK,EAAET,IAAI,EAAEU,SAAS,EAAEC,QAAQ,KAAK;EAClE,MAAMC,MAAM,GAAGd,oBAAoB,CAACW,KAAK,CAAC;EAE1C,IAAI,OAAOG,MAAM,KAAK,UAAU,EAAE;IAChC,OAAOA,MAAM,CAACZ,IAAI,CAAC;EACrB;EAEA,OAAOY,MAAM;AACf,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
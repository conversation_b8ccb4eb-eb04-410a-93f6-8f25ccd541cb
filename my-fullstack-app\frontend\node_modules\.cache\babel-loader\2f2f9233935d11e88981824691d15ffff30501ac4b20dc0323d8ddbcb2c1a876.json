{"ast": null, "code": "import{useCallback,useEffect,useState}from\"react\";import api from'../services/api';// 定義 UserRole 中每筆物件型別\nexport default function useUser(RoleId){const[userRole,setuserRole]=useState([]);const[Roleloading,setRoleloading]=useState(true);const fetchUsers=useCallback(()=>{setRoleloading(true);api.get(\"/api/users/GetList\",{params:{name:\"\",roleId:RoleId}}).then(res=>setuserRole(res.data)).catch(err=>console.error(\"API Error:\",err)).finally(()=>setRoleloading(false));},[RoleId]);useEffect(()=>{fetchUsers();},[fetchUsers]);const refetch=useCallback(()=>{fetchUsers();},[fetchUsers]);return{userRole,Roleloading,refetch};}", "map": {"version": 3, "names": ["useCallback", "useEffect", "useState", "api", "useUser", "RoleId", "userRole", "setuserRole", "Roleloading", "setRoleloading", "fetchUsers", "get", "params", "name", "roleId", "then", "res", "data", "catch", "err", "console", "error", "finally", "refetch"], "sources": ["C:/Users/<USER>/Desktop/demo-git/demo-react/my-fullstack-app/frontend/src/hooks/useUser.ts"], "sourcesContent": ["import { useCallback, useEffect, useState } from \"react\";\r\nimport api from '../services/api';\r\n\r\n// 定義 UserRole 中每筆物件型別\r\ninterface UserRole {\r\n  userId: number;\r\n  userName: string;\r\n  userAccount: string;\r\n  userEmail: string;\r\n  userPhone: string;\r\n  isEnabled: boolean;\r\n  roleId: number;\r\n  roleName: string;\r\n  createdAt: string;\r\n  updatedAt: string;\r\n}\r\n\r\nexport default function useUser(\r\n    RoleId: number\r\n) {\r\n  const [userRole, setuserRole] = useState<UserRole[]>([]);\r\n  const [Roleloading, setRoleloading] = useState(true);\r\n\r\n  const fetchUsers = useCallback(() => {\r\n    setRoleloading(true);\r\n    api.get<UserRole[]>(\"/api/users/GetList\", {\r\n        params: {\r\n          name: \"\",\r\n          roleId: RoleId,\r\n        },\r\n      })\r\n      .then(res => setuserRole(res.data))\r\n      .catch(err => console.error(\"API Error:\", err))\r\n      .finally(() => setRoleloading(false));\r\n  }, [RoleId]);\r\n\r\n  useEffect(() => {\r\n    fetchUsers();\r\n  }, [fetchUsers]);\r\n\r\n  const refetch = useCallback(() => {\r\n    fetchUsers();\r\n  }, [fetchUsers]);\r\n\r\n  return { userRole, Roleloading, refetch };\r\n}"], "mappings": "AAAA,OAASA,WAAW,CAAEC,SAAS,CAAEC,QAAQ,KAAQ,OAAO,CACxD,MAAO,CAAAC,GAAG,KAAM,iBAAiB,CAEjC;AAcA,cAAe,SAAS,CAAAC,OAAOA,CAC3BC,MAAc,CAChB,CACA,KAAM,CAACC,QAAQ,CAAEC,WAAW,CAAC,CAAGL,QAAQ,CAAa,EAAE,CAAC,CACxD,KAAM,CAACM,WAAW,CAAEC,cAAc,CAAC,CAAGP,QAAQ,CAAC,IAAI,CAAC,CAEpD,KAAM,CAAAQ,UAAU,CAAGV,WAAW,CAAC,IAAM,CACnCS,cAAc,CAAC,IAAI,CAAC,CACpBN,GAAG,CAACQ,GAAG,CAAa,oBAAoB,CAAE,CACtCC,MAAM,CAAE,CACNC,IAAI,CAAE,EAAE,CACRC,MAAM,CAAET,MACV,CACF,CAAC,CAAC,CACDU,IAAI,CAACC,GAAG,EAAIT,WAAW,CAACS,GAAG,CAACC,IAAI,CAAC,CAAC,CAClCC,KAAK,CAACC,GAAG,EAAIC,OAAO,CAACC,KAAK,CAAC,YAAY,CAAEF,GAAG,CAAC,CAAC,CAC9CG,OAAO,CAAC,IAAMb,cAAc,CAAC,KAAK,CAAC,CAAC,CACzC,CAAC,CAAE,CAACJ,MAAM,CAAC,CAAC,CAEZJ,SAAS,CAAC,IAAM,CACdS,UAAU,CAAC,CAAC,CACd,CAAC,CAAE,CAACA,UAAU,CAAC,CAAC,CAEhB,KAAM,CAAAa,OAAO,CAAGvB,WAAW,CAAC,IAAM,CAChCU,UAAU,CAAC,CAAC,CACd,CAAC,CAAE,CAACA,UAAU,CAAC,CAAC,CAEhB,MAAO,CAAEJ,QAAQ,CAAEE,WAAW,CAAEe,OAAQ,CAAC,CAC3C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
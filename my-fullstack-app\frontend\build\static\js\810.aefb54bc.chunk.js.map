{"version": 3, "file": "static/js/810.aefb54bc.chunk.js", "mappings": "yUA2DA,MAirBA,EAjrBgCA,KAC9B,MAAMC,GAAQC,EAAAA,EAAAA,QAAc,MACtBC,GAAWC,EAAAA,EAAAA,OAEVC,EAAQC,IAAaC,EAAAA,EAAAA,UAA0B,KAE/CC,EAAYC,IAAiBF,EAAAA,EAAAA,WAAS,IACtCG,EAAYC,IAAiBJ,EAAAA,EAAAA,UAAqB,QAClDK,EAAeC,IAAoBN,EAAAA,EAAAA,UAA+B,OAClEO,EAASC,IAAcR,EAAAA,EAAAA,UAAmB,KAC1CS,EAAUC,IAAeV,EAAAA,EAAAA,UAAoB,KAC7CW,EAAWC,IAAgBZ,EAAAA,EAAAA,WAAS,IACpCa,EAAUC,IAAed,EAAAA,EAAAA,UAAwB,CACtDe,SAAU,KACVC,UAAW,KACXC,UAAW,KACXC,QAAS,KACTC,UAAW,QACXC,QAAS,QACTC,UAAU,EACVC,WAAY,QACZC,YAAa,KAIfC,EAAAA,EAAAA,YAAU,KACgBC,WACtB,IAAK,IAADC,EACFC,QAAQC,IAAI,2CAGZ,MAAOC,EAAiBC,EAAkBC,SAA2BC,QAAQC,IAAI,CAC/EC,EAAAA,EAAIC,IAAI,yBACRD,EAAAA,EAAIC,IAAI,6BACRD,EAAAA,EAAIC,IAAI,mBAGVR,QAAQC,IAAI,kCAAUC,EAAgBO,MACtCT,QAAQC,IAAI,4BAASE,EAAiBM,MACtCT,QAAQC,IAAI,4BAASG,EAAkBK,MAEvC,MAAMC,EAAcR,EAAgBO,KAC9BE,EAAeR,EAAiBM,KAChCG,EAAgBR,EAAkBK,KAExC5B,EAAW6B,GACX3B,EAAY4B,GAGZ,MAAME,EAAkBD,EAAcE,KAAKC,IAAa,CACtDC,GAAID,EAASC,GACbC,MAAOF,EAASE,MAChBC,MAAOH,EAASG,MAChBC,IAAKJ,EAASI,IACdC,gBAAiBL,EAASK,iBAAmB,UAC7CC,YAAaN,EAASM,YACtBjC,SAAU2B,EAAS3B,SACnBC,UAAW0B,EAAS1B,UACpBiC,WAAYP,EAASO,WACrBC,YAAaR,EAASQ,gBAGxBvB,QAAQC,IAAI,8CAAYY,GACxBzC,EAAUyC,GAEG,QAAbd,EAAAhC,EAAMyD,eAAO,IAAAzB,GAAbA,EAAe0B,KAAK,CAClBC,SAAU,UACVC,QAAS,eACTC,OAAQ,wCAEZ,CAAE,MAAOC,GAAQ,IAADC,EACd9B,QAAQ6B,MAAM,wCAAWA,GACZ,QAAbC,EAAA/D,EAAMyD,eAAO,IAAAM,GAAbA,EAAeL,KAAK,CAClBC,SAAU,QACVC,QAAS,eACTC,OAAQ,wCAEZ,GAGFG,KACC,IAGH,MAAMC,EAAkBlC,UACtB,IAAK,IAADmC,EACFhD,GAAa,GACb,MAAMiD,QAAiB3B,EAAAA,EAAIC,IAAI,iBAGzBK,EAFgBqB,EAASzB,KAEOK,KAAKC,IAAa,CACtDC,GAAID,EAASC,GACbC,MAAOF,EAASE,MAChBC,MAAOH,EAASG,MAChBC,IAAKJ,EAASI,IACdC,gBAAiBL,EAASK,iBAAmB,UAC7CC,YAAaN,EAASM,YACtBjC,SAAU2B,EAAS3B,SACnBC,UAAW0B,EAAS1B,UACpB8C,YAAapB,EAASoB,YACtBb,WAAYP,EAASO,WACrBC,YAAaR,EAASQ,YACtBa,YAAarB,EAASqB,gBAGxBhE,EAAUyC,GAEG,QAAboB,EAAAlE,EAAMyD,eAAO,IAAAS,GAAbA,EAAeR,KAAK,CAClBC,SAAU,UACVC,QAAS,eACTC,OAAQ,0DAEZ,CAAE,MAAOC,GAAQ,IAADQ,EACdrC,QAAQ6B,MAAM,oDAAaA,GACd,QAAbQ,EAAAtE,EAAMyD,eAAO,IAAAa,GAAbA,EAAeZ,KAAK,CAClBC,SAAU,QACVC,QAAS,eACTC,OAAQ,oDAEZ,CAAC,QACC3C,GAAa,EACf,GAKIqD,EAAoBA,CAACC,EAA4BC,KACrDrD,GAAYsD,IAAIC,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACXD,GAAI,IACP,CAACF,GAAQC,OAIPG,EAAiB7C,UACuE,IAAD8C,EAA3F,GAAK1D,EAASE,UAAaF,EAASG,WAAcH,EAASI,WAAcJ,EAASK,QASlF,IACE,MAAMsD,EAAgB,IAAIC,KAAK5D,EAASI,WAClCyD,EAAc,IAAID,KAAK5D,EAASK,UAG/ByD,EAAWC,GAAe/D,EAASM,UAAU0D,MAAM,MACnDC,EAASC,GAAalE,EAASO,QAAQyD,MAAM,KAEpDL,EAAcQ,SAASC,SAASN,GAAsBM,SAASL,IAC/DF,EAAYM,SAASC,SAASH,GAAoBG,SAASF,IAG3D,MAAMG,EAAsBC,IAC1B,OAAQA,GACN,IAAK,QAAS,OAAO,EACrB,IAAK,SAAU,OAAO,EACtB,IAAK,UAAW,OAAO,EACvB,QAAS,OAAO,IAIdC,EAAe,CACnBrE,SAAUF,EAASE,SACnBC,UAAWH,EAASG,UACpBwD,cAAeA,EAAca,cAC7BX,YAAaA,EAAYW,cACzBtB,YAAa,GACbhB,gBAAiB,UACjBzB,WAAYT,EAASQ,SAAW6D,EAAmBrE,EAASS,YAAc,EAC1EC,YAAaV,EAASQ,SAAWR,EAASU,YAAc,GAG1D,GAAmB,SAAfpB,GAAyBE,GAAiBA,EAAcsC,GAAI,CAAC,IAAD2C,EAE9D,MAAMC,GAAUlB,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACXe,GAAY,IACfzC,GAAIsC,SAAS5E,EAAcsC,YAGvBT,EAAAA,EAAIsD,IAAI,iBAADC,OAAkBpF,EAAcsC,IAAM4C,SAG7C5B,IAEO,QAAb2B,EAAA5F,EAAMyD,eAAO,IAAAmC,GAAbA,EAAelC,KAAK,CAClBC,SAAU,UACVC,QAAS,eACTC,OAAQ,kCAEZ,KAAO,CAAC,IAADmC,QAECxD,EAAAA,EAAIyD,KAAK,gBAAiBP,SAG1BzB,IAEO,QAAb+B,EAAAhG,EAAMyD,eAAO,IAAAuC,GAAbA,EAAetC,KAAK,CAClBC,SAAU,UACVC,QAAS,eACTC,OAAQ,kCAEZ,CAEArD,GAAc,GACd0F,GACF,CAAE,MAAOpC,GAAa,IAADqC,EAAAC,EAAAC,EACnBpE,QAAQ6B,MAAM,4BAASA,GACV,QAAbqC,EAAAnG,EAAMyD,eAAO,IAAA0C,GAAbA,EAAezC,KAAK,CAClBC,SAAU,QACVC,QAAS,eACTC,QAAsB,QAAduC,EAAAtC,EAAMK,gBAAQ,IAAAiC,GAAM,QAANC,EAAdD,EAAgB1D,YAAI,IAAA2D,OAAN,EAAdA,EAAsBC,UAAW,4BAE7C,MAhFe,QAAbzB,EAAA7E,EAAMyD,eAAO,IAAAoB,GAAbA,EAAenB,KAAK,CAClBC,SAAU,OACVC,QAAS,eACTC,OAAQ,4DAgFRqC,EAAYA,KAChB9E,EAAY,CACVC,SAAU,KACVC,UAAW,KACXC,UAAW,KACXC,QAAS,KACTC,UAAW,QACXC,QAAS,QACTC,UAAU,EACVC,WAAY,QACZC,YAAa,IAEfjB,EAAiB,MACjBF,EAAc,QAsIhB,OACE6F,EAAAA,EAAAA,MAAA,OAAKC,UAAU,iBAAgBC,SAAA,EAC7BC,EAAAA,EAAAA,KAACC,EAAAA,EAAK,CAACC,IAAK5G,KACZ0G,EAAAA,EAAAA,KAACG,EAAAA,EAAa,KAGdH,EAAAA,EAAAA,KAAA,OAAKF,UAAU,OAAOM,QAAM,EAAAL,UAC1BC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,mDAAkDC,UAC/DF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,aAAYC,SAAA,EACzBC,EAAAA,EAAAA,KAACK,EAAAA,EAAM,CACLC,MAAM,2BACNC,KAAMhG,EAAY,wBAA0B,gBAC5CiG,QAASjD,EACTuC,UAAU,qBACVW,UAAQ,EACRC,SAAUnG,KAEZyF,EAAAA,EAAAA,KAACK,EAAAA,EAAM,CACLC,MAAM,2BACNC,KAAK,aACLC,QAASA,IAAM1G,GAAc,GAC7BgG,UAAU,6BAOlBE,EAAAA,EAAAA,KAAA,OAAKF,UAAU,OAAMC,UACnBC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,qBAAoBC,UACjCC,EAAAA,EAAAA,KAACW,EAAAA,EAAY,CACXC,QAAS,CAACC,EAAAA,EAAeC,EAAAA,EAAgBC,EAAAA,IACzCC,OAAO,OACPC,YAAY,eACZC,OAAO,QACPC,UAAU,EACVC,YAAY,EACZC,cAAe,CACbC,KAAM,kBACNC,OAAQ,QACRC,MAAO,gDAETC,YAAa,CACTC,KAAM,UACNC,MAAO,QAEXC,gBAAiB,CACfC,IAAK,UACLC,QAAS,UAEXC,gBAAiB,CACfC,KAAM,UACNC,OAAQ,UACRC,QAAQ,GAEVC,cAAe,CACbC,WAAY,CACVC,KAAM,eACNC,MAAO/E,GAETgF,QAAS,CACPF,KAAM,eACNC,MAAOA,IAAMxI,GAAc,KAG/B0I,YAAY,WACZC,YAAY,WACZ/I,OAAQA,EACRgJ,WAvMgBC,IACxB,MAAMC,EAAYlJ,EAAOmJ,MAAKC,GAASA,EAAMvG,KAAOoG,EAAUG,MAAMvG,KAChEqG,IACF1I,EAAiB0I,GACjB5I,EAAc,QAGdU,EAAY,CACVC,SAAUiI,EAAUjI,UAAY,KAChCC,UAAWgI,EAAUhI,WAAa,KAClCC,UAAW,IAAIwD,KAAKuE,EAAUnG,OAC9B3B,QAAS,IAAIuD,KAAKuE,EAAUlG,KAC5B3B,WAAWgI,EAAAA,EAAAA,GAAkBH,EAAUnG,MAAO,SAC9CzB,SAAS+H,EAAAA,EAAAA,GAAkBH,EAAUlG,IAAK,SAC1CzB,UAAU,EACVC,WAAY,QACZC,YAAa,IAGfrB,GAAc,KAqLRkJ,YAAY,EACZC,cAAc,EACdC,cAAc,EACdC,cAAe,CACbC,WAAY,CAAC,EAAG,EAAG,EAAG,EAAG,EAAG,GAC5BrI,UAAW,QACXC,QAAS,gBAOjB6E,EAAAA,EAAAA,MAACwD,EAAAA,EAAM,CACLC,OACiB,QAAfvJ,EAAuB,2BACR,SAAfA,EAAwB,2BAAS,2BAEnCwJ,QAAS1J,EACT2J,MAAO,CAAEC,MAAO,SAChBC,OAAQA,KACN5J,GAAc,GACd0F,KAEFmE,QACE3D,EAAAA,EAAAA,KAAA,OAAKF,UAAU,aAAYC,SACT,SAAfhG,GACC8F,EAAAA,EAAAA,MAAA+D,EAAAA,SAAA,CAAA7D,SAAA,EACEC,EAAAA,EAAAA,KAACK,EAAAA,EAAM,CACLC,MAAM,eACNC,KAAK,oBACLT,UAAU,mBACVU,QAzKOnF,UACrB,GAAKpB,GAAkBA,EAAcW,UAErC,IACE,IAAI8C,EAAczD,EAAcyD,YAEhC,IAAKA,EAAa,CAEhBnC,QAAQC,IAAI,oDAOZkC,SAN6B5B,EAAAA,EAAIyD,KAAK,wBAAyB,CAC7DsE,UAAW5J,EAAcW,UACzBkJ,SAAU7J,EAAcU,YAIGqB,KAAK0B,YAClCnC,QAAQC,IAAI,yDAAa,CAAEkC,sBAGrB5B,EAAAA,EAAIiI,MAAM,iBAAD1E,OAAkBpF,EAAcsC,GAAE,cAAc,CAC7DyH,YAAatG,IAEfnC,QAAQC,IAAI,iDAGZ+B,GACF,CAGAhC,QAAQC,IAAI,uCAAU,CAAEkC,gBAExB,MAMMuG,SAN0BnI,EAAAA,EAAIC,IAAI,iBAAkB,CACxDmI,OAAQ,CACNC,aAAczG,MAIsB1B,KAGpC0B,GACFlE,EAAS,GAAD6F,OAAI+E,EAAAA,GAAOC,iBAAgB,QAAAhF,OAAO3B,GAAe,CACvD4G,MAAO,CAAEC,UAAWN,EAAeO,QAAS,CAAEjI,GAAI0H,EAAcrJ,aAItE,CAAE,MAAOwC,GAAQ,IAADqH,EACdlJ,QAAQ6B,MAAM,2BAAQA,GACT,QAAbqH,EAAAnL,EAAMyD,eAAO,IAAA0H,GAAbA,EAAezH,KAAK,CAClBC,SAAU,QACVC,QAAS,2BACTC,OAAQ,uFACRuH,KAAM,KAEV,MAqHY1E,EAAAA,EAAAA,KAACK,EAAAA,EAAM,CACLC,MAAM,eACNC,KAAK,eACLC,QAtNGmE,KACjB3K,EAAc,YAuNFgG,EAAAA,EAAAA,KAACK,EAAAA,EAAM,CACLC,MAAM,eACNC,KAAK,cACLT,UAAU,kBACVU,QAxNKoE,KACd3K,IAEL4K,EAAAA,EAAAA,GAAc,CACZjF,QAAQ,+CAADP,OAAcpF,EAAcuC,MAAK,kBACxC8G,OAAQ,2BACR/C,KAAM,6BACNuE,OAAQzJ,UACN,IAAK,IAAD0J,QAEIjJ,EAAAA,EAAIkJ,OAAO,iBAAD3F,OAAkBpF,EAAcsC,WAG1CgB,IACNzD,GAAc,GACd0F,IAEa,QAAbuF,EAAAzL,EAAMyD,eAAO,IAAAgI,GAAbA,EAAe/H,KAAK,CAClBC,SAAU,UACVC,QAAS,eACTC,OAAQ,kCAEZ,CAAE,MAAOC,GAAa,IAAD6H,EAAAC,EAAAC,EACnB5J,QAAQ6B,MAAM,4BAASA,GACvB,IAAID,EAA2B,MAAjBC,EAAMgI,OAAiB,sEAA8B,QAAdH,EAAA7H,EAAMK,gBAAQ,IAAAwH,GAAM,QAANC,EAAdD,EAAgBjJ,YAAI,IAAAkJ,OAAN,EAAdA,EAAsBtF,UAAW,2BAEzE,QAAbuF,EAAA7L,EAAMyD,eAAO,IAAAoI,GAAbA,EAAenI,KAAK,CAClBC,SAAU,QACVC,QAAS,eACTC,OAAQA,GAEZ,SA2LQ6C,EAAAA,EAAAA,KAACK,EAAAA,EAAM,CACLC,MAAM,eACNC,KAAK,cACLT,UAAU,oBACVU,QAASA,KACP1G,GAAc,GACd0F,WAKNK,EAAAA,EAAAA,MAAA+D,EAAAA,SAAA,CAAA7D,SAAA,EACEC,EAAAA,EAAAA,KAACK,EAAAA,EAAM,CACLC,MAAM,eACNC,KAAK,cACLT,UAAU,oBACVU,QAASA,KACP1G,GAAc,GACd0F,QAGJQ,EAAAA,EAAAA,KAACK,EAAAA,EAAM,CACLC,MAAsB,SAAfvG,EAAwB,eAAO,eACtCwG,KAAK,cACLC,QAAwB,SAAfzG,EArJFsL,MACrBR,EAAAA,EAAAA,GAAc,CACZjF,QAAS,yDACT0D,OAAQ,2BACR/C,KAAM,wBACNuE,OAAQA,KACN5G,QA+I4DA,SAK3D6B,SAAA,EAEDF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,OAAMC,SAAA,EACnBC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,iBAAgBC,UAC7BF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,QAAOC,SAAA,EACpBC,EAAAA,EAAAA,KAAA,SAAOsF,QAAQ,SAASxF,UAAU,uBAAsBC,SAAC,0BAGzDC,EAAAA,EAAAA,KAACuF,EAAAA,EAAQ,CACPhJ,GAAG,SACHwB,MAAOtD,EAASE,SAChB6K,QAASrL,EACTsL,SAAWC,GAAM7H,EAAkB,WAAY6H,EAAE3H,OACjD4H,YAAY,OACZC,YAAY,KACZC,YAAY,uCACZ/F,UAAU,SACVY,SAAyB,SAAf3G,UAKhBiG,EAAAA,EAAAA,KAAA,OAAKF,UAAU,iBAAgBC,UAC7BF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,QAAOC,SAAA,EACpBC,EAAAA,EAAAA,KAAA,SAAOsF,QAAQ,UAAUxF,UAAU,uBAAsBC,SAAC,oBAG1DC,EAAAA,EAAAA,KAACuF,EAAAA,EAAQ,CACPhJ,GAAG,UACHwB,MAAOtD,EAASG,UAChB4K,QAASnL,EACToL,SAAWC,GAAM7H,EAAkB,YAAa6H,EAAE3H,OAClD4H,YAAY,OACZC,YAAY,KACZC,YAAY,iCACZ/F,UAAU,SACVY,SAAyB,SAAf3G,UAKhBiG,EAAAA,EAAAA,KAAA,OAAKF,UAAU,kBAAiBC,UAC9BF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,QAAOC,SAAA,EACpBC,EAAAA,EAAAA,KAAA,SAAOsF,QAAQ,YAAYxF,UAAU,uBAAsBC,SAAC,gCAG5DC,EAAAA,EAAAA,KAAC8F,EAAAA,EAAQ,CACPvJ,GAAG,YACHwB,MAAOtD,EAASI,UAChB4K,SAAWC,GAAM7H,EAAkB,YAAa6H,EAAE3H,OAClDgI,WAAW,WACXC,UAAQ,EACRlG,UAAU,SACVY,SAAyB,SAAf3G,UAKhBiG,EAAAA,EAAAA,KAAA,OAAKF,UAAU,kBAAiBC,UAC9BF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,QAAOC,SAAA,EACpBC,EAAAA,EAAAA,KAAA,SAAOsF,QAAQ,UAAUxF,UAAU,uBAAsBC,SAAC,gCAG1DC,EAAAA,EAAAA,KAAC8F,EAAAA,EAAQ,CACPvJ,GAAG,UACHwB,MAAOtD,EAASK,QAChB2K,SAAWC,GAAM7H,EAAkB,UAAW6H,EAAE3H,OAChDgI,WAAW,WACXC,UAAQ,EACRlG,UAAU,SACVY,SAAyB,SAAf3G,UAKhBiG,EAAAA,EAAAA,KAAA,OAAKF,UAAU,iBAAgBC,UAC7BF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,QAAOC,SAAA,EACpBC,EAAAA,EAAAA,KAAA,SAAOsF,QAAQ,YAAYxF,UAAU,uBAAsBC,SAAC,8BAG5DC,EAAAA,EAAAA,KAACiG,EAAAA,EAAS,CACR1J,GAAG,YACHwC,KAAK,OACLhB,MAAOtD,EAASM,UAChB0K,SAAWC,GAAM7H,EAAkB,YAAa6H,EAAEQ,OAAOnI,OACzD+B,UAAU,SACVY,SAAyB,SAAf3G,UAKhBiG,EAAAA,EAAAA,KAAA,OAAKF,UAAU,iBAAgBC,UAC7BF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,QAAOC,SAAA,EACpBC,EAAAA,EAAAA,KAAA,SAAOsF,QAAQ,UAAUxF,UAAU,uBAAsBC,SAAC,8BAG1DC,EAAAA,EAAAA,KAACiG,EAAAA,EAAS,CACR1J,GAAG,UACHwC,KAAK,OACLhB,MAAOtD,EAASO,QAChByK,SAAWC,GAAM7H,EAAkB,UAAW6H,EAAEQ,OAAOnI,OACvD+B,UAAU,SACVY,SAAyB,SAAf3G,YAOF,SAAfA,IACC8F,EAAAA,EAAAA,MAAA,OAAKC,UAAU,OAAMC,SAAA,EACnBC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,SAAQC,UACrBF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,iBAAgBC,SAAA,EAC7BC,EAAAA,EAAAA,KAACmG,EAAAA,EAAQ,CACPC,QAAQ,WACRC,QAAS5L,EAASQ,SAClBwK,SAAWC,GAAM7H,EAAkB,WAAY6H,EAAEW,UAAW,MAE9DrG,EAAAA,EAAAA,KAAA,SAAOsF,QAAQ,WAAWxF,UAAU,iBAAgBC,SAAC,kCAMxDtF,EAASQ,WACR4E,EAAAA,EAAAA,MAAA+D,EAAAA,SAAA,CAAA7D,SAAA,EACEC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,iBAAgBC,UAC7BF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,QAAOC,SAAA,EACpBC,EAAAA,EAAAA,KAAA,SAAOsF,QAAQ,aAAaxF,UAAU,uBAAsBC,SAAC,8BAG7DC,EAAAA,EAAAA,KAACuF,EAAAA,EAAQ,CACPhJ,GAAG,aACHwB,MAAOtD,EAASS,WAChBsK,QAAS,CACP,CAAElF,MAAO,eAAMvC,MAAO,SACtB,CAAEuC,MAAO,eAAMvC,MAAO,UACtB,CAAEuC,MAAO,eAAMvC,MAAO,YAExB0H,SAAWC,GAAM7H,EAAkB,aAAc6H,EAAE3H,OACnD+B,UAAU,SACV0D,MAAO,CAAEC,MAAO,eAKtBzD,EAAAA,EAAAA,KAAA,OAAKF,UAAU,iBAAgBC,UAC7BF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,QAAOC,SAAA,EACpBC,EAAAA,EAAAA,KAAA,SAAOsF,QAAQ,cAAcxF,UAAU,uBAAsBC,SAAC,8BAG9DC,EAAAA,EAAAA,KAACsG,EAAAA,EAAW,CACV/J,GAAG,cACHwB,MAAOtD,EAASU,YAChBoL,cAAgBb,GAAM7H,EAAkB,cAAe6H,EAAE3H,OAAS,GAClEyI,IAAK,EACLC,IAAK,GACLC,WAAY,CAAEjD,MAAO,eAK3BzD,EAAAA,EAAAA,KAAA,OAAKF,UAAU,SAAQC,UACrBC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,8BAA6BC,UAC1CF,EAAAA,EAAAA,MAAA,KAAGC,UAAU,4BAA2BC,SAAA,EACtCC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,2BACY,UAAxBrF,EAASS,YAAsB,kCAAAmE,OAAa5E,EAASU,YAAW,WACxC,WAAxBV,EAASS,YAAuB,kCAAAmE,OAAa5E,EAASU,YAAW,WACzC,YAAxBV,EAASS,YAAwB,kCAAAmE,OAAa5E,EAASU,YAAW,8B", "sources": ["components/Page/SchedulesPage.tsx"], "sourcesContent": ["import { formatUtcToTaipei } from '../../utils/dateUtils';\r\nimport dayGridPlugin from '@fullcalendar/daygrid';\r\nimport interactionPlugin from '@fullcalendar/interaction';\r\nimport FullCalendar from '@fullcalendar/react';\r\nimport timeGridPlugin from '@fullcalendar/timegrid';\r\nimport { Button } from 'primereact/button';\r\nimport { Calendar } from 'primereact/calendar';\r\nimport { ConfirmDialog, confirmDialog } from 'primereact/confirmdialog';\r\nimport { Dialog } from 'primereact/dialog';\r\nimport { Dropdown } from 'primereact/dropdown';\r\nimport { InputText } from 'primereact/inputtext';\r\nimport { Toast } from 'primereact/toast';\r\nimport { Checkbox } from 'primereact/checkbox';\r\nimport { InputNumber } from 'primereact/inputnumber';\r\nimport React, { useEffect, useRef, useState } from 'react';\r\nimport { useNavigate } from 'react-router-dom';\r\nimport { ROUTES } from '../../constants/routes';\r\nimport api from '../../services/api';\r\n\r\ninterface CalendarEvent {\r\n  id?: string;\r\n  title: string;\r\n  start: string;\r\n  end: string;\r\n  backgroundColor?: string;\r\n  borderColor?: string;\r\n  doctorId?: number;\r\n  patientId?: number;\r\n  treatmentId?: number;\r\n  doctorName?: string;\r\n  patientName?: string;\r\n  description?: string;\r\n}\r\n\r\ninterface EventFormData {\r\n  doctorId: number | null;\r\n  patientId: number | null;\r\n  startDate: Date | null;\r\n  endDate: Date | null;\r\n  startTime: string;\r\n  endTime: string;\r\n  // 重複設定\r\n  isRepeat: boolean;\r\n  repeatType: 'daily' | 'weekly' | 'monthly';\r\n  repeatCount: number;\r\n}\r\n\r\ninterface Doctor {\r\n  Id: number;\r\n  Name: string;\r\n}\r\n\r\ninterface Patient {\r\n  Id: number;\r\n  Name: string;\r\n}\r\n\r\ntype DialogMode = 'add' | 'edit' | 'view';\r\n\r\nconst SchedulesPage: React.FC = () => {\r\n  const toast = useRef<Toast>(null);\r\n  const navigate = useNavigate();\r\n\r\n  const [events, setEvents] = useState<CalendarEvent[]>([]);\r\n\r\n  const [showDialog, setShowDialog] = useState(false);\r\n  const [dialogMode, setDialogMode] = useState<DialogMode>('add');\r\n  const [selectedEvent, setSelectedEvent] = useState<CalendarEvent | null>(null);\r\n  const [doctors, setDoctors] = useState<Doctor[]>([]);\r\n  const [patients, setPatients] = useState<Patient[]>([]);\r\n  const [isLoading, setIsLoading] = useState(false);\r\n  const [formData, setFormData] = useState<EventFormData>({\r\n    doctorId: null,\r\n    patientId: null,\r\n    startDate: null,\r\n    endDate: null,\r\n    startTime: '09:00',\r\n    endTime: '10:00',\r\n    isRepeat: false,\r\n    repeatType: 'daily',\r\n    repeatCount: 1,\r\n  });\r\n\r\n  // 載入所有數據\r\n  useEffect(() => {\r\n    const loadInitialData = async () => {\r\n      try {\r\n        console.log('開始載入數據...');\r\n\r\n        // 並行載入治療師、病患和行程數據\r\n        const [doctorsResponse, patientsResponse, schedulesResponse] = await Promise.all([\r\n          api.get('/api/users/DoctorList'),\r\n          api.get('/api/patients/PatientList'),\r\n          api.get('/api/schedule')\r\n        ]);\r\n\r\n        console.log('治療師數據:', doctorsResponse.data);\r\n        console.log('病患數據:', patientsResponse.data);\r\n        console.log('行程數據:', schedulesResponse.data);\r\n\r\n        const doctorsData = doctorsResponse.data;\r\n        const patientsData = patientsResponse.data;\r\n        const schedulesData = schedulesResponse.data;\r\n\r\n        setDoctors(doctorsData);\r\n        setPatients(patientsData);\r\n\r\n        // 轉換後端數據格式為前端 CalendarEvent 格式\r\n        const formattedEvents = schedulesData.map((schedule: any) => ({\r\n          id: schedule.id,\r\n          title: schedule.title,\r\n          start: schedule.start,\r\n          end: schedule.end,\r\n          backgroundColor: schedule.backgroundColor || '#3788d8',\r\n          borderColor: schedule.borderColor,\r\n          doctorId: schedule.doctorId,\r\n          patientId: schedule.patientId,\r\n          doctorName: schedule.doctorName,\r\n          patientName: schedule.patientName,\r\n        }));\r\n\r\n        console.log('格式化後的事件:', formattedEvents);\r\n        setEvents(formattedEvents);\r\n\r\n        toast.current?.show({\r\n          severity: 'success',\r\n          summary: '成功',\r\n          detail: '數據載入完成',\r\n        });\r\n      } catch (error) {\r\n        console.error('載入數據失敗:', error);\r\n        toast.current?.show({\r\n          severity: 'error',\r\n          summary: '錯誤',\r\n          detail: '載入數據失敗',\r\n        });\r\n      }\r\n    };\r\n\r\n    loadInitialData();\r\n  }, []);\r\n\r\n  // 重新載入行程數據\r\n  const reloadSchedules = async () => {\r\n    try {\r\n      setIsLoading(true);\r\n      const response = await api.get('/api/schedule');\r\n      const schedulesData = response.data;\r\n\r\n      const formattedEvents = schedulesData.map((schedule: any) => ({\r\n        id: schedule.id,\r\n        title: schedule.title,\r\n        start: schedule.start,\r\n        end: schedule.end,\r\n        backgroundColor: schedule.backgroundColor || '#3788d8',\r\n        borderColor: schedule.borderColor,\r\n        doctorId: schedule.doctorId,\r\n        patientId: schedule.patientId,\r\n        treatmentId: schedule.treatmentId,\r\n        doctorName: schedule.doctorName,\r\n        patientName: schedule.patientName,\r\n        description: schedule.description,\r\n      }));\r\n\r\n      setEvents(formattedEvents);\r\n\r\n      toast.current?.show({\r\n        severity: 'success',\r\n        summary: '成功',\r\n        detail: '行程數據已重新載入',\r\n      });\r\n    } catch (error) {\r\n      console.error('重新載入行程失敗:', error);\r\n      toast.current?.show({\r\n        severity: 'error',\r\n        summary: '錯誤',\r\n        detail: '重新載入行程失敗',\r\n      });\r\n    } finally {\r\n      setIsLoading(false);\r\n    }\r\n  };\r\n\r\n  \r\n\r\n  const handleInputChange = (field: keyof EventFormData, value: string | Date | null | number | boolean) => {\r\n    setFormData(prev => ({\r\n      ...prev,\r\n      [field]: value,\r\n    }));\r\n  };\r\n\r\n  const handleAddEvent = async () => {\r\n    if (!formData.doctorId || !formData.patientId || !formData.startDate || !formData.endDate) {\r\n      toast.current?.show({\r\n        severity: 'warn',\r\n        summary: '警告',\r\n        detail: '請填寫所有必要欄位',\r\n      });\r\n      return;\r\n    }\r\n\r\n    try {\r\n      const startDateTime = new Date(formData.startDate);\r\n      const endDateTime = new Date(formData.endDate);\r\n\r\n      // 設置時間\r\n      const [startHour, startMinute] = formData.startTime.split(':');\r\n      const [endHour, endMinute] = formData.endTime.split(':');\r\n\r\n      startDateTime.setHours(parseInt(startHour as string), parseInt(startMinute as string));\r\n      endDateTime.setHours(parseInt(endHour as string), parseInt(endMinute as string));\r\n\r\n      // 轉換重複類型\r\n      const getRepeatTypeValue = (type: string) => {\r\n        switch (type) {\r\n          case 'daily': return 1;\r\n          case 'weekly': return 2;\r\n          case 'monthly': return 3;\r\n          default: return 0;\r\n        }\r\n      };\r\n\r\n      const scheduleData = {\r\n        doctorId: formData.doctorId,\r\n        patientId: formData.patientId,\r\n        startDateTime: startDateTime.toISOString(),\r\n        endDateTime: endDateTime.toISOString(),\r\n        description: '', // 可以後續添加描述欄位\r\n        backgroundColor: '#3788d8',\r\n        repeatType: formData.isRepeat ? getRepeatTypeValue(formData.repeatType) : 0,\r\n        repeatCount: formData.isRepeat ? formData.repeatCount : 1,\r\n      };\r\n\r\n      if (dialogMode === 'edit' && selectedEvent && selectedEvent.id) {\r\n        // 編輯模式 - 調用 PUT API\r\n        const updateData = {\r\n          ...scheduleData,\r\n          id: parseInt(selectedEvent.id),\r\n        };\r\n\r\n        await api.put(`/api/schedule/${selectedEvent.id}`, updateData);\r\n\r\n        // 重新載入所有行程數據以確保同步\r\n        await reloadSchedules();\r\n\r\n        toast.current?.show({\r\n          severity: 'success',\r\n          summary: '成功',\r\n          detail: '行程已更新',\r\n        });\r\n      } else {\r\n        // 新增模式 - 調用 POST API\r\n        await api.post('/api/schedule', scheduleData);\r\n\r\n        // 重新載入所有行程數據以確保同步\r\n        await reloadSchedules();\r\n\r\n        toast.current?.show({\r\n          severity: 'success',\r\n          summary: '成功',\r\n          detail: '行程已新增',\r\n        });\r\n      }\r\n\r\n      setShowDialog(false);\r\n      resetForm();\r\n    } catch (error: any) {\r\n      console.error('操作失敗:', error);\r\n      toast.current?.show({\r\n        severity: 'error',\r\n        summary: '錯誤',\r\n        detail: error.response?.data?.message || '操作失敗',\r\n      });\r\n    }\r\n  };\r\n\r\n  const resetForm = () => {\r\n    setFormData({\r\n      doctorId: null,\r\n      patientId: null,\r\n      startDate: null,\r\n      endDate: null,\r\n      startTime: '09:00',\r\n      endTime: '10:00',\r\n      isRepeat: false,\r\n      repeatType: 'daily',\r\n      repeatCount: 1,\r\n    });\r\n    setSelectedEvent(null);\r\n    setDialogMode('add');\r\n  };\r\n\r\n  const handleEventClick = (clickInfo: any) => {\r\n    const eventData = events.find(event => event.id === clickInfo.event.id);\r\n    if (eventData) {\r\n      setSelectedEvent(eventData);\r\n      setDialogMode('view');\r\n\r\n      // 填充表單數據\r\n      setFormData({\r\n        doctorId: eventData.doctorId || null,\r\n        patientId: eventData.patientId || null,\r\n        startDate: new Date(eventData.start),\r\n        endDate: new Date(eventData.end),\r\n        startTime: formatUtcToTaipei(eventData.start, 'HH:mm'),\r\n        endTime: formatUtcToTaipei(eventData.end, 'HH:mm'),\r\n        isRepeat: false,\r\n        repeatType: 'daily',\r\n        repeatCount: 1,\r\n      });\r\n\r\n      setShowDialog(true);\r\n    }\r\n  };\r\n\r\n  const handleEdit = () => {\r\n    setDialogMode('edit');\r\n  };\r\n\r\n  const handleDelete = () => {\r\n    if (!selectedEvent) return;\r\n\r\n    confirmDialog({\r\n      message: `確定要刪除行程 \"${selectedEvent.title}\" 嗎？`,\r\n      header: '確認刪除',\r\n      icon: 'pi pi-exclamation-triangle',\r\n      accept: async () => {\r\n        try {\r\n          // 調用後端 DELETE API\r\n          await api.delete(`/api/schedule/${selectedEvent.id}`);\r\n\r\n          // 重新載入所有行程數據以確保同步\r\n          await reloadSchedules();\r\n          setShowDialog(false);\r\n          resetForm();\r\n\r\n          toast.current?.show({\r\n            severity: 'success',\r\n            summary: '成功',\r\n            detail: '行程已刪除',\r\n          });\r\n        } catch (error: any) {\r\n          console.error('刪除失敗:', error);\r\n          var detail =  error.status === 403 ? \"您無權限，請通知管理員\" : error.response?.data?.message || '刪除失敗';\r\n          \r\n          toast.current?.show({\r\n            severity: 'error',\r\n            summary: '錯誤',\r\n            detail: detail,\r\n          });\r\n        }\r\n      },\r\n    });\r\n  };\r\n\r\n  const handleOpenCase = async () => {\r\n    if (!selectedEvent || !selectedEvent.patientId) return;\r\n\r\n    try {\r\n      let treatmentId = selectedEvent.treatmentId;\r\n\r\n      if (!treatmentId) {\r\n        // 如果沒有 TreatmentId，創建新的治療案件\r\n        console.log('創建新的治療案件');\r\n        const createResponse = await api.post('/api/treatment/Insert', {\r\n          PatientId: selectedEvent.patientId,\r\n          DoctorId: selectedEvent.doctorId,\r\n          // 其他必要的初始數據\r\n        });\r\n\r\n        treatmentId = createResponse.data.treatmentId;\r\n        console.log('新治療案件創建成功', { treatmentId });\r\n\r\n        // 更新 Schedule 的 TreatmentId\r\n        await api.patch(`/api/schedule/${selectedEvent.id}/treatment`, {\r\n          TreatmentId: treatmentId\r\n        });\r\n        console.log('Schedule TreatmentId 更新成功');\r\n\r\n        // 重新載入行程數據\r\n        reloadSchedules();\r\n      }\r\n\r\n      // 獲取完整的治療數據\r\n      console.log('獲取治療數據', { treatmentId });\r\n      \r\n      const treatmentResponse = await api.get('/api/treatment', {\r\n        params: {\r\n          treatmentsId: treatmentId\r\n        }\r\n      });\r\n\r\n      const treatmentData = treatmentResponse.data;\r\n\r\n      // 跳轉到 TreatmentsDetailPage\r\n      if (treatmentId) {\r\n        navigate(`${ROUTES.TREATMENT_DETAIL}?id=${treatmentId}`, {\r\n          state: { treatment: treatmentData, patient: { id: treatmentData.patientId }}\r\n        });\r\n      }\r\n\r\n    } catch (error) {\r\n      console.error('開案失敗', error);\r\n      toast.current?.show({\r\n        severity: 'error',\r\n        summary: '開案失敗',\r\n        detail: '無法開啟治療案件，請稍後再試',\r\n        life: 3000\r\n      });\r\n    }\r\n  };\r\n\r\n  const handleSaveEdit = () => {\r\n    confirmDialog({\r\n      message: '確定要保存修改嗎？',\r\n      header: '確認編輯',\r\n      icon: 'pi pi-question-circle',\r\n      accept: () => {\r\n        handleAddEvent(); // 重用新增邏輯，但會根據 dialogMode 判斷是編輯還是新增\r\n      },\r\n    });\r\n  };\r\n\r\n  return (\r\n    <div className=\"schedules-page\">\r\n      <Toast ref={toast} />\r\n      <ConfirmDialog />\r\n\r\n      {/* 操作按鈕 */}\r\n      <div className=\"card\" hidden>\r\n        <div className=\"flex pb-3 justify-content-end align-items-center\">\r\n          <div className=\"flex gap-2\">\r\n            <Button\r\n              label=\"重新載入\"\r\n              icon={isLoading ? \"pi pi-spin pi-spinner\" : \"pi pi-refresh\"}\r\n              onClick={reloadSchedules}\r\n              className=\"p-button-secondary\"\r\n              outlined\r\n              disabled={isLoading}\r\n            />\r\n            <Button\r\n              label=\"新增行程\"\r\n              icon=\"pi pi-plus\"\r\n              onClick={() => setShowDialog(true)}\r\n              className=\"p-button-primary\"\r\n            />\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      {/* 日曆 */}\r\n      <div className=\"card\">\r\n        <div className=\"calendar-container\">\r\n          <FullCalendar\r\n            plugins={[dayGridPlugin, timeGridPlugin, interactionPlugin]}\r\n            height=\"auto\"\r\n            initialView=\"timeGridWeek\"\r\n            locale=\"zh-tw\"\r\n            weekends={true}\r\n            allDaySlot={false}\r\n            headerToolbar={{\r\n              left: 'prev,next today',\r\n              center: 'title',\r\n              right: 'reloadDate,addDate dayGridMonth,timeGridWeek',\r\n            }}\r\n            titleFormat={{\r\n                year: 'numeric', // 顯示年份 (例如: 2023)\r\n                month: 'long'    // 顯示完整月份名稱 (例如: 十二月)\r\n            }}\r\n            dayHeaderFormat={{\r\n              day: 'numeric', // 顯示日期數字 (例如: 13)\r\n              weekday: 'narrow' // 顯示短格式的星期 (例如: 日, 一, 二)\r\n            }}\r\n            slotLabelFormat={{\r\n              hour: '2-digit', // 小時顯示為兩位數 (例如: 09)\r\n              minute: 'numeric', // 分鐘顯示為兩位數 (例如: 00)\r\n              hour12: false // 禁用 12 小時制，啟用 24 小時制\r\n            }}\r\n            customButtons={{\r\n              reloadDate: {\r\n                text: '重整',\r\n                click: reloadSchedules,\r\n              },\r\n              addDate: {\r\n                text: '新增',\r\n                click: () => setShowDialog(true),\r\n              },\r\n            }}\r\n            slotMinTime=\"09:00:00\"\r\n            slotMaxTime=\"22:00:00\"\r\n            events={events}\r\n            eventClick={handleEventClick}\r\n            selectable={true}\r\n            selectMirror={true}\r\n            dayMaxEvents={true}\r\n            businessHours={{\r\n              daysOfWeek: [1, 2, 3, 4, 5, 6],\r\n              startTime: '10:00',\r\n              endTime: '18:00',\r\n            }}\r\n          />\r\n        </div>\r\n      </div>\r\n\r\n      {/* 行程對話框 */}\r\n      <Dialog\r\n        header={\r\n          dialogMode === 'add' ? '新增行程' :\r\n          dialogMode === 'edit' ? '編輯行程' : '行程詳情'\r\n        }\r\n        visible={showDialog}\r\n        style={{ width: '500px' }}\r\n        onHide={() => {\r\n          setShowDialog(false);\r\n          resetForm();\r\n        }}\r\n        footer={\r\n          <div className=\"flex gap-2\">\r\n            {dialogMode === 'view' ? (\r\n              <>\r\n                <Button\r\n                  label=\"開案\"\r\n                  icon=\"pi pi-folder-open\"\r\n                  className=\"p-button-success\"\r\n                  onClick={handleOpenCase}\r\n                />\r\n                <Button\r\n                  label=\"編輯\"\r\n                  icon=\"pi pi-pencil\"\r\n                  onClick={handleEdit}\r\n                />\r\n                <Button\r\n                  label=\"刪除\"\r\n                  icon=\"pi pi-trash\"\r\n                  className=\"p-button-danger\"\r\n                  onClick={handleDelete}\r\n                />\r\n                <Button\r\n                  label=\"關閉\"\r\n                  icon=\"pi pi-times\"\r\n                  className=\"p-button-outlined\"\r\n                  onClick={() => {\r\n                    setShowDialog(false);\r\n                    resetForm();\r\n                  }}\r\n                />\r\n              </>\r\n            ) : (\r\n              <>\r\n                <Button\r\n                  label=\"取消\"\r\n                  icon=\"pi pi-times\"\r\n                  className=\"p-button-outlined\"\r\n                  onClick={() => {\r\n                    setShowDialog(false);\r\n                    resetForm();\r\n                  }}\r\n                />\r\n                <Button\r\n                  label={dialogMode === 'edit' ? '保存' : '新增'}\r\n                  icon=\"pi pi-check\"\r\n                  onClick={dialogMode === 'edit' ? handleSaveEdit : handleAddEvent}\r\n                />\r\n              </>\r\n            )}\r\n          </div>\r\n        }\r\n      >\r\n        <div className=\"grid\">\r\n          <div className=\"col-6 md:col-6\">\r\n            <div className=\"field\">\r\n              <label htmlFor=\"doctor\" className=\"font-bold block mb-2\">\r\n                治療師 *\r\n              </label>\r\n              <Dropdown\r\n                id=\"doctor\"\r\n                value={formData.doctorId}\r\n                options={doctors}\r\n                onChange={(e) => handleInputChange('doctorId', e.value)}\r\n                optionLabel=\"Name\"\r\n                optionValue=\"Id\"\r\n                placeholder=\"請選擇治療師\"\r\n                className=\"w-full\"\r\n                disabled={dialogMode === 'view'}\r\n              />\r\n            </div>\r\n          </div>\r\n\r\n          <div className=\"col-6 md:col-6\">\r\n            <div className=\"field\">\r\n              <label htmlFor=\"patient\" className=\"font-bold block mb-2\">\r\n                病患 *\r\n              </label>\r\n              <Dropdown\r\n                id=\"patient\"\r\n                value={formData.patientId}\r\n                options={patients}\r\n                onChange={(e) => handleInputChange('patientId', e.value)}\r\n                optionLabel=\"Name\"\r\n                optionValue=\"Id\"\r\n                placeholder=\"請選擇病患\"\r\n                className=\"w-full\"\r\n                disabled={dialogMode === 'view'}\r\n              />\r\n            </div>\r\n          </div>\r\n\r\n          <div className=\"col-12 md:col-6\">\r\n            <div className=\"field\">\r\n              <label htmlFor=\"startDate\" className=\"font-bold block mb-2\">\r\n                開始日期 *\r\n              </label>\r\n              <Calendar\r\n                id=\"startDate\"\r\n                value={formData.startDate}\r\n                onChange={(e) => handleInputChange('startDate', e.value as Date)}\r\n                dateFormat=\"yy-mm-dd\"\r\n                showIcon\r\n                className=\"w-full\"\r\n                disabled={dialogMode === 'view'}\r\n              />\r\n            </div>\r\n          </div>\r\n\r\n          <div className=\"col-12 md:col-6\">\r\n            <div className=\"field\">\r\n              <label htmlFor=\"endDate\" className=\"font-bold block mb-2\">\r\n                結束日期 *\r\n              </label>\r\n              <Calendar\r\n                id=\"endDate\"\r\n                value={formData.endDate}\r\n                onChange={(e) => handleInputChange('endDate', e.value as Date)}\r\n                dateFormat=\"yy-mm-dd\"\r\n                showIcon\r\n                className=\"w-full\"\r\n                disabled={dialogMode === 'view'}\r\n              />\r\n            </div>\r\n          </div>\r\n\r\n          <div className=\"col-6 md:col-6\">\r\n            <div className=\"field\">\r\n              <label htmlFor=\"startTime\" className=\"font-bold block mb-2\">\r\n                開始時間\r\n              </label>\r\n              <InputText\r\n                id=\"startTime\"\r\n                type=\"time\"\r\n                value={formData.startTime}\r\n                onChange={(e) => handleInputChange('startTime', e.target.value)}\r\n                className=\"w-full\"\r\n                disabled={dialogMode === 'view'}\r\n              />\r\n            </div>\r\n          </div>\r\n\r\n          <div className=\"col-6 md:col-6\">\r\n            <div className=\"field\">\r\n              <label htmlFor=\"endTime\" className=\"font-bold block mb-2\">\r\n                結束時間\r\n              </label>\r\n              <InputText\r\n                id=\"endTime\"\r\n                type=\"time\"\r\n                value={formData.endTime}\r\n                onChange={(e) => handleInputChange('endTime', e.target.value)}\r\n                className=\"w-full\"\r\n                disabled={dialogMode === 'view'}\r\n              />\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        {/* 重複設定 */}\r\n        {dialogMode !== 'view' && (\r\n          <div className=\"grid\">\r\n            <div className=\"col-12\">\r\n              <div className=\"field-checkbox\">\r\n                <Checkbox\r\n                  inputId=\"isRepeat\"\r\n                  checked={formData.isRepeat}\r\n                  onChange={(e) => handleInputChange('isRepeat', e.checked || false)}\r\n                />\r\n                <label htmlFor=\"isRepeat\" className=\"ml-2 font-bold\">\r\n                  重複行程\r\n                </label>\r\n              </div>\r\n            </div>\r\n\r\n            {formData.isRepeat && (\r\n              <>\r\n                <div className=\"col-6 md:col-6\">\r\n                  <div className=\"field\">\r\n                    <label htmlFor=\"repeatType\" className=\"font-bold block mb-2\">\r\n                      重複類型\r\n                    </label>\r\n                    <Dropdown\r\n                      id=\"repeatType\"\r\n                      value={formData.repeatType}\r\n                      options={[\r\n                        { label: '每日', value: 'daily' },\r\n                        { label: '每週', value: 'weekly' },\r\n                        { label: '每月', value: 'monthly' }\r\n                      ]}\r\n                      onChange={(e) => handleInputChange('repeatType', e.value)}\r\n                      className=\"w-full\"\r\n                      style={{ width: '90%' }}\r\n                    />\r\n                  </div>\r\n                </div>\r\n\r\n                <div className=\"col-6 md:col-6\">\r\n                  <div className=\"field\">\r\n                    <label htmlFor=\"repeatCount\" className=\"font-bold block mb-2\">\r\n                      重複次數\r\n                    </label>\r\n                    <InputNumber\r\n                      id=\"repeatCount\"\r\n                      value={formData.repeatCount}\r\n                      onValueChange={(e) => handleInputChange('repeatCount', e.value || 1)}\r\n                      min={1}\r\n                      max={10}\r\n                      inputStyle={{ width: '90%' }}\r\n                    />\r\n                  </div>\r\n                </div>\r\n\r\n                <div className=\"col-12\">\r\n                  <div className=\"p-1 bg-blue-50 border-round\">\r\n                    <p className=\"text-sm text-blue-800 m-0\">\r\n                      <i className=\"pi pi-info-circle mr-2\"></i>\r\n                      {formData.repeatType === 'daily' && `將每日重複 ${formData.repeatCount} 次`}\r\n                      {formData.repeatType === 'weekly' && `將每週重複 ${formData.repeatCount} 次`}\r\n                      {formData.repeatType === 'monthly' && `將每月重複 ${formData.repeatCount} 次`}\r\n                    </p>\r\n                  </div>\r\n                </div>\r\n              </>\r\n            )}\r\n          </div>\r\n        )}\r\n      </Dialog>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default SchedulesPage;\r\n\r\n"], "names": ["SchedulesPage", "toast", "useRef", "navigate", "useNavigate", "events", "setEvents", "useState", "showDialog", "setShowDialog", "dialogMode", "setDialogMode", "selectedEvent", "setSelectedEvent", "doctors", "setDoctors", "patients", "setPatients", "isLoading", "setIsLoading", "formData", "setFormData", "doctorId", "patientId", "startDate", "endDate", "startTime", "endTime", "isRepeat", "repeatType", "repeatCount", "useEffect", "async", "_toast$current", "console", "log", "doctorsResponse", "patientsResponse", "schedulesResponse", "Promise", "all", "api", "get", "data", "doctorsData", "patientsData", "schedulesData", "formattedEvents", "map", "schedule", "id", "title", "start", "end", "backgroundColor", "borderColor", "<PERSON><PERSON><PERSON>", "patientName", "current", "show", "severity", "summary", "detail", "error", "_toast$current2", "loadInitialData", "reloadSchedules", "_toast$current3", "response", "treatmentId", "description", "_toast$current4", "handleInputChange", "field", "value", "prev", "_objectSpread", "handleAddEvent", "_toast$current5", "startDateTime", "Date", "endDateTime", "startHour", "startMinute", "split", "endHour", "endMinute", "setHours", "parseInt", "getRepeatTypeValue", "type", "scheduleData", "toISOString", "_toast$current6", "updateData", "put", "concat", "_toast$current7", "post", "resetForm", "_toast$current8", "_error$response", "_error$response$data", "message", "_jsxs", "className", "children", "_jsx", "Toast", "ref", "ConfirmDialog", "hidden", "<PERSON><PERSON>", "label", "icon", "onClick", "outlined", "disabled", "FullCalendar", "plugins", "dayGridPlugin", "timeGridPlugin", "interactionPlugin", "height", "initialView", "locale", "weekends", "allDaySlot", "headerToolbar", "left", "center", "right", "titleFormat", "year", "month", "dayHeaderFormat", "day", "weekday", "slotLabelFormat", "hour", "minute", "hour12", "customButtons", "reloadDate", "text", "click", "addDate", "slotMinTime", "slotMaxTime", "eventClick", "clickInfo", "eventData", "find", "event", "formatUtcToTaipei", "selectable", "selectMirror", "dayMaxEvents", "businessHours", "daysOfWeek", "Dialog", "header", "visible", "style", "width", "onHide", "footer", "_Fragment", "PatientId", "DoctorId", "patch", "TreatmentId", "treatmentData", "params", "treatmentsId", "ROUTES", "TREATMENT_DETAIL", "state", "treatment", "patient", "_toast$current1", "life", "handleEdit", "handleDelete", "confirmDialog", "accept", "_toast$current9", "delete", "_error$response2", "_error$response2$data", "_toast$current0", "status", "handleSaveEdit", "htmlFor", "Dropdown", "options", "onChange", "e", "optionLabel", "optionValue", "placeholder", "Calendar", "dateFormat", "showIcon", "InputText", "target", "Checkbox", "inputId", "checked", "InputNumber", "onValueChange", "min", "max", "inputStyle"], "sourceRoot": ""}
{"ast": null, "code": "const formatDistanceLocale = {\n  lessThanXSeconds: {\n    one: \"mindre enn eitt sekund\",\n    other: \"mindre enn {{count}} sekund\"\n  },\n  xSeconds: {\n    one: \"eitt sekund\",\n    other: \"{{count}} sekund\"\n  },\n  halfAMinute: \"eit halvt minutt\",\n  lessThanXMinutes: {\n    one: \"mindre enn eitt minutt\",\n    other: \"mindre enn {{count}} minutt\"\n  },\n  xMinutes: {\n    one: \"eitt minutt\",\n    other: \"{{count}} minutt\"\n  },\n  aboutXHours: {\n    one: \"omtrent ein time\",\n    other: \"omtrent {{count}} timar\"\n  },\n  xHours: {\n    one: \"ein time\",\n    other: \"{{count}} timar\"\n  },\n  xDays: {\n    one: \"ein dag\",\n    other: \"{{count}} dagar\"\n  },\n  aboutXWeeks: {\n    one: \"omtrent ei veke\",\n    other: \"omtrent {{count}} veker\"\n  },\n  xWeeks: {\n    one: \"ei veke\",\n    other: \"{{count}} veker\"\n  },\n  aboutXMonths: {\n    one: \"omtrent ein månad\",\n    other: \"omtrent {{count}} månader\"\n  },\n  xMonths: {\n    one: \"ein månad\",\n    other: \"{{count}} månader\"\n  },\n  aboutXYears: {\n    one: \"omtrent eitt år\",\n    other: \"omtrent {{count}} år\"\n  },\n  xYears: {\n    one: \"eitt år\",\n    other: \"{{count}} år\"\n  },\n  overXYears: {\n    one: \"over eitt år\",\n    other: \"over {{count}} år\"\n  },\n  almostXYears: {\n    one: \"nesten eitt år\",\n    other: \"nesten {{count}} år\"\n  }\n};\nconst wordMapping = [\"null\", \"ein\", \"to\", \"tre\", \"fire\", \"fem\", \"seks\", \"sju\", \"åtte\", \"ni\", \"ti\", \"elleve\", \"tolv\"];\nexport const formatDistance = (token, count, options) => {\n  let result;\n  const tokenValue = formatDistanceLocale[token];\n  if (typeof tokenValue === \"string\") {\n    result = tokenValue;\n  } else if (count === 1) {\n    result = tokenValue.one;\n  } else {\n    result = tokenValue.other.replace(\"{{count}}\", count < 13 ? wordMapping[count] : String(count));\n  }\n  if (options !== null && options !== void 0 && options.addSuffix) {\n    if (options.comparison && options.comparison > 0) {\n      return \"om \" + result;\n    } else {\n      return result + \" sidan\";\n    }\n  }\n  return result;\n};", "map": {"version": 3, "names": ["formatDistanceLocale", "lessThanXSeconds", "one", "other", "xSeconds", "halfAMinute", "lessThanXMinutes", "xMinutes", "aboutXHours", "xHours", "xDays", "aboutXWeeks", "xWeeks", "aboutXMonths", "xMonths", "aboutXYears", "xYears", "overXYears", "almostXYears", "wordMapping", "formatDistance", "token", "count", "options", "result", "tokenValue", "replace", "String", "addSuffix", "comparison"], "sources": ["C:/Users/<USER>/Desktop/demo-git/demo-react/my-fullstack-app/frontend/node_modules/date-fns/locale/nn/_lib/formatDistance.js"], "sourcesContent": ["const formatDistanceLocale = {\n  lessThanXSeconds: {\n    one: \"mindre enn eitt sekund\",\n    other: \"mindre enn {{count}} sekund\",\n  },\n\n  xSeconds: {\n    one: \"eitt sekund\",\n    other: \"{{count}} sekund\",\n  },\n\n  halfAMinute: \"eit halvt minutt\",\n\n  lessThanXMinutes: {\n    one: \"mindre enn eitt minutt\",\n    other: \"mindre enn {{count}} minutt\",\n  },\n\n  xMinutes: {\n    one: \"eitt minutt\",\n    other: \"{{count}} minutt\",\n  },\n\n  aboutXHours: {\n    one: \"omtrent ein time\",\n    other: \"omtrent {{count}} timar\",\n  },\n\n  xHours: {\n    one: \"ein time\",\n    other: \"{{count}} timar\",\n  },\n\n  xDays: {\n    one: \"ein dag\",\n    other: \"{{count}} dagar\",\n  },\n\n  aboutXWeeks: {\n    one: \"omtrent ei veke\",\n    other: \"omtrent {{count}} veker\",\n  },\n\n  xWeeks: {\n    one: \"ei veke\",\n    other: \"{{count}} veker\",\n  },\n\n  aboutXMonths: {\n    one: \"omtrent ein månad\",\n    other: \"omtrent {{count}} månader\",\n  },\n\n  xMonths: {\n    one: \"ein månad\",\n    other: \"{{count}} månader\",\n  },\n\n  aboutXYears: {\n    one: \"omtrent eitt år\",\n    other: \"omtrent {{count}} år\",\n  },\n\n  xYears: {\n    one: \"eitt år\",\n    other: \"{{count}} år\",\n  },\n\n  overXYears: {\n    one: \"over eitt år\",\n    other: \"over {{count}} år\",\n  },\n\n  almostXYears: {\n    one: \"nesten eitt år\",\n    other: \"nesten {{count}} år\",\n  },\n};\n\nconst wordMapping = [\n  \"null\",\n  \"ein\",\n  \"to\",\n  \"tre\",\n  \"fire\",\n  \"fem\",\n  \"seks\",\n  \"sju\",\n  \"åtte\",\n  \"ni\",\n  \"ti\",\n  \"elleve\",\n  \"tolv\",\n];\n\nexport const formatDistance = (token, count, options) => {\n  let result;\n\n  const tokenValue = formatDistanceLocale[token];\n  if (typeof tokenValue === \"string\") {\n    result = tokenValue;\n  } else if (count === 1) {\n    result = tokenValue.one;\n  } else {\n    result = tokenValue.other.replace(\n      \"{{count}}\",\n      count < 13 ? wordMapping[count] : String(count),\n    );\n  }\n\n  if (options?.addSuffix) {\n    if (options.comparison && options.comparison > 0) {\n      return \"om \" + result;\n    } else {\n      return result + \" sidan\";\n    }\n  }\n\n  return result;\n};\n"], "mappings": "AAAA,MAAMA,oBAAoB,GAAG;EAC3BC,gBAAgB,EAAE;IAChBC,GAAG,EAAE,wBAAwB;IAC7BC,KAAK,EAAE;EACT,CAAC;EAEDC,QAAQ,EAAE;IACRF,GAAG,EAAE,aAAa;IAClBC,KAAK,EAAE;EACT,CAAC;EAEDE,WAAW,EAAE,kBAAkB;EAE/BC,gBAAgB,EAAE;IAChBJ,GAAG,EAAE,wBAAwB;IAC7BC,KAAK,EAAE;EACT,CAAC;EAEDI,QAAQ,EAAE;IACRL,GAAG,EAAE,aAAa;IAClBC,KAAK,EAAE;EACT,CAAC;EAEDK,WAAW,EAAE;IACXN,GAAG,EAAE,kBAAkB;IACvBC,KAAK,EAAE;EACT,CAAC;EAEDM,MAAM,EAAE;IACNP,GAAG,EAAE,UAAU;IACfC,KAAK,EAAE;EACT,CAAC;EAEDO,KAAK,EAAE;IACLR,GAAG,EAAE,SAAS;IACdC,KAAK,EAAE;EACT,CAAC;EAEDQ,WAAW,EAAE;IACXT,GAAG,EAAE,iBAAiB;IACtBC,KAAK,EAAE;EACT,CAAC;EAEDS,MAAM,EAAE;IACNV,GAAG,EAAE,SAAS;IACdC,KAAK,EAAE;EACT,CAAC;EAEDU,YAAY,EAAE;IACZX,GAAG,EAAE,mBAAmB;IACxBC,KAAK,EAAE;EACT,CAAC;EAEDW,OAAO,EAAE;IACPZ,GAAG,EAAE,WAAW;IAChBC,KAAK,EAAE;EACT,CAAC;EAEDY,WAAW,EAAE;IACXb,GAAG,EAAE,iBAAiB;IACtBC,KAAK,EAAE;EACT,CAAC;EAEDa,MAAM,EAAE;IACNd,GAAG,EAAE,SAAS;IACdC,KAAK,EAAE;EACT,CAAC;EAEDc,UAAU,EAAE;IACVf,GAAG,EAAE,cAAc;IACnBC,KAAK,EAAE;EACT,CAAC;EAEDe,YAAY,EAAE;IACZhB,GAAG,EAAE,gBAAgB;IACrBC,KAAK,EAAE;EACT;AACF,CAAC;AAED,MAAMgB,WAAW,GAAG,CAClB,MAAM,EACN,KAAK,EACL,IAAI,EACJ,KAAK,EACL,MAAM,EACN,KAAK,EACL,MAAM,EACN,KAAK,EACL,MAAM,EACN,IAAI,EACJ,IAAI,EACJ,QAAQ,EACR,MAAM,CACP;AAED,OAAO,MAAMC,cAAc,GAAGA,CAACC,KAAK,EAAEC,KAAK,EAAEC,OAAO,KAAK;EACvD,IAAIC,MAAM;EAEV,MAAMC,UAAU,GAAGzB,oBAAoB,CAACqB,KAAK,CAAC;EAC9C,IAAI,OAAOI,UAAU,KAAK,QAAQ,EAAE;IAClCD,MAAM,GAAGC,UAAU;EACrB,CAAC,MAAM,IAAIH,KAAK,KAAK,CAAC,EAAE;IACtBE,MAAM,GAAGC,UAAU,CAACvB,GAAG;EACzB,CAAC,MAAM;IACLsB,MAAM,GAAGC,UAAU,CAACtB,KAAK,CAACuB,OAAO,CAC/B,WAAW,EACXJ,KAAK,GAAG,EAAE,GAAGH,WAAW,CAACG,KAAK,CAAC,GAAGK,MAAM,CAACL,KAAK,CAChD,CAAC;EACH;EAEA,IAAIC,OAAO,aAAPA,OAAO,eAAPA,OAAO,CAAEK,SAAS,EAAE;IACtB,IAAIL,OAAO,CAACM,UAAU,IAAIN,OAAO,CAACM,UAAU,GAAG,CAAC,EAAE;MAChD,OAAO,KAAK,GAAGL,MAAM;IACvB,CAAC,MAAM;MACL,OAAOA,MAAM,GAAG,QAAQ;IAC1B;EACF;EAEA,OAAOA,MAAM;AACf,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
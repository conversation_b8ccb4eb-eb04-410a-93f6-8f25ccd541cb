"use strict";(self.webpackChunkfrontend=self.webpackChunkfrontend||[]).push([[240],{1104:(e,t,n)=>{n.d(t,{T:()=>S,Z:()=>w});var r=n(5043),a=n(4052),o=n(2018),l=n(1828),i=n(5797),s=n(2028),c=n(9988),u=n(8794),d=n(4504);function p(){return p=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)({}).hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},p.apply(null,arguments)}function m(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=Array(t);n<t;n++)r[n]=e[n];return r}function f(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,a,o,l,i=[],s=!0,c=!1;try{if(o=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;s=!1}else for(;!(s=(r=o.call(n)).done)&&(i.push(r.value),i.length!==t);s=!0);}catch(e){c=!0,a=e}finally{try{if(!s&&null!=n.return&&(l=n.return(),Object(l)!==l))return}finally{if(c)throw a}}return i}}(e,t)||function(e,t){if(e){if("string"==typeof e)return m(e,t);var n={}.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?m(e,t):void 0}}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function v(e){return v="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},v(e)}function y(e){var t=function(e,t){if("object"!=v(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!=v(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==v(t)?t:t+""}function g(e,t,n){return(t=y(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var b={root:"p-confirm-dialog",message:"p-confirm-dialog-message",icon:"p-confirm-dialog-icon",acceptButton:"p-confirm-dialog-accept",rejectButton:function(e){var t=e.getPropValue;return(0,d.xW)("p-confirm-dialog-reject",{"p-button-text":!t("rejectClassName")})}},h=l.x.extend({defaultProps:{__TYPE:"ConfirmDialog",accept:null,acceptClassName:null,acceptIcon:null,acceptLabel:null,appendTo:null,breakpoints:null,children:void 0,className:null,content:null,defaultFocus:"accept",footer:null,icon:null,message:null,onHide:null,reject:null,rejectClassName:null,rejectIcon:null,rejectLabel:null,tagKey:void 0,visible:void 0},css:{classes:b}});function j(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function x(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?j(Object(n),!0).forEach((function(t){g(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):j(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var w=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};(e=x(x({},e),{visible:void 0===e.visible||e.visible})).visible&&c.s.emit("confirm-dialog",e);return{show:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};c.s.emit("confirm-dialog",x(x(x({},e),t),{visible:!0}))},hide:function(){c.s.emit("confirm-dialog",{visible:!1})}}},S=r.memo(r.forwardRef((function(e,t){var n=(0,s.qV)(),m=r.useContext(a.UM),v=h.getProps(e,m),y=f(r.useState(v.visible),2),g=y[0],b=y[1],j=f(r.useState(!1),2),w=j[0],S=j[1],N=r.useRef(null),O=r.useRef(!1),P=r.useRef(null),E=function(){var e=v.group;return N.current&&(e=N.current.group),Object.assign({},v,N.current,{group:e})},C=function(e){return E()[e]},I=function(e){for(var t=arguments.length,n=new Array(t>1?t-1:0),r=1;r<t;r++)n[r-1]=arguments[r];return d.BF.getPropValue(C(e),n)},D=C("acceptLabel")||(0,a.WP)("accept"),T=C("rejectLabel")||(0,a.WP)("reject"),V={props:v,state:{visible:g}},A=h.setMetaData(V),M=A.ptm,k=A.cx,L=A.isUnstyled;(0,l.j)(h.css.styles,L,{name:"confirmdialog"});var B=function(){O.current||(O.current=!0,I("accept"),_("accept"))},F=function(){O.current||(O.current=!0,I("reject"),_("reject"))},R=function(){E().group===v.group&&(b(!0),O.current=!1,P.current=document.activeElement)},_=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"cancel";g&&("string"!==typeof e&&(e="cancel"),b(!1),I("onHide",e),d.DV.focus(P.current),P.current=null)},W=function(e){if(e.tagKey===v.tagKey){var t=g!==e.visible;C("target")!==e.target&&!v.target?(_(),N.current=e,S(!0)):t&&(N.current=e,e.visible?R():_())}};r.useEffect((function(){v.visible?R():_()}),[v.visible]),r.useEffect((function(){return v.target||v.message||c.s.on("confirm-dialog",W),function(){c.s.off("confirm-dialog",W)}}),[v.target]),(0,s.w5)((function(){w&&R()}),[w]),(0,s.l0)((function(){c.s.off("confirm-dialog",W)})),r.useImperativeHandle(t,(function(){return{props:v,confirm:W}}));var H=function(){var t=E(),a=d.BF.getJSXElement(C("message"),t),l=n({className:k("icon")},M("icon")),s=d.Hj.getJSXIcon(C("icon"),x({},l),{props:t}),c=function(){var e=C("defaultFocus"),t=(0,d.xW)("p-confirm-dialog-accept",C("acceptClassName")),a=(0,d.xW)("p-confirm-dialog-reject",{"p-button-text":!C("rejectClassName")},C("rejectClassName")),l=n({label:T,autoFocus:"reject"===e,icon:C("rejectIcon"),className:(0,d.xW)(C("rejectClassName"),k("rejectButton",{getPropValue:C})),onClick:F,pt:M("rejectButton"),unstyled:v.unstyled,__parentMetadata:{parent:V}},M("rejectButton")),i=n({label:D,autoFocus:void 0===e||"accept"===e,icon:C("acceptIcon"),className:(0,d.xW)(C("acceptClassName"),k("acceptButton")),onClick:B,pt:M("acceptButton"),unstyled:v.unstyled,__parentMetadata:{parent:V}},M("acceptButton")),s=r.createElement(r.Fragment,null,r.createElement(o.$,l),r.createElement(o.$,i));if(C("footer")){var c={accept:B,reject:F,acceptClassName:t,rejectClassName:a,acceptLabel:D,rejectLabel:T,element:s,props:E()};return d.BF.getJSXElement(C("footer"),c)}return s}(),u=n({className:k("message")},M("message")),m=n({visible:g,className:(0,d.xW)(C("className"),k("root")),footer:c,onHide:_,breakpoints:C("breakpoints"),pt:t.pt,unstyled:v.unstyled,appendTo:C("appendTo"),__parentMetadata:{parent:V}},h.getOtherProps(t));return r.createElement(i.l,p({},m,{content:null===e||void 0===e?void 0:e.content}),s,r.createElement("span",u,a))}();return r.createElement(u.Z,{element:H,appendTo:C("appendTo")})})));S.displayName="ConfirmDialog"},6104:(e,t,n)=>{n.d(t,{v:()=>f});var r=n(5043),a=n(4052),o=n(1828),l=n(2028),i=n(4504);function s(e){return s="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},s(e)}function c(e){var t=function(e,t){if("object"!=s(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!=s(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==s(t)?t:t+""}function u(e,t,n){return(t=c(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var d={value:"p-tag-value",icon:"p-tag-icon",root:function(e){var t=e.props;return(0,i.xW)("p-tag p-component",u(u({},"p-tag-".concat(t.severity),null!==t.severity),"p-tag-rounded",t.rounded))}},p=o.x.extend({defaultProps:{__TYPE:"Tag",value:null,severity:null,rounded:!1,icon:null,style:null,className:null,children:void 0},css:{classes:d,styles:"\n@layer primereact {\n    .p-tag {\n        display: inline-flex;\n        align-items: center;\n        justify-content: center;\n    }\n    \n    .p-tag-icon,\n    .p-tag-value,\n    .p-tag-icon.pi {\n        line-height: 1.5;\n    }\n    \n    .p-tag.p-tag-rounded {\n        border-radius: 10rem;\n    }\n}\n"}});function m(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}var f=r.forwardRef((function(e,t){var n=(0,l.qV)(),s=r.useContext(a.UM),c=p.getProps(e,s),d=p.setMetaData({props:c}),f=d.ptm,v=d.cx,y=d.isUnstyled;(0,o.j)(p.css.styles,y,{name:"tag"});var g=r.useRef(null),b=n({className:v("icon")},f("icon")),h=i.Hj.getJSXIcon(c.icon,function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?m(Object(n),!0).forEach((function(t){u(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):m(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}({},b),{props:c});r.useImperativeHandle(t,(function(){return{props:c,getElement:function(){return g.current}}}));var j=n({ref:g,className:(0,i.xW)(c.className,v("root")),style:c.style},p.getOtherProps(c),f("root")),x=n({className:v("value")},f("value"));return r.createElement("span",j,h,r.createElement("span",x,c.value),r.createElement("span",null,c.children))}));f.displayName="Tag"},6240:(e,t,n)=>{n.r(t),n.d(t,{default:()=>w});var r=n(5855),a=n(2018),o=n(5371),l=n(9642),i=n(1104),s=n(1063),c=n(8060),u=n(2052),d=n(5556),p=n(6104),m=n(828),f=n(5043),v=n(5666),y=n(724),g=n(9304),b=n(9604);var h=n(402),j=n(8150),x=n(579);const w=()=>{const e=(0,v.Zp)(),t=(0,f.useRef)(null),[n,w]=(0,f.useState)(""),[S,N]=(0,f.useState)(""),[O,P]=(0,f.useState)(void 0),[E,C]=(0,f.useState)(void 0),[I,D]=(0,f.useState)(void 0),[T,V]=(0,f.useState)(0),[A,M]=(0,f.useState)(!1),[k,L]=(0,f.useState)([]),[B,F]=(0,f.useState)(!0),[R,_]=(0,f.useState)({name:"",nationalId:"",doctortId:null,starttime:null,endtime:null,refreshKey:0}),{treatments:W,loading:H}=function(){let{patientname:e="",nationalId:t="",doctortid:n,startTime:r,endTime:a,refreshKey:o=0}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};const l=(0,f.useMemo)((()=>({patientname:e||void 0,nationalId:t||void 0,doctortid:n||void 0,starttime:null===r||void 0===r?void 0:r.toISOString(),endtime:null===a||void 0===a?void 0:a.toISOString()})),[e,t,n,r,a]),i=(0,f.useMemo)((()=>()=>g.R.getList(l)),[l]),{data:s=[],loading:c,error:u,refetch:d}=(0,b.K)(i,{dependencies:[o],initialData:[]});return{treatments:s,loading:c,error:u,refetch:d}}({patientname:R.name,nationalId:R.nationalId,doctortid:R.doctortId||null,startTime:R.starttime||null,endTime:R.endtime||null,refreshKey:T}),K=e=>{switch(e){case"10":return"info";case"20":return"warning";case"30":return"danger";case"40":return"success";default:return null}},$={1:"\u7537\u6027",2:"\u5973\u6027",3:"\u5176\u4ed6"},U={10:"\u65b0\u6848",20:"\u6cbb\u7642",30:"\u4e0a\u50b3",40:"\u7d50\u6848",50:"\u6536\u64da"};(0,f.useEffect)((()=>{(async()=>{try{F(!0),console.log("\u958b\u59cb\u8f09\u5165\u6cbb\u7642\u5e2b\u6578\u64da...");const e=await h.A.get("/api/users/DoctorList");console.log("\u6cbb\u7642\u5e2b\u6578\u64da:",e.data);const t="string"===typeof e.data?JSON.parse(e.data):e.data;L(t)}catch(n){var e;console.error("\u8f09\u5165\u6cbb\u7642\u5e2b\u6578\u64da\u5931\u6557:",n),null===(e=t.current)||void 0===e||e.show({severity:"error",summary:"\u932f\u8aa4",detail:n.details})}finally{F(!1)}})()}),[]),(0,f.useEffect)((()=>{var e;A&&!H&&(null===(e=t.current)||void 0===e||e.show({severity:"success",summary:"\u6210\u529f",detail:"\u75c5\u60a3\u8a3a\u7642\u7d00\u9304\u5df2\u522a\u9664"}),M(!1))}),[A,H]);const Z=()=>{V((e=>e+1))},z=(0,x.jsx)(a.$,{type:"button",icon:"pi pi-refresh",text:!0,onClick:()=>Z()}),J=(0,x.jsx)("div",{}),X=e=>{(0,i.Z)({message:"\u78ba\u5b9a\u8981\u522a\u9664\u9019\u7b46\u8cc7\u6599\u55ce\uff1f",header:"\u522a\u9664\u78ba\u8a8d",icon:"pi pi-exclamation-triangle",defaultFocus:"reject",acceptClassName:"p-button-danger",acceptLabel:"\u78ba\u5b9a",rejectLabel:"\u53d6\u6d88",accept:()=>(async e=>{try{await h.A.get("/api/treatment/Delete",{params:{orderNo:e}}),M(!0),Z()}catch(l){var n,r,a,o=403===l.status?"\u60a8\u7121\u6b0a\u9650\uff0c\u8acb\u901a\u77e5\u7ba1\u7406\u54e1":(null===(n=l.response)||void 0===n||null===(r=n.data)||void 0===r?void 0:r.message)||"\u522a\u9664\u5931\u6557";null===(a=t.current)||void 0===a||a.show({severity:"error",summary:"\u932f\u8aa4",detail:o})}})(e)})},Y=e=>e?(0,r.$)(e,"yyyy/MM/dd HH:mm:ss"):"";return H?(0,x.jsx)(d.A,{message:"\u8f09\u5165\u8a3a\u7642\u8cc7\u6599\u4e2d..."}):(0,x.jsxs)("div",{children:[(0,x.jsx)(m.y,{ref:t}),(0,x.jsx)(i.T,{}),(0,x.jsx)(j.Z,{title:"\u8a3a\u7642\u7d00\u9304",className:"mb-4",children:(0,x.jsx)("p",{className:"text-600 line-height-3 m-0",children:"\u75c5\u60a3\u7684\u8a3a\u7642\u7d00\u9304\uff0c\u53ef\u4ee5\u67e5\u8a62\u3001\u7de8\u8f2f\u3001\u522a\u9664\u8a3a\u7642\u8cc7\u6599\uff0c\u7d50\u6848\u7684\u8a3a\u7642\u8cc7\u6599\u53ef\u4ee5\u7de8\u8f2f\u6536\u64da\u4e26\u88fd\u4f5c\u5831\u8868\u3002"})}),(0,x.jsx)(j.Z,{className:"mb-4",children:(0,x.jsxs)("div",{className:"grid",children:[(0,x.jsx)("div",{className:"col-12 md:col-4",children:(0,x.jsx)(u.S,{id:"name",type:"text",value:n,onChange:e=>w(e.target.value),placeholder:"\u75c5\u60a3\u59d3\u540d",className:"w-full"})}),(0,x.jsx)("div",{className:"col-6 md:col-4",children:(0,x.jsx)(c.m,{value:O,onChange:e=>P(e.value),options:k,optionLabel:"Name",optionValue:"Id",placeholder:"\u8acb\u9078\u64c7\u6cbb\u7642\u5e2b",disabled:B,className:"w-full",showClear:!0})}),(0,x.jsx)("div",{className:"col-6 md:col-4",children:(0,x.jsx)(u.S,{id:"nationalId",type:"text",value:S,onChange:e=>N(e.target.value),placeholder:"\u75c5\u60a3\u8eab\u5206\u8b49",className:"w-full"})}),(0,x.jsx)("div",{className:"col-6 md:col-3",children:(0,x.jsx)(o.V,{id:"starttime",value:E,onChange:e=>C(e.value),placeholder:"\u958b\u59cb\u6642\u9593",className:"w-full",dateFormat:"yy/mm/dd",showIcon:!0})}),(0,x.jsx)("div",{className:"col-6 md:col-3",children:(0,x.jsx)(o.V,{id:"endtime",value:I,onChange:e=>D(e.value),placeholder:"\u7d50\u675f\u6642\u9593",className:"w-full",dateFormat:"yy/mm/dd",showIcon:!0})}),(0,x.jsx)("div",{className:"col-12 md:col-4",children:(0,x.jsx)("div",{className:"flex gap-2",children:(0,x.jsx)(a.$,{label:"\u67e5\u8a62",icon:"pi pi-search",onClick:()=>{V(T+1),_({name:n,nationalId:S,doctortId:O,starttime:E,endtime:I,refreshKey:T})}})})})]})}),(0,x.jsx)(j.Z,{children:(0,x.jsxs)(s.b,{value:W,paginator:!0,rows:10,rowsPerPageOptions:[10,20,30,40],tableStyle:{minWidth:"50rem"},emptyMessage:"\u6c92\u6709\u627e\u5230\u8a3a\u7642\u8cc7\u6599",paginatorLeft:z,paginatorRight:J,children:[(0,x.jsx)(l.V,{field:"orderNo",header:"\u6848\u865f",style:{width:"5%"}}),(0,x.jsx)(l.V,{field:"patientName",header:"\u75c5\u60a3\u59d3\u540d",style:{width:"5%"}}),(0,x.jsx)(l.V,{field:"doctorName",header:"\u6cbb\u7642\u6cbb\u7642\u5e2b",style:{width:"5%"}}),(0,x.jsx)(l.V,{field:"patientGender",header:"\u6027\u5225",style:{width:"3%"},body:e=>{var t=String(e.patientGender);const n=$[t];return(0,x.jsx)("div",{children:n})}}),(0,x.jsx)(l.V,{field:"patientBirthDate",header:"\u5e74\u9f61",style:{width:"3%"},body:e=>(e=>{if(!e)return"";const t=new Date(e),n=new Date;let r=n.getFullYear()-t.getFullYear();return(n.getMonth()<t.getMonth()||n.getMonth()===t.getMonth()&&n.getDate()<t.getDate())&&r--,r})(e.patientBirthDate)}),(0,x.jsx)(l.V,{field:"step",header:"\u968e\u6bb5",style:{width:"3%"},body:e=>(0,x.jsx)("div",{children:(0,x.jsx)(p.v,{value:U[String(e.step)],severity:K(String(e.step))})})}),(0,x.jsx)(l.V,{field:"createdAt",header:"\u65b0\u589e\u65e5\u671f",style:{width:"8%"},body:e=>Y(e.createdAt)}),(0,x.jsx)(l.V,{field:"updatedAt",header:"\u66f4\u65b0\u65e5\u671f",style:{width:"8%"},body:e=>Y(e.updatedAt)}),(0,x.jsx)(l.V,{field:"operatorUserName",header:"\u64cd\u4f5c\u4eba",style:{width:"5%"}}),(0,x.jsx)(l.V,{field:"option",header:"\u529f\u80fd",style:{width:"12%"},body:n=>(0,x.jsxs)("div",{className:"flex gap-1",children:[(0,x.jsx)(a.$,{label:"\u7de8\u8f2f",type:"button",icon:"pi pi-file-edit",onClick:()=>(async(n,r)=>{try{const t=(await h.A.get("/api/treatment/",{params:{Id:n}})).data;t&&e(y.bw.TREATMENT_DETAIL,{state:{treatment:t,patient:{id:r}}})}catch(s){var a,o,l,i=403===s.status?"\u60a8\u7121\u6b0a\u9650\uff0c\u8acb\u901a\u77e5\u7ba1\u7406\u54e1":(null===(a=s.response)||void 0===a||null===(o=a.data)||void 0===o?void 0:o.message)||"\u7de8\u8f2f\u5931\u6557";null===(l=t.current)||void 0===l||l.show({severity:"error",summary:"\u932f\u8aa4",detail:i})}})(n.id,n.patientId),size:"small",severity:"info",style:{fontSize:"1rem",margin:"3px"}}),(0,x.jsx)(a.$,{label:"\u6536\u64da",type:"button",icon:"pi pi-clipboard",onClick:()=>(async t=>{e(y.bw.RECEIPT_DETAIL,{state:{treatment:{receiptUrl:t.receiptUrl,id:t.id,patientId:t.patientId}}})})(n),size:"small",severity:"success",style:{fontSize:"1rem",margin:"3px"},disabled:40!==n.step&&50!==n.step}),(0,x.jsx)(a.$,{label:"\u522a\u9664",type:"button",icon:"pi pi-file-excel",onClick:()=>X(n.orderNo),size:"small",severity:"danger",style:{fontSize:"1rem",margin:"3px"},disabled:40===n.step||50===n.step})]})})]})})]})}},9604:(e,t,n)=>{n.d(t,{K:()=>o});var r=n(5043),a=n(1512);function o(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};const{initialData:n,dependencies:o=[],enabled:l=!0,onSuccess:i,onError:s}=t,[c,u]=(0,r.useState)(n),[d,p]=(0,r.useState)(!1),[m,f]=(0,r.useState)(null),{handleError:v}=(0,a.u)(),y=(0,r.useRef)(!0),g=(0,r.useCallback)((async()=>{if(l)try{p(!0),f(null);const t=await e();y.current&&(u(t),null===i||void 0===i||i(t))}catch(t){if(y.current){const e=v(t);f(e),null===s||void 0===s||s(e)}}finally{y.current&&p(!1)}}),[e,l,v,i,s]);return(0,r.useEffect)((()=>{g()}),[g,...o]),(0,r.useEffect)((()=>()=>{y.current=!1}),[]),{data:c,loading:d,error:m,refetch:g,setData:u}}}}]);
//# sourceMappingURL=240.c1dbde19.chunk.js.map
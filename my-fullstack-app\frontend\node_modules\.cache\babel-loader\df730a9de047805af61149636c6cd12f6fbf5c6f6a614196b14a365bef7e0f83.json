{"ast": null, "code": "const formatRelativeLocale = {\n  lastWeek: \"eeee 'الماضي عند الساعة' p\",\n  yesterday: \"'الأمس عند الساعة' p\",\n  today: \"'اليوم عند الساعة' p\",\n  tomorrow: \"'غدا عند الساعة' p\",\n  nextWeek: \"eeee 'القادم عند الساعة' p\",\n  other: \"P\"\n};\nexport const formatRelative = token => formatRelativeLocale[token];", "map": {"version": 3, "names": ["formatRelativeLocale", "lastWeek", "yesterday", "today", "tomorrow", "nextWeek", "other", "formatRelative", "token"], "sources": ["C:/Users/<USER>/Desktop/demo-git/demo-react/my-fullstack-app/frontend/node_modules/date-fns/locale/ar/_lib/formatRelative.js"], "sourcesContent": ["const formatRelativeLocale = {\n  lastWeek: \"eeee 'الماضي عند الساعة' p\",\n  yesterday: \"'الأمس عند الساعة' p\",\n  today: \"'اليوم عند الساعة' p\",\n  tomorrow: \"'غدا عند الساعة' p\",\n  nextWeek: \"eeee 'القادم عند الساعة' p\",\n  other: \"P\",\n};\n\nexport const formatRelative = (token) => formatRelativeLocale[token];\n"], "mappings": "AAAA,MAAMA,oBAAoB,GAAG;EAC3BC,QAAQ,EAAE,4BAA4B;EACtCC,SAAS,EAAE,sBAAsB;EACjCC,KAAK,EAAE,sBAAsB;EAC7BC,QAAQ,EAAE,oBAAoB;EAC9BC,QAAQ,EAAE,4BAA4B;EACtCC,KAAK,EAAE;AACT,CAAC;AAED,OAAO,MAAMC,cAAc,GAAIC,KAAK,IAAKR,oBAAoB,CAACQ,KAAK,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
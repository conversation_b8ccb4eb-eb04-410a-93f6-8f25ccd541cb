{"ast": null, "code": "const formatRelativeLocale = {\n  lastWeek: \"'上個'eeee p\",\n  yesterday: \"'昨天' p\",\n  today: \"'今天' p\",\n  tomorrow: \"'明天' p\",\n  nextWeek: \"'下個'eeee p\",\n  other: \"P\"\n};\nexport const formatRelative = (token, _date, _baseDate, _options) => formatRelativeLocale[token];", "map": {"version": 3, "names": ["formatRelativeLocale", "lastWeek", "yesterday", "today", "tomorrow", "nextWeek", "other", "formatRelative", "token", "_date", "_baseDate", "_options"], "sources": ["C:/Users/<USER>/Desktop/demo-git/demo-react/my-fullstack-app/frontend/node_modules/date-fns/locale/zh-HK/_lib/formatRelative.js"], "sourcesContent": ["const formatRelativeLocale = {\n  lastWeek: \"'上個'eeee p\",\n  yesterday: \"'昨天' p\",\n  today: \"'今天' p\",\n  tomorrow: \"'明天' p\",\n  nextWeek: \"'下個'eeee p\",\n  other: \"P\",\n};\n\nexport const formatRelative = (token, _date, _baseDate, _options) =>\n  formatRelativeLocale[token];\n"], "mappings": "AAAA,MAAMA,oBAAoB,GAAG;EAC3BC,QAAQ,EAAE,YAAY;EACtBC,SAAS,EAAE,QAAQ;EACnBC,KAAK,EAAE,QAAQ;EACfC,QAAQ,EAAE,QAAQ;EAClBC,QAAQ,EAAE,YAAY;EACtBC,KAAK,EAAE;AACT,CAAC;AAED,OAAO,MAAMC,cAAc,GAAGA,CAACC,KAAK,EAAEC,KAAK,EAAEC,SAAS,EAAEC,QAAQ,KAC9DX,oBAAoB,CAACQ,KAAK,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
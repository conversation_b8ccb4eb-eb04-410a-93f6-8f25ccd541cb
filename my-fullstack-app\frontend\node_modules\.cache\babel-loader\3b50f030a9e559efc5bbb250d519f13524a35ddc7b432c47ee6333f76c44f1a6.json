{"ast": null, "code": "import React,{useEffect,useState}from'react';import{useNavigate,useLocation}from'react-router-dom';import{ROUTES}from'../../constants/routes';import LoadingSpinner from'../Common/LoadingSpinner';import{jsx as _jsx,Fragment as _Fragment}from\"react/jsx-runtime\";const PasswordCheckRoute=_ref=>{let{children}=_ref;const navigate=useNavigate();const location=useLocation();const[isChecking,setIsChecking]=useState(true);const[shouldRender,setShouldRender]=useState(false);useEffect(()=>{const checkPasswordStatus=async()=>{try{const token=localStorage.getItem('token');const userId=localStorage.getItem('userId');// 如果沒有登入信息，不渲染內容（讓 ProtectedRoute 處理跳轉）\nif(!token||!userId){console.log('PasswordCheckRoute: 沒有登入信息，不渲染內容');setShouldRender(false);setIsChecking(false);return;}// 如果當前已經在更新密碼頁面，直接渲染\nif(location.pathname===ROUTES.UPDATE_PASSWORD){console.log('PasswordCheckRoute: 當前在更新密碼頁面，直接渲染');setShouldRender(true);setIsChecking(false);return;}// 檢查用戶是否使用預設密碼\n// 從 localStorage 中讀取登入時儲存的密碼狀態\nconst isDefaultPassword=localStorage.getItem('isDefaultPassword');if(isDefaultPassword==='true'){console.log('PasswordCheckRoute: 檢測到使用預設密碼，導航到更新密碼頁面');navigate(ROUTES.UPDATE_PASSWORD);return;}// 密碼已更新，可以正常渲染頁面\nconsole.log('PasswordCheckRoute: 密碼已更新，正常渲染頁面');setShouldRender(true);}catch(error){console.error('PasswordCheckRoute: 檢查密碼狀態失敗:',error);// 發生錯誤時，不渲染內容\nsetShouldRender(false);}finally{setIsChecking(false);}};checkPasswordStatus();},[navigate,location.pathname]);if(isChecking){return/*#__PURE__*/_jsx(LoadingSpinner,{message:\"\\u6AA2\\u67E5\\u7528\\u6236\\u72C0\\u614B\\u4E2D...\"});}if(!shouldRender){return null;}return/*#__PURE__*/_jsx(_Fragment,{children:children});};export default PasswordCheckRoute;", "map": {"version": 3, "names": ["React", "useEffect", "useState", "useNavigate", "useLocation", "ROUTES", "LoadingSpinner", "jsx", "_jsx", "Fragment", "_Fragment", "PasswordCheckRoute", "_ref", "children", "navigate", "location", "isChecking", "setIsChecking", "shouldRender", "setShouldRender", "checkPasswordStatus", "token", "localStorage", "getItem", "userId", "console", "log", "pathname", "UPDATE_PASSWORD", "isDefaultPassword", "error", "message"], "sources": ["C:/Users/<USER>/Desktop/demo-git/demo-react/my-fullstack-app/frontend/src/components/Auth/PasswordCheckRoute.tsx"], "sourcesContent": ["import React, { useEffect, useState } from 'react';\r\nimport { useNavigate, useLocation } from 'react-router-dom';\r\nimport { ROUTES } from '../../constants/routes';\r\nimport LoadingSpinner from '../Common/LoadingSpinner';\r\n\r\ninterface PasswordCheckRouteProps {\r\n  children: React.ReactNode;\r\n}\r\n\r\nconst PasswordCheckRoute: React.FC<PasswordCheckRouteProps> = ({ children }) => {\r\n  const navigate = useNavigate();\r\n  const location = useLocation();\r\n  const [isChecking, setIsChecking] = useState(true);\r\n  const [shouldRender, setShouldRender] = useState(false);\r\n\r\n  useEffect(() => {\r\n    const checkPasswordStatus = async () => {\r\n      try {\r\n        const token = localStorage.getItem('token');\r\n        const userId = localStorage.getItem('userId');\r\n\r\n        // 如果沒有登入信息，不渲染內容（讓 ProtectedRoute 處理跳轉）\r\n        if (!token || !userId) {\r\n          console.log('PasswordCheckRoute: 沒有登入信息，不渲染內容');\r\n          setShouldRender(false);\r\n          setIsChecking(false);\r\n          return;\r\n        }\r\n\r\n        // 如果當前已經在更新密碼頁面，直接渲染\r\n        if (location.pathname === ROUTES.UPDATE_PASSWORD) {\r\n          console.log('PasswordCheckRoute: 當前在更新密碼頁面，直接渲染');\r\n          setShouldRender(true);\r\n          setIsChecking(false);\r\n          return;\r\n        }\r\n\r\n        // 檢查用戶是否使用預設密碼\r\n        // 從 localStorage 中讀取登入時儲存的密碼狀態\r\n        const isDefaultPassword = localStorage.getItem('isDefaultPassword');\r\n\r\n        if (isDefaultPassword === 'true') {\r\n          console.log('PasswordCheckRoute: 檢測到使用預設密碼，導航到更新密碼頁面');\r\n          navigate(ROUTES.UPDATE_PASSWORD);\r\n          return;\r\n        }\r\n\r\n        // 密碼已更新，可以正常渲染頁面\r\n        console.log('PasswordCheckRoute: 密碼已更新，正常渲染頁面');\r\n        setShouldRender(true);\r\n\r\n      } catch (error) {\r\n        console.error('PasswordCheckRoute: 檢查密碼狀態失敗:', error);\r\n        // 發生錯誤時，不渲染內容\r\n        setShouldRender(false);\r\n      } finally {\r\n        setIsChecking(false);\r\n      }\r\n    };\r\n\r\n    checkPasswordStatus();\r\n  }, [navigate, location.pathname]);\r\n\r\n  if (isChecking) {\r\n    return <LoadingSpinner message=\"檢查用戶狀態中...\" />;\r\n  }\r\n\r\n  if (!shouldRender) {\r\n    return null;\r\n  }\r\n\r\n  return <>{children}</>;\r\n};\r\n\r\nexport default PasswordCheckRoute;\r\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,SAAS,CAAEC,QAAQ,KAAQ,OAAO,CAClD,OAASC,WAAW,CAAEC,WAAW,KAAQ,kBAAkB,CAC3D,OAASC,MAAM,KAAQ,wBAAwB,CAC/C,MAAO,CAAAC,cAAc,KAAM,0BAA0B,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,QAAA,IAAAC,SAAA,yBAMtD,KAAM,CAAAC,kBAAqD,CAAGC,IAAA,EAAkB,IAAjB,CAAEC,QAAS,CAAC,CAAAD,IAAA,CACzE,KAAM,CAAAE,QAAQ,CAAGX,WAAW,CAAC,CAAC,CAC9B,KAAM,CAAAY,QAAQ,CAAGX,WAAW,CAAC,CAAC,CAC9B,KAAM,CAACY,UAAU,CAAEC,aAAa,CAAC,CAAGf,QAAQ,CAAC,IAAI,CAAC,CAClD,KAAM,CAACgB,YAAY,CAAEC,eAAe,CAAC,CAAGjB,QAAQ,CAAC,KAAK,CAAC,CAEvDD,SAAS,CAAC,IAAM,CACd,KAAM,CAAAmB,mBAAmB,CAAG,KAAAA,CAAA,GAAY,CACtC,GAAI,CACF,KAAM,CAAAC,KAAK,CAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC,CAC3C,KAAM,CAAAC,MAAM,CAAGF,YAAY,CAACC,OAAO,CAAC,QAAQ,CAAC,CAE7C;AACA,GAAI,CAACF,KAAK,EAAI,CAACG,MAAM,CAAE,CACrBC,OAAO,CAACC,GAAG,CAAC,kCAAkC,CAAC,CAC/CP,eAAe,CAAC,KAAK,CAAC,CACtBF,aAAa,CAAC,KAAK,CAAC,CACpB,OACF,CAEA;AACA,GAAIF,QAAQ,CAACY,QAAQ,GAAKtB,MAAM,CAACuB,eAAe,CAAE,CAChDH,OAAO,CAACC,GAAG,CAAC,oCAAoC,CAAC,CACjDP,eAAe,CAAC,IAAI,CAAC,CACrBF,aAAa,CAAC,KAAK,CAAC,CACpB,OACF,CAEA;AACA;AACA,KAAM,CAAAY,iBAAiB,CAAGP,YAAY,CAACC,OAAO,CAAC,mBAAmB,CAAC,CAEnE,GAAIM,iBAAiB,GAAK,MAAM,CAAE,CAChCJ,OAAO,CAACC,GAAG,CAAC,yCAAyC,CAAC,CACtDZ,QAAQ,CAACT,MAAM,CAACuB,eAAe,CAAC,CAChC,OACF,CAEA;AACAH,OAAO,CAACC,GAAG,CAAC,kCAAkC,CAAC,CAC/CP,eAAe,CAAC,IAAI,CAAC,CAEvB,CAAE,MAAOW,KAAK,CAAE,CACdL,OAAO,CAACK,KAAK,CAAC,+BAA+B,CAAEA,KAAK,CAAC,CACrD;AACAX,eAAe,CAAC,KAAK,CAAC,CACxB,CAAC,OAAS,CACRF,aAAa,CAAC,KAAK,CAAC,CACtB,CACF,CAAC,CAEDG,mBAAmB,CAAC,CAAC,CACvB,CAAC,CAAE,CAACN,QAAQ,CAAEC,QAAQ,CAACY,QAAQ,CAAC,CAAC,CAEjC,GAAIX,UAAU,CAAE,CACd,mBAAOR,IAAA,CAACF,cAAc,EAACyB,OAAO,CAAC,+CAAY,CAAE,CAAC,CAChD,CAEA,GAAI,CAACb,YAAY,CAAE,CACjB,MAAO,KAAI,CACb,CAEA,mBAAOV,IAAA,CAAAE,SAAA,EAAAG,QAAA,CAAGA,QAAQ,CAAG,CAAC,CACxB,CAAC,CAED,cAAe,CAAAF,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
# 頁面權限控制實作總結

## 📋 **實作概述**

基於現有的 Microsoft.AspNetCore.Authorization 框架，擴展實作了完整的頁面權限控制系統，包含後端權限檢查 API 和前端權限保護機制。

## 🔧 **後端實作**

### **AuthController 新增功能**

#### **1. 頁面權限檢查 API**
```csharp
[Authorize]
[HttpGet("check-page-permission")]
public async Task<IActionResult> CheckPagePermission([FromQuery] string pageName)
```

**功能特色：**
- ✅ 基於頁面名稱的權限映射
- ✅ 支援多角色權限檢查
- ✅ 返回詳細的權限資訊
- ✅ 自動處理未定義頁面（預設允許）

**權限映射表：**
```csharp
var pagePermissions = new Dictionary<string, string[]>
{
    { "users", new[] { "Admin", "Manager" } },
    { "doctors", new[] { "Admin", "Manager" } },
    { "patients", new[] { "Admin", "Manager", "Doctor" } },
    { "treatments", new[] { "Admin", "Manager", "Doctor" } },
    { "schedules", new[] { "Admin", "Manager", "Doctor" } },
    { "receipts", new[] { "Admin", "Manager" } },
    { "reports", new[] { "Admin", "Manager" } },
    { "login-logs", new[] { "Admin", "Manager" } },
    { "ip-blocks", new[] { "Admin" } },
    { "backup", new[] { "Admin" } }
};
```

#### **2. 用戶資訊 API**
```csharp
[Authorize]
[HttpGet("user-info")]
public async Task<IActionResult> GetUserInfo()
```

**回應格式：**
```json
{
  "id": 1,
  "username": "admin",
  "role": "Admin",
  "isEnabled": true
}
```

## 🎨 **前端實作**

### **權限檢查 Hook (usePermission.ts)**

#### **1. usePagePermission Hook**
```typescript
const { hasPermission, loading, error, permissionData } = usePagePermission('users');
```

**功能：**
- ✅ 自動檢查指定頁面權限
- ✅ 處理載入狀態和錯誤
- ✅ 返回詳細的權限資訊

#### **2. useUserInfo Hook**
```typescript
const { userInfo, loading, error } = useUserInfo();
```

**功能：**
- ✅ 獲取當前用戶資訊
- ✅ 自動處理認證狀態
- ✅ 提供用戶角色資訊

#### **3. PermissionGuard 組件**
```typescript
<PermissionGuard pageName="users" fallback={<AccessDenied />}>
  <YourPageContent />
</PermissionGuard>
```

**功能：**
- ✅ 聲明式權限保護
- ✅ 自定義拒絕訪問頁面
- ✅ 載入狀態處理

### **UsersPage 權限控制實作**

#### **權限檢查邏輯**
```typescript
const UsersPage: React.FC = () => {
  // 權限檢查
  const { hasPermission, loading: permissionLoading, error: permissionError } = usePagePermission('users');
  const { userInfo } = useUserInfo();

  // 權限檢查載入中
  if (permissionLoading) {
    return <LoadingScreen message="檢查權限中..." />;
  }

  // 權限檢查失敗
  if (permissionError) {
    return <ErrorScreen error={permissionError} />;
  }

  // 沒有權限
  if (!hasPermission) {
    return <AccessDeniedScreen userRole={userInfo?.role} requiredRoles="Admin, Manager" />;
  }

  // 正常頁面內容
  return <PageContent />;
};
```

#### **UI 狀態處理**

**1. 權限檢查載入中**
```tsx
<div className="flex align-items-center justify-content-center min-h-screen">
  <div className="text-center">
    <ProgressSpinner />
    <p className="mt-3">檢查權限中...</p>
  </div>
</div>
```

**2. 權限檢查失敗**
```tsx
<div className="text-center">
  <i className="pi pi-exclamation-triangle text-6xl text-orange-500 mb-3"></i>
  <h3 className="text-xl font-semibold mb-2">權限檢查失敗</h3>
  <p className="text-gray-600 mb-4">{permissionError}</p>
  <Button label="重新載入" icon="pi pi-refresh" onClick={() => window.location.reload()} />
</div>
```

**3. 訪問被拒絕**
```tsx
<div className="text-center">
  <i className="pi pi-ban text-6xl text-red-500 mb-3"></i>
  <h3 className="text-xl font-semibold mb-2">訪問被拒絕</h3>
  <p className="text-gray-600 mb-2">您沒有權限訪問用戶管理頁面</p>
  <p className="text-sm text-gray-500 mb-4">
    當前角色: {userInfo?.role || '未知'} | 需要角色: Admin, Manager
  </p>
  <Button label="返回首頁" icon="pi pi-home" onClick={() => window.location.href = '/'} />
</div>
```

## 🎯 **權限控制流程**

### **頁面訪問流程**
```mermaid
graph TD
    A[用戶訪問頁面] --> B[檢查 JWT Token]
    B --> C{Token 有效?}
    C -->|否| D[重定向到登入頁]
    C -->|是| E[調用權限檢查 API]
    E --> F{有權限?}
    F -->|否| G[顯示拒絕訪問頁面]
    F -->|是| H[顯示頁面內容]
    E --> I{API 錯誤?}
    I -->|是| J[顯示錯誤頁面]
```

### **權限檢查邏輯**
```mermaid
graph TD
    A[接收頁面名稱] --> B[查找權限映射]
    B --> C{頁面已定義?}
    C -->|否| D[預設允許訪問]
    C -->|是| E[獲取需要角色]
    E --> F[檢查用戶角色]
    F --> G{角色匹配?}
    G -->|是| H[允許訪問]
    G -->|否| I[拒絕訪問]
```

## 📊 **權限矩陣**

| 頁面/功能 | Admin | Manager | Doctor | User |
|-----------|-------|---------|--------|------|
| 用戶管理 | ✅ | ✅ | ❌ | ❌ |
| 醫師管理 | ✅ | ✅ | ❌ | ❌ |
| 病患管理 | ✅ | ✅ | ✅ | ❌ |
| 治療管理 | ✅ | ✅ | ✅ | ❌ |
| 排程管理 | ✅ | ✅ | ✅ | ❌ |
| 收據管理 | ✅ | ✅ | ❌ | ❌ |
| 報表管理 | ✅ | ✅ | ❌ | ❌ |
| 登入紀錄 | ✅ | ✅ | ❌ | ❌ |
| IP 封鎖 | ✅ | ❌ | ❌ | ❌ |
| 備份管理 | ✅ | ❌ | ❌ | ❌ |

## 🚀 **使用方式**

### **1. 應用到新頁面**

```typescript
import { usePagePermission, useUserInfo } from '../../hooks/usePermission';

const NewPage: React.FC = () => {
  const { hasPermission, loading, error } = usePagePermission('new-page');
  const { userInfo } = useUserInfo();

  // 權限檢查邏輯
  if (loading) return <LoadingScreen />;
  if (error) return <ErrorScreen error={error} />;
  if (!hasPermission) return <AccessDeniedScreen />;

  return <YourPageContent />;
};
```

### **2. 添加新頁面權限**

在後端 `AuthController.CheckPagePermission` 中添加：
```csharp
{ "new-page", new[] { "Admin", "Manager" } }
```

### **3. 使用 PermissionGuard 組件**

```typescript
<PermissionGuard pageName="users">
  <UsersPageContent />
</PermissionGuard>
```

## 🔧 **技術特色**

### ✅ **安全性**
- JWT Token 驗證
- 後端權限檢查
- 前端雙重保護

### ✅ **使用者體驗**
- 載入狀態提示
- 友好的錯誤頁面
- 清晰的權限說明

### ✅ **開發友好**
- 聲明式權限控制
- 可重用的 Hook
- 靈活的配置

### ✅ **維護性**
- 集中的權限映射
- 統一的錯誤處理
- 模組化設計

## 🎉 **實作完成**

**頁面權限控制系統已完全實作完成！**

### **核心功能**
- ✅ **後端權限檢查 API**: 完整的頁面權限驗證
- ✅ **前端權限 Hook**: 易用的權限檢查工具
- ✅ **UsersPage 權限控制**: 完整的權限保護實作
- ✅ **錯誤處理機制**: 友好的用戶體驗
- ✅ **載入狀態管理**: 流暢的操作體驗

### **立即可用**
- 🎯 **UsersPage**: 已實作完整權限控制
- 🎯 **權限 Hook**: 可應用到其他頁面
- 🎯 **API 端點**: 支援所有頁面權限檢查
- 🎯 **擴展性**: 易於添加新頁面權限

**系統現在具備完整的頁面級權限控制能力，確保只有授權用戶才能訪問相應的功能頁面！**

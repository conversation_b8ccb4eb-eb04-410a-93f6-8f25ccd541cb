import { Button } from 'primereact/button';
import { Password } from 'primereact/password';
import { Toast } from 'primereact/toast';
import React, { useRef, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';
import api from '../../services/api';

interface UpdatePasswordForm {
  oldPassword: string;
  newPassword: string;
  confirmPassword: string;
}

const UpdatePasswordPage: React.FC = () => {
  const navigate = useNavigate();
  const toast = useRef<Toast>(null);
  const { logout: authLogout } = useAuth();
  
  const [formData, setFormData] = useState<UpdatePasswordForm>({
    oldPassword: '',
    newPassword: '',
    confirmPassword: ''
  });
  
  const [loading, setLoading] = useState(false);
  const [errors, setErrors] = useState<Partial<UpdatePasswordForm>>({});

  const handleInputChange = (field: keyof UpdatePasswordForm, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    // 清除該欄位的錯誤
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: undefined }));
    }
  };

  const validateForm = (): boolean => {
    const newErrors: Partial<UpdatePasswordForm> = {};

    if (!formData.oldPassword) {
      newErrors.oldPassword = '請輸入舊密碼';
    }

    if (!formData.newPassword) {
      newErrors.newPassword = '請輸入新密碼';
    } else if (formData.newPassword.length < 6) {
      newErrors.newPassword = '新密碼長度至少需要6個字符';
    }

    if (!formData.confirmPassword) {
      newErrors.confirmPassword = '請確認新密碼';
    } else if (formData.newPassword !== formData.confirmPassword) {
      newErrors.confirmPassword = '新密碼與確認密碼不一致';
    }

    if (formData.oldPassword === formData.newPassword) {
      newErrors.newPassword = '新密碼不能與舊密碼相同';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async () => {
    if (!validateForm()) {
      return;
    }

    setLoading(true);
    try {
      const userId = localStorage.getItem('userId');
      if (!userId) {
        toast.current?.show({
          severity: 'error',
          summary: '錯誤',
          detail: '用戶信息不存在，請重新登入'
        });
        navigate('/login');
        return;
      }

      const response = await api.post('/api/Users/<USER>', {
        userId: parseInt(userId),
        oldPassword: formData.oldPassword,
        newPassword: formData.newPassword,
        confirmPassword: formData.confirmPassword
      });

      toast.current?.show({
        severity: 'success',
        summary: '成功',
        detail: response.data.message || '密碼更新成功'
      });

      // 清除預設密碼狀態
      localStorage.setItem('isDefaultPassword', 'false');

      // 清除表單
      setFormData({
        oldPassword: '',
        newPassword: '',
        confirmPassword: ''
      });

      // 延遲跳轉到主頁面
      setTimeout(() => {
        navigate('/');
      }, 1000);

    } catch (error: any) {
      toast.current?.show({
        severity: 'error',
        summary: '更新失敗',
        detail: error.response?.data || '密碼更新失敗，請稍後再試'
      });
    } finally {
      setLoading(false);
    }
  };

  const handleLogout = () => {
    console.log('UpdatePasswordPage: 執行登出');
    // 使用 AuthContext 的 logout 方法
    authLogout();
    // 延遲導航，確保狀態清除完成
    setTimeout(() => {
      console.log('UpdatePasswordPage: 導航到登入頁面');
      navigate('/login', { replace: true });
    }, 100);
  };

  return (
    <div className="update-password-page">
      <Toast ref={toast} />
      
      <div className="flex align-items-center justify-content-center min-h-screen">
        <div className="surface-card p-4 shadow-2 border-round w-full lg:w-4">
          <div className="text-center mb-5">
            <div className="text-900 text-3xl font-medium mb-3">更新密碼</div>
            <span className="text-600 font-medium line-height-3">
              為了您的帳號安全，請更新您的密碼
            </span>
          </div>

          <div>
            <div className="field mb-3">
              <label htmlFor="oldPassword" className="block text-900 font-medium mb-2">
                舊密碼 <span className="text-red-500">*</span>
              </label>
              <Password
                id="oldPassword"
                value={formData.oldPassword}
                onChange={(e) => handleInputChange('oldPassword', e.target.value)}
                placeholder="請輸入舊密碼"
                className={`w-full ${errors.oldPassword ? 'p-invalid' : ''}`}
                feedback={false}
                toggleMask
              />
              {errors.oldPassword && (
                <small className="p-error">{errors.oldPassword}</small>
              )}
            </div>

            <div className="field mb-3">
              <label htmlFor="newPassword" className="block text-900 font-medium mb-2">
                新密碼 <span className="text-red-500">*</span>
              </label>
              <Password
                id="newPassword"
                value={formData.newPassword}
                onChange={(e) => handleInputChange('newPassword', e.target.value)}
                placeholder="請輸入新密碼（至少6個字符）"
                className={`w-full ${errors.newPassword ? 'p-invalid' : ''}`}
                toggleMask
              />
              {errors.newPassword && (
                <small className="p-error">{errors.newPassword}</small>
              )}
            </div>

            <div className="field mb-3">
              <label htmlFor="confirmPassword" className="block text-900 font-medium mb-2">
                確認新密碼 <span className="text-red-500">*</span>
              </label>
              <Password
                id="confirmPassword"
                value={formData.confirmPassword}
                onChange={(e) => handleInputChange('confirmPassword', e.target.value)}
                placeholder="請再次輸入新密碼"
                className={`w-full ${errors.confirmPassword ? 'p-invalid' : ''}`}
                feedback={false}
                toggleMask
              />
              {errors.confirmPassword && (
                <small className="p-error">{errors.confirmPassword}</small>
              )}
            </div>

            <div className="flex gap-2 mt-4">
              <Button
                label="更新密碼"
                icon="pi pi-check"
                className="w-full"
                onClick={handleSubmit}
                loading={loading}
              />
              <Button
                label="登出"
                icon="pi pi-sign-out"
                className="w-full p-button-outlined"
                onClick={handleLogout}
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default UpdatePasswordPage;

{"ast": null, "code": "import { formatDistance } from \"./mn/_lib/formatDistance.js\";\nimport { formatLong } from \"./mn/_lib/formatLong.js\";\nimport { formatRelative } from \"./mn/_lib/formatRelative.js\";\nimport { localize } from \"./mn/_lib/localize.js\";\nimport { match } from \"./mn/_lib/match.js\";\n\n/**\n * @category Locales\n * @summary Mongolian locale.\n * @language Mongolian\n * @iso-639-2 mon\n * <AUTHOR> [@bilguun0203](https://github.com/bilguun0203)\n */\nexport const mn = {\n  code: \"mn\",\n  formatDistance: formatDistance,\n  formatLong: formatLong,\n  formatRelative: formatRelative,\n  localize: localize,\n  match: match,\n  options: {\n    weekStartsOn: 1 /* Monday */,\n    firstWeekContainsDate: 1\n  }\n};\n\n// Fallback for modularized imports:\nexport default mn;", "map": {"version": 3, "names": ["formatDistance", "formatLong", "formatRelative", "localize", "match", "mn", "code", "options", "weekStartsOn", "firstWeekContainsDate"], "sources": ["C:/Users/<USER>/Desktop/demo-git/demo-react/my-fullstack-app/frontend/node_modules/date-fns/locale/mn.js"], "sourcesContent": ["import { formatDistance } from \"./mn/_lib/formatDistance.js\";\nimport { formatLong } from \"./mn/_lib/formatLong.js\";\nimport { formatRelative } from \"./mn/_lib/formatRelative.js\";\nimport { localize } from \"./mn/_lib/localize.js\";\nimport { match } from \"./mn/_lib/match.js\";\n\n/**\n * @category Locales\n * @summary Mongolian locale.\n * @language Mongolian\n * @iso-639-2 mon\n * <AUTHOR> [@bilguun0203](https://github.com/bilguun0203)\n */\nexport const mn = {\n  code: \"mn\",\n  formatDistance: formatDistance,\n  formatLong: formatLong,\n  formatRelative: formatRelative,\n  localize: localize,\n  match: match,\n  options: {\n    weekStartsOn: 1 /* Monday */,\n    firstWeekContainsDate: 1,\n  },\n};\n\n// Fallback for modularized imports:\nexport default mn;\n"], "mappings": "AAAA,SAASA,cAAc,QAAQ,6BAA6B;AAC5D,SAASC,UAAU,QAAQ,yBAAyB;AACpD,SAASC,cAAc,QAAQ,6BAA6B;AAC5D,SAASC,QAAQ,QAAQ,uBAAuB;AAChD,SAASC,KAAK,QAAQ,oBAAoB;;AAE1C;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMC,EAAE,GAAG;EAChBC,IAAI,EAAE,IAAI;EACVN,cAAc,EAAEA,cAAc;EAC9BC,UAAU,EAAEA,UAAU;EACtBC,cAAc,EAAEA,cAAc;EAC9BC,QAAQ,EAAEA,QAAQ;EAClBC,KAAK,EAAEA,KAAK;EACZG,OAAO,EAAE;IACPC,YAAY,EAAE,CAAC,CAAC;IAChBC,qBAAqB,EAAE;EACzB;AACF,CAAC;;AAED;AACA,eAAeJ,EAAE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "/**\n * Application route constants\n */\n\nexport const ROUTES = {\n  // Auth routes\n  LOGIN: '/login',\n  UPDATE_PASSWORD: '/update-password',\n  // Main routes\n  HOME: '/',\n  // Doctor routes\n  DOCTORS: '/doctors',\n  DOCTOR_DETAIL: '/doctordetail',\n  // Treatment routes\n  TREATMENTS: '/treatments',\n  TREATMENT_DETAIL: '/treatmentsdetail',\n  // Patient routes\n  PATIENTS: '/patients',\n  PATIENT_DETAIL: '/patientsdetail',\n  // Schedule routes\n  SCHEDULES: '/schedules',\n  // Receipt routes\n  RECEIPTS: '/receipts',\n  RECEIPT_DETAIL: '/receiptsdetail',\n  // User routes\n  USERS: '/userrole-management',\n  // Backup routes\n  BACKUP_MANAGEMENT: '/backup-management',\n  // Report routes\n  REPORT_MANAGEMENT: '/report-management',\n  // Image routes\n  IMAGE_MANAGEMENT: '/image-management',\n  // Login Security routes\n  LOGIN_LOGS: '/login-logs',\n  IP_BLOCKS: '/ip-blocks',\n  // Debug routes\n  DEBUG: '/debug',\n  // Permission routes\n  PERMISSION_MANAGEMENT: '/permission-management'\n};\n\n/**\n * Route metadata for navigation and permissions\n */\n\nexport const ROUTE_METADATA = {\n  [ROUTES.LOGIN]: {\n    path: ROUTES.LOGIN,\n    title: 'Login',\n    requiresAuth: false\n  },\n  [ROUTES.UPDATE_PASSWORD]: {\n    path: ROUTES.UPDATE_PASSWORD,\n    title: 'Update Password',\n    requiresAuth: true\n  },\n  [ROUTES.HOME]: {\n    path: ROUTES.HOME,\n    title: 'Home',\n    requiresAuth: true,\n    icon: 'pi pi-home'\n  },\n  [ROUTES.DOCTORS]: {\n    path: ROUTES.DOCTORS,\n    title: 'Doctors',\n    requiresAuth: true,\n    icon: 'pi pi-users'\n  },\n  [ROUTES.DOCTOR_DETAIL]: {\n    path: ROUTES.DOCTOR_DETAIL,\n    title: 'Doctor Detail',\n    requiresAuth: true\n  },\n  [ROUTES.TREATMENTS]: {\n    path: ROUTES.TREATMENTS,\n    title: 'Treatments',\n    requiresAuth: true,\n    icon: 'pi pi-heart'\n  },\n  [ROUTES.TREATMENT_DETAIL]: {\n    path: ROUTES.TREATMENT_DETAIL,\n    title: 'Treatment Detail',\n    requiresAuth: true\n  },\n  [ROUTES.PATIENTS]: {\n    path: ROUTES.PATIENTS,\n    title: 'Patients',\n    requiresAuth: true,\n    icon: 'pi pi-user'\n  },\n  [ROUTES.PATIENT_DETAIL]: {\n    path: ROUTES.PATIENT_DETAIL,\n    title: 'Patient Detail',\n    requiresAuth: true\n  },\n  [ROUTES.SCHEDULES]: {\n    path: ROUTES.SCHEDULES,\n    title: 'Schedules',\n    requiresAuth: true,\n    icon: 'pi pi-calendar'\n  },\n  [ROUTES.RECEIPTS]: {\n    path: ROUTES.RECEIPTS,\n    title: 'Receipts',\n    requiresAuth: true,\n    icon: 'pi pi-file'\n  },\n  [ROUTES.RECEIPT_DETAIL]: {\n    path: ROUTES.RECEIPT_DETAIL,\n    title: 'Receipt Detail',\n    requiresAuth: true\n  },\n  [ROUTES.USERS]: {\n    path: ROUTES.USERS,\n    title: 'User Role Management',\n    requiresAuth: true,\n    icon: 'pi pi-users'\n  },\n  [ROUTES.BACKUP_MANAGEMENT]: {\n    path: ROUTES.BACKUP_MANAGEMENT,\n    title: 'Backup Management',\n    requiresAuth: true,\n    permissions: ['Admin'],\n    icon: 'pi pi-database'\n  },\n  [ROUTES.REPORT_MANAGEMENT]: {\n    path: ROUTES.REPORT_MANAGEMENT,\n    title: 'Report Management',\n    requiresAuth: true,\n    permissions: ['Admin', 'Manager'],\n    icon: 'pi pi-file-pdf'\n  },\n  [ROUTES.IMAGE_MANAGEMENT]: {\n    path: ROUTES.IMAGE_MANAGEMENT,\n    title: 'Image Management',\n    requiresAuth: true,\n    permissions: ['Admin', 'Manager'],\n    icon: 'pi pi-images'\n  },\n  [ROUTES.LOGIN_LOGS]: {\n    path: ROUTES.LOGIN_LOGS,\n    title: 'Login Logs',\n    requiresAuth: true,\n    permissions: ['Admin', 'Manager'],\n    icon: 'pi pi-history'\n  },\n  [ROUTES.IP_BLOCKS]: {\n    path: ROUTES.IP_BLOCKS,\n    title: 'IP Blocks',\n    requiresAuth: true,\n    permissions: ['Admin'],\n    icon: 'pi pi-ban'\n  },\n  [ROUTES.DEBUG]: {\n    path: ROUTES.DEBUG,\n    title: 'Debug',\n    requiresAuth: true,\n    icon: 'pi pi-cog'\n  },\n  [ROUTES.PERMISSION_MANAGEMENT]: {\n    path: ROUTES.PERMISSION_MANAGEMENT,\n    title: 'Permission Management',\n    requiresAuth: true,\n    permissions: ['Admin'],\n    icon: 'pi pi-lock'\n  }\n};\n\n/**\n * Get route metadata by path\n */\nexport const getRouteMetadata = path => {\n  return ROUTE_METADATA[path];\n};\n\n/**\n * Check if route requires authentication\n */\nexport const isProtectedRoute = path => {\n  var _metadata$requiresAut;\n  const metadata = getRouteMetadata(path);\n  return (_metadata$requiresAut = metadata === null || metadata === void 0 ? void 0 : metadata.requiresAuth) !== null && _metadata$requiresAut !== void 0 ? _metadata$requiresAut : true;\n};", "map": {"version": 3, "names": ["ROUTES", "LOGIN", "UPDATE_PASSWORD", "HOME", "DOCTORS", "DOCTOR_DETAIL", "TREATMENTS", "TREATMENT_DETAIL", "PATIENTS", "PATIENT_DETAIL", "SCHEDULES", "RECEIPTS", "RECEIPT_DETAIL", "USERS", "BACKUP_MANAGEMENT", "REPORT_MANAGEMENT", "IMAGE_MANAGEMENT", "LOGIN_LOGS", "IP_BLOCKS", "DEBUG", "PERMISSION_MANAGEMENT", "ROUTE_METADATA", "path", "title", "requiresAuth", "icon", "permissions", "getRouteMetadata", "isProtectedRoute", "_metadata$requiresAut", "metadata"], "sources": ["C:/Users/<USER>/Desktop/demo-git/demo-react/my-fullstack-app/frontend/src/constants/routes.ts"], "sourcesContent": ["/**\n * Application route constants\n */\n\nexport const ROUTES = {\n  // Auth routes\n  LOGIN: '/login',\n  UPDATE_PASSWORD: '/update-password',\n  \n  // Main routes\n  HOME: '/',\n  \n  // Doctor routes\n  DOCTORS: '/doctors',\n  DOCTOR_DETAIL: '/doctordetail',\n  \n  // Treatment routes\n  TREATMENTS: '/treatments',\n  TREATMENT_DETAIL: '/treatmentsdetail',\n  \n  // Patient routes\n  PATIENTS: '/patients',\n  PATIENT_DETAIL: '/patientsdetail',\n  \n  // Schedule routes\n  SCHEDULES: '/schedules',\n  \n  // Receipt routes\n  RECEIPTS: '/receipts',\n  RECEIPT_DETAIL: '/receiptsdetail',\n  \n  // User routes\n  USERS: '/userrole-management',\n\n  // Backup routes\n  BACKUP_MANAGEMENT: '/backup-management',\n\n  // Report routes\n  REPORT_MANAGEMENT: '/report-management',\n\n  // Image routes\n  IMAGE_MANAGEMENT: '/image-management',\n\n  // Login Security routes\n  LOGIN_LOGS: '/login-logs',\n  IP_BLOCKS: '/ip-blocks',\n\n  // Debug routes\n  DEBUG: '/debug',\n\n  // Permission routes\n  PERMISSION_MANAGEMENT: '/permission-management',\n} as const;\n\nexport type RouteKey = keyof typeof ROUTES;\nexport type RoutePath = typeof ROUTES[RouteKey];\n\n/**\n * Route metadata for navigation and permissions\n */\nexport interface RouteMetadata {\n  path: RoutePath;\n  title: string;\n  requiresAuth: boolean;\n  permissions?: string[];\n  icon?: string;\n}\n\nexport const ROUTE_METADATA: Record<RoutePath, RouteMetadata> = {\n  [ROUTES.LOGIN]: {\n    path: ROUTES.LOGIN,\n    title: 'Login',\n    requiresAuth: false,\n  },\n  [ROUTES.UPDATE_PASSWORD]: {\n    path: ROUTES.UPDATE_PASSWORD,\n    title: 'Update Password',\n    requiresAuth: true,\n  },\n  [ROUTES.HOME]: {\n    path: ROUTES.HOME,\n    title: 'Home',\n    requiresAuth: true,\n    icon: 'pi pi-home',\n  },\n  [ROUTES.DOCTORS]: {\n    path: ROUTES.DOCTORS,\n    title: 'Doctors',\n    requiresAuth: true,\n    icon: 'pi pi-users',\n  },\n  [ROUTES.DOCTOR_DETAIL]: {\n    path: ROUTES.DOCTOR_DETAIL,\n    title: 'Doctor Detail',\n    requiresAuth: true,\n  },\n  [ROUTES.TREATMENTS]: {\n    path: ROUTES.TREATMENTS,\n    title: 'Treatments',\n    requiresAuth: true,\n    icon: 'pi pi-heart',\n  },\n  [ROUTES.TREATMENT_DETAIL]: {\n    path: ROUTES.TREATMENT_DETAIL,\n    title: 'Treatment Detail',\n    requiresAuth: true,\n  },\n  [ROUTES.PATIENTS]: {\n    path: ROUTES.PATIENTS,\n    title: 'Patients',\n    requiresAuth: true,\n    icon: 'pi pi-user',\n  },\n  [ROUTES.PATIENT_DETAIL]: {\n    path: ROUTES.PATIENT_DETAIL,\n    title: 'Patient Detail',\n    requiresAuth: true,\n  },\n  [ROUTES.SCHEDULES]: {\n    path: ROUTES.SCHEDULES,\n    title: 'Schedules',\n    requiresAuth: true,\n    icon: 'pi pi-calendar',\n  },\n  [ROUTES.RECEIPTS]: {\n    path: ROUTES.RECEIPTS,\n    title: 'Receipts',\n    requiresAuth: true,\n    icon: 'pi pi-file',\n  },\n  [ROUTES.RECEIPT_DETAIL]: {\n    path: ROUTES.RECEIPT_DETAIL,\n    title: 'Receipt Detail',\n    requiresAuth: true,\n  },\n  [ROUTES.USERS]: {\n    path: ROUTES.USERS,\n    title: 'User Role Management',\n    requiresAuth: true,\n    icon: 'pi pi-users',\n  },\n  [ROUTES.BACKUP_MANAGEMENT]: {\n    path: ROUTES.BACKUP_MANAGEMENT,\n    title: 'Backup Management',\n    requiresAuth: true,\n    permissions: ['Admin'],\n    icon: 'pi pi-database',\n  },\n  [ROUTES.REPORT_MANAGEMENT]: {\n    path: ROUTES.REPORT_MANAGEMENT,\n    title: 'Report Management',\n    requiresAuth: true,\n    permissions: ['Admin', 'Manager'],\n    icon: 'pi pi-file-pdf',\n  },\n  [ROUTES.IMAGE_MANAGEMENT]: {\n    path: ROUTES.IMAGE_MANAGEMENT,\n    title: 'Image Management',\n    requiresAuth: true,\n    permissions: ['Admin', 'Manager'],\n    icon: 'pi pi-images',\n  },\n  [ROUTES.LOGIN_LOGS]: {\n    path: ROUTES.LOGIN_LOGS,\n    title: 'Login Logs',\n    requiresAuth: true,\n    permissions: ['Admin', 'Manager'],\n    icon: 'pi pi-history',\n  },\n  [ROUTES.IP_BLOCKS]: {\n    path: ROUTES.IP_BLOCKS,\n    title: 'IP Blocks',\n    requiresAuth: true,\n    permissions: ['Admin'],\n    icon: 'pi pi-ban',\n  },\n  [ROUTES.DEBUG]: {\n    path: ROUTES.DEBUG,\n    title: 'Debug',\n    requiresAuth: true,\n    icon: 'pi pi-cog',\n  },\n  [ROUTES.PERMISSION_MANAGEMENT]: {\n    path: ROUTES.PERMISSION_MANAGEMENT,\n    title: 'Permission Management',\n    requiresAuth: true,\n    permissions: ['Admin'],\n    icon: 'pi pi-lock',\n  },\n};\n\n/**\n * Get route metadata by path\n */\nexport const getRouteMetadata = (path: string): RouteMetadata | undefined => {\n  return ROUTE_METADATA[path as RoutePath];\n};\n\n/**\n * Check if route requires authentication\n */\nexport const isProtectedRoute = (path: string): boolean => {\n  const metadata = getRouteMetadata(path);\n  return metadata?.requiresAuth ?? true;\n};\n"], "mappings": "AAAA;AACA;AACA;;AAEA,OAAO,MAAMA,MAAM,GAAG;EACpB;EACAC,KAAK,EAAE,QAAQ;EACfC,eAAe,EAAE,kBAAkB;EAEnC;EACAC,IAAI,EAAE,GAAG;EAET;EACAC,OAAO,EAAE,UAAU;EACnBC,aAAa,EAAE,eAAe;EAE9B;EACAC,UAAU,EAAE,aAAa;EACzBC,gBAAgB,EAAE,mBAAmB;EAErC;EACAC,QAAQ,EAAE,WAAW;EACrBC,cAAc,EAAE,iBAAiB;EAEjC;EACAC,SAAS,EAAE,YAAY;EAEvB;EACAC,QAAQ,EAAE,WAAW;EACrBC,cAAc,EAAE,iBAAiB;EAEjC;EACAC,KAAK,EAAE,sBAAsB;EAE7B;EACAC,iBAAiB,EAAE,oBAAoB;EAEvC;EACAC,iBAAiB,EAAE,oBAAoB;EAEvC;EACAC,gBAAgB,EAAE,mBAAmB;EAErC;EACAC,UAAU,EAAE,aAAa;EACzBC,SAAS,EAAE,YAAY;EAEvB;EACAC,KAAK,EAAE,QAAQ;EAEf;EACAC,qBAAqB,EAAE;AACzB,CAAU;;AAKV;AACA;AACA;;AASA,OAAO,MAAMC,cAAgD,GAAG;EAC9D,CAACrB,MAAM,CAACC,KAAK,GAAG;IACdqB,IAAI,EAAEtB,MAAM,CAACC,KAAK;IAClBsB,KAAK,EAAE,OAAO;IACdC,YAAY,EAAE;EAChB,CAAC;EACD,CAACxB,MAAM,CAACE,eAAe,GAAG;IACxBoB,IAAI,EAAEtB,MAAM,CAACE,eAAe;IAC5BqB,KAAK,EAAE,iBAAiB;IACxBC,YAAY,EAAE;EAChB,CAAC;EACD,CAACxB,MAAM,CAACG,IAAI,GAAG;IACbmB,IAAI,EAAEtB,MAAM,CAACG,IAAI;IACjBoB,KAAK,EAAE,MAAM;IACbC,YAAY,EAAE,IAAI;IAClBC,IAAI,EAAE;EACR,CAAC;EACD,CAACzB,MAAM,CAACI,OAAO,GAAG;IAChBkB,IAAI,EAAEtB,MAAM,CAACI,OAAO;IACpBmB,KAAK,EAAE,SAAS;IAChBC,YAAY,EAAE,IAAI;IAClBC,IAAI,EAAE;EACR,CAAC;EACD,CAACzB,MAAM,CAACK,aAAa,GAAG;IACtBiB,IAAI,EAAEtB,MAAM,CAACK,aAAa;IAC1BkB,KAAK,EAAE,eAAe;IACtBC,YAAY,EAAE;EAChB,CAAC;EACD,CAACxB,MAAM,CAACM,UAAU,GAAG;IACnBgB,IAAI,EAAEtB,MAAM,CAACM,UAAU;IACvBiB,KAAK,EAAE,YAAY;IACnBC,YAAY,EAAE,IAAI;IAClBC,IAAI,EAAE;EACR,CAAC;EACD,CAACzB,MAAM,CAACO,gBAAgB,GAAG;IACzBe,IAAI,EAAEtB,MAAM,CAACO,gBAAgB;IAC7BgB,KAAK,EAAE,kBAAkB;IACzBC,YAAY,EAAE;EAChB,CAAC;EACD,CAACxB,MAAM,CAACQ,QAAQ,GAAG;IACjBc,IAAI,EAAEtB,MAAM,CAACQ,QAAQ;IACrBe,KAAK,EAAE,UAAU;IACjBC,YAAY,EAAE,IAAI;IAClBC,IAAI,EAAE;EACR,CAAC;EACD,CAACzB,MAAM,CAACS,cAAc,GAAG;IACvBa,IAAI,EAAEtB,MAAM,CAACS,cAAc;IAC3Bc,KAAK,EAAE,gBAAgB;IACvBC,YAAY,EAAE;EAChB,CAAC;EACD,CAACxB,MAAM,CAACU,SAAS,GAAG;IAClBY,IAAI,EAAEtB,MAAM,CAACU,SAAS;IACtBa,KAAK,EAAE,WAAW;IAClBC,YAAY,EAAE,IAAI;IAClBC,IAAI,EAAE;EACR,CAAC;EACD,CAACzB,MAAM,CAACW,QAAQ,GAAG;IACjBW,IAAI,EAAEtB,MAAM,CAACW,QAAQ;IACrBY,KAAK,EAAE,UAAU;IACjBC,YAAY,EAAE,IAAI;IAClBC,IAAI,EAAE;EACR,CAAC;EACD,CAACzB,MAAM,CAACY,cAAc,GAAG;IACvBU,IAAI,EAAEtB,MAAM,CAACY,cAAc;IAC3BW,KAAK,EAAE,gBAAgB;IACvBC,YAAY,EAAE;EAChB,CAAC;EACD,CAACxB,MAAM,CAACa,KAAK,GAAG;IACdS,IAAI,EAAEtB,MAAM,CAACa,KAAK;IAClBU,KAAK,EAAE,sBAAsB;IAC7BC,YAAY,EAAE,IAAI;IAClBC,IAAI,EAAE;EACR,CAAC;EACD,CAACzB,MAAM,CAACc,iBAAiB,GAAG;IAC1BQ,IAAI,EAAEtB,MAAM,CAACc,iBAAiB;IAC9BS,KAAK,EAAE,mBAAmB;IAC1BC,YAAY,EAAE,IAAI;IAClBE,WAAW,EAAE,CAAC,OAAO,CAAC;IACtBD,IAAI,EAAE;EACR,CAAC;EACD,CAACzB,MAAM,CAACe,iBAAiB,GAAG;IAC1BO,IAAI,EAAEtB,MAAM,CAACe,iBAAiB;IAC9BQ,KAAK,EAAE,mBAAmB;IAC1BC,YAAY,EAAE,IAAI;IAClBE,WAAW,EAAE,CAAC,OAAO,EAAE,SAAS,CAAC;IACjCD,IAAI,EAAE;EACR,CAAC;EACD,CAACzB,MAAM,CAACgB,gBAAgB,GAAG;IACzBM,IAAI,EAAEtB,MAAM,CAACgB,gBAAgB;IAC7BO,KAAK,EAAE,kBAAkB;IACzBC,YAAY,EAAE,IAAI;IAClBE,WAAW,EAAE,CAAC,OAAO,EAAE,SAAS,CAAC;IACjCD,IAAI,EAAE;EACR,CAAC;EACD,CAACzB,MAAM,CAACiB,UAAU,GAAG;IACnBK,IAAI,EAAEtB,MAAM,CAACiB,UAAU;IACvBM,KAAK,EAAE,YAAY;IACnBC,YAAY,EAAE,IAAI;IAClBE,WAAW,EAAE,CAAC,OAAO,EAAE,SAAS,CAAC;IACjCD,IAAI,EAAE;EACR,CAAC;EACD,CAACzB,MAAM,CAACkB,SAAS,GAAG;IAClBI,IAAI,EAAEtB,MAAM,CAACkB,SAAS;IACtBK,KAAK,EAAE,WAAW;IAClBC,YAAY,EAAE,IAAI;IAClBE,WAAW,EAAE,CAAC,OAAO,CAAC;IACtBD,IAAI,EAAE;EACR,CAAC;EACD,CAACzB,MAAM,CAACmB,KAAK,GAAG;IACdG,IAAI,EAAEtB,MAAM,CAACmB,KAAK;IAClBI,KAAK,EAAE,OAAO;IACdC,YAAY,EAAE,IAAI;IAClBC,IAAI,EAAE;EACR,CAAC;EACD,CAACzB,MAAM,CAACoB,qBAAqB,GAAG;IAC9BE,IAAI,EAAEtB,MAAM,CAACoB,qBAAqB;IAClCG,KAAK,EAAE,uBAAuB;IAC9BC,YAAY,EAAE,IAAI;IAClBE,WAAW,EAAE,CAAC,OAAO,CAAC;IACtBD,IAAI,EAAE;EACR;AACF,CAAC;;AAED;AACA;AACA;AACA,OAAO,MAAME,gBAAgB,GAAIL,IAAY,IAAgC;EAC3E,OAAOD,cAAc,CAACC,IAAI,CAAc;AAC1C,CAAC;;AAED;AACA;AACA;AACA,OAAO,MAAMM,gBAAgB,GAAIN,IAAY,IAAc;EAAA,IAAAO,qBAAA;EACzD,MAAMC,QAAQ,GAAGH,gBAAgB,CAACL,IAAI,CAAC;EACvC,QAAAO,qBAAA,GAAOC,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEN,YAAY,cAAAK,qBAAA,cAAAA,qBAAA,GAAI,IAAI;AACvC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
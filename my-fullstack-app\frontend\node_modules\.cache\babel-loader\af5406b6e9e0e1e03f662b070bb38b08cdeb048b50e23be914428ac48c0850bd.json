{"ast": null, "code": "import{Button}from'primereact/button';import{Column}from'primereact/column';import{DataTable}from'primereact/datatable';import{Dialog}from'primereact/dialog';import{Dropdown}from'primereact/dropdown';import{InputText}from'primereact/inputtext';import{Toast}from'primereact/toast';import{Tag}from'primereact/tag';import{MultiSelect}from'primereact/multiselect';import{Card}from'primereact/card';import{ProgressSpinner}from'primereact/progressspinner';import React,{useRef,useState,useEffect}from'react';import api from'../../services/api';import{log}from'../../utils/logger';import{usePermissions}from'../../hooks/usePermissions';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const UsersPage=()=>{const toast=useRef(null);const[users,setUsers]=useState([]);const[roles,setRoles]=useState([]);const[loading,setLoading]=useState(true);const[searchName,setSearchName]=useState('');const[selectedRoleFilter,setSelectedRoleFilter]=useState(null);const[showEditDialog,setShowEditDialog]=useState(false);const[editingUser,setEditingUser]=useState(null);const[selectedRoles,setSelectedRoles]=useState([]);const[refreshing,setRefreshing]=useState(false);const{hasPermission}=usePermissions();// 載入用戶列表\nconst loadUsers=async()=>{try{setRefreshing(true);log.api('載入用戶列表',{searchName,selectedRoleFilter});const response=await api.get('/api/users/GetUserRolesList',{params:{name:searchName}});let filteredUsers=response.data;// 如果選擇了角色篩選，則進行前端篩選\nif(selectedRoleFilter){filteredUsers=response.data.filter(user=>user.roles&&user.roles.some(role=>role.roleId===selectedRoleFilter));}setUsers(filteredUsers);log.api('用戶列表載入成功',{total:response.data.length,filtered:filteredUsers.length,roleFilter:selectedRoleFilter});}catch(error){var _toast$current;log.error('載入用戶列表失敗',error);(_toast$current=toast.current)===null||_toast$current===void 0?void 0:_toast$current.show({severity:'error',summary:'載入失敗',detail:'無法載入用戶列表',life:5000});}finally{setLoading(false);setRefreshing(false);}};// 載入角色列表\nconst loadRoles=async()=>{try{log.api('載入角色列表');const response=await api.get('/api/users/GetRoles');setRoles(response.data);log.api('角色列表載入成功',{count:response.data.length});}catch(error){var _toast$current2;log.error('載入角色列表失敗',error);(_toast$current2=toast.current)===null||_toast$current2===void 0?void 0:_toast$current2.show({severity:'error',summary:'載入失敗',detail:'無法載入角色列表',life:5000});}};// 打開編輯對話框\nconst handleEditUserRoles=async user=>{try{log.api('載入用戶角色',{userId:user.userId});const response=await api.get(\"/api/users/GetUserRoles/\".concat(user.userId));const userData=response.data;setEditingUser({userId:userData.userId,userName:userData.userName,userAccount:userData.userAccount,roles:userData.roles||[]});// 安全檢查：確保 Roles 存在且為陣列\nconst roles=userData.roles||[];setSelectedRoles(roles.map(role=>role.roleId));setShowEditDialog(true);log.api('用戶角色載入成功',userData);}catch(error){var _toast$current3;log.error('載入用戶角色失敗',error);(_toast$current3=toast.current)===null||_toast$current3===void 0?void 0:_toast$current3.show({severity:'error',summary:'載入失敗',detail:'無法載入用戶角色資訊',life:5000});}};// 保存用戶角色\nconst handleSaveUserRoles=async()=>{if(!editingUser)return;try{var _toast$current4;log.api('更新用戶角色',{userId:editingUser.userId,roleIds:selectedRoles});await api.put('/api/users/UpdateUserRoles',{UserId:editingUser.userId,RoleIds:selectedRoles});(_toast$current4=toast.current)===null||_toast$current4===void 0?void 0:_toast$current4.show({severity:'success',summary:'更新成功',detail:'用戶角色權限已更新',life:3000});setShowEditDialog(false);loadUsers();// 重新載入用戶列表\nlog.api('用戶角色更新成功');}catch(error){var _toast$current5;log.error('更新用戶角色失敗',error);(_toast$current5=toast.current)===null||_toast$current5===void 0?void 0:_toast$current5.show({severity:'error',summary:'更新失敗',detail:'無法更新用戶角色權限',life:5000});}};// 初始化載入\nuseEffect(()=>{loadUsers();loadRoles();// eslint-disable-next-line react-hooks/exhaustive-deps\n},[]);// 監聽角色篩選變化\nuseEffect(()=>{if(roles.length>0){loadUsers();}// eslint-disable-next-line react-hooks/exhaustive-deps\n},[selectedRoleFilter]);// 搜索處理\nconst handleSearch=()=>{loadUsers();};// 重置緩存\nconst resetMemCache=async()=>{try{var _toast$current6;await api.get('/api/system/ResetMemCache');(_toast$current6=toast.current)===null||_toast$current6===void 0?void 0:_toast$current6.show({severity:'success',summary:'重置成功',detail:'緩存已重置',life:3000});}catch(error){var _toast$current7;log.error('重置緩存失敗',error);(_toast$current7=toast.current)===null||_toast$current7===void 0?void 0:_toast$current7.show({severity:'error',summary:'重置失敗',detail:'無法重置緩存',life:5000});}};// 角色標籤模板\nconst rolesBodyTemplate=rowData=>{// 安全檢查：確保 Roles 存在且為陣列\nif(!rowData.roles||!Array.isArray(rowData.roles)||rowData.roles.length===0){return/*#__PURE__*/_jsx(Tag,{value:\"\\u7121\\u89D2\\u8272\",severity:\"warning\",className:\"text-sm\"});}return/*#__PURE__*/_jsx(\"div\",{className:\"flex flex-wrap gap-1\",children:rowData.roles.map(role=>/*#__PURE__*/_jsx(Tag,{value:role.roleName,severity:\"info\",className:\"text-sm\"},role.roleId))});};// 操作按鈕模板\nconst actionBodyTemplate=rowData=>{return/*#__PURE__*/_jsx(\"div\",{className:\"flex gap-2\",children:hasPermission('userroles.write')&&/*#__PURE__*/_jsx(Button,{icon:\"pi pi-pencil\",className:\"p-button-success\",size:\"small\",onClick:()=>handleEditUserRoles(rowData),label:\"\\u7DE8\\u8F2F\"})});};// 狀態模板\nconst statusBodyTemplate=rowData=>{return/*#__PURE__*/_jsx(Tag,{value:rowData.isEnabled?'啟用':'停用',severity:rowData.isEnabled?'success':'danger'});};// 可選角色選項\nconst availableRoleOptions=roles.map(role=>({label:role.Name,value:role.Id}));const paginatorLeft=/*#__PURE__*/_jsx(Button,{type:\"button\",icon:\"pi pi-refresh\",text:true,onClick:()=>loadUsers()});const paginatorRight=/*#__PURE__*/_jsx(\"div\",{});if(loading){return/*#__PURE__*/_jsx(\"div\",{className:\"flex align-items-center justify-content-center min-h-screen\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"text-center\",children:[/*#__PURE__*/_jsx(ProgressSpinner,{}),/*#__PURE__*/_jsx(\"p\",{className:\"mt-3\",children:\"\\u8F09\\u5165\\u7528\\u6236\\u8CC7\\u6599\\u4E2D...\"})]})});}return/*#__PURE__*/_jsxs(\"div\",{className:\"users-page\",children:[/*#__PURE__*/_jsx(Toast,{ref:toast}),/*#__PURE__*/_jsx(Card,{title:\"\\u7528\\u6236\\u6B0A\\u9650\\u7BA1\\u7406\",className:\"mb-4\",children:/*#__PURE__*/_jsx(\"p\",{className:\"text-600 line-height-3 m-0\",children:\"\\u7BA1\\u7406\\u7CFB\\u7D71\\u7684\\u7528\\u6236\\u6B0A\\u9650\\uFF0C\\u5305\\u62EC\\u5206\\u914D\\u89D2\\u8272\\u548C\\u67E5\\u770B\\u7528\\u6236\\u8A73\\u7D30\\u4FE1\\u606F\\u3002\\u60A8\\u53EF\\u4EE5\\u7DE8\\u8F2F\\u7528\\u6236\\u7684\\u89D2\\u8272\\u6B0A\\u9650\\u4EE5\\u63A7\\u5236\\u5176\\u5728\\u7CFB\\u7D71\\u4E2D\\u7684\\u8A2A\\u554F\\u548C\\u64CD\\u4F5C\\u3002\"})}),/*#__PURE__*/_jsx(Card,{className:\"mb-4\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"grid\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"col-6 md:col-4\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"p-inputgroup\",children:[/*#__PURE__*/_jsx(InputText,{placeholder:\"\\u641C\\u5C0B\\u7528\\u6236\\u540D\\u7A31\",value:searchName,onChange:e=>setSearchName(e.target.value),onKeyDown:e=>e.key==='Enter'&&handleSearch()}),/*#__PURE__*/_jsx(Button,{icon:\"pi pi-search\",onClick:handleSearch,disabled:refreshing})]})}),/*#__PURE__*/_jsx(\"div\",{className:\"col-6 md:col-4\",children:/*#__PURE__*/_jsx(Dropdown,{value:selectedRoleFilter,options:[{label:'全部角色',value:null},...roles.map(role=>({label:role.Name,value:role.Id}))],onChange:e=>setSelectedRoleFilter(e.value),placeholder:\"\\u7BE9\\u9078\\u89D2\\u8272\",className:\"w-full\",showClear:true})}),/*#__PURE__*/_jsx(\"div\",{className:\"col-6 md:col-4 \",children:/*#__PURE__*/_jsx(\"div\",{className:\"flex gap-2\",children:/*#__PURE__*/_jsx(Button,{label:\"\\u7DE9\\u5B58\\u91CD\\u7F6E\",className:\"p-button-danger\",icon:\"pi pi-trash\",onClick:resetMemCache})})})]})}),/*#__PURE__*/_jsx(Card,{children:/*#__PURE__*/_jsxs(DataTable,{value:users,paginator:true,rows:10,rowsPerPageOptions:[5,10,25,50],sortMode:\"multiple\",removableSort:true,filterDisplay:\"menu\",globalFilterFields:['userName','userAccount','userEmail'],emptyMessage:\"\\u6C92\\u6709\\u627E\\u5230\\u7528\\u6236\\u8CC7\\u6599\",className:\"p-datatable-gridlines\",paginatorLeft:paginatorLeft,paginatorRight:paginatorRight,children:[/*#__PURE__*/_jsx(Column,{field:\"userName\",header:\"\\u7528\\u6236\\u540D\\u7A31\",sortable:true,filter:true,filterPlaceholder:\"\\u641C\\u5C0B\\u540D\\u7A31\",style:{minWidth:'150px'}}),/*#__PURE__*/_jsx(Column,{field:\"userAccount\",header:\"\\u5E33\\u865F\",sortable:true,filter:true,filterPlaceholder:\"\\u641C\\u5C0B\\u5E33\\u865F\",style:{minWidth:'120px'}}),/*#__PURE__*/_jsx(Column,{field:\"userEmail\",header:\"Email\",sortable:true,filter:true,filterPlaceholder:\"\\u641C\\u5C0BEmail\",style:{minWidth:'200px'}}),/*#__PURE__*/_jsx(Column,{header:\"\\u89D2\\u8272\\u6B0A\\u9650\",body:rolesBodyTemplate,style:{minWidth:'200px'}}),/*#__PURE__*/_jsx(Column,{field:\"isEnabled\",header:\"\\u72C0\\u614B\",body:statusBodyTemplate,sortable:true,style:{minWidth:'100px'}}),/*#__PURE__*/_jsx(Column,{header:\"\\u64CD\\u4F5C\",body:actionBodyTemplate,style:{minWidth:'100px'}})]})}),/*#__PURE__*/_jsx(Dialog,{header:\"\\u7DE8\\u8F2F\\u7528\\u6236\\u89D2\\u8272\\u6B0A\\u9650 - \".concat(editingUser===null||editingUser===void 0?void 0:editingUser.userName),visible:showEditDialog,style:{width:'500px'},onHide:()=>setShowEditDialog(false),footer:/*#__PURE__*/_jsxs(\"div\",{className:\"flex justify-content-end gap-2\",children:[/*#__PURE__*/_jsx(Button,{label:\"\\u53D6\\u6D88\",icon:\"pi pi-times\",onClick:()=>setShowEditDialog(false),className:\"p-button-text\"}),/*#__PURE__*/_jsx(Button,{label:\"\\u4FDD\\u5B58\",icon:\"pi pi-check\",onClick:handleSaveUserRoles,className:\"p-button-primary\"})]}),children:editingUser&&/*#__PURE__*/_jsxs(\"div\",{className:\"grid\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"col-12\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"field\",children:[/*#__PURE__*/_jsx(\"label\",{className:\"font-bold\",children:\"\\u7528\\u6236\\u5E33\\u865F:\"}),/*#__PURE__*/_jsx(\"p\",{className:\"m-0\",children:editingUser.userAccount})]})}),/*#__PURE__*/_jsx(\"div\",{className:\"col-12\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"field\",children:[/*#__PURE__*/_jsx(\"label\",{htmlFor:\"roles\",className:\"font-bold\",children:\"\\u89D2\\u8272\\u6B0A\\u9650:\"}),/*#__PURE__*/_jsx(MultiSelect,{id:\"roles\",value:selectedRoles,options:availableRoleOptions,onChange:e=>setSelectedRoles(e.value),placeholder:\"\\u9078\\u64C7\\u89D2\\u8272\\u6B0A\\u9650\",className:\"w-full\",display:\"chip\"})]})})]})})]});};export default UsersPage;", "map": {"version": 3, "names": ["<PERSON><PERSON>", "Column", "DataTable", "Dialog", "Dropdown", "InputText", "Toast", "Tag", "MultiSelect", "Card", "ProgressSpinner", "React", "useRef", "useState", "useEffect", "api", "log", "usePermissions", "jsx", "_jsx", "jsxs", "_jsxs", "UsersPage", "toast", "users", "setUsers", "roles", "setRoles", "loading", "setLoading", "searchName", "setSearchName", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "setSelectedRoleFilter", "showEditDialog", "setShowEditDialog", "editingUser", "setEditingUser", "selectedRoles", "setSelectedRoles", "refreshing", "setRefreshing", "hasPermission", "loadUsers", "response", "get", "params", "name", "filteredUsers", "data", "filter", "user", "some", "role", "roleId", "total", "length", "filtered", "<PERSON><PERSON><PERSON>er", "error", "_toast$current", "current", "show", "severity", "summary", "detail", "life", "loadRoles", "count", "_toast$current2", "handleEditUserRoles", "userId", "concat", "userData", "userName", "userAccount", "map", "_toast$current3", "handleSaveUserRoles", "_toast$current4", "roleIds", "put", "UserId", "RoleIds", "_toast$current5", "handleSearch", "resetMemCache", "_toast$current6", "_toast$current7", "rolesBodyTemplate", "rowData", "Array", "isArray", "value", "className", "children", "<PERSON><PERSON><PERSON>", "actionBodyTemplate", "icon", "size", "onClick", "label", "statusBodyTemplate", "isEnabled", "availableRoleOptions", "Name", "Id", "paginatorLeft", "type", "text", "paginatorRight", "ref", "title", "placeholder", "onChange", "e", "target", "onKeyDown", "key", "disabled", "options", "showClear", "paginator", "rows", "rowsPerPageOptions", "sortMode", "removableSort", "filterDisplay", "globalFilterFields", "emptyMessage", "field", "header", "sortable", "filterPlaceholder", "style", "min<PERSON><PERSON><PERSON>", "body", "visible", "width", "onHide", "footer", "htmlFor", "id", "display"], "sources": ["C:/Users/<USER>/Desktop/demo-git/demo-react/my-fullstack-app/frontend/src/components/Page/UsersPage.tsx"], "sourcesContent": ["import { But<PERSON> } from 'primereact/button';\nimport { Column } from 'primereact/column';\nimport { DataTable } from 'primereact/datatable';\nimport { Dialog } from 'primereact/dialog';\nimport { Dropdown } from 'primereact/dropdown';\nimport { InputText } from 'primereact/inputtext';\nimport { Toast } from 'primereact/toast';\nimport { Tag } from 'primereact/tag';\nimport { MultiSelect } from 'primereact/multiselect';\nimport { Card } from 'primereact/card';\nimport { ProgressSpinner } from 'primereact/progressspinner';\nimport React, { useRef, useState, useEffect } from 'react';\nimport api from '../../services/api';\nimport { log } from '../../utils/logger';\nimport { usePermissions } from '../../hooks/usePermissions';\n\ninterface Role {\n  Id: number;\n  Name: string;\n}\n\ninterface UserRole {\n  roleId: number;\n  roleName: string;\n}\n\ninterface User {\n  userId: number;\n  userName: string;\n  userAccount: string;\n  userEmail?: string;\n  userPhone?: string;\n  address?: string;\n  gender?: string;\n  birthDate?: string;\n  isEnabled: boolean;\n  createdAt: string;\n  updatedAt: string;\n  roles?: UserRole[];\n}\n\ninterface EditUserRolesData {\n  userId: number;\n  userName: string;\n  userAccount: string;\n  roles: UserRole[];\n}\n\nconst UsersPage: React.FC = () => {\n  const toast = useRef<Toast>(null);\n  const [users, setUsers] = useState<User[]>([]);\n  const [roles, setRoles] = useState<Role[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [searchName, setSearchName] = useState('');\n  const [selectedRoleFilter, setSelectedRoleFilter] = useState<number | null>(null);\n  const [showEditDialog, setShowEditDialog] = useState(false);\n  const [editingUser, setEditingUser] = useState<EditUserRolesData | null>(null);\n  const [selectedRoles, setSelectedRoles] = useState<number[]>([]);\n  const [refreshing, setRefreshing] = useState(false);\n  const { hasPermission } = usePermissions();\n\n  // 載入用戶列表\n  const loadUsers = async () => {\n    try {\n      setRefreshing(true);\n      log.api('載入用戶列表', { searchName, selectedRoleFilter });\n\n      const response = await api.get('/api/users/GetUserRolesList', {\n        params: { name: searchName }\n      });\n\n      let filteredUsers = response.data;\n\n      // 如果選擇了角色篩選，則進行前端篩選\n      if (selectedRoleFilter) {\n        filteredUsers = response.data.filter((user: User) =>\n          user.roles && user.roles.some((role: UserRole) => role.roleId === selectedRoleFilter)\n        );\n      }\n\n      setUsers(filteredUsers);\n      log.api('用戶列表載入成功', {\n        total: response.data.length,\n        filtered: filteredUsers.length,\n        roleFilter: selectedRoleFilter\n      });\n\n    } catch (error: any) {\n      log.error('載入用戶列表失敗', error);\n      toast.current?.show({\n        severity: 'error',\n        summary: '載入失敗',\n        detail: '無法載入用戶列表',\n        life: 5000\n      });\n    } finally {\n      setLoading(false);\n      setRefreshing(false);\n    }\n  };\n\n  // 載入角色列表\n  const loadRoles = async () => {\n    try {\n      log.api('載入角色列表');\n      \n      const response = await api.get('/api/users/GetRoles');\n      setRoles(response.data);\n      \n      log.api('角色列表載入成功', { count: response.data.length });\n      \n    } catch (error: any) {\n      log.error('載入角色列表失敗', error);\n      toast.current?.show({\n        severity: 'error',\n        summary: '載入失敗',\n        detail: '無法載入角色列表',\n        life: 5000\n      });\n    }\n  };\n\n  // 打開編輯對話框\n  const handleEditUserRoles = async (user: User) => {\n    try {\n      \n      log.api('載入用戶角色', { userId: user.userId });\n\n      const response = await api.get(`/api/users/GetUserRoles/${user.userId}`);\n      const userData = response.data;\n      \n      setEditingUser({\n        userId: userData.userId,\n        userName: userData.userName,\n        userAccount: userData.userAccount,\n        roles: userData.roles || []\n      });\n\n      // 安全檢查：確保 Roles 存在且為陣列\n      const roles = userData.roles || [];\n      setSelectedRoles(roles.map((role: UserRole) => role.roleId));\n      setShowEditDialog(true);\n      \n      log.api('用戶角色載入成功', userData);\n      \n    } catch (error: any) {\n      log.error('載入用戶角色失敗', error);\n      toast.current?.show({\n        severity: 'error',\n        summary: '載入失敗',\n        detail: '無法載入用戶角色資訊',\n        life: 5000\n      });\n    }\n  };\n\n  // 保存用戶角色\n  const handleSaveUserRoles = async () => {\n    if (!editingUser) return;\n    \n    try {\n      log.api('更新用戶角色', { \n        userId: editingUser.userId, \n        roleIds: selectedRoles \n      });\n      \n      await api.put('/api/users/UpdateUserRoles', {\n        UserId: editingUser.userId,\n        RoleIds: selectedRoles\n      });\n      \n      toast.current?.show({\n        severity: 'success',\n        summary: '更新成功',\n        detail: '用戶角色權限已更新',\n        life: 3000\n      });\n      \n      setShowEditDialog(false);\n      loadUsers(); // 重新載入用戶列表\n      \n      log.api('用戶角色更新成功');\n      \n    } catch (error: any) {\n      log.error('更新用戶角色失敗', error);\n      toast.current?.show({\n        severity: 'error',\n        summary: '更新失敗',\n        detail: '無法更新用戶角色權限',\n        life: 5000\n      });\n    }\n  };\n\n  // 初始化載入\n  useEffect(() => {\n    loadUsers();\n    loadRoles();\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, []);\n\n  // 監聽角色篩選變化\n  useEffect(() => {\n    if (roles.length > 0) {\n      loadUsers();\n    }\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [selectedRoleFilter]);\n\n  // 搜索處理\n  const handleSearch = () => {\n    loadUsers();\n  };\n\n    // 重置緩存\n  const resetMemCache = async () => {\n    try {\n      await api.get('/api/system/ResetMemCache');\n      toast.current?.show({\n        severity: 'success',\n        summary: '重置成功',\n        detail: '緩存已重置',\n        life: 3000\n      });\n    } catch (error: any) {\n      log.error('重置緩存失敗', error);\n      toast.current?.show({\n        severity: 'error',\n        summary: '重置失敗',\n        detail: '無法重置緩存',\n        life: 5000\n      });\n    } \n  };\n\n  // 角色標籤模板\n  const rolesBodyTemplate = (rowData: User) => {\n    // 安全檢查：確保 Roles 存在且為陣列\n    if (!rowData.roles || !Array.isArray(rowData.roles) || rowData.roles.length === 0) {\n      return (\n        <Tag\n          value=\"無角色\"\n          severity=\"warning\"\n          className=\"text-sm\"\n        />\n      );\n    }\n\n    return (\n      <div className=\"flex flex-wrap gap-1\">\n        {rowData.roles.map((role: UserRole) => (\n          <Tag\n            key={role.roleId}\n            value={role.roleName}\n            severity=\"info\"\n            className=\"text-sm\"\n          />\n        ))}\n      </div>\n    );\n  };\n\n  // 操作按鈕模板\n  const actionBodyTemplate = (rowData: User) => {\n    return (\n      <div className=\"flex gap-2\">\n        {hasPermission('userroles.write') && (\n          <Button\n            icon=\"pi pi-pencil\"\n            className=\"p-button-success\"\n            size=\"small\" \n            onClick={() => handleEditUserRoles(rowData)}\n            label=\"編輯\"\n          />\n        )}\n      </div>\n    );\n  };\n\n  // 狀態模板\n  const statusBodyTemplate = (rowData: User) => {\n    return (\n      <Tag \n        value={rowData.isEnabled ? '啟用' : '停用'} \n        severity={rowData.isEnabled ? 'success' : 'danger'}\n      />\n    );\n  };\n\n  // 可選角色選項\n  const availableRoleOptions = roles.map(role => ({\n    label: role.Name,\n    value: role.Id\n  }));\n\n  const paginatorLeft = (\n      <Button\n          type=\"button\"\n          icon=\"pi pi-refresh\"\n          text\n          onClick={() => loadUsers()}\n      />\n  );\n  const paginatorRight = <div></div>;\n\n  if (loading) {\n    return (\n      <div className=\"flex align-items-center justify-content-center min-h-screen\">\n        <div className=\"text-center\">\n          <ProgressSpinner />\n          <p className=\"mt-3\">載入用戶資料中...</p>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"users-page\">\n      <Toast ref={toast} />\n      \n      <Card title=\"用戶權限管理\" className=\"mb-4\">\n        <p className=\"text-600 line-height-3 m-0\">\n          管理系統的用戶權限，包括分配角色和查看用戶詳細信息。您可以編輯用戶的角色權限以控制其在系統中的訪問和操作。\n        </p>\n      </Card>\n      \n      {/* 搜尋條件 */}\n      <Card className=\"mb-4\">\n        <div className=\"grid\">\n          <div className=\"col-6 md:col-4\">\n            <div className=\"p-inputgroup\">\n              <InputText\n                placeholder=\"搜尋用戶名稱\"\n                value={searchName}\n                onChange={(e) => setSearchName(e.target.value)}\n                onKeyDown={(e) => e.key === 'Enter' && handleSearch()}\n              />\n              <Button\n                icon=\"pi pi-search\"\n                onClick={handleSearch}\n                disabled={refreshing}\n              />\n            </div>\n          </div>\n          <div className=\"col-6 md:col-4\">\n            <Dropdown\n              value={selectedRoleFilter}\n              options={[\n                { label: '全部角色', value: null },\n                ...roles.map(role => ({ label: role.Name, value: role.Id }))\n              ]}\n              onChange={(e) => setSelectedRoleFilter(e.value)}\n              placeholder=\"篩選角色\"\n              className=\"w-full\"\n              showClear\n            />\n          </div>\n          <div className=\"col-6 md:col-4 \">\n            <div className=\"flex gap-2\">\n              <Button\n                  label=\"緩存重置\"\n                  className=\"p-button-danger\"\n                  icon=\"pi pi-trash\"\n                  onClick={resetMemCache}\n                />\n            </div>\n          </div>\n        </div>\n      </Card>\n\n      {/* 用戶列表 */}\n      <Card>\n        <DataTable\n          value={users}\n          paginator\n          rows={10}\n          rowsPerPageOptions={[5, 10, 25, 50]}\n          sortMode=\"multiple\"\n          removableSort\n          filterDisplay=\"menu\"\n          globalFilterFields={['userName', 'userAccount', 'userEmail']}\n          emptyMessage=\"沒有找到用戶資料\"\n          className=\"p-datatable-gridlines\"\n          paginatorLeft={paginatorLeft}\n          paginatorRight={paginatorRight}\n        >\n          <Column\n            field=\"userName\"\n            header=\"用戶名稱\"\n            sortable\n            filter\n            filterPlaceholder=\"搜尋名稱\"\n            style={{ minWidth: '150px' }}\n          />\n          <Column\n            field=\"userAccount\"\n            header=\"帳號\"\n            sortable\n            filter\n            filterPlaceholder=\"搜尋帳號\"\n            style={{ minWidth: '120px' }}\n          />\n          <Column\n            field=\"userEmail\"\n            header=\"Email\"\n            sortable\n            filter\n            filterPlaceholder=\"搜尋Email\"\n            style={{ minWidth: '200px' }}\n          />\n          <Column\n            header=\"角色權限\"\n            body={rolesBodyTemplate}\n            style={{ minWidth: '200px' }}\n          />\n          <Column\n            field=\"isEnabled\"\n            header=\"狀態\"\n            body={statusBodyTemplate}\n            sortable\n            style={{ minWidth: '100px' }}\n          />\n          <Column\n            header=\"操作\"\n            body={actionBodyTemplate}\n            style={{ minWidth: '100px' }}\n          />\n        </DataTable>\n      </Card>\n\n      {/* 編輯角色對話框 */}\n      <Dialog\n        header={`編輯用戶角色權限 - ${editingUser?.userName}`}\n        visible={showEditDialog}\n        style={{ width: '500px' }}\n        onHide={() => setShowEditDialog(false)}\n        footer={\n          <div className=\"flex justify-content-end gap-2\">\n            <Button\n              label=\"取消\"\n              icon=\"pi pi-times\"\n              onClick={() => setShowEditDialog(false)}\n              className=\"p-button-text\"\n            />\n            <Button\n              label=\"保存\"\n              icon=\"pi pi-check\"\n              onClick={handleSaveUserRoles}\n              className=\"p-button-primary\"\n            />\n          </div>\n        }\n      >\n        {editingUser && (\n          <div className=\"grid\">\n            <div className=\"col-12\">\n              <div className=\"field\">\n                <label className=\"font-bold\">用戶帳號:</label>\n                <p className=\"m-0\">{editingUser.userAccount}</p>\n              </div>\n            </div>\n            <div className=\"col-12\">\n              <div className=\"field\">\n                <label htmlFor=\"roles\" className=\"font-bold\">角色權限:</label>\n                <MultiSelect\n                  id=\"roles\"\n                  value={selectedRoles}\n                  options={availableRoleOptions}\n                  onChange={(e) => setSelectedRoles(e.value)}\n                  placeholder=\"選擇角色權限\"\n                  className=\"w-full\"\n                  display=\"chip\"\n                />\n              </div>\n            </div>\n          </div>\n        )}\n      </Dialog>\n    </div>\n  );\n};\n\nexport default UsersPage;\n"], "mappings": "AAAA,OAASA,MAAM,KAAQ,mBAAmB,CAC1C,OAASC,MAAM,KAAQ,mBAAmB,CAC1C,OAASC,SAAS,KAAQ,sBAAsB,CAChD,OAASC,MAAM,KAAQ,mBAAmB,CAC1C,OAASC,QAAQ,KAAQ,qBAAqB,CAC9C,OAASC,SAAS,KAAQ,sBAAsB,CAChD,OAASC,KAAK,KAAQ,kBAAkB,CACxC,OAASC,GAAG,KAAQ,gBAAgB,CACpC,OAASC,WAAW,KAAQ,wBAAwB,CACpD,OAASC,IAAI,KAAQ,iBAAiB,CACtC,OAASC,eAAe,KAAQ,4BAA4B,CAC5D,MAAO,CAAAC,KAAK,EAAIC,MAAM,CAAEC,QAAQ,CAAEC,SAAS,KAAQ,OAAO,CAC1D,MAAO,CAAAC,GAAG,KAAM,oBAAoB,CACpC,OAASC,GAAG,KAAQ,oBAAoB,CACxC,OAASC,cAAc,KAAQ,4BAA4B,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAkC5D,KAAM,CAAAC,SAAmB,CAAGA,CAAA,GAAM,CAChC,KAAM,CAAAC,KAAK,CAAGX,MAAM,CAAQ,IAAI,CAAC,CACjC,KAAM,CAACY,KAAK,CAAEC,QAAQ,CAAC,CAAGZ,QAAQ,CAAS,EAAE,CAAC,CAC9C,KAAM,CAACa,KAAK,CAAEC,QAAQ,CAAC,CAAGd,QAAQ,CAAS,EAAE,CAAC,CAC9C,KAAM,CAACe,OAAO,CAAEC,UAAU,CAAC,CAAGhB,QAAQ,CAAC,IAAI,CAAC,CAC5C,KAAM,CAACiB,UAAU,CAAEC,aAAa,CAAC,CAAGlB,QAAQ,CAAC,EAAE,CAAC,CAChD,KAAM,CAACmB,kBAAkB,CAAEC,qBAAqB,CAAC,CAAGpB,QAAQ,CAAgB,IAAI,CAAC,CACjF,KAAM,CAACqB,cAAc,CAAEC,iBAAiB,CAAC,CAAGtB,QAAQ,CAAC,KAAK,CAAC,CAC3D,KAAM,CAACuB,WAAW,CAAEC,cAAc,CAAC,CAAGxB,QAAQ,CAA2B,IAAI,CAAC,CAC9E,KAAM,CAACyB,aAAa,CAAEC,gBAAgB,CAAC,CAAG1B,QAAQ,CAAW,EAAE,CAAC,CAChE,KAAM,CAAC2B,UAAU,CAAEC,aAAa,CAAC,CAAG5B,QAAQ,CAAC,KAAK,CAAC,CACnD,KAAM,CAAE6B,aAAc,CAAC,CAAGzB,cAAc,CAAC,CAAC,CAE1C;AACA,KAAM,CAAA0B,SAAS,CAAG,KAAAA,CAAA,GAAY,CAC5B,GAAI,CACFF,aAAa,CAAC,IAAI,CAAC,CACnBzB,GAAG,CAACD,GAAG,CAAC,QAAQ,CAAE,CAAEe,UAAU,CAAEE,kBAAmB,CAAC,CAAC,CAErD,KAAM,CAAAY,QAAQ,CAAG,KAAM,CAAA7B,GAAG,CAAC8B,GAAG,CAAC,6BAA6B,CAAE,CAC5DC,MAAM,CAAE,CAAEC,IAAI,CAAEjB,UAAW,CAC7B,CAAC,CAAC,CAEF,GAAI,CAAAkB,aAAa,CAAGJ,QAAQ,CAACK,IAAI,CAEjC;AACA,GAAIjB,kBAAkB,CAAE,CACtBgB,aAAa,CAAGJ,QAAQ,CAACK,IAAI,CAACC,MAAM,CAAEC,IAAU,EAC9CA,IAAI,CAACzB,KAAK,EAAIyB,IAAI,CAACzB,KAAK,CAAC0B,IAAI,CAAEC,IAAc,EAAKA,IAAI,CAACC,MAAM,GAAKtB,kBAAkB,CACtF,CAAC,CACH,CAEAP,QAAQ,CAACuB,aAAa,CAAC,CACvBhC,GAAG,CAACD,GAAG,CAAC,UAAU,CAAE,CAClBwC,KAAK,CAAEX,QAAQ,CAACK,IAAI,CAACO,MAAM,CAC3BC,QAAQ,CAAET,aAAa,CAACQ,MAAM,CAC9BE,UAAU,CAAE1B,kBACd,CAAC,CAAC,CAEJ,CAAE,MAAO2B,KAAU,CAAE,KAAAC,cAAA,CACnB5C,GAAG,CAAC2C,KAAK,CAAC,UAAU,CAAEA,KAAK,CAAC,CAC5B,CAAAC,cAAA,CAAArC,KAAK,CAACsC,OAAO,UAAAD,cAAA,iBAAbA,cAAA,CAAeE,IAAI,CAAC,CAClBC,QAAQ,CAAE,OAAO,CACjBC,OAAO,CAAE,MAAM,CACfC,MAAM,CAAE,UAAU,CAClBC,IAAI,CAAE,IACR,CAAC,CAAC,CACJ,CAAC,OAAS,CACRrC,UAAU,CAAC,KAAK,CAAC,CACjBY,aAAa,CAAC,KAAK,CAAC,CACtB,CACF,CAAC,CAED;AACA,KAAM,CAAA0B,SAAS,CAAG,KAAAA,CAAA,GAAY,CAC5B,GAAI,CACFnD,GAAG,CAACD,GAAG,CAAC,QAAQ,CAAC,CAEjB,KAAM,CAAA6B,QAAQ,CAAG,KAAM,CAAA7B,GAAG,CAAC8B,GAAG,CAAC,qBAAqB,CAAC,CACrDlB,QAAQ,CAACiB,QAAQ,CAACK,IAAI,CAAC,CAEvBjC,GAAG,CAACD,GAAG,CAAC,UAAU,CAAE,CAAEqD,KAAK,CAAExB,QAAQ,CAACK,IAAI,CAACO,MAAO,CAAC,CAAC,CAEtD,CAAE,MAAOG,KAAU,CAAE,KAAAU,eAAA,CACnBrD,GAAG,CAAC2C,KAAK,CAAC,UAAU,CAAEA,KAAK,CAAC,CAC5B,CAAAU,eAAA,CAAA9C,KAAK,CAACsC,OAAO,UAAAQ,eAAA,iBAAbA,eAAA,CAAeP,IAAI,CAAC,CAClBC,QAAQ,CAAE,OAAO,CACjBC,OAAO,CAAE,MAAM,CACfC,MAAM,CAAE,UAAU,CAClBC,IAAI,CAAE,IACR,CAAC,CAAC,CACJ,CACF,CAAC,CAED;AACA,KAAM,CAAAI,mBAAmB,CAAG,KAAO,CAAAnB,IAAU,EAAK,CAChD,GAAI,CAEFnC,GAAG,CAACD,GAAG,CAAC,QAAQ,CAAE,CAAEwD,MAAM,CAAEpB,IAAI,CAACoB,MAAO,CAAC,CAAC,CAE1C,KAAM,CAAA3B,QAAQ,CAAG,KAAM,CAAA7B,GAAG,CAAC8B,GAAG,4BAAA2B,MAAA,CAA4BrB,IAAI,CAACoB,MAAM,CAAE,CAAC,CACxE,KAAM,CAAAE,QAAQ,CAAG7B,QAAQ,CAACK,IAAI,CAE9BZ,cAAc,CAAC,CACbkC,MAAM,CAAEE,QAAQ,CAACF,MAAM,CACvBG,QAAQ,CAAED,QAAQ,CAACC,QAAQ,CAC3BC,WAAW,CAAEF,QAAQ,CAACE,WAAW,CACjCjD,KAAK,CAAE+C,QAAQ,CAAC/C,KAAK,EAAI,EAC3B,CAAC,CAAC,CAEF;AACA,KAAM,CAAAA,KAAK,CAAG+C,QAAQ,CAAC/C,KAAK,EAAI,EAAE,CAClCa,gBAAgB,CAACb,KAAK,CAACkD,GAAG,CAAEvB,IAAc,EAAKA,IAAI,CAACC,MAAM,CAAC,CAAC,CAC5DnB,iBAAiB,CAAC,IAAI,CAAC,CAEvBnB,GAAG,CAACD,GAAG,CAAC,UAAU,CAAE0D,QAAQ,CAAC,CAE/B,CAAE,MAAOd,KAAU,CAAE,KAAAkB,eAAA,CACnB7D,GAAG,CAAC2C,KAAK,CAAC,UAAU,CAAEA,KAAK,CAAC,CAC5B,CAAAkB,eAAA,CAAAtD,KAAK,CAACsC,OAAO,UAAAgB,eAAA,iBAAbA,eAAA,CAAef,IAAI,CAAC,CAClBC,QAAQ,CAAE,OAAO,CACjBC,OAAO,CAAE,MAAM,CACfC,MAAM,CAAE,YAAY,CACpBC,IAAI,CAAE,IACR,CAAC,CAAC,CACJ,CACF,CAAC,CAED;AACA,KAAM,CAAAY,mBAAmB,CAAG,KAAAA,CAAA,GAAY,CACtC,GAAI,CAAC1C,WAAW,CAAE,OAElB,GAAI,KAAA2C,eAAA,CACF/D,GAAG,CAACD,GAAG,CAAC,QAAQ,CAAE,CAChBwD,MAAM,CAAEnC,WAAW,CAACmC,MAAM,CAC1BS,OAAO,CAAE1C,aACX,CAAC,CAAC,CAEF,KAAM,CAAAvB,GAAG,CAACkE,GAAG,CAAC,4BAA4B,CAAE,CAC1CC,MAAM,CAAE9C,WAAW,CAACmC,MAAM,CAC1BY,OAAO,CAAE7C,aACX,CAAC,CAAC,CAEF,CAAAyC,eAAA,CAAAxD,KAAK,CAACsC,OAAO,UAAAkB,eAAA,iBAAbA,eAAA,CAAejB,IAAI,CAAC,CAClBC,QAAQ,CAAE,SAAS,CACnBC,OAAO,CAAE,MAAM,CACfC,MAAM,CAAE,WAAW,CACnBC,IAAI,CAAE,IACR,CAAC,CAAC,CAEF/B,iBAAiB,CAAC,KAAK,CAAC,CACxBQ,SAAS,CAAC,CAAC,CAAE;AAEb3B,GAAG,CAACD,GAAG,CAAC,UAAU,CAAC,CAErB,CAAE,MAAO4C,KAAU,CAAE,KAAAyB,eAAA,CACnBpE,GAAG,CAAC2C,KAAK,CAAC,UAAU,CAAEA,KAAK,CAAC,CAC5B,CAAAyB,eAAA,CAAA7D,KAAK,CAACsC,OAAO,UAAAuB,eAAA,iBAAbA,eAAA,CAAetB,IAAI,CAAC,CAClBC,QAAQ,CAAE,OAAO,CACjBC,OAAO,CAAE,MAAM,CACfC,MAAM,CAAE,YAAY,CACpBC,IAAI,CAAE,IACR,CAAC,CAAC,CACJ,CACF,CAAC,CAED;AACApD,SAAS,CAAC,IAAM,CACd6B,SAAS,CAAC,CAAC,CACXwB,SAAS,CAAC,CAAC,CACX;AACF,CAAC,CAAE,EAAE,CAAC,CAEN;AACArD,SAAS,CAAC,IAAM,CACd,GAAIY,KAAK,CAAC8B,MAAM,CAAG,CAAC,CAAE,CACpBb,SAAS,CAAC,CAAC,CACb,CACA;AACF,CAAC,CAAE,CAACX,kBAAkB,CAAC,CAAC,CAExB;AACA,KAAM,CAAAqD,YAAY,CAAGA,CAAA,GAAM,CACzB1C,SAAS,CAAC,CAAC,CACb,CAAC,CAEC;AACF,KAAM,CAAA2C,aAAa,CAAG,KAAAA,CAAA,GAAY,CAChC,GAAI,KAAAC,eAAA,CACF,KAAM,CAAAxE,GAAG,CAAC8B,GAAG,CAAC,2BAA2B,CAAC,CAC1C,CAAA0C,eAAA,CAAAhE,KAAK,CAACsC,OAAO,UAAA0B,eAAA,iBAAbA,eAAA,CAAezB,IAAI,CAAC,CAClBC,QAAQ,CAAE,SAAS,CACnBC,OAAO,CAAE,MAAM,CACfC,MAAM,CAAE,OAAO,CACfC,IAAI,CAAE,IACR,CAAC,CAAC,CACJ,CAAE,MAAOP,KAAU,CAAE,KAAA6B,eAAA,CACnBxE,GAAG,CAAC2C,KAAK,CAAC,QAAQ,CAAEA,KAAK,CAAC,CAC1B,CAAA6B,eAAA,CAAAjE,KAAK,CAACsC,OAAO,UAAA2B,eAAA,iBAAbA,eAAA,CAAe1B,IAAI,CAAC,CAClBC,QAAQ,CAAE,OAAO,CACjBC,OAAO,CAAE,MAAM,CACfC,MAAM,CAAE,QAAQ,CAChBC,IAAI,CAAE,IACR,CAAC,CAAC,CACJ,CACF,CAAC,CAED;AACA,KAAM,CAAAuB,iBAAiB,CAAIC,OAAa,EAAK,CAC3C;AACA,GAAI,CAACA,OAAO,CAAChE,KAAK,EAAI,CAACiE,KAAK,CAACC,OAAO,CAACF,OAAO,CAAChE,KAAK,CAAC,EAAIgE,OAAO,CAAChE,KAAK,CAAC8B,MAAM,GAAK,CAAC,CAAE,CACjF,mBACErC,IAAA,CAACZ,GAAG,EACFsF,KAAK,CAAC,oBAAK,CACX9B,QAAQ,CAAC,SAAS,CAClB+B,SAAS,CAAC,SAAS,CACpB,CAAC,CAEN,CAEA,mBACE3E,IAAA,QAAK2E,SAAS,CAAC,sBAAsB,CAAAC,QAAA,CAClCL,OAAO,CAAChE,KAAK,CAACkD,GAAG,CAAEvB,IAAc,eAChClC,IAAA,CAACZ,GAAG,EAEFsF,KAAK,CAAExC,IAAI,CAAC2C,QAAS,CACrBjC,QAAQ,CAAC,MAAM,CACf+B,SAAS,CAAC,SAAS,EAHdzC,IAAI,CAACC,MAIX,CACF,CAAC,CACC,CAAC,CAEV,CAAC,CAED;AACA,KAAM,CAAA2C,kBAAkB,CAAIP,OAAa,EAAK,CAC5C,mBACEvE,IAAA,QAAK2E,SAAS,CAAC,YAAY,CAAAC,QAAA,CACxBrD,aAAa,CAAC,iBAAiB,CAAC,eAC/BvB,IAAA,CAACnB,MAAM,EACLkG,IAAI,CAAC,cAAc,CACnBJ,SAAS,CAAC,kBAAkB,CAC5BK,IAAI,CAAC,OAAO,CACZC,OAAO,CAAEA,CAAA,GAAM9B,mBAAmB,CAACoB,OAAO,CAAE,CAC5CW,KAAK,CAAC,cAAI,CACX,CACF,CACE,CAAC,CAEV,CAAC,CAED;AACA,KAAM,CAAAC,kBAAkB,CAAIZ,OAAa,EAAK,CAC5C,mBACEvE,IAAA,CAACZ,GAAG,EACFsF,KAAK,CAAEH,OAAO,CAACa,SAAS,CAAG,IAAI,CAAG,IAAK,CACvCxC,QAAQ,CAAE2B,OAAO,CAACa,SAAS,CAAG,SAAS,CAAG,QAAS,CACpD,CAAC,CAEN,CAAC,CAED;AACA,KAAM,CAAAC,oBAAoB,CAAG9E,KAAK,CAACkD,GAAG,CAACvB,IAAI,GAAK,CAC9CgD,KAAK,CAAEhD,IAAI,CAACoD,IAAI,CAChBZ,KAAK,CAAExC,IAAI,CAACqD,EACd,CAAC,CAAC,CAAC,CAEH,KAAM,CAAAC,aAAa,cACfxF,IAAA,CAACnB,MAAM,EACH4G,IAAI,CAAC,QAAQ,CACbV,IAAI,CAAC,eAAe,CACpBW,IAAI,MACJT,OAAO,CAAEA,CAAA,GAAMzD,SAAS,CAAC,CAAE,CAC9B,CACJ,CACD,KAAM,CAAAmE,cAAc,cAAG3F,IAAA,SAAU,CAAC,CAElC,GAAIS,OAAO,CAAE,CACX,mBACET,IAAA,QAAK2E,SAAS,CAAC,6DAA6D,CAAAC,QAAA,cAC1E1E,KAAA,QAAKyE,SAAS,CAAC,aAAa,CAAAC,QAAA,eAC1B5E,IAAA,CAACT,eAAe,GAAE,CAAC,cACnBS,IAAA,MAAG2E,SAAS,CAAC,MAAM,CAAAC,QAAA,CAAC,+CAAU,CAAG,CAAC,EAC/B,CAAC,CACH,CAAC,CAEV,CAEA,mBACE1E,KAAA,QAAKyE,SAAS,CAAC,YAAY,CAAAC,QAAA,eACzB5E,IAAA,CAACb,KAAK,EAACyG,GAAG,CAAExF,KAAM,CAAE,CAAC,cAErBJ,IAAA,CAACV,IAAI,EAACuG,KAAK,CAAC,sCAAQ,CAAClB,SAAS,CAAC,MAAM,CAAAC,QAAA,cACnC5E,IAAA,MAAG2E,SAAS,CAAC,4BAA4B,CAAAC,QAAA,CAAC,gUAE1C,CAAG,CAAC,CACA,CAAC,cAGP5E,IAAA,CAACV,IAAI,EAACqF,SAAS,CAAC,MAAM,CAAAC,QAAA,cACpB1E,KAAA,QAAKyE,SAAS,CAAC,MAAM,CAAAC,QAAA,eACnB5E,IAAA,QAAK2E,SAAS,CAAC,gBAAgB,CAAAC,QAAA,cAC7B1E,KAAA,QAAKyE,SAAS,CAAC,cAAc,CAAAC,QAAA,eAC3B5E,IAAA,CAACd,SAAS,EACR4G,WAAW,CAAC,sCAAQ,CACpBpB,KAAK,CAAE/D,UAAW,CAClBoF,QAAQ,CAAGC,CAAC,EAAKpF,aAAa,CAACoF,CAAC,CAACC,MAAM,CAACvB,KAAK,CAAE,CAC/CwB,SAAS,CAAGF,CAAC,EAAKA,CAAC,CAACG,GAAG,GAAK,OAAO,EAAIjC,YAAY,CAAC,CAAE,CACvD,CAAC,cACFlE,IAAA,CAACnB,MAAM,EACLkG,IAAI,CAAC,cAAc,CACnBE,OAAO,CAAEf,YAAa,CACtBkC,QAAQ,CAAE/E,UAAW,CACtB,CAAC,EACC,CAAC,CACH,CAAC,cACNrB,IAAA,QAAK2E,SAAS,CAAC,gBAAgB,CAAAC,QAAA,cAC7B5E,IAAA,CAACf,QAAQ,EACPyF,KAAK,CAAE7D,kBAAmB,CAC1BwF,OAAO,CAAE,CACP,CAAEnB,KAAK,CAAE,MAAM,CAAER,KAAK,CAAE,IAAK,CAAC,CAC9B,GAAGnE,KAAK,CAACkD,GAAG,CAACvB,IAAI,GAAK,CAAEgD,KAAK,CAAEhD,IAAI,CAACoD,IAAI,CAAEZ,KAAK,CAAExC,IAAI,CAACqD,EAAG,CAAC,CAAC,CAAC,CAC5D,CACFQ,QAAQ,CAAGC,CAAC,EAAKlF,qBAAqB,CAACkF,CAAC,CAACtB,KAAK,CAAE,CAChDoB,WAAW,CAAC,0BAAM,CAClBnB,SAAS,CAAC,QAAQ,CAClB2B,SAAS,MACV,CAAC,CACC,CAAC,cACNtG,IAAA,QAAK2E,SAAS,CAAC,iBAAiB,CAAAC,QAAA,cAC9B5E,IAAA,QAAK2E,SAAS,CAAC,YAAY,CAAAC,QAAA,cACzB5E,IAAA,CAACnB,MAAM,EACHqG,KAAK,CAAC,0BAAM,CACZP,SAAS,CAAC,iBAAiB,CAC3BI,IAAI,CAAC,aAAa,CAClBE,OAAO,CAAEd,aAAc,CACxB,CAAC,CACD,CAAC,CACH,CAAC,EACH,CAAC,CACF,CAAC,cAGPnE,IAAA,CAACV,IAAI,EAAAsF,QAAA,cACH1E,KAAA,CAACnB,SAAS,EACR2F,KAAK,CAAErE,KAAM,CACbkG,SAAS,MACTC,IAAI,CAAE,EAAG,CACTC,kBAAkB,CAAE,CAAC,CAAC,CAAE,EAAE,CAAE,EAAE,CAAE,EAAE,CAAE,CACpCC,QAAQ,CAAC,UAAU,CACnBC,aAAa,MACbC,aAAa,CAAC,MAAM,CACpBC,kBAAkB,CAAE,CAAC,UAAU,CAAE,aAAa,CAAE,WAAW,CAAE,CAC7DC,YAAY,CAAC,kDAAU,CACvBnC,SAAS,CAAC,uBAAuB,CACjCa,aAAa,CAAEA,aAAc,CAC7BG,cAAc,CAAEA,cAAe,CAAAf,QAAA,eAE/B5E,IAAA,CAAClB,MAAM,EACLiI,KAAK,CAAC,UAAU,CAChBC,MAAM,CAAC,0BAAM,CACbC,QAAQ,MACRlF,MAAM,MACNmF,iBAAiB,CAAC,0BAAM,CACxBC,KAAK,CAAE,CAAEC,QAAQ,CAAE,OAAQ,CAAE,CAC9B,CAAC,cACFpH,IAAA,CAAClB,MAAM,EACLiI,KAAK,CAAC,aAAa,CACnBC,MAAM,CAAC,cAAI,CACXC,QAAQ,MACRlF,MAAM,MACNmF,iBAAiB,CAAC,0BAAM,CACxBC,KAAK,CAAE,CAAEC,QAAQ,CAAE,OAAQ,CAAE,CAC9B,CAAC,cACFpH,IAAA,CAAClB,MAAM,EACLiI,KAAK,CAAC,WAAW,CACjBC,MAAM,CAAC,OAAO,CACdC,QAAQ,MACRlF,MAAM,MACNmF,iBAAiB,CAAC,mBAAS,CAC3BC,KAAK,CAAE,CAAEC,QAAQ,CAAE,OAAQ,CAAE,CAC9B,CAAC,cACFpH,IAAA,CAAClB,MAAM,EACLkI,MAAM,CAAC,0BAAM,CACbK,IAAI,CAAE/C,iBAAkB,CACxB6C,KAAK,CAAE,CAAEC,QAAQ,CAAE,OAAQ,CAAE,CAC9B,CAAC,cACFpH,IAAA,CAAClB,MAAM,EACLiI,KAAK,CAAC,WAAW,CACjBC,MAAM,CAAC,cAAI,CACXK,IAAI,CAAElC,kBAAmB,CACzB8B,QAAQ,MACRE,KAAK,CAAE,CAAEC,QAAQ,CAAE,OAAQ,CAAE,CAC9B,CAAC,cACFpH,IAAA,CAAClB,MAAM,EACLkI,MAAM,CAAC,cAAI,CACXK,IAAI,CAAEvC,kBAAmB,CACzBqC,KAAK,CAAE,CAAEC,QAAQ,CAAE,OAAQ,CAAE,CAC9B,CAAC,EACO,CAAC,CACR,CAAC,cAGPpH,IAAA,CAAChB,MAAM,EACLgI,MAAM,uDAAA3D,MAAA,CAAgBpC,WAAW,SAAXA,WAAW,iBAAXA,WAAW,CAAEsC,QAAQ,CAAG,CAC9C+D,OAAO,CAAEvG,cAAe,CACxBoG,KAAK,CAAE,CAAEI,KAAK,CAAE,OAAQ,CAAE,CAC1BC,MAAM,CAAEA,CAAA,GAAMxG,iBAAiB,CAAC,KAAK,CAAE,CACvCyG,MAAM,cACJvH,KAAA,QAAKyE,SAAS,CAAC,gCAAgC,CAAAC,QAAA,eAC7C5E,IAAA,CAACnB,MAAM,EACLqG,KAAK,CAAC,cAAI,CACVH,IAAI,CAAC,aAAa,CAClBE,OAAO,CAAEA,CAAA,GAAMjE,iBAAiB,CAAC,KAAK,CAAE,CACxC2D,SAAS,CAAC,eAAe,CAC1B,CAAC,cACF3E,IAAA,CAACnB,MAAM,EACLqG,KAAK,CAAC,cAAI,CACVH,IAAI,CAAC,aAAa,CAClBE,OAAO,CAAEtB,mBAAoB,CAC7BgB,SAAS,CAAC,kBAAkB,CAC7B,CAAC,EACC,CACN,CAAAC,QAAA,CAEA3D,WAAW,eACVf,KAAA,QAAKyE,SAAS,CAAC,MAAM,CAAAC,QAAA,eACnB5E,IAAA,QAAK2E,SAAS,CAAC,QAAQ,CAAAC,QAAA,cACrB1E,KAAA,QAAKyE,SAAS,CAAC,OAAO,CAAAC,QAAA,eACpB5E,IAAA,UAAO2E,SAAS,CAAC,WAAW,CAAAC,QAAA,CAAC,2BAAK,CAAO,CAAC,cAC1C5E,IAAA,MAAG2E,SAAS,CAAC,KAAK,CAAAC,QAAA,CAAE3D,WAAW,CAACuC,WAAW,CAAI,CAAC,EAC7C,CAAC,CACH,CAAC,cACNxD,IAAA,QAAK2E,SAAS,CAAC,QAAQ,CAAAC,QAAA,cACrB1E,KAAA,QAAKyE,SAAS,CAAC,OAAO,CAAAC,QAAA,eACpB5E,IAAA,UAAO0H,OAAO,CAAC,OAAO,CAAC/C,SAAS,CAAC,WAAW,CAAAC,QAAA,CAAC,2BAAK,CAAO,CAAC,cAC1D5E,IAAA,CAACX,WAAW,EACVsI,EAAE,CAAC,OAAO,CACVjD,KAAK,CAAEvD,aAAc,CACrBkF,OAAO,CAAEhB,oBAAqB,CAC9BU,QAAQ,CAAGC,CAAC,EAAK5E,gBAAgB,CAAC4E,CAAC,CAACtB,KAAK,CAAE,CAC3CoB,WAAW,CAAC,sCAAQ,CACpBnB,SAAS,CAAC,QAAQ,CAClBiD,OAAO,CAAC,MAAM,CACf,CAAC,EACC,CAAC,CACH,CAAC,EACH,CACN,CACK,CAAC,EACN,CAAC,CAEV,CAAC,CAED,cAAe,CAAAzH,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
import { useState, useEffect, useCallback, useRef } from 'react';
import { useError<PERSON>andler } from './useErrorHandler';

interface UseApiDataOptions<T> {
  initialData?: T;
  dependencies?: any[];
  enabled?: boolean;
  onSuccess?: (data: T) => void;
  onError?: (error: string) => void;
}

interface UseApiDataReturn<T> {
  data: T | undefined;
  loading: boolean;
  error: string | null;
  refetch: () => Promise<void>;
  setData: (data: T | undefined) => void;
}

/**
 * Generic hook for API data fetching with loading states and error handling
 */
export function useApiData<T>(
  apiCall: () => Promise<T>,
  options: UseApiDataOptions<T> = {}
): UseApiDataReturn<T> {
  const {
    initialData,
    dependencies = [],
    enabled = true,
    onSuccess,
    onError,
  } = options;

  const [data, setData] = useState<T | undefined>(initialData);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const { handleError } = useErrorHandler();
  const isMountedRef = useRef(true);

  const fetchData = useCallback(async () => {
    if (!enabled) return;

    try {
      setLoading(true);
      setError(null);
      
      const result = await apiCall();
      
      if (isMountedRef.current) {
        setData(result);
        onSuccess?.(result);
      }
    } catch (err) {
      if (isMountedRef.current) {
        const errorMessage = handleError(err);
        setError(errorMessage);
        onError?.(errorMessage);
      }
    } finally {
      if (isMountedRef.current) {
        setLoading(false);
      }
    }
  }, [apiCall, enabled, handleError, onSuccess, onError]);

  useEffect(() => {
    fetchData();
  }, [fetchData, ...dependencies]);

  useEffect(() => {
    return () => {
      isMountedRef.current = false;
    };
  }, []);

  return {
    data,
    loading,
    error,
    refetch: fetchData,
    setData,
  };
}

/**
 * Hook for paginated API data
 */
interface UsePaginatedDataOptions<T> extends UseApiDataOptions<T[]> {
  pageSize?: number;
}

interface UsePaginatedDataReturn<T> extends UseApiDataReturn<T[]> {
  page: number;
  totalPages: number;
  hasNextPage: boolean;
  hasPreviousPage: boolean;
  nextPage: () => void;
  previousPage: () => void;
  goToPage: (page: number) => void;
}

export function usePaginatedData<T>(
  apiCall: (page: number, pageSize: number) => Promise<{ data: T[]; total: number }>,
  options: UsePaginatedDataOptions<T> = {}
): UsePaginatedDataReturn<T> {
  const { pageSize = 10, ...restOptions } = options;
  const [page, setPage] = useState(1);
  const [totalPages, setTotalPages] = useState(0);

  const paginatedApiCall = useCallback(async () => {
    const result = await apiCall(page, pageSize);
    setTotalPages(Math.ceil(result.total / pageSize));
    return result.data;
  }, [apiCall, page, pageSize]);

  const apiDataResult = useApiData(paginatedApiCall, {
    ...restOptions,
    dependencies: [page, pageSize, ...(restOptions.dependencies || [])],
  });

  const nextPage = useCallback(() => {
    setPage(prev => Math.min(prev + 1, totalPages));
  }, [totalPages]);

  const previousPage = useCallback(() => {
    setPage(prev => Math.max(prev - 1, 1));
  }, []);

  const goToPage = useCallback((newPage: number) => {
    setPage(Math.max(1, Math.min(newPage, totalPages)));
  }, [totalPages]);

  return {
    ...apiDataResult,
    page,
    totalPages,
    hasNextPage: page < totalPages,
    hasPreviousPage: page > 1,
    nextPage,
    previousPage,
    goToPage,
  };
}

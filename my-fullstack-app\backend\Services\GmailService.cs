using System;
using System.IO;
using System.Threading.Tasks;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Google.Apis.Auth.OAuth2;
using Google.Apis.Gmail.v1;
using Google.Apis.Gmail.v1.Data;
using Google.Apis.Services;
using MimeKit;
using Google.Apis.Util.Store;
using System.Threading;
using Google.Apis.Auth.OAuth2.Flows;
using Google.Apis.Auth.OAuth2.Responses;

namespace MyApi.Services
{
    public class GmailService : IGmailService
    {
        private readonly ILogger<GmailService> _logger;
        private readonly IConfiguration _configuration;
        private readonly string _credentialsPath;
        private readonly string _senderEmail;
        private readonly bool _useRealGmail;

        // 儲存用戶授權 token 的檔案路徑
        private const string TokenFileName = "token.json";

        public GmailService(ILogger<GmailService> logger, IConfiguration configuration)
        {
            _logger = logger;
            _configuration = configuration;
            _credentialsPath = _configuration["GoogleApiService:ServiceAccountKeyPath"] ?? "gmail-credentials.json";
            _senderEmail = _configuration["GoogleApiService:SenderEmail"] ?? "";
            _useRealGmail = _configuration.GetValue<bool>("GoogleApiService:UseRealGmail", false);
        }

        public async Task<bool> SendEmailAsync(string to, string subject, string body, string? attachmentPath = null)
        {
            try
            {
                _logger.LogInformation("開始發送郵件到: {To}, 主題: {Subject}", to, subject);

                if (!_useRealGmail)
                {
                    // 模擬模式
                    _logger.LogInformation("使用模擬模式發送郵件");
                    await Task.Delay(1000);
                    _logger.LogInformation("郵件發送成功（模擬）");
                    return true;
                }

                // 真實 Gmail API 發送
                var gmailService = await CreateGmailServiceAsync();
                var message = await CreateEmailMessageAsync(to, subject, body, attachmentPath);

                var request = gmailService.Users.Messages.Send(message, "me");
                var result = await request.ExecuteAsync();

                _logger.LogInformation("郵件發送成功，訊息ID: {MessageId}", result.Id);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "發送郵件失敗到: {To}", to);
                return false;
            }
        }

        public async Task<bool> SendReceiptEmailAsync(string to, string patientName, string receiptNo, string pdfPath)
        {
            try
            {
                var subject = $"厝邊頭家物理治療所 - 收據 {receiptNo}";
                var body = CreateReceiptEmailBody(patientName, receiptNo);

                return await SendEmailAsync(to, subject, body, pdfPath);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "發送收據郵件失敗: {ReceiptNo} to {To}", receiptNo, to);
                return false;
            }
        }

        private string CreateReceiptEmailBody(string patientName, string receiptNo)
        {


            var html = $@"
<!DOCTYPE html>
<html>
<head>
    <meta charset='utf-8'>
    <style>
        body {{ font-family: Arial, sans-serif; line-height: 1.6; color: #333; }}
        .container {{ max-width: 600px; margin: 0 auto; padding: 20px; }}
        .header {{ background-color: #2c5aa0; color: white; padding: 20px; text-align: center; }}
        .content {{ padding: 20px; background-color: #f9f9f9; }}
        .footer {{ padding: 20px; text-align: center; color: #666; font-size: 12px; }}
        .highlight {{ color: #2c5aa0; font-weight: bold; }}
    </style>
</head>
<body>
    <div class='container'>
        <div class='header'>
            <h1>厝邊頭家物理治療所</h1>
            <p>收據通知</p>
        </div>
        
        <div class='content'>
            <p>親愛的 <span class='highlight'>{patientName}</span> 您好：</p>
            
            <p>感謝您選擇厝邊頭家物理治療所的服務。</p>
            
            <p>您的收據 <span class='highlight'>{receiptNo}</span> 已經準備完成，請查看附件中的 PDF 檔案。</p>
            
            <p>如有任何問題，請隨時與我們聯繫。</p>
            
            <p>祝您身體健康！</p>
        </div>
        
        <div class='footer'>
            <p>厝邊頭家物理治療所</p>
            <p>此郵件為系統自動發送，請勿直接回覆</p>
        </div>
    </div>
</body>
</html>";

            return html;
        }

        /// <summary>
        /// 創建 Gmail 服務實例
        /// </summary>
        private async Task<Google.Apis.Gmail.v1.GmailService> CreateGmailServiceAsync()
        {
            try
            {
                _logger.LogInformation("開始創建 Gmail 服務...");

                // 從設定檔讀取金鑰路徑和 Token 儲存路徑
                var oauthClientKeyPath = _configuration["GoogleApiService:OAuthClientKeyPath"];
                var tokenStoragePath = _configuration["GoogleApiService:TokenStoragePath"] ?? "gmail-token.json";

                if (string.IsNullOrEmpty(oauthClientKeyPath))
                {
                    throw new InvalidOperationException("設定檔中缺少 GoogleApiService:OAuthClientKeyPath。");
                }

                var fullClientKeyPath = Path.Combine(Directory.GetCurrentDirectory(), oauthClientKeyPath);
                if (!File.Exists(fullClientKeyPath))
                {
                    throw new FileNotFoundException("OAuth Client ID 的 JSON 金鑰檔案未找到。", fullClientKeyPath);
                }

                var fullTokenPath = Path.Combine(Directory.GetCurrentDirectory(), tokenStoragePath);
                if (!File.Exists(fullTokenPath))
                {
                    _logger.LogError("Refresh Token 檔案不存在於: {TokenPath}", fullTokenPath);
                    _logger.LogError("請先執行一次性的 OAuth 授權流程，請瀏覽 /api/auth/signin-google 來完成授權。");
                    throw new FileNotFoundException("Refresh Token 檔案未找到，請先完成應用程式授權。", fullTokenPath);
                }

                _logger.LogInformation("從 {path} 載入 Google Client Secrets。", fullClientKeyPath);

                // 【修改處】從 JSON 檔案非同步載入 Client Secrets
                var clientSecrets = (await GoogleClientSecrets.FromFileAsync(fullClientKeyPath)).Secrets;

                var refreshToken = await File.ReadAllTextAsync(fullTokenPath);
                _logger.LogInformation("成功從 {TokenPath} 載入 Refresh Token。", fullTokenPath);

                var credential = new UserCredential(new GoogleAuthorizationCodeFlow(
                    new GoogleAuthorizationCodeFlow.Initializer
                    {
                        ClientSecrets = clientSecrets, // 使用從檔案載入的 Secrets
                        Scopes = new[] { Google.Apis.Gmail.v1.GmailService.Scope.GmailSend }
                    }),
                    "user",
                    new TokenResponse { RefreshToken = refreshToken }
                );

                var applicationName = _configuration["GoogleApiService:ApplicationName"] ?? "MyApi Schedule System";

                var service = new Google.Apis.Gmail.v1.GmailService(new BaseClientService.Initializer()
                {
                    HttpClientInitializer = credential,
                    ApplicationName = applicationName,
                });

                _logger.LogInformation("Gmail 服務創建成功");
                return service;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "初始化 Google Api Service 服務失敗");
                throw;
            }
        }

        /// <summary>
        /// 創建郵件訊息
        /// </summary>
        private async Task<Message> CreateEmailMessageAsync(string to, string subject, string body, string? attachmentPath = null)
        {
            try
            {
                var message = new MimeMessage();
                message.From.Add(new MailboxAddress("厝邊頭家物理治療所", _senderEmail));
                message.To.Add(new MailboxAddress("", to));
                message.Subject = subject;

                var builder = new BodyBuilder();
                builder.HtmlBody = body;

                // 添加附件
                if (!string.IsNullOrEmpty(attachmentPath) && File.Exists(attachmentPath))
                {
                    var attachment = await builder.Attachments.AddAsync(attachmentPath);
                    attachment.ContentDisposition.FileName = Path.GetFileName(attachmentPath);
                    _logger.LogInformation("添加附件: {AttachmentPath}", attachmentPath);
                }

                message.Body = builder.ToMessageBody();

                // 轉換為 Gmail API 格式
                using var stream = new MemoryStream();
                await message.WriteToAsync(stream);
                var rawMessage = Convert.ToBase64String(stream.ToArray())
                    .Replace('+', '-')
                    .Replace('/', '_')
                    .Replace("=", "");

                return new Message
                {
                    Raw = rawMessage
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "創建郵件訊息失敗");
                throw;
            }
        }
    }
}

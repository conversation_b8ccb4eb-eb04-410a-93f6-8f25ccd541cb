#!/bin/bash

# 還原至指定日期的腳本
# 使用方式：bash restore-mysql.sh YYYY-MM-DD

RESTORE_DATE="$1"

if [[ -z "$RESTORE_DATE" ]]; then
  echo "❌ 請輸入要還原的日期（格式：YYYY-MM-DD）"
  exit 1
fi

# 設定
FULL_BACKUP_DIR="/home/<USER>/mysql-backup/full"
BINLOG_DIR="/opt/mysql-backup/binlog"
MYSQL_CONTAINER="my-mysql"
MYSQL_USER="root"
MYSQL_PASSWORD="example"

# 檢查完整備份是否存在
FULL_BACKUP_FILE="$FULL_BACKUP_DIR/mysql-full-$RESTORE_DATE.sql.gz"

if [[ ! -f "$FULL_BACKUP_FILE" ]]; then
  echo "❌ 找不到完整備份檔案：$FULL_BACKUP_FILE"
  exit 1
fi

echo "🚨 將還原 MySQL 至 $RESTORE_DATE ..."

read -p "⚠️ 請確認已備份當前資料庫，是否繼續？(y/n): " CONFIRM
if [[ "$CONFIRM" != "y" ]]; then
  echo "⏹️ 中止還原"
  exit 0
fi

echo "🔁 清空現有資料庫..."
docker exec "$MYSQL_CONTAINER" sh -c \
  "mysql -u$MYSQL_USER -p$MYSQL_PASSWORD -e 'DROP DATABASE IF EXISTS test; SHOW DATABASES;' | grep -Ev '(Database|mysql|information_schema|performance_schema|sys)' | xargs -I{} mysql -u$MYSQL_USER -p$MYSQL_PASSWORD -e 'DROP DATABASE IF EXISTS \`{}\`;'"

echo "📦 還原完整備份..."
gunzip -c "$FULL_BACKUP_FILE" | docker exec -i "$MYSQL_CONTAINER" \
  mysql -u"$MYSQL_USER" -p"$MYSQL_PASSWORD"

echo "📚 開始套用 binlog 增量檔案..."

for BINLOG in $(ls "$BINLOG_DIR"/binlog-*.sql.gz 2>/dev/null | sort); do
  FILE_DATE=$(basename "$BINLOG" | cut -d'-' -f2- | cut -d'.' -f1)
  if [[ "$FILE_DATE" > "$RESTORE_DATE" ]]; then
    break
  fi
  echo "  ➕ 套用 $BINLOG"
  gunzip -c "$BINLOG" | docker exec -i "$MYSQL_CONTAINER" \
    mysql -u"$MYSQL_USER" -p"$MYSQL_PASSWORD"
done

echo "✅ 還原完成（至 $RESTORE_DATE）"
{"ast": null, "code": "const formatRelativeLocale = {\n  lastWeek: \"'síðasta' dddd 'kl.' p\",\n  yesterday: \"'í gær kl.' p\",\n  today: \"'í dag kl.' p\",\n  tomorrow: \"'á morgun kl.' p\",\n  nextWeek: \"dddd 'kl.' p\",\n  other: \"P\"\n};\nexport const formatRelative = (token, _date, _baseDate, _options) => formatRelativeLocale[token];", "map": {"version": 3, "names": ["formatRelativeLocale", "lastWeek", "yesterday", "today", "tomorrow", "nextWeek", "other", "formatRelative", "token", "_date", "_baseDate", "_options"], "sources": ["C:/Users/<USER>/Desktop/demo-git/demo-react/my-fullstack-app/frontend/node_modules/date-fns/locale/is/_lib/formatRelative.js"], "sourcesContent": ["const formatRelativeLocale = {\n  lastWeek: \"'síðasta' dddd 'kl.' p\",\n  yesterday: \"'í gær kl.' p\",\n  today: \"'í dag kl.' p\",\n  tomorrow: \"'á morgun kl.' p\",\n  nextWeek: \"dddd 'kl.' p\",\n  other: \"P\",\n};\n\nexport const formatRelative = (token, _date, _baseDate, _options) =>\n  formatRelativeLocale[token];\n"], "mappings": "AAAA,MAAMA,oBAAoB,GAAG;EAC3BC,QAAQ,EAAE,wBAAwB;EAClCC,SAAS,EAAE,eAAe;EAC1BC,KAAK,EAAE,eAAe;EACtBC,QAAQ,EAAE,kBAAkB;EAC5BC,QAAQ,EAAE,cAAc;EACxBC,KAAK,EAAE;AACT,CAAC;AAED,OAAO,MAAMC,cAAc,GAAGA,CAACC,KAAK,EAAEC,KAAK,EAAEC,SAAS,EAAEC,QAAQ,KAC9DX,oBAAoB,CAACQ,KAAK,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
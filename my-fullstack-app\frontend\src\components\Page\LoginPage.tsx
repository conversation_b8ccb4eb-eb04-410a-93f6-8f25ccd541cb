import { But<PERSON> } from 'primereact/button';
import { Card } from 'primereact/card';
import { InputText } from "primereact/inputtext";
import { Toast } from "primereact/toast";
import React, { useEffect, useRef, useState } from "react";
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';

const LoginPage: React.FC = () => {
  const toast = useRef<Toast>(null);
  const navigate = useNavigate();
  const { login, isAuthenticated } = useAuth();
  const [username, setUsername] = useState('');
  const [password, setPassword] = useState('');
  const [isLoggingIn, setIsLoggingIn] = useState(false);

  useEffect(() => {
    // 檢查是否已經登入
    const localToken = localStorage.getItem('token');
    console.log('LoginPage: 檢查登入狀態', {
      isAuthenticated,
      hasLocalToken: !!localToken,
      currentPath: window.location.pathname
    });

    if (isAuthenticated || localToken) {
      console.log('LoginPage: 已登入，導航到首頁');
      navigate('/');
    }
  }, [isAuthenticated, navigate]);

  const handleLogin = async (e?: React.FormEvent) => {
    if (e) {
      e.preventDefault(); // 防止表單提交導致頁面重新整理
    }
    if (isLoggingIn) return; // 防止重複點擊

    try {
      setIsLoggingIn(true);
      console.log('開始登入...', { username, password });

      const result = await login({ username, password });
      console.log('登入成功，檢查密碼狀態...', result);

      // 檢查是否使用預設密碼
      if (result?.isDefaultPassword) {
        console.log('使用預設密碼，導航到更新密碼頁面');
        // 延遲導航，確保狀態更新完成
        setTimeout(() => {
          navigate('/update-password');
        }, 100);
      } else {
        console.log('密碼已更新，導航到首頁');
        // 延遲導航，確保狀態更新完成
        setTimeout(() => {
          navigate('/');
        }, 100);
      }
    } catch (err: any) {
      console.error('登入失敗:', err);

      // 錯誤訊息已經由 API 層標準化
      const errorMessage = err.details || '登入失敗，請稍後再試';

      // 確保 Toast 組件已經渲染完成再顯示訊息
      setTimeout(() => {
        toast.current?.show({
            severity: "error",
            summary: "登入失敗",
            detail: errorMessage,
        });
      }, 100); // 縮短延遲，因為我們不需要等待 DOM 渲染
    } finally {
      setIsLoggingIn(false);
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      handleLogin();
    }
  };


  return (
    <div className="flex align-items-center justify-content-center min-h-screen bg-gray-100">
      <Toast ref={toast} />
      <Card title="厝邊頭家物理治療所" subTitle="後台系統" className="w-25rem">
        <form onSubmit={handleLogin} className="p-fluid">
          <div className="field mb-3">
            <label htmlFor="username" className="block mb-2">帳號</label>
            <InputText
              id="username"
              type="text"
              value={username}
              onChange={(e) => setUsername(e.target.value)}
              onKeyDown={handleKeyDown}
              placeholder="輸入帳號"
            />
          </div>
          <div className="field mb-4">
            <label htmlFor="password" className="block mb-2">密碼</label>
            <InputText
              id="password"
              type="password"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              onKeyDown={handleKeyDown}
              placeholder="輸入密碼"
            />
          </div>
          <Button
            label="登入"
            type="button"
            onClick={() => handleLogin()}
            className="w-full"
            loading={isLoggingIn}
            disabled={isLoggingIn || !username || !password}
          />
        </form>
      </Card>
    </div>
  );
}

export default LoginPage;
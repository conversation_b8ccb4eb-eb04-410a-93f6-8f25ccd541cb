{"ast": null, "code": "import { formatDistance } from \"./en-US/_lib/formatDistance.js\";\nimport { formatRelative } from \"./en-US/_lib/formatRelative.js\";\nimport { localize } from \"./en-US/_lib/localize.js\";\nimport { match } from \"./en-US/_lib/match.js\";\nimport { formatLong } from \"./en-GB/_lib/formatLong.js\";\n\n/**\n * @category Locales\n * @summary English locale (Ireland).\n * @language English\n * @iso-639-2 eng\n * <AUTHOR> [@tan75](https://github.com/tan75)\n */\nexport const enIE = {\n  code: \"en-IE\",\n  formatDistance: formatDistance,\n  formatLong: formatLong,\n  formatRelative: formatRelative,\n  localize: localize,\n  match: match,\n  options: {\n    weekStartsOn: 1 /* Monday */,\n    firstWeekContainsDate: 4\n  }\n};\n\n// Fallback for modularized imports:\nexport default enIE;", "map": {"version": 3, "names": ["formatDistance", "formatRelative", "localize", "match", "formatLong", "enIE", "code", "options", "weekStartsOn", "firstWeekContainsDate"], "sources": ["C:/Users/<USER>/Desktop/demo-git/demo-react/my-fullstack-app/frontend/node_modules/date-fns/locale/en-IE.js"], "sourcesContent": ["import { formatDistance } from \"./en-US/_lib/formatDistance.js\";\nimport { formatRelative } from \"./en-US/_lib/formatRelative.js\";\nimport { localize } from \"./en-US/_lib/localize.js\";\nimport { match } from \"./en-US/_lib/match.js\";\n\nimport { formatLong } from \"./en-GB/_lib/formatLong.js\";\n\n/**\n * @category Locales\n * @summary English locale (Ireland).\n * @language English\n * @iso-639-2 eng\n * <AUTHOR> [@tan75](https://github.com/tan75)\n */\nexport const enIE = {\n  code: \"en-IE\",\n  formatDistance: formatDistance,\n  formatLong: formatLong,\n  formatRelative: formatRelative,\n  localize: localize,\n  match: match,\n  options: {\n    weekStartsOn: 1 /* Monday */,\n    firstWeekContainsDate: 4,\n  },\n};\n\n// Fallback for modularized imports:\nexport default enIE;\n"], "mappings": "AAAA,SAASA,cAAc,QAAQ,gCAAgC;AAC/D,SAASC,cAAc,QAAQ,gCAAgC;AAC/D,SAASC,QAAQ,QAAQ,0BAA0B;AACnD,SAASC,KAAK,QAAQ,uBAAuB;AAE7C,SAASC,UAAU,QAAQ,4BAA4B;;AAEvD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMC,IAAI,GAAG;EAClBC,IAAI,EAAE,OAAO;EACbN,cAAc,EAAEA,cAAc;EAC9BI,UAAU,EAAEA,UAAU;EACtBH,cAAc,EAAEA,cAAc;EAC9BC,QAAQ,EAAEA,QAAQ;EAClBC,KAAK,EAAEA,KAAK;EACZI,OAAO,EAAE;IACPC,YAAY,EAAE,CAAC,CAAC;IAChBC,qBAAqB,EAAE;EACzB;AACF,CAAC;;AAED;AACA,eAAeJ,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "import'primeflex/primeflex.css';// 彈性網格\nimport'primeicons/primeicons.css';// 圖標\nimport'primereact/resources/primereact.min.css';// 核心\nimport'primereact/resources/themes/lara-light-indigo/theme.css';// 主題\nimport ReactDOM from'react-dom/client';import App from'./App';import'./index.css';import reportWebVitals from'./reportWebVitals';import{jsx as _jsx}from\"react/jsx-runtime\";const root=ReactDOM.createRoot(document.getElementById('root'));root.render(/*#__PURE__*/// <React.StrictMode>\n//   <App />\n// </React.StrictMode>\n_jsx(App,{}));// If you want to start measuring performance in your app, pass a function\n// to log results (for example: reportWebVitals(console.log))\n// or send to an analytics endpoint. Learn more: https://bit.ly/CRA-vitals\nreportWebVitals();", "map": {"version": 3, "names": ["ReactDOM", "App", "reportWebVitals", "jsx", "_jsx", "root", "createRoot", "document", "getElementById", "render"], "sources": ["C:/Users/<USER>/Desktop/demo-git/demo-react/my-fullstack-app/frontend/src/index.tsx"], "sourcesContent": ["import 'primeflex/primeflex.css'; // 彈性網格\r\nimport 'primeicons/primeicons.css'; // 圖標\r\nimport 'primereact/resources/primereact.min.css'; // 核心\r\nimport 'primereact/resources/themes/lara-light-indigo/theme.css'; // 主題\r\nimport ReactDOM from 'react-dom/client';\r\nimport App from './App';\r\nimport './index.css';\r\nimport reportWebVitals from './reportWebVitals';\r\n\r\nconst root = ReactDOM.createRoot(\r\n  document.getElementById('root') as HTMLElement\r\n);\r\nroot.render(\r\n  // <React.StrictMode>\r\n  //   <App />\r\n  // </React.StrictMode>\r\n  <App />\r\n);\r\n\r\n// If you want to start measuring performance in your app, pass a function\r\n// to log results (for example: reportWebVitals(console.log))\r\n// or send to an analytics endpoint. Learn more: https://bit.ly/CRA-vitals\r\nreportWebVitals();\r\n"], "mappings": "AAAA,MAAO,yBAAyB,CAAE;AAClC,MAAO,2BAA2B,CAAE;AACpC,MAAO,yCAAyC,CAAE;AAClD,MAAO,yDAAyD,CAAE;AAClE,MAAO,CAAAA,QAAQ,KAAM,kBAAkB,CACvC,MAAO,CAAAC,GAAG,KAAM,OAAO,CACvB,MAAO,aAAa,CACpB,MAAO,CAAAC,eAAe,KAAM,mBAAmB,CAAC,OAAAC,GAAA,IAAAC,IAAA,yBAEhD,KAAM,CAAAC,IAAI,CAAGL,QAAQ,CAACM,UAAU,CAC9BC,QAAQ,CAACC,cAAc,CAAC,MAAM,CAChC,CAAC,CACDH,IAAI,CAACI,MAAM,cACT;AACA;AACA;AACAL,IAAA,CAACH,GAAG,GAAE,CACR,CAAC,CAED;AACA;AACA;AACAC,eAAe,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
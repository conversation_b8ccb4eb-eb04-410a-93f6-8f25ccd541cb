using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace MyApi.Models
{
    public class Schedule
    {
        [Key]
        public int Id { get; set; }

        [Required]
        [StringLength(200)]
        public string Title { get; set; } = string.Empty;

        [Required]
        public DateTime StartDateTime { get; set; }

        [Required]
        public DateTime EndDateTime { get; set; }

        [Required]
        public int DoctorId { get; set; }

        [Required]
        public int PatientId { get; set; }

        // 治療案件ID，允許為空
        public int? TreatmentId { get; set; }

        [StringLength(500)]
        public string? Description { get; set; }

        [StringLength(50)]
        public string? BackgroundColor { get; set; } = "#3788d8";

        [StringLength(50)]
        public string? BorderColor { get; set; }

        public bool IsActive { get; set; } = true;

        // 重複設定
        public string? RepeatGroupId { get; set; } // 重複群組ID，用於關聯重複的行程

        public int RepeatType { get; set; } = 0; // 0=不重複, 1=每日, 2=每週, 3=每月

        public int? RepeatSequence { get; set; } // 在重複序列中的順序

        public string? GoogleCalendarEventId { get; set; } // Google Calendar 事件 ID

        public DateTime CreatedAt { get; set; } = DateTime.Now;

        public DateTime UpdatedAt { get; set; } = DateTime.Now;

        public int CreatedBy { get; set; }

        public int UpdatedBy { get; set; }

        // Navigation properties
        [ForeignKey("DoctorId")]
        public virtual User? Doctor { get; set; }

        [ForeignKey("PatientId")]
        public virtual Patient? Patient { get; set; }

        [ForeignKey("TreatmentId")]
        public virtual Treatment? Treatment { get; set; }

        [ForeignKey("CreatedBy")]
        public virtual User? Creator { get; set; }

        [ForeignKey("UpdatedBy")]
        public virtual User? Updater { get; set; }
    }
}

{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\demo-git\\\\demo-react\\\\my-fullstack-app\\\\frontend\\\\src\\\\components\\\\Page\\\\PermissionPage.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useCallback } from 'react';\nimport { Dropdown } from 'primereact/dropdown';\nimport { Checkbox } from 'primereact/checkbox';\nimport { Button } from 'primereact/button';\nimport { Toast } from 'primereact/toast';\nimport { Card } from 'primereact/card';\nimport { classNames } from 'primereact/utils';\nimport api from '../../services/api';\n\n// Type Definitions\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst DEFAULT_PERMISSIONS = {\n  canRead: true,\n  canWrite: false,\n  canDelete: false,\n  canExport: false,\n  canImport: false\n};\nconst PermissionPage = () => {\n  _s();\n  const {\n    toast,\n    showToast\n  } = useToast();\n  const [roles, setRoles] = useState([]);\n  const [selectedRole, setSelectedRole] = useState(null);\n  const [menuGroups, setMenuGroups] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const fetchRoles = useCallback(async () => {\n    try {\n      const response = await api.get('/api/RoleMenu/GetRoles');\n      setRoles(response.data);\n    } catch (error) {\n      var _error$response, _error$response$data;\n      showToast('error', '獲取角色列表失敗', ((_error$response = error.response) === null || _error$response === void 0 ? void 0 : (_error$response$data = _error$response.data) === null || _error$response$data === void 0 ? void 0 : _error$response$data.message) || error.message);\n    }\n  }, [showToast]);\n  useEffect(() => {\n    fetchRoles();\n  }, [fetchRoles]);\n  const fetchRolePermissions = useCallback(async roleId => {\n    setLoading(true);\n    try {\n      const response = await api.get(`/api/RoleMenu/GetRoleMenus/${roleId}`);\n      setMenuGroups(response.data.menuGroups || []);\n    } catch (error) {\n      var _error$response2, _error$response2$data;\n      showToast('error', '獲取權限失敗', ((_error$response2 = error.response) === null || _error$response2 === void 0 ? void 0 : (_error$response2$data = _error$response2.data) === null || _error$response2$data === void 0 ? void 0 : _error$response2$data.message) || error.message);\n      setMenuGroups([]);\n    } finally {\n      setLoading(false);\n    }\n  }, [showToast]);\n  useEffect(() => {\n    if (selectedRole) {\n      fetchRolePermissions(selectedRole);\n    } else {\n      setMenuGroups([]);\n    }\n  }, [selectedRole, fetchRolePermissions]);\n  const handlePermissionChange = (menuId, permissionKey, isChecked) => {\n    setMenuGroups(prevGroups => prevGroups.map(group => ({\n      ...group,\n      menus: group.menus.map(menu => {\n        if (menu.menuId === menuId) {\n          var _menu$permissions;\n          const currentPermissions = (_menu$permissions = menu.permissions) !== null && _menu$permissions !== void 0 ? _menu$permissions : {\n            ...DEFAULT_PERMISSIONS,\n            canRead: menu.isAssigned\n          };\n          const newPermissions = {\n            ...currentPermissions,\n            [permissionKey]: isChecked\n          };\n          const newIsAssigned = Object.values(newPermissions).some(p => p === true);\n          return {\n            ...menu,\n            permissions: newPermissions,\n            isAssigned: newIsAssigned\n          };\n        }\n        return menu;\n      })\n    })));\n  };\n  const handleSaveChanges = async () => {\n    if (!selectedRole) return;\n    setLoading(true);\n    const payload = {\n      roleId: selectedRole,\n      menuPermissions: menuGroups.flatMap(group => group.menus.filter(menu => menu.isAssigned && menu.permissions).map(menu => ({\n        menuId: menu.menuId,\n        ...(menu.permissions || DEFAULT_PERMISSIONS)\n      })))\n    };\n    try {\n      await api.post('/api/RoleMenu/UpdateRoleMenus', payload);\n      showToast('success', '權限更新成功');\n      fetchRolePermissions(selectedRole);\n    } catch (error) {\n      var _error$response3, _error$response3$data;\n      showToast('error', '權限更新失敗', ((_error$response3 = error.response) === null || _error$response3 === void 0 ? void 0 : (_error$response3$data = _error$response3.data) === null || _error$response3$data === void 0 ? void 0 : _error$response3$data.message) || error.message);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const renderPermissionCheckbox = (menu, pKey) => {\n    const permissionKey = pKey;\n    const isChecked = menu.permissions ? menu.permissions[permissionKey] : false;\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: classNames('p-col-6 p-md-2'),\n      children: [/*#__PURE__*/_jsxDEV(Checkbox, {\n        inputId: `${menu.menuId}-${permissionKey}`,\n        checked: isChecked,\n        onChange: e => handlePermissionChange(menu.menuId, permissionKey, !!e.checked),\n        disabled: !menu.isAssigned\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 147,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n        htmlFor: `${menu.menuId}-${permissionKey}`,\n        className: \"p-ml-2\",\n        children: permissionKey.replace('can', '')\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 153,\n        columnNumber: 17\n      }, this)]\n    }, permissionKey, true, {\n      fileName: _jsxFileName,\n      lineNumber: 146,\n      columnNumber: 13\n    }, this);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"p-grid p-fluid\",\n    children: [/*#__PURE__*/_jsxDEV(Toast, {\n      ref: toast\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 162,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"p-col-12\",\n      children: /*#__PURE__*/_jsxDEV(Card, {\n        title: \"\\u6B0A\\u9650\\u7BA1\\u7406\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-field p-grid mb-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            htmlFor: \"role\",\n            className: \"p-col-12 p-md-2\",\n            children: \"\\u9078\\u64C7\\u89D2\\u8272\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 166,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"p-col-12 p-md-10\",\n            children: /*#__PURE__*/_jsxDEV(Dropdown, {\n              id: \"role\",\n              value: selectedRole,\n              options: roles,\n              onChange: e => setSelectedRole(e.value),\n              optionLabel: \"name\",\n              optionValue: \"id\",\n              placeholder: \"\\u8ACB\\u9078\\u64C7\\u4E00\\u500B\\u89D2\\u8272\",\n              style: {\n                width: '100%'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 168,\n              columnNumber: 29\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 167,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 165,\n          columnNumber: 21\n        }, this), menuGroups.map(group => /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-field mb-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n            className: \"text-lg font-bold mb-2\",\n            children: group.groupName\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 183,\n            columnNumber: 29\n          }, this), group.menus.map(menu => /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"p-grid p-align-center ml-2 mb-2\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"p-col-12 p-md-3\",\n              children: /*#__PURE__*/_jsxDEV(\"strong\", {\n                children: menu.name\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 187,\n                columnNumber: 41\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 186,\n              columnNumber: 37\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"p-col-12 p-md-9 p-grid\",\n              children: [renderPermissionCheckbox(menu, 'canRead'), renderPermissionCheckbox(menu, 'canWrite'), renderPermissionCheckbox(menu, 'canDelete'), renderPermissionCheckbox(menu, 'canExport'), renderPermissionCheckbox(menu, 'canImport')]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 189,\n              columnNumber: 37\n            }, this)]\n          }, menu.menuId, true, {\n            fileName: _jsxFileName,\n            lineNumber: 185,\n            columnNumber: 33\n          }, this))]\n        }, group.groupId, true, {\n          fileName: _jsxFileName,\n          lineNumber: 182,\n          columnNumber: 25\n        }, this)), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-d-flex p-jc-end mt-4\",\n          children: /*#__PURE__*/_jsxDEV(Button, {\n            label: \"\\u5132\\u5B58\\u8B8A\\u66F4\",\n            icon: \"pi pi-check\",\n            onClick: handleSaveChanges,\n            disabled: !selectedRole || loading,\n            loading: loading\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 202,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 201,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 164,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 163,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 161,\n    columnNumber: 9\n  }, this);\n};\n_s(PermissionPage, \"rEjSzDkIaphMLq+nqpbuLeBd+zc=\", true);\n_c = PermissionPage;\nexport default PermissionPage;\nvar _c;\n$RefreshReg$(_c, \"PermissionPage\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useCallback", "Dropdown", "Checkbox", "<PERSON><PERSON>", "Toast", "Card", "classNames", "api", "jsxDEV", "_jsxDEV", "DEFAULT_PERMISSIONS", "canRead", "canWrite", "canDelete", "canExport", "canImport", "PermissionPage", "_s", "toast", "showToast", "useToast", "roles", "setRoles", "selectedR<PERSON>", "setSelectedRole", "menuGroups", "setMenuGroups", "loading", "setLoading", "fetchRoles", "response", "get", "data", "error", "_error$response", "_error$response$data", "message", "fetchRolePermissions", "roleId", "_error$response2", "_error$response2$data", "handlePermissionChange", "menuId", "<PERSON><PERSON><PERSON>", "isChecked", "prevGroups", "map", "group", "menus", "menu", "_menu$permissions", "currentPermissions", "permissions", "isAssigned", "newPermissions", "newIsAssigned", "Object", "values", "some", "p", "handleSaveChanges", "payload", "menuPermissions", "flatMap", "filter", "post", "_error$response3", "_error$response3$data", "renderPermissionCheckbox", "p<PERSON>ey", "className", "children", "inputId", "checked", "onChange", "e", "disabled", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "htmlFor", "replace", "ref", "title", "id", "value", "options", "optionLabel", "optionValue", "placeholder", "style", "width", "groupName", "name", "groupId", "label", "icon", "onClick", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/demo-git/demo-react/my-fullstack-app/frontend/src/components/Page/PermissionPage.tsx"], "sourcesContent": ["import React, { useState, useEffect, useCallback, useRef } from 'react';\nimport { Dropdown, DropdownChangeEvent } from 'primereact/dropdown';\nimport { Checkbox, CheckboxChangeEvent } from 'primereact/checkbox';\nimport { Button } from 'primereact/button';\nimport { Toast } from 'primereact/toast';\nimport { Card } from 'primereact/card';\nimport { classNames } from 'primereact/utils';\nimport api from '../../services/api';\n\n// Type Definitions\ninterface Role {\n    id: number;\n    name: string;\n}\n\ninterface Permissions {\n    canRead: boolean;\n    canWrite: boolean;\n    canDelete: boolean;\n    canExport: boolean;\n    canImport: boolean;\n}\n\ninterface Menu {\n    menuId: number;\n    path: string;\n    name: string;\n    isAssigned: boolean;\n    permissions: Permissions | null;\n}\n\ninterface MenuGroup {\n    groupId: number;\n    groupName: string;\n    groupIcon: string;\n    menus: Menu[];\n}\n\nconst DEFAULT_PERMISSIONS: Permissions = {\n    canRead: true,\n    canWrite: false,\n    canDelete: false,\n    canExport: false,\n    canImport: false,\n};\n\nconst PermissionPage: React.FC = () => {\n    const { toast, showToast } = useToast();\n    const [roles, setRoles] = useState<Role[]>([]);\n    const [selectedRole, setSelectedRole] = useState<number | null>(null);\n    const [menuGroups, setMenuGroups] = useState<MenuGroup[]>([]);\n    const [loading, setLoading] = useState(false);\n\n    const fetchRoles = useCallback(async () => {\n        try {\n            const response = await api.get<Role[]>('/api/RoleMenu/GetRoles');\n            setRoles(response.data);\n        } catch (error: any) {\n            showToast('error', '獲取角色列表失敗', error.response?.data?.message || error.message);\n        }\n    }, [showToast]);\n\n    useEffect(() => {\n        fetchRoles();\n    }, [fetchRoles]);\n\n    const fetchRolePermissions = useCallback(async (roleId: number) => {\n        setLoading(true);\n        try {\n            const response = await api.get<{ menuGroups: MenuGroup[] }>(`/api/RoleMenu/GetRoleMenus/${roleId}`);\n            setMenuGroups(response.data.menuGroups || []);\n        } catch (error: any) {\n            showToast('error', '獲取權限失敗', error.response?.data?.message || error.message);\n            setMenuGroups([]);\n        } finally {\n            setLoading(false);\n        }\n    }, [showToast]);\n\n    useEffect(() => {\n        if (selectedRole) {\n            fetchRolePermissions(selectedRole);\n        } else {\n            setMenuGroups([]);\n        }\n    }, [selectedRole, fetchRolePermissions]);\n\n    const handlePermissionChange = (menuId: number, permissionKey: keyof Permissions, isChecked: boolean) => {\n        setMenuGroups(prevGroups =>\n            prevGroups.map(group => ({\n                ...group,\n                menus: group.menus.map(menu => {\n                    if (menu.menuId === menuId) {\n                        const currentPermissions = menu.permissions ?? { ...DEFAULT_PERMISSIONS, canRead: menu.isAssigned };\n                        const newPermissions: Permissions = {\n                            ...currentPermissions,\n                            [permissionKey]: isChecked,\n                        };\n                        \n                        const newIsAssigned = Object.values(newPermissions).some(p => p === true);\n\n                        return {\n                            ...menu,\n                            permissions: newPermissions,\n                            isAssigned: newIsAssigned,\n                        };\n                    }\n                    return menu;\n                }),\n            }))\n        );\n    };\n    \n    const handleSaveChanges = async () => {\n        if (!selectedRole) return;\n        setLoading(true);\n\n        const payload = {\n            roleId: selectedRole,\n            menuPermissions: menuGroups.flatMap(group =>\n                group.menus\n                    .filter(menu => menu.isAssigned && menu.permissions)\n                    .map(menu => ({\n                        menuId: menu.menuId,\n                        ...(menu.permissions || DEFAULT_PERMISSIONS)\n                    }))\n            ),\n        };\n\n        try {\n            await api.post('/api/RoleMenu/UpdateRoleMenus', payload);\n            showToast('success', '權限更新成功');\n            fetchRolePermissions(selectedRole);\n        } catch (error: any) {\n            showToast('error', '權限更新失敗', error.response?.data?.message || error.message);\n        } finally {\n            setLoading(false);\n        }\n    };\n\n    const renderPermissionCheckbox = (menu: Menu, pKey: string) => {\n        const permissionKey = pKey as keyof Permissions;\n        const isChecked = menu.permissions ? menu.permissions[permissionKey] : false;\n\n        return (\n            <div key={permissionKey} className={classNames('p-col-6 p-md-2')}>\n                <Checkbox\n                    inputId={`${menu.menuId}-${permissionKey}`}\n                    checked={isChecked}\n                    onChange={(e: CheckboxChangeEvent) => handlePermissionChange(menu.menuId, permissionKey, !!e.checked)}\n                    disabled={!menu.isAssigned}\n                />\n                <label htmlFor={`${menu.menuId}-${permissionKey}`} className=\"p-ml-2\">\n                    {permissionKey.replace('can', '')}\n                </label>\n            </div>\n        );\n    };\n\n    return (\n        <div className=\"p-grid p-fluid\">\n            <Toast ref={toast} />\n            <div className=\"p-col-12\">\n                <Card title=\"權限管理\">\n                    <div className=\"p-field p-grid mb-4\">\n                        <label htmlFor=\"role\" className=\"p-col-12 p-md-2\">選擇角色</label>\n                        <div className=\"p-col-12 p-md-10\">\n                            <Dropdown\n                                id=\"role\"\n                                value={selectedRole}\n                                options={roles}\n                                onChange={(e: DropdownChangeEvent) => setSelectedRole(e.value)}\n                                optionLabel=\"name\"\n                                optionValue=\"id\"\n                                placeholder=\"請選擇一個角色\"\n                                style={{ width: '100%' }}\n                            />\n                        </div>\n                    </div>\n\n                    {menuGroups.map(group => (\n                        <div key={group.groupId} className=\"p-field mb-4\">\n                            <h5 className=\"text-lg font-bold mb-2\">{group.groupName}</h5>\n                            {group.menus.map(menu => (\n                                <div key={menu.menuId} className=\"p-grid p-align-center ml-2 mb-2\">\n                                    <div className=\"p-col-12 p-md-3\">\n                                        <strong>{menu.name}</strong>\n                                    </div>\n                                    <div className=\"p-col-12 p-md-9 p-grid\">\n                                        {renderPermissionCheckbox(menu, 'canRead')}\n                                        {renderPermissionCheckbox(menu, 'canWrite')}\n                                        {renderPermissionCheckbox(menu, 'canDelete')}\n                                        {renderPermissionCheckbox(menu, 'canExport')}\n                                        {renderPermissionCheckbox(menu, 'canImport')}\n                                    </div>\n                                </div>\n                            ))}\n                        </div>\n                    ))}\n\n                    <div className=\"p-d-flex p-jc-end mt-4\">\n                        <Button\n                            label=\"儲存變更\"\n                            icon=\"pi pi-check\"\n                            onClick={handleSaveChanges}\n                            disabled={!selectedRole || loading}\n                            loading={loading}\n                        />\n                    </div>\n                </Card>\n            </div>\n        </div>\n    );\n};\n\nexport default PermissionPage;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,WAAW,QAAgB,OAAO;AACvE,SAASC,QAAQ,QAA6B,qBAAqB;AACnE,SAASC,QAAQ,QAA6B,qBAAqB;AACnE,SAASC,MAAM,QAAQ,mBAAmB;AAC1C,SAASC,KAAK,QAAQ,kBAAkB;AACxC,SAASC,IAAI,QAAQ,iBAAiB;AACtC,SAASC,UAAU,QAAQ,kBAAkB;AAC7C,OAAOC,GAAG,MAAM,oBAAoB;;AAEpC;AAAA,SAAAC,MAAA,IAAAC,OAAA;AA6BA,MAAMC,mBAAgC,GAAG;EACrCC,OAAO,EAAE,IAAI;EACbC,QAAQ,EAAE,KAAK;EACfC,SAAS,EAAE,KAAK;EAChBC,SAAS,EAAE,KAAK;EAChBC,SAAS,EAAE;AACf,CAAC;AAED,MAAMC,cAAwB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACnC,MAAM;IAAEC,KAAK;IAAEC;EAAU,CAAC,GAAGC,QAAQ,CAAC,CAAC;EACvC,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAGxB,QAAQ,CAAS,EAAE,CAAC;EAC9C,MAAM,CAACyB,YAAY,EAAEC,eAAe,CAAC,GAAG1B,QAAQ,CAAgB,IAAI,CAAC;EACrE,MAAM,CAAC2B,UAAU,EAAEC,aAAa,CAAC,GAAG5B,QAAQ,CAAc,EAAE,CAAC;EAC7D,MAAM,CAAC6B,OAAO,EAAEC,UAAU,CAAC,GAAG9B,QAAQ,CAAC,KAAK,CAAC;EAE7C,MAAM+B,UAAU,GAAG7B,WAAW,CAAC,YAAY;IACvC,IAAI;MACA,MAAM8B,QAAQ,GAAG,MAAMvB,GAAG,CAACwB,GAAG,CAAS,wBAAwB,CAAC;MAChET,QAAQ,CAACQ,QAAQ,CAACE,IAAI,CAAC;IAC3B,CAAC,CAAC,OAAOC,KAAU,EAAE;MAAA,IAAAC,eAAA,EAAAC,oBAAA;MACjBhB,SAAS,CAAC,OAAO,EAAE,UAAU,EAAE,EAAAe,eAAA,GAAAD,KAAK,CAACH,QAAQ,cAAAI,eAAA,wBAAAC,oBAAA,GAAdD,eAAA,CAAgBF,IAAI,cAAAG,oBAAA,uBAApBA,oBAAA,CAAsBC,OAAO,KAAIH,KAAK,CAACG,OAAO,CAAC;IAClF;EACJ,CAAC,EAAE,CAACjB,SAAS,CAAC,CAAC;EAEfpB,SAAS,CAAC,MAAM;IACZ8B,UAAU,CAAC,CAAC;EAChB,CAAC,EAAE,CAACA,UAAU,CAAC,CAAC;EAEhB,MAAMQ,oBAAoB,GAAGrC,WAAW,CAAC,MAAOsC,MAAc,IAAK;IAC/DV,UAAU,CAAC,IAAI,CAAC;IAChB,IAAI;MACA,MAAME,QAAQ,GAAG,MAAMvB,GAAG,CAACwB,GAAG,CAA8B,8BAA8BO,MAAM,EAAE,CAAC;MACnGZ,aAAa,CAACI,QAAQ,CAACE,IAAI,CAACP,UAAU,IAAI,EAAE,CAAC;IACjD,CAAC,CAAC,OAAOQ,KAAU,EAAE;MAAA,IAAAM,gBAAA,EAAAC,qBAAA;MACjBrB,SAAS,CAAC,OAAO,EAAE,QAAQ,EAAE,EAAAoB,gBAAA,GAAAN,KAAK,CAACH,QAAQ,cAAAS,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBP,IAAI,cAAAQ,qBAAA,uBAApBA,qBAAA,CAAsBJ,OAAO,KAAIH,KAAK,CAACG,OAAO,CAAC;MAC5EV,aAAa,CAAC,EAAE,CAAC;IACrB,CAAC,SAAS;MACNE,UAAU,CAAC,KAAK,CAAC;IACrB;EACJ,CAAC,EAAE,CAACT,SAAS,CAAC,CAAC;EAEfpB,SAAS,CAAC,MAAM;IACZ,IAAIwB,YAAY,EAAE;MACdc,oBAAoB,CAACd,YAAY,CAAC;IACtC,CAAC,MAAM;MACHG,aAAa,CAAC,EAAE,CAAC;IACrB;EACJ,CAAC,EAAE,CAACH,YAAY,EAAEc,oBAAoB,CAAC,CAAC;EAExC,MAAMI,sBAAsB,GAAGA,CAACC,MAAc,EAAEC,aAAgC,EAAEC,SAAkB,KAAK;IACrGlB,aAAa,CAACmB,UAAU,IACpBA,UAAU,CAACC,GAAG,CAACC,KAAK,KAAK;MACrB,GAAGA,KAAK;MACRC,KAAK,EAAED,KAAK,CAACC,KAAK,CAACF,GAAG,CAACG,IAAI,IAAI;QAC3B,IAAIA,IAAI,CAACP,MAAM,KAAKA,MAAM,EAAE;UAAA,IAAAQ,iBAAA;UACxB,MAAMC,kBAAkB,IAAAD,iBAAA,GAAGD,IAAI,CAACG,WAAW,cAAAF,iBAAA,cAAAA,iBAAA,GAAI;YAAE,GAAGxC,mBAAmB;YAAEC,OAAO,EAAEsC,IAAI,CAACI;UAAW,CAAC;UACnG,MAAMC,cAA2B,GAAG;YAChC,GAAGH,kBAAkB;YACrB,CAACR,aAAa,GAAGC;UACrB,CAAC;UAED,MAAMW,aAAa,GAAGC,MAAM,CAACC,MAAM,CAACH,cAAc,CAAC,CAACI,IAAI,CAACC,CAAC,IAAIA,CAAC,KAAK,IAAI,CAAC;UAEzE,OAAO;YACH,GAAGV,IAAI;YACPG,WAAW,EAAEE,cAAc;YAC3BD,UAAU,EAAEE;UAChB,CAAC;QACL;QACA,OAAON,IAAI;MACf,CAAC;IACL,CAAC,CAAC,CACN,CAAC;EACL,CAAC;EAED,MAAMW,iBAAiB,GAAG,MAAAA,CAAA,KAAY;IAClC,IAAI,CAACrC,YAAY,EAAE;IACnBK,UAAU,CAAC,IAAI,CAAC;IAEhB,MAAMiC,OAAO,GAAG;MACZvB,MAAM,EAAEf,YAAY;MACpBuC,eAAe,EAAErC,UAAU,CAACsC,OAAO,CAAChB,KAAK,IACrCA,KAAK,CAACC,KAAK,CACNgB,MAAM,CAACf,IAAI,IAAIA,IAAI,CAACI,UAAU,IAAIJ,IAAI,CAACG,WAAW,CAAC,CACnDN,GAAG,CAACG,IAAI,KAAK;QACVP,MAAM,EAAEO,IAAI,CAACP,MAAM;QACnB,IAAIO,IAAI,CAACG,WAAW,IAAI1C,mBAAmB;MAC/C,CAAC,CAAC,CACV;IACJ,CAAC;IAED,IAAI;MACA,MAAMH,GAAG,CAAC0D,IAAI,CAAC,+BAA+B,EAAEJ,OAAO,CAAC;MACxD1C,SAAS,CAAC,SAAS,EAAE,QAAQ,CAAC;MAC9BkB,oBAAoB,CAACd,YAAY,CAAC;IACtC,CAAC,CAAC,OAAOU,KAAU,EAAE;MAAA,IAAAiC,gBAAA,EAAAC,qBAAA;MACjBhD,SAAS,CAAC,OAAO,EAAE,QAAQ,EAAE,EAAA+C,gBAAA,GAAAjC,KAAK,CAACH,QAAQ,cAAAoC,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBlC,IAAI,cAAAmC,qBAAA,uBAApBA,qBAAA,CAAsB/B,OAAO,KAAIH,KAAK,CAACG,OAAO,CAAC;IAChF,CAAC,SAAS;MACNR,UAAU,CAAC,KAAK,CAAC;IACrB;EACJ,CAAC;EAED,MAAMwC,wBAAwB,GAAGA,CAACnB,IAAU,EAAEoB,IAAY,KAAK;IAC3D,MAAM1B,aAAa,GAAG0B,IAAyB;IAC/C,MAAMzB,SAAS,GAAGK,IAAI,CAACG,WAAW,GAAGH,IAAI,CAACG,WAAW,CAACT,aAAa,CAAC,GAAG,KAAK;IAE5E,oBACIlC,OAAA;MAAyB6D,SAAS,EAAEhE,UAAU,CAAC,gBAAgB,CAAE;MAAAiE,QAAA,gBAC7D9D,OAAA,CAACP,QAAQ;QACLsE,OAAO,EAAE,GAAGvB,IAAI,CAACP,MAAM,IAAIC,aAAa,EAAG;QAC3C8B,OAAO,EAAE7B,SAAU;QACnB8B,QAAQ,EAAGC,CAAsB,IAAKlC,sBAAsB,CAACQ,IAAI,CAACP,MAAM,EAAEC,aAAa,EAAE,CAAC,CAACgC,CAAC,CAACF,OAAO,CAAE;QACtGG,QAAQ,EAAE,CAAC3B,IAAI,CAACI;MAAW;QAAAwB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC9B,CAAC,eACFvE,OAAA;QAAOwE,OAAO,EAAE,GAAGhC,IAAI,CAACP,MAAM,IAAIC,aAAa,EAAG;QAAC2B,SAAS,EAAC,QAAQ;QAAAC,QAAA,EAChE5B,aAAa,CAACuC,OAAO,CAAC,KAAK,EAAE,EAAE;MAAC;QAAAL,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC9B,CAAC;IAAA,GATFrC,aAAa;MAAAkC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAUlB,CAAC;EAEd,CAAC;EAED,oBACIvE,OAAA;IAAK6D,SAAS,EAAC,gBAAgB;IAAAC,QAAA,gBAC3B9D,OAAA,CAACL,KAAK;MAAC+E,GAAG,EAAEjE;IAAM;MAAA2D,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACrBvE,OAAA;MAAK6D,SAAS,EAAC,UAAU;MAAAC,QAAA,eACrB9D,OAAA,CAACJ,IAAI;QAAC+E,KAAK,EAAC,0BAAM;QAAAb,QAAA,gBACd9D,OAAA;UAAK6D,SAAS,EAAC,qBAAqB;UAAAC,QAAA,gBAChC9D,OAAA;YAAOwE,OAAO,EAAC,MAAM;YAACX,SAAS,EAAC,iBAAiB;YAAAC,QAAA,EAAC;UAAI;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAC9DvE,OAAA;YAAK6D,SAAS,EAAC,kBAAkB;YAAAC,QAAA,eAC7B9D,OAAA,CAACR,QAAQ;cACLoF,EAAE,EAAC,MAAM;cACTC,KAAK,EAAE/D,YAAa;cACpBgE,OAAO,EAAElE,KAAM;cACfqD,QAAQ,EAAGC,CAAsB,IAAKnD,eAAe,CAACmD,CAAC,CAACW,KAAK,CAAE;cAC/DE,WAAW,EAAC,MAAM;cAClBC,WAAW,EAAC,IAAI;cAChBC,WAAW,EAAC,4CAAS;cACrBC,KAAK,EAAE;gBAAEC,KAAK,EAAE;cAAO;YAAE;cAAAf,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5B;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,EAELvD,UAAU,CAACqB,GAAG,CAACC,KAAK,iBACjBtC,OAAA;UAAyB6D,SAAS,EAAC,cAAc;UAAAC,QAAA,gBAC7C9D,OAAA;YAAI6D,SAAS,EAAC,wBAAwB;YAAAC,QAAA,EAAExB,KAAK,CAAC8C;UAAS;YAAAhB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,EAC5DjC,KAAK,CAACC,KAAK,CAACF,GAAG,CAACG,IAAI,iBACjBxC,OAAA;YAAuB6D,SAAS,EAAC,iCAAiC;YAAAC,QAAA,gBAC9D9D,OAAA;cAAK6D,SAAS,EAAC,iBAAiB;cAAAC,QAAA,eAC5B9D,OAAA;gBAAA8D,QAAA,EAAStB,IAAI,CAAC6C;cAAI;gBAAAjB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAS;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3B,CAAC,eACNvE,OAAA;cAAK6D,SAAS,EAAC,wBAAwB;cAAAC,QAAA,GAClCH,wBAAwB,CAACnB,IAAI,EAAE,SAAS,CAAC,EACzCmB,wBAAwB,CAACnB,IAAI,EAAE,UAAU,CAAC,EAC1CmB,wBAAwB,CAACnB,IAAI,EAAE,WAAW,CAAC,EAC3CmB,wBAAwB,CAACnB,IAAI,EAAE,WAAW,CAAC,EAC3CmB,wBAAwB,CAACnB,IAAI,EAAE,WAAW,CAAC;YAAA;cAAA4B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3C,CAAC;UAAA,GAVA/B,IAAI,CAACP,MAAM;YAAAmC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAWhB,CACR,CAAC;QAAA,GAfIjC,KAAK,CAACgD,OAAO;UAAAlB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAgBlB,CACR,CAAC,eAEFvE,OAAA;UAAK6D,SAAS,EAAC,wBAAwB;UAAAC,QAAA,eACnC9D,OAAA,CAACN,MAAM;YACH6F,KAAK,EAAC,0BAAM;YACZC,IAAI,EAAC,aAAa;YAClBC,OAAO,EAAEtC,iBAAkB;YAC3BgB,QAAQ,EAAE,CAACrD,YAAY,IAAII,OAAQ;YACnCA,OAAO,EAAEA;UAAQ;YAAAkD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEd,CAAC;AAAC/D,EAAA,CAvKID,cAAwB;AAAAmF,EAAA,GAAxBnF,cAAwB;AAyK9B,eAAeA,cAAc;AAAC,IAAAmF,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
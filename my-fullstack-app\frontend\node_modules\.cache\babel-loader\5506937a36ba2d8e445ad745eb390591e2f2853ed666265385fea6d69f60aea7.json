{"ast": null, "code": "const formatRelativeLocale = {\n  lastWeek: \"'mu dheireadh' eeee 'aig' p\",\n  //FIX\n  yesterday: \"'an-dè aig' p\",\n  today: \"'an-diugh aig' p\",\n  tomorrow: \"'a-màireach aig' p\",\n  nextWeek: \"eeee 'aig' p\",\n  other: \"P\"\n};\nexport const formatRelative = (token, _date, _baseDate, _options) => formatRelativeLocale[token];", "map": {"version": 3, "names": ["formatRelativeLocale", "lastWeek", "yesterday", "today", "tomorrow", "nextWeek", "other", "formatRelative", "token", "_date", "_baseDate", "_options"], "sources": ["C:/Users/<USER>/Desktop/demo-git/demo-react/my-fullstack-app/frontend/node_modules/date-fns/locale/gd/_lib/formatRelative.js"], "sourcesContent": ["const formatRelativeLocale = {\n  lastWeek: \"'mu dheireadh' eeee 'aig' p\", //FIX\n  yesterday: \"'an-dè aig' p\",\n  today: \"'an-diugh aig' p\",\n  tomorrow: \"'a-màireach aig' p\",\n  nextWeek: \"eeee 'aig' p\",\n  other: \"P\",\n};\n\nexport const formatRelative = (token, _date, _baseDate, _options) =>\n  formatRelativeLocale[token];\n"], "mappings": "AAAA,MAAMA,oBAAoB,GAAG;EAC3BC,QAAQ,EAAE,6BAA6B;EAAE;EACzCC,SAAS,EAAE,eAAe;EAC1BC,KAAK,EAAE,kBAAkB;EACzBC,QAAQ,EAAE,oBAAoB;EAC9BC,QAAQ,EAAE,cAAc;EACxBC,KAAK,EAAE;AACT,CAAC;AAED,OAAO,MAAMC,cAAc,GAAGA,CAACC,KAAK,EAAEC,KAAK,EAAEC,SAAS,EAAEC,QAAQ,KAC9DX,oBAAoB,CAACQ,KAAK,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
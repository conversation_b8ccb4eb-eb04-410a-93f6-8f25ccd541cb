using Google.Apis.Auth.OAuth2;
using Google.Apis.Calendar.v3;
using Google.Apis.Calendar.v3.Data;
using Google.Apis.Services;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using MyApi.Models;
using System;
using System.Collections.Generic;
using System.IO;
using System.Threading.Tasks;

namespace MyApi.Services
{
    public interface IGoogleCalendarService
    {
        Task<string?> CreateEventAsync(Schedule schedule);
        Task<bool> UpdateEventAsync(Schedule schedule, string googleEventId);
        Task<bool> DeleteEventAsync(string googleEventId);
        Task<Event?> GetEventAsync(string googleEventId);
    }

    public class GoogleCalendarService : IGoogleCalendarService
    {
        private readonly CalendarService? _calendarService;
        private readonly IConfiguration _configuration;
        private readonly ILogger<GoogleCalendarService> _logger;
        private readonly string _calendarId;
        private readonly bool _isEnabled;

        public GoogleCalendarService(IConfiguration configuration, ILogger<GoogleCalendarService> logger)
        {
            _logger = logger;
            _configuration = configuration;
            _calendarId = _configuration["GoogleApiService:CalendarId"] ?? "primary";

            try
            {
                // 初始化 Google Calendar 服務
                _calendarService = InitializeCalendarService();
                _isEnabled = true;
            }
            catch (Exception ex)
            {
                _logger.LogError($"Google Calendar 服務初始化失敗: {ex.Message}");
                _calendarService = null;
                _isEnabled = false;
            }
        }

        private CalendarService InitializeCalendarService()
        {
            try
            {
                GoogleCredential credential;

                // 從配置文件讀取服務帳戶金鑰路徑
                var serviceAccountKeyPath = _configuration["GoogleApiService:ServiceAccountKeyPath"];

                if (!string.IsNullOrEmpty(serviceAccountKeyPath))
                {
                    // 檢查相對路徑和絕對路徑
                    var fullPath = Path.IsPathRooted(serviceAccountKeyPath)
                        ? serviceAccountKeyPath
                        : Path.Combine(Directory.GetCurrentDirectory(), serviceAccountKeyPath);

                    if (File.Exists(fullPath))
                    {
                        // 使用服務帳戶金鑰文件
                        credential = GoogleCredential.FromFile(fullPath)
                            .CreateScoped(CalendarService.Scope.Calendar);
                    }
                    else
                    {
                        throw new FileNotFoundException($"Google Api Service 服務帳戶金鑰文件未找到: {fullPath}");
                    }
                }
                else
                {
                    // 嘗試使用環境變數或默認認證
                    credential = GoogleCredential.GetApplicationDefault()
                        .CreateScoped(CalendarService.Scope.Calendar);
                }

                var applicationName = _configuration["GoogleApiService:ApplicationName"] ?? "MyApi Schedule System";

                return new CalendarService(new BaseClientService.Initializer()
                {
                    HttpClientInitializer = credential,
                    ApplicationName = applicationName,
                });
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"初始化 Google Api Service 服務失敗: {ex.Message}", ex);
            }
        }

        public async Task<string?> CreateEventAsync(Schedule schedule)
        {
            if (!_isEnabled || _calendarService == null)
            {
                return null;
            }

            try
            {
                var calendarEvent = new Event
                {
                    Summary = schedule.Title,
                    Description = schedule.Description,
                    Start = new EventDateTime()
                    {
                        DateTime = schedule.StartDateTime,
                        TimeZone = "Asia/Taipei"
                    },
                    End = new EventDateTime()
                    {
                        DateTime = schedule.EndDateTime,
                        TimeZone = "Asia/Taipei"
                    },
                    ColorId = GetColorIdFromBackground(schedule.BackgroundColor),
                    ExtendedProperties = new Event.ExtendedPropertiesData()
                    {
                        Private__ = new Dictionary<string, string>
                        {
                            ["scheduleId"] = schedule.Id.ToString(),
                            ["doctorId"] = schedule.DoctorId.ToString(),
                            ["patientId"] = schedule.PatientId.ToString(),
                            ["repeatGroupId"] = schedule.RepeatGroupId ?? "",
                            ["repeatType"] = schedule.RepeatType.ToString(),
                            ["repeatSequence"] = schedule.RepeatSequence?.ToString() ?? ""
                        }
                    }
                };

                var request = _calendarService.Events.Insert(calendarEvent, _calendarId);
                var createdEvent = await request.ExecuteAsync();

                return createdEvent.Id;
            }
            catch (Exception ex)
            {
                _logger.LogError($"創建 Google Calendar 事件失敗: {ex.Message}");
                return null;
            }
        }

        public async Task<bool> UpdateEventAsync(Schedule schedule, string googleEventId)
        {
            if (!_isEnabled || _calendarService == null)
            {
                return false;
            }

            try
            {
                // 先獲取現有事件
                var existingEvent = await _calendarService.Events.Get(_calendarId, googleEventId).ExecuteAsync();

                if (existingEvent == null)
                {
                    return false;
                }

                // 更新事件資料
                existingEvent.Summary = schedule.Title;
                existingEvent.Description = schedule.Description;
                existingEvent.Start = new EventDateTime()
                {
                    DateTime = schedule.StartDateTime,
                    TimeZone = "Asia/Taipei"
                };
                existingEvent.End = new EventDateTime()
                {
                    DateTime = schedule.EndDateTime,
                    TimeZone = "Asia/Taipei"
                };
                existingEvent.ColorId = GetColorIdFromBackground(schedule.BackgroundColor);

                // 更新擴展屬性
                if (existingEvent.ExtendedProperties?.Private__ != null)
                {
                    existingEvent.ExtendedProperties.Private__["scheduleId"] = schedule.Id.ToString();
                    existingEvent.ExtendedProperties.Private__["doctorId"] = schedule.DoctorId.ToString();
                    existingEvent.ExtendedProperties.Private__["patientId"] = schedule.PatientId.ToString();
                }

                var request = _calendarService.Events.Update(existingEvent, _calendarId, googleEventId);
                await request.ExecuteAsync();

                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError($"更新 Google Calendar 事件失敗: {ex.Message}");
                return false;
            }
        }

        public async Task<bool> DeleteEventAsync(string googleEventId)
        {
            if (!_isEnabled || _calendarService == null)
            {
                return false;
            }

            try
            {
                var request = _calendarService.Events.Delete(_calendarId, googleEventId);
                await request.ExecuteAsync();
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError($"刪除 Google Calendar 事件失敗: {ex.Message}");
                return false;
            }
        }

        public async Task<Event?> GetEventAsync(string googleEventId)
        {
            if (!_isEnabled || _calendarService == null)
            {
                return null;
            }

            try
            {
                var request = _calendarService.Events.Get(_calendarId, googleEventId);
                return await request.ExecuteAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError($"獲取 Google Calendar 事件失敗: {ex.Message}");
                return null;
            }
        }

        private string GetColorIdFromBackground(string? backgroundColor)
        {
            // 將背景顏色映射到 Google Calendar 的顏色 ID
            return backgroundColor?.ToLower() switch
            {
                "#3788d8" => "1", // 藍色
                "#28a745" => "2", // 綠色
                "#dc3545" => "3", // 紅色
                "#ffc107" => "4", // 黃色
                "#6f42c1" => "5", // 紫色
                "#fd7e14" => "6", // 橙色
                "#20c997" => "7", // 青色
                "#6c757d" => "8", // 灰色
                "#007bff" => "9", // 深藍色
                "#17a2b8" => "10", // 淺藍色
                _ => "1" // 默認藍色
            };
        }

        public void Dispose()
        {
            _calendarService?.Dispose();
        }
    }
}

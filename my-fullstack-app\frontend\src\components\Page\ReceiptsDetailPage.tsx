import { But<PERSON> } from 'primereact/button';
import { Column } from 'primereact/column';
import { ConfirmDialog, confirmDialog } from 'primereact/confirmdialog';
import { DataTable } from 'primereact/datatable';
import { Dropdown } from 'primereact/dropdown';
import { InputNumber } from 'primereact/inputnumber';
import { Toast } from 'primereact/toast';
import React, { useEffect, useRef, useState } from 'react';
import { useLocation } from "react-router-dom";
import api from "../../services/api";
import connection from "../../services/signalr";
import useDataType from "../../hooks/useDataType";

interface Receipt {
  id?: number;
  treatmentItem: string;
  treatmentMoney: number;
  treatmentId: number;
  patientId: number;
  orderNo?: string;
}

const ReceiptsDetailPage: React.FC = () => {
  const location = useLocation();
  const treatment = location.state?.treatment;
  
  const [treatmentItem, setTreatmentItem] = useState('');
  const [treatmentMoney, setTreatmentMoney] = useState<number>(0);
  const [orderNo, setorderNo] = useState<string>('');
  const [isFileCreate, setisFileCreate] = useState<boolean>(false);
  const [DisableCopyBtn, setDisableCopyBtn] = useState(false);
  const toast = useRef<Toast>(null);
  const [receipts, setReceipts] = useState<Receipt[]>([]);
  const { dataType, loading } = useDataType();
  const [progress, setProgress] = useState(0);
  const [message, setMessage] = useState("");

  // Use message to avoid unused variable warning
  console.log('Progress:', progress, 'Message:', message);

  const treatmentItemsUsed = receipts.map(r => r.treatmentItem);

  useEffect(() => {
    setisFileCreate(treatment.receiptUrl? true : false )

    loadReceipts();

    connection
    .start()
    .then(() => {
      console.log("已連線至 SignalR");
      //console.log("連線 ID", connection.connectionId);
    })
    .catch(err => console.error("SignalR 連線失敗:", err));

    connection.on("ReportProgress", (value) => {
      setProgress(value);
    });

    connection.on("ReportFinished", (msg) => {
      setMessage(msg);
    });

    return () => {
      connection.stop();
    };

  }, []);

  const loadReceipts = async () => {

    try {
      const res = await api.get('/api/receipt/', {
        params: {
          Id: treatment.id
        }
      });
      setReceipts(res.data);
      if (res.data.length > 0) {
        setorderNo(res.data[0].orderNo); // 綁定 orderNo
        setDisableCopyBtn(true);
      }
    } catch (err) {
      toast.current?.show({ severity: 'error', summary: '錯誤', detail: '讀取收據資料失敗' });
    }
  };


  const getNo = () => {
    const min = 10000; 
    const max = 99999; 

    return Math.floor(Math.random() * (max - min + 1)) + min;
  };

    const getOptions = (groupId: number) => {
    return dataType.find(group => group.groupId === groupId)?.dataTypes.map(item => ({
      label: item.name,
      value: item.name
    })) || [];
  };

  const addRow = () => {
    if (!treatmentItem || !treatmentMoney) return alert('請填寫完整欄位');
    if (receipts.length >= 4) return alert('最多只能新增 4 筆資料');
    if (treatmentItemsUsed.includes(treatmentItem)) return alert('項目名稱不可重複');

    const newRow: Receipt = {
      id: getNo(),
      treatmentItem,
      treatmentMoney,
      treatmentId: treatment.id,
      patientId: treatment.patientId,
    };
    setReceipts([...receipts, newRow]);
    setTreatmentItem('');
    setTreatmentMoney(0);
  };

  const copyLatestRecord = () => {
    confirmDialog({
      message: '是否複製上一筆收據紀錄',
      header: '複製確認',
      icon: 'pi pi-exclamation-triangle',
      accept: async () => {
        try {
          const response = await api.get(`/api/Receipt/GetLatestRecord/${treatment.patientId}`, {
            method: 'GET',
            headers: {
              'Content-Type': 'application/json',
            },
          });

          if (response) {
            const latestRecords = await response.data;
            // 清空現有的收據記錄
            setReceipts([]);
            // 複製上一筆記錄，但生成新的 ID
            const copiedRecords = latestRecords.map((record: Receipt) => ({
              ...record,
              id: getNo(),
              treatmentId: treatment.id,
              orderNo: undefined // 清空 orderNo，讓它成為新記錄
            }));
            setReceipts(copiedRecords);
            toast.current?.show({ severity: "success", summary: "複製成功", detail: "已複製上一筆收據紀錄" });
          } 
        } catch (error:any) {
          toast.current?.show({ severity: "error", summary: "複製失敗", detail: error.details });
        }
      }
    });
  };

  const deleteRow = (rowData: Receipt) => {
    if(receipts.length == 1){
      toast.current?.show({ severity: 'error', summary: '錯誤', detail: "資料請勿少於一筆" });
    }else{
      const updated = receipts.filter((r) => r.id !== rowData.id);
      setReceipts(updated);
    }
    
  };

  const saveToServer = async () => {
    if(receipts.length == 0){
      toast.current?.show({ severity: 'error', summary: '錯誤', detail: "資料請勿少於一筆" });
      return 
    }

    try {
      if (!orderNo) {
        // 新增模式
        const res = await api.post('/api/receipt/Insert', receipts);
        toast.current?.show({ severity: 'success', summary: '成功', detail: res.data.msg });
        setorderNo(res.data.orderNo); // 取得新的 orderNo
        setDisableCopyBtn(true);
      } else {
        // 更新模式
        const updated = receipts.map(r => ({
          ...r,
          orderNo
        }));
        await api.put('/api/receipt/Update', updated);
        toast.current?.show({ severity: 'success', summary: '成功', detail: '更新完成' });
      }
    } catch (err: any) {
      const detail = err?.response?.data || '儲存失敗';
      toast.current?.show({ severity: 'error', summary: '錯誤', detail });
    }
  };

  const exportToPDF = async () => {
    if(receipts.length == 0){
      toast.current?.show({ severity: 'error', summary: '錯誤', detail: "資料請勿少於一筆" });
      return 
    }

    try {
      const response = await api.get("/api/receipt/ExportReceiptsPdf", {
        params: { 
          TreatmentId: treatment.id,
          orderNo: orderNo,
          connectionId: connection.connectionId
        },
        responseType: 'blob'  // blob 格式取得資料
      });
      
      toast.current?.show({ severity: "success", summary: "成功", detail: "收據製作成功"});

      // 產生blob url
      const file = new Blob([response.data], { type: 'application/pdf' });
      const fileURL = URL.createObjectURL(file);
      setisFileCreate(true)
      // 在新分頁開啟PDF
      window.open(fileURL);
    } 
    catch (error:any) {
        toast.current?.show({ severity: "error", summary: "錯誤", detail: error.message });
    }
  };

  const actionBodyTemplate = (rowData: Receipt) => {
    return (
      <Button
        icon="pi pi-trash"
        className="p-button-danger"
        onClick={() => deleteRow(rowData)}
      />
    );
  };

  const confirm = () => {
      confirmDialog({
          message: '收據開立後就無法修改內容，確定要開立收據嗎？',
          header: '收據開立確認',
          icon: 'pi pi-exclamation-triangle',
          defaultFocus: 'accept',
          acceptLabel: '確定',
          rejectLabel: '取消',
          accept: () => exportToPDF(),
      });
  };

  if (loading) return <p>Loading...</p>;

  const toolbar = (
        <div className="card flex flex-wrap p-fluid">
          <div className="col-6 md:col-2">
            <div className="flex-auto">
              <label className="font-bold block mb-2">治療項目</label>
              <Dropdown
                value={treatmentItem}
                options={getOptions(9).filter(o => !treatmentItemsUsed.includes(o.value))}
                onChange={(e) => setTreatmentItem(e.value)}
                placeholder="請選擇"
              />
            </div>
          </div>
          <div className="col-5 md:col-2">
            <div className="flex-auto">
              <label htmlFor="mile" className="font-bold block mb-2">金額</label>
              <InputNumber 
                value={treatmentMoney}
                onValueChange={(e) => setTreatmentMoney(Number(e.target.value))}
                
              />
            </div>
          </div>
          <div className=" flex flex-wrap col-11 md:col-5">
            <div className="flex col-6 md:col-2">
              <div className="flex-auto">
                <label className="font-bold block mb-2"> </label>
                <Button label="新增" icon="pi pi-plus" onClick={addRow} disabled={receipts.length >= 5} />
              </div>
            </div>
            <div className="flex col-5 md:col-2">
              <div className="flex-auto">
                <label className="font-bold block mb-2"> </label>
                <Button label="複製" icon="pi pi-copy" severity="info" onClick={copyLatestRecord} disabled={DisableCopyBtn} />
              </div>
            </div>
            <div className="flex col-6 md:col-2">
              <div className="flex-auto">
                <label className="font-bold block mb-2"> </label>
                <Button label="儲存" icon="pi pi-save" severity="success" onClick={saveToServer} />
              </div>
            </div>
            { isFileCreate && (<div className="flex col-6 md:col-3">
              <div className="flex-auto">
                <label className="font-bold block mb-2"> </label>
                <Button label="檢視收據" icon="pi pi-file-pdf" severity="secondary" onClick={exportToPDF} /> 
              </div>
            </div>)}
            { !isFileCreate && (<div className="flex col-5 md:col-3">
              <div className="flex-auto">
                <label className="font-bold block mb-2"> </label>
                <Button label="開立收據" icon="pi pi-file-pdf" severity="secondary" onClick={confirm} /> 
              </div>
            </div>) }
            <div className="flex col-5 md:col-2">
              <div className="flex-auto">
                <label className="font-bold block mb-2">報表產生進度</label>
                <progress value={progress} max="100" style={{ width: '100%' }} />
              </div>
            </div>
          </div>
        </div>
  );

return (
    <div className="card">
      <Toast ref={toast} />
      <ConfirmDialog />
      {toolbar}
      <DataTable value={receipts} dataKey="id">
        <Column field="treatmentItem" header="項目" />
        <Column dataType="numeric" field="treatmentMoney" header="金額" />
        <Column body={actionBodyTemplate} header="刪除" />
      </DataTable>
    </div>
  );
};

export default ReceiptsDetailPage;
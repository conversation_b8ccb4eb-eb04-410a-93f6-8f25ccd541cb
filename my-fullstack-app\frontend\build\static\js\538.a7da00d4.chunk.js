"use strict";(self.webpackChunkfrontend=self.webpackChunkfrontend||[]).push([[538],{1576:(e,t,r)=>{r.d(t,{z:()=>v});var n=r(5043),a=r(4052),i=r(1828),l=r(2028),s=r(4504);function o(){return o=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},o.apply(null,arguments)}function d(e){return d="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},d(e)}function c(e){var t=function(e,t){if("object"!=d(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=d(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==d(t)?t:t+""}function p(e,t,r){return(t=c(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}var m={root:function(e){return"indeterminate"===e.props.mode?(0,s.xW)("p-progressbar p-component p-progressbar-indeterminate"):(0,s.xW)("p-progressbar p-component p-progressbar-determinate")},value:"p-progressbar-value p-progressbar-value-animate",label:"p-progressbar-label",container:"p-progressbar-indeterminate-container"},u={value:function(e){var t=e.props,r=Math.max(t.value,2),n=t.value?t.color:"transparent";return"indeterminate"===t.mode?{backgroundColor:t.color}:{width:r+"%",display:"flex",backgroundColor:n}}},h=i.x.extend({defaultProps:{__TYPE:"ProgressBar",__parentMetadata:null,id:null,value:null,showValue:!0,unit:"%",style:null,className:null,mode:"determinate",displayValueTemplate:null,color:null,children:void 0},css:{classes:m,styles:"\n@layer primereact {\n  .p-progressbar {\n      position: relative;\n      overflow: hidden;\n  }\n  \n  .p-progressbar-determinate .p-progressbar-value {\n      height: 100%;\n      width: 0%;\n      position: absolute;\n      display: none;\n      border: 0 none;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      overflow: hidden;\n  }\n  \n  .p-progressbar-determinate .p-progressbar-label {\n      display: inline-flex;\n  }\n  \n  .p-progressbar-determinate .p-progressbar-value-animate {\n      transition: width 1s ease-in-out;\n  }\n  \n  .p-progressbar-indeterminate .p-progressbar-value::before {\n        content: '';\n        position: absolute;\n        background-color: inherit;\n        top: 0;\n        left: 0;\n        bottom: 0;\n        will-change: left, right;\n        -webkit-animation: p-progressbar-indeterminate-anim 2.1s cubic-bezier(0.65, 0.815, 0.735, 0.395) infinite;\n                animation: p-progressbar-indeterminate-anim 2.1s cubic-bezier(0.65, 0.815, 0.735, 0.395) infinite;\n  }\n  \n  .p-progressbar-indeterminate .p-progressbar-value::after {\n      content: '';\n      position: absolute;\n      background-color: inherit;\n      top: 0;\n      left: 0;\n      bottom: 0;\n      will-change: left, right;\n      -webkit-animation: p-progressbar-indeterminate-anim-short 2.1s cubic-bezier(0.165, 0.84, 0.44, 1) infinite;\n              animation: p-progressbar-indeterminate-anim-short 2.1s cubic-bezier(0.165, 0.84, 0.44, 1) infinite;\n      -webkit-animation-delay: 1.15s;\n              animation-delay: 1.15s;\n  }\n}\n\n@-webkit-keyframes p-progressbar-indeterminate-anim {\n  0% {\n    left: -35%;\n    right: 100%; }\n  60% {\n    left: 100%;\n    right: -90%; }\n  100% {\n    left: 100%;\n    right: -90%; }\n}\n@keyframes p-progressbar-indeterminate-anim {\n  0% {\n    left: -35%;\n    right: 100%; }\n  60% {\n    left: 100%;\n    right: -90%; }\n  100% {\n    left: 100%;\n    right: -90%; }\n}\n\n@-webkit-keyframes p-progressbar-indeterminate-anim-short {\n  0% {\n    left: -200%;\n    right: 100%; }\n  60% {\n    left: 107%;\n    right: -8%; }\n  100% {\n    left: 107%;\n    right: -8%; }\n}\n@keyframes p-progressbar-indeterminate-anim-short {\n  0% {\n    left: -200%;\n    right: 100%; }\n  60% {\n    left: 107%;\n    right: -8%; }\n  100% {\n    left: 107%;\n    right: -8%; }\n}\n",inlineStyles:u}});function b(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}var v=n.memo(n.forwardRef((function(e,t){var r=(0,l.qV)(),d=n.useContext(a.UM),c=h.getProps(e,d),m=h.setMetaData(function(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?b(Object(r),!0).forEach((function(t){p(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):b(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}({props:c},c.__parentMetadata)),u=m.ptm,v=m.cx,f=m.isUnstyled;(0,i.j)(h.css.styles,f,{name:"progressbar"});var y=n.useRef(null);if(n.useImperativeHandle(t,(function(){return{props:c,getElement:function(){return y.current}}})),"determinate"===c.mode)return function(){var e=c.showValue&&null!=c.value?c.displayValueTemplate?c.displayValueTemplate(c.value):c.value+c.unit:null,t=r({className:(0,s.xW)(c.className,v("root")),style:c.style,role:"progressbar","aria-valuemin":"0","aria-valuenow":c.value,"aria-valuemax":"100"},h.getOtherProps(c),u("root")),a=r({className:v("value"),style:{width:c.value+"%",display:"flex",backgroundColor:c.color}},u("value")),i=r({className:v("label")},u("label"));return n.createElement("div",o({id:c.id,ref:y},t),n.createElement("div",a,null!=e&&n.createElement("div",i,e)))}();if("indeterminate"===c.mode)return function(){var e=r({className:(0,s.xW)(c.className,v("root")),style:c.style,role:"progressbar","aria-valuemin":"0","aria-valuenow":c.value,"aria-valuemax":"100"},h.getOtherProps(c),u("root")),t=r({className:v("container")},u("container")),a=r({className:v("value"),style:{backgroundColor:c.color}},u("value"));return n.createElement("div",o({id:c.id,ref:y},e),n.createElement("div",t,n.createElement("div",a)))}();throw new Error(c.mode+" is not a valid mode for the ProgressBar. Valid values are 'determinate' and 'indeterminate'")})));v.displayName="ProgressBar"},8685:(e,t,r)=>{r.d(t,{A:()=>n});const n=(new(r(7127).$)).withUrl("https://nbhphysical.idv.tw/reportHub").withAutomaticReconnect().build()},9538:(e,t,r)=>{r.r(t),r.d(t,{default:()=>w});var n=r(5855),a=r(2018),i=r(5371),l=r(9642),s=r(1104),o=r(1063),d=r(5797),c=r(8060),p=r(2052),m=r(1576),u=r(5556),h=r(828),b=r(5043),v=r(5666),f=r(724),y=r(402);var g=r(8685),x=r(8150),j=r(579);const w=()=>{const e=(0,v.Zp)(),t=(0,b.useRef)(null),[r,w]=(0,b.useState)(""),[N,S]=(0,b.useState)(""),[O,k]=(0,b.useState)(void 0),[C,P]=(0,b.useState)(void 0),[E,V]=(0,b.useState)(0),[A,I]=(0,b.useState)({name:"",nationalId:"",starttime:null,endtime:null,refreshKey:0}),[R,M]=(0,b.useState)([]),[D,T]=(0,b.useState)(!1),[$,z]=(0,b.useState)(0),[F,U]=(0,b.useState)(0),[H,_]=(0,b.useState)(!1),[B,L]=(0,b.useState)(!1),[W,Z]=(0,b.useState)(null),[G,Y]=(0,b.useState)(""),[K,q]=(0,b.useState)(!1),{receipts:J,loading:Q}=function(e,t,r,n,a){const[i,l]=(0,b.useState)([]),[s,o]=(0,b.useState)(!0);return(0,b.useEffect)((()=>{(async()=>{o(!0),y.A.get("/api/receipt/GetList",{params:{patientname:e,nationalId:t,starttime:null===r||void 0===r?void 0:r.toISOString(),endtime:null===n||void 0===n?void 0:n.toISOString()}}).then((e=>l(e.data))).catch((e=>console.error("API Error:",e))).finally((()=>o(!1)))})()}),[e,t,r,n,a]),{receipts:i,loading:s}}(A.name,A.nationalId,A.starttime,A.endtime,E),X={1:"\u7537\u6027",2:"\u5973\u6027",3:"\u5176\u4ed6"};(0,b.useEffect)((()=>(g.A.start().then((()=>{console.log("\u5df2\u9023\u7dda\u81f3 SignalR")})).catch((e=>console.error("SignalR \u9023\u7dda\u5931\u6557:",e))),g.A.on("ReportProgress",(e=>{U(e)})),g.A.on("ReportFinished",(e=>{U(e)})),()=>{g.A.stop()})),[]);const ee=(0,j.jsx)(a.$,{type:"button",icon:"pi pi-refresh",text:!0,onClick:()=>{V((e=>e+1))}}),te=(0,j.jsx)("div",{}),re=e=>e?(0,n.$)(e,"yyyy/MM/dd HH:mm:ss"):"";return Q?(0,j.jsx)(u.A,{message:"\u8f09\u5165\u6536\u64da\u8cc7\u6599\u4e2d..."}):(0,j.jsxs)("div",{children:[(0,j.jsx)(h.y,{ref:t}),(0,j.jsx)(s.T,{}),(0,j.jsx)(x.Z,{title:"\u6536\u64da\u7ba1\u7406",className:"mb-4",children:(0,j.jsx)("p",{className:"text-600 line-height-3 m-0",children:"\u6536\u64da\u7ba1\u7406\u9801\u9762\uff0c\u53ef\u4ee5\u67e5\u8a62\u3001\u6aa2\u8996\u3001\u88fd\u4f5cPDF\u3001\u767c\u9001Email\u6536\u64da\u8cc7\u6599\u3002"})}),(0,j.jsx)(x.Z,{className:"mb-4",children:(0,j.jsxs)("div",{className:"grid",children:[(0,j.jsx)("div",{className:"col-6 md:col-3",children:(0,j.jsx)(p.S,{id:"name",type:"text",value:r,onChange:e=>w(e.target.value),className:"w-full",placeholder:"\u75c5\u60a3\u59d3\u540d"})}),(0,j.jsx)("div",{className:"col-6 md:col-3",children:(0,j.jsx)(p.S,{id:"nationalId",type:"text",value:N,onChange:e=>S(e.target.value),className:"w-full",placeholder:"\u75c5\u60a3\u8eab\u5206\u8b49"})}),(0,j.jsx)("div",{className:"col-6 md:col-3",children:(0,j.jsx)(i.V,{id:"starttime",value:O,onChange:e=>k(e.value),placeholder:"\u958b\u59cb\u6642\u9593",className:"w-full",dateFormat:"yy/mm/dd",showIcon:!0})}),(0,j.jsx)("div",{className:"col-6 md:col-3",children:(0,j.jsx)(i.V,{id:"endtime",value:C,onChange:e=>P(e.value),placeholder:"\u7d50\u675f\u6642\u9593",className:"w-full",dateFormat:"yy/mm/dd",showIcon:!0})}),(0,j.jsx)("div",{className:"col-12 md:col-4",children:(0,j.jsxs)("div",{className:"flex gap-2",children:[(0,j.jsx)(a.$,{label:"\u67e5\u8a62",icon:"pi pi-search",onClick:()=>{V(E+1),I({name:r,nationalId:N,starttime:O,endtime:C,refreshKey:E})}}),(0,j.jsx)(a.$,{label:"\u6279\u91cf\u532f\u51fa",icon:"pi pi-download",onClick:()=>{var e;0!==R.length?T(!0):null===(e=t.current)||void 0===e||e.show({severity:"error",summary:"\u932f\u8aa4",detail:"\u8acb\u52fe\u9078\u6b32\u532f\u51fa\u7684\u6536\u64da"})},className:"p-button-success",disabled:0===R.length})]})})]})}),(0,j.jsx)(x.Z,{children:(0,j.jsxs)(o.b,{value:J,selection:R,onSelectionChange:e=>M(e.value),paginator:!0,rows:10,rowsPerPageOptions:[10,20,30,40],emptyMessage:"\u6c92\u6709\u627e\u5230\u6536\u64da\u8cc7\u6599",tableStyle:{minWidth:"50rem"},paginatorLeft:ee,paginatorRight:te,children:[(0,j.jsx)(l.V,{selectionMode:"multiple",headerStyle:{width:"2%"}}),(0,j.jsx)(l.V,{field:"orderNo",header:"\u6848\u865f",style:{width:"5%"}}),(0,j.jsx)(l.V,{field:"receiptOrderNo",header:"\u6536\u64da\u7de8\u865f",style:{width:"5%"}}),(0,j.jsx)(l.V,{field:"patientName",header:"\u75c5\u60a3\u59d3\u540d",style:{width:"5%"}}),(0,j.jsx)(l.V,{field:"patientGender",header:"\u6027\u5225",style:{width:"3%"},body:e=>{var t=String(e.patientGender);const r=X[t];return(0,j.jsx)("div",{children:r})}}),(0,j.jsx)(l.V,{field:"patientBirthDate",header:"\u5e74\u9f61",style:{width:"3%"},body:e=>(e=>{if(!e)return"";const t=new Date(e),r=new Date;let n=r.getFullYear()-t.getFullYear();return(r.getMonth()<t.getMonth()||r.getMonth()===t.getMonth()&&r.getDate()<t.getDate())&&n--,n})(e.patientBirthDate)}),(0,j.jsx)(l.V,{field:"receiptCreatedAt",header:"\u65b0\u589e\u65e5\u671f",style:{width:"8%"},body:e=>re(e.receiptCreatedAt)}),(0,j.jsx)(l.V,{field:"receiptUpdatedAt",header:"\u66f4\u65b0\u65e5\u671f",style:{width:"8%"},body:e=>re(e.receiptUpdatedAt)}),(0,j.jsx)(l.V,{field:"receiptOperatorUserName",header:"\u64cd\u4f5c\u4eba",style:{width:"5%"}}),(0,j.jsx)(l.V,{field:"Option",header:"\u529f\u80fd",style:{width:"12%"},body:t=>(0,j.jsxs)("div",{className:"flex gap-2",children:[(0,j.jsx)(a.$,{label:"\u6aa2\u8996",type:"button",icon:"pi pi-file-edit",onClick:()=>e(f.bw.RECEIPT_DETAIL,{state:{treatment:t}}),size:"small",severity:"info",style:{fontSize:"1rem",margin:"3px"}}),(0,j.jsx)(a.$,{label:"\u9001\u4fe1",type:"button",icon:"pi pi-send",onClick:()=>{return Z(e=t),Y(e.patientEmail),void L(!0);var e},size:"small",severity:"success",style:{fontSize:"1rem",margin:"3px"},disabled:!t.patientEmail})]})})]})}),(0,j.jsxs)(d.l,{header:"\u6279\u91cf\u532f\u51fa\u6536\u64da",visible:D,style:{width:"50vw"},onHide:()=>T(!1),modal:!0,children:[(0,j.jsxs)("div",{className:"mb-4",children:[(0,j.jsxs)("h5",{children:["\u5df2\u9078\u64c7\u7684\u6536\u64da (",R.length," \u7b46)"]}),(0,j.jsxs)(o.b,{value:R,scrollable:!0,scrollHeight:"300px",emptyMessage:"\u6c92\u6709\u9078\u64c7\u7684\u6536\u64da",children:[(0,j.jsx)(l.V,{field:"orderNo",header:"\u6848\u865f",style:{width:"15%"}}),(0,j.jsx)(l.V,{field:"receiptorderNo",header:"\u6536\u64da\u7de8\u865f",style:{width:"15%"}}),(0,j.jsx)(l.V,{field:"patientName",header:"\u75c5\u60a3\u59d3\u540d",style:{width:"15%"}}),(0,j.jsx)(l.V,{field:"receiptCreatedAt",header:"\u65b0\u589e\u65e5\u671f",style:{width:"20%"},body:e=>re(e.receiptCreatedAt)})]})]}),(0,j.jsxs)("div",{className:"grid align-items-end",children:[(0,j.jsxs)("div",{className:"col-12 md:col-4",children:[(0,j.jsx)("label",{htmlFor:"titleCode",className:"font-bold block mb-2",children:"\u6536\u64da\u985e\u578b"}),(0,j.jsx)(c.m,{id:"titleCode",value:$,options:[{label:"\u5168\u90e8",value:0},{label:"\u7e73\u6b3e\u4eba\u6536\u57f7\u806f",value:1},{label:"\u55ae\u4f4d\u5b58\u6839\u806f",value:2},{label:"\u55ae\u4f4d\u6263\u5e95\u806f",value:3}],onChange:e=>z(e.value),placeholder:"\u9078\u64c7\u6536\u64da\u985e\u578b",className:"w-full"})]}),(0,j.jsx)("div",{className:"col-12 md:col-4",children:(0,j.jsx)(a.$,{label:"\u532f\u51fa",icon:"pi pi-download",onClick:async()=>{var e;if(g.A)try{_(!0),U(0);const e=R.map((e=>e.id)),t={TreatmentId:e,orderNo:R.map((e=>e.receiptOrderNo)),Titlecode:$},r=await y.A.post("/api/receipt/ExportReceiptsLisrPdf",t,{params:{connectionId:g.A.connectionId},responseType:"blob",headers:{"Content-Type":"application/json"}}),n=new Blob([r.data],{type:"application/pdf"}),a=URL.createObjectURL(n);window.open(a),T(!1),M([])}catch(a){var r,n;_(!1),U(0),null===(r=t.current)||void 0===r||r.show({severity:"error",summary:"\u932f\u8aa4",detail:(null===(n=a.response)||void 0===n?void 0:n.data)||"\u532f\u51fa\u5931\u6557"})}else null===(e=t.current)||void 0===e||e.show({severity:"error",summary:"\u932f\u8aa4",detail:"SignalR \u9023\u63a5\u672a\u5efa\u7acb"})},disabled:H,className:"p-button-success w-full"})}),(0,j.jsxs)("div",{className:"col-12 md:col-4",children:[(0,j.jsx)("label",{className:"font-bold block mb-2",children:"\u532f\u51fa\u9032\u5ea6"}),(0,j.jsx)(m.z,{value:F,showValue:!1,style:{height:"1.5rem"}}),(0,j.jsxs)("small",{className:"text-center block mt-1",children:[F,"%"]})]})]})]}),(0,j.jsxs)(d.l,{header:"\u767c\u9001\u6536\u64da\u90f5\u4ef6",visible:B,style:{width:"500px"},onHide:()=>L(!1),modal:!0,children:[(0,j.jsxs)("div",{className:"mb-4",children:[W&&(0,j.jsxs)("div",{className:"p-3 bg-gray-50 border-round mb-3",children:[(0,j.jsx)("h3",{className:"mt-0 mb-2",children:"\u6536\u64da\u8cc7\u8a0a"}),(0,j.jsxs)("p",{className:"mb-1",children:[(0,j.jsx)("strong",{children:"\u6536\u64da\u7de8\u865f:"})," ",W.receiptOrderNo]}),(0,j.jsxs)("p",{className:"mb-1",children:[(0,j.jsx)("strong",{children:"\u75c5\u60a3\u59d3\u540d:"})," ",W.patientName]}),(0,j.jsxs)("p",{className:"mb-0",children:[(0,j.jsx)("strong",{children:"\u5efa\u7acb\u65e5\u671f:"})," ",re(W.receiptCreatedAt)]})]}),(0,j.jsxs)("div",{className:"field",children:[(0,j.jsx)("label",{htmlFor:"recipientEmail",className:"font-bold block mb-2",children:"\u6536\u4ef6\u4eba\u90f5\u7bb1 *"}),(0,j.jsx)(p.S,{id:"recipientEmail",value:G,onChange:e=>Y(e.target.value),placeholder:"\u8acb\u8f38\u5165\u6536\u4ef6\u4eba\u90f5\u7bb1\u5730\u5740",className:"w-full",disabled:K})]})]}),(0,j.jsxs)("div",{className:"flex justify-content-end gap-2",children:[(0,j.jsx)(a.$,{label:"\u53d6\u6d88",icon:"pi pi-times",onClick:()=>L(!1),className:"p-button-secondary",disabled:K}),(0,j.jsx)(a.$,{label:K?"\u767c\u9001\u4e2d...":"\u767c\u9001",icon:K?"pi pi-spin pi-spinner":"pi pi-send",onClick:async()=>{var e;if(!G.trim())return void(null===(e=t.current)||void 0===e||e.show({severity:"error",summary:"\u932f\u8aa4",detail:"\u8acb\u8f38\u5165\u6536\u4ef6\u4eba\u90f5\u7bb1"}));var r;if(/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(G))try{var n;q(!0),await y.A.post("/api/receipt/SendReceiptEmail",{email:G,orderNo:W.receiptOrderNo}),null===(n=t.current)||void 0===n||n.show({severity:"success",summary:"\u6210\u529f",detail:"\u6536\u64da\u90f5\u4ef6\u767c\u9001\u6210\u529f"}),L(!1),Y(""),Z(null)}catch(s){var a,i,l;null===(a=t.current)||void 0===a||a.show({severity:"error",summary:"\u932f\u8aa4",detail:(null===(i=s.response)||void 0===i||null===(l=i.data)||void 0===l?void 0:l.error)||"\u90f5\u4ef6\u767c\u9001\u5931\u6557"})}finally{q(!1)}else null===(r=t.current)||void 0===r||r.show({severity:"error",summary:"\u932f\u8aa4",detail:"\u8acb\u8f38\u5165\u6709\u6548\u7684\u90f5\u7bb1\u5730\u5740"})},disabled:K||!G.trim(),className:"p-button-primary"})]})]})]})}}}]);
//# sourceMappingURL=538.a7da00d4.chunk.js.map
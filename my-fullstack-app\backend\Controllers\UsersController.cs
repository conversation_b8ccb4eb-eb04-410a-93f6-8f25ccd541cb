﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Internal;
using MyApi.Data;
using MyApi.Models;
using MyApi.Services;
using Newtonsoft.Json;
using StackExchange.Redis;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Threading.Tasks;
using System.Xml.Linq;
using static MyApi.Helpers.Enums;
using static QuestPDF.Helpers.Colors;
using static SkiaSharp.HarfBuzz.SKShaper;
using static System.Runtime.InteropServices.JavaScript.JSType;

namespace MyApi.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class UsersController : ControllerBase
    {
        private readonly AppDbContext _context;
        private readonly RedisService _redisService;

        public UsersController(AppDbContext context, RedisService redis)
        {
            _context = context;
            _redisService = redis;
        }

        [Authorize(Roles = "Admin,Manager,User")]
        [HttpGet]
        public IActionResult Get([FromQuery] int id)
        {
            var user = _context.Users
                .Where(t => t.Id == id)
                .FirstOrDefault();

            return Ok(user);
        }

        // 獲取所有角色
        [Authorize(Roles = "Admin,Manager")]
        [HttpGet("GetRoles")]
        public async Task<IActionResult> GetRoles()
        {
            try
            {
                var redisdata = await _redisService.GetStringAsync("RoleList");

                if (redisdata == null)
                {
                    var roles = _context.Roles
                    .Where(r => r.IsEnabled)
                    .OrderBy(r => r.Id)
                    .Select(r => new
                    {
                        Id = r.Id,
                        Name = r.Name
                    })
                    .ToList();

                    var redisvalue = JsonConvert.SerializeObject(roles);

                    await _redisService.SetStringAsync("RoleList", redisvalue);

                    redisdata = redisvalue;
                }

                return Ok(redisdata);
            }
            catch (Exception ex)
            {
                return StatusCode(500, $"獲取角色列表失敗: {ex.Message}");
            }
        }

        // 獲取所有用戶及其角色權限
        [Authorize(Roles = "Admin,Manager")]
        [HttpGet("GetUserRolesList")]
        public IActionResult GetUserRolesList([FromQuery] string name)
        {
            try
            {
                var userRolesQuery = _context.Users
                    .Include(u => u.UserRoles)
                    .ThenInclude(ur => ur.Role)
                    .Where(u => u.IsEnabled)
                    .AsQueryable();

                if (!string.IsNullOrEmpty(name))
                {
                    userRolesQuery = userRolesQuery.Where(u => u.Name.Contains(name));
                }

                var result = userRolesQuery
                    .OrderByDescending(u => u.CreatedAt)
                    .Select(u => new
                    {
                        UserId = u.Id,
                        UserName = u.Name,
                        UserAccount = u.Username,
                        UserEmail = u.Email,
                        UserPhone = u.Phone,
                        Address = u.Address,
                        Gender = u.Gender == Gender.Male ? "男" : u.Gender == Gender.Female ? "女" : "其他",
                        BirthDate = u.BirthDate,
                        IsEnabled = u.IsEnabled,
                        CreatedAt = u.CreatedAt,
                        UpdatedAt = u.UpdatedAt,
                        Roles = u.UserRoles
                            .Where(ur => ur.IsEnabled && ur.Role.IsEnabled)
                            .Select(ur => new
                            {
                                RoleId = ur.RoleId,
                                RoleName = ur.Role.Name
                            }).ToList()
                    })
                    .ToList();

                return Ok(result);
            }
            catch (Exception ex)
            {
                return StatusCode(500, $"獲取用戶角色列表失敗: {ex.Message}");
            }
        }

        // 獲取單一用戶的角色權限
        [Authorize(Roles = "Admin,Manager")]
        [HttpGet("GetUserRoles/{userId}")]
        public IActionResult GetUserRoles(int userId)
        {
            try
            {
                var user = _context.Users
                    .Include(u => u.UserRoles)
                        .ThenInclude(ur => ur.Role)
                    .FirstOrDefault(u => u.Id == userId && u.IsEnabled);

                if (user == null)
                {
                    return NotFound("用戶不存在");
                }

                var result = new
                {
                    UserId = user.Id,
                    UserName = user.Name,
                    UserAccount = user.Username,
                    Roles = user.UserRoles
                        .Where(ur => ur.IsEnabled && ur.Role.IsEnabled)
                        .Select(ur => new
                        {
                            RoleId = ur.RoleId,
                            RoleName = ur.Role.Name
                        }).ToList()
                };

                return Ok(result);
            }
            catch (Exception ex)
            {
                return StatusCode(500, $"獲取用戶角色失敗: {ex.Message}");
            }
        }

        // 更新用戶角色權限
        [Authorize(Roles = "Admin,Manager")]
        [HttpPut("UpdateUserRoles")]
        public async Task<IActionResult> UpdateUserRoles([FromBody] UpdateUserRolesDto data)
        {
            try
            {
                var userId = User.FindFirst("UserId");
                if (userId == null)
                {
                    return Unauthorized("無法獲取當前用戶信息");
                }

                var user = _context.Users.FirstOrDefault(u => u.Id == data.UserId && u.IsEnabled);
                if (user == null)
                {
                    return NotFound("用戶不存在");
                }

                var operatorUser = _context.UserRoles
                    .Include(ur => ur.Role)
                    .Any(ur => ur.UserId == int.Parse(userId.Value) && ur.Role.Name == "Admin" && ur.IsEnabled);

                // 檢查是否嘗試修改 Admin 用戶的權限
                var isAdminUser = _context.UserRoles
                    .Include(ur => ur.Role)
                    .Any(ur => ur.UserId == data.UserId && ur.Role.Name == "Admin" && ur.IsEnabled);

                if (isAdminUser && !operatorUser)
                {
                    return BadRequest("無法修改管理員的角色權限");
                }

                // 停用該用戶的所有現有角色
                var existingUserRoles = _context.UserRoles
                    .Where(ur => ur.UserId == data.UserId)
                    .ToList();

                foreach (var userRole in existingUserRoles)
                {
                    userRole.IsEnabled = false;
                    userRole.UpdatedAt = DateTime.Now;
                    userRole.OperatorUserId = int.Parse(userId.Value);
                }

                // 添加新的角色
                foreach (var roleId in data.RoleIds)
                {
                    // 檢查角色是否存在
                    var role = _context.Roles.FirstOrDefault(r => r.Id == roleId && r.IsEnabled);
                    if (role == null)
                    {
                        return BadRequest($"角色 ID {roleId} 不存在");
                    }

                    // 檢查是否已存在該角色關聯
                    var existingRole = existingUserRoles.FirstOrDefault(ur => ur.RoleId == roleId);
                    if (existingRole != null)
                    {
                        // 重新啟用現有角色
                        existingRole.IsEnabled = true;
                        existingRole.UpdatedAt = DateTime.Now;
                        existingRole.OperatorUserId = int.Parse(userId.Value);
                    }
                    else
                    {
                        // 創建新的角色關聯
                        var newUserRole = new UserRole
                        {
                            UserId = data.UserId,
                            RoleId = roleId,
                            IsEnabled = true,
                            OperatorUserId = int.Parse(userId.Value),
                            CreatedAt = DateTime.Now,
                            UpdatedAt = DateTime.Now
                        };
                        _context.UserRoles.Add(newUserRole);
                    }
                }

                await _context.SaveChangesAsync();

                return Ok("用戶角色權限更新成功");
            }
            catch (Exception ex)
            {
                return StatusCode(500, $"更新用戶角色權限失敗: {ex.Message}");
            }
        }

        [Authorize(Roles = "Admin,Manager,User")]
        [HttpGet("GetList")]
        public IActionResult GetList([FromQuery] string name, int roleId)
        {
            var Users = (
                   from userRole in _context.UserRoles
                   join user in _context.Users on userRole.UserId equals user.Id
                   join role in _context.Roles on userRole.RoleId equals role.Id
                   join operatoruser in _context.Users on userRole.OperatorUserId equals operatoruser.Id
                   where role.Name != "Admin"
                   orderby userRole.CreatedAt descending
                   select new
                   {
                       UserId = user.Id,
                       UserName = user.Name,
                       UserAccount = user.Username,
                       UserEmail = user.Email,
                       UserPhone = user.Phone,
                       Address = user.Address,
                       Gender = user.Gender == Gender.Male ? "男" : user.Gender == Gender.Female ? "女" : "其他",
                       BirthDate = user.BirthDate,
                       IsEnabled = user.IsEnabled,
                       RoleId = userRole.RoleId,
                       RoleName = userRole.Role.Name,
                       CreatedAt = user.CreatedAt,
                       UpdatedAt = user.UpdatedAt,
                       OperatorUserName = operatoruser.Name
                   }
               ).ToList();

            if(!string.IsNullOrEmpty(name))
            {
                Users = Users.Where(t => t.UserName.Contains(name)).ToList();
            }

            if (roleId != 0)
            {
                Users = Users.Where(t => t.RoleId == roleId).ToList();
            }

            return Ok(Users);
        }

        [Authorize(Roles = "Admin,Manager,User")]
        [HttpPost("Insert")]
        public async Task<IActionResult> Insert([FromBody] UserCreateDto userDto)
        {
            var userId = User.FindFirst("UserId");

            try
            {
                // 檢查帳號是否已存在
                var existingUser = _context.Users.FirstOrDefault(u => u.Username == userDto.Username);
                if (existingUser != null)
                {
                    return BadRequest("帳號已存在");
                }

                // 檢查 Email 是否已存在
                if (!string.IsNullOrEmpty(userDto.Email))
                {
                    var existingEmail = _context.Users.FirstOrDefault(u => u.Email == userDto.Email);
                    if (existingEmail != null)
                    {
                        return BadRequest("Email 已存在");
                    }
                }

                // 處理性別欄位
                Gender gender = Gender.Other;
                if (userDto.Gender == "男")
                {
                    gender = Gender.Male;
                }
                else if (userDto.Gender == "女")
                {
                    gender = Gender.Female;
                }

                // 創建新用戶
                var user = new User
                {
                    Name = userDto.Name,
                    Username = userDto.Username,
                    Email = userDto.Email,
                    Phone = userDto.Phone,
                    Address = userDto.Address,
                    Gender = gender,
                    BirthDate = userDto.BirthDate == null ? null : DateTime.Parse(userDto.BirthDate.ToString()).ToLocalTime(),
                    IsEnabled = userDto.IsEnabled,
                    PasswordHash = BCrypt.Net.BCrypt.HashPassword("123456"),
                    CreatedAt = DateTime.Now,
                    UpdatedAt = DateTime.Now
                };

                _context.Users.Add(user);

                await _context.SaveChangesAsync();

                // 創建新用戶腳色
                var userRole = new UserRole
                {
                    UserId = user.Id,
                    RoleId = 3,
                    IsEnabled = true,
                    OperatorUserId = int.Parse(userId.Value),
                    CreatedAt = DateTime.Now,
                    UpdatedAt = DateTime.Now
                };

                _context.UserRoles.Add(userRole);

                await _context.SaveChangesAsync();

                // 清除 Redis 快取
                await _redisService.DeleteKeyAsync("DoctorList");

                return Ok(new { message = "用戶新增成功", userId = user.Id });
            }
            catch (Exception ex)
            {
                return BadRequest($"新增失敗: {ex.Message}");
            }
        }

        [Authorize(Roles = "Admin,Manager,User")]
        [HttpPut("Update")]
        public async Task<IActionResult> Update([FromBody] UserUpdateDto data)
        {
            try
            {
                var user = _context.Users.FirstOrDefault(u => u.Id == data.Id);
                if (user == null)
                {
                    return NotFound("用戶不存在");
                }

                // 檢查帳號是否被其他用戶使用
                var existingUser = _context.Users.FirstOrDefault(u => u.Username == data.Username && u.Id != data.Id);
                if (existingUser != null)
                {
                    return BadRequest("帳號已被其他用戶使用");
                }

                // 檢查 Email 是否被其他用戶使用
                if (!string.IsNullOrEmpty(data.Email))
                {
                    var existingEmail = _context.Users.FirstOrDefault(u => u.Email == data.Email && u.Id != data.Id);
                    if (existingEmail != null)
                    {
                        return BadRequest("Email 已被其他用戶使用");
                    }
                }

                // 處理性別欄位
                Gender gender = Gender.Other;
                if (data.Gender == "男")
                {
                    gender = Gender.Male;
                }
                else if (data.Gender == "女")
                {
                    gender = Gender.Female;
                }

                // 更新用戶資料
                user.Name = data.Name;
                user.Username = data.Username;
                user.Email = data.Email;
                user.Phone = data.Phone;
                user.Address = data.Address;
                user.Gender = gender;
                user.BirthDate = data.BirthDate == null ? null : DateTime.Parse(data.BirthDate.ToString()).ToLocalTime();
                user.IsEnabled = data.IsEnabled;
                user.UpdatedAt = DateTime.Now;

                await _context.SaveChangesAsync();

                // 清除 Redis 快取
                await _redisService.DeleteKeyAsync("DoctorList");

                return Ok(new { message = "用戶更新成功" });
            }
            catch (Exception ex)
            {
                return BadRequest($"更新失敗: {ex.Message}");
            }
        }

        [Authorize(Roles = "Admin,Manager")]
        [HttpDelete("Delete/{id}")]
        public async Task<IActionResult> Delete(int id)
        {
            try
            {
                var user = _context.Users.FirstOrDefault(u => u.Id == id);
                if (user == null)
                {
                    return NotFound("用戶不存在");
                }

                // 檢查是否為 Admin 角色
                var userRole = _context.UserRoles.Include(ur => ur.Role).FirstOrDefault(ur => ur.UserId == id);
                if (userRole?.Role.Name == "Admin")
                {
                    return BadRequest("無法刪除管理員帳號");
                }

                // 檢查是否有關聯的治療記錄
                var hasTreatments = _context.Treatments.Any(t => t.UserId == id);
                if (hasTreatments)
                {
                    return BadRequest("此用戶有關聯的治療記錄，無法刪除");
                }

                // 先刪除用戶角色關聯
                var userRoles = _context.UserRoles.Where(ur => ur.UserId == id);
                _context.UserRoles.RemoveRange(userRoles);

                // 刪除用戶
                _context.Users.Remove(user);
                await _context.SaveChangesAsync();

                // 清除 Redis 快取
                await _redisService.DeleteKeyAsync("DoctorList");

                return Ok(new { message = "用戶刪除成功" });
            }
            catch (Exception ex)
            {
                return BadRequest($"刪除失敗: {ex.Message}");
            }
        }

        [Authorize(Roles = "Admin,Manager")]
        [HttpPost("ResetPassWord")]
        public async Task<IActionResult> ResetPassWord([FromBody] ResetPasswordRequest request)
        {
            try
            {
                var userId = User.FindFirst("UserId");

                var user = _context.Users.SingleOrDefault(u => u.Id == request.UserId);

                if (user == null)
                {
                    return NotFound("用戶不存在");
                }

                // 檢查是否為 Admin 角色
                var userRole = _context.UserRoles.Include(ur => ur.Role).FirstOrDefault(ur => ur.UserId == request.UserId);
                if (userRole?.Role.Name == "Admin")
                {
                    return BadRequest("無法重置管理員密碼");
                }

                user.PasswordHash = BCrypt.Net.BCrypt.HashPassword("123456");
                user.UpdatedAt = DateTime.Now;
                await _context.SaveChangesAsync();

                return Ok(new { message = "密碼重置完成，預設碼為：123456" });
            }
            catch (Exception ex)
            {
                return BadRequest($"密碼重置失敗: {ex.Message}");
            }
        }

        [Authorize(Roles = "Admin,Manager,User,Option")]
        [HttpPost("UpdatePassword")]
        public async Task<IActionResult> UpdatePassword([FromBody] UpdatePasswordRequest request)
        {
            try
            {
                var user = _context.Users.SingleOrDefault(u => u.Id == request.UserId);

                if (user == null)
                {
                    return NotFound("用戶不存在");
                }

                // 驗證舊密碼
                if (!BCrypt.Net.BCrypt.Verify(request.OldPassword, user.PasswordHash))
                {
                    return BadRequest("舊密碼不正確");
                }

                // 檢查新密碼不能與舊密碼相同
                if (BCrypt.Net.BCrypt.Verify(request.NewPassword, user.PasswordHash))
                {
                    return BadRequest("新密碼不能與舊密碼相同");
                }

                // 檢查新密碼強度
                if (request.NewPassword.Length < 6)
                {
                    return BadRequest("新密碼長度至少需要6個字符");
                }

                // 更新密碼
                user.PasswordHash = BCrypt.Net.BCrypt.HashPassword(request.NewPassword);
                user.UpdatedAt = DateTime.Now;
                await _context.SaveChangesAsync();

                return Ok(new { message = "密碼更新成功" });
            }
            catch (Exception ex)
            {
                return BadRequest($"密碼更新失敗: {ex.Message}");
            }
        }

        [Authorize(Roles = "Admin,Manager,User")]
        [HttpGet("DoctorList")]
        public async Task<IActionResult> DoctorList()
        {

            var redisdata = await _redisService.GetStringAsync("DoctorList");

            if (redisdata == null)
            {
                var users = _context.Users
                    .Where(t => t.IsEnabled == true)
                    .OrderByDescending(p => p.CreatedAt)
                    .ToList();

                var result = users.Select(t => new
                {
                    Id = t.Id,
                    Name = t.Name
                }).ToList();

                var redisvalue = JsonConvert.SerializeObject(result);

                await _redisService.SetStringAsync("DoctorList", redisvalue);

                redisdata = redisvalue;
            }

            return Ok(redisdata);
        }

    }

}

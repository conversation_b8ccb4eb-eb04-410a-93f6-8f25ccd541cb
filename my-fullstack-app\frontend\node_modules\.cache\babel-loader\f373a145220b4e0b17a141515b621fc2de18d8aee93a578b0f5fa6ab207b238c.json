{"ast": null, "code": "import { buildLocalizeFn } from \"../../_lib/buildLocalizeFn.js\";\nconst eraValues = {\n  narrow: [\"Q\", \"W\"],\n  abbreviated: [\"QK\", \"WK\"],\n  wide: [\"qabe<PERSON>\", \"wara <PERSON>\"]\n};\nconst quarterValues = {\n  narrow: [\"1\", \"2\", \"3\", \"4\"],\n  abbreviated: [\"K1\", \"K2\", \"K3\", \"K4\"],\n  wide: [\"1. kwart\", \"2. kwart\", \"3. kwart\", \"4. kwart\"]\n};\nconst monthValues = {\n  narrow: [\"J\", \"F\", \"M\", \"A\", \"M\", \"Ġ\", \"L\", \"A\", \"S\", \"O\", \"N\", \"D\"],\n  abbreviated: [\"Jan\", \"Fra\", \"Mar\", \"Apr\", \"Mej\", \"Ġun\", \"Lul\", \"Aww\", \"Set\", \"Ott\", \"Nov\", \"Diċ\"],\n  wide: [\"<PERSON><PERSON>\", \"<PERSON><PERSON>\", \"<PERSON><PERSON>\", \"April\", \"<PERSON>j<PERSON>\", \"Ġunju\", \"<PERSON><PERSON><PERSON>\", \"<PERSON>wwiss<PERSON>\", \"<PERSON><PERSON><PERSON><PERSON>\", \"<PERSON><PERSON><PERSON><PERSON>\", \"<PERSON>em<PERSON><PERSON>\", \"Diċembru\"]\n};\nconst dayValues = {\n  narrow: [\"Ħ\", \"T\", \"T\", \"E\", \"Ħ\", \"Ġ\", \"S\"],\n  short: [\"Ħa\", \"Tn\", \"Tl\", \"Er\", \"Ħa\", \"Ġi\", \"Si\"],\n  abbreviated: [\"Ħad\", \"Tne\", \"Tli\", \"Erb\", \"Ħam\", \"Ġim\", \"Sib\"],\n  wide: [\"Il-Ħadd\", \"It-Tnejn\", \"It-Tlieta\", \"L-Erbgħa\", \"Il-Ħamis\", \"Il-Ġimgħa\", \"Is-Sibt\"]\n};\nconst dayPeriodValues = {\n  narrow: {\n    am: \"a\",\n    pm: \"p\",\n    midnight: \"nofsillejl\",\n    noon: \"nofsinhar\",\n    morning: \"għodwa\",\n    afternoon: \"wara nofsinhar\",\n    evening: \"filgħaxija\",\n    night: \"lejl\"\n  },\n  abbreviated: {\n    am: \"AM\",\n    pm: \"PM\",\n    midnight: \"nofsillejl\",\n    noon: \"nofsinhar\",\n    morning: \"għodwa\",\n    afternoon: \"wara nofsinhar\",\n    evening: \"filgħaxija\",\n    night: \"lejl\"\n  },\n  wide: {\n    am: \"a.m.\",\n    pm: \"p.m.\",\n    midnight: \"nofsillejl\",\n    noon: \"nofsinhar\",\n    morning: \"għodwa\",\n    afternoon: \"wara nofsinhar\",\n    evening: \"filgħaxija\",\n    night: \"lejl\"\n  }\n};\nconst formattingDayPeriodValues = {\n  narrow: {\n    am: \"a\",\n    pm: \"p\",\n    midnight: \"f'nofsillejl\",\n    noon: \"f'nofsinhar\",\n    morning: \"filgħodu\",\n    afternoon: \"wara nofsinhar\",\n    evening: \"filgħaxija\",\n    night: \"billejl\"\n  },\n  abbreviated: {\n    am: \"AM\",\n    pm: \"PM\",\n    midnight: \"f'nofsillejl\",\n    noon: \"f'nofsinhar\",\n    morning: \"filgħodu\",\n    afternoon: \"wara nofsinhar\",\n    evening: \"filgħaxija\",\n    night: \"billejl\"\n  },\n  wide: {\n    am: \"a.m.\",\n    pm: \"p.m.\",\n    midnight: \"f'nofsillejl\",\n    noon: \"f'nofsinhar\",\n    morning: \"filgħodu\",\n    afternoon: \"wara nofsinhar\",\n    evening: \"filgħaxija\",\n    night: \"billejl\"\n  }\n};\nconst ordinalNumber = (dirtyNumber, _options) => {\n  const number = Number(dirtyNumber);\n  return number + \"º\";\n};\nexport const localize = {\n  ordinalNumber,\n  era: buildLocalizeFn({\n    values: eraValues,\n    defaultWidth: \"wide\"\n  }),\n  quarter: buildLocalizeFn({\n    values: quarterValues,\n    defaultWidth: \"wide\",\n    argumentCallback: quarter => quarter - 1\n  }),\n  month: buildLocalizeFn({\n    values: monthValues,\n    defaultWidth: \"wide\"\n  }),\n  day: buildLocalizeFn({\n    values: dayValues,\n    defaultWidth: \"wide\"\n  }),\n  dayPeriod: buildLocalizeFn({\n    values: dayPeriodValues,\n    defaultWidth: \"wide\",\n    formattingValues: formattingDayPeriodValues,\n    defaultFormattingWidth: \"wide\"\n  })\n};", "map": {"version": 3, "names": ["buildLocalizeFn", "<PERSON><PERSON><PERSON><PERSON>", "narrow", "abbreviated", "wide", "quarterValues", "month<PERSON><PERSON><PERSON>", "dayV<PERSON><PERSON>", "short", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "am", "pm", "midnight", "noon", "morning", "afternoon", "evening", "night", "formattingDayPeriodValues", "ordinalNumber", "dirtyNumber", "_options", "number", "Number", "localize", "era", "values", "defaultWidth", "quarter", "argument<PERSON>allback", "month", "day", "<PERSON><PERSON><PERSON><PERSON>", "formattingValues", "defaultFormattingWidth"], "sources": ["C:/Users/<USER>/Desktop/demo-git/demo-react/my-fullstack-app/frontend/node_modules/date-fns/locale/mt/_lib/localize.js"], "sourcesContent": ["import { buildLocalizeFn } from \"../../_lib/buildLocalizeFn.js\";\n\nconst eraValues = {\n  narrow: [\"Q\", \"W\"],\n  abbreviated: [\"QK\", \"WK\"],\n  wide: [\"qabe<PERSON>\", \"wara <PERSON>\"],\n};\n\nconst quarterValues = {\n  narrow: [\"1\", \"2\", \"3\", \"4\"],\n  abbreviated: [\"K1\", \"K2\", \"K3\", \"K4\"],\n  wide: [\"1. kwart\", \"2. kwart\", \"3. kwart\", \"4. kwart\"],\n};\n\nconst monthValues = {\n  narrow: [\"J\", \"F\", \"M\", \"A\", \"M\", \"Ġ\", \"L\", \"A\", \"S\", \"O\", \"N\", \"D\"],\n  abbreviated: [\n    \"Jan\",\n    \"Fra\",\n    \"Mar\",\n    \"Apr\",\n    \"Mej\",\n    \"Ġun\",\n    \"Lul\",\n    \"Aww\",\n    \"Set\",\n    \"Ott\",\n    \"Nov\",\n    \"Diċ\",\n  ],\n\n  wide: [\n    \"<PERSON><PERSON>\",\n    \"<PERSON><PERSON>\",\n    \"<PERSON><PERSON>\",\n    \"April\",\n    \"<PERSON>j<PERSON>\",\n    \"Ġunju\",\n    \"<PERSON><PERSON><PERSON>\",\n    \"<PERSON>wwiss<PERSON>\",\n    \"<PERSON><PERSON><PERSON><PERSON>\",\n    \"<PERSON><PERSON><PERSON><PERSON>\",\n    \"<PERSON>embru\",\n    \"Diċembru\",\n  ],\n};\n\nconst dayValues = {\n  narrow: [\"Ħ\", \"T\", \"T\", \"E\", \"Ħ\", \"Ġ\", \"S\"],\n  short: [\"Ħa\", \"Tn\", \"Tl\", \"Er\", \"Ħa\", \"Ġi\", \"Si\"],\n  abbreviated: [\"Ħad\", \"Tne\", \"Tli\", \"Erb\", \"Ħam\", \"Ġim\", \"Sib\"],\n  wide: [\n    \"Il-Ħadd\",\n    \"It-Tnejn\",\n    \"It-Tlieta\",\n    \"L-Erbgħa\",\n    \"Il-Ħamis\",\n    \"Il-Ġimgħa\",\n    \"Is-Sibt\",\n  ],\n};\n\nconst dayPeriodValues = {\n  narrow: {\n    am: \"a\",\n    pm: \"p\",\n    midnight: \"nofsillejl\",\n    noon: \"nofsinhar\",\n    morning: \"għodwa\",\n    afternoon: \"wara nofsinhar\",\n    evening: \"filgħaxija\",\n    night: \"lejl\",\n  },\n  abbreviated: {\n    am: \"AM\",\n    pm: \"PM\",\n    midnight: \"nofsillejl\",\n    noon: \"nofsinhar\",\n    morning: \"għodwa\",\n    afternoon: \"wara nofsinhar\",\n    evening: \"filgħaxija\",\n    night: \"lejl\",\n  },\n  wide: {\n    am: \"a.m.\",\n    pm: \"p.m.\",\n    midnight: \"nofsillejl\",\n    noon: \"nofsinhar\",\n    morning: \"għodwa\",\n    afternoon: \"wara nofsinhar\",\n    evening: \"filgħaxija\",\n    night: \"lejl\",\n  },\n};\n\nconst formattingDayPeriodValues = {\n  narrow: {\n    am: \"a\",\n    pm: \"p\",\n    midnight: \"f'nofsillejl\",\n    noon: \"f'nofsinhar\",\n    morning: \"filgħodu\",\n    afternoon: \"wara nofsinhar\",\n    evening: \"filgħaxija\",\n    night: \"billejl\",\n  },\n  abbreviated: {\n    am: \"AM\",\n    pm: \"PM\",\n    midnight: \"f'nofsillejl\",\n    noon: \"f'nofsinhar\",\n    morning: \"filgħodu\",\n    afternoon: \"wara nofsinhar\",\n    evening: \"filgħaxija\",\n    night: \"billejl\",\n  },\n  wide: {\n    am: \"a.m.\",\n    pm: \"p.m.\",\n    midnight: \"f'nofsillejl\",\n    noon: \"f'nofsinhar\",\n    morning: \"filgħodu\",\n    afternoon: \"wara nofsinhar\",\n    evening: \"filgħaxija\",\n    night: \"billejl\",\n  },\n};\n\nconst ordinalNumber = (dirtyNumber, _options) => {\n  const number = Number(dirtyNumber);\n  return number + \"º\";\n};\n\nexport const localize = {\n  ordinalNumber,\n\n  era: buildLocalizeFn({\n    values: eraValues,\n    defaultWidth: \"wide\",\n  }),\n\n  quarter: buildLocalizeFn({\n    values: quarterValues,\n    defaultWidth: \"wide\",\n    argumentCallback: (quarter) => quarter - 1,\n  }),\n\n  month: buildLocalizeFn({\n    values: monthValues,\n    defaultWidth: \"wide\",\n  }),\n\n  day: buildLocalizeFn({\n    values: dayValues,\n    defaultWidth: \"wide\",\n  }),\n\n  dayPeriod: buildLocalizeFn({\n    values: dayPeriodValues,\n    defaultWidth: \"wide\",\n    formattingValues: formattingDayPeriodValues,\n    defaultFormattingWidth: \"wide\",\n  }),\n};\n"], "mappings": "AAAA,SAASA,eAAe,QAAQ,+BAA+B;AAE/D,MAAMC,SAAS,GAAG;EAChBC,MAAM,EAAE,CAAC,GAAG,EAAE,GAAG,CAAC;EAClBC,WAAW,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC;EACzBC,IAAI,EAAE,CAAC,cAAc,EAAE,aAAa;AACtC,CAAC;AAED,MAAMC,aAAa,GAAG;EACpBH,MAAM,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EAC5BC,WAAW,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;EACrCC,IAAI,EAAE,CAAC,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU;AACvD,CAAC;AAED,MAAME,WAAW,GAAG;EAClBJ,MAAM,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EACpEC,WAAW,EAAE,CACX,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,CACN;EAEDC,IAAI,EAAE,CACJ,QAAQ,EACR,MAAM,EACN,OAAO,EACP,OAAO,EACP,OAAO,EACP,OAAO,EACP,OAAO,EACP,SAAS,EACT,WAAW,EACX,SAAS,EACT,UAAU,EACV,UAAU;AAEd,CAAC;AAED,MAAMG,SAAS,GAAG;EAChBL,MAAM,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EAC3CM,KAAK,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;EACjDL,WAAW,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC;EAC9DC,IAAI,EAAE,CACJ,SAAS,EACT,UAAU,EACV,WAAW,EACX,UAAU,EACV,UAAU,EACV,WAAW,EACX,SAAS;AAEb,CAAC;AAED,MAAMK,eAAe,GAAG;EACtBP,MAAM,EAAE;IACNQ,EAAE,EAAE,GAAG;IACPC,EAAE,EAAE,GAAG;IACPC,QAAQ,EAAE,YAAY;IACtBC,IAAI,EAAE,WAAW;IACjBC,OAAO,EAAE,QAAQ;IACjBC,SAAS,EAAE,gBAAgB;IAC3BC,OAAO,EAAE,YAAY;IACrBC,KAAK,EAAE;EACT,CAAC;EACDd,WAAW,EAAE;IACXO,EAAE,EAAE,IAAI;IACRC,EAAE,EAAE,IAAI;IACRC,QAAQ,EAAE,YAAY;IACtBC,IAAI,EAAE,WAAW;IACjBC,OAAO,EAAE,QAAQ;IACjBC,SAAS,EAAE,gBAAgB;IAC3BC,OAAO,EAAE,YAAY;IACrBC,KAAK,EAAE;EACT,CAAC;EACDb,IAAI,EAAE;IACJM,EAAE,EAAE,MAAM;IACVC,EAAE,EAAE,MAAM;IACVC,QAAQ,EAAE,YAAY;IACtBC,IAAI,EAAE,WAAW;IACjBC,OAAO,EAAE,QAAQ;IACjBC,SAAS,EAAE,gBAAgB;IAC3BC,OAAO,EAAE,YAAY;IACrBC,KAAK,EAAE;EACT;AACF,CAAC;AAED,MAAMC,yBAAyB,GAAG;EAChChB,MAAM,EAAE;IACNQ,EAAE,EAAE,GAAG;IACPC,EAAE,EAAE,GAAG;IACPC,QAAQ,EAAE,cAAc;IACxBC,IAAI,EAAE,aAAa;IACnBC,OAAO,EAAE,UAAU;IACnBC,SAAS,EAAE,gBAAgB;IAC3BC,OAAO,EAAE,YAAY;IACrBC,KAAK,EAAE;EACT,CAAC;EACDd,WAAW,EAAE;IACXO,EAAE,EAAE,IAAI;IACRC,EAAE,EAAE,IAAI;IACRC,QAAQ,EAAE,cAAc;IACxBC,IAAI,EAAE,aAAa;IACnBC,OAAO,EAAE,UAAU;IACnBC,SAAS,EAAE,gBAAgB;IAC3BC,OAAO,EAAE,YAAY;IACrBC,KAAK,EAAE;EACT,CAAC;EACDb,IAAI,EAAE;IACJM,EAAE,EAAE,MAAM;IACVC,EAAE,EAAE,MAAM;IACVC,QAAQ,EAAE,cAAc;IACxBC,IAAI,EAAE,aAAa;IACnBC,OAAO,EAAE,UAAU;IACnBC,SAAS,EAAE,gBAAgB;IAC3BC,OAAO,EAAE,YAAY;IACrBC,KAAK,EAAE;EACT;AACF,CAAC;AAED,MAAME,aAAa,GAAGA,CAACC,WAAW,EAAEC,QAAQ,KAAK;EAC/C,MAAMC,MAAM,GAAGC,MAAM,CAACH,WAAW,CAAC;EAClC,OAAOE,MAAM,GAAG,GAAG;AACrB,CAAC;AAED,OAAO,MAAME,QAAQ,GAAG;EACtBL,aAAa;EAEbM,GAAG,EAAEzB,eAAe,CAAC;IACnB0B,MAAM,EAAEzB,SAAS;IACjB0B,YAAY,EAAE;EAChB,CAAC,CAAC;EAEFC,OAAO,EAAE5B,eAAe,CAAC;IACvB0B,MAAM,EAAErB,aAAa;IACrBsB,YAAY,EAAE,MAAM;IACpBE,gBAAgB,EAAGD,OAAO,IAAKA,OAAO,GAAG;EAC3C,CAAC,CAAC;EAEFE,KAAK,EAAE9B,eAAe,CAAC;IACrB0B,MAAM,EAAEpB,WAAW;IACnBqB,YAAY,EAAE;EAChB,CAAC,CAAC;EAEFI,GAAG,EAAE/B,eAAe,CAAC;IACnB0B,MAAM,EAAEnB,SAAS;IACjBoB,YAAY,EAAE;EAChB,CAAC,CAAC;EAEFK,SAAS,EAAEhC,eAAe,CAAC;IACzB0B,MAAM,EAAEjB,eAAe;IACvBkB,YAAY,EAAE,MAAM;IACpBM,gBAAgB,EAAEf,yBAAyB;IAC3CgB,sBAAsB,EAAE;EAC1B,CAAC;AACH,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
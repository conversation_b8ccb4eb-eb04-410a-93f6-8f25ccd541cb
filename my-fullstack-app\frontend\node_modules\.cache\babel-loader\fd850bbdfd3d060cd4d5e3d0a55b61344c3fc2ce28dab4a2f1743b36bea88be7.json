{"ast": null, "code": "/**\n * Application route constants\n */export const ROUTES={// Auth routes\nLOGIN:'/login',UPDATE_PASSWORD:'/update-password',// Main routes\nHOME:'/',// Doctor routes\nDOCTORS:'/doctors',DOCTOR_DETAIL:'/doctordetail',// Treatment routes\nTREATMENTS:'/treatments',TREATMENT_DETAIL:'/treatmentsdetail',// Patient routes\nPATIENTS:'/patients',PATIENT_DETAIL:'/patientsdetail',// Schedule routes\nSCHEDULES:'/schedules',// Receipt routes\nRECEIPTS:'/receipts',RECEIPT_DETAIL:'/receiptsdetail',// User routes\nUSERS:'/userrole-management',// Backup routes\nBACKUP_MANAGEMENT:'/backup-management',// Report routes\nREPORT_MANAGEMENT:'/report-management',// Image routes\nIMAGE_MANAGEMENT:'/image-management',// Login Security routes\nLOGIN_LOGS:'/login-logs',IP_BLOCKS:'/ip-blocks',// Debug routes\nDEBUG:'/debug',// Permission routes\nPERMISSION_MANAGEMENT:'/permission-management'};/**\n * Route metadata for navigation and permissions\n */export const ROUTE_METADATA={[ROUTES.LOGIN]:{path:ROUTES.LOGIN,title:'Login',requiresAuth:false},[ROUTES.UPDATE_PASSWORD]:{path:ROUTES.UPDATE_PASSWORD,title:'Update Password',requiresAuth:true},[ROUTES.HOME]:{path:ROUTES.HOME,title:'Home',requiresAuth:true,icon:'pi pi-home'},[ROUTES.DOCTORS]:{path:ROUTES.DOCTORS,title:'Doctors',requiresAuth:true,icon:'pi pi-users'},[ROUTES.DOCTOR_DETAIL]:{path:ROUTES.DOCTOR_DETAIL,title:'Doctor Detail',requiresAuth:true},[ROUTES.TREATMENTS]:{path:ROUTES.TREATMENTS,title:'Treatments',requiresAuth:true,icon:'pi pi-heart'},[ROUTES.TREATMENT_DETAIL]:{path:ROUTES.TREATMENT_DETAIL,title:'Treatment Detail',requiresAuth:true},[ROUTES.PATIENTS]:{path:ROUTES.PATIENTS,title:'Patients',requiresAuth:true,icon:'pi pi-user'},[ROUTES.PATIENT_DETAIL]:{path:ROUTES.PATIENT_DETAIL,title:'Patient Detail',requiresAuth:true},[ROUTES.SCHEDULES]:{path:ROUTES.SCHEDULES,title:'Schedules',requiresAuth:true,icon:'pi pi-calendar'},[ROUTES.RECEIPTS]:{path:ROUTES.RECEIPTS,title:'Receipts',requiresAuth:true,icon:'pi pi-file'},[ROUTES.RECEIPT_DETAIL]:{path:ROUTES.RECEIPT_DETAIL,title:'Receipt Detail',requiresAuth:true},[ROUTES.USERS]:{path:ROUTES.USERS,title:'User Role Management',requiresAuth:true,icon:'pi pi-users'},[ROUTES.BACKUP_MANAGEMENT]:{path:ROUTES.BACKUP_MANAGEMENT,title:'Backup Management',requiresAuth:true,permissions:['Admin'],icon:'pi pi-database'},[ROUTES.REPORT_MANAGEMENT]:{path:ROUTES.REPORT_MANAGEMENT,title:'Report Management',requiresAuth:true,permissions:['Admin','Manager'],icon:'pi pi-file-pdf'},[ROUTES.IMAGE_MANAGEMENT]:{path:ROUTES.IMAGE_MANAGEMENT,title:'Image Management',requiresAuth:true,permissions:['Admin','Manager'],icon:'pi pi-images'},[ROUTES.LOGIN_LOGS]:{path:ROUTES.LOGIN_LOGS,title:'Login Logs',requiresAuth:true,permissions:['Admin','Manager'],icon:'pi pi-history'},[ROUTES.IP_BLOCKS]:{path:ROUTES.IP_BLOCKS,title:'IP Blocks',requiresAuth:true,permissions:['Admin'],icon:'pi pi-ban'},[ROUTES.DEBUG]:{path:ROUTES.DEBUG,title:'Debug',requiresAuth:true,icon:'pi pi-cog'},[ROUTES.PERMISSION_MANAGEMENT]:{path:ROUTES.PERMISSION_MANAGEMENT,title:'Permission Management',requiresAuth:true,permissions:['Admin'],icon:'pi pi-lock'}};/**\n * Get route metadata by path\n */export const getRouteMetadata=path=>{return ROUTE_METADATA[path];};/**\n * Check if route requires authentication\n */export const isProtectedRoute=path=>{var _metadata$requiresAut;const metadata=getRouteMetadata(path);return(_metadata$requiresAut=metadata===null||metadata===void 0?void 0:metadata.requiresAuth)!==null&&_metadata$requiresAut!==void 0?_metadata$requiresAut:true;};", "map": {"version": 3, "names": ["ROUTES", "LOGIN", "UPDATE_PASSWORD", "HOME", "DOCTORS", "DOCTOR_DETAIL", "TREATMENTS", "TREATMENT_DETAIL", "PATIENTS", "PATIENT_DETAIL", "SCHEDULES", "RECEIPTS", "RECEIPT_DETAIL", "USERS", "BACKUP_MANAGEMENT", "REPORT_MANAGEMENT", "IMAGE_MANAGEMENT", "LOGIN_LOGS", "IP_BLOCKS", "DEBUG", "PERMISSION_MANAGEMENT", "ROUTE_METADATA", "path", "title", "requiresAuth", "icon", "permissions", "getRouteMetadata", "isProtectedRoute", "_metadata$requiresAut", "metadata"], "sources": ["C:/Users/<USER>/Desktop/demo-git/demo-react/my-fullstack-app/frontend/src/constants/routes.ts"], "sourcesContent": ["/**\n * Application route constants\n */\n\nexport const ROUTES = {\n  // Auth routes\n  LOGIN: '/login',\n  UPDATE_PASSWORD: '/update-password',\n  \n  // Main routes\n  HOME: '/',\n  \n  // Doctor routes\n  DOCTORS: '/doctors',\n  DOCTOR_DETAIL: '/doctordetail',\n  \n  // Treatment routes\n  TREATMENTS: '/treatments',\n  TREATMENT_DETAIL: '/treatmentsdetail',\n  \n  // Patient routes\n  PATIENTS: '/patients',\n  PATIENT_DETAIL: '/patientsdetail',\n  \n  // Schedule routes\n  SCHEDULES: '/schedules',\n  \n  // Receipt routes\n  RECEIPTS: '/receipts',\n  RECEIPT_DETAIL: '/receiptsdetail',\n  \n  // User routes\n  USERS: '/userrole-management',\n\n  // Backup routes\n  BACKUP_MANAGEMENT: '/backup-management',\n\n  // Report routes\n  REPORT_MANAGEMENT: '/report-management',\n\n  // Image routes\n  IMAGE_MANAGEMENT: '/image-management',\n\n  // Login Security routes\n  LOGIN_LOGS: '/login-logs',\n  IP_BLOCKS: '/ip-blocks',\n\n  // Debug routes\n  DEBUG: '/debug',\n\n  // Permission routes\n  PERMISSION_MANAGEMENT: '/permission-management',\n} as const;\n\nexport type RouteKey = keyof typeof ROUTES;\nexport type RoutePath = typeof ROUTES[RouteKey];\n\n/**\n * Route metadata for navigation and permissions\n */\nexport interface RouteMetadata {\n  path: RoutePath;\n  title: string;\n  requiresAuth: boolean;\n  permissions?: string[];\n  icon?: string;\n}\n\nexport const ROUTE_METADATA: Record<RoutePath, RouteMetadata> = {\n  [ROUTES.LOGIN]: {\n    path: ROUTES.LOGIN,\n    title: 'Login',\n    requiresAuth: false,\n  },\n  [ROUTES.UPDATE_PASSWORD]: {\n    path: ROUTES.UPDATE_PASSWORD,\n    title: 'Update Password',\n    requiresAuth: true,\n  },\n  [ROUTES.HOME]: {\n    path: ROUTES.HOME,\n    title: 'Home',\n    requiresAuth: true,\n    icon: 'pi pi-home',\n  },\n  [ROUTES.DOCTORS]: {\n    path: ROUTES.DOCTORS,\n    title: 'Doctors',\n    requiresAuth: true,\n    icon: 'pi pi-users',\n  },\n  [ROUTES.DOCTOR_DETAIL]: {\n    path: ROUTES.DOCTOR_DETAIL,\n    title: 'Doctor Detail',\n    requiresAuth: true,\n  },\n  [ROUTES.TREATMENTS]: {\n    path: ROUTES.TREATMENTS,\n    title: 'Treatments',\n    requiresAuth: true,\n    icon: 'pi pi-heart',\n  },\n  [ROUTES.TREATMENT_DETAIL]: {\n    path: ROUTES.TREATMENT_DETAIL,\n    title: 'Treatment Detail',\n    requiresAuth: true,\n  },\n  [ROUTES.PATIENTS]: {\n    path: ROUTES.PATIENTS,\n    title: 'Patients',\n    requiresAuth: true,\n    icon: 'pi pi-user',\n  },\n  [ROUTES.PATIENT_DETAIL]: {\n    path: ROUTES.PATIENT_DETAIL,\n    title: 'Patient Detail',\n    requiresAuth: true,\n  },\n  [ROUTES.SCHEDULES]: {\n    path: ROUTES.SCHEDULES,\n    title: 'Schedules',\n    requiresAuth: true,\n    icon: 'pi pi-calendar',\n  },\n  [ROUTES.RECEIPTS]: {\n    path: ROUTES.RECEIPTS,\n    title: 'Receipts',\n    requiresAuth: true,\n    icon: 'pi pi-file',\n  },\n  [ROUTES.RECEIPT_DETAIL]: {\n    path: ROUTES.RECEIPT_DETAIL,\n    title: 'Receipt Detail',\n    requiresAuth: true,\n  },\n  [ROUTES.USERS]: {\n    path: ROUTES.USERS,\n    title: 'User Role Management',\n    requiresAuth: true,\n    icon: 'pi pi-users',\n  },\n  [ROUTES.BACKUP_MANAGEMENT]: {\n    path: ROUTES.BACKUP_MANAGEMENT,\n    title: 'Backup Management',\n    requiresAuth: true,\n    permissions: ['Admin'],\n    icon: 'pi pi-database',\n  },\n  [ROUTES.REPORT_MANAGEMENT]: {\n    path: ROUTES.REPORT_MANAGEMENT,\n    title: 'Report Management',\n    requiresAuth: true,\n    permissions: ['Admin', 'Manager'],\n    icon: 'pi pi-file-pdf',\n  },\n  [ROUTES.IMAGE_MANAGEMENT]: {\n    path: ROUTES.IMAGE_MANAGEMENT,\n    title: 'Image Management',\n    requiresAuth: true,\n    permissions: ['Admin', 'Manager'],\n    icon: 'pi pi-images',\n  },\n  [ROUTES.LOGIN_LOGS]: {\n    path: ROUTES.LOGIN_LOGS,\n    title: 'Login Logs',\n    requiresAuth: true,\n    permissions: ['Admin', 'Manager'],\n    icon: 'pi pi-history',\n  },\n  [ROUTES.IP_BLOCKS]: {\n    path: ROUTES.IP_BLOCKS,\n    title: 'IP Blocks',\n    requiresAuth: true,\n    permissions: ['Admin'],\n    icon: 'pi pi-ban',\n  },\n  [ROUTES.DEBUG]: {\n    path: ROUTES.DEBUG,\n    title: 'Debug',\n    requiresAuth: true,\n    icon: 'pi pi-cog',\n  },\n  [ROUTES.PERMISSION_MANAGEMENT]: {\n    path: ROUTES.PERMISSION_MANAGEMENT,\n    title: 'Permission Management',\n    requiresAuth: true,\n    permissions: ['Admin'],\n    icon: 'pi pi-lock',\n  },\n};\n\n/**\n * Get route metadata by path\n */\nexport const getRouteMetadata = (path: string): RouteMetadata | undefined => {\n  return ROUTE_METADATA[path as RoutePath];\n};\n\n/**\n * Check if route requires authentication\n */\nexport const isProtectedRoute = (path: string): boolean => {\n  const metadata = getRouteMetadata(path);\n  return metadata?.requiresAuth ?? true;\n};\n"], "mappings": "AAAA;AACA;AACA,GAEA,MAAO,MAAM,CAAAA,MAAM,CAAG,CACpB;AACAC,KAAK,CAAE,QAAQ,CACfC,eAAe,CAAE,kBAAkB,CAEnC;AACAC,IAAI,CAAE,GAAG,CAET;AACAC,OAAO,CAAE,UAAU,CACnBC,aAAa,CAAE,eAAe,CAE9B;AACAC,UAAU,CAAE,aAAa,CACzBC,gBAAgB,CAAE,mBAAmB,CAErC;AACAC,QAAQ,CAAE,WAAW,CACrBC,cAAc,CAAE,iBAAiB,CAEjC;AACAC,SAAS,CAAE,YAAY,CAEvB;AACAC,QAAQ,CAAE,WAAW,CACrBC,cAAc,CAAE,iBAAiB,CAEjC;AACAC,KAAK,CAAE,sBAAsB,CAE7B;AACAC,iBAAiB,CAAE,oBAAoB,CAEvC;AACAC,iBAAiB,CAAE,oBAAoB,CAEvC;AACAC,gBAAgB,CAAE,mBAAmB,CAErC;AACAC,UAAU,CAAE,aAAa,CACzBC,SAAS,CAAE,YAAY,CAEvB;AACAC,KAAK,CAAE,QAAQ,CAEf;AACAC,qBAAqB,CAAE,wBACzB,CAAU,CAKV;AACA;AACA,GASA,MAAO,MAAM,CAAAC,cAAgD,CAAG,CAC9D,CAACrB,MAAM,CAACC,KAAK,EAAG,CACdqB,IAAI,CAAEtB,MAAM,CAACC,KAAK,CAClBsB,KAAK,CAAE,OAAO,CACdC,YAAY,CAAE,KAChB,CAAC,CACD,CAACxB,MAAM,CAACE,eAAe,EAAG,CACxBoB,IAAI,CAAEtB,MAAM,CAACE,eAAe,CAC5BqB,KAAK,CAAE,iBAAiB,CACxBC,YAAY,CAAE,IAChB,CAAC,CACD,CAACxB,MAAM,CAACG,IAAI,EAAG,CACbmB,IAAI,CAAEtB,MAAM,CAACG,IAAI,CACjBoB,KAAK,CAAE,MAAM,CACbC,YAAY,CAAE,IAAI,CAClBC,IAAI,CAAE,YACR,CAAC,CACD,CAACzB,MAAM,CAACI,OAAO,EAAG,CAChBkB,IAAI,CAAEtB,MAAM,CAACI,OAAO,CACpBmB,KAAK,CAAE,SAAS,CAChBC,YAAY,CAAE,IAAI,CAClBC,IAAI,CAAE,aACR,CAAC,CACD,CAACzB,MAAM,CAACK,aAAa,EAAG,CACtBiB,IAAI,CAAEtB,MAAM,CAACK,aAAa,CAC1BkB,KAAK,CAAE,eAAe,CACtBC,YAAY,CAAE,IAChB,CAAC,CACD,CAACxB,MAAM,CAACM,UAAU,EAAG,CACnBgB,IAAI,CAAEtB,MAAM,CAACM,UAAU,CACvBiB,KAAK,CAAE,YAAY,CACnBC,YAAY,CAAE,IAAI,CAClBC,IAAI,CAAE,aACR,CAAC,CACD,CAACzB,MAAM,CAACO,gBAAgB,EAAG,CACzBe,IAAI,CAAEtB,MAAM,CAACO,gBAAgB,CAC7BgB,KAAK,CAAE,kBAAkB,CACzBC,YAAY,CAAE,IAChB,CAAC,CACD,CAACxB,MAAM,CAACQ,QAAQ,EAAG,CACjBc,IAAI,CAAEtB,MAAM,CAACQ,QAAQ,CACrBe,KAAK,CAAE,UAAU,CACjBC,YAAY,CAAE,IAAI,CAClBC,IAAI,CAAE,YACR,CAAC,CACD,CAACzB,MAAM,CAACS,cAAc,EAAG,CACvBa,IAAI,CAAEtB,MAAM,CAACS,cAAc,CAC3Bc,KAAK,CAAE,gBAAgB,CACvBC,YAAY,CAAE,IAChB,CAAC,CACD,CAACxB,MAAM,CAACU,SAAS,EAAG,CAClBY,IAAI,CAAEtB,MAAM,CAACU,SAAS,CACtBa,KAAK,CAAE,WAAW,CAClBC,YAAY,CAAE,IAAI,CAClBC,IAAI,CAAE,gBACR,CAAC,CACD,CAACzB,MAAM,CAACW,QAAQ,EAAG,CACjBW,IAAI,CAAEtB,MAAM,CAACW,QAAQ,CACrBY,KAAK,CAAE,UAAU,CACjBC,YAAY,CAAE,IAAI,CAClBC,IAAI,CAAE,YACR,CAAC,CACD,CAACzB,MAAM,CAACY,cAAc,EAAG,CACvBU,IAAI,CAAEtB,MAAM,CAACY,cAAc,CAC3BW,KAAK,CAAE,gBAAgB,CACvBC,YAAY,CAAE,IAChB,CAAC,CACD,CAACxB,MAAM,CAACa,KAAK,EAAG,CACdS,IAAI,CAAEtB,MAAM,CAACa,KAAK,CAClBU,KAAK,CAAE,sBAAsB,CAC7BC,YAAY,CAAE,IAAI,CAClBC,IAAI,CAAE,aACR,CAAC,CACD,CAACzB,MAAM,CAACc,iBAAiB,EAAG,CAC1BQ,IAAI,CAAEtB,MAAM,CAACc,iBAAiB,CAC9BS,KAAK,CAAE,mBAAmB,CAC1BC,YAAY,CAAE,IAAI,CAClBE,WAAW,CAAE,CAAC,OAAO,CAAC,CACtBD,IAAI,CAAE,gBACR,CAAC,CACD,CAACzB,MAAM,CAACe,iBAAiB,EAAG,CAC1BO,IAAI,CAAEtB,MAAM,CAACe,iBAAiB,CAC9BQ,KAAK,CAAE,mBAAmB,CAC1BC,YAAY,CAAE,IAAI,CAClBE,WAAW,CAAE,CAAC,OAAO,CAAE,SAAS,CAAC,CACjCD,IAAI,CAAE,gBACR,CAAC,CACD,CAACzB,MAAM,CAACgB,gBAAgB,EAAG,CACzBM,IAAI,CAAEtB,MAAM,CAACgB,gBAAgB,CAC7BO,KAAK,CAAE,kBAAkB,CACzBC,YAAY,CAAE,IAAI,CAClBE,WAAW,CAAE,CAAC,OAAO,CAAE,SAAS,CAAC,CACjCD,IAAI,CAAE,cACR,CAAC,CACD,CAACzB,MAAM,CAACiB,UAAU,EAAG,CACnBK,IAAI,CAAEtB,MAAM,CAACiB,UAAU,CACvBM,KAAK,CAAE,YAAY,CACnBC,YAAY,CAAE,IAAI,CAClBE,WAAW,CAAE,CAAC,OAAO,CAAE,SAAS,CAAC,CACjCD,IAAI,CAAE,eACR,CAAC,CACD,CAACzB,MAAM,CAACkB,SAAS,EAAG,CAClBI,IAAI,CAAEtB,MAAM,CAACkB,SAAS,CACtBK,KAAK,CAAE,WAAW,CAClBC,YAAY,CAAE,IAAI,CAClBE,WAAW,CAAE,CAAC,OAAO,CAAC,CACtBD,IAAI,CAAE,WACR,CAAC,CACD,CAACzB,MAAM,CAACmB,KAAK,EAAG,CACdG,IAAI,CAAEtB,MAAM,CAACmB,KAAK,CAClBI,KAAK,CAAE,OAAO,CACdC,YAAY,CAAE,IAAI,CAClBC,IAAI,CAAE,WACR,CAAC,CACD,CAACzB,MAAM,CAACoB,qBAAqB,EAAG,CAC9BE,IAAI,CAAEtB,MAAM,CAACoB,qBAAqB,CAClCG,KAAK,CAAE,uBAAuB,CAC9BC,YAAY,CAAE,IAAI,CAClBE,WAAW,CAAE,CAAC,OAAO,CAAC,CACtBD,IAAI,CAAE,YACR,CACF,CAAC,CAED;AACA;AACA,GACA,MAAO,MAAM,CAAAE,gBAAgB,CAAIL,IAAY,EAAgC,CAC3E,MAAO,CAAAD,cAAc,CAACC,IAAI,CAAc,CAC1C,CAAC,CAED;AACA;AACA,GACA,MAAO,MAAM,CAAAM,gBAAgB,CAAIN,IAAY,EAAc,KAAAO,qBAAA,CACzD,KAAM,CAAAC,QAAQ,CAAGH,gBAAgB,CAACL,IAAI,CAAC,CACvC,OAAAO,qBAAA,CAAOC,QAAQ,SAARA,QAAQ,iBAARA,QAAQ,CAAEN,YAAY,UAAAK,qBAAA,UAAAA,qBAAA,CAAI,IAAI,CACvC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
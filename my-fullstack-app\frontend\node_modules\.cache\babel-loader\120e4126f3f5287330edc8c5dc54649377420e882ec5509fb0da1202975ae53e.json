{"ast": null, "code": "import { getDefaultOptions } from 'date-fns';\n/**\n * Returns the formatted time zone name of the provided `timeZone` or the current\n * system time zone if omitted, accounting for DST according to the UTC value of\n * the date.\n */\nexport function tzIntlTimeZoneName(length, date, options) {\n  var _options$locale;\n  const defaultOptions = getDefaultOptions();\n  const dtf = getDTF(length, options.timeZone, (_options$locale = options.locale) !== null && _options$locale !== void 0 ? _options$locale : defaultOptions.locale);\n  return 'formatToParts' in dtf ? partsTimeZone(dtf, date) : hackyTimeZone(dtf, date);\n}\nfunction partsTimeZone(dtf, date) {\n  const formatted = dtf.formatToParts(date);\n  for (let i = formatted.length - 1; i >= 0; --i) {\n    if (formatted[i].type === 'timeZoneName') {\n      return formatted[i].value;\n    }\n  }\n  return undefined;\n}\nfunction hackyTimeZone(dtf, date) {\n  const formatted = dtf.format(date).replace(/\\u200E/g, '');\n  const tzNameMatch = / [\\w-+ ]+$/.exec(formatted);\n  return tzNameMatch ? tzNameMatch[0].substr(1) : '';\n}\n// If a locale has been provided `en-US` is used as a fallback in case it is an\n// invalid locale, otherwise the locale is left undefined to use the system locale.\nfunction getDTF(length, timeZone, locale) {\n  return new Intl.DateTimeFormat(locale ? [locale.code, 'en-US'] : undefined, {\n    timeZone: timeZone,\n    timeZoneName: length\n  });\n}", "map": {"version": 3, "names": ["getDefaultOptions", "tzIntlTimeZoneName", "length", "date", "options", "_options$locale", "defaultOptions", "dtf", "getDTF", "timeZone", "locale", "partsTimeZone", "hackyTimeZone", "formatted", "formatToParts", "i", "type", "value", "undefined", "format", "replace", "tzNameMatch", "exec", "substr", "Intl", "DateTimeFormat", "code", "timeZoneName"], "sources": ["C:/Users/<USER>/Desktop/demo-git/demo-react/my-fullstack-app/frontend/node_modules/date-fns-tz/dist/esm/_lib/tzIntlTimeZoneName/index.js"], "sourcesContent": ["import { getDefaultOptions } from 'date-fns';\n/**\n * Returns the formatted time zone name of the provided `timeZone` or the current\n * system time zone if omitted, accounting for DST according to the UTC value of\n * the date.\n */\nexport function tzIntlTimeZoneName(length, date, options) {\n    const defaultOptions = getDefaultOptions();\n    const dtf = getDTF(length, options.timeZone, options.locale ?? defaultOptions.locale);\n    return 'formatToParts' in dtf ? partsTimeZone(dtf, date) : hackyTimeZone(dtf, date);\n}\nfunction partsTimeZone(dtf, date) {\n    const formatted = dtf.formatToParts(date);\n    for (let i = formatted.length - 1; i >= 0; --i) {\n        if (formatted[i].type === 'timeZoneName') {\n            return formatted[i].value;\n        }\n    }\n    return undefined;\n}\nfunction hackyTimeZone(dtf, date) {\n    const formatted = dtf.format(date).replace(/\\u200E/g, '');\n    const tzNameMatch = / [\\w-+ ]+$/.exec(formatted);\n    return tzNameMatch ? tzNameMatch[0].substr(1) : '';\n}\n// If a locale has been provided `en-US` is used as a fallback in case it is an\n// invalid locale, otherwise the locale is left undefined to use the system locale.\nfunction getDTF(length, timeZone, locale) {\n    return new Intl.DateTimeFormat(locale ? [locale.code, 'en-US'] : undefined, {\n        timeZone: timeZone,\n        timeZoneName: length,\n    });\n}\n"], "mappings": "AAAA,SAASA,iBAAiB,QAAQ,UAAU;AAC5C;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,kBAAkBA,CAACC,MAAM,EAAEC,IAAI,EAAEC,OAAO,EAAE;EAAA,IAAAC,eAAA;EACtD,MAAMC,cAAc,GAAGN,iBAAiB,CAAC,CAAC;EAC1C,MAAMO,GAAG,GAAGC,MAAM,CAACN,MAAM,EAAEE,OAAO,CAACK,QAAQ,GAAAJ,eAAA,GAAED,OAAO,CAACM,MAAM,cAAAL,eAAA,cAAAA,eAAA,GAAIC,cAAc,CAACI,MAAM,CAAC;EACrF,OAAO,eAAe,IAAIH,GAAG,GAAGI,aAAa,CAACJ,GAAG,EAAEJ,IAAI,CAAC,GAAGS,aAAa,CAACL,GAAG,EAAEJ,IAAI,CAAC;AACvF;AACA,SAASQ,aAAaA,CAACJ,GAAG,EAAEJ,IAAI,EAAE;EAC9B,MAAMU,SAAS,GAAGN,GAAG,CAACO,aAAa,CAACX,IAAI,CAAC;EACzC,KAAK,IAAIY,CAAC,GAAGF,SAAS,CAACX,MAAM,GAAG,CAAC,EAAEa,CAAC,IAAI,CAAC,EAAE,EAAEA,CAAC,EAAE;IAC5C,IAAIF,SAAS,CAACE,CAAC,CAAC,CAACC,IAAI,KAAK,cAAc,EAAE;MACtC,OAAOH,SAAS,CAACE,CAAC,CAAC,CAACE,KAAK;IAC7B;EACJ;EACA,OAAOC,SAAS;AACpB;AACA,SAASN,aAAaA,CAACL,GAAG,EAAEJ,IAAI,EAAE;EAC9B,MAAMU,SAAS,GAAGN,GAAG,CAACY,MAAM,CAAChB,IAAI,CAAC,CAACiB,OAAO,CAAC,SAAS,EAAE,EAAE,CAAC;EACzD,MAAMC,WAAW,GAAG,YAAY,CAACC,IAAI,CAACT,SAAS,CAAC;EAChD,OAAOQ,WAAW,GAAGA,WAAW,CAAC,CAAC,CAAC,CAACE,MAAM,CAAC,CAAC,CAAC,GAAG,EAAE;AACtD;AACA;AACA;AACA,SAASf,MAAMA,CAACN,MAAM,EAAEO,QAAQ,EAAEC,MAAM,EAAE;EACtC,OAAO,IAAIc,IAAI,CAACC,cAAc,CAACf,MAAM,GAAG,CAACA,MAAM,CAACgB,IAAI,EAAE,OAAO,CAAC,GAAGR,SAAS,EAAE;IACxET,QAAQ,EAAEA,QAAQ;IAClBkB,YAAY,EAAEzB;EAClB,CAAC,CAAC;AACN", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "import { buildLocalizeFn } from \"../../_lib/buildLocalizeFn.js\";\nconst eraValues = {\n  narrow: [\"ab. J.C.\", \"apr. J.C.\"],\n  abbreviated: [\"ab. J.C.\", \"apr. J.C.\"],\n  wide: [\"abans <PERSON>\", \"apr<PERSON>\"]\n};\nconst quarterValues = {\n  narrow: [\"T1\", \"T2\", \"T3\", \"T4\"],\n  abbreviated: [\"1èr trim.\", \"2nd trim.\", \"3en trim.\", \"4en trim.\"],\n  wide: [\"1èr trimèstre\", \"2nd trimèstre\", \"3en trimèstre\", \"4en trimèstre\"]\n};\nconst monthValues = {\n  narrow: [\"GN\", \"FB\", \"MÇ\", \"AB\", \"MA\", \"JN\", \"JL\", \"AG\", \"ST\", \"OC\", \"NV\", \"DC\"],\n  abbreviated: [\"gen.\", \"febr.\", \"març\", \"abr.\", \"mai\", \"junh\", \"jul.\", \"ag.\", \"set.\", \"oct.\", \"nov.\", \"dec.\"],\n  wide: [\"genièr\", \"febrièr\", \"març\", \"abril\", \"mai\", \"junh\", \"julhet\", \"agost\", \"setembre\", \"octòbre\", \"novembre\", \"decembre\"]\n};\nconst dayValues = {\n  narrow: [\"dg.\", \"dl.\", \"dm.\", \"dc.\", \"dj.\", \"dv.\", \"ds.\"],\n  short: [\"dg.\", \"dl.\", \"dm.\", \"dc.\", \"dj.\", \"dv.\", \"ds.\"],\n  abbreviated: [\"dg.\", \"dl.\", \"dm.\", \"dc.\", \"dj.\", \"dv.\", \"ds.\"],\n  wide: [\"dimenge\", \"diluns\", \"dimars\", \"dimècres\", \"dijòus\", \"divendres\", \"dissabte\"]\n};\nconst dayPeriodValues = {\n  narrow: {\n    am: \"am\",\n    pm: \"pm\",\n    midnight: \"mièjanuèch\",\n    noon: \"miègjorn\",\n    morning: \"matin\",\n    afternoon: \"aprèp-miègjorn\",\n    evening: \"vèspre\",\n    night: \"nuèch\"\n  },\n  abbreviated: {\n    am: \"a.m.\",\n    pm: \"p.m.\",\n    midnight: \"mièjanuèch\",\n    noon: \"miègjorn\",\n    morning: \"matin\",\n    afternoon: \"aprèp-miègjorn\",\n    evening: \"vèspre\",\n    night: \"nuèch\"\n  },\n  wide: {\n    am: \"a.m.\",\n    pm: \"p.m.\",\n    midnight: \"mièjanuèch\",\n    noon: \"miègjorn\",\n    morning: \"matin\",\n    afternoon: \"aprèp-miègjorn\",\n    evening: \"vèspre\",\n    night: \"nuèch\"\n  }\n};\nconst formattingDayPeriodValues = {\n  narrow: {\n    am: \"am\",\n    pm: \"pm\",\n    midnight: \"mièjanuèch\",\n    noon: \"miègjorn\",\n    morning: \"del matin\",\n    afternoon: \"de l’aprèp-miègjorn\",\n    evening: \"del ser\",\n    night: \"de la nuèch\"\n  },\n  abbreviated: {\n    am: \"AM\",\n    pm: \"PM\",\n    midnight: \"mièjanuèch\",\n    noon: \"miègjorn\",\n    morning: \"del matin\",\n    afternoon: \"de l’aprèp-miègjorn\",\n    evening: \"del ser\",\n    night: \"de la nuèch\"\n  },\n  wide: {\n    am: \"ante meridiem\",\n    pm: \"post meridiem\",\n    midnight: \"mièjanuèch\",\n    noon: \"miègjorn\",\n    morning: \"del matin\",\n    afternoon: \"de l’aprèp-miègjorn\",\n    evening: \"del ser\",\n    night: \"de la nuèch\"\n  }\n};\nconst ordinalNumber = (dirtyNumber, options) => {\n  const number = Number(dirtyNumber);\n  const unit = options === null || options === void 0 ? void 0 : options.unit;\n  let ordinal;\n  switch (number) {\n    case 1:\n      ordinal = \"èr\";\n      break;\n    case 2:\n      ordinal = \"nd\";\n      break;\n    default:\n      ordinal = \"en\";\n  }\n\n  // feminine for year, week, hour, minute, second\n  if (unit === \"year\" || unit === \"week\" || unit === \"hour\" || unit === \"minute\" || unit === \"second\") {\n    ordinal += \"a\";\n  }\n  return number + ordinal;\n};\nexport const localize = {\n  ordinalNumber,\n  era: buildLocalizeFn({\n    values: eraValues,\n    defaultWidth: \"wide\"\n  }),\n  quarter: buildLocalizeFn({\n    values: quarterValues,\n    defaultWidth: \"wide\",\n    argumentCallback: quarter => quarter - 1\n  }),\n  month: buildLocalizeFn({\n    values: monthValues,\n    defaultWidth: \"wide\"\n  }),\n  day: buildLocalizeFn({\n    values: dayValues,\n    defaultWidth: \"wide\"\n  }),\n  dayPeriod: buildLocalizeFn({\n    values: dayPeriodValues,\n    defaultWidth: \"wide\",\n    formattingValues: formattingDayPeriodValues,\n    defaultFormattingWidth: \"wide\"\n  })\n};", "map": {"version": 3, "names": ["buildLocalizeFn", "<PERSON><PERSON><PERSON><PERSON>", "narrow", "abbreviated", "wide", "quarterValues", "month<PERSON><PERSON><PERSON>", "dayV<PERSON><PERSON>", "short", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "am", "pm", "midnight", "noon", "morning", "afternoon", "evening", "night", "formattingDayPeriodValues", "ordinalNumber", "dirtyNumber", "options", "number", "Number", "unit", "ordinal", "localize", "era", "values", "defaultWidth", "quarter", "argument<PERSON>allback", "month", "day", "<PERSON><PERSON><PERSON><PERSON>", "formattingValues", "defaultFormattingWidth"], "sources": ["C:/Users/<USER>/Desktop/demo-git/demo-react/my-fullstack-app/frontend/node_modules/date-fns/locale/oc/_lib/localize.js"], "sourcesContent": ["import { buildLocalizeFn } from \"../../_lib/buildLocalizeFn.js\";\n\nconst eraValues = {\n  narrow: [\"ab. J.C.\", \"apr. J.C.\"],\n  abbreviated: [\"ab. J.C.\", \"apr. J.C.\"],\n  wide: [\"abans <PERSON>\", \"apr<PERSON>\"],\n};\n\nconst quarterValues = {\n  narrow: [\"T1\", \"T2\", \"T3\", \"T4\"],\n  abbreviated: [\"1èr trim.\", \"2nd trim.\", \"3en trim.\", \"4en trim.\"],\n  wide: [\"1èr trimèstre\", \"2nd trimèstre\", \"3en trimèstre\", \"4en trimèstre\"],\n};\n\nconst monthValues = {\n  narrow: [\n    \"GN\",\n    \"FB\",\n    \"MÇ\",\n    \"AB\",\n    \"MA\",\n    \"JN\",\n    \"JL\",\n    \"AG\",\n    \"ST\",\n    \"OC\",\n    \"NV\",\n    \"DC\",\n  ],\n\n  abbreviated: [\n    \"gen.\",\n    \"febr.\",\n    \"març\",\n    \"abr.\",\n    \"mai\",\n    \"junh\",\n    \"jul.\",\n    \"ag.\",\n    \"set.\",\n    \"oct.\",\n    \"nov.\",\n    \"dec.\",\n  ],\n\n  wide: [\n    \"genièr\",\n    \"febrièr\",\n    \"març\",\n    \"abril\",\n    \"mai\",\n    \"junh\",\n    \"julhet\",\n    \"agost\",\n    \"setembre\",\n    \"octòbre\",\n    \"novembre\",\n    \"decembre\",\n  ],\n};\n\nconst dayValues = {\n  narrow: [\"dg.\", \"dl.\", \"dm.\", \"dc.\", \"dj.\", \"dv.\", \"ds.\"],\n  short: [\"dg.\", \"dl.\", \"dm.\", \"dc.\", \"dj.\", \"dv.\", \"ds.\"],\n  abbreviated: [\"dg.\", \"dl.\", \"dm.\", \"dc.\", \"dj.\", \"dv.\", \"ds.\"],\n  wide: [\n    \"dimenge\",\n    \"diluns\",\n    \"dimars\",\n    \"dimècres\",\n    \"dijòus\",\n    \"divendres\",\n    \"dissabte\",\n  ],\n};\n\nconst dayPeriodValues = {\n  narrow: {\n    am: \"am\",\n    pm: \"pm\",\n    midnight: \"mièjanuèch\",\n    noon: \"miègjorn\",\n    morning: \"matin\",\n    afternoon: \"aprèp-miègjorn\",\n    evening: \"vèspre\",\n    night: \"nuèch\",\n  },\n  abbreviated: {\n    am: \"a.m.\",\n    pm: \"p.m.\",\n    midnight: \"mièjanuèch\",\n    noon: \"miègjorn\",\n    morning: \"matin\",\n    afternoon: \"aprèp-miègjorn\",\n    evening: \"vèspre\",\n    night: \"nuèch\",\n  },\n  wide: {\n    am: \"a.m.\",\n    pm: \"p.m.\",\n    midnight: \"mièjanuèch\",\n    noon: \"miègjorn\",\n    morning: \"matin\",\n    afternoon: \"aprèp-miègjorn\",\n    evening: \"vèspre\",\n    night: \"nuèch\",\n  },\n};\n\nconst formattingDayPeriodValues = {\n  narrow: {\n    am: \"am\",\n    pm: \"pm\",\n    midnight: \"mièjanuèch\",\n    noon: \"miègjorn\",\n    morning: \"del matin\",\n    afternoon: \"de l’aprèp-miègjorn\",\n    evening: \"del ser\",\n    night: \"de la nuèch\",\n  },\n  abbreviated: {\n    am: \"AM\",\n    pm: \"PM\",\n    midnight: \"mièjanuèch\",\n    noon: \"miègjorn\",\n    morning: \"del matin\",\n    afternoon: \"de l’aprèp-miègjorn\",\n    evening: \"del ser\",\n    night: \"de la nuèch\",\n  },\n  wide: {\n    am: \"ante meridiem\",\n    pm: \"post meridiem\",\n    midnight: \"mièjanuèch\",\n    noon: \"miègjorn\",\n    morning: \"del matin\",\n    afternoon: \"de l’aprèp-miègjorn\",\n    evening: \"del ser\",\n    night: \"de la nuèch\",\n  },\n};\n\nconst ordinalNumber = (dirtyNumber, options) => {\n  const number = Number(dirtyNumber);\n  const unit = options?.unit;\n  let ordinal;\n\n  switch (number) {\n    case 1:\n      ordinal = \"èr\";\n      break;\n    case 2:\n      ordinal = \"nd\";\n      break;\n    default:\n      ordinal = \"en\";\n  }\n\n  // feminine for year, week, hour, minute, second\n  if (\n    unit === \"year\" ||\n    unit === \"week\" ||\n    unit === \"hour\" ||\n    unit === \"minute\" ||\n    unit === \"second\"\n  ) {\n    ordinal += \"a\";\n  }\n\n  return number + ordinal;\n};\n\nexport const localize = {\n  ordinalNumber,\n\n  era: buildLocalizeFn({\n    values: eraValues,\n    defaultWidth: \"wide\",\n  }),\n\n  quarter: buildLocalizeFn({\n    values: quarterValues,\n    defaultWidth: \"wide\",\n    argumentCallback: (quarter) => quarter - 1,\n  }),\n\n  month: buildLocalizeFn({\n    values: monthValues,\n    defaultWidth: \"wide\",\n  }),\n\n  day: buildLocalizeFn({\n    values: dayValues,\n    defaultWidth: \"wide\",\n  }),\n\n  dayPeriod: buildLocalizeFn({\n    values: dayPeriodValues,\n    defaultWidth: \"wide\",\n    formattingValues: formattingDayPeriodValues,\n    defaultFormattingWidth: \"wide\",\n  }),\n};\n"], "mappings": "AAAA,SAASA,eAAe,QAAQ,+BAA+B;AAE/D,MAAMC,SAAS,GAAG;EAChBC,MAAM,EAAE,CAAC,UAAU,EAAE,WAAW,CAAC;EACjCC,WAAW,EAAE,CAAC,UAAU,EAAE,WAAW,CAAC;EACtCC,IAAI,EAAE,CAAC,mBAAmB,EAAE,mBAAmB;AACjD,CAAC;AAED,MAAMC,aAAa,GAAG;EACpBH,MAAM,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;EAChCC,WAAW,EAAE,CAAC,WAAW,EAAE,WAAW,EAAE,WAAW,EAAE,WAAW,CAAC;EACjEC,IAAI,EAAE,CAAC,eAAe,EAAE,eAAe,EAAE,eAAe,EAAE,eAAe;AAC3E,CAAC;AAED,MAAME,WAAW,GAAG;EAClBJ,MAAM,EAAE,CACN,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,CACL;EAEDC,WAAW,EAAE,CACX,MAAM,EACN,OAAO,EACP,MAAM,EACN,MAAM,EACN,KAAK,EACL,MAAM,EACN,MAAM,EACN,KAAK,EACL,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,CACP;EAEDC,IAAI,EAAE,CACJ,QAAQ,EACR,SAAS,EACT,MAAM,EACN,OAAO,EACP,KAAK,EACL,MAAM,EACN,QAAQ,EACR,OAAO,EACP,UAAU,EACV,SAAS,EACT,UAAU,EACV,UAAU;AAEd,CAAC;AAED,MAAMG,SAAS,GAAG;EAChBL,MAAM,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC;EACzDM,KAAK,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC;EACxDL,WAAW,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC;EAC9DC,IAAI,EAAE,CACJ,SAAS,EACT,QAAQ,EACR,QAAQ,EACR,UAAU,EACV,QAAQ,EACR,WAAW,EACX,UAAU;AAEd,CAAC;AAED,MAAMK,eAAe,GAAG;EACtBP,MAAM,EAAE;IACNQ,EAAE,EAAE,IAAI;IACRC,EAAE,EAAE,IAAI;IACRC,QAAQ,EAAE,YAAY;IACtBC,IAAI,EAAE,UAAU;IAChBC,OAAO,EAAE,OAAO;IAChBC,SAAS,EAAE,gBAAgB;IAC3BC,OAAO,EAAE,QAAQ;IACjBC,KAAK,EAAE;EACT,CAAC;EACDd,WAAW,EAAE;IACXO,EAAE,EAAE,MAAM;IACVC,EAAE,EAAE,MAAM;IACVC,QAAQ,EAAE,YAAY;IACtBC,IAAI,EAAE,UAAU;IAChBC,OAAO,EAAE,OAAO;IAChBC,SAAS,EAAE,gBAAgB;IAC3BC,OAAO,EAAE,QAAQ;IACjBC,KAAK,EAAE;EACT,CAAC;EACDb,IAAI,EAAE;IACJM,EAAE,EAAE,MAAM;IACVC,EAAE,EAAE,MAAM;IACVC,QAAQ,EAAE,YAAY;IACtBC,IAAI,EAAE,UAAU;IAChBC,OAAO,EAAE,OAAO;IAChBC,SAAS,EAAE,gBAAgB;IAC3BC,OAAO,EAAE,QAAQ;IACjBC,KAAK,EAAE;EACT;AACF,CAAC;AAED,MAAMC,yBAAyB,GAAG;EAChChB,MAAM,EAAE;IACNQ,EAAE,EAAE,IAAI;IACRC,EAAE,EAAE,IAAI;IACRC,QAAQ,EAAE,YAAY;IACtBC,IAAI,EAAE,UAAU;IAChBC,OAAO,EAAE,WAAW;IACpBC,SAAS,EAAE,qBAAqB;IAChCC,OAAO,EAAE,SAAS;IAClBC,KAAK,EAAE;EACT,CAAC;EACDd,WAAW,EAAE;IACXO,EAAE,EAAE,IAAI;IACRC,EAAE,EAAE,IAAI;IACRC,QAAQ,EAAE,YAAY;IACtBC,IAAI,EAAE,UAAU;IAChBC,OAAO,EAAE,WAAW;IACpBC,SAAS,EAAE,qBAAqB;IAChCC,OAAO,EAAE,SAAS;IAClBC,KAAK,EAAE;EACT,CAAC;EACDb,IAAI,EAAE;IACJM,EAAE,EAAE,eAAe;IACnBC,EAAE,EAAE,eAAe;IACnBC,QAAQ,EAAE,YAAY;IACtBC,IAAI,EAAE,UAAU;IAChBC,OAAO,EAAE,WAAW;IACpBC,SAAS,EAAE,qBAAqB;IAChCC,OAAO,EAAE,SAAS;IAClBC,KAAK,EAAE;EACT;AACF,CAAC;AAED,MAAME,aAAa,GAAGA,CAACC,WAAW,EAAEC,OAAO,KAAK;EAC9C,MAAMC,MAAM,GAAGC,MAAM,CAACH,WAAW,CAAC;EAClC,MAAMI,IAAI,GAAGH,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEG,IAAI;EAC1B,IAAIC,OAAO;EAEX,QAAQH,MAAM;IACZ,KAAK,CAAC;MACJG,OAAO,GAAG,IAAI;MACd;IACF,KAAK,CAAC;MACJA,OAAO,GAAG,IAAI;MACd;IACF;MACEA,OAAO,GAAG,IAAI;EAClB;;EAEA;EACA,IACED,IAAI,KAAK,MAAM,IACfA,IAAI,KAAK,MAAM,IACfA,IAAI,KAAK,MAAM,IACfA,IAAI,KAAK,QAAQ,IACjBA,IAAI,KAAK,QAAQ,EACjB;IACAC,OAAO,IAAI,GAAG;EAChB;EAEA,OAAOH,MAAM,GAAGG,OAAO;AACzB,CAAC;AAED,OAAO,MAAMC,QAAQ,GAAG;EACtBP,aAAa;EAEbQ,GAAG,EAAE3B,eAAe,CAAC;IACnB4B,MAAM,EAAE3B,SAAS;IACjB4B,YAAY,EAAE;EAChB,CAAC,CAAC;EAEFC,OAAO,EAAE9B,eAAe,CAAC;IACvB4B,MAAM,EAAEvB,aAAa;IACrBwB,YAAY,EAAE,MAAM;IACpBE,gBAAgB,EAAGD,OAAO,IAAKA,OAAO,GAAG;EAC3C,CAAC,CAAC;EAEFE,KAAK,EAAEhC,eAAe,CAAC;IACrB4B,MAAM,EAAEtB,WAAW;IACnBuB,YAAY,EAAE;EAChB,CAAC,CAAC;EAEFI,GAAG,EAAEjC,eAAe,CAAC;IACnB4B,MAAM,EAAErB,SAAS;IACjBsB,YAAY,EAAE;EAChB,CAAC,CAAC;EAEFK,SAAS,EAAElC,eAAe,CAAC;IACzB4B,MAAM,EAAEnB,eAAe;IACvBoB,YAAY,EAAE,MAAM;IACpBM,gBAAgB,EAAEjB,yBAAyB;IAC3CkB,sBAAsB,EAAE;EAC1B,CAAC;AACH,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "import { tzParseTimezone } from '../_lib/tzParseTimezone/index.js';\nimport { toDate } from '../toDate/index.js';\n/**\n * @name toZonedTime\n * @category Time Zone Helpers\n * @summary Get a date/time representing local time in a given time zone from the UTC date\n *\n * @description\n * Returns a date instance with values representing the local time in the time zone\n * specified of the UTC time from the date provided. In other words, when the new date\n * is formatted it will show the equivalent hours in the target time zone regardless\n * of the current system time zone.\n *\n * @param date the date with the relevant UTC time\n * @param timeZone the time zone to get local time for, can be an offset or IANA time zone\n * @param options the object with options. See [Options]{@link https://date-fns.org/docs/Options}\n * @param {0|1|2} [options.additionalDigits=2] - passed to `toDate`. See [toDate]{@link https://date-fns.org/docs/toDate}\n *\n * @throws {TypeError} 2 arguments required\n * @throws {RangeError} `options.additionalDigits` must be 0, 1 or 2\n *\n * @example\n * // In June 10am UTC is 6am in New York (-04:00)\n * const result = toZonedTime('2014-06-25T10:00:00.000Z', 'America/New_York')\n * //=> Jun 25 2014 06:00:00\n */\nexport function toZonedTime(date, timeZone, options) {\n  date = toDate(date, options);\n  const offsetMilliseconds = tzParseTimezone(timeZone, date, true);\n  const d = new Date(date.getTime() - offsetMilliseconds);\n  const resultDate = new Date(0);\n  resultDate.setFullYear(d.getUTCFullYear(), d.getUTCMonth(), d.getUTCDate());\n  resultDate.setHours(d.getUTCHours(), d.getUTCMinutes(), d.getUTCSeconds(), d.getUTCMilliseconds());\n  return resultDate;\n}", "map": {"version": 3, "names": ["tzParseTimezone", "toDate", "toZonedTime", "date", "timeZone", "options", "offsetMilliseconds", "d", "Date", "getTime", "resultDate", "setFullYear", "getUTCFullYear", "getUTCMonth", "getUTCDate", "setHours", "getUTCHours", "getUTCMinutes", "getUTCSeconds", "getUTCMilliseconds"], "sources": ["C:/Users/<USER>/Desktop/demo-git/demo-react/my-fullstack-app/frontend/node_modules/date-fns-tz/dist/esm/toZonedTime/index.js"], "sourcesContent": ["import { tzParseTimezone } from '../_lib/tzParseTimezone/index.js';\nimport { toDate } from '../toDate/index.js';\n/**\n * @name toZonedTime\n * @category Time Zone Helpers\n * @summary Get a date/time representing local time in a given time zone from the UTC date\n *\n * @description\n * Returns a date instance with values representing the local time in the time zone\n * specified of the UTC time from the date provided. In other words, when the new date\n * is formatted it will show the equivalent hours in the target time zone regardless\n * of the current system time zone.\n *\n * @param date the date with the relevant UTC time\n * @param timeZone the time zone to get local time for, can be an offset or IANA time zone\n * @param options the object with options. See [Options]{@link https://date-fns.org/docs/Options}\n * @param {0|1|2} [options.additionalDigits=2] - passed to `toDate`. See [toDate]{@link https://date-fns.org/docs/toDate}\n *\n * @throws {TypeError} 2 arguments required\n * @throws {RangeError} `options.additionalDigits` must be 0, 1 or 2\n *\n * @example\n * // In June 10am UTC is 6am in New York (-04:00)\n * const result = toZonedTime('2014-06-25T10:00:00.000Z', 'America/New_York')\n * //=> Jun 25 2014 06:00:00\n */\nexport function toZonedTime(date, timeZone, options) {\n    date = toDate(date, options);\n    const offsetMilliseconds = tzParseTimezone(timeZone, date, true);\n    const d = new Date(date.getTime() - offsetMilliseconds);\n    const resultDate = new Date(0);\n    resultDate.setFullYear(d.getUTCFullYear(), d.getUTCMonth(), d.getUTCDate());\n    resultDate.setHours(d.getUTCHours(), d.getUTCMinutes(), d.getUTCSeconds(), d.getUTCMilliseconds());\n    return resultDate;\n}\n"], "mappings": "AAAA,SAASA,eAAe,QAAQ,kCAAkC;AAClE,SAASC,MAAM,QAAQ,oBAAoB;AAC3C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,WAAWA,CAACC,IAAI,EAAEC,QAAQ,EAAEC,OAAO,EAAE;EACjDF,IAAI,GAAGF,MAAM,CAACE,IAAI,EAAEE,OAAO,CAAC;EAC5B,MAAMC,kBAAkB,GAAGN,eAAe,CAACI,QAAQ,EAAED,IAAI,EAAE,IAAI,CAAC;EAChE,MAAMI,CAAC,GAAG,IAAIC,IAAI,CAACL,IAAI,CAACM,OAAO,CAAC,CAAC,GAAGH,kBAAkB,CAAC;EACvD,MAAMI,UAAU,GAAG,IAAIF,IAAI,CAAC,CAAC,CAAC;EAC9BE,UAAU,CAACC,WAAW,CAACJ,CAAC,CAACK,cAAc,CAAC,CAAC,EAAEL,CAAC,CAACM,WAAW,CAAC,CAAC,EAAEN,CAAC,CAACO,UAAU,CAAC,CAAC,CAAC;EAC3EJ,UAAU,CAACK,QAAQ,CAACR,CAAC,CAACS,WAAW,CAAC,CAAC,EAAET,CAAC,CAACU,aAAa,CAAC,CAAC,EAAEV,CAAC,CAACW,aAAa,CAAC,CAAC,EAAEX,CAAC,CAACY,kBAAkB,CAAC,CAAC,CAAC;EAClG,OAAOT,UAAU;AACrB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
import React, { useState, useRef, useEffect } from 'react';
import { Button } from 'primereact/button';
import { Column } from 'primereact/column';
import { DataTable } from 'primereact/datatable';
import { Toast } from 'primereact/toast';
import { ConfirmDialog, confirmDialog } from 'primereact/confirmdialog';
import { Card } from 'primereact/card';
import { ProgressSpinner } from 'primereact/progressspinner';
import { Tag } from 'primereact/tag';
import { InputText } from 'primereact/inputtext';
import { Calendar } from 'primereact/calendar';
import { formatUtcToTaipei } from "../../utils/dateUtils";
import api from '../../services/api';
import { log } from '../../utils/logger';

interface ReportFile {
  fileName: string;
  filePath: string;
  fileSize: number;
  createdDate: string;
  modifiedDate: string;
}

const ReportManagementPage: React.FC = () => {
  const toast = useRef<Toast>(null);
  const [reports, setReports] = useState<ReportFile[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [fileNameFilter, setFileNameFilter] = useState('');
  const [startDateFilter, setStartDateFilter] = useState<Date | null>(null);
  const [endDateFilter, setEndDateFilter] = useState<Date | null>(null);

  // 載入報表列表
  const loadReports = async () => {
    try {
      setRefreshing(true);
      log.api('載入報表列表');

      const params = {
        fileName: fileNameFilter,
        startDate: startDateFilter ? startDateFilter.toISOString() : undefined,
        endDate: endDateFilter ? endDateFilter.toISOString() : undefined,
      };

      const response = await api.get('/api/file/GetReportFiles', { params });
      setReports(response.data);

      log.api('報表列表載入成功', { count: response.data.length });

    } catch (error: any) {
      log.error('載入報表列表失敗', error);
      toast.current?.show({
        severity: 'error',
        summary: '載入失敗',
        detail: '無法載入報表列表',
        life: 5000
      });
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  const handleSearch = () => {
    loadReports();
  };

  const handleReset = () => {
    setFileNameFilter('');
    setStartDateFilter(null);
    setEndDateFilter(null);
    loadReports();
  };

  // 刪除報表
  const deleteReport = async (report: ReportFile) => {
    try {
      log.api('刪除報表', { fileName: report.fileName });

      await api.delete(`/api/file/DeleteReportFile`, {
        params: { fileName: report.fileName }
      });

      toast.current?.show({
        severity: 'success',
        summary: '刪除成功',
        detail: `報表 ${report.fileName} 已刪除`,
        life: 3000
      });

      // 重新載入列表
      loadReports();

    } catch (error: any) {
      log.error('刪除報表失敗', error);
      toast.current?.show({
        severity: 'error',
        summary: '刪除失敗',
        detail: error.response?.data?.message || '刪除報表失敗',
        life: 5000
      });
    }
  };

  // 下載報表
  const downloadReport = async (report: ReportFile) => {
    try {
      log.api('下載報表', { fileName: report.fileName });

      const response = await api.get(`/api/file/DownloadReportFile`, {
        params: { fileName: report.fileName },
        responseType: 'blob'
      });

      // 創建下載連結
      const blob = new Blob([response.data], { type: 'application/pdf' });
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = report.fileName;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);

      toast.current?.show({
        severity: 'success',
        summary: '下載成功',
        detail: `報表 ${report.fileName} 下載完成`,
        life: 3000
      });

    } catch (error: any) {
      log.error('下載報表失敗', error);
      toast.current?.show({
        severity: 'error',
        summary: '下載失敗',
        detail: '下載報表失敗',
        life: 5000
      });
    }
  };

  // 預覽報表
  const previewReport = async (report: ReportFile) => {
    try {
      log.api('預覽報表', { fileName: report.fileName });

      const response = await api.get(`/api/file/DownloadReportFile`, {
        params: { fileName: report.fileName },
        responseType: 'blob'
      });

      // 在新視窗開啟 PDF
      const blob = new Blob([response.data], { type: 'application/pdf' });
      const url = window.URL.createObjectURL(blob);
      window.open(url, '_blank');

    } catch (error: any) {
      log.error('預覽報表失敗', error);
      toast.current?.show({
        severity: 'error',
        summary: '預覽失敗',
        detail: '預覽報表失敗',
        life: 5000
      });
    }
  };

  // 確認刪除
  const confirmDelete = (report: ReportFile) => {
    confirmDialog({
      message: `確定要刪除報表 "${report.fileName}" 嗎？此操作無法復原。`,
      header: '確認刪除',
      icon: 'pi pi-exclamation-triangle',
      acceptLabel: '確定',
      rejectLabel: '取消',
      accept: () => deleteReport(report),
    });
  };

  // 格式化檔案大小
  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  // 格式化日期
  const formatDate = (dateString: string): string => {
    if (!dateString) return '';
    try {
      return formatUtcToTaipei(dateString, 'yyyy/MM/dd HH:mm:ss');
    } catch (error) {
      console.error('Error formatting date:', error);
      return dateString;
    }
  };

  // 檔案大小模板
  const fileSizeBodyTemplate = (rowData: ReportFile) => {
    return formatFileSize(rowData.fileSize);
  };

  // 建立日期模板
  const createdDateBodyTemplate = (rowData: ReportFile) => {
    return formatDate(rowData.createdDate);
  };

  // 修改日期模板
  const modifiedDateBodyTemplate = (rowData: ReportFile) => {
    return formatDate(rowData.modifiedDate);
  };

  // 檔案類型模板
  const fileTypeBodyTemplate = (rowData: ReportFile) => {
    const extension = rowData.fileName.split('.').pop()?.toUpperCase();
    let severity: "success" | "info" | "warning" | "danger" = 'info';

    switch (extension) {
      case 'PDF':
        severity = 'danger';
        break;
      case 'XLSX':
      case 'XLS':
        severity = 'success';
        break;
      case 'DOCX':
      case 'DOC':
        severity = 'info';
        break;
      default:
        severity = 'warning';
    }

    return <Tag value={extension} severity={severity} />;
  };

  // 操作按鈕模板
  const actionBodyTemplate = (rowData: ReportFile) => {
    return (
      <div className="flex gap-2">
        <Button
          icon="pi pi-eye"
          className="p-button-info p-button-sm"
          onClick={() => previewReport(rowData)}
          tooltip="預覽"
          tooltipOptions={{ position: 'top' }}
        />
        <Button
          icon="pi pi-download"
          className="p-button-success p-button-sm"
          onClick={() => downloadReport(rowData)}
          tooltip="下載"
          tooltipOptions={{ position: 'top' }}
        />
        <Button
          icon="pi pi-trash"
          className="p-button-danger p-button-sm"
          onClick={() => confirmDelete(rowData)}
          tooltip="刪除"
          tooltipOptions={{ position: 'top' }}
        />
      </div>
    );
  };

  // 分頁器左側
  const paginatorLeft = (
    <Button
      type="button"
      icon="pi pi-refresh"
      text
      onClick={loadReports}
      disabled={refreshing}
    />
  );

  const paginatorRight = <div></div>;

  useEffect(() => {
    loadReports();
  }, []);

  if (loading) {
    return (
      <div className="flex justify-content-center align-items-center" style={{ height: '400px' }}>
        <ProgressSpinner />
      </div>
    );
  }

  return (
    <div>
      <Toast ref={toast} />
      <ConfirmDialog />

      <Card title="報表管理" className="mb-4">
        <p className="text-600 line-height-3 m-0">
          管理系統生成的 PDF 報表檔案，包括收據、統計報表等。您可以預覽、下載或刪除不需要的報表檔案。
        </p>
      </Card>

      {/* 搜尋條件 */}
      <Card className="mb-4">
        <div className="grid">
          <div className="col-12 md:col-4">
            <InputText 
              id="fileNameFilter" 
              value={fileNameFilter} 
              onChange={(e) => setFileNameFilter(e.target.value)} 
              placeholder="依檔名搜尋" 
              className="w-full"
            />
          </div>
          <div className="col-6 md:col-3">
            <Calendar 
              id="startDateFilter" 
              value={startDateFilter} 
              onChange={(e) => setStartDateFilter(e.value as Date)} 
              placeholder="選擇開始日期"
              className="w-full"
              dateFormat="yy/mm/dd" 
              showIcon />
          </div>
          <div className="col-6 md:col-3">
            <Calendar 
              id="endDateFilter" 
              value={endDateFilter} 
              onChange={(e) => setEndDateFilter(e.value as Date)} 
              placeholder="選擇結束日期"
              className="w-full"
              dateFormat="yy/mm/dd" 
              showIcon />
          </div>
          <div className="col-12 md:col-4">
            <div className="flex gap-2">
              <Button 
                label="搜尋" 
                icon="pi pi-search" 
                onClick={handleSearch} 
                className="mr-2" />
              <Button 
                label="重設" 
                icon="pi pi-undo" 
                onClick={handleReset} 
                className="p-button-secondary" />
            </div>
          </div>
        </div>
      </Card>

      <Card>
        <DataTable
          value={reports}
          paginator
          rows={20}
          rowsPerPageOptions={[10, 20, 50]}
          emptyMessage="沒有找到報表檔案"
          tableStyle={{ minWidth: '50rem' }}
          paginatorLeft={paginatorLeft}
          paginatorRight={paginatorRight}
          loading={refreshing}
        >
          <Column field="fileName" header="檔案名稱" sortable style={{ width: '30%' }} />
          <Column field="fileType" header="類型" body={fileTypeBodyTemplate} style={{ width: '10%' }} />
          <Column field="fileSize" header="檔案大小" body={fileSizeBodyTemplate} sortable style={{ width: '15%' }} />
          <Column field="createdDate" header="建立日期" body={createdDateBodyTemplate} sortable style={{ width: '20%' }} />
          <Column field="modifiedDate" header="修改日期" body={modifiedDateBodyTemplate} sortable style={{ width: '20%' }} />
          <Column header="操作" body={actionBodyTemplate} style={{ width: '15%' }} />
        </DataTable>
      </Card>
    </div>
  );
};

export default ReportManagementPage;

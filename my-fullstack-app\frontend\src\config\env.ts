/**
 * Environment configuration utilities
 */

export interface AppConfig {
  apiUrl: string;
  environment: 'development' | 'production' | 'test';
  debug: boolean;
  logLevel: 'debug' | 'info' | 'warn' | 'error';
}

/**
 * Get application configuration from environment variables
 */
export const getConfig = (): AppConfig => {
  const apiUrl = process.env.REACT_APP_API_URL;
  const environment = process.env.REACT_APP_ENVIRONMENT as AppConfig['environment'];
  const debug = process.env.REACT_APP_DEBUG === 'true';
  const logLevel = (process.env.REACT_APP_LOG_LEVEL || 'info') as AppConfig['logLevel'];

  // Validate required environment variables
  if (!apiUrl) {
    throw new Error('REACT_APP_API_URL is required');
  }

  if (!environment) {
    throw new Error('REACT_APP_ENVIRONMENT is required');
  }

  return {
    apiUrl,
    environment,
    debug,
    logLevel,
  };
};

/**
 * Application configuration instance
 */
export const config = getConfig();

/**
 * Check if running in development mode
 */
export const isDevelopment = () => config.environment === 'development';

/**
 * Check if running in production mode
 */
export const isProduction = () => config.environment === 'production';

/**
 * Check if debug mode is enabled
 */
export const isDebugEnabled = () => config.debug;

{"ast": null, "code": "const formatRelativeLocale = {\n  lastWeek: \"'ថ្ងៃ'eeee'ស​ប្តា​ហ៍​មុនម៉ោង' p\",\n  yesterday: \"'ម្សិលមិញនៅម៉ោង' p\",\n  today: \"'ថ្ងៃនេះម៉ោង' p\",\n  tomorrow: \"'ថ្ងៃស្អែកម៉ោង' p\",\n  nextWeek: \"'ថ្ងៃ'eeee'ស​ប្តា​ហ៍​ក្រោយម៉ោង' p\",\n  other: \"P\"\n};\nexport const formatRelative = (token, _date, _baseDate, _options) => formatRelativeLocale[token];", "map": {"version": 3, "names": ["formatRelativeLocale", "lastWeek", "yesterday", "today", "tomorrow", "nextWeek", "other", "formatRelative", "token", "_date", "_baseDate", "_options"], "sources": ["C:/Users/<USER>/Desktop/demo-git/demo-react/my-fullstack-app/frontend/node_modules/date-fns/locale/km/_lib/formatRelative.js"], "sourcesContent": ["const formatRelativeLocale = {\n  lastWeek: \"'ថ្ងៃ'eeee'ស​ប្តា​ហ៍​មុនម៉ោង' p\",\n  yesterday: \"'ម្សិលមិញនៅម៉ោង' p\",\n  today: \"'ថ្ងៃនេះម៉ោង' p\",\n  tomorrow: \"'ថ្ងៃស្អែកម៉ោង' p\",\n  nextWeek: \"'ថ្ងៃ'eeee'ស​ប្តា​ហ៍​ក្រោយម៉ោង' p\",\n  other: \"P\",\n};\n\nexport const formatRelative = (token, _date, _baseDate, _options) =>\n  formatRelativeLocale[token];\n"], "mappings": "AAAA,MAAMA,oBAAoB,GAAG;EAC3BC,QAAQ,EAAE,iCAAiC;EAC3CC,SAAS,EAAE,oBAAoB;EAC/BC,KAAK,EAAE,iBAAiB;EACxBC,QAAQ,EAAE,mBAAmB;EAC7BC,QAAQ,EAAE,mCAAmC;EAC7CC,KAAK,EAAE;AACT,CAAC;AAED,OAAO,MAAMC,cAAc,GAAGA,CAACC,KAAK,EAAEC,KAAK,EAAEC,SAAS,EAAEC,QAAQ,KAC9DX,oBAAoB,CAACQ,KAAK,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
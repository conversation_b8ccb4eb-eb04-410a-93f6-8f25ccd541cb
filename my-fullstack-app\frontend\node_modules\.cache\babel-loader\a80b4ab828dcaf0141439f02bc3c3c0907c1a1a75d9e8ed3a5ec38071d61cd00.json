{"ast": null, "code": "import { isSameWeek } from \"../../../isSameWeek.js\";\nconst adjectivesLastWeek = {\n  masculine: \"ostatni\",\n  feminine: \"ostatnia\"\n};\nconst adjectivesThisWeek = {\n  masculine: \"ten\",\n  feminine: \"ta\"\n};\nconst adjectivesNextWeek = {\n  masculine: \"następny\",\n  feminine: \"następna\"\n};\nconst dayGrammaticalGender = {\n  0: \"feminine\",\n  1: \"masculine\",\n  2: \"masculine\",\n  3: \"feminine\",\n  4: \"masculine\",\n  5: \"masculine\",\n  6: \"feminine\"\n};\nfunction dayAndTimeWithAdjective(token, date, baseDate, options) {\n  let adjectives;\n  if (isSameWeek(date, baseDate, options)) {\n    adjectives = adjectivesThisWeek;\n  } else if (token === \"lastWeek\") {\n    adjectives = adjectivesLastWeek;\n  } else if (token === \"nextWeek\") {\n    adjectives = adjectivesNextWeek;\n  } else {\n    throw new Error(\"Cannot determine adjectives for token \".concat(token));\n  }\n  const day = date.getDay();\n  const grammaticalGender = dayGrammaticalGender[day];\n  const adjective = adjectives[grammaticalGender];\n  return \"'\".concat(adjective, \"' eeee 'o' p\");\n}\nconst formatRelativeLocale = {\n  lastWeek: dayAndTimeWithAdjective,\n  yesterday: \"'wczoraj o' p\",\n  today: \"'dzisiaj o' p\",\n  tomorrow: \"'jutro o' p\",\n  nextWeek: dayAndTimeWithAdjective,\n  other: \"P\"\n};\nexport const formatRelative = (token, date, baseDate, options) => {\n  const format = formatRelativeLocale[token];\n  if (typeof format === \"function\") {\n    return format(token, date, baseDate, options);\n  }\n  return format;\n};", "map": {"version": 3, "names": ["isSameWeek", "adjectivesLastWeek", "masculine", "feminine", "adjectivesThisWeek", "adjectivesNextWeek", "dayGram<PERSON><PERSON><PERSON>", "dayAndTimeWithAdjective", "token", "date", "baseDate", "options", "adjectives", "Error", "concat", "day", "getDay", "grammaticalGender", "adjective", "formatRelativeLocale", "lastWeek", "yesterday", "today", "tomorrow", "nextWeek", "other", "formatRelative", "format"], "sources": ["C:/Users/<USER>/Desktop/demo-git/demo-react/my-fullstack-app/frontend/node_modules/date-fns/locale/pl/_lib/formatRelative.js"], "sourcesContent": ["import { isSameWeek } from \"../../../isSameWeek.js\";\n\nconst adjectivesLastWeek = {\n  masculine: \"ostatni\",\n  feminine: \"ostatnia\",\n};\n\nconst adjectivesThisWeek = {\n  masculine: \"ten\",\n  feminine: \"ta\",\n};\n\nconst adjectivesNextWeek = {\n  masculine: \"następny\",\n  feminine: \"następna\",\n};\n\nconst dayGrammaticalGender = {\n  0: \"feminine\",\n  1: \"masculine\",\n  2: \"masculine\",\n  3: \"feminine\",\n  4: \"masculine\",\n  5: \"masculine\",\n  6: \"feminine\",\n};\n\nfunction dayAndTimeWithAdjective(token, date, baseDate, options) {\n  let adjectives;\n  if (isSameWeek(date, baseDate, options)) {\n    adjectives = adjectivesThisWeek;\n  } else if (token === \"lastWeek\") {\n    adjectives = adjectivesLastWeek;\n  } else if (token === \"nextWeek\") {\n    adjectives = adjectivesNextWeek;\n  } else {\n    throw new Error(`Cannot determine adjectives for token ${token}`);\n  }\n\n  const day = date.getDay();\n  const grammaticalGender = dayGrammaticalGender[day];\n\n  const adjective = adjectives[grammaticalGender];\n\n  return `'${adjective}' eeee 'o' p`;\n}\n\nconst formatRelativeLocale = {\n  lastWeek: dayAndTimeWithAdjective,\n  yesterday: \"'wczoraj o' p\",\n  today: \"'dzisiaj o' p\",\n  tomorrow: \"'jutro o' p\",\n  nextWeek: dayAndTimeWithAdjective,\n  other: \"P\",\n};\n\nexport const formatRelative = (token, date, baseDate, options) => {\n  const format = formatRelativeLocale[token];\n\n  if (typeof format === \"function\") {\n    return format(token, date, baseDate, options);\n  }\n\n  return format;\n};\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,wBAAwB;AAEnD,MAAMC,kBAAkB,GAAG;EACzBC,SAAS,EAAE,SAAS;EACpBC,QAAQ,EAAE;AACZ,CAAC;AAED,MAAMC,kBAAkB,GAAG;EACzBF,SAAS,EAAE,KAAK;EAChBC,QAAQ,EAAE;AACZ,CAAC;AAED,MAAME,kBAAkB,GAAG;EACzBH,SAAS,EAAE,UAAU;EACrBC,QAAQ,EAAE;AACZ,CAAC;AAED,MAAMG,oBAAoB,GAAG;EAC3B,CAAC,EAAE,UAAU;EACb,CAAC,EAAE,WAAW;EACd,CAAC,EAAE,WAAW;EACd,CAAC,EAAE,UAAU;EACb,CAAC,EAAE,WAAW;EACd,CAAC,EAAE,WAAW;EACd,CAAC,EAAE;AACL,CAAC;AAED,SAASC,uBAAuBA,CAACC,KAAK,EAAEC,IAAI,EAAEC,QAAQ,EAAEC,OAAO,EAAE;EAC/D,IAAIC,UAAU;EACd,IAAIZ,UAAU,CAACS,IAAI,EAAEC,QAAQ,EAAEC,OAAO,CAAC,EAAE;IACvCC,UAAU,GAAGR,kBAAkB;EACjC,CAAC,MAAM,IAAII,KAAK,KAAK,UAAU,EAAE;IAC/BI,UAAU,GAAGX,kBAAkB;EACjC,CAAC,MAAM,IAAIO,KAAK,KAAK,UAAU,EAAE;IAC/BI,UAAU,GAAGP,kBAAkB;EACjC,CAAC,MAAM;IACL,MAAM,IAAIQ,KAAK,0CAAAC,MAAA,CAA0CN,KAAK,CAAE,CAAC;EACnE;EAEA,MAAMO,GAAG,GAAGN,IAAI,CAACO,MAAM,CAAC,CAAC;EACzB,MAAMC,iBAAiB,GAAGX,oBAAoB,CAACS,GAAG,CAAC;EAEnD,MAAMG,SAAS,GAAGN,UAAU,CAACK,iBAAiB,CAAC;EAE/C,WAAAH,MAAA,CAAWI,SAAS;AACtB;AAEA,MAAMC,oBAAoB,GAAG;EAC3BC,QAAQ,EAAEb,uBAAuB;EACjCc,SAAS,EAAE,eAAe;EAC1BC,KAAK,EAAE,eAAe;EACtBC,QAAQ,EAAE,aAAa;EACvBC,QAAQ,EAAEjB,uBAAuB;EACjCkB,KAAK,EAAE;AACT,CAAC;AAED,OAAO,MAAMC,cAAc,GAAGA,CAAClB,KAAK,EAAEC,IAAI,EAAEC,QAAQ,EAAEC,OAAO,KAAK;EAChE,MAAMgB,MAAM,GAAGR,oBAAoB,CAACX,KAAK,CAAC;EAE1C,IAAI,OAAOmB,MAAM,KAAK,UAAU,EAAE;IAChC,OAAOA,MAAM,CAACnB,KAAK,EAAEC,IAAI,EAAEC,QAAQ,EAAEC,OAAO,CAAC;EAC/C;EAEA,OAAOgB,MAAM;AACf,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "import { formatDistance } from \"./be-tarask/_lib/formatDistance.js\";\nimport { formatLong } from \"./be-tarask/_lib/formatLong.js\";\nimport { formatRelative } from \"./be-tarask/_lib/formatRelative.js\";\nimport { localize } from \"./be-tarask/_lib/localize.js\";\nimport { match } from \"./be-tarask/_lib/match.js\";\n\n/**\n * @category Locales\n * @summary Belarusian Classic locale.\n * @language Belarusian Classic\n * @iso-639-2 bel\n * <AUTHOR> [@nopears](https://github.com/nopears)\n */\nexport const beTarask = {\n  code: \"be-tarask\",\n  formatDistance: formatDistance,\n  formatLong: formatLong,\n  formatRelative: formatRelative,\n  localize: localize,\n  match: match,\n  options: {\n    weekStartsOn: 1 /* Monday */,\n    firstWeekContainsDate: 1\n  }\n};\n\n// Fallback for modularized imports:\nexport default beTarask;", "map": {"version": 3, "names": ["formatDistance", "formatLong", "formatRelative", "localize", "match", "beTarask", "code", "options", "weekStartsOn", "firstWeekContainsDate"], "sources": ["C:/Users/<USER>/Desktop/demo-git/demo-react/my-fullstack-app/frontend/node_modules/date-fns/locale/be-tarask.js"], "sourcesContent": ["import { formatDistance } from \"./be-tarask/_lib/formatDistance.js\";\nimport { formatLong } from \"./be-tarask/_lib/formatLong.js\";\nimport { formatRelative } from \"./be-tarask/_lib/formatRelative.js\";\nimport { localize } from \"./be-tarask/_lib/localize.js\";\nimport { match } from \"./be-tarask/_lib/match.js\";\n\n/**\n * @category Locales\n * @summary Belarusian Classic locale.\n * @language Belarusian Classic\n * @iso-639-2 bel\n * <AUTHOR> [@nopears](https://github.com/nopears)\n */\nexport const beTarask = {\n  code: \"be-tarask\",\n  formatDistance: formatDistance,\n  formatLong: formatLong,\n  formatRelative: formatRelative,\n  localize: localize,\n  match: match,\n  options: {\n    weekStartsOn: 1 /* Monday */,\n    firstWeekContainsDate: 1,\n  },\n};\n\n// Fallback for modularized imports:\nexport default beTarask;\n"], "mappings": "AAAA,SAASA,cAAc,QAAQ,oCAAoC;AACnE,SAASC,UAAU,QAAQ,gCAAgC;AAC3D,SAASC,cAAc,QAAQ,oCAAoC;AACnE,SAASC,QAAQ,QAAQ,8BAA8B;AACvD,SAASC,KAAK,QAAQ,2BAA2B;;AAEjD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMC,QAAQ,GAAG;EACtBC,IAAI,EAAE,WAAW;EACjBN,cAAc,EAAEA,cAAc;EAC9BC,UAAU,EAAEA,UAAU;EACtBC,cAAc,EAAEA,cAAc;EAC9BC,QAAQ,EAAEA,QAAQ;EAClBC,KAAK,EAAEA,KAAK;EACZG,OAAO,EAAE;IACPC,YAAY,EAAE,CAAC,CAAC;IAChBC,qBAAqB,EAAE;EACzB;AACF,CAAC;;AAED;AACA,eAAeJ,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
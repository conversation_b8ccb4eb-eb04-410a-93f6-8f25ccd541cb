-- 修改 IpBlock 表的 IPAddress 欄位約束
-- 將 UNIQUE 約束改為普通索引

-- 1. 顯示當前表結構
SELECT '=== 修改前的表結構 ===' as info;
DESCRIBE IpBlock;

-- 2. 顯示當前索引
SELECT '=== 修改前的索引 ===' as info;
SHOW INDEX FROM IpBlock;

-- 3. 刪除 UNIQUE 約束
-- 從輸出可以看到有一個名為 'IPAddress' 的 UNIQUE 索引
ALTER TABLE IpBlock DROP INDEX IPAddress;

-- 4. 驗證修改結果
SELECT '=== 修改後的表結構 ===' as info;
DESCRIBE IpBlock;

SELECT '=== 修改後的索引 ===' as info;
SHOW INDEX FROM IpBlock;

-- 5. 測試插入重複 IP（驗證約束已移除）
SELECT '=== 測試插入重複 IP ===' as info;

-- 插入測試資料
INSERT INTO IpBlock (IPAddress, CreatedAt, UpdatedAt, ExpiredAt) 
VALUES 
    ('*************', NOW(), NOW(), DATE_ADD(NOW(), INTERVAL 1 DAY)),
    ('*************', NOW(), NOW(), DATE_ADD(NOW(), INTERVAL 2 DAY));

-- 查看結果
SELECT 
    Id,
    IPAddress,
    CreatedAt,
    ExpiredAt,
    CASE 
        WHEN ExpiredAt > NOW() THEN 'Active'
        ELSE 'Expired'
    END as Status
FROM IpBlock 
WHERE IPAddress = '*************'
ORDER BY CreatedAt DESC;

-- 6. 清理測試資料
DELETE FROM IpBlock WHERE IPAddress = '*************';

SELECT '=== 修改完成 ===' as result;
SELECT 'IPAddress 欄位現在允許重複值，使用 idx_ipaddress 普通索引' as message;

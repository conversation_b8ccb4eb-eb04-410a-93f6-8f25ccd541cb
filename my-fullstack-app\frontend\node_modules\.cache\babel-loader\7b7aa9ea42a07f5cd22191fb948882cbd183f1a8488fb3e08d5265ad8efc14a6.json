{"ast": null, "code": "import { formatDistance } from \"./ca/_lib/formatDistance.js\";\nimport { formatLong } from \"./ca/_lib/formatLong.js\";\nimport { formatRelative } from \"./ca/_lib/formatRelative.js\";\nimport { localize } from \"./ca/_lib/localize.js\";\nimport { match } from \"./ca/_lib/match.js\";\n\n/**\n * @category Locales\n * @summary Catalan locale.\n * @language Catalan\n * @iso-639-2 cat\n * <AUTHOR> [@guigrpa](https://github.com/guigrpa)\n * <AUTHOR> [@avizcaino](https://github.com/avizcaino)\n */\nexport const ca = {\n  code: \"ca\",\n  formatDistance: formatDistance,\n  formatLong: formatLong,\n  formatRelative: formatRelative,\n  localize: localize,\n  match: match,\n  options: {\n    weekStartsOn: 1 /* Monday */,\n    firstWeekContainsDate: 4\n  }\n};\n\n// Fallback for modularized imports:\nexport default ca;", "map": {"version": 3, "names": ["formatDistance", "formatLong", "formatRelative", "localize", "match", "ca", "code", "options", "weekStartsOn", "firstWeekContainsDate"], "sources": ["C:/Users/<USER>/Desktop/demo-git/demo-react/my-fullstack-app/frontend/node_modules/date-fns/locale/ca.js"], "sourcesContent": ["import { formatDistance } from \"./ca/_lib/formatDistance.js\";\nimport { formatLong } from \"./ca/_lib/formatLong.js\";\nimport { formatRelative } from \"./ca/_lib/formatRelative.js\";\nimport { localize } from \"./ca/_lib/localize.js\";\nimport { match } from \"./ca/_lib/match.js\";\n\n/**\n * @category Locales\n * @summary Catalan locale.\n * @language Catalan\n * @iso-639-2 cat\n * <AUTHOR> [@guigrpa](https://github.com/guigrpa)\n * <AUTHOR> [@avizcaino](https://github.com/avizcaino)\n */\nexport const ca = {\n  code: \"ca\",\n  formatDistance: formatDistance,\n  formatLong: formatLong,\n  formatRelative: formatRelative,\n  localize: localize,\n  match: match,\n  options: {\n    weekStartsOn: 1 /* Monday */,\n    firstWeekContainsDate: 4,\n  },\n};\n\n// Fallback for modularized imports:\nexport default ca;\n"], "mappings": "AAAA,SAASA,cAAc,QAAQ,6BAA6B;AAC5D,SAASC,UAAU,QAAQ,yBAAyB;AACpD,SAASC,cAAc,QAAQ,6BAA6B;AAC5D,SAASC,QAAQ,QAAQ,uBAAuB;AAChD,SAASC,KAAK,QAAQ,oBAAoB;;AAE1C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMC,EAAE,GAAG;EAChBC,IAAI,EAAE,IAAI;EACVN,cAAc,EAAEA,cAAc;EAC9BC,UAAU,EAAEA,UAAU;EACtBC,cAAc,EAAEA,cAAc;EAC9BC,QAAQ,EAAEA,QAAQ;EAClBC,KAAK,EAAEA,KAAK;EACZG,OAAO,EAAE;IACPC,YAAY,EAAE,CAAC,CAAC;IAChBC,qBAAqB,EAAE;EACzB;AACF,CAAC;;AAED;AACA,eAAeJ,EAAE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
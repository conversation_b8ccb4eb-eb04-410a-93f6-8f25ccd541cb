{"ast": null, "code": "import{useEffect,useState}from\"react\";import api from'../services/api';// 對應 Treatment 的型別定義\nexport default function useReceipt(patientname,nationalId,startTime,endTime,refreshKey){const[receipts,setReceipts]=useState([]);const[loading,setLoading]=useState(true);useEffect(()=>{const fetchReceipts=async()=>{setLoading(true);api.get(\"/api/receipt/GetList\",{params:{patientname:patientname,nationalId:nationalId,starttime:startTime===null||startTime===void 0?void 0:startTime.toISOString(),endtime:endTime===null||endTime===void 0?void 0:endTime.toISOString()}}).then(res=>setReceipts(res.data)).catch(err=>console.error(\"API Error:\",err)).finally(()=>setLoading(false));};fetchReceipts();},[patientname,nationalId,startTime,endTime,refreshKey]);return{receipts,loading};}", "map": {"version": 3, "names": ["useEffect", "useState", "api", "useReceipt", "patientname", "nationalId", "startTime", "endTime", "refresh<PERSON><PERSON>", "receipts", "setReceipts", "loading", "setLoading", "fetchReceipts", "get", "params", "starttime", "toISOString", "endtime", "then", "res", "data", "catch", "err", "console", "error", "finally"], "sources": ["C:/Users/<USER>/Desktop/demo-git/demo-react/my-fullstack-app/frontend/src/hooks/useReceipt.ts"], "sourcesContent": ["import { useEffect, useState } from \"react\";\r\nimport api from '../services/api';\r\n\r\n// 對應 Treatment 的型別定義\r\nexport interface ReceiptItem {\r\n  Id: number;\r\n  orderNo: string;\r\n  ReceiptUrl: string;\r\n  ReceiptorderNo: string;\r\n  ReceiptCreatedAt: string;\r\n  ReceiptUpdatedAt: string;\r\n  ReceiptOperatorUserName: string;\r\n  PatientId: number;\r\n}\r\n\r\nexport default function useReceipt(\r\n  patientname: string,\r\n  nationalId: string,\r\n  startTime: Date | null | undefined,\r\n  endTime: Date | null | undefined,\r\n  refreshKey: number\r\n) {\r\n  const [receipts, setReceipts] = useState<ReceiptItem[]>([]);\r\n  const [loading, setLoading] = useState(true);\r\n\r\n  useEffect(() => {\r\n    const fetchReceipts = async () => {\r\n      setLoading(true);\r\n      api.get<ReceiptItem[]>(\"/api/receipt/GetList\", {\r\n        params: {\r\n          patientname: patientname,\r\n          nationalId: nationalId,\r\n          starttime: startTime?.toISOString(),\r\n          endtime: endTime?.toISOString(),\r\n        },\r\n      })\r\n        .then((res) => setReceipts(res.data))\r\n        .catch((err) => console.error(\"API Error:\", err))\r\n        .finally(() => setLoading(false));\r\n    };\r\n\r\n    fetchReceipts();\r\n  }, [patientname, nationalId, startTime, endTime, refreshKey]);\r\n\r\n  return { receipts, loading };\r\n}"], "mappings": "AAAA,OAASA,SAAS,CAAEC,QAAQ,KAAQ,OAAO,CAC3C,MAAO,CAAAC,GAAG,KAAM,iBAAiB,CAEjC;AAYA,cAAe,SAAS,CAAAC,UAAUA,CAChCC,WAAmB,CACnBC,UAAkB,CAClBC,SAAkC,CAClCC,OAAgC,CAChCC,UAAkB,CAClB,CACA,KAAM,CAACC,QAAQ,CAAEC,WAAW,CAAC,CAAGT,QAAQ,CAAgB,EAAE,CAAC,CAC3D,KAAM,CAACU,OAAO,CAAEC,UAAU,CAAC,CAAGX,QAAQ,CAAC,IAAI,CAAC,CAE5CD,SAAS,CAAC,IAAM,CACd,KAAM,CAAAa,aAAa,CAAG,KAAAA,CAAA,GAAY,CAChCD,UAAU,CAAC,IAAI,CAAC,CAChBV,GAAG,CAACY,GAAG,CAAgB,sBAAsB,CAAE,CAC7CC,MAAM,CAAE,CACNX,WAAW,CAAEA,WAAW,CACxBC,UAAU,CAAEA,UAAU,CACtBW,SAAS,CAAEV,SAAS,SAATA,SAAS,iBAATA,SAAS,CAAEW,WAAW,CAAC,CAAC,CACnCC,OAAO,CAAEX,OAAO,SAAPA,OAAO,iBAAPA,OAAO,CAAEU,WAAW,CAAC,CAChC,CACF,CAAC,CAAC,CACCE,IAAI,CAAEC,GAAG,EAAKV,WAAW,CAACU,GAAG,CAACC,IAAI,CAAC,CAAC,CACpCC,KAAK,CAAEC,GAAG,EAAKC,OAAO,CAACC,KAAK,CAAC,YAAY,CAAEF,GAAG,CAAC,CAAC,CAChDG,OAAO,CAAC,IAAMd,UAAU,CAAC,KAAK,CAAC,CAAC,CACrC,CAAC,CAEDC,aAAa,CAAC,CAAC,CACjB,CAAC,CAAE,CAACT,WAAW,CAAEC,UAAU,CAAEC,SAAS,CAAEC,OAAO,CAAEC,UAAU,CAAC,CAAC,CAE7D,MAAO,CAAEC,QAAQ,CAAEE,OAAQ,CAAC,CAC9B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
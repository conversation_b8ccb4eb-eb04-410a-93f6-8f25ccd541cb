{"ast": null, "code": "import React,{useState,useEffect,useCallback,useRef}from'react';import{Dropdown}from'primereact/dropdown';import{Checkbox}from'primereact/checkbox';import{Button}from'primereact/button';import{Toast}from'primereact/toast';import{Card}from'primereact/card';import{PermissionApi}from'../../services/apiService';// 導入新的 PermissionApi\n// 導入 Permission 類型\nimport{UserApi}from'../../services/apiService';// 導入 UserApi 以獲取角色列表\n// Type Definitions\nimport{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const PermissionPage=()=>{const toast=useRef(null);const[roles,setRoles]=useState([]);const[selectedRole,setSelectedRole]=useState(null);const[allPermissions,setAllPermissions]=useState([]);// 所有可用的細粒度權限\nconst[rolePermissions,setRolePermissions]=useState([]);// 當前角色擁有的權限代碼列表\nconst[loading,setLoading]=useState(false);// 獲取所有角色\nconst fetchRoles=useCallback(async()=>{try{// 假設獲取角色列表的 API 在 UserApi 中\nconst response=await UserApi.getRoles();setRoles(response.data.map(r=>({id:r.id,name:r.name})));}catch(error){var _toast$current,_error$response,_error$response$data;(_toast$current=toast.current)===null||_toast$current===void 0?void 0:_toast$current.show({severity:'error',summary:'獲取角色列表失敗',detail:((_error$response=error.response)===null||_error$response===void 0?void 0:(_error$response$data=_error$response.data)===null||_error$response$data===void 0?void 0:_error$response$data.message)||error.message});}},[]);// 獲取所有權限定義\nconst fetchAllPermissions=useCallback(async()=>{try{const permissions=await PermissionApi.getAllPermissions();setAllPermissions(permissions);}catch(error){var _toast$current2,_error$response2,_error$response2$data;(_toast$current2=toast.current)===null||_toast$current2===void 0?void 0:_toast$current2.show({severity:'error',summary:'獲取所有權限失敗',detail:((_error$response2=error.response)===null||_error$response2===void 0?void 0:(_error$response2$data=_error$response2.data)===null||_error$response2$data===void 0?void 0:_error$response2$data.message)||error.message});}},[]);// 獲取指定角色的權限\nconst fetchRolePermissions=useCallback(async roleId=>{setLoading(true);try{const permissions=await PermissionApi.getRolePermissions(roleId);setRolePermissions(permissions);}catch(error){var _toast$current3,_error$response3,_error$response3$data;(_toast$current3=toast.current)===null||_toast$current3===void 0?void 0:_toast$current3.show({severity:'error',summary:'獲取角色權限失敗',detail:((_error$response3=error.response)===null||_error$response3===void 0?void 0:(_error$response3$data=_error$response3.data)===null||_error$response3$data===void 0?void 0:_error$response3$data.message)||error.message});setRolePermissions([]);}finally{setLoading(false);}},[]);useEffect(()=>{fetchRoles();fetchAllPermissions();// 頁面載入時獲取所有權限定義\n},[fetchRoles,fetchAllPermissions]);useEffect(()=>{if(selectedRole){fetchRolePermissions(selectedRole);}else{setRolePermissions([]);}},[selectedRole,fetchRolePermissions]);const handlePermissionChange=(permissionCode,isChecked)=>{setRolePermissions(prevCodes=>{if(isChecked){return[...prevCodes,permissionCode];}else{return prevCodes.filter(code=>code!==permissionCode);}});};const handleSaveChanges=async()=>{if(!selectedRole)return;setLoading(true);try{var _toast$current4;await PermissionApi.updateRolePermissions(selectedRole,rolePermissions);(_toast$current4=toast.current)===null||_toast$current4===void 0?void 0:_toast$current4.show({severity:'success',summary:'權限更新成功',detail:\"\"});fetchRolePermissions(selectedRole);// 重新獲取以確保狀態同步\n}catch(error){var _toast$current5,_error$response4,_error$response4$data;(_toast$current5=toast.current)===null||_toast$current5===void 0?void 0:_toast$current5.show({severity:'error',summary:'權限更新失敗',detail:((_error$response4=error.response)===null||_error$response4===void 0?void 0:(_error$response4$data=_error$response4.data)===null||_error$response4$data===void 0?void 0:_error$response4$data.message)||error.message});}finally{setLoading(false);}};// 將權限按類別分組\nconst groupedPermissions=allPermissions.reduce((acc,permission)=>{const category=permission.Category||'未分類';if(!acc[category]){acc[category]=[];}acc[category].push(permission);return acc;},{});return/*#__PURE__*/_jsxs(\"div\",{className:\"p-grid p-fluid\",children:[/*#__PURE__*/_jsx(Toast,{ref:toast}),/*#__PURE__*/_jsx(\"div\",{className:\"p-col-12\",children:/*#__PURE__*/_jsxs(Card,{title:\"\\u6B0A\\u9650\\u7BA1\\u7406\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"p-field p-grid mb-4\",children:[/*#__PURE__*/_jsx(\"label\",{htmlFor:\"role\",className:\"p-col-12 p-md-2\",children:\"\\u9078\\u64C7\\u89D2\\u8272\"}),/*#__PURE__*/_jsx(\"div\",{className:\"p-col-12 p-md-10\",children:/*#__PURE__*/_jsx(Dropdown,{id:\"role\",value:selectedRole,options:roles,onChange:e=>setSelectedRole(e.value),optionLabel:\"name\",optionValue:\"id\",placeholder:\"\\u8ACB\\u9078\\u64C7\\u4E00\\u500B\\u89D2\\u8272\",style:{width:'100%'}})})]}),selectedRole&&Object.keys(groupedPermissions).map(category=>/*#__PURE__*/_jsxs(\"div\",{className:\"p-field mb-4\",children:[/*#__PURE__*/_jsx(\"h5\",{className:\"text-lg font-bold mb-2\",children:category}),/*#__PURE__*/_jsx(\"div\",{className:\"p-grid\",children:groupedPermissions[category].map(permission=>/*#__PURE__*/_jsxs(\"div\",{className:\"p-col-12 p-md-4\",children:[/*#__PURE__*/_jsx(Checkbox,{inputId:permission.Code,checked:rolePermissions.includes(permission.Code),onChange:e=>handlePermissionChange(permission.Code,!!e.checked)}),/*#__PURE__*/_jsxs(\"label\",{htmlFor:permission.Code,className:\"p-ml-2\",children:[permission.Name,\" (\",permission.Code,\")\"]})]},permission.Code))})]},category)),/*#__PURE__*/_jsx(\"div\",{className:\"p-d-flex p-jc-end mt-4\",children:/*#__PURE__*/_jsx(Button,{label:\"\\u5132\\u5B58\\u8B8A\\u66F4\",icon:\"pi pi-check\",onClick:handleSaveChanges,disabled:!selectedRole||loading,loading:loading})})]})})]});};export default PermissionPage;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useCallback", "useRef", "Dropdown", "Checkbox", "<PERSON><PERSON>", "Toast", "Card", "PermissionApi", "UserApi", "jsx", "_jsx", "jsxs", "_jsxs", "PermissionPage", "toast", "roles", "setRoles", "selectedR<PERSON>", "setSelectedRole", "allPermissions", "setAllPermissions", "rolePermissions", "setRolePermissions", "loading", "setLoading", "fetchRoles", "response", "getRoles", "data", "map", "r", "id", "name", "error", "_toast$current", "_error$response", "_error$response$data", "current", "show", "severity", "summary", "detail", "message", "fetchAllPermissions", "permissions", "getAllPermissions", "_toast$current2", "_error$response2", "_error$response2$data", "fetchRolePermissions", "roleId", "getRolePermissions", "_toast$current3", "_error$response3", "_error$response3$data", "handlePermissionChange", "permissionCode", "isChecked", "prevCodes", "filter", "code", "handleSaveChanges", "_toast$current4", "updateRolePermissions", "_toast$current5", "_error$response4", "_error$response4$data", "groupedPermissions", "reduce", "acc", "permission", "category", "Category", "push", "className", "children", "ref", "title", "htmlFor", "value", "options", "onChange", "e", "optionLabel", "optionValue", "placeholder", "style", "width", "Object", "keys", "inputId", "Code", "checked", "includes", "Name", "label", "icon", "onClick", "disabled"], "sources": ["C:/Users/<USER>/Desktop/demo-git/demo-react/my-fullstack-app/frontend/src/components/Page/PermissionPage.tsx"], "sourcesContent": ["import React, { useState, useEffect, useCallback, useRef } from 'react';\nimport { Dropdown, DropdownChangeEvent } from 'primereact/dropdown';\nimport { Checkbox, CheckboxChangeEvent } from 'primereact/checkbox';\nimport { Button } from 'primereact/button';\nimport { Toast } from 'primereact/toast';\nimport { Card } from 'primereact/card';\nimport { classNames } from 'primereact/utils';\nimport { PermissionApi } from '../../services/apiService'; // 導入新的 PermissionApi\nimport { Permission } from '../../types/api'; // 導入 Permission 類型\nimport { UserApi } from '../../services/apiService'; // 導入 UserApi 以獲取角色列表\n\n// Type Definitions\ninterface Role {\n    id: number;\n    name: string;\n}\n\ninterface GroupedPermissions {\n    [category: string]: Permission[];\n}\n\nconst PermissionPage: React.FC = () => {\n    const toast = useRef<Toast>(null);\n    const [roles, setRoles] = useState<Role[]>([]);\n    const [selectedRole, setSelectedRole] = useState<number | null>(null);\n    const [allPermissions, setAllPermissions] = useState<Permission[]>([]); // 所有可用的細粒度權限\n    const [rolePermissions, setRolePermissions] = useState<string[]>([]); // 當前角色擁有的權限代碼列表\n    const [loading, setLoading] = useState(false);\n\n    // 獲取所有角色\n    const fetchRoles = useCallback(async () => {\n        try {\n            // 假設獲取角色列表的 API 在 UserApi 中\n            const response = await UserApi.getRoles(); \n            setRoles(response.data.map((r: any) => ({ id: r.id, name: r.name })));\n        } catch (error: any) {\n            toast.current?.show({ severity: 'error', summary: '獲取角色列表失敗', detail: error.response?.data?.message || error.message });\n        }\n    }, []);\n\n    // 獲取所有權限定義\n    const fetchAllPermissions = useCallback(async () => {\n        try {\n            const permissions = await PermissionApi.getAllPermissions();\n            setAllPermissions(permissions);\n        } catch (error: any) {\n            toast.current?.show({ severity: 'error', summary: '獲取所有權限失敗', detail: error.response?.data?.message || error.message });\n        }\n    }, []);\n\n    // 獲取指定角色的權限\n    const fetchRolePermissions = useCallback(async (roleId: number) => {\n        setLoading(true);\n        try {\n            const permissions = await PermissionApi.getRolePermissions(roleId);\n            setRolePermissions(permissions);\n        } catch (error: any) {\n            toast.current?.show({ severity: 'error', summary: '獲取角色權限失敗', detail: error.response?.data?.message || error.message });\n            setRolePermissions([]);\n        } finally {\n            setLoading(false);\n        }\n    }, []);\n\n    useEffect(() => {\n        fetchRoles();\n        fetchAllPermissions(); // 頁面載入時獲取所有權限定義\n    }, [fetchRoles, fetchAllPermissions]);\n\n    useEffect(() => {\n        if (selectedRole) {\n            fetchRolePermissions(selectedRole);\n        } else {\n            setRolePermissions([]);\n        }\n    }, [selectedRole, fetchRolePermissions]);\n\n    const handlePermissionChange = (permissionCode: string, isChecked: boolean) => {\n        setRolePermissions(prevCodes => {\n            if (isChecked) {\n                return [...prevCodes, permissionCode];\n            } else {\n                return prevCodes.filter(code => code !== permissionCode);\n            }\n        });\n    };\n    \n    const handleSaveChanges = async () => {\n        if (!selectedRole) return;\n        setLoading(true);\n\n        try {\n            await PermissionApi.updateRolePermissions(selectedRole, rolePermissions);\n            toast.current?.show({ severity: 'success', summary: '權限更新成功', detail: \"\" });\n            fetchRolePermissions(selectedRole); // 重新獲取以確保狀態同步\n        } catch (error: any) {\n            toast.current?.show({ severity: 'error', summary: '權限更新失敗', detail: error.response?.data?.message || error.message });\n        } finally {\n            setLoading(false);\n        }\n    };\n\n    // 將權限按類別分組\n    const groupedPermissions = allPermissions.reduce<GroupedPermissions>((acc, permission) => {\n        const category = permission.Category || '未分類';\n        if (!acc[category]) {\n            acc[category] = [];\n        }\n        acc[category].push(permission);\n        return acc;\n    }, {});\n\n    return (\n        <div className=\"p-grid p-fluid\">\n            <Toast ref={toast} />\n            <div className=\"p-col-12\">\n                <Card title=\"權限管理\">\n                    <div className=\"p-field p-grid mb-4\">\n                        <label htmlFor=\"role\" className=\"p-col-12 p-md-2\">選擇角色</label>\n                        <div className=\"p-col-12 p-md-10\">\n                            <Dropdown\n                                id=\"role\"\n                                value={selectedRole}\n                                options={roles}\n                                onChange={(e: DropdownChangeEvent) => setSelectedRole(e.value)}\n                                optionLabel=\"name\"\n                                optionValue=\"id\"\n                                placeholder=\"請選擇一個角色\"\n                                style={{ width: '100%' }}\n                            />\n                        </div>\n                    </div>\n\n                    {selectedRole && Object.keys(groupedPermissions).map(category => (\n                        <div key={category} className=\"p-field mb-4\">\n                            <h5 className=\"text-lg font-bold mb-2\">{category}</h5>\n                            <div className=\"p-grid\">\n                                {groupedPermissions[category].map(permission => (\n                                    <div key={permission.Code} className=\"p-col-12 p-md-4\">\n                                        <Checkbox\n                                            inputId={permission.Code}\n                                            checked={rolePermissions.includes(permission.Code)}\n                                            onChange={(e: CheckboxChangeEvent) => handlePermissionChange(permission.Code, !!e.checked)}\n                                        />\n                                        <label htmlFor={permission.Code} className=\"p-ml-2\">\n                                            {permission.Name} ({permission.Code})\n                                        </label>\n                                    </div>\n                                ))}\n                            </div>\n                        </div>\n                    ))}\n\n                    <div className=\"p-d-flex p-jc-end mt-4\">\n                        <Button\n                            label=\"儲存變更\"\n                            icon=\"pi pi-check\"\n                            onClick={handleSaveChanges}\n                            disabled={!selectedRole || loading}\n                            loading={loading}\n                        />\n                    </div>\n                </Card>\n            </div>\n        </div>\n    );\n};\n\nexport default PermissionPage;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,CAAEC,WAAW,CAAEC,MAAM,KAAQ,OAAO,CACvE,OAASC,QAAQ,KAA6B,qBAAqB,CACnE,OAASC,QAAQ,KAA6B,qBAAqB,CACnE,OAASC,MAAM,KAAQ,mBAAmB,CAC1C,OAASC,KAAK,KAAQ,kBAAkB,CACxC,OAASC,IAAI,KAAQ,iBAAiB,CAEtC,OAASC,aAAa,KAAQ,2BAA2B,CAAE;AACb;AAC9C,OAASC,OAAO,KAAQ,2BAA2B,CAAE;AAErD;AAAA,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAUA,KAAM,CAAAC,cAAwB,CAAGA,CAAA,GAAM,CACnC,KAAM,CAAAC,KAAK,CAAGb,MAAM,CAAQ,IAAI,CAAC,CACjC,KAAM,CAACc,KAAK,CAAEC,QAAQ,CAAC,CAAGlB,QAAQ,CAAS,EAAE,CAAC,CAC9C,KAAM,CAACmB,YAAY,CAAEC,eAAe,CAAC,CAAGpB,QAAQ,CAAgB,IAAI,CAAC,CACrE,KAAM,CAACqB,cAAc,CAAEC,iBAAiB,CAAC,CAAGtB,QAAQ,CAAe,EAAE,CAAC,CAAE;AACxE,KAAM,CAACuB,eAAe,CAAEC,kBAAkB,CAAC,CAAGxB,QAAQ,CAAW,EAAE,CAAC,CAAE;AACtE,KAAM,CAACyB,OAAO,CAAEC,UAAU,CAAC,CAAG1B,QAAQ,CAAC,KAAK,CAAC,CAE7C;AACA,KAAM,CAAA2B,UAAU,CAAGzB,WAAW,CAAC,SAAY,CACvC,GAAI,CACA;AACA,KAAM,CAAA0B,QAAQ,CAAG,KAAM,CAAAlB,OAAO,CAACmB,QAAQ,CAAC,CAAC,CACzCX,QAAQ,CAACU,QAAQ,CAACE,IAAI,CAACC,GAAG,CAAEC,CAAM,GAAM,CAAEC,EAAE,CAAED,CAAC,CAACC,EAAE,CAAEC,IAAI,CAAEF,CAAC,CAACE,IAAK,CAAC,CAAC,CAAC,CAAC,CACzE,CAAE,MAAOC,KAAU,CAAE,KAAAC,cAAA,CAAAC,eAAA,CAAAC,oBAAA,CACjB,CAAAF,cAAA,CAAApB,KAAK,CAACuB,OAAO,UAAAH,cAAA,iBAAbA,cAAA,CAAeI,IAAI,CAAC,CAAEC,QAAQ,CAAE,OAAO,CAAEC,OAAO,CAAE,UAAU,CAAEC,MAAM,CAAE,EAAAN,eAAA,CAAAF,KAAK,CAACP,QAAQ,UAAAS,eAAA,kBAAAC,oBAAA,CAAdD,eAAA,CAAgBP,IAAI,UAAAQ,oBAAA,iBAApBA,oBAAA,CAAsBM,OAAO,GAAIT,KAAK,CAACS,OAAQ,CAAC,CAAC,CAC3H,CACJ,CAAC,CAAE,EAAE,CAAC,CAEN;AACA,KAAM,CAAAC,mBAAmB,CAAG3C,WAAW,CAAC,SAAY,CAChD,GAAI,CACA,KAAM,CAAA4C,WAAW,CAAG,KAAM,CAAArC,aAAa,CAACsC,iBAAiB,CAAC,CAAC,CAC3DzB,iBAAiB,CAACwB,WAAW,CAAC,CAClC,CAAE,MAAOX,KAAU,CAAE,KAAAa,eAAA,CAAAC,gBAAA,CAAAC,qBAAA,CACjB,CAAAF,eAAA,CAAAhC,KAAK,CAACuB,OAAO,UAAAS,eAAA,iBAAbA,eAAA,CAAeR,IAAI,CAAC,CAAEC,QAAQ,CAAE,OAAO,CAAEC,OAAO,CAAE,UAAU,CAAEC,MAAM,CAAE,EAAAM,gBAAA,CAAAd,KAAK,CAACP,QAAQ,UAAAqB,gBAAA,kBAAAC,qBAAA,CAAdD,gBAAA,CAAgBnB,IAAI,UAAAoB,qBAAA,iBAApBA,qBAAA,CAAsBN,OAAO,GAAIT,KAAK,CAACS,OAAQ,CAAC,CAAC,CAC3H,CACJ,CAAC,CAAE,EAAE,CAAC,CAEN;AACA,KAAM,CAAAO,oBAAoB,CAAGjD,WAAW,CAAC,KAAO,CAAAkD,MAAc,EAAK,CAC/D1B,UAAU,CAAC,IAAI,CAAC,CAChB,GAAI,CACA,KAAM,CAAAoB,WAAW,CAAG,KAAM,CAAArC,aAAa,CAAC4C,kBAAkB,CAACD,MAAM,CAAC,CAClE5B,kBAAkB,CAACsB,WAAW,CAAC,CACnC,CAAE,MAAOX,KAAU,CAAE,KAAAmB,eAAA,CAAAC,gBAAA,CAAAC,qBAAA,CACjB,CAAAF,eAAA,CAAAtC,KAAK,CAACuB,OAAO,UAAAe,eAAA,iBAAbA,eAAA,CAAed,IAAI,CAAC,CAAEC,QAAQ,CAAE,OAAO,CAAEC,OAAO,CAAE,UAAU,CAAEC,MAAM,CAAE,EAAAY,gBAAA,CAAApB,KAAK,CAACP,QAAQ,UAAA2B,gBAAA,kBAAAC,qBAAA,CAAdD,gBAAA,CAAgBzB,IAAI,UAAA0B,qBAAA,iBAApBA,qBAAA,CAAsBZ,OAAO,GAAIT,KAAK,CAACS,OAAQ,CAAC,CAAC,CACvHpB,kBAAkB,CAAC,EAAE,CAAC,CAC1B,CAAC,OAAS,CACNE,UAAU,CAAC,KAAK,CAAC,CACrB,CACJ,CAAC,CAAE,EAAE,CAAC,CAENzB,SAAS,CAAC,IAAM,CACZ0B,UAAU,CAAC,CAAC,CACZkB,mBAAmB,CAAC,CAAC,CAAE;AAC3B,CAAC,CAAE,CAAClB,UAAU,CAAEkB,mBAAmB,CAAC,CAAC,CAErC5C,SAAS,CAAC,IAAM,CACZ,GAAIkB,YAAY,CAAE,CACdgC,oBAAoB,CAAChC,YAAY,CAAC,CACtC,CAAC,IAAM,CACHK,kBAAkB,CAAC,EAAE,CAAC,CAC1B,CACJ,CAAC,CAAE,CAACL,YAAY,CAAEgC,oBAAoB,CAAC,CAAC,CAExC,KAAM,CAAAM,sBAAsB,CAAGA,CAACC,cAAsB,CAAEC,SAAkB,GAAK,CAC3EnC,kBAAkB,CAACoC,SAAS,EAAI,CAC5B,GAAID,SAAS,CAAE,CACX,MAAO,CAAC,GAAGC,SAAS,CAAEF,cAAc,CAAC,CACzC,CAAC,IAAM,CACH,MAAO,CAAAE,SAAS,CAACC,MAAM,CAACC,IAAI,EAAIA,IAAI,GAAKJ,cAAc,CAAC,CAC5D,CACJ,CAAC,CAAC,CACN,CAAC,CAED,KAAM,CAAAK,iBAAiB,CAAG,KAAAA,CAAA,GAAY,CAClC,GAAI,CAAC5C,YAAY,CAAE,OACnBO,UAAU,CAAC,IAAI,CAAC,CAEhB,GAAI,KAAAsC,eAAA,CACA,KAAM,CAAAvD,aAAa,CAACwD,qBAAqB,CAAC9C,YAAY,CAAEI,eAAe,CAAC,CACxE,CAAAyC,eAAA,CAAAhD,KAAK,CAACuB,OAAO,UAAAyB,eAAA,iBAAbA,eAAA,CAAexB,IAAI,CAAC,CAAEC,QAAQ,CAAE,SAAS,CAAEC,OAAO,CAAE,QAAQ,CAAEC,MAAM,CAAE,EAAG,CAAC,CAAC,CAC3EQ,oBAAoB,CAAChC,YAAY,CAAC,CAAE;AACxC,CAAE,MAAOgB,KAAU,CAAE,KAAA+B,eAAA,CAAAC,gBAAA,CAAAC,qBAAA,CACjB,CAAAF,eAAA,CAAAlD,KAAK,CAACuB,OAAO,UAAA2B,eAAA,iBAAbA,eAAA,CAAe1B,IAAI,CAAC,CAAEC,QAAQ,CAAE,OAAO,CAAEC,OAAO,CAAE,QAAQ,CAAEC,MAAM,CAAE,EAAAwB,gBAAA,CAAAhC,KAAK,CAACP,QAAQ,UAAAuC,gBAAA,kBAAAC,qBAAA,CAAdD,gBAAA,CAAgBrC,IAAI,UAAAsC,qBAAA,iBAApBA,qBAAA,CAAsBxB,OAAO,GAAIT,KAAK,CAACS,OAAQ,CAAC,CAAC,CACzH,CAAC,OAAS,CACNlB,UAAU,CAAC,KAAK,CAAC,CACrB,CACJ,CAAC,CAED;AACA,KAAM,CAAA2C,kBAAkB,CAAGhD,cAAc,CAACiD,MAAM,CAAqB,CAACC,GAAG,CAAEC,UAAU,GAAK,CACtF,KAAM,CAAAC,QAAQ,CAAGD,UAAU,CAACE,QAAQ,EAAI,KAAK,CAC7C,GAAI,CAACH,GAAG,CAACE,QAAQ,CAAC,CAAE,CAChBF,GAAG,CAACE,QAAQ,CAAC,CAAG,EAAE,CACtB,CACAF,GAAG,CAACE,QAAQ,CAAC,CAACE,IAAI,CAACH,UAAU,CAAC,CAC9B,MAAO,CAAAD,GAAG,CACd,CAAC,CAAE,CAAC,CAAC,CAAC,CAEN,mBACIzD,KAAA,QAAK8D,SAAS,CAAC,gBAAgB,CAAAC,QAAA,eAC3BjE,IAAA,CAACL,KAAK,EAACuE,GAAG,CAAE9D,KAAM,CAAE,CAAC,cACrBJ,IAAA,QAAKgE,SAAS,CAAC,UAAU,CAAAC,QAAA,cACrB/D,KAAA,CAACN,IAAI,EAACuE,KAAK,CAAC,0BAAM,CAAAF,QAAA,eACd/D,KAAA,QAAK8D,SAAS,CAAC,qBAAqB,CAAAC,QAAA,eAChCjE,IAAA,UAAOoE,OAAO,CAAC,MAAM,CAACJ,SAAS,CAAC,iBAAiB,CAAAC,QAAA,CAAC,0BAAI,CAAO,CAAC,cAC9DjE,IAAA,QAAKgE,SAAS,CAAC,kBAAkB,CAAAC,QAAA,cAC7BjE,IAAA,CAACR,QAAQ,EACL6B,EAAE,CAAC,MAAM,CACTgD,KAAK,CAAE9D,YAAa,CACpB+D,OAAO,CAAEjE,KAAM,CACfkE,QAAQ,CAAGC,CAAsB,EAAKhE,eAAe,CAACgE,CAAC,CAACH,KAAK,CAAE,CAC/DI,WAAW,CAAC,MAAM,CAClBC,WAAW,CAAC,IAAI,CAChBC,WAAW,CAAC,4CAAS,CACrBC,KAAK,CAAE,CAAEC,KAAK,CAAE,MAAO,CAAE,CAC5B,CAAC,CACD,CAAC,EACL,CAAC,CAELtE,YAAY,EAAIuE,MAAM,CAACC,IAAI,CAACtB,kBAAkB,CAAC,CAACtC,GAAG,CAAC0C,QAAQ,eACzD3D,KAAA,QAAoB8D,SAAS,CAAC,cAAc,CAAAC,QAAA,eACxCjE,IAAA,OAAIgE,SAAS,CAAC,wBAAwB,CAAAC,QAAA,CAAEJ,QAAQ,CAAK,CAAC,cACtD7D,IAAA,QAAKgE,SAAS,CAAC,QAAQ,CAAAC,QAAA,CAClBR,kBAAkB,CAACI,QAAQ,CAAC,CAAC1C,GAAG,CAACyC,UAAU,eACxC1D,KAAA,QAA2B8D,SAAS,CAAC,iBAAiB,CAAAC,QAAA,eAClDjE,IAAA,CAACP,QAAQ,EACLuF,OAAO,CAAEpB,UAAU,CAACqB,IAAK,CACzBC,OAAO,CAAEvE,eAAe,CAACwE,QAAQ,CAACvB,UAAU,CAACqB,IAAI,CAAE,CACnDV,QAAQ,CAAGC,CAAsB,EAAK3B,sBAAsB,CAACe,UAAU,CAACqB,IAAI,CAAE,CAAC,CAACT,CAAC,CAACU,OAAO,CAAE,CAC9F,CAAC,cACFhF,KAAA,UAAOkE,OAAO,CAAER,UAAU,CAACqB,IAAK,CAACjB,SAAS,CAAC,QAAQ,CAAAC,QAAA,EAC9CL,UAAU,CAACwB,IAAI,CAAC,IAAE,CAACxB,UAAU,CAACqB,IAAI,CAAC,GACxC,EAAO,CAAC,GARFrB,UAAU,CAACqB,IAShB,CACR,CAAC,CACD,CAAC,GAfApB,QAgBL,CACR,CAAC,cAEF7D,IAAA,QAAKgE,SAAS,CAAC,wBAAwB,CAAAC,QAAA,cACnCjE,IAAA,CAACN,MAAM,EACH2F,KAAK,CAAC,0BAAM,CACZC,IAAI,CAAC,aAAa,CAClBC,OAAO,CAAEpC,iBAAkB,CAC3BqC,QAAQ,CAAE,CAACjF,YAAY,EAAIM,OAAQ,CACnCA,OAAO,CAAEA,OAAQ,CACpB,CAAC,CACD,CAAC,EACJ,CAAC,CACN,CAAC,EACL,CAAC,CAEd,CAAC,CAED,cAAe,CAAAV,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
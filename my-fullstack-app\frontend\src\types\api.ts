/**
 * Common API types and interfaces
 */

// Base entity interface
export interface BaseEntity {
  Id: number;
  CreatedAt: string;
  UpdatedAt: string;
  IsDelete: boolean;
  OperatorUserId: number;
}

// Menu types
export interface MenuItem {
  itemId: string;
  path: string;
  name: string;
  isEnabled: boolean;
}

export interface MenuGroupItem {
  groupId: string;
  groupName: string;
  groupIcon: string;
  menus: MenuItem[];
}

// Data type interfaces
export interface DataTypeItem {
  itemId: number;
  number: string;
  name: string;
  isEnabled: boolean;
}

export interface DataTypeGroupItem {
  groupId: number;
  groupName: string;
  isEnabled: boolean;
  dataTypes: DataTypeItem[];
}

// Patient interface
export interface Patient extends BaseEntity {
  FullName: string;
  Gender: number;
  Phone: string;
  Address: string;
  BirthDate: string;
  Emergencycontact: string;
  Emergencyrelationship: string;
  Emergencyphone: string;
  NationalId: string;
  MedicalHistory: string;
  ExerciseHabit: string;
  ExerciseFrequency: string;
  InjuryHistory: string;
}

// Treatment interface
export interface Treatment extends BaseEntity {
  orderNo: string;
  Step: number;
  FrontAndBack: string;
  DiscomfortArea: string;
  DiscomfortSituation: string;
  DiscomfortPeriod: string;
  PossibleCauses: string;
  TreatmentHistory: string;
  HowToKnowOur: string;
  HospitalFormUrl: string;
  TreatmentConsentFormUrl: string;
  Subjective: string;
  Objective: string;
  Assessment: string;
  Plan: string;
  ReceiptUrl: string;
  UserId: number;
  PatientId: number;
}

// Receipt interface
export interface Receipt extends BaseEntity {
  // Add receipt specific properties here
  Amount: number;
  Description: string;
  PatientId: number;
  TreatmentId: number;
}

// User role interface
export interface UserRole {
  userId: number;
  userName: string;
  roleId: number;
  roleName: string;
}

// API request/response types
export interface PaginatedRequest {
  page?: number;
  pageSize?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

export interface PaginatedResponse<T> {
  data: T[];
  total: number;
  page: number;
  pageSize: number;
  totalPages: number;
}

// Search/filter types
export interface PatientSearchParams {
  name?: string | undefined;
  starttime?: string | undefined;
  endtime?: string | undefined;
}

export interface TreatmentSearchParams {
  patientname?: string  | undefined;
  nationalId?: string | undefined;
  doctortid?: number | undefined;
  starttime?: string | undefined;
  endtime?: string | undefined;
}

export interface ReceiptSearchParams {
  patientname?: string;
  nationalId?: string;
  starttime?: string;
  endtime?: string;
}

// Auth API types
export interface LoginResponse {
  token: string;
  username: string;
  userId: number;
  isDefaultPassword: boolean;
}

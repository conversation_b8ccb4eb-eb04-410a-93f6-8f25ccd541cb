import { useMemo } from "react";
import { PatientApi } from '../services/apiService';
import { Patient, PatientSearchParams } from '../types/api';
import { useApiData } from './useApiData';

interface UsePatientParams {
  fullName?: string;
  startTime?: Date | null;
  endTime?: Date | null;
  refreshKey?: number;
}

interface UsePatientReturn {
  patients: Patient[];
  loading: boolean;
  error: string | null;
  refetch: () => Promise<void>;
}

export default function usePatient({
  fullName = '',
  startTime,
  endTime,
  refreshKey = 0,
}: UsePatientParams = {}): UsePatientReturn {

  const searchParams: PatientSearchParams = useMemo(() => ({
    name: fullName || undefined,
    starttime: startTime?.toISOString(),
    endtime: endTime?.toISOString(),
  }), [fullName, startTime, endTime]);

  const apiCall = useMemo(() =>
    () => PatientApi.getList(searchParams),
    [searchParams]
  );

  const { data: patients = [], loading, error, refetch } = useApiData(
    apiCall,
    {
      dependencies: [refreshKey],
      initialData: [],
    }
  );

  return {
    patients,
    loading,
    error,
    refetch,
  };
}
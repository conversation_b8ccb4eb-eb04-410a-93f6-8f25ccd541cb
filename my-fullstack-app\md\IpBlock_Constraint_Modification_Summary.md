# IpBlock 表約束修改總結

## 📋 **修改內容**

### 🗄️ **資料庫結構變更**

#### **修改前**
```sql
IPAddress varchar(45) NOT NULL UNIQUE
```
- **約束類型**: UNIQUE (UNI)
- **限制**: 同一 IP 位址只能有一筆記錄
- **問題**: 無法記錄同一 IP 的多次封鎖歷史

#### **修改後**
```sql
IPAddress varchar(45) NOT NULL
```
- **約束類型**: 普通索引 (MUL)
- **優勢**: 同一 IP 位址可以有多筆記錄
- **索引**: 保留 `idx_ipaddress` 索引以維持查詢效能

### 🔧 **執行的 SQL 操作**

```sql
-- 1. 刪除 UNIQUE 約束
ALTER TABLE IpBlock DROP INDEX IPAddress;

-- 2. 驗證修改結果
DESCRIBE IpBlock;
SHOW INDEX FROM IpBlock;

-- 3. 測試插入重複 IP
INSERT INTO IpBlock (IPAddress, CreatedAt, UpdatedAt, ExpiredAt) 
VALUES 
    ('*************', NOW(), NOW(), DATE_ADD(NOW(), INTERVAL 1 DAY)),
    ('*************', NOW(), NOW(), DATE_ADD(NOW(), INTERVAL 2 DAY));
```

### 📊 **修改結果驗證**

#### **表結構對比**

| 欄位 | 修改前 | 修改後 |
|------|--------|--------|
| IPAddress | varchar(45) NOT NULL **UNI** | varchar(45) NOT NULL **MUL** |

#### **索引結構**

| 索引名稱 | 類型 | 欄位 | 說明 |
|----------|------|------|------|
| PRIMARY | UNIQUE | Id | 主鍵索引 |
| idx_ipaddress | **NON-UNIQUE** | IPAddress | 普通索引（查詢優化） |
| idx_expiredat | NON-UNIQUE | ExpiredAt | 到期時間索引 |
| idx_createdat | NON-UNIQUE | CreatedAt | 創建時間索引 |

### 🔧 **後端程式碼調整**

#### **SystemController.AddIpBlock 方法修改**

**修改前：**
```csharp
// 檢查是否已存在
var existing = await _context.IpBlocks
    .FirstOrDefaultAsync(x => x.IPAddress == request.IPAddress && x.ExpiredAt > DateTime.Now);

if (existing != null)
{
    // 更新現有記錄
    existing.ExpiredAt = expiredAt;
    existing.UpdatedAt = DateTime.Now;
}
else
{
    // 添加新記錄
    _context.IpBlocks.Add(ipBlock);
}
```

**修改後：**
```csharp
// 直接添加新記錄（允許同一 IP 有多筆記錄）
_context.IpBlocks.Add(ipBlock);
await _context.SaveChangesAsync();
```

#### **IpBlock 模型**
```csharp
[Table("IpBlock")]
public class IpBlock
{
    [Key]
    public int Id { get; set; }

    [Required]
    [StringLength(45)]
    public string IPAddress { get; set; } = string.Empty; // 移除 UNIQUE 約束

    [Required]
    public DateTime CreatedAt { get; set; } = DateTime.Now;

    [Required]
    public DateTime UpdatedAt { get; set; } = DateTime.Now;

    [Required]
    public DateTime ExpiredAt { get; set; }
}
```

## 🎯 **修改優勢**

### ✅ **功能增強**

1. **歷史記錄追蹤**
   - 可以記錄同一 IP 的多次封鎖歷史
   - 便於分析 IP 的封鎖模式和頻率

2. **靈活的封鎖管理**
   - 支援同一 IP 的不同封鎖期間
   - 可以設定多個重疊的封鎖時間段

3. **審計追蹤**
   - 完整的封鎖操作歷史
   - 便於安全分析和合規要求

### ✅ **系統穩定性**

1. **避免衝突**
   - 消除因 UNIQUE 約束導致的插入失敗
   - 提高系統的容錯能力

2. **並發處理**
   - 支援多個管理員同時操作
   - 減少資料庫鎖定衝突

## 🔍 **使用場景範例**

### **場景 1: 重複封鎖**
```sql
-- 同一 IP 可以有多筆封鎖記錄
INSERT INTO IpBlock (IPAddress, ExpiredAt) VALUES 
('*************', '2025-07-22 10:00:00'),  -- 第一次封鎖
('*************', '2025-07-25 15:30:00');  -- 第二次封鎖
```

### **場景 2: 歷史查詢**
```sql
-- 查詢某 IP 的所有封鎖歷史
SELECT 
    Id,
    IPAddress,
    CreatedAt,
    ExpiredAt,
    CASE 
        WHEN ExpiredAt > NOW() THEN 'Active'
        ELSE 'Expired'
    END as Status
FROM IpBlock 
WHERE IPAddress = '*************'
ORDER BY CreatedAt DESC;
```

### **場景 3: 活躍封鎖檢查**
```sql
-- 檢查 IP 是否目前被封鎖
SELECT COUNT(*) as ActiveBlocks
FROM IpBlock 
WHERE IPAddress = '*************' 
    AND ExpiredAt > NOW();
```

## 🚀 **部署狀態**

### ✅ **已完成項目**

1. **資料庫結構修改** ✅
   - 移除 IPAddress 的 UNIQUE 約束
   - 保留普通索引以維持查詢效能
   - 測試驗證修改成功

2. **後端程式碼更新** ✅
   - 修改 AddIpBlock 方法邏輯
   - 移除重複檢查機制
   - 支援同一 IP 多筆記錄

3. **服務重啟** ✅
   - 後端服務已重啟
   - 新邏輯已生效

### 📋 **影響評估**

#### **正面影響**
- ✅ 增強了系統的靈活性
- ✅ 支援更完整的審計追蹤
- ✅ 避免了 UNIQUE 約束衝突
- ✅ 提高了並發處理能力

#### **注意事項**
- 🔍 查詢活躍封鎖時需要檢查 ExpiredAt
- 🔍 Redis 快取邏輯保持不變
- 🔍 前端顯示邏輯無需修改

## 🎉 **修改完成**

**IpBlock 表的 IPAddress 欄位約束已成功從 UNIQUE (UNI) 修改為普通索引 (MUL)，現在支援同一 IP 位址的多筆封鎖記錄，增強了系統的靈活性和功能完整性。**

所有相關的後端程式碼也已同步更新，系統現在可以正常處理同一 IP 的重複封鎖操作。

# 進階權限系統實施指南

## 功能概述

本系統實現了完整的功能級權限控制和實時權限更新機制，包括：

1. **功能級權限控制**：讀取、編輯、刪除、導出、導入
2. **實時權限更新**：使用 SignalR 即時通知用戶權限變更
3. **權限守衛組件**：前端組件級權限控制
4. **權限檢查屬性**：後端 API 級權限驗證

## 新增功能

### 1. 功能級權限類型

- **讀取 (Read)**：查看數據的權限
- **編輯 (Write)**：新增和修改數據的權限
- **刪除 (Delete)**：刪除數據的權限
- **導出 (Export)**：導出數據的權限
- **導入 (Import)**：導入數據的權限

### 2. 實時權限更新

#### 後端實現
- **PermissionHub**：SignalR Hub 處理實時通信
- **權限變更通知**：角色權限更新時自動通知相關用戶
- **緩存失效**：權限更新時自動清理相關緩存

#### 前端實現
- **usePermissions Hook**：管理用戶權限狀態
- **SignalR 連接**：監聽權限更新事件
- **自動重新載入**：收到權限更新通知時自動刷新權限

### 3. 權限守衛系統

#### PermissionGuard 組件
```tsx
<PermissionGuard menuPath="/patients" action="write">
  <Button label="編輯" onClick={handleEdit} />
</PermissionGuard>
```

#### ActionButton 組件
```tsx
<ActionButton
  menuPath="/patients"
  action="delete"
  label="刪除"
  icon="pi pi-trash"
  severity="danger"
  onClick={handleDelete}
/>
```

### 4. 後端權限檢查

#### RequirePermission 屬性
```csharp
[RequirePermission("/patients", "write")]
[HttpPost("CreatePatient")]
public async Task<IActionResult> CreatePatient([FromBody] Patient patient)
{
    // 只有有寫入權限的用戶才能執行
}
```

## 實施步驟

### 1. 數據庫更新
```bash
# 執行權限擴展腳本
mysql -u username -p database < backend/Scripts/UpdateRoleMenuWithPermissions.sql
```

### 2. 後端服務註冊
已在 `Program.cs` 中自動註冊：
- `IPermissionService`
- `PermissionHub`

### 3. 前端權限集成

#### 在組件中使用權限
```tsx
import { usePermissions } from '../hooks/usePermissions';
import PermissionGuard from '../components/Common/PermissionGuard';

const MyComponent = () => {
  const { hasPermission } = usePermissions();

  return (
    <div>
      {/* 條件渲染 */}
      {hasPermission('/patients', 'write') && (
        <Button label="新增病患" onClick={handleAdd} />
      )}

      {/* 權限守衛 */}
      <PermissionGuard menuPath="/patients" action="delete">
        <Button label="刪除" severity="danger" onClick={handleDelete} />
      </PermissionGuard>
    </div>
  );
};
```

### 4. 控制器權限保護
```csharp
[RequirePermission("/patients", "read")]
[HttpGet("GetPatients")]
public async Task<IActionResult> GetPatients()
{
    // 實現邏輯
}

[RequirePermission("/patients", "write")]
[HttpPost("CreatePatient")]
public async Task<IActionResult> CreatePatient([FromBody] Patient patient)
{
    // 實現邏輯
}
```

## API 端點

### 權限管理
- `GET /api/RoleMenu/GetUserPermissions` - 獲取當前用戶權限
- `POST /api/RoleMenu/CheckPermission` - 檢查特定權限
- `POST /api/RoleMenu/UpdateRoleMenus` - 更新角色菜單權限（支持功能級權限）

### SignalR Hub
- `/permissionHub` - 權限實時更新 Hub

## 使用示例

### 1. 病患管理頁面權限控制
```tsx
const PatientsPage = () => {
  const { hasPermission } = usePermissions();

  return (
    <div>
      <DataTable value={patients}>
        <Column field="name" header="姓名" />
        <Column 
          header="操作" 
          body={(rowData) => (
            <div>
              <ActionButton
                menuPath="/patients"
                action="write"
                icon="pi pi-pencil"
                onClick={() => handleEdit(rowData)}
              />
              <ActionButton
                menuPath="/patients"
                action="delete"
                icon="pi pi-trash"
                severity="danger"
                onClick={() => handleDelete(rowData)}
              />
            </div>
          )}
        />
      </DataTable>

      <PermissionGuard menuPath="/patients" action="export">
        <Button label="導出數據" icon="pi pi-download" onClick={handleExport} />
      </PermissionGuard>
    </div>
  );
};
```

### 2. 治療記錄權限控制
```csharp
[ApiController]
[Route("api/[controller]")]
public class TreatmentsController : ControllerBase
{
    [RequirePermission("/treatments", "read")]
    [HttpGet]
    public async Task<IActionResult> GetTreatments()
    {
        // 查看治療記錄
    }

    [RequirePermission("/treatments", "write")]
    [HttpPost]
    public async Task<IActionResult> CreateTreatment([FromBody] Treatment treatment)
    {
        // 新增治療記錄
    }

    [RequirePermission("/treatments", "delete")]
    [HttpDelete("{id}")]
    public async Task<IActionResult> DeleteTreatment(int id)
    {
        // 刪除治療記錄
    }
}
```

## 權限配置建議

### Admin 角色
- 所有菜單：完整權限（讀取、編輯、刪除、導出、導入）

### Manager 角色
- 治療相關：讀取、編輯、導出
- 病患管理：讀取、編輯、導出
- 系統管理：讀取、導出（部分菜單）

### User 角色
- 治療相關：讀取、編輯
- 病患管理：讀取、編輯
- 系統管理：無權限

### Guest 角色
- 治療相關：僅讀取
- 病患管理：僅讀取
- 系統管理：無權限

## 性能優化

1. **權限緩存**：用戶權限緩存 30 分鐘
2. **批量權限檢查**：一次性載入用戶所有權限
3. **SignalR 群組**：按用戶分組，精確推送更新
4. **前端緩存**：避免重複的權限檢查請求

## 安全考量

1. **雙重驗證**：前端隱藏 + 後端驗證
2. **權限繼承**：多角色用戶取權限聯集
3. **緩存失效**：權限變更時立即清理緩存
4. **審計日誌**：記錄權限變更操作

## 故障排除

### 問題：權限更新不即時
**解決方案**：
1. 檢查 SignalR 連接狀態
2. 確認用戶已加入對應群組
3. 檢查權限緩存是否正確清理

### 問題：權限檢查失效
**解決方案**：
1. 確認 RequirePermission 屬性正確使用
2. 檢查 PermissionService 是否正確註冊
3. 驗證數據庫權限數據完整性

### 問題：前端權限顯示錯誤
**解決方案**：
1. 檢查 usePermissions Hook 是否正確載入
2. 確認權限數據格式正確
3. 驗證 PermissionGuard 組件邏輯

## 擴展建議

1. **時間限制權限**：添加權限有效期
2. **IP 限制權限**：基於 IP 地址的權限控制
3. **數據級權限**：行級數據訪問控制
4. **權限申請流程**：用戶申請權限的工作流
5. **權限報告**：權限使用情況統計和報告
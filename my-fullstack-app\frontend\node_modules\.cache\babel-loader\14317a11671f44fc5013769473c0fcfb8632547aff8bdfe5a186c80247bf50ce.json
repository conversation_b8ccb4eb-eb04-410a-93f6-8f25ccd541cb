{"ast": null, "code": "import _objectSpread from \"C:/Users/<USER>/Desktop/demo-git/demo-react/my-fullstack-app/frontend/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";\nimport { toDate } from '../toDate/index.js';\nimport { tzPattern } from '../_lib/tzPattern/index.js';\nimport { tzParseTimezone } from '../_lib/tzParseTimezone/index.js';\nimport { newDateUTC } from '../_lib/newDateUTC/index.js';\n/**\n * @name fromZonedTime\n * @category Time Zone Helpers\n * @summary Get the UTC date/time from a date representing local time in a given time zone\n *\n * @description\n * Returns a date instance with the UTC time of the provided date of which the values\n * represented the local time in the time zone specified. In other words, if the input\n * date represented local time in time zone, the timestamp of the output date will\n * give the equivalent UTC of that local time regardless of the current system time zone.\n *\n * @param date the date with values representing the local time\n * @param timeZone the time zone of this local time, can be an offset or IANA time zone\n * @param options the object with options. See [Options]{@link https://date-fns.org/docs/Options}\n * @param {0|1|2} [options.additionalDigits=2] - passed to `toDate`. See [toDate]{@link https://date-fns.org/docs/toDate}\n * @throws {TypeError} 2 arguments required\n * @throws {RangeError} `options.additionalDigits` must be 0, 1 or 2\n *\n * @example\n * // In June 10am in Los Angeles is 5pm UTC\n * const result = fromZonedTime(new Date(2014, 5, 25, 10, 0, 0), 'America/Los_Angeles')\n * //=> 2014-06-25T17:00:00.000Z\n */\nexport function fromZonedTime(date, timeZone, options) {\n  if (typeof date === 'string' && !date.match(tzPattern)) {\n    return toDate(date, _objectSpread(_objectSpread({}, options), {}, {\n      timeZone\n    }));\n  }\n  date = toDate(date, options);\n  const utc = newDateUTC(date.getFullYear(), date.getMonth(), date.getDate(), date.getHours(), date.getMinutes(), date.getSeconds(), date.getMilliseconds()).getTime();\n  const offsetMilliseconds = tzParseTimezone(timeZone, new Date(utc));\n  return new Date(utc + offsetMilliseconds);\n}", "map": {"version": 3, "names": ["toDate", "tzPattern", "tzParseTimezone", "newDateUTC", "fromZonedTime", "date", "timeZone", "options", "match", "_objectSpread", "utc", "getFullYear", "getMonth", "getDate", "getHours", "getMinutes", "getSeconds", "getMilliseconds", "getTime", "offsetMilliseconds", "Date"], "sources": ["C:/Users/<USER>/Desktop/demo-git/demo-react/my-fullstack-app/frontend/node_modules/date-fns-tz/dist/esm/fromZonedTime/index.js"], "sourcesContent": ["import { toDate } from '../toDate/index.js';\nimport { tzPattern } from '../_lib/tzPattern/index.js';\nimport { tzParseTimezone } from '../_lib/tzParseTimezone/index.js';\nimport { newDateUTC } from '../_lib/newDateUTC/index.js';\n/**\n * @name fromZonedTime\n * @category Time Zone Helpers\n * @summary Get the UTC date/time from a date representing local time in a given time zone\n *\n * @description\n * Returns a date instance with the UTC time of the provided date of which the values\n * represented the local time in the time zone specified. In other words, if the input\n * date represented local time in time zone, the timestamp of the output date will\n * give the equivalent UTC of that local time regardless of the current system time zone.\n *\n * @param date the date with values representing the local time\n * @param timeZone the time zone of this local time, can be an offset or IANA time zone\n * @param options the object with options. See [Options]{@link https://date-fns.org/docs/Options}\n * @param {0|1|2} [options.additionalDigits=2] - passed to `toDate`. See [toDate]{@link https://date-fns.org/docs/toDate}\n * @throws {TypeError} 2 arguments required\n * @throws {RangeError} `options.additionalDigits` must be 0, 1 or 2\n *\n * @example\n * // In June 10am in Los Angeles is 5pm UTC\n * const result = fromZonedTime(new Date(2014, 5, 25, 10, 0, 0), 'America/Los_Angeles')\n * //=> 2014-06-25T17:00:00.000Z\n */\nexport function fromZonedTime(date, timeZone, options) {\n    if (typeof date === 'string' && !date.match(tzPattern)) {\n        return toDate(date, { ...options, timeZone });\n    }\n    date = toDate(date, options);\n    const utc = newDateUTC(date.getFullYear(), date.getMonth(), date.getDate(), date.getHours(), date.getMinutes(), date.getSeconds(), date.getMilliseconds()).getTime();\n    const offsetMilliseconds = tzParseTimezone(timeZone, new Date(utc));\n    return new Date(utc + offsetMilliseconds);\n}\n"], "mappings": ";AAAA,SAASA,MAAM,QAAQ,oBAAoB;AAC3C,SAASC,SAAS,QAAQ,4BAA4B;AACtD,SAASC,eAAe,QAAQ,kCAAkC;AAClE,SAASC,UAAU,QAAQ,6BAA6B;AACxD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,aAAaA,CAACC,IAAI,EAAEC,QAAQ,EAAEC,OAAO,EAAE;EACnD,IAAI,OAAOF,IAAI,KAAK,QAAQ,IAAI,CAACA,IAAI,CAACG,KAAK,CAACP,SAAS,CAAC,EAAE;IACpD,OAAOD,MAAM,CAACK,IAAI,EAAAI,aAAA,CAAAA,aAAA,KAAOF,OAAO;MAAED;IAAQ,EAAE,CAAC;EACjD;EACAD,IAAI,GAAGL,MAAM,CAACK,IAAI,EAAEE,OAAO,CAAC;EAC5B,MAAMG,GAAG,GAAGP,UAAU,CAACE,IAAI,CAACM,WAAW,CAAC,CAAC,EAAEN,IAAI,CAACO,QAAQ,CAAC,CAAC,EAAEP,IAAI,CAACQ,OAAO,CAAC,CAAC,EAAER,IAAI,CAACS,QAAQ,CAAC,CAAC,EAAET,IAAI,CAACU,UAAU,CAAC,CAAC,EAAEV,IAAI,CAACW,UAAU,CAAC,CAAC,EAAEX,IAAI,CAACY,eAAe,CAAC,CAAC,CAAC,CAACC,OAAO,CAAC,CAAC;EACpK,MAAMC,kBAAkB,GAAGjB,eAAe,CAACI,QAAQ,EAAE,IAAIc,IAAI,CAACV,GAAG,CAAC,CAAC;EACnE,OAAO,IAAIU,IAAI,CAACV,GAAG,GAAGS,kBAAkB,CAAC;AAC7C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
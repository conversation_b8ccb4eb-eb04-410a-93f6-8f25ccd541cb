﻿// <auto-generated/>

#nullable enable annotations
#nullable disable warnings

// Suppress warnings about [Obsolete] member usage in generated code.
#pragma warning disable CS0612, CS0618

namespace MyApi.Helpers
{
    public partial class AppJsonContext
    {
        private static readonly global::System.Text.Json.JsonEncodedText PropName_Username = global::System.Text.Json.JsonEncodedText.Encode("Username");
        private static readonly global::System.Text.Json.JsonEncodedText PropName_Password = global::System.Text.Json.JsonEncodedText.Encode("Password");
    }
}

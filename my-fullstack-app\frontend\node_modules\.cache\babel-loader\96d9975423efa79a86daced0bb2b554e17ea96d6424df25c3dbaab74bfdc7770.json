{"ast": null, "code": "import{useCallback}from'react';import{useNavigate}from'react-router-dom';import{log}from'../utils/logger';/**\r\n * Custom hook for consistent error handling across the application\r\n */export function useErrorHandler(){const navigate=useNavigate();const handleError=useCallback(error=>{// Handle API errors\nif(isApiError(error)){return error.message;}// Handle standard Error objects\nif(error instanceof Error){return error.message;}// Handle string errors\nif(typeof error==='string'){return error;}// Fallback for unknown error types\nreturn'An unexpected error occurred';},[]);const handleErrorWithNavigation=useCallback((error,context)=>{var _error$response;// 標準化錯誤對象\nconst errorInfo={message:(error===null||error===void 0?void 0:error.message)||(error===null||error===void 0?void 0:error.toString())||'未知錯誤',stack:error===null||error===void 0?void 0:error.stack,code:error===null||error===void 0?void 0:error.code,statusCode:(error===null||error===void 0?void 0:(_error$response=error.response)===null||_error$response===void 0?void 0:_error$response.status)||(error===null||error===void 0?void 0:error.statusCode)};// 記錄錯誤\nlog.error(\"\\u932F\\u8AA4\\u8655\\u7406 \".concat(context?\"[\".concat(context,\"]\"):''),errorInfo);// 根據錯誤類型決定處理方式\nif(errorInfo.statusCode===401){log.warn('未授權訪問，重定向到登入頁面');navigate('/login',{replace:true});return;}if(errorInfo.statusCode===403){log.warn('訪問被禁止');navigate('/error',{state:{error:errorInfo,message:'您沒有權限訪問此資源'},replace:true});return;}// 其他錯誤 - 導航到通用錯誤頁面\nnavigate('/error',{state:{error:errorInfo,message:errorInfo.message},replace:true});},[navigate]);const handleApiError=useCallback((error,context)=>{var _error$response2,_error$response2$data,_error$response3,_error$response4,_error$response4$data;// 專門處理 API 錯誤\nconst apiError={message:(error===null||error===void 0?void 0:(_error$response2=error.response)===null||_error$response2===void 0?void 0:(_error$response2$data=_error$response2.data)===null||_error$response2$data===void 0?void 0:_error$response2$data.message)||(error===null||error===void 0?void 0:error.message)||'API 請求失敗',statusCode:error===null||error===void 0?void 0:(_error$response3=error.response)===null||_error$response3===void 0?void 0:_error$response3.status,code:(error===null||error===void 0?void 0:(_error$response4=error.response)===null||_error$response4===void 0?void 0:(_error$response4$data=_error$response4.data)===null||_error$response4$data===void 0?void 0:_error$response4$data.code)||(error===null||error===void 0?void 0:error.code),stack:error===null||error===void 0?void 0:error.stack};handleErrorWithNavigation(apiError,context||'API');},[handleErrorWithNavigation]);return{handleError,handleErrorWithNavigation,handleApiError};}/**\r\n * Type guard to check if error is an ApiError\r\n */function isApiError(error){return typeof error==='object'&&error!==null&&'message'in error&&typeof error.message==='string';}", "map": {"version": 3, "names": ["useCallback", "useNavigate", "log", "useErrorHandler", "navigate", "handleError", "error", "isApiError", "message", "Error", "handleErrorWithNavigation", "context", "_error$response", "errorInfo", "toString", "stack", "code", "statusCode", "response", "status", "concat", "warn", "replace", "state", "handleApiError", "_error$response2", "_error$response2$data", "_error$response3", "_error$response4", "_error$response4$data", "apiError", "data"], "sources": ["C:/Users/<USER>/Desktop/demo-git/demo-react/my-fullstack-app/frontend/src/hooks/useErrorHandler.ts"], "sourcesContent": ["import { useCallback } from 'react';\r\nimport { useNavigate } from 'react-router-dom';\r\nimport { ApiError } from '../services/api';\r\nimport { log } from '../utils/logger';\r\n\r\nexport interface ErrorInfo {\r\n  message: string;\r\n  stack?: string;\r\n  code?: string | number;\r\n  statusCode?: number;\r\n}\r\n\r\ninterface UseErrorHandlerReturn {\r\n  handleError: (error: unknown) => string;\r\n  handleErrorWithNavigation: (error: Error | ErrorInfo | any, context?: string) => void;\r\n  handleApiError: (error: any, context?: string) => void;\r\n}\r\n\r\n/**\r\n * Custom hook for consistent error handling across the application\r\n */\r\nexport function useErrorHandler(): UseErrorHandlerReturn {\r\n  const navigate = useNavigate();\r\n\r\n  const handleError = useCallback((error: unknown): string => {\r\n    // Handle API errors\r\n    if (isApiError(error)) {\r\n      return error.message;\r\n    }\r\n\r\n    // Handle standard Error objects\r\n    if (error instanceof Error) {\r\n      return error.message;\r\n    }\r\n\r\n    // Handle string errors\r\n    if (typeof error === 'string') {\r\n      return error;\r\n    }\r\n\r\n    // Fallback for unknown error types\r\n    return 'An unexpected error occurred';\r\n  }, []);\r\n\r\n  const handleErrorWithNavigation = useCallback((error: Error | ErrorInfo | any, context?: string) => {\r\n    // 標準化錯誤對象\r\n    const errorInfo: ErrorInfo = {\r\n      message: error?.message || error?.toString() || '未知錯誤',\r\n      stack: error?.stack,\r\n      code: error?.code,\r\n      statusCode: error?.response?.status || error?.statusCode\r\n    };\r\n\r\n    // 記錄錯誤\r\n    log.error(`錯誤處理 ${context ? `[${context}]` : ''}`, errorInfo);\r\n\r\n    // 根據錯誤類型決定處理方式\r\n    if (errorInfo.statusCode === 401) {\r\n      log.warn('未授權訪問，重定向到登入頁面');\r\n      navigate('/login', { replace: true });\r\n      return;\r\n    }\r\n\r\n    if (errorInfo.statusCode === 403) {\r\n      log.warn('訪問被禁止');\r\n      navigate('/error', {\r\n        state: {\r\n          error: errorInfo,\r\n          message: '您沒有權限訪問此資源'\r\n        },\r\n        replace: true\r\n      });\r\n      return;\r\n    }\r\n\r\n    // 其他錯誤 - 導航到通用錯誤頁面\r\n    navigate('/error', {\r\n      state: {\r\n        error: errorInfo,\r\n        message: errorInfo.message\r\n      },\r\n      replace: true\r\n    });\r\n  }, [navigate]);\r\n\r\n  const handleApiError = useCallback((error: any, context?: string) => {\r\n    // 專門處理 API 錯誤\r\n    const apiError: ErrorInfo = {\r\n      message: error?.response?.data?.message || error?.message || 'API 請求失敗',\r\n      statusCode: error?.response?.status,\r\n      code: error?.response?.data?.code || error?.code,\r\n      stack: error?.stack\r\n    };\r\n\r\n    handleErrorWithNavigation(apiError, context || 'API');\r\n  }, [handleErrorWithNavigation]);\r\n\r\n  return {\r\n    handleError,\r\n    handleErrorWithNavigation,\r\n    handleApiError\r\n  };\r\n}\r\n\r\n/**\r\n * Type guard to check if error is an ApiError\r\n */\r\nfunction isApiError(error: unknown): error is ApiError {\r\n  return (\r\n    typeof error === 'object' &&\r\n    error !== null &&\r\n    'message' in error &&\r\n    typeof (error as any).message === 'string'\r\n  );\r\n}\r\n"], "mappings": "AAAA,OAASA,WAAW,KAAQ,OAAO,CACnC,OAASC,WAAW,KAAQ,kBAAkB,CAE9C,OAASC,GAAG,KAAQ,iBAAiB,CAerC;AACA;AACA,GACA,MAAO,SAAS,CAAAC,eAAeA,CAAA,CAA0B,CACvD,KAAM,CAAAC,QAAQ,CAAGH,WAAW,CAAC,CAAC,CAE9B,KAAM,CAAAI,WAAW,CAAGL,WAAW,CAAEM,KAAc,EAAa,CAC1D;AACA,GAAIC,UAAU,CAACD,KAAK,CAAC,CAAE,CACrB,MAAO,CAAAA,KAAK,CAACE,OAAO,CACtB,CAEA;AACA,GAAIF,KAAK,WAAY,CAAAG,KAAK,CAAE,CAC1B,MAAO,CAAAH,KAAK,CAACE,OAAO,CACtB,CAEA;AACA,GAAI,MAAO,CAAAF,KAAK,GAAK,QAAQ,CAAE,CAC7B,MAAO,CAAAA,KAAK,CACd,CAEA;AACA,MAAO,8BAA8B,CACvC,CAAC,CAAE,EAAE,CAAC,CAEN,KAAM,CAAAI,yBAAyB,CAAGV,WAAW,CAAC,CAACM,KAA8B,CAAEK,OAAgB,GAAK,KAAAC,eAAA,CAClG;AACA,KAAM,CAAAC,SAAoB,CAAG,CAC3BL,OAAO,CAAE,CAAAF,KAAK,SAALA,KAAK,iBAALA,KAAK,CAAEE,OAAO,IAAIF,KAAK,SAALA,KAAK,iBAALA,KAAK,CAAEQ,QAAQ,CAAC,CAAC,GAAI,MAAM,CACtDC,KAAK,CAAET,KAAK,SAALA,KAAK,iBAALA,KAAK,CAAES,KAAK,CACnBC,IAAI,CAAEV,KAAK,SAALA,KAAK,iBAALA,KAAK,CAAEU,IAAI,CACjBC,UAAU,CAAE,CAAAX,KAAK,SAALA,KAAK,kBAAAM,eAAA,CAALN,KAAK,CAAEY,QAAQ,UAAAN,eAAA,iBAAfA,eAAA,CAAiBO,MAAM,IAAIb,KAAK,SAALA,KAAK,iBAALA,KAAK,CAAEW,UAAU,CAC1D,CAAC,CAED;AACAf,GAAG,CAACI,KAAK,6BAAAc,MAAA,CAAST,OAAO,KAAAS,MAAA,CAAOT,OAAO,MAAM,EAAE,EAAIE,SAAS,CAAC,CAE7D;AACA,GAAIA,SAAS,CAACI,UAAU,GAAK,GAAG,CAAE,CAChCf,GAAG,CAACmB,IAAI,CAAC,gBAAgB,CAAC,CAC1BjB,QAAQ,CAAC,QAAQ,CAAE,CAAEkB,OAAO,CAAE,IAAK,CAAC,CAAC,CACrC,OACF,CAEA,GAAIT,SAAS,CAACI,UAAU,GAAK,GAAG,CAAE,CAChCf,GAAG,CAACmB,IAAI,CAAC,OAAO,CAAC,CACjBjB,QAAQ,CAAC,QAAQ,CAAE,CACjBmB,KAAK,CAAE,CACLjB,KAAK,CAAEO,SAAS,CAChBL,OAAO,CAAE,YACX,CAAC,CACDc,OAAO,CAAE,IACX,CAAC,CAAC,CACF,OACF,CAEA;AACAlB,QAAQ,CAAC,QAAQ,CAAE,CACjBmB,KAAK,CAAE,CACLjB,KAAK,CAAEO,SAAS,CAChBL,OAAO,CAAEK,SAAS,CAACL,OACrB,CAAC,CACDc,OAAO,CAAE,IACX,CAAC,CAAC,CACJ,CAAC,CAAE,CAAClB,QAAQ,CAAC,CAAC,CAEd,KAAM,CAAAoB,cAAc,CAAGxB,WAAW,CAAC,CAACM,KAAU,CAAEK,OAAgB,GAAK,KAAAc,gBAAA,CAAAC,qBAAA,CAAAC,gBAAA,CAAAC,gBAAA,CAAAC,qBAAA,CACnE;AACA,KAAM,CAAAC,QAAmB,CAAG,CAC1BtB,OAAO,CAAE,CAAAF,KAAK,SAALA,KAAK,kBAAAmB,gBAAA,CAALnB,KAAK,CAAEY,QAAQ,UAAAO,gBAAA,kBAAAC,qBAAA,CAAfD,gBAAA,CAAiBM,IAAI,UAAAL,qBAAA,iBAArBA,qBAAA,CAAuBlB,OAAO,IAAIF,KAAK,SAALA,KAAK,iBAALA,KAAK,CAAEE,OAAO,GAAI,UAAU,CACvES,UAAU,CAAEX,KAAK,SAALA,KAAK,kBAAAqB,gBAAA,CAALrB,KAAK,CAAEY,QAAQ,UAAAS,gBAAA,iBAAfA,gBAAA,CAAiBR,MAAM,CACnCH,IAAI,CAAE,CAAAV,KAAK,SAALA,KAAK,kBAAAsB,gBAAA,CAALtB,KAAK,CAAEY,QAAQ,UAAAU,gBAAA,kBAAAC,qBAAA,CAAfD,gBAAA,CAAiBG,IAAI,UAAAF,qBAAA,iBAArBA,qBAAA,CAAuBb,IAAI,IAAIV,KAAK,SAALA,KAAK,iBAALA,KAAK,CAAEU,IAAI,EAChDD,KAAK,CAAET,KAAK,SAALA,KAAK,iBAALA,KAAK,CAAES,KAChB,CAAC,CAEDL,yBAAyB,CAACoB,QAAQ,CAAEnB,OAAO,EAAI,KAAK,CAAC,CACvD,CAAC,CAAE,CAACD,yBAAyB,CAAC,CAAC,CAE/B,MAAO,CACLL,WAAW,CACXK,yBAAyB,CACzBc,cACF,CAAC,CACH,CAEA;AACA;AACA,GACA,QAAS,CAAAjB,UAAUA,CAACD,KAAc,CAAqB,CACrD,MACE,OAAO,CAAAA,KAAK,GAAK,QAAQ,EACzBA,KAAK,GAAK,IAAI,EACd,SAAS,EAAI,CAAAA,KAAK,EAClB,MAAQ,CAAAA,KAAK,CAASE,OAAO,GAAK,QAAQ,CAE9C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "const formatDistanceLocale = {\n  lessThanXSeconds: {\n    one: \"1秒未満\",\n    other: \"{{count}}秒未満\",\n    oneWithSuffix: \"約1秒\",\n    otherWithSuffix: \"約{{count}}秒\"\n  },\n  xSeconds: {\n    one: \"1秒\",\n    other: \"{{count}}秒\"\n  },\n  halfAMinute: \"30秒\",\n  lessThanXMinutes: {\n    one: \"1分未満\",\n    other: \"{{count}}分未満\",\n    oneWithSuffix: \"約1分\",\n    otherWithSuffix: \"約{{count}}分\"\n  },\n  xMinutes: {\n    one: \"1分\",\n    other: \"{{count}}分\"\n  },\n  aboutXHours: {\n    one: \"約1時間\",\n    other: \"約{{count}}時間\"\n  },\n  xHours: {\n    one: \"1時間\",\n    other: \"{{count}}時間\"\n  },\n  xDays: {\n    one: \"1日\",\n    other: \"{{count}}日\"\n  },\n  aboutXWeeks: {\n    one: \"約1週間\",\n    other: \"約{{count}}週間\"\n  },\n  xWeeks: {\n    one: \"1週間\",\n    other: \"{{count}}週間\"\n  },\n  aboutXMonths: {\n    one: \"約1か月\",\n    other: \"約{{count}}か月\"\n  },\n  xMonths: {\n    one: \"1か月\",\n    other: \"{{count}}か月\"\n  },\n  aboutXYears: {\n    one: \"約1年\",\n    other: \"約{{count}}年\"\n  },\n  xYears: {\n    one: \"1年\",\n    other: \"{{count}}年\"\n  },\n  overXYears: {\n    one: \"1年以上\",\n    other: \"{{count}}年以上\"\n  },\n  almostXYears: {\n    one: \"1年近く\",\n    other: \"{{count}}年近く\"\n  }\n};\nexport const formatDistance = (token, count, options) => {\n  options = options || {};\n  let result;\n  const tokenValue = formatDistanceLocale[token];\n  if (typeof tokenValue === \"string\") {\n    result = tokenValue;\n  } else if (count === 1) {\n    if (options.addSuffix && tokenValue.oneWithSuffix) {\n      result = tokenValue.oneWithSuffix;\n    } else {\n      result = tokenValue.one;\n    }\n  } else {\n    if (options.addSuffix && tokenValue.otherWithSuffix) {\n      result = tokenValue.otherWithSuffix.replace(\"{{count}}\", String(count));\n    } else {\n      result = tokenValue.other.replace(\"{{count}}\", String(count));\n    }\n  }\n  if (options.addSuffix) {\n    if (options.comparison && options.comparison > 0) {\n      return result + \"後\";\n    } else {\n      return result + \"前\";\n    }\n  }\n  return result;\n};", "map": {"version": 3, "names": ["formatDistanceLocale", "lessThanXSeconds", "one", "other", "oneWithSuffix", "otherWithSuffix", "xSeconds", "halfAMinute", "lessThanXMinutes", "xMinutes", "aboutXHours", "xHours", "xDays", "aboutXWeeks", "xWeeks", "aboutXMonths", "xMonths", "aboutXYears", "xYears", "overXYears", "almostXYears", "formatDistance", "token", "count", "options", "result", "tokenValue", "addSuffix", "replace", "String", "comparison"], "sources": ["C:/Users/<USER>/Desktop/demo-git/demo-react/my-fullstack-app/frontend/node_modules/date-fns/locale/ja/_lib/formatDistance.js"], "sourcesContent": ["const formatDistanceLocale = {\n  lessThanXSeconds: {\n    one: \"1秒未満\",\n    other: \"{{count}}秒未満\",\n    oneWithSuffix: \"約1秒\",\n    otherWithSuffix: \"約{{count}}秒\",\n  },\n\n  xSeconds: {\n    one: \"1秒\",\n    other: \"{{count}}秒\",\n  },\n\n  halfAMinute: \"30秒\",\n\n  lessThanXMinutes: {\n    one: \"1分未満\",\n    other: \"{{count}}分未満\",\n    oneWithSuffix: \"約1分\",\n    otherWithSuffix: \"約{{count}}分\",\n  },\n\n  xMinutes: {\n    one: \"1分\",\n    other: \"{{count}}分\",\n  },\n\n  aboutXHours: {\n    one: \"約1時間\",\n    other: \"約{{count}}時間\",\n  },\n\n  xHours: {\n    one: \"1時間\",\n    other: \"{{count}}時間\",\n  },\n\n  xDays: {\n    one: \"1日\",\n    other: \"{{count}}日\",\n  },\n\n  aboutXWeeks: {\n    one: \"約1週間\",\n    other: \"約{{count}}週間\",\n  },\n\n  xWeeks: {\n    one: \"1週間\",\n    other: \"{{count}}週間\",\n  },\n\n  aboutXMonths: {\n    one: \"約1か月\",\n    other: \"約{{count}}か月\",\n  },\n\n  xMonths: {\n    one: \"1か月\",\n    other: \"{{count}}か月\",\n  },\n\n  aboutXYears: {\n    one: \"約1年\",\n    other: \"約{{count}}年\",\n  },\n\n  xYears: {\n    one: \"1年\",\n    other: \"{{count}}年\",\n  },\n\n  overXYears: {\n    one: \"1年以上\",\n    other: \"{{count}}年以上\",\n  },\n\n  almostXYears: {\n    one: \"1年近く\",\n    other: \"{{count}}年近く\",\n  },\n};\n\nexport const formatDistance = (token, count, options) => {\n  options = options || {};\n\n  let result;\n\n  const tokenValue = formatDistanceLocale[token];\n  if (typeof tokenValue === \"string\") {\n    result = tokenValue;\n  } else if (count === 1) {\n    if (options.addSuffix && tokenValue.oneWithSuffix) {\n      result = tokenValue.oneWithSuffix;\n    } else {\n      result = tokenValue.one;\n    }\n  } else {\n    if (options.addSuffix && tokenValue.otherWithSuffix) {\n      result = tokenValue.otherWithSuffix.replace(\"{{count}}\", String(count));\n    } else {\n      result = tokenValue.other.replace(\"{{count}}\", String(count));\n    }\n  }\n\n  if (options.addSuffix) {\n    if (options.comparison && options.comparison > 0) {\n      return result + \"後\";\n    } else {\n      return result + \"前\";\n    }\n  }\n\n  return result;\n};\n"], "mappings": "AAAA,MAAMA,oBAAoB,GAAG;EAC3BC,gBAAgB,EAAE;IAChBC,GAAG,EAAE,MAAM;IACXC,KAAK,EAAE,cAAc;IACrBC,aAAa,EAAE,KAAK;IACpBC,eAAe,EAAE;EACnB,CAAC;EAEDC,QAAQ,EAAE;IACRJ,GAAG,EAAE,IAAI;IACTC,KAAK,EAAE;EACT,CAAC;EAEDI,WAAW,EAAE,KAAK;EAElBC,gBAAgB,EAAE;IAChBN,GAAG,EAAE,MAAM;IACXC,KAAK,EAAE,cAAc;IACrBC,aAAa,EAAE,KAAK;IACpBC,eAAe,EAAE;EACnB,CAAC;EAEDI,QAAQ,EAAE;IACRP,GAAG,EAAE,IAAI;IACTC,KAAK,EAAE;EACT,CAAC;EAEDO,WAAW,EAAE;IACXR,GAAG,EAAE,MAAM;IACXC,KAAK,EAAE;EACT,CAAC;EAEDQ,MAAM,EAAE;IACNT,GAAG,EAAE,KAAK;IACVC,KAAK,EAAE;EACT,CAAC;EAEDS,KAAK,EAAE;IACLV,GAAG,EAAE,IAAI;IACTC,KAAK,EAAE;EACT,CAAC;EAEDU,WAAW,EAAE;IACXX,GAAG,EAAE,MAAM;IACXC,KAAK,EAAE;EACT,CAAC;EAEDW,MAAM,EAAE;IACNZ,GAAG,EAAE,KAAK;IACVC,KAAK,EAAE;EACT,CAAC;EAEDY,YAAY,EAAE;IACZb,GAAG,EAAE,MAAM;IACXC,KAAK,EAAE;EACT,CAAC;EAEDa,OAAO,EAAE;IACPd,GAAG,EAAE,KAAK;IACVC,KAAK,EAAE;EACT,CAAC;EAEDc,WAAW,EAAE;IACXf,GAAG,EAAE,KAAK;IACVC,KAAK,EAAE;EACT,CAAC;EAEDe,MAAM,EAAE;IACNhB,GAAG,EAAE,IAAI;IACTC,KAAK,EAAE;EACT,CAAC;EAEDgB,UAAU,EAAE;IACVjB,GAAG,EAAE,MAAM;IACXC,KAAK,EAAE;EACT,CAAC;EAEDiB,YAAY,EAAE;IACZlB,GAAG,EAAE,MAAM;IACXC,KAAK,EAAE;EACT;AACF,CAAC;AAED,OAAO,MAAMkB,cAAc,GAAGA,CAACC,KAAK,EAAEC,KAAK,EAAEC,OAAO,KAAK;EACvDA,OAAO,GAAGA,OAAO,IAAI,CAAC,CAAC;EAEvB,IAAIC,MAAM;EAEV,MAAMC,UAAU,GAAG1B,oBAAoB,CAACsB,KAAK,CAAC;EAC9C,IAAI,OAAOI,UAAU,KAAK,QAAQ,EAAE;IAClCD,MAAM,GAAGC,UAAU;EACrB,CAAC,MAAM,IAAIH,KAAK,KAAK,CAAC,EAAE;IACtB,IAAIC,OAAO,CAACG,SAAS,IAAID,UAAU,CAACtB,aAAa,EAAE;MACjDqB,MAAM,GAAGC,UAAU,CAACtB,aAAa;IACnC,CAAC,MAAM;MACLqB,MAAM,GAAGC,UAAU,CAACxB,GAAG;IACzB;EACF,CAAC,MAAM;IACL,IAAIsB,OAAO,CAACG,SAAS,IAAID,UAAU,CAACrB,eAAe,EAAE;MACnDoB,MAAM,GAAGC,UAAU,CAACrB,eAAe,CAACuB,OAAO,CAAC,WAAW,EAAEC,MAAM,CAACN,KAAK,CAAC,CAAC;IACzE,CAAC,MAAM;MACLE,MAAM,GAAGC,UAAU,CAACvB,KAAK,CAACyB,OAAO,CAAC,WAAW,EAAEC,MAAM,CAACN,KAAK,CAAC,CAAC;IAC/D;EACF;EAEA,IAAIC,OAAO,CAACG,SAAS,EAAE;IACrB,IAAIH,OAAO,CAACM,UAAU,IAAIN,OAAO,CAACM,UAAU,GAAG,CAAC,EAAE;MAChD,OAAOL,MAAM,GAAG,GAAG;IACrB,CAAC,MAAM;MACL,OAAOA,MAAM,GAAG,GAAG;IACrB;EACF;EAEA,OAAOA,MAAM;AACf,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "const formatDistanceLocale = {\n  lessThanXSeconds: {\n    one: \"ավելի քիչ քան 1 վայրկյան\",\n    other: \"ավելի քիչ քան {{count}} վայրկյան\"\n  },\n  xSeconds: {\n    one: \"1 վայրկյան\",\n    other: \"{{count}} վայրկյան\"\n  },\n  halfAMinute: \"կես րոպե\",\n  lessThanXMinutes: {\n    one: \"ավելի քիչ քան 1 րոպե\",\n    other: \"ավելի քիչ քան {{count}} րոպե\"\n  },\n  xMinutes: {\n    one: \"1 րոպե\",\n    other: \"{{count}} րոպե\"\n  },\n  aboutXHours: {\n    one: \"մոտ 1 ժամ\",\n    other: \"մոտ {{count}} ժամ\"\n  },\n  xHours: {\n    one: \"1 ժամ\",\n    other: \"{{count}} ժամ\"\n  },\n  xDays: {\n    one: \"1 օր\",\n    other: \"{{count}} օր\"\n  },\n  aboutXWeeks: {\n    one: \"մոտ 1 շաբաթ\",\n    other: \"մոտ {{count}} շաբաթ\"\n  },\n  xWeeks: {\n    one: \"1 շաբաթ\",\n    other: \"{{count}} շաբաթ\"\n  },\n  aboutXMonths: {\n    one: \"մոտ 1 ամիս\",\n    other: \"մոտ {{count}} ամիս\"\n  },\n  xMonths: {\n    one: \"1 ամիս\",\n    other: \"{{count}} ամիս\"\n  },\n  aboutXYears: {\n    one: \"մոտ 1 տարի\",\n    other: \"մոտ {{count}} տարի\"\n  },\n  xYears: {\n    one: \"1 տարի\",\n    other: \"{{count}} տարի\"\n  },\n  overXYears: {\n    one: \"ավելի քան 1 տարի\",\n    other: \"ավելի քան {{count}} տարի\"\n  },\n  almostXYears: {\n    one: \"համարյա 1 տարի\",\n    other: \"համարյա {{count}} տարի\"\n  }\n};\nexport const formatDistance = (token, count, options) => {\n  let result;\n  const tokenValue = formatDistanceLocale[token];\n  if (typeof tokenValue === \"string\") {\n    result = tokenValue;\n  } else if (count === 1) {\n    result = tokenValue.one;\n  } else {\n    result = tokenValue.other.replace(\"{{count}}\", String(count));\n  }\n  if (options !== null && options !== void 0 && options.addSuffix) {\n    if (options.comparison && options.comparison > 0) {\n      return result + \" հետո\";\n    } else {\n      return result + \" առաջ\";\n    }\n  }\n  return result;\n};", "map": {"version": 3, "names": ["formatDistanceLocale", "lessThanXSeconds", "one", "other", "xSeconds", "halfAMinute", "lessThanXMinutes", "xMinutes", "aboutXHours", "xHours", "xDays", "aboutXWeeks", "xWeeks", "aboutXMonths", "xMonths", "aboutXYears", "xYears", "overXYears", "almostXYears", "formatDistance", "token", "count", "options", "result", "tokenValue", "replace", "String", "addSuffix", "comparison"], "sources": ["C:/Users/<USER>/Desktop/demo-git/demo-react/my-fullstack-app/frontend/node_modules/date-fns/locale/hy/_lib/formatDistance.js"], "sourcesContent": ["const formatDistanceLocale = {\n  lessThanXSeconds: {\n    one: \"ավելի քիչ քան 1 վայրկյան\",\n    other: \"ավելի քիչ քան {{count}} վայրկյան\",\n  },\n\n  xSeconds: {\n    one: \"1 վայրկյան\",\n    other: \"{{count}} վայրկյան\",\n  },\n\n  halfAMinute: \"կես րոպե\",\n\n  lessThanXMinutes: {\n    one: \"ավելի քիչ քան 1 րոպե\",\n    other: \"ավելի քիչ քան {{count}} րոպե\",\n  },\n\n  xMinutes: {\n    one: \"1 րոպե\",\n    other: \"{{count}} րոպե\",\n  },\n\n  aboutXHours: {\n    one: \"մոտ 1 ժամ\",\n    other: \"մոտ {{count}} ժամ\",\n  },\n\n  xHours: {\n    one: \"1 ժամ\",\n    other: \"{{count}} ժամ\",\n  },\n\n  xDays: {\n    one: \"1 օր\",\n    other: \"{{count}} օր\",\n  },\n\n  aboutXWeeks: {\n    one: \"մոտ 1 շաբաթ\",\n    other: \"մոտ {{count}} շաբաթ\",\n  },\n\n  xWeeks: {\n    one: \"1 շաբաթ\",\n    other: \"{{count}} շաբաթ\",\n  },\n\n  aboutXMonths: {\n    one: \"մոտ 1 ամիս\",\n    other: \"մոտ {{count}} ամիս\",\n  },\n\n  xMonths: {\n    one: \"1 ամիս\",\n    other: \"{{count}} ամիս\",\n  },\n\n  aboutXYears: {\n    one: \"մոտ 1 տարի\",\n    other: \"մոտ {{count}} տարի\",\n  },\n\n  xYears: {\n    one: \"1 տարի\",\n    other: \"{{count}} տարի\",\n  },\n\n  overXYears: {\n    one: \"ավելի քան 1 տարի\",\n    other: \"ավելի քան {{count}} տարի\",\n  },\n\n  almostXYears: {\n    one: \"համարյա 1 տարի\",\n    other: \"համարյա {{count}} տարի\",\n  },\n};\n\nexport const formatDistance = (token, count, options) => {\n  let result;\n\n  const tokenValue = formatDistanceLocale[token];\n  if (typeof tokenValue === \"string\") {\n    result = tokenValue;\n  } else if (count === 1) {\n    result = tokenValue.one;\n  } else {\n    result = tokenValue.other.replace(\"{{count}}\", String(count));\n  }\n\n  if (options?.addSuffix) {\n    if (options.comparison && options.comparison > 0) {\n      return result + \" հետո\";\n    } else {\n      return result + \" առաջ\";\n    }\n  }\n\n  return result;\n};\n"], "mappings": "AAAA,MAAMA,oBAAoB,GAAG;EAC3BC,gBAAgB,EAAE;IAChBC,GAAG,EAAE,0BAA0B;IAC/BC,KAAK,EAAE;EACT,CAAC;EAEDC,QAAQ,EAAE;IACRF,GAAG,EAAE,YAAY;IACjBC,KAAK,EAAE;EACT,CAAC;EAEDE,WAAW,EAAE,UAAU;EAEvBC,gBAAgB,EAAE;IAChBJ,GAAG,EAAE,sBAAsB;IAC3BC,KAAK,EAAE;EACT,CAAC;EAEDI,QAAQ,EAAE;IACRL,GAAG,EAAE,QAAQ;IACbC,KAAK,EAAE;EACT,CAAC;EAEDK,WAAW,EAAE;IACXN,GAAG,EAAE,WAAW;IAChBC,KAAK,EAAE;EACT,CAAC;EAEDM,MAAM,EAAE;IACNP,GAAG,EAAE,OAAO;IACZC,KAAK,EAAE;EACT,CAAC;EAEDO,KAAK,EAAE;IACLR,GAAG,EAAE,MAAM;IACXC,KAAK,EAAE;EACT,CAAC;EAEDQ,WAAW,EAAE;IACXT,GAAG,EAAE,aAAa;IAClBC,KAAK,EAAE;EACT,CAAC;EAEDS,MAAM,EAAE;IACNV,GAAG,EAAE,SAAS;IACdC,KAAK,EAAE;EACT,CAAC;EAEDU,YAAY,EAAE;IACZX,GAAG,EAAE,YAAY;IACjBC,KAAK,EAAE;EACT,CAAC;EAEDW,OAAO,EAAE;IACPZ,GAAG,EAAE,QAAQ;IACbC,KAAK,EAAE;EACT,CAAC;EAEDY,WAAW,EAAE;IACXb,GAAG,EAAE,YAAY;IACjBC,KAAK,EAAE;EACT,CAAC;EAEDa,MAAM,EAAE;IACNd,GAAG,EAAE,QAAQ;IACbC,KAAK,EAAE;EACT,CAAC;EAEDc,UAAU,EAAE;IACVf,GAAG,EAAE,kBAAkB;IACvBC,KAAK,EAAE;EACT,CAAC;EAEDe,YAAY,EAAE;IACZhB,GAAG,EAAE,gBAAgB;IACrBC,KAAK,EAAE;EACT;AACF,CAAC;AAED,OAAO,MAAMgB,cAAc,GAAGA,CAACC,KAAK,EAAEC,KAAK,EAAEC,OAAO,KAAK;EACvD,IAAIC,MAAM;EAEV,MAAMC,UAAU,GAAGxB,oBAAoB,CAACoB,KAAK,CAAC;EAC9C,IAAI,OAAOI,UAAU,KAAK,QAAQ,EAAE;IAClCD,MAAM,GAAGC,UAAU;EACrB,CAAC,MAAM,IAAIH,KAAK,KAAK,CAAC,EAAE;IACtBE,MAAM,GAAGC,UAAU,CAACtB,GAAG;EACzB,CAAC,MAAM;IACLqB,MAAM,GAAGC,UAAU,CAACrB,KAAK,CAACsB,OAAO,CAAC,WAAW,EAAEC,MAAM,CAACL,KAAK,CAAC,CAAC;EAC/D;EAEA,IAAIC,OAAO,aAAPA,OAAO,eAAPA,OAAO,CAAEK,SAAS,EAAE;IACtB,IAAIL,OAAO,CAACM,UAAU,IAAIN,OAAO,CAACM,UAAU,GAAG,CAAC,EAAE;MAChD,OAAOL,MAAM,GAAG,OAAO;IACzB,CAAC,MAAM;MACL,OAAOA,MAAM,GAAG,OAAO;IACzB;EACF;EAEA,OAAOA,MAAM;AACf,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
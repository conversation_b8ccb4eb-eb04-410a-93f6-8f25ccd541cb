{"ast": null, "code": "import { formatDistance } from \"./ar/_lib/formatDistance.js\";\nimport { formatLong } from \"./ar/_lib/formatLong.js\";\nimport { formatRelative } from \"./ar/_lib/formatRelative.js\";\nimport { localize } from \"./ar/_lib/localize.js\";\nimport { match } from \"./ar/_lib/match.js\";\n\n/**\n * @category Locales\n * @summary Arabic locale (Modern Standard Arabic - Al-fussha).\n * @language Modern Standard Arabic\n * @iso-639-2 ara\n * <AUTHOR> [@AbdallahAHO](https://github.com/AbdallahAHO)\n * <AUTHOR> [@essana3](https://github.com/essana3)\n */\nexport const ar = {\n  code: \"ar\",\n  formatDistance: formatDistance,\n  formatLong: formatLong,\n  formatRelative: formatRelative,\n  localize: localize,\n  match: match,\n  options: {\n    weekStartsOn: 6 /* Saturday */,\n    firstWeekContainsDate: 1\n  }\n};\n\n// Fallback for modularized imports:\nexport default ar;", "map": {"version": 3, "names": ["formatDistance", "formatLong", "formatRelative", "localize", "match", "ar", "code", "options", "weekStartsOn", "firstWeekContainsDate"], "sources": ["C:/Users/<USER>/Desktop/demo-git/demo-react/my-fullstack-app/frontend/node_modules/date-fns/locale/ar.js"], "sourcesContent": ["import { formatDistance } from \"./ar/_lib/formatDistance.js\";\nimport { formatLong } from \"./ar/_lib/formatLong.js\";\nimport { formatRelative } from \"./ar/_lib/formatRelative.js\";\nimport { localize } from \"./ar/_lib/localize.js\";\nimport { match } from \"./ar/_lib/match.js\";\n\n/**\n * @category Locales\n * @summary Arabic locale (Modern Standard Arabic - Al-fussha).\n * @language Modern Standard Arabic\n * @iso-639-2 ara\n * <AUTHOR> [@AbdallahAHO](https://github.com/AbdallahAHO)\n * <AUTHOR> [@essana3](https://github.com/essana3)\n */\nexport const ar = {\n  code: \"ar\",\n  formatDistance: formatDistance,\n  formatLong: formatLong,\n  formatRelative: formatRelative,\n  localize: localize,\n  match: match,\n  options: {\n    weekStartsOn: 6 /* Saturday */,\n    firstWeekContainsDate: 1,\n  },\n};\n\n// Fallback for modularized imports:\nexport default ar;\n"], "mappings": "AAAA,SAASA,cAAc,QAAQ,6BAA6B;AAC5D,SAASC,UAAU,QAAQ,yBAAyB;AACpD,SAASC,cAAc,QAAQ,6BAA6B;AAC5D,SAASC,QAAQ,QAAQ,uBAAuB;AAChD,SAASC,KAAK,QAAQ,oBAAoB;;AAE1C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMC,EAAE,GAAG;EAChBC,IAAI,EAAE,IAAI;EACVN,cAAc,EAAEA,cAAc;EAC9BC,UAAU,EAAEA,UAAU;EACtBC,cAAc,EAAEA,cAAc;EAC9BC,QAAQ,EAAEA,QAAQ;EAClBC,KAAK,EAAEA,KAAK;EACZG,OAAO,EAAE;IACPC,YAAY,EAAE,CAAC,CAAC;IAChBC,qBAAqB,EAAE;EACzB;AACF,CAAC;;AAED;AACA,eAAeJ,EAAE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
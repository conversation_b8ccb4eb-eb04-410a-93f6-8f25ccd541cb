"use strict";(self.webpackChunkfrontend=self.webpackChunkfrontend||[]).push([[904],{904:(e,s,l)=>{l.r(s),l.d(s,{default:()=>j});var a=l(2018),r=l(9642),i=l(1063),t=l(5797),c=l(8060),n=l(2052),d=l(828),o=l(6104),m=l(748),u=l(8150),h=l(3740),p=l(5043),x=l(402),v=l(8018),f=l(2760),y=l(579);const j=()=>{const e=(0,p.useRef)(null),[s,l]=(0,p.useState)([]),[j,N]=(0,p.useState)([]),[b,g]=(0,p.useState)(!0),[w,R]=(0,p.useState)(""),[I,C]=(0,p.useState)(null),[k,A]=(0,p.useState)(!1),[S,E]=(0,p.useState)(null),[P,V]=(0,p.useState)([]),[W,$]=(0,p.useState)(!1),{hasPermission:F}=(0,f.S)(),U=async()=>{try{$(!0),v.Rm.api("\u8f09\u5165\u7528\u6236\u5217\u8868",{searchName:w,selectedRoleFilter:I});const e=await x.A.get("/api/users/GetUserRolesList",{params:{name:w}});let s=e.data;I&&(s=e.data.filter((e=>e.roles&&e.roles.some((e=>e.roleId===I))))),l(s),v.Rm.api("\u7528\u6236\u5217\u8868\u8f09\u5165\u6210\u529f",{total:e.data.length,filtered:s.length,roleFilter:I})}catch(a){var s;v.Rm.error("\u8f09\u5165\u7528\u6236\u5217\u8868\u5931\u6557",a),null===(s=e.current)||void 0===s||s.show({severity:"error",summary:"\u8f09\u5165\u5931\u6557",detail:"\u7121\u6cd5\u8f09\u5165\u7528\u6236\u5217\u8868",life:5e3})}finally{g(!1),$(!1)}};(0,p.useEffect)((()=>{U(),(async()=>{try{v.Rm.api("\u8f09\u5165\u89d2\u8272\u5217\u8868");const e=await x.A.get("/api/users/GetRoles");N(e.data),v.Rm.api("\u89d2\u8272\u5217\u8868\u8f09\u5165\u6210\u529f",{count:e.data.length})}catch(l){var s;v.Rm.error("\u8f09\u5165\u89d2\u8272\u5217\u8868\u5931\u6557",l),null===(s=e.current)||void 0===s||s.show({severity:"error",summary:"\u8f09\u5165\u5931\u6557",detail:"\u7121\u6cd5\u8f09\u5165\u89d2\u8272\u5217\u8868",life:5e3})}})()}),[]),(0,p.useEffect)((()=>{j.length>0&&U()}),[I]);const G=()=>{U()},M=j.map((e=>({label:e.Name,value:e.Id}))),Z=(0,y.jsx)(a.$,{type:"button",icon:"pi pi-refresh",text:!0,onClick:()=>U()}),D=(0,y.jsx)("div",{});return b?(0,y.jsx)("div",{className:"flex align-items-center justify-content-center min-h-screen",children:(0,y.jsxs)("div",{className:"text-center",children:[(0,y.jsx)(h.p,{}),(0,y.jsx)("p",{className:"mt-3",children:"\u8f09\u5165\u7528\u6236\u8cc7\u6599\u4e2d..."})]})}):(0,y.jsxs)("div",{className:"users-page",children:[(0,y.jsx)(d.y,{ref:e}),(0,y.jsx)(u.Z,{title:"\u7528\u6236\u6b0a\u9650\u7ba1\u7406",className:"mb-4",children:(0,y.jsx)("p",{className:"text-600 line-height-3 m-0",children:"\u7ba1\u7406\u7cfb\u7d71\u7684\u7528\u6236\u6b0a\u9650\uff0c\u5305\u62ec\u5206\u914d\u89d2\u8272\u548c\u67e5\u770b\u7528\u6236\u8a73\u7d30\u4fe1\u606f\u3002\u60a8\u53ef\u4ee5\u7de8\u8f2f\u7528\u6236\u7684\u89d2\u8272\u6b0a\u9650\u4ee5\u63a7\u5236\u5176\u5728\u7cfb\u7d71\u4e2d\u7684\u8a2a\u554f\u548c\u64cd\u4f5c\u3002"})}),(0,y.jsx)(u.Z,{className:"mb-4",children:(0,y.jsxs)("div",{className:"grid",children:[(0,y.jsx)("div",{className:"col-6 md:col-4",children:(0,y.jsxs)("div",{className:"p-inputgroup",children:[(0,y.jsx)(n.S,{placeholder:"\u641c\u5c0b\u7528\u6236\u540d\u7a31",value:w,onChange:e=>R(e.target.value),onKeyDown:e=>"Enter"===e.key&&G()}),(0,y.jsx)(a.$,{icon:"pi pi-search",onClick:G,disabled:W})]})}),(0,y.jsx)("div",{className:"col-6 md:col-4",children:(0,y.jsx)(c.m,{value:I,options:[{label:"\u5168\u90e8\u89d2\u8272",value:null},...j.map((e=>({label:e.Name,value:e.Id})))],onChange:e=>C(e.value),placeholder:"\u7be9\u9078\u89d2\u8272",className:"w-full",showClear:!0})}),(0,y.jsx)("div",{className:"col-6 md:col-4 ",children:(0,y.jsx)("div",{className:"flex gap-2",children:(0,y.jsx)(a.$,{label:"\u7de9\u5b58\u91cd\u7f6e",className:"p-button-danger",icon:"pi pi-trash",onClick:async()=>{try{var s;await x.A.get("/api/system/ResetMemCache"),null===(s=e.current)||void 0===s||s.show({severity:"success",summary:"\u91cd\u7f6e\u6210\u529f",detail:"\u7de9\u5b58\u5df2\u91cd\u7f6e",life:3e3})}catch(a){var l;v.Rm.error("\u91cd\u7f6e\u7de9\u5b58\u5931\u6557",a),null===(l=e.current)||void 0===l||l.show({severity:"error",summary:"\u91cd\u7f6e\u5931\u6557",detail:"\u7121\u6cd5\u91cd\u7f6e\u7de9\u5b58",life:5e3})}}})})})]})}),(0,y.jsx)(u.Z,{children:(0,y.jsxs)(i.b,{value:s,paginator:!0,rows:10,rowsPerPageOptions:[5,10,25,50],sortMode:"multiple",removableSort:!0,filterDisplay:"menu",globalFilterFields:["userName","userAccount","userEmail"],emptyMessage:"\u6c92\u6709\u627e\u5230\u7528\u6236\u8cc7\u6599",className:"p-datatable-gridlines",paginatorLeft:Z,paginatorRight:D,children:[(0,y.jsx)(r.V,{field:"userName",header:"\u7528\u6236\u540d\u7a31",sortable:!0,filter:!0,filterPlaceholder:"\u641c\u5c0b\u540d\u7a31",style:{minWidth:"150px"}}),(0,y.jsx)(r.V,{field:"userAccount",header:"\u5e33\u865f",sortable:!0,filter:!0,filterPlaceholder:"\u641c\u5c0b\u5e33\u865f",style:{minWidth:"120px"}}),(0,y.jsx)(r.V,{field:"userEmail",header:"Email",sortable:!0,filter:!0,filterPlaceholder:"\u641c\u5c0bEmail",style:{minWidth:"200px"}}),(0,y.jsx)(r.V,{header:"\u89d2\u8272\u6b0a\u9650",body:e=>e.roles&&Array.isArray(e.roles)&&0!==e.roles.length?(0,y.jsx)("div",{className:"flex flex-wrap gap-1",children:e.roles.map((e=>(0,y.jsx)(o.v,{value:e.roleName,severity:"info",className:"text-sm"},e.roleId)))}):(0,y.jsx)(o.v,{value:"\u7121\u89d2\u8272",severity:"warning",className:"text-sm"}),style:{minWidth:"200px"}}),(0,y.jsx)(r.V,{field:"isEnabled",header:"\u72c0\u614b",body:e=>(0,y.jsx)(o.v,{value:e.isEnabled?"\u555f\u7528":"\u505c\u7528",severity:e.isEnabled?"success":"danger"}),sortable:!0,style:{minWidth:"100px"}}),(0,y.jsx)(r.V,{header:"\u64cd\u4f5c",body:s=>(0,y.jsx)("div",{className:"flex gap-2",children:F("userroles.write")&&(0,y.jsx)(a.$,{icon:"pi pi-pencil",className:"p-button-success",size:"small",onClick:()=>(async s=>{try{v.Rm.api("\u8f09\u5165\u7528\u6236\u89d2\u8272",{userId:s.userId});const e=(await x.A.get("/api/users/GetUserRoles/".concat(s.userId))).data;E({userId:e.userId,userName:e.userName,userAccount:e.userAccount,roles:e.roles||[]});const l=e.roles||[];V(l.map((e=>e.roleId))),A(!0),v.Rm.api("\u7528\u6236\u89d2\u8272\u8f09\u5165\u6210\u529f",e)}catch(a){var l;v.Rm.error("\u8f09\u5165\u7528\u6236\u89d2\u8272\u5931\u6557",a),null===(l=e.current)||void 0===l||l.show({severity:"error",summary:"\u8f09\u5165\u5931\u6557",detail:"\u7121\u6cd5\u8f09\u5165\u7528\u6236\u89d2\u8272\u8cc7\u8a0a",life:5e3})}})(s),label:"\u7de8\u8f2f"})}),style:{minWidth:"100px"}})]})}),(0,y.jsx)(t.l,{header:"\u7de8\u8f2f\u7528\u6236\u89d2\u8272\u6b0a\u9650 - ".concat(null===S||void 0===S?void 0:S.userName),visible:k,style:{width:"500px"},onHide:()=>A(!1),footer:(0,y.jsxs)("div",{className:"flex justify-content-end gap-2",children:[(0,y.jsx)(a.$,{label:"\u53d6\u6d88",icon:"pi pi-times",onClick:()=>A(!1),className:"p-button-text"}),(0,y.jsx)(a.$,{label:"\u4fdd\u5b58",icon:"pi pi-check",onClick:async()=>{if(S)try{var s;v.Rm.api("\u66f4\u65b0\u7528\u6236\u89d2\u8272",{userId:S.userId,roleIds:P}),await x.A.put("/api/users/UpdateUserRoles",{UserId:S.userId,RoleIds:P}),null===(s=e.current)||void 0===s||s.show({severity:"success",summary:"\u66f4\u65b0\u6210\u529f",detail:"\u7528\u6236\u89d2\u8272\u6b0a\u9650\u5df2\u66f4\u65b0",life:3e3}),A(!1),U(),v.Rm.api("\u7528\u6236\u89d2\u8272\u66f4\u65b0\u6210\u529f")}catch(a){var l;v.Rm.error("\u66f4\u65b0\u7528\u6236\u89d2\u8272\u5931\u6557",a),null===(l=e.current)||void 0===l||l.show({severity:"error",summary:"\u66f4\u65b0\u5931\u6557",detail:"\u7121\u6cd5\u66f4\u65b0\u7528\u6236\u89d2\u8272\u6b0a\u9650",life:5e3})}},className:"p-button-primary"})]}),children:S&&(0,y.jsxs)("div",{className:"grid",children:[(0,y.jsx)("div",{className:"col-12",children:(0,y.jsxs)("div",{className:"field",children:[(0,y.jsx)("label",{className:"font-bold",children:"\u7528\u6236\u5e33\u865f:"}),(0,y.jsx)("p",{className:"m-0",children:S.userAccount})]})}),(0,y.jsx)("div",{className:"col-12",children:(0,y.jsxs)("div",{className:"field",children:[(0,y.jsx)("label",{htmlFor:"roles",className:"font-bold",children:"\u89d2\u8272\u6b0a\u9650:"}),(0,y.jsx)(m.K,{id:"roles",value:P,options:M,onChange:e=>V(e.value),placeholder:"\u9078\u64c7\u89d2\u8272\u6b0a\u9650",className:"w-full",display:"chip"})]})})]})})]})}}}]);
//# sourceMappingURL=904.cc99aeed.chunk.js.map
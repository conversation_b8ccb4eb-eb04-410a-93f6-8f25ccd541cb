﻿// <auto-generated/>
#nullable enable
#pragma warning disable CS0612, CS0618 // Suppress warnings about [Obsolete] member usage in generated code.

namespace System.Runtime.CompilerServices
{
    using System;
    using System.CodeDom.Compiler;

    [GeneratedCode("Microsoft.Extensions.Configuration.Binder.SourceGeneration", "8.0.9.3103")]
    [AttributeUsage(AttributeTargets.Method, AllowMultiple = true)]
    file sealed class InterceptsLocationAttribute : Attribute
    {
        public InterceptsLocationAttribute(string filePath, int line, int column)
        {
        }
    }
}

namespace Microsoft.Extensions.Configuration.Binder.SourceGeneration
{
    using Microsoft.Extensions.Configuration;
    using System;
    using System.CodeDom.Compiler;
    using System.Globalization;
    using System.Runtime.CompilerServices;

    [GeneratedCode("Microsoft.Extensions.Configuration.Binder.SourceGeneration", "8.0.9.3103")]
    file static class BindingExtensions
    {
        #region IConfiguration extensions.
        /// <summary>Extracts the value with the specified key and converts it to the specified type.</summary>
        [InterceptsLocation(@"C:\Users\<USER>\Desktop\demo-git\demo-react\my-fullstack-app\backend\Program.cs", 22, 39)]
        public static T? GetValue<T>(this IConfiguration configuration, string key, T defaultValue) => (T?)(BindingExtensions.GetValueCore(configuration, typeof(T), key) ?? defaultValue);
        #endregion IConfiguration extensions.

        #region Core binding extensions.
        public static object? GetValueCore(this IConfiguration configuration, Type type, string key)
        {
            if (configuration is null)
            {
                throw new ArgumentNullException(nameof(configuration));
            }

            IConfigurationSection section = configuration.GetSection(key);

            if (section.Value is not string value)
            {
                return null;
            }

            if (type == typeof(bool))
            {
                return ParseBool(value, () => section.Path);
            }

            return null;
        }

        public static bool ParseBool(string value, Func<string?> getPath)
        {
            try
            {
                return bool.Parse(value);
            }
            catch (Exception exception)
            {
                throw new InvalidOperationException($"Failed to convert configuration value at '{getPath()}' to type '{typeof(bool)}'.", exception);
            }
        }
        #endregion Core binding extensions.
    }
}

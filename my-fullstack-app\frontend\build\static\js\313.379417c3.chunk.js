"use strict";(self.webpackChunkfrontend=self.webpackChunkfrontend||[]).push([[313],{1182:(e,t,n)=>{n.d(t,{A:()=>o});var l=n(5043),r=n(402);function o(){const[e,t]=(0,l.useState)([]),[n,o]=(0,l.useState)(!0);return(0,l.useEffect)((()=>{r.A.get("/api/system/GetDataType").then((e=>t(e.data))).catch((e=>console.error("API Error:",e))).finally((()=>o(!1)))}),[]),{dataType:e,loading:n}}},3313:(e,t,n)=>{n.r(t),n.d(t,{default:()=>y});var l=n(9379),r=n(2018),o=n(5371),a=n(4972),i=n(8060),c=n(2052),s=n(7932),u=n(828),d=n(5043),f=n(5666),p=n(1182),m=n(402),b=n(579);const v=[{label:"\u7537",value:1},{label:"\u5973",value:2}],y=()=>{var e,t,n;const y=null===(e=(0,f.zy)().state)||void 0===e?void 0:e.patient,h=(0,d.useRef)(null),g=(0,f.Zp)(),{dataType:x,loading:j}=(0,p.A)(),[O,N]=(0,d.useState)([]),k=null!==(t=null===(n=x.find((e=>8===e.groupId)))||void 0===n?void 0:n.dataTypes)&&void 0!==t?t:[],[w,P]=(0,d.useState)({fullName:"",gender:"",phone:"",address:"",email:"",birthDate:null,emergencyContact:"",emergencyRelationship:"",emergencyPhone:"",nationalId:"",medicalHistory:"",exerciseHabit:"",exerciseFrequency:"",injuryHistory:""});(0,d.useEffect)((()=>{var e;y&&(P((0,l.A)((0,l.A)({},y),{},{birthDate:y.birthDate?new Date(y.birthDate):null})),N((null===(e=y.medicalHistory)||void 0===e?void 0:e.split(", "))||[]))}),[y]);const S=e=>{const{name:t,value:n}=e.target;P((e=>(0,l.A)((0,l.A)({},e),{},{[t]:n})))},D=e=>{let t=[...O];e.checked?t.push(e.value):t=t.filter((t=>t!==e.value)),N(t),P((e=>(0,l.A)((0,l.A)({},e),{},{medicalHistory:t.join(", ")})))};return j?(0,b.jsx)("p",{children:"Loading..."}):(0,b.jsxs)("div",{className:"p-4",children:[(0,b.jsx)(u.y,{ref:h}),(0,b.jsxs)("div",{className:"grid formgrid p-fluid gap-3",children:[(0,b.jsxs)("div",{className:"col-6 md:col-2",children:[(0,b.jsx)("label",{className:"font-bold block mb-2",children:"\u59d3\u540d"}),(0,b.jsx)(c.S,{name:"fullName",value:w.fullName,onChange:S})]}),(0,b.jsxs)("div",{className:"col-5 md:col-2",children:[(0,b.jsx)("label",{className:"font-bold block mb-2",children:"\u6027\u5225"}),(0,b.jsx)(i.m,{value:w.gender,options:v,onChange:e=>{return t="gender",n=e.value,void P((e=>(0,l.A)((0,l.A)({},e),{},{[t]:n})));var t,n},placeholder:"\u8acb\u9078\u64c7\u6027\u5225"})]}),(0,b.jsxs)("div",{className:"col-6 md:col-2",children:[(0,b.jsx)("label",{className:"font-bold block mb-2",children:"\u751f\u65e5"}),(0,b.jsx)(o.V,{value:w.birthDate,onChange:e=>P((0,l.A)((0,l.A)({},w),{},{birthDate:e.value})),dateFormat:"yy-mm-dd",showIcon:!0})]}),(0,b.jsxs)("div",{className:"col-5 md:col-3",children:[(0,b.jsx)("label",{className:"font-bold block mb-2",children:"\u96fb\u8a71"}),(0,b.jsx)(c.S,{name:"phone",value:w.phone,onChange:S})]}),(0,b.jsxs)("div",{className:"col-6 md:col-2",children:[(0,b.jsx)("label",{className:"font-bold block mb-2",children:"\u8eab\u5206\u8b49\u5b57\u865f"}),(0,b.jsx)(c.S,{name:"nationalId",value:w.nationalId,onChange:S})]}),(0,b.jsxs)("div",{className:"col-12 md:col-8 ",children:[(0,b.jsx)("label",{className:"font-bold block mb-2",children:"\u5730\u5740"}),(0,b.jsx)(c.S,{name:"address",value:w.address,onChange:S})]}),(0,b.jsxs)("div",{className:"col-12 md:col-3 ",children:[(0,b.jsx)("label",{className:"font-bold block mb-2",children:"\u96fb\u5b50\u4fe1\u7bb1"}),(0,b.jsx)(c.S,{name:"email",value:w.email,onChange:S})]}),(0,b.jsxs)("div",{className:"col-6 md:col-4",children:[(0,b.jsx)("label",{className:"font-bold block mb-2",children:"\u7dca\u6025\u9023\u7d61\u4eba"}),(0,b.jsx)(c.S,{name:"emergencyContact",value:w.emergencyContact,onChange:S})]}),(0,b.jsxs)("div",{className:"col-5 md:col-2",children:[(0,b.jsx)("label",{className:"font-bold block mb-2",children:"\u7dca\u6025\u9023\u7d61\u4eba\u95dc\u4fc2"}),(0,b.jsx)(c.S,{name:"emergencyRelationship",value:w.emergencyRelationship,onChange:S})]}),(0,b.jsxs)("div",{className:"col-6 md:col-4",children:[(0,b.jsx)("label",{className:"font-bold block mb-2",children:"\u7dca\u6025\u9023\u7d61\u4eba\u96fb\u8a71"}),(0,b.jsx)(c.S,{name:"emergencyPhone",value:w.emergencyPhone,onChange:S})]}),(0,b.jsxs)("div",{className:"col-12 md:col-6",children:[(0,b.jsx)("label",{className:"font-bold block mb-2",children:"\u7cfb\u7d71\u6027\u75be\u75c5\u53f2"}),(0,b.jsx)("div",{className:"flex flex-wrap gap-3",children:k.map((e=>(0,b.jsxs)("div",{className:"flex align-items-center",children:[(0,b.jsx)(a.S,{inputId:e.number,name:"medicalHistory",value:e.name,onChange:D,checked:O.includes(e.name)}),(0,b.jsx)("label",{htmlFor:e.number,className:"ml-2",children:e.name})]},e.itemId)))}),(0,b.jsx)(s.Z,{name:"medicalHistory",rows:3,value:w.medicalHistory,onChange:S})]}),(0,b.jsxs)("div",{className:"col-12 md:col-6",children:[(0,b.jsx)("label",{className:"font-bold block mb-2",children:"\u904b\u52d5\u9805\u76ee"}),(0,b.jsx)(s.Z,{name:"exerciseHabit",value:w.exerciseHabit,onChange:S})]}),(0,b.jsxs)("div",{className:"col-12 md:col-5",children:[(0,b.jsx)("label",{className:"font-bold block mb-2",children:"\u904b\u52d5\u983b\u7387"}),(0,b.jsx)(s.Z,{name:"exerciseFrequency",value:w.exerciseFrequency,onChange:S})]}),(0,b.jsxs)("div",{className:"col-12 md:col-11",children:[(0,b.jsx)("label",{className:"font-bold block mb-2",children:"\u91cd\u5927\u610f\u5916\u3001\u5916\u50b7\u53f2"}),(0,b.jsx)(s.Z,{name:"injuryHistory",rows:2,value:w.injuryHistory,onChange:S})]}),(0,b.jsx)("div",{className:"col-4 md:col-2 text-center mt-2",children:(0,b.jsx)(r.$,{label:"\u9001\u51fa",icon:"pi pi-save",onClick:async()=>{const e=(0,l.A)((0,l.A)({},w),{},{birthDate:w.birthDate?w.birthDate.toISOString():null});y?await m.A.put("/api/patients/Update/",e).then((()=>{var e;return null===(e=h.current)||void 0===e?void 0:e.show({severity:"success",summary:"\u6210\u529f",detail:"\u75c5\u60a3\u8cc7\u6599\u5df2\u66f4\u65b0"})})).catch((e=>{var t;return null===(t=h.current)||void 0===t?void 0:t.show({severity:"error",summary:"\u66f4\u65b0\u5931\u6557",detail:e.details})})):await m.A.post("/api/patients/Insert",e).then((()=>{var e;return null===(e=h.current)||void 0===e?void 0:e.show({severity:"success",summary:"\u6210\u529f",detail:"\u75c5\u60a3\u8cc7\u6599\u5df2\u65b0\u589e"})})).catch((e=>{var t;return null===(t=h.current)||void 0===t?void 0:t.show({severity:"error",summary:"\u65b0\u589e\u5931\u6557",detail:e.details})})),setTimeout((()=>g("/patients")),1500)}})})]})]})}},4972:(e,t,n)=>{n.d(t,{S:()=>x});var l=n(5043),r=n(4052),o=n(1828),a=n(2028),i=n(2897),c=n(1356),s=n(4504);function u(){return u=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var l in n)({}).hasOwnProperty.call(n,l)&&(e[l]=n[l])}return e},u.apply(null,arguments)}function d(e){return d="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},d(e)}function f(e){var t=function(e,t){if("object"!=d(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var l=n.call(e,t||"default");if("object"!=d(l))return l;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==d(t)?t:t+""}function p(e,t,n){return(t=f(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function m(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,l=Array(t);n<t;n++)l[n]=e[n];return l}function b(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var l,r,o,a,i=[],c=!0,s=!1;try{if(o=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;c=!1}else for(;!(c=(l=o.call(n)).done)&&(i.push(l.value),i.length!==t);c=!0);}catch(e){s=!0,r=e}finally{try{if(!c&&null!=n.return&&(a=n.return(),Object(a)!==a))return}finally{if(s)throw r}}return i}}(e,t)||function(e,t){if(e){if("string"==typeof e)return m(e,t);var n={}.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?m(e,t):void 0}}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}var v={box:"p-checkbox-box",input:"p-checkbox-input",icon:"p-checkbox-icon",root:function(e){var t=e.props,n=e.checked,l=e.context;return(0,s.xW)("p-checkbox p-component",{"p-highlight":n,"p-disabled":t.disabled,"p-invalid":t.invalid,"p-variant-filled":t.variant?"filled"===t.variant:l&&"filled"===l.inputStyle})}},y=o.x.extend({defaultProps:{__TYPE:"Checkbox",autoFocus:!1,checked:!1,className:null,disabled:!1,falseValue:!1,icon:null,id:null,inputId:null,inputRef:null,invalid:!1,variant:null,name:null,onChange:null,onContextMenu:null,onMouseDown:null,readOnly:!1,required:!1,style:null,tabIndex:null,tooltip:null,tooltipOptions:null,trueValue:!0,value:null,children:void 0},css:{classes:v}});function h(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var l=Object.getOwnPropertySymbols(e);t&&(l=l.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,l)}return n}function g(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?h(Object(n),!0).forEach((function(t){p(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):h(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var x=l.memo(l.forwardRef((function(e,t){var n=(0,a.qV)(),d=l.useContext(r.UM),f=y.getProps(e,d),p=b(l.useState(!1),2),m=p[0],v=p[1],h=y.setMetaData({props:f,state:{focused:m},context:{checked:f.checked===f.trueValue,disabled:f.disabled}}),x=h.ptm,j=h.cx,O=h.isUnstyled;(0,o.j)(y.css.styles,O,{name:"checkbox"});var N=l.useRef(null),k=l.useRef(f.inputRef),w=function(){return f.checked===f.trueValue};l.useImperativeHandle(t,(function(){return{props:f,focus:function(){return s.DV.focus(k.current)},getElement:function(){return N.current},getInput:function(){return k.current}}})),l.useEffect((function(){s.BF.combinedRefs(k,f.inputRef)}),[k,f.inputRef]),(0,a.w5)((function(){k.current.checked=w()}),[f.checked,f.trueValue]),(0,a.uU)((function(){f.autoFocus&&s.DV.focus(k.current,f.autoFocus)}));var P=w(),S=s.BF.isNotEmpty(f.tooltip),D=y.getOtherProps(f),C=n({id:f.id,className:(0,s.xW)(f.className,j("root",{checked:P,context:d})),style:f.style,"data-p-highlight":P,"data-p-disabled":f.disabled,onContextMenu:f.onContextMenu,onMouseDown:f.onMouseDown},D,x("root"));return l.createElement(l.Fragment,null,l.createElement("div",u({ref:N},C),function(){var e=s.BF.reduceKeys(D,s.DV.ARIA_PROPS),t=n(g({id:f.inputId,type:"checkbox",className:j("input"),name:f.name,tabIndex:f.tabIndex,onFocus:function(e){return function(e){var t;v(!0),null===f||void 0===f||null===(t=f.onFocus)||void 0===t||t.call(f,e)}(e)},onBlur:function(e){return function(e){var t;v(!1),null===f||void 0===f||null===(t=f.onBlur)||void 0===t||t.call(f,e)}(e)},onChange:function(e){return function(e){if(!f.disabled&&!f.readOnly&&f.onChange){var t,n=w()?f.falseValue:f.trueValue,l={originalEvent:e,value:f.value,checked:n,stopPropagation:function(){null===e||void 0===e||e.stopPropagation()},preventDefault:function(){null===e||void 0===e||e.preventDefault()},target:{type:"checkbox",name:f.name,id:f.id,value:f.value,checked:n}};if(null===f||void 0===f||null===(t=f.onChange)||void 0===t||t.call(f,l),e.defaultPrevented)return;s.DV.focus(k.current)}}(e)},disabled:f.disabled,readOnly:f.readOnly,required:f.required,"aria-invalid":f.invalid,checked:P},e),x("input"));return l.createElement("input",u({ref:k},t))}(),function(){var e=n({className:j("icon")},x("icon")),t=n({className:j("box",{checked:P}),"data-p-highlight":P,"data-p-disabled":f.disabled},x("box")),r=P?f.icon||l.createElement(i.S,e):null,o=s.Hj.getJSXIcon(r,g({},e),{props:f,checked:P});return l.createElement("div",t,o)}()),S&&l.createElement(c.m,u({target:N,content:f.tooltip,pt:x("tooltip")},f.tooltipOptions)))})));x.displayName="Checkbox"},7932:(e,t,n)=>{n.d(t,{Z:()=>h});var l=n(5043),r=n(4052),o=n(1828),a=n(2028),i=n(2224),c=n(1356),s=n(4504);function u(){return u=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var l in n)({}).hasOwnProperty.call(n,l)&&(e[l]=n[l])}return e},u.apply(null,arguments)}function d(e){return d="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},d(e)}function f(e){var t=function(e,t){if("object"!=d(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var l=n.call(e,t||"default");if("object"!=d(l))return l;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==d(t)?t:t+""}function p(e,t,n){return(t=f(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var m={root:function(e){var t=e.props,n=e.context,l=e.isFilled;return(0,s.xW)("p-inputtextarea p-inputtext p-component",{"p-disabled":t.disabled,"p-filled":l,"p-inputtextarea-resizable":t.autoResize,"p-invalid":t.invalid,"p-variant-filled":t.variant?"filled"===t.variant:n&&"filled"===n.inputStyle})}},b=o.x.extend({defaultProps:{__TYPE:"InputTextarea",__parentMetadata:null,autoResize:!1,invalid:!1,variant:null,keyfilter:null,onBlur:null,onFocus:null,onBeforeInput:null,onInput:null,onKeyDown:null,onKeyUp:null,onPaste:null,tooltip:null,tooltipOptions:null,validateOnly:!1,children:void 0,className:null},css:{classes:m,styles:"\n@layer primereact {\n    .p-inputtextarea-resizable {\n        overflow: hidden;\n        resize: none;\n    }\n    \n    .p-fluid .p-inputtextarea {\n        width: 100%;\n    }\n}\n"}});function v(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var l=Object.getOwnPropertySymbols(e);t&&(l=l.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,l)}return n}function y(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?v(Object(n),!0).forEach((function(t){p(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):v(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var h=l.memo(l.forwardRef((function(e,t){var n=(0,a.qV)(),d=l.useContext(r.UM),f=b.getProps(e,d),p=l.useRef(t),m=l.useRef(0),v=b.setMetaData(y(y({props:f},f.__parentMetadata),{},{context:{disabled:f.disabled}})),h=v.ptm,g=v.cx,x=v.isUnstyled;(0,o.j)(b.css.styles,x,{name:"inputtextarea"});var j=function(e){var t=p.current;t&&O()&&(m.current||(m.current=t.scrollHeight,t.style.overflow="hidden"),(m.current!==t.scrollHeight||e)&&(t.style.height="",t.style.height=t.scrollHeight+"px",parseFloat(t.style.height)>=parseFloat(t.style.maxHeight)?(t.style.overflowY="scroll",t.style.height=t.style.maxHeight):t.style.overflow="hidden",m.current=t.scrollHeight))},O=function(){if(s.DV.isVisible(p.current)){var e=p.current.getBoundingClientRect();return e.width>0&&e.height>0}return!1};l.useEffect((function(){s.BF.combinedRefs(p,t)}),[p,t]),l.useEffect((function(){f.autoResize&&j(!0)}),[f.autoResize,f.value]);var N=l.useMemo((function(){return s.BF.isNotEmpty(f.value)||s.BF.isNotEmpty(f.defaultValue)}),[f.value,f.defaultValue]),k=s.BF.isNotEmpty(f.tooltip),w=n({ref:p,className:(0,s.xW)(f.className,g("root",{context:d,isFilled:N})),onFocus:function(e){f.autoResize&&j(),f.onFocus&&f.onFocus(e)},onBlur:function(e){f.autoResize&&j(),f.onBlur&&f.onBlur(e)},onKeyUp:function(e){f.autoResize&&j(),f.onKeyUp&&f.onKeyUp(e)},onKeyDown:function(e){f.onKeyDown&&f.onKeyDown(e),f.keyfilter&&i.Q.onKeyPress(e,f.keyfilter,f.validateOnly)},onBeforeInput:function(e){f.onBeforeInput&&f.onBeforeInput(e),f.keyfilter&&i.Q.onBeforeInput(e,f.keyfilter,f.validateOnly)},onInput:function(e){var t=e.target;f.autoResize&&j(s.BF.isEmpty(t.value)),f.onInput&&f.onInput(e),s.BF.isNotEmpty(t.value)?s.DV.addClass(t,"p-filled"):s.DV.removeClass(t,"p-filled")},onPaste:function(e){f.onPaste&&f.onPaste(e),f.keyfilter&&i.Q.onPaste(e,f.keyfilter,f.validateOnly)}},b.getOtherProps(f),h("root"));return l.createElement(l.Fragment,null,l.createElement("textarea",w),k&&l.createElement(c.m,u({target:p,content:f.tooltip,pt:h("tooltip")},f.tooltipOptions)))})));h.displayName="InputTextarea"}}]);
//# sourceMappingURL=313.379417c3.chunk.js.map
# 本機檔案上傳遠端機
scp docker-compose.yml .env root@178.128.125.17:/app
scp -r opt root@178.128.125.17:/home/<USER>/

# 清除所有懸空映象 (dangling images)
docker image prune
# 建置
docker-compose up --build
# 套用
docker-compose up -d

backend 
Migration 
dotnet ef migrations add InitialCreate 		(專案第一次建立)
dotnet ef migrations add add<TableName> 		(新增表)
dotnet ef migrations add add<TableName><coloum> (新增欄位)
dotnet ef migrations add Rename_<Oldcoloum>_To_<Newcoloum>_In_<TableName> 
dotnet ef database update

frontend
npm start (.env.development)
npm run build (.env.production)


# 備份排程
# Crontab 排程
crontab -e
# 每天凌晨 2:00 執行增量備份
0 2 * * * /home/<USER>/mysql-backup/daily-incremental-backup.sh >> /opt/mysql-backup/backup.log 2>&1
# 每週日凌晨 3:00 執行完整備份
0 3 * * 0 /home/<USER>/mysql-backup/weekly-full-backup.sh >> /opt/mysql-backup/backup.log 2>&1
# 權限與執行權限
chmod +x /home/<USER>//mysql-backup/*.sh

# 還原
bash /home/<USER>/mysql-backup/restore-mysql.sh 2025-07-08



import { formatUtcToTaipei } from '../../utils/dateUtils';
import dayGridPlugin from '@fullcalendar/daygrid';
import interactionPlugin from '@fullcalendar/interaction';
import FullCalendar from '@fullcalendar/react';
import timeGridPlugin from '@fullcalendar/timegrid';
import { Button } from 'primereact/button';
import { Calendar } from 'primereact/calendar';
import { ConfirmDialog, confirmDialog } from 'primereact/confirmdialog';
import { Dialog } from 'primereact/dialog';
import { Dropdown } from 'primereact/dropdown';
import { InputText } from 'primereact/inputtext';
import { Toast } from 'primereact/toast';
import { Checkbox } from 'primereact/checkbox';
import { InputNumber } from 'primereact/inputnumber';
import React, { useEffect, useRef, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { ROUTES } from '../../constants/routes';
import api from '../../services/api';

interface CalendarEvent {
  id?: string;
  title: string;
  start: string;
  end: string;
  backgroundColor?: string;
  borderColor?: string;
  doctorId?: number;
  patientId?: number;
  treatmentId?: number;
  doctorName?: string;
  patientName?: string;
  description?: string;
}

interface EventFormData {
  doctorId: number | null;
  patientId: number | null;
  startDate: Date | null;
  endDate: Date | null;
  startTime: string;
  endTime: string;
  // 重複設定
  isRepeat: boolean;
  repeatType: 'daily' | 'weekly' | 'monthly';
  repeatCount: number;
}

interface Doctor {
  Id: number;
  Name: string;
}

interface Patient {
  Id: number;
  Name: string;
}

type DialogMode = 'add' | 'edit' | 'view';

const SchedulesPage: React.FC = () => {
  const toast = useRef<Toast>(null);
  const navigate = useNavigate();

  const [events, setEvents] = useState<CalendarEvent[]>([]);

  const [showDialog, setShowDialog] = useState(false);
  const [dialogMode, setDialogMode] = useState<DialogMode>('add');
  const [selectedEvent, setSelectedEvent] = useState<CalendarEvent | null>(null);
  const [doctors, setDoctors] = useState<Doctor[]>([]);
  const [patients, setPatients] = useState<Patient[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [formData, setFormData] = useState<EventFormData>({
    doctorId: null,
    patientId: null,
    startDate: null,
    endDate: null,
    startTime: '09:00',
    endTime: '10:00',
    isRepeat: false,
    repeatType: 'daily',
    repeatCount: 1,
  });

  // 載入所有數據
  useEffect(() => {
    const loadInitialData = async () => {
      try {
        console.log('開始載入數據...');

        // 並行載入治療師、病患和行程數據
        const [doctorsResponse, patientsResponse, schedulesResponse] = await Promise.all([
          api.get('/api/users/DoctorList'),
          api.get('/api/patients/PatientList'),
          api.get('/api/schedule')
        ]);

        console.log('治療師數據:', doctorsResponse.data);
        console.log('病患數據:', patientsResponse.data);
        console.log('行程數據:', schedulesResponse.data);

        const doctorsData = doctorsResponse.data;
        const patientsData = patientsResponse.data;
        const schedulesData = schedulesResponse.data;

        setDoctors(doctorsData);
        setPatients(patientsData);

        // 轉換後端數據格式為前端 CalendarEvent 格式
        const formattedEvents = schedulesData.map((schedule: any) => ({
          id: schedule.id,
          title: schedule.title,
          start: schedule.start,
          end: schedule.end,
          backgroundColor: schedule.backgroundColor || '#3788d8',
          borderColor: schedule.borderColor,
          doctorId: schedule.doctorId,
          patientId: schedule.patientId,
          doctorName: schedule.doctorName,
          patientName: schedule.patientName,
        }));

        console.log('格式化後的事件:', formattedEvents);
        setEvents(formattedEvents);

        toast.current?.show({
          severity: 'success',
          summary: '成功',
          detail: '數據載入完成',
        });
      } catch (error) {
        console.error('載入數據失敗:', error);
        toast.current?.show({
          severity: 'error',
          summary: '錯誤',
          detail: '載入數據失敗',
        });
      }
    };

    loadInitialData();
  }, []);

  // 重新載入行程數據
  const reloadSchedules = async () => {
    try {
      setIsLoading(true);
      const response = await api.get('/api/schedule');
      const schedulesData = response.data;

      const formattedEvents = schedulesData.map((schedule: any) => ({
        id: schedule.id,
        title: schedule.title,
        start: schedule.start,
        end: schedule.end,
        backgroundColor: schedule.backgroundColor || '#3788d8',
        borderColor: schedule.borderColor,
        doctorId: schedule.doctorId,
        patientId: schedule.patientId,
        treatmentId: schedule.treatmentId,
        doctorName: schedule.doctorName,
        patientName: schedule.patientName,
        description: schedule.description,
      }));

      setEvents(formattedEvents);

      toast.current?.show({
        severity: 'success',
        summary: '成功',
        detail: '行程數據已重新載入',
      });
    } catch (error) {
      console.error('重新載入行程失敗:', error);
      toast.current?.show({
        severity: 'error',
        summary: '錯誤',
        detail: '重新載入行程失敗',
      });
    } finally {
      setIsLoading(false);
    }
  };

  

  const handleInputChange = (field: keyof EventFormData, value: string | Date | null | number | boolean) => {
    setFormData(prev => ({
      ...prev,
      [field]: value,
    }));
  };

  const handleAddEvent = async () => {
    if (!formData.doctorId || !formData.patientId || !formData.startDate || !formData.endDate) {
      toast.current?.show({
        severity: 'warn',
        summary: '警告',
        detail: '請填寫所有必要欄位',
      });
      return;
    }

    try {
      const startDateTime = new Date(formData.startDate);
      const endDateTime = new Date(formData.endDate);

      // 設置時間
      const [startHour, startMinute] = formData.startTime.split(':');
      const [endHour, endMinute] = formData.endTime.split(':');

      startDateTime.setHours(parseInt(startHour as string), parseInt(startMinute as string));
      endDateTime.setHours(parseInt(endHour as string), parseInt(endMinute as string));

      // 轉換重複類型
      const getRepeatTypeValue = (type: string) => {
        switch (type) {
          case 'daily': return 1;
          case 'weekly': return 2;
          case 'monthly': return 3;
          default: return 0;
        }
      };

      const scheduleData = {
        doctorId: formData.doctorId,
        patientId: formData.patientId,
        startDateTime: startDateTime.toISOString(),
        endDateTime: endDateTime.toISOString(),
        description: '', // 可以後續添加描述欄位
        backgroundColor: '#3788d8',
        repeatType: formData.isRepeat ? getRepeatTypeValue(formData.repeatType) : 0,
        repeatCount: formData.isRepeat ? formData.repeatCount : 1,
      };

      if (dialogMode === 'edit' && selectedEvent && selectedEvent.id) {
        // 編輯模式 - 調用 PUT API
        const updateData = {
          ...scheduleData,
          id: parseInt(selectedEvent.id),
        };

        await api.put(`/api/schedule/${selectedEvent.id}`, updateData);

        // 重新載入所有行程數據以確保同步
        await reloadSchedules();

        toast.current?.show({
          severity: 'success',
          summary: '成功',
          detail: '行程已更新',
        });
      } else {
        // 新增模式 - 調用 POST API
        await api.post('/api/schedule', scheduleData);

        // 重新載入所有行程數據以確保同步
        await reloadSchedules();

        toast.current?.show({
          severity: 'success',
          summary: '成功',
          detail: '行程已新增',
        });
      }

      setShowDialog(false);
      resetForm();
    } catch (error: any) {
      console.error('操作失敗:', error);
      toast.current?.show({
        severity: 'error',
        summary: '錯誤',
        detail: error.response?.data?.message || '操作失敗',
      });
    }
  };

  const resetForm = () => {
    setFormData({
      doctorId: null,
      patientId: null,
      startDate: null,
      endDate: null,
      startTime: '09:00',
      endTime: '10:00',
      isRepeat: false,
      repeatType: 'daily',
      repeatCount: 1,
    });
    setSelectedEvent(null);
    setDialogMode('add');
  };

  const handleEventClick = (clickInfo: any) => {
    const eventData = events.find(event => event.id === clickInfo.event.id);
    if (eventData) {
      setSelectedEvent(eventData);
      setDialogMode('view');

      // 填充表單數據
      setFormData({
        doctorId: eventData.doctorId || null,
        patientId: eventData.patientId || null,
        startDate: new Date(eventData.start),
        endDate: new Date(eventData.end),
        startTime: formatUtcToTaipei(eventData.start, 'HH:mm'),
        endTime: formatUtcToTaipei(eventData.end, 'HH:mm'),
        isRepeat: false,
        repeatType: 'daily',
        repeatCount: 1,
      });

      setShowDialog(true);
    }
  };

  const handleEdit = () => {
    setDialogMode('edit');
  };

  const handleDelete = () => {
    if (!selectedEvent) return;

    confirmDialog({
      message: `確定要刪除行程 "${selectedEvent.title}" 嗎？`,
      header: '確認刪除',
      icon: 'pi pi-exclamation-triangle',
      accept: async () => {
        try {
          // 調用後端 DELETE API
          await api.delete(`/api/schedule/${selectedEvent.id}`);

          // 重新載入所有行程數據以確保同步
          await reloadSchedules();
          setShowDialog(false);
          resetForm();

          toast.current?.show({
            severity: 'success',
            summary: '成功',
            detail: '行程已刪除',
          });
        } catch (error: any) {
          console.error('刪除失敗:', error);
          var detail =  error.status === 403 ? "您無權限，請通知管理員" : error.response?.data?.message || '刪除失敗';
          
          toast.current?.show({
            severity: 'error',
            summary: '錯誤',
            detail: detail,
          });
        }
      },
    });
  };

  const handleOpenCase = async () => {
    if (!selectedEvent || !selectedEvent.patientId) return;

    try {
      let treatmentId = selectedEvent.treatmentId;

      if (!treatmentId) {
        // 如果沒有 TreatmentId，創建新的治療案件
        console.log('創建新的治療案件');
        const createResponse = await api.post('/api/treatment/Insert', {
          PatientId: selectedEvent.patientId,
          DoctorId: selectedEvent.doctorId,
          // 其他必要的初始數據
        });

        treatmentId = createResponse.data.treatmentId;
        console.log('新治療案件創建成功', { treatmentId });

        // 更新 Schedule 的 TreatmentId
        await api.patch(`/api/schedule/${selectedEvent.id}/treatment`, {
          TreatmentId: treatmentId
        });
        console.log('Schedule TreatmentId 更新成功');

        // 重新載入行程數據
        reloadSchedules();
      }

      // 獲取完整的治療數據
      console.log('獲取治療數據', { treatmentId });
      
      const treatmentResponse = await api.get('/api/treatment', {
        params: {
          treatmentsId: treatmentId
        }
      });

      const treatmentData = treatmentResponse.data;

      // 跳轉到 TreatmentsDetailPage
      if (treatmentId) {
        navigate(`${ROUTES.TREATMENT_DETAIL}?id=${treatmentId}`, {
          state: { treatment: treatmentData, patient: { id: treatmentData.patientId }}
        });
      }

    } catch (error) {
      console.error('開案失敗', error);
      toast.current?.show({
        severity: 'error',
        summary: '開案失敗',
        detail: '無法開啟治療案件，請稍後再試',
        life: 3000
      });
    }
  };

  const handleSaveEdit = () => {
    confirmDialog({
      message: '確定要保存修改嗎？',
      header: '確認編輯',
      icon: 'pi pi-question-circle',
      accept: () => {
        handleAddEvent(); // 重用新增邏輯，但會根據 dialogMode 判斷是編輯還是新增
      },
    });
  };

  return (
    <div className="schedules-page">
      <Toast ref={toast} />
      <ConfirmDialog />

      {/* 操作按鈕 */}
      <div className="card" hidden>
        <div className="flex pb-3 justify-content-end align-items-center">
          <div className="flex gap-2">
            <Button
              label="重新載入"
              icon={isLoading ? "pi pi-spin pi-spinner" : "pi pi-refresh"}
              onClick={reloadSchedules}
              className="p-button-secondary"
              outlined
              disabled={isLoading}
            />
            <Button
              label="新增行程"
              icon="pi pi-plus"
              onClick={() => setShowDialog(true)}
              className="p-button-primary"
            />
          </div>
        </div>
      </div>

      {/* 日曆 */}
      <div className="card">
        <div className="calendar-container">
          <FullCalendar
            plugins={[dayGridPlugin, timeGridPlugin, interactionPlugin]}
            height="auto"
            initialView="timeGridWeek"
            locale="zh-tw"
            weekends={true}
            allDaySlot={false}
            headerToolbar={{
              left: 'prev,next today',
              center: 'title',
              right: 'reloadDate,addDate dayGridMonth,timeGridWeek',
            }}
            titleFormat={{
                year: 'numeric', // 顯示年份 (例如: 2023)
                month: 'long'    // 顯示完整月份名稱 (例如: 十二月)
            }}
            dayHeaderFormat={{
              day: 'numeric', // 顯示日期數字 (例如: 13)
              weekday: 'narrow' // 顯示短格式的星期 (例如: 日, 一, 二)
            }}
            slotLabelFormat={{
              hour: '2-digit', // 小時顯示為兩位數 (例如: 09)
              minute: 'numeric', // 分鐘顯示為兩位數 (例如: 00)
              hour12: false // 禁用 12 小時制，啟用 24 小時制
            }}
            customButtons={{
              reloadDate: {
                text: '重整',
                click: reloadSchedules,
              },
              addDate: {
                text: '新增',
                click: () => setShowDialog(true),
              },
            }}
            slotMinTime="09:00:00"
            slotMaxTime="22:00:00"
            events={events}
            eventClick={handleEventClick}
            selectable={true}
            selectMirror={true}
            dayMaxEvents={true}
            businessHours={{
              daysOfWeek: [1, 2, 3, 4, 5, 6],
              startTime: '10:00',
              endTime: '18:00',
            }}
          />
        </div>
      </div>

      {/* 行程對話框 */}
      <Dialog
        header={
          dialogMode === 'add' ? '新增行程' :
          dialogMode === 'edit' ? '編輯行程' : '行程詳情'
        }
        visible={showDialog}
        style={{ width: '500px' }}
        onHide={() => {
          setShowDialog(false);
          resetForm();
        }}
        footer={
          <div className="flex gap-2">
            {dialogMode === 'view' ? (
              <>
                <Button
                  label="開案"
                  icon="pi pi-folder-open"
                  className="p-button-success"
                  onClick={handleOpenCase}
                />
                <Button
                  label="編輯"
                  icon="pi pi-pencil"
                  onClick={handleEdit}
                />
                <Button
                  label="刪除"
                  icon="pi pi-trash"
                  className="p-button-danger"
                  onClick={handleDelete}
                />
                <Button
                  label="關閉"
                  icon="pi pi-times"
                  className="p-button-outlined"
                  onClick={() => {
                    setShowDialog(false);
                    resetForm();
                  }}
                />
              </>
            ) : (
              <>
                <Button
                  label="取消"
                  icon="pi pi-times"
                  className="p-button-outlined"
                  onClick={() => {
                    setShowDialog(false);
                    resetForm();
                  }}
                />
                <Button
                  label={dialogMode === 'edit' ? '保存' : '新增'}
                  icon="pi pi-check"
                  onClick={dialogMode === 'edit' ? handleSaveEdit : handleAddEvent}
                />
              </>
            )}
          </div>
        }
      >
        <div className="grid">
          <div className="col-6 md:col-6">
            <div className="field">
              <label htmlFor="doctor" className="font-bold block mb-2">
                治療師 *
              </label>
              <Dropdown
                id="doctor"
                value={formData.doctorId}
                options={doctors}
                onChange={(e) => handleInputChange('doctorId', e.value)}
                optionLabel="Name"
                optionValue="Id"
                placeholder="請選擇治療師"
                className="w-full"
                disabled={dialogMode === 'view'}
              />
            </div>
          </div>

          <div className="col-6 md:col-6">
            <div className="field">
              <label htmlFor="patient" className="font-bold block mb-2">
                病患 *
              </label>
              <Dropdown
                id="patient"
                value={formData.patientId}
                options={patients}
                onChange={(e) => handleInputChange('patientId', e.value)}
                optionLabel="Name"
                optionValue="Id"
                placeholder="請選擇病患"
                className="w-full"
                disabled={dialogMode === 'view'}
              />
            </div>
          </div>

          <div className="col-12 md:col-6">
            <div className="field">
              <label htmlFor="startDate" className="font-bold block mb-2">
                開始日期 *
              </label>
              <Calendar
                id="startDate"
                value={formData.startDate}
                onChange={(e) => handleInputChange('startDate', e.value as Date)}
                dateFormat="yy-mm-dd"
                showIcon
                className="w-full"
                disabled={dialogMode === 'view'}
              />
            </div>
          </div>

          <div className="col-12 md:col-6">
            <div className="field">
              <label htmlFor="endDate" className="font-bold block mb-2">
                結束日期 *
              </label>
              <Calendar
                id="endDate"
                value={formData.endDate}
                onChange={(e) => handleInputChange('endDate', e.value as Date)}
                dateFormat="yy-mm-dd"
                showIcon
                className="w-full"
                disabled={dialogMode === 'view'}
              />
            </div>
          </div>

          <div className="col-6 md:col-6">
            <div className="field">
              <label htmlFor="startTime" className="font-bold block mb-2">
                開始時間
              </label>
              <InputText
                id="startTime"
                type="time"
                value={formData.startTime}
                onChange={(e) => handleInputChange('startTime', e.target.value)}
                className="w-full"
                disabled={dialogMode === 'view'}
              />
            </div>
          </div>

          <div className="col-6 md:col-6">
            <div className="field">
              <label htmlFor="endTime" className="font-bold block mb-2">
                結束時間
              </label>
              <InputText
                id="endTime"
                type="time"
                value={formData.endTime}
                onChange={(e) => handleInputChange('endTime', e.target.value)}
                className="w-full"
                disabled={dialogMode === 'view'}
              />
            </div>
          </div>
        </div>

        {/* 重複設定 */}
        {dialogMode !== 'view' && (
          <div className="grid">
            <div className="col-12">
              <div className="field-checkbox">
                <Checkbox
                  inputId="isRepeat"
                  checked={formData.isRepeat}
                  onChange={(e) => handleInputChange('isRepeat', e.checked || false)}
                />
                <label htmlFor="isRepeat" className="ml-2 font-bold">
                  重複行程
                </label>
              </div>
            </div>

            {formData.isRepeat && (
              <>
                <div className="col-6 md:col-6">
                  <div className="field">
                    <label htmlFor="repeatType" className="font-bold block mb-2">
                      重複類型
                    </label>
                    <Dropdown
                      id="repeatType"
                      value={formData.repeatType}
                      options={[
                        { label: '每日', value: 'daily' },
                        { label: '每週', value: 'weekly' },
                        { label: '每月', value: 'monthly' }
                      ]}
                      onChange={(e) => handleInputChange('repeatType', e.value)}
                      className="w-full"
                      style={{ width: '90%' }}
                    />
                  </div>
                </div>

                <div className="col-6 md:col-6">
                  <div className="field">
                    <label htmlFor="repeatCount" className="font-bold block mb-2">
                      重複次數
                    </label>
                    <InputNumber
                      id="repeatCount"
                      value={formData.repeatCount}
                      onValueChange={(e) => handleInputChange('repeatCount', e.value || 1)}
                      min={1}
                      max={10}
                      inputStyle={{ width: '90%' }}
                    />
                  </div>
                </div>

                <div className="col-12">
                  <div className="p-1 bg-blue-50 border-round">
                    <p className="text-sm text-blue-800 m-0">
                      <i className="pi pi-info-circle mr-2"></i>
                      {formData.repeatType === 'daily' && `將每日重複 ${formData.repeatCount} 次`}
                      {formData.repeatType === 'weekly' && `將每週重複 ${formData.repeatCount} 次`}
                      {formData.repeatType === 'monthly' && `將每月重複 ${formData.repeatCount} 次`}
                    </p>
                  </div>
                </div>
              </>
            )}
          </div>
        )}
      </Dialog>
    </div>
  );
};

export default SchedulesPage;


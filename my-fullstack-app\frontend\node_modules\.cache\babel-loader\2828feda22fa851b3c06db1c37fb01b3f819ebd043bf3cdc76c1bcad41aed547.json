{"ast": null, "code": "import React from'react';import{useNavigate,useLocation}from'react-router-dom';import{Button}from'primereact/button';import{Card}from'primereact/card';import{ROUTES}from'../../constants/routes';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const ErrorPage=_ref=>{var _location$state,_location$state2;let{error,resetError}=_ref;const navigate=useNavigate();const location=useLocation();// 從 location.state 獲取錯誤信息\nconst errorInfo=((_location$state=location.state)===null||_location$state===void 0?void 0:_location$state.error)||error;const errorMessage=((_location$state2=location.state)===null||_location$state2===void 0?void 0:_location$state2.message)||(errorInfo===null||errorInfo===void 0?void 0:errorInfo.message)||'發生未知錯誤';const errorStack=errorInfo===null||errorInfo===void 0?void 0:errorInfo.stack;const handleGoHome=()=>{navigate(ROUTES.HOME,{replace:true});};const handleGoBack=()=>{window.history.back();};const handleReload=()=>{if(resetError){resetError();}else{window.location.reload();}};// const handleReportError = () => {\n//   // 這裡可以實現錯誤報告功能\n//   const errorReport = {\n//     message: errorMessage,\n//     stack: errorStack,\n//     url: window.location.href,\n//     userAgent: navigator.userAgent,\n//     timestamp: new Date().toISOString()\n//   };\n//   console.error('錯誤報告:', errorReport);\n//   // 可以發送到錯誤追蹤服務\n//   // 例如: Sentry, LogRocket 等\n//   alert('錯誤報告已記錄，感謝您的回饋！');\n// };\nreturn/*#__PURE__*/_jsx(\"div\",{className:\"error-page min-h-screen flex align-items-center justify-content-center bg-gray-50\",children:/*#__PURE__*/_jsx(\"div\",{className:\"max-w-md w-full\",children:/*#__PURE__*/_jsxs(Card,{className:\"text-center\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"mb-4\",children:[/*#__PURE__*/_jsx(\"i\",{className:\"pi pi-exclamation-triangle text-6xl text-red-500 mb-3\"}),/*#__PURE__*/_jsx(\"h1\",{className:\"text-3xl font-bold text-gray-800 mb-2\",children:\"\\u7CDF\\u7CD5\\uFF01\"}),/*#__PURE__*/_jsx(\"h2\",{className:\"text-xl text-gray-600 mb-4\",children:\"\\u9801\\u9762\\u767C\\u751F\\u932F\\u8AA4\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"mb-4 p-3 bg-red-50 border-round\",children:[/*#__PURE__*/_jsx(\"p\",{className:\"text-red-700 font-medium mb-2\",children:\"\\u932F\\u8AA4\\u8A73\\u60C5\\uFF1A\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-red-600 text-sm\",children:errorMessage})]}),process.env.NODE_ENV==='development'&&errorStack&&/*#__PURE__*/_jsxs(\"div\",{className:\"mb-4 p-3 bg-gray-100 border-round\",children:[/*#__PURE__*/_jsx(\"p\",{className:\"text-gray-700 font-medium mb-2\",children:\"\\u6280\\u8853\\u8A73\\u60C5\\uFF1A\"}),/*#__PURE__*/_jsx(\"pre\",{className:\"text-xs text-gray-600 text-left overflow-auto max-h-20rem\",children:errorStack})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-column gap-2\",children:[/*#__PURE__*/_jsx(Button,{label:\"\\u91CD\\u65B0\\u8F09\\u5165\\u9801\\u9762\",icon:\"pi pi-refresh\",onClick:handleReload,className:\"p-button-primary\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex gap-2\",children:[/*#__PURE__*/_jsx(Button,{label:\"\\u8FD4\\u56DE\\u4E0A\\u9801\",icon:\"pi pi-arrow-left\",onClick:handleGoBack,className:\"p-button-secondary flex-1\",outlined:true}),/*#__PURE__*/_jsx(Button,{label:\"\\u56DE\\u5230\\u9996\\u9801\",icon:\"pi pi-home\",onClick:handleGoHome,className:\"p-button-secondary flex-1\",outlined:true})]})]}),/*#__PURE__*/_jsx(\"div\",{className:\"mt-4 pt-3 border-top-1 surface-border\",children:/*#__PURE__*/_jsx(\"p\",{className:\"text-sm text-gray-500\",children:\"\\u5982\\u679C\\u554F\\u984C\\u6301\\u7E8C\\u767C\\u751F\\uFF0C\\u8ACB\\u806F\\u7E6B\\u7CFB\\u7D71\\u7BA1\\u7406\\u54E1\"})})]})})});};export default ErrorPage;", "map": {"version": 3, "names": ["React", "useNavigate", "useLocation", "<PERSON><PERSON>", "Card", "ROUTES", "jsx", "_jsx", "jsxs", "_jsxs", "ErrorPage", "_ref", "_location$state", "_location$state2", "error", "resetError", "navigate", "location", "errorInfo", "state", "errorMessage", "message", "errorStack", "stack", "handleGoHome", "HOME", "replace", "handleGoBack", "window", "history", "back", "handleReload", "reload", "className", "children", "process", "env", "NODE_ENV", "label", "icon", "onClick", "outlined"], "sources": ["C:/Users/<USER>/Desktop/demo-git/demo-react/my-fullstack-app/frontend/src/components/Page/ErrorPage.tsx"], "sourcesContent": ["import React from 'react';\r\nimport { useNavigate, useLocation } from 'react-router-dom';\r\nimport { Button } from 'primereact/button';\r\nimport { Card } from 'primereact/card';\r\nimport { ROUTES } from '../../constants/routes';\r\n\r\ninterface ErrorPageProps {\r\n  error?: Error;\r\n  resetError?: () => void;\r\n}\r\n\r\nconst ErrorPage: React.FC<ErrorPageProps> = ({ error, resetError }) => {\r\n  const navigate = useNavigate();\r\n  const location = useLocation();\r\n  \r\n  // 從 location.state 獲取錯誤信息\r\n  const errorInfo = location.state?.error || error;\r\n  const errorMessage = location.state?.message || errorInfo?.message || '發生未知錯誤';\r\n  const errorStack = errorInfo?.stack;\r\n\r\n  const handleGoHome = () => {\r\n    navigate(ROUTES.HOME, { replace: true });\r\n  };\r\n\r\n  const handleGoBack = () => {\r\n    window.history.back();\r\n  };\r\n\r\n  const handleReload = () => {\r\n    if (resetError) {\r\n      resetError();\r\n    } else {\r\n      window.location.reload();\r\n    }\r\n  };\r\n\r\n  // const handleReportError = () => {\r\n  //   // 這裡可以實現錯誤報告功能\r\n  //   const errorReport = {\r\n  //     message: errorMessage,\r\n  //     stack: errorStack,\r\n  //     url: window.location.href,\r\n  //     userAgent: navigator.userAgent,\r\n  //     timestamp: new Date().toISOString()\r\n  //   };\r\n    \r\n  //   console.error('錯誤報告:', errorReport);\r\n    \r\n  //   // 可以發送到錯誤追蹤服務\r\n  //   // 例如: Sentry, LogRocket 等\r\n  //   alert('錯誤報告已記錄，感謝您的回饋！');\r\n  // };\r\n\r\n  return (\r\n    <div className=\"error-page min-h-screen flex align-items-center justify-content-center bg-gray-50\">\r\n      <div className=\"max-w-md w-full\">\r\n        <Card className=\"text-center\">\r\n          <div className=\"mb-4\">\r\n            <i className=\"pi pi-exclamation-triangle text-6xl text-red-500 mb-3\"></i>\r\n            <h1 className=\"text-3xl font-bold text-gray-800 mb-2\">糟糕！</h1>\r\n            <h2 className=\"text-xl text-gray-600 mb-4\">頁面發生錯誤</h2>\r\n          </div>\r\n\r\n          <div className=\"mb-4 p-3 bg-red-50 border-round\">\r\n            <p className=\"text-red-700 font-medium mb-2\">錯誤詳情：</p>\r\n            <p className=\"text-red-600 text-sm\">{errorMessage}</p>\r\n          </div>\r\n\r\n          {process.env.NODE_ENV === 'development' && errorStack && (\r\n            <div className=\"mb-4 p-3 bg-gray-100 border-round\">\r\n              <p className=\"text-gray-700 font-medium mb-2\">技術詳情：</p>\r\n              <pre className=\"text-xs text-gray-600 text-left overflow-auto max-h-20rem\">\r\n                {errorStack}\r\n              </pre>\r\n            </div>\r\n          )}\r\n\r\n          <div className=\"flex flex-column gap-2\">\r\n            <Button\r\n              label=\"重新載入頁面\"\r\n              icon=\"pi pi-refresh\"\r\n              onClick={handleReload}\r\n              className=\"p-button-primary\"\r\n            />\r\n            \r\n            <div className=\"flex gap-2\">\r\n              <Button\r\n                label=\"返回上頁\"\r\n                icon=\"pi pi-arrow-left\"\r\n                onClick={handleGoBack}\r\n                className=\"p-button-secondary flex-1\"\r\n                outlined\r\n              />\r\n              \r\n              <Button\r\n                label=\"回到首頁\"\r\n                icon=\"pi pi-home\"\r\n                onClick={handleGoHome}\r\n                className=\"p-button-secondary flex-1\"\r\n                outlined\r\n              />\r\n            </div>\r\n\r\n            {/* <Button\r\n              label=\"回報問題\"\r\n              icon=\"pi pi-send\"\r\n              onClick={handleReportError}\r\n              className=\"p-button-help\"\r\n              outlined\r\n              size=\"small\"\r\n            /> */}\r\n          </div>\r\n\r\n          <div className=\"mt-4 pt-3 border-top-1 surface-border\">\r\n            <p className=\"text-sm text-gray-500\">\r\n              如果問題持續發生，請聯繫系統管理員\r\n            </p>\r\n          </div>\r\n        </Card>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default ErrorPage;\r\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CACzB,OAASC,WAAW,CAAEC,WAAW,KAAQ,kBAAkB,CAC3D,OAASC,MAAM,KAAQ,mBAAmB,CAC1C,OAASC,IAAI,KAAQ,iBAAiB,CACtC,OAASC,MAAM,KAAQ,wBAAwB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAOhD,KAAM,CAAAC,SAAmC,CAAGC,IAAA,EAA2B,KAAAC,eAAA,CAAAC,gBAAA,IAA1B,CAAEC,KAAK,CAAEC,UAAW,CAAC,CAAAJ,IAAA,CAChE,KAAM,CAAAK,QAAQ,CAAGf,WAAW,CAAC,CAAC,CAC9B,KAAM,CAAAgB,QAAQ,CAAGf,WAAW,CAAC,CAAC,CAE9B;AACA,KAAM,CAAAgB,SAAS,CAAG,EAAAN,eAAA,CAAAK,QAAQ,CAACE,KAAK,UAAAP,eAAA,iBAAdA,eAAA,CAAgBE,KAAK,GAAIA,KAAK,CAChD,KAAM,CAAAM,YAAY,CAAG,EAAAP,gBAAA,CAAAI,QAAQ,CAACE,KAAK,UAAAN,gBAAA,iBAAdA,gBAAA,CAAgBQ,OAAO,IAAIH,SAAS,SAATA,SAAS,iBAATA,SAAS,CAAEG,OAAO,GAAI,QAAQ,CAC9E,KAAM,CAAAC,UAAU,CAAGJ,SAAS,SAATA,SAAS,iBAATA,SAAS,CAAEK,KAAK,CAEnC,KAAM,CAAAC,YAAY,CAAGA,CAAA,GAAM,CACzBR,QAAQ,CAACX,MAAM,CAACoB,IAAI,CAAE,CAAEC,OAAO,CAAE,IAAK,CAAC,CAAC,CAC1C,CAAC,CAED,KAAM,CAAAC,YAAY,CAAGA,CAAA,GAAM,CACzBC,MAAM,CAACC,OAAO,CAACC,IAAI,CAAC,CAAC,CACvB,CAAC,CAED,KAAM,CAAAC,YAAY,CAAGA,CAAA,GAAM,CACzB,GAAIhB,UAAU,CAAE,CACdA,UAAU,CAAC,CAAC,CACd,CAAC,IAAM,CACLa,MAAM,CAACX,QAAQ,CAACe,MAAM,CAAC,CAAC,CAC1B,CACF,CAAC,CAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AAEA;AACA;AACA;AACA;AAEA,mBACEzB,IAAA,QAAK0B,SAAS,CAAC,mFAAmF,CAAAC,QAAA,cAChG3B,IAAA,QAAK0B,SAAS,CAAC,iBAAiB,CAAAC,QAAA,cAC9BzB,KAAA,CAACL,IAAI,EAAC6B,SAAS,CAAC,aAAa,CAAAC,QAAA,eAC3BzB,KAAA,QAAKwB,SAAS,CAAC,MAAM,CAAAC,QAAA,eACnB3B,IAAA,MAAG0B,SAAS,CAAC,uDAAuD,CAAI,CAAC,cACzE1B,IAAA,OAAI0B,SAAS,CAAC,uCAAuC,CAAAC,QAAA,CAAC,oBAAG,CAAI,CAAC,cAC9D3B,IAAA,OAAI0B,SAAS,CAAC,4BAA4B,CAAAC,QAAA,CAAC,sCAAM,CAAI,CAAC,EACnD,CAAC,cAENzB,KAAA,QAAKwB,SAAS,CAAC,iCAAiC,CAAAC,QAAA,eAC9C3B,IAAA,MAAG0B,SAAS,CAAC,+BAA+B,CAAAC,QAAA,CAAC,gCAAK,CAAG,CAAC,cACtD3B,IAAA,MAAG0B,SAAS,CAAC,sBAAsB,CAAAC,QAAA,CAAEd,YAAY,CAAI,CAAC,EACnD,CAAC,CAELe,OAAO,CAACC,GAAG,CAACC,QAAQ,GAAK,aAAa,EAAIf,UAAU,eACnDb,KAAA,QAAKwB,SAAS,CAAC,mCAAmC,CAAAC,QAAA,eAChD3B,IAAA,MAAG0B,SAAS,CAAC,gCAAgC,CAAAC,QAAA,CAAC,gCAAK,CAAG,CAAC,cACvD3B,IAAA,QAAK0B,SAAS,CAAC,2DAA2D,CAAAC,QAAA,CACvEZ,UAAU,CACR,CAAC,EACH,CACN,cAEDb,KAAA,QAAKwB,SAAS,CAAC,wBAAwB,CAAAC,QAAA,eACrC3B,IAAA,CAACJ,MAAM,EACLmC,KAAK,CAAC,sCAAQ,CACdC,IAAI,CAAC,eAAe,CACpBC,OAAO,CAAET,YAAa,CACtBE,SAAS,CAAC,kBAAkB,CAC7B,CAAC,cAEFxB,KAAA,QAAKwB,SAAS,CAAC,YAAY,CAAAC,QAAA,eACzB3B,IAAA,CAACJ,MAAM,EACLmC,KAAK,CAAC,0BAAM,CACZC,IAAI,CAAC,kBAAkB,CACvBC,OAAO,CAAEb,YAAa,CACtBM,SAAS,CAAC,2BAA2B,CACrCQ,QAAQ,MACT,CAAC,cAEFlC,IAAA,CAACJ,MAAM,EACLmC,KAAK,CAAC,0BAAM,CACZC,IAAI,CAAC,YAAY,CACjBC,OAAO,CAAEhB,YAAa,CACtBS,SAAS,CAAC,2BAA2B,CACrCQ,QAAQ,MACT,CAAC,EACC,CAAC,EAUH,CAAC,cAENlC,IAAA,QAAK0B,SAAS,CAAC,uCAAuC,CAAAC,QAAA,cACpD3B,IAAA,MAAG0B,SAAS,CAAC,uBAAuB,CAAAC,QAAA,CAAC,wGAErC,CAAG,CAAC,CACD,CAAC,EACF,CAAC,CACJ,CAAC,CACH,CAAC,CAEV,CAAC,CAED,cAAe,CAAAxB,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "import { buildLocalizeFn } from \"../../_lib/buildLocalizeFn.js\";\nconst eraValues = {\n  narrow: [\"Ք\", \"Մ\"],\n  abbreviated: [\"ՔԱ\", \"ՄԹ\"],\n  wide: [\"Քրիստոսից առաջ\", \"Մեր թվարկության\"]\n};\nconst quarterValues = {\n  narrow: [\"1\", \"2\", \"3\", \"4\"],\n  abbreviated: [\"Ք1\", \"Ք2\", \"Ք3\", \"Ք4\"],\n  wide: [\"1֊ին քառորդ\", \"2֊րդ քառորդ\", \"3֊րդ քառորդ\", \"4֊րդ քառորդ\"]\n};\nconst monthValues = {\n  narrow: [\"Հ\", \"Փ\", \"Մ\", \"Ա\", \"Մ\", \"Հ\", \"Հ\", \"Օ\", \"Ս\", \"Հ\", \"Ն\", \"Դ\"],\n  abbreviated: [\"հուն\", \"փետ\", \"մար\", \"ապր\", \"մայ\", \"հուն\", \"հուլ\", \"օգս\", \"սեպ\", \"հոկ\", \"նոյ\", \"դեկ\"],\n  wide: [\"հունվար\", \"փետրվար\", \"մարտ\", \"ապրիլ\", \"մայիս\", \"հունիս\", \"հուլիս\", \"օգոստոս\", \"սեպտեմբեր\", \"հոկտեմբեր\", \"նոյեմբեր\", \"դեկտեմբեր\"]\n};\nconst dayValues = {\n  narrow: [\"Կ\", \"Ե\", \"Ե\", \"Չ\", \"Հ\", \"Ո\", \"Շ\"],\n  short: [\"կր\", \"եր\", \"եք\", \"չք\", \"հգ\", \"ուր\", \"շբ\"],\n  abbreviated: [\"կիր\", \"երկ\", \"երք\", \"չոր\", \"հնգ\", \"ուրբ\", \"շաբ\"],\n  wide: [\"կիրակի\", \"երկուշաբթի\", \"երեքշաբթի\", \"չորեքշաբթի\", \"հինգշաբթի\", \"ուրբաթ\", \"շաբաթ\"]\n};\nconst dayPeriodValues = {\n  narrow: {\n    am: \"a\",\n    pm: \"p\",\n    midnight: \"կեսգշ\",\n    noon: \"կեսօր\",\n    morning: \"առավոտ\",\n    afternoon: \"ցերեկ\",\n    evening: \"երեկո\",\n    night: \"գիշեր\"\n  },\n  abbreviated: {\n    am: \"AM\",\n    pm: \"PM\",\n    midnight: \"կեսգիշեր\",\n    noon: \"կեսօր\",\n    morning: \"առավոտ\",\n    afternoon: \"ցերեկ\",\n    evening: \"երեկո\",\n    night: \"գիշեր\"\n  },\n  wide: {\n    am: \"a.m.\",\n    pm: \"p.m.\",\n    midnight: \"կեսգիշեր\",\n    noon: \"կեսօր\",\n    morning: \"առավոտ\",\n    afternoon: \"ցերեկ\",\n    evening: \"երեկո\",\n    night: \"գիշեր\"\n  }\n};\nconst formattingDayPeriodValues = {\n  narrow: {\n    am: \"a\",\n    pm: \"p\",\n    midnight: \"կեսգշ\",\n    noon: \"կեսօր\",\n    morning: \"առավոտը\",\n    afternoon: \"ցերեկը\",\n    evening: \"երեկոյան\",\n    night: \"գիշերը\"\n  },\n  abbreviated: {\n    am: \"AM\",\n    pm: \"PM\",\n    midnight: \"կեսգիշերին\",\n    noon: \"կեսօրին\",\n    morning: \"առավոտը\",\n    afternoon: \"ցերեկը\",\n    evening: \"երեկոյան\",\n    night: \"գիշերը\"\n  },\n  wide: {\n    am: \"a.m.\",\n    pm: \"p.m.\",\n    midnight: \"կեսգիշերին\",\n    noon: \"կեսօրին\",\n    morning: \"առավոտը\",\n    afternoon: \"ցերեկը\",\n    evening: \"երեկոյան\",\n    night: \"գիշերը\"\n  }\n};\nconst ordinalNumber = (dirtyNumber, _options) => {\n  const number = Number(dirtyNumber);\n\n  // If ordinal numbers depend on context, for example,\n  // if they are different for different grammatical genders,\n  // use `options.unit`.\n  //\n  // `unit` can be 'year', 'quarter', 'month', 'week', 'date', 'dayOfYear',\n  // 'day', 'hour', 'minute', 'second'.\n\n  const rem100 = number % 100;\n  if (rem100 < 10) {\n    if (rem100 % 10 === 1) {\n      return number + \"֊ին\";\n    }\n  }\n  return number + \"֊րդ\";\n};\nexport const localize = {\n  ordinalNumber,\n  era: buildLocalizeFn({\n    values: eraValues,\n    defaultWidth: \"wide\"\n  }),\n  quarter: buildLocalizeFn({\n    values: quarterValues,\n    defaultWidth: \"wide\",\n    argumentCallback: quarter => quarter - 1\n  }),\n  month: buildLocalizeFn({\n    values: monthValues,\n    defaultWidth: \"wide\"\n  }),\n  day: buildLocalizeFn({\n    values: dayValues,\n    defaultWidth: \"wide\"\n  }),\n  dayPeriod: buildLocalizeFn({\n    values: dayPeriodValues,\n    defaultWidth: \"wide\",\n    formattingValues: formattingDayPeriodValues,\n    defaultFormattingWidth: \"wide\"\n  })\n};", "map": {"version": 3, "names": ["buildLocalizeFn", "<PERSON><PERSON><PERSON><PERSON>", "narrow", "abbreviated", "wide", "quarterValues", "month<PERSON><PERSON><PERSON>", "dayV<PERSON><PERSON>", "short", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "am", "pm", "midnight", "noon", "morning", "afternoon", "evening", "night", "formattingDayPeriodValues", "ordinalNumber", "dirtyNumber", "_options", "number", "Number", "rem100", "localize", "era", "values", "defaultWidth", "quarter", "argument<PERSON>allback", "month", "day", "<PERSON><PERSON><PERSON><PERSON>", "formattingValues", "defaultFormattingWidth"], "sources": ["C:/Users/<USER>/Desktop/demo-git/demo-react/my-fullstack-app/frontend/node_modules/date-fns/locale/hy/_lib/localize.js"], "sourcesContent": ["import { buildLocalizeFn } from \"../../_lib/buildLocalizeFn.js\";\n\nconst eraValues = {\n  narrow: [\"Ք\", \"Մ\"],\n  abbreviated: [\"ՔԱ\", \"ՄԹ\"],\n  wide: [\"Քրիստոսից առաջ\", \"Մեր թվարկության\"],\n};\n\nconst quarterValues = {\n  narrow: [\"1\", \"2\", \"3\", \"4\"],\n  abbreviated: [\"Ք1\", \"Ք2\", \"Ք3\", \"Ք4\"],\n  wide: [\"1֊ին քառորդ\", \"2֊րդ քառորդ\", \"3֊րդ քառորդ\", \"4֊րդ քառորդ\"],\n};\n\nconst monthValues = {\n  narrow: [\"Հ\", \"Փ\", \"Մ\", \"Ա\", \"Մ\", \"Հ\", \"Հ\", \"Օ\", \"Ս\", \"Հ\", \"Ն\", \"Դ\"],\n  abbreviated: [\n    \"հուն\",\n    \"փետ\",\n    \"մար\",\n    \"ապր\",\n    \"մայ\",\n    \"հուն\",\n    \"հուլ\",\n    \"օգս\",\n    \"սեպ\",\n    \"հոկ\",\n    \"նոյ\",\n    \"դեկ\",\n  ],\n\n  wide: [\n    \"հունվար\",\n    \"փետրվար\",\n    \"մարտ\",\n    \"ապրիլ\",\n    \"մայիս\",\n    \"հունիս\",\n    \"հուլիս\",\n    \"օգոստոս\",\n    \"սեպտեմբեր\",\n    \"հոկտեմբեր\",\n    \"նոյեմբեր\",\n    \"դեկտեմբեր\",\n  ],\n};\n\nconst dayValues = {\n  narrow: [\"Կ\", \"Ե\", \"Ե\", \"Չ\", \"Հ\", \"Ո\", \"Շ\"],\n  short: [\"կր\", \"եր\", \"եք\", \"չք\", \"հգ\", \"ուր\", \"շբ\"],\n  abbreviated: [\"կիր\", \"երկ\", \"երք\", \"չոր\", \"հնգ\", \"ուրբ\", \"շաբ\"],\n  wide: [\n    \"կիրակի\",\n    \"երկուշաբթի\",\n    \"երեքշաբթի\",\n    \"չորեքշաբթի\",\n    \"հինգշաբթի\",\n    \"ուրբաթ\",\n    \"շաբաթ\",\n  ],\n};\n\nconst dayPeriodValues = {\n  narrow: {\n    am: \"a\",\n    pm: \"p\",\n    midnight: \"կեսգշ\",\n    noon: \"կեսօր\",\n    morning: \"առավոտ\",\n    afternoon: \"ցերեկ\",\n    evening: \"երեկո\",\n    night: \"գիշեր\",\n  },\n  abbreviated: {\n    am: \"AM\",\n    pm: \"PM\",\n    midnight: \"կեսգիշեր\",\n    noon: \"կեսօր\",\n    morning: \"առավոտ\",\n    afternoon: \"ցերեկ\",\n    evening: \"երեկո\",\n    night: \"գիշեր\",\n  },\n  wide: {\n    am: \"a.m.\",\n    pm: \"p.m.\",\n    midnight: \"կեսգիշեր\",\n    noon: \"կեսօր\",\n    morning: \"առավոտ\",\n    afternoon: \"ցերեկ\",\n    evening: \"երեկո\",\n    night: \"գիշեր\",\n  },\n};\n\nconst formattingDayPeriodValues = {\n  narrow: {\n    am: \"a\",\n    pm: \"p\",\n    midnight: \"կեսգշ\",\n    noon: \"կեսօր\",\n    morning: \"առավոտը\",\n    afternoon: \"ցերեկը\",\n    evening: \"երեկոյան\",\n    night: \"գիշերը\",\n  },\n  abbreviated: {\n    am: \"AM\",\n    pm: \"PM\",\n    midnight: \"կեսգիշերին\",\n    noon: \"կեսօրին\",\n    morning: \"առավոտը\",\n    afternoon: \"ցերեկը\",\n    evening: \"երեկոյան\",\n    night: \"գիշերը\",\n  },\n  wide: {\n    am: \"a.m.\",\n    pm: \"p.m.\",\n    midnight: \"կեսգիշերին\",\n    noon: \"կեսօրին\",\n    morning: \"առավոտը\",\n    afternoon: \"ցերեկը\",\n    evening: \"երեկոյան\",\n    night: \"գիշերը\",\n  },\n};\n\nconst ordinalNumber = (dirtyNumber, _options) => {\n  const number = Number(dirtyNumber);\n\n  // If ordinal numbers depend on context, for example,\n  // if they are different for different grammatical genders,\n  // use `options.unit`.\n  //\n  // `unit` can be 'year', 'quarter', 'month', 'week', 'date', 'dayOfYear',\n  // 'day', 'hour', 'minute', 'second'.\n\n  const rem100 = number % 100;\n  if (rem100 < 10) {\n    if (rem100 % 10 === 1) {\n      return number + \"֊ին\";\n    }\n  }\n  return number + \"֊րդ\";\n};\n\nexport const localize = {\n  ordinalNumber,\n\n  era: buildLocalizeFn({\n    values: eraValues,\n    defaultWidth: \"wide\",\n  }),\n\n  quarter: buildLocalizeFn({\n    values: quarterValues,\n    defaultWidth: \"wide\",\n    argumentCallback: (quarter) => quarter - 1,\n  }),\n\n  month: buildLocalizeFn({\n    values: monthValues,\n    defaultWidth: \"wide\",\n  }),\n\n  day: buildLocalizeFn({\n    values: dayValues,\n    defaultWidth: \"wide\",\n  }),\n\n  dayPeriod: buildLocalizeFn({\n    values: dayPeriodValues,\n    defaultWidth: \"wide\",\n    formattingValues: formattingDayPeriodValues,\n    defaultFormattingWidth: \"wide\",\n  }),\n};\n"], "mappings": "AAAA,SAASA,eAAe,QAAQ,+BAA+B;AAE/D,MAAMC,SAAS,GAAG;EAChBC,MAAM,EAAE,CAAC,GAAG,EAAE,GAAG,CAAC;EAClBC,WAAW,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC;EACzBC,IAAI,EAAE,CAAC,gBAAgB,EAAE,iBAAiB;AAC5C,CAAC;AAED,MAAMC,aAAa,GAAG;EACpBH,MAAM,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EAC5BC,WAAW,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;EACrCC,IAAI,EAAE,CAAC,aAAa,EAAE,aAAa,EAAE,aAAa,EAAE,aAAa;AACnE,CAAC;AAED,MAAME,WAAW,GAAG;EAClBJ,MAAM,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EACpEC,WAAW,EAAE,CACX,MAAM,EACN,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,MAAM,EACN,MAAM,EACN,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,CACN;EAEDC,IAAI,EAAE,CACJ,SAAS,EACT,SAAS,EACT,MAAM,EACN,OAAO,EACP,OAAO,EACP,QAAQ,EACR,QAAQ,EACR,SAAS,EACT,WAAW,EACX,WAAW,EACX,UAAU,EACV,WAAW;AAEf,CAAC;AAED,MAAMG,SAAS,GAAG;EAChBL,MAAM,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EAC3CM,KAAK,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,CAAC;EAClDL,WAAW,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK,CAAC;EAC/DC,IAAI,EAAE,CACJ,QAAQ,EACR,YAAY,EACZ,WAAW,EACX,YAAY,EACZ,WAAW,EACX,QAAQ,EACR,OAAO;AAEX,CAAC;AAED,MAAMK,eAAe,GAAG;EACtBP,MAAM,EAAE;IACNQ,EAAE,EAAE,GAAG;IACPC,EAAE,EAAE,GAAG;IACPC,QAAQ,EAAE,OAAO;IACjBC,IAAI,EAAE,OAAO;IACbC,OAAO,EAAE,QAAQ;IACjBC,SAAS,EAAE,OAAO;IAClBC,OAAO,EAAE,OAAO;IAChBC,KAAK,EAAE;EACT,CAAC;EACDd,WAAW,EAAE;IACXO,EAAE,EAAE,IAAI;IACRC,EAAE,EAAE,IAAI;IACRC,QAAQ,EAAE,UAAU;IACpBC,IAAI,EAAE,OAAO;IACbC,OAAO,EAAE,QAAQ;IACjBC,SAAS,EAAE,OAAO;IAClBC,OAAO,EAAE,OAAO;IAChBC,KAAK,EAAE;EACT,CAAC;EACDb,IAAI,EAAE;IACJM,EAAE,EAAE,MAAM;IACVC,EAAE,EAAE,MAAM;IACVC,QAAQ,EAAE,UAAU;IACpBC,IAAI,EAAE,OAAO;IACbC,OAAO,EAAE,QAAQ;IACjBC,SAAS,EAAE,OAAO;IAClBC,OAAO,EAAE,OAAO;IAChBC,KAAK,EAAE;EACT;AACF,CAAC;AAED,MAAMC,yBAAyB,GAAG;EAChChB,MAAM,EAAE;IACNQ,EAAE,EAAE,GAAG;IACPC,EAAE,EAAE,GAAG;IACPC,QAAQ,EAAE,OAAO;IACjBC,IAAI,EAAE,OAAO;IACbC,OAAO,EAAE,SAAS;IAClBC,SAAS,EAAE,QAAQ;IACnBC,OAAO,EAAE,UAAU;IACnBC,KAAK,EAAE;EACT,CAAC;EACDd,WAAW,EAAE;IACXO,EAAE,EAAE,IAAI;IACRC,EAAE,EAAE,IAAI;IACRC,QAAQ,EAAE,YAAY;IACtBC,IAAI,EAAE,SAAS;IACfC,OAAO,EAAE,SAAS;IAClBC,SAAS,EAAE,QAAQ;IACnBC,OAAO,EAAE,UAAU;IACnBC,KAAK,EAAE;EACT,CAAC;EACDb,IAAI,EAAE;IACJM,EAAE,EAAE,MAAM;IACVC,EAAE,EAAE,MAAM;IACVC,QAAQ,EAAE,YAAY;IACtBC,IAAI,EAAE,SAAS;IACfC,OAAO,EAAE,SAAS;IAClBC,SAAS,EAAE,QAAQ;IACnBC,OAAO,EAAE,UAAU;IACnBC,KAAK,EAAE;EACT;AACF,CAAC;AAED,MAAME,aAAa,GAAGA,CAACC,WAAW,EAAEC,QAAQ,KAAK;EAC/C,MAAMC,MAAM,GAAGC,MAAM,CAACH,WAAW,CAAC;;EAElC;EACA;EACA;EACA;EACA;EACA;;EAEA,MAAMI,MAAM,GAAGF,MAAM,GAAG,GAAG;EAC3B,IAAIE,MAAM,GAAG,EAAE,EAAE;IACf,IAAIA,MAAM,GAAG,EAAE,KAAK,CAAC,EAAE;MACrB,OAAOF,MAAM,GAAG,KAAK;IACvB;EACF;EACA,OAAOA,MAAM,GAAG,KAAK;AACvB,CAAC;AAED,OAAO,MAAMG,QAAQ,GAAG;EACtBN,aAAa;EAEbO,GAAG,EAAE1B,eAAe,CAAC;IACnB2B,MAAM,EAAE1B,SAAS;IACjB2B,YAAY,EAAE;EAChB,CAAC,CAAC;EAEFC,OAAO,EAAE7B,eAAe,CAAC;IACvB2B,MAAM,EAAEtB,aAAa;IACrBuB,YAAY,EAAE,MAAM;IACpBE,gBAAgB,EAAGD,OAAO,IAAKA,OAAO,GAAG;EAC3C,CAAC,CAAC;EAEFE,KAAK,EAAE/B,eAAe,CAAC;IACrB2B,MAAM,EAAErB,WAAW;IACnBsB,YAAY,EAAE;EAChB,CAAC,CAAC;EAEFI,GAAG,EAAEhC,eAAe,CAAC;IACnB2B,MAAM,EAAEpB,SAAS;IACjBqB,YAAY,EAAE;EAChB,CAAC,CAAC;EAEFK,SAAS,EAAEjC,eAAe,CAAC;IACzB2B,MAAM,EAAElB,eAAe;IACvBmB,YAAY,EAAE,MAAM;IACpBM,gBAAgB,EAAEhB,yBAAyB;IAC3CiB,sBAAsB,EAAE;EAC1B,CAAC;AACH,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
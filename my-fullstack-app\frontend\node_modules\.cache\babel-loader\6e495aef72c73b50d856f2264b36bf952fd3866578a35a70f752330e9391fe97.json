{"ast": null, "code": "/** Regex to identify the presence of a time zone specifier in a date string */\nexport const tzPattern = /(Z|[+-]\\d{2}(?::?\\d{2})?| UTC| [a-zA-Z]+\\/[a-zA-Z_]+(?:\\/[a-zA-Z_]+)?)$/;", "map": {"version": 3, "names": ["tzPattern"], "sources": ["C:/Users/<USER>/Desktop/demo-git/demo-react/my-fullstack-app/frontend/node_modules/date-fns-tz/dist/esm/_lib/tzPattern/index.js"], "sourcesContent": ["/** Regex to identify the presence of a time zone specifier in a date string */\nexport const tzPattern = /(Z|[+-]\\d{2}(?::?\\d{2})?| UTC| [a-zA-Z]+\\/[a-zA-Z_]+(?:\\/[a-zA-Z_]+)?)$/;\n"], "mappings": "AAAA;AACA,OAAO,MAAMA,SAAS,GAAG,yEAAyE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
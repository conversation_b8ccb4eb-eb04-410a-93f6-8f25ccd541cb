import { useCallback, useEffect, useState } from "react";
import api from '../services/api';

// 定義 UserRole 中每筆物件型別
interface UserRole {
  userId: number;
  userName: string;
  userAccount: string;
  userEmail: string;
  userPhone: string;
  isEnabled: boolean;
  roleId: number;
  roleName: string;
  createdAt: string;
  updatedAt: string;
}

export default function useUser(
    RoleId: number
) {
  const [userRole, setuserRole] = useState<UserRole[]>([]);
  const [Roleloading, setRoleloading] = useState(true);

  const fetchUsers = useCallback(() => {
    setRoleloading(true);
    api.get<UserRole[]>("/api/users/GetList", {
        params: {
          name: "",
          roleId: RoleId,
        },
      })
      .then(res => setuserRole(res.data))
      .catch(err => console.error("API Error:", err))
      .finally(() => setRoleloading(false));
  }, [RoleId]);

  useEffect(() => {
    fetchUsers();
  }, [fetchUsers]);

  const refetch = useCallback(() => {
    fetchUsers();
  }, [fetchUsers]);

  return { userRole, Roleloading, refetch };
}
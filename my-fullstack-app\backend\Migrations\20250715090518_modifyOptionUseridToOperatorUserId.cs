﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace MyApi.Migrations
{
    /// <inheritdoc />
    public partial class modifyOptionUseridToOperatorUserId : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.RenameColumn(
                name: "OptionUserId",
                table: "Users",
                newName: "OperatorUserId");

            migrationBuilder.RenameColumn(
                name: "OptionUserId",
                table: "UserRoles",
                newName: "OperatorUserId");

            migrationBuilder.RenameColumn(
                name: "OptionUserId",
                table: "Treatments",
                newName: "OperatorUserId");

            migrationBuilder.RenameColumn(
                name: "OptionUserId",
                table: "Roles",
                newName: "OperatorUserId");

            migrationBuilder.RenameColumn(
                name: "OptionUserId",
                table: "Receipts",
                newName: "OperatorUserId");

            migrationBuilder.RenameColumn(
                name: "OptionUserId",
                table: "Patients",
                newName: "OperatorUserId");

            migrationBuilder.RenameColumn(
                name: "OptionUserId",
                table: "Menus",
                newName: "OperatorUserId");

            migrationBuilder.RenameColumn(
                name: "OptionUserId",
                table: "MenuGroups",
                newName: "OperatorUserId");

            migrationBuilder.RenameColumn(
                name: "OptionUserId",
                table: "DataTypes",
                newName: "OperatorUserId");

            migrationBuilder.RenameColumn(
                name: "OptionUserId",
                table: "DataTypeGroups",
                newName: "OperatorUserId");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.RenameColumn(
                name: "OperatorUserId",
                table: "Users",
                newName: "OptionUserId");

            migrationBuilder.RenameColumn(
                name: "OperatorUserId",
                table: "UserRoles",
                newName: "OptionUserId");

            migrationBuilder.RenameColumn(
                name: "OperatorUserId",
                table: "Treatments",
                newName: "OptionUserId");

            migrationBuilder.RenameColumn(
                name: "OperatorUserId",
                table: "Roles",
                newName: "OptionUserId");

            migrationBuilder.RenameColumn(
                name: "OperatorUserId",
                table: "Receipts",
                newName: "OptionUserId");

            migrationBuilder.RenameColumn(
                name: "OperatorUserId",
                table: "Patients",
                newName: "OptionUserId");

            migrationBuilder.RenameColumn(
                name: "OperatorUserId",
                table: "Menus",
                newName: "OptionUserId");

            migrationBuilder.RenameColumn(
                name: "OperatorUserId",
                table: "MenuGroups",
                newName: "OptionUserId");

            migrationBuilder.RenameColumn(
                name: "OperatorUserId",
                table: "DataTypes",
                newName: "OptionUserId");

            migrationBuilder.RenameColumn(
                name: "OperatorUserId",
                table: "DataTypeGroups",
                newName: "OptionUserId");
        }
    }
}

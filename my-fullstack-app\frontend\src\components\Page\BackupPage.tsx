import { formatUtcToTaipei } from '../../utils/dateUtils';
import React, { useState, useEffect, useRef } from 'react';
import { DataTable } from 'primereact/datatable';
import { Column } from 'primereact/column';
import { Card } from 'primereact/card';
import { Button } from 'primereact/button';
import { Toast } from 'primereact/toast';
import { Tag } from 'primereact/tag';
import { Message } from 'primereact/message';
import { ProgressSpinner } from 'primereact/progressspinner';
import api from '../../services/api';
import { log } from '../../utils/logger';

interface BackupFile {
  fileName: string;
  sizeMB: number;
  lastModified: string;
}

interface BackupStatus {
  hasTodayBackup: boolean;
  todayBackupFile?: string;
  totalBackupFiles: number;
  latestBackupTime?: string;
}

const BackupPage: React.FC = () => {
  const [backupFiles, setBackupFiles] = useState<BackupFile[]>([]);
  const [backupStatus, setBackupStatus] = useState<BackupStatus | null>(null);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const toast = useRef<Toast>(null);

  // 載入備份資料
  const loadBackupData = async () => {
    try {
      setRefreshing(true);
      log.api('載入備份資料');

      // 同時載入備份清單和狀態
      const [filesResponse, statusResponse] = await Promise.all([
        api.get('/api/backup/list'),
        api.get('/api/backup/status')
      ]);

      setBackupFiles(filesResponse.data);
      setBackupStatus(statusResponse.data);

      log.api('備份資料載入成功', {
        filesCount: filesResponse.data.length,
        hasTodayBackup: statusResponse.data.hasTodayBackup
      });

    } catch (error: any) {
      log.error('載入備份資料失敗', error);
      var detail =  error.status === 403 ? "您沒有權限查看備份資料" : error.response?.data?.message || '載入失敗';

      toast.current?.show({
        severity: 'error',
        summary: '載入失敗',
        detail: detail,
        life: 5000
      });
      
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  // 下載備份檔案
  const downloadBackup = async (fileName: string) => {
    try {
      log.api('開始下載備份檔案', { fileName });

      const response = await api.get(`/api/backup/download?file=${encodeURIComponent(fileName)}`, {
        responseType: 'blob'
      });

      // 創建下載連結
      const url = window.URL.createObjectURL(new Blob([response.data]));
      const link = document.createElement('a');
      link.href = url;
      link.setAttribute('download', fileName);
      document.body.appendChild(link);
      link.click();
      link.remove();
      window.URL.revokeObjectURL(url);

      toast.current?.show({
        severity: 'success',
        summary: '下載成功',
        detail: `檔案 ${fileName} 下載完成`,
        life: 3000
      });

      log.api('備份檔案下載成功', { fileName });

    } catch (error: any) {
      log.error('下載備份檔案失敗', error);
      
      toast.current?.show({
        severity: 'error',
        summary: '下載失敗',
        detail: `無法下載檔案 ${fileName}`,
        life: 5000
      });
    }
  };

  // 格式化檔案大小
  const formatFileSize = (sizeMB: number) => {
    if (sizeMB < 1) {
      return `${Math.round(sizeMB * 1024)} KB`;
    }
    return `${sizeMB.toFixed(2)} MB`;
  };

  // 格式化日期時間
  const formatDateTime = (dateString: string) => {
    if (!dateString) return '';
    return formatUtcToTaipei(dateString, "yyyy/MM/dd HH:mm:ss");
  };

  // 檔案名稱欄位模板（可點擊下載）
  const fileNameTemplate = (rowData: BackupFile) => {
    return (
      <Button
        label={rowData.fileName}
        className="p-button-link p-0"
        onClick={() => downloadBackup(rowData.fileName)}
        tooltip="點擊下載檔案"
      />
    );
  };

  // 檔案大小欄位模板
  const fileSizeTemplate = (rowData: BackupFile) => {
    return formatFileSize(rowData.sizeMB);
  };

  // 最後修改時間欄位模板
  const lastModifiedTemplate = (rowData: BackupFile) => {
    return formatDateTime(rowData.lastModified);
  };

  // 今日備份狀態組件
  const TodayBackupStatus = () => {
    if (!backupStatus) return null;

    return (
      <div className="mb-4">
        {backupStatus.hasTodayBackup ? (
          <Message
            severity="success"
            text={`今日備份完成 - ${backupStatus.todayBackupFile}`}
            className="w-full"
          />
        ) : (
          <Message
            severity="error"
            text="今日尚未備份"
            className="w-full"
          />
        )}
      </div>
    );
  };

  // 備份統計資訊
  const BackupStats = () => {
    if (!backupStatus) return null;

    return (
      <div className="grid mb-4">
        <div className="col-12 md:col-4">
          <div className="surface-card p-3 border-round">
            <div className="flex justify-content-between align-items-start">
              <div>
                <span className="block text-500 font-medium mb-1">總備份檔案</span>
                <div className="text-900 font-medium text-xl">{backupStatus.totalBackupFiles}</div>
              </div>
              <div className="flex align-items-center justify-content-center bg-blue-100 border-round" style={{width: '2.5rem', height: '2.5rem'}}>
                <i className="pi pi-file text-blue-500 text-xl"></i>
              </div>
            </div>
          </div>
        </div>
        <div className="col-12 md:col-4">
          <div className="surface-card p-3 border-round">
            <div className="flex justify-content-between align-items-start">
              <div>
                <span className="block text-500 font-medium mb-1">今日備份狀態</span>
                <div className="text-900 font-medium text-xl">
                  <Tag 
                    value={backupStatus.hasTodayBackup ? "已完成" : "未完成"} 
                    severity={backupStatus.hasTodayBackup ? "success" : "danger"}
                  />
                </div>
              </div>
              <div className="flex align-items-center justify-content-center bg-green-100 border-round" style={{width: '2.5rem', height: '2.5rem'}}>
                <i className="pi pi-check-circle text-green-500 text-xl"></i>
              </div>
            </div>
          </div>
        </div>
        <div className="col-12 md:col-4">
          <div className="surface-card p-3 border-round">
            <div className="flex justify-content-between align-items-start">
              <div>
                <span className="block text-500 font-medium mb-1">最新備份時間</span>
                <div className="text-900 font-medium text-sm">
                  {backupStatus.latestBackupTime 
                    ? formatDateTime(backupStatus.latestBackupTime)
                    : '無'
                  }
                </div>
              </div>
              <div className="flex align-items-center justify-content-center bg-orange-100 border-round" style={{width: '2.5rem', height: '2.5rem'}}>
                <i className="pi pi-clock text-orange-500 text-xl"></i>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  };

  useEffect(() => {
    loadBackupData();
  }, []);

  if (loading) {
    return (
      <div className="flex align-items-center justify-content-center min-h-screen">
        <div className="text-center">
          <ProgressSpinner />
          <p className="mt-3">載入備份資料中...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="backup-page">
      <Toast ref={toast} />
      
      <div className="grid">
        <div className="col-12">
          <Card title="資料庫備份管理" className="mb-4">
            <div className="flex justify-content-between align-items-center mb-4">
              <h5 className="m-0">備份檔案清單</h5>
              <Button
                label="重新整理"
                icon={refreshing ? "pi pi-spin pi-spinner" : "pi pi-refresh"}
                onClick={loadBackupData}
                disabled={refreshing}
                className="p-button-outlined"
              />
            </div>

            <TodayBackupStatus />
            <BackupStats />

            <DataTable
              value={backupFiles}
              paginator
              rows={10}
              rowsPerPageOptions={[5, 10, 25, 50]}
              sortMode="multiple"
              removableSort
              filterDisplay="menu"
              globalFilterFields={['fileName']}
              emptyMessage="沒有找到備份檔案"
              className="p-datatable-gridlines"
            >
              <Column
                field="fileName"
                header="檔案名稱"
                sortable
                filter
                filterPlaceholder="搜尋檔案名稱"
                body={fileNameTemplate}
                style={{ minWidth: '300px' }}
              />
              <Column
                field="sizeMB"
                header="檔案大小"
                sortable
                body={fileSizeTemplate}
                style={{ minWidth: '120px' }}
              />
              <Column
                field="lastModified"
                header="最後修改時間"
                sortable
                body={lastModifiedTemplate}
                style={{ minWidth: '200px' }}
              />
            </DataTable>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default BackupPage;

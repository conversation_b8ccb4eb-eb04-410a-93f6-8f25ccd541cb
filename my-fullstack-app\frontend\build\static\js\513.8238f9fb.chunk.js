"use strict";(self.webpackChunkfrontend=self.webpackChunkfrontend||[]).push([[513],{1104:(e,t,n)=>{n.d(t,{T:()=>N,Z:()=>w});var r=n(5043),a=n(4052),i=n(2018),l=n(1828),o=n(5797),s=n(2028),c=n(9988),u=n(8794),d=n(4504);function p(){return p=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)({}).hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},p.apply(null,arguments)}function f(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=Array(t);n<t;n++)r[n]=e[n];return r}function m(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,a,i,l,o=[],s=!0,c=!1;try{if(i=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;s=!1}else for(;!(s=(r=i.call(n)).done)&&(o.push(r.value),o.length!==t);s=!0);}catch(e){c=!0,a=e}finally{try{if(!s&&null!=n.return&&(l=n.return(),Object(l)!==l))return}finally{if(c)throw a}}return o}}(e,t)||function(e,t){if(e){if("string"==typeof e)return f(e,t);var n={}.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?f(e,t):void 0}}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function g(e){return g="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},g(e)}function v(e){var t=function(e,t){if("object"!=g(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!=g(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==g(t)?t:t+""}function y(e,t,n){return(t=v(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var b={root:"p-confirm-dialog",message:"p-confirm-dialog-message",icon:"p-confirm-dialog-icon",acceptButton:"p-confirm-dialog-accept",rejectButton:function(e){var t=e.getPropValue;return(0,d.xW)("p-confirm-dialog-reject",{"p-button-text":!t("rejectClassName")})}},h=l.x.extend({defaultProps:{__TYPE:"ConfirmDialog",accept:null,acceptClassName:null,acceptIcon:null,acceptLabel:null,appendTo:null,breakpoints:null,children:void 0,className:null,content:null,defaultFocus:"accept",footer:null,icon:null,message:null,onHide:null,reject:null,rejectClassName:null,rejectIcon:null,rejectLabel:null,tagKey:void 0,visible:void 0},css:{classes:b}});function j(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function x(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?j(Object(n),!0).forEach((function(t){y(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):j(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var w=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};(e=x(x({},e),{visible:void 0===e.visible||e.visible})).visible&&c.s.emit("confirm-dialog",e);return{show:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};c.s.emit("confirm-dialog",x(x(x({},e),t),{visible:!0}))},hide:function(){c.s.emit("confirm-dialog",{visible:!1})}}},N=r.memo(r.forwardRef((function(e,t){var n=(0,s.qV)(),f=r.useContext(a.UM),g=h.getProps(e,f),v=m(r.useState(g.visible),2),y=v[0],b=v[1],j=m(r.useState(!1),2),w=j[0],N=j[1],S=r.useRef(null),C=r.useRef(!1),O=r.useRef(null),E=function(){var e=g.group;return S.current&&(e=S.current.group),Object.assign({},g,S.current,{group:e})},P=function(e){return E()[e]},D=function(e){for(var t=arguments.length,n=new Array(t>1?t-1:0),r=1;r<t;r++)n[r-1]=arguments[r];return d.BF.getPropValue(P(e),n)},T=P("acceptLabel")||(0,a.WP)("accept"),A=P("rejectLabel")||(0,a.WP)("reject"),k={props:g,state:{visible:y}},I=h.setMetaData(k),M=I.ptm,V=I.cx,F=I.isUnstyled;(0,l.j)(h.css.styles,F,{name:"confirmdialog"});var L=function(){C.current||(C.current=!0,D("accept"),_("accept"))},B=function(){C.current||(C.current=!0,D("reject"),_("reject"))},$=function(){E().group===g.group&&(b(!0),C.current=!1,O.current=document.activeElement)},_=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"cancel";y&&("string"!==typeof e&&(e="cancel"),b(!1),D("onHide",e),d.DV.focus(O.current),O.current=null)},K=function(e){if(e.tagKey===g.tagKey){var t=y!==e.visible;P("target")!==e.target&&!g.target?(_(),S.current=e,N(!0)):t&&(S.current=e,e.visible?$():_())}};r.useEffect((function(){g.visible?$():_()}),[g.visible]),r.useEffect((function(){return g.target||g.message||c.s.on("confirm-dialog",K),function(){c.s.off("confirm-dialog",K)}}),[g.target]),(0,s.w5)((function(){w&&$()}),[w]),(0,s.l0)((function(){c.s.off("confirm-dialog",K)})),r.useImperativeHandle(t,(function(){return{props:g,confirm:K}}));var W=function(){var t=E(),a=d.BF.getJSXElement(P("message"),t),l=n({className:V("icon")},M("icon")),s=d.Hj.getJSXIcon(P("icon"),x({},l),{props:t}),c=function(){var e=P("defaultFocus"),t=(0,d.xW)("p-confirm-dialog-accept",P("acceptClassName")),a=(0,d.xW)("p-confirm-dialog-reject",{"p-button-text":!P("rejectClassName")},P("rejectClassName")),l=n({label:A,autoFocus:"reject"===e,icon:P("rejectIcon"),className:(0,d.xW)(P("rejectClassName"),V("rejectButton",{getPropValue:P})),onClick:B,pt:M("rejectButton"),unstyled:g.unstyled,__parentMetadata:{parent:k}},M("rejectButton")),o=n({label:T,autoFocus:void 0===e||"accept"===e,icon:P("acceptIcon"),className:(0,d.xW)(P("acceptClassName"),V("acceptButton")),onClick:L,pt:M("acceptButton"),unstyled:g.unstyled,__parentMetadata:{parent:k}},M("acceptButton")),s=r.createElement(r.Fragment,null,r.createElement(i.$,l),r.createElement(i.$,o));if(P("footer")){var c={accept:L,reject:B,acceptClassName:t,rejectClassName:a,acceptLabel:T,rejectLabel:A,element:s,props:E()};return d.BF.getJSXElement(P("footer"),c)}return s}(),u=n({className:V("message")},M("message")),f=n({visible:y,className:(0,d.xW)(P("className"),V("root")),footer:c,onHide:_,breakpoints:P("breakpoints"),pt:t.pt,unstyled:g.unstyled,appendTo:P("appendTo"),__parentMetadata:{parent:k}},h.getOtherProps(t));return r.createElement(o.l,p({},f,{content:null===e||void 0===e?void 0:e.content}),s,r.createElement("span",u,a))}();return r.createElement(u.Z,{element:W,appendTo:P("appendTo")})})));N.displayName="ConfirmDialog"},2513:(e,t,n)=>{n.r(t),n.d(t,{default:()=>j});var r=n(5855),a=n(2018),i=n(5371),l=n(9642),o=n(1104),s=n(1063),c=n(2052),u=n(5556),d=n(828),p=n(5043),f=n(5666),m=n(724),g=n(9304),v=n(9604);var y=n(402),b=n(8150),h=n(579);const j=()=>{const e=(0,f.Zp)(),t=(0,p.useRef)(null),[n,j]=(0,p.useState)(""),[x,w]=(0,p.useState)(void 0),[N,S]=(0,p.useState)(void 0),[C,O]=(0,p.useState)(0),[E,P]=(0,p.useState)(!1),[D,T]=(0,p.useState)({name:"",starttime:null,endtime:null,refreshKey:0}),{patients:A,loading:k}=function(){let{fullName:e="",startTime:t,endTime:n,refreshKey:r=0}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};const a=(0,p.useMemo)((()=>({name:e||void 0,starttime:null===t||void 0===t?void 0:t.toISOString(),endtime:null===n||void 0===n?void 0:n.toISOString()})),[e,t,n]),i=(0,p.useMemo)((()=>()=>g.s6.getList(a)),[a]),{data:l=[],loading:o,error:s,refetch:c}=(0,v.K)(i,{dependencies:[r],initialData:[]});return{patients:l,loading:o,error:s,refetch:c}}({fullName:D.name,startTime:D.starttime||null,endTime:D.endtime||null,refreshKey:C}),I={1:"\u7537\u6027",2:"\u5973\u6027",3:"\u5176\u4ed6"};(0,p.useEffect)((()=>{var e;E&&!k&&(null===(e=t.current)||void 0===e||e.show({severity:"success",summary:"\u6210\u529f",detail:"\u75c5\u60a3\u8cc7\u6599\u5df2\u522a\u9664"}),P(!1))}),[k]);const M=()=>{O((e=>e+1))},V=(0,h.jsx)(a.$,{type:"button",icon:"pi pi-refresh",text:!0,onClick:()=>M()}),F=(0,h.jsx)("div",{}),L=e=>{(0,o.Z)({message:"\u78ba\u5b9a\u8981\u522a\u9664\u9019\u7b46\u8cc7\u6599\u55ce\uff1f",header:"\u522a\u9664\u78ba\u8a8d",icon:"pi pi-exclamation-triangle",defaultFocus:"reject",acceptClassName:"p-button-danger",acceptLabel:"\u78ba\u5b9a",rejectLabel:"\u53d6\u6d88",accept:()=>(async e=>{try{await y.A.get("/api/patients/Delete",{params:{id:e}}),P(!0),M()}catch(l){var n,r,a,i=403===l.status?"\u60a8\u7121\u6b0a\u9650\uff0c\u8acb\u901a\u77e5\u7ba1\u7406\u54e1":(null===(n=l.response)||void 0===n||null===(r=n.data)||void 0===r?void 0:r.message)||"\u522a\u9664\u5931\u6557";null===(a=t.current)||void 0===a||a.show({severity:"error",summary:"\u932f\u8aa4",detail:i})}})(e)})},B=e=>e?(0,r.$)(e,"yyyy/MM/dd HH:mm:ss"):"";return k?(0,h.jsx)(u.A,{message:"\u8f09\u5165\u75c5\u60a3\u8cc7\u6599\u4e2d..."}):(0,h.jsxs)("div",{children:[(0,h.jsx)(d.y,{ref:t}),(0,h.jsx)(o.T,{}),(0,h.jsx)(b.Z,{title:"\u75c5\u60a3\u7ba1\u7406",className:"mb-4",children:(0,h.jsx)("p",{className:"text-600 line-height-3 m-0",children:"\u75c5\u60a3\u7ba1\u7406\u9801\u9762\uff0c\u53ef\u4ee5\u67e5\u8a62\u3001\u65b0\u589e\u3001\u7de8\u8f2f\u3001\u522a\u9664\u75c5\u60a3\u8cc7\u6599\u3002"})}),(0,h.jsx)(b.Z,{className:"mb-4",children:(0,h.jsxs)("div",{className:"grid",children:[(0,h.jsx)("div",{className:"col-12 md:col-4",children:(0,h.jsx)(c.S,{id:"name",type:"text",value:n,onChange:e=>j(e.target.value),placeholder:"\u75c5\u60a3\u59d3\u540d",className:"w-full"})}),(0,h.jsx)("div",{className:"col-6 md:col-3",children:(0,h.jsx)(i.V,{id:"starttime",value:x,onChange:e=>w(e.value),placeholder:"\u958b\u59cb\u6642\u9593",className:"w-full",dateFormat:"yy/mm/dd",showIcon:!0})}),(0,h.jsx)("div",{className:"col-6 md:col-3",children:(0,h.jsx)(i.V,{id:"endtime",value:N,onChange:e=>S(e.value),placeholder:"\u7d50\u675f\u6642\u9593",className:"w-full",dateFormat:"yy/mm/dd",showIcon:!0})}),(0,h.jsx)("div",{className:"col-12 md:col-4",children:(0,h.jsxs)("div",{className:"flex gap-2",children:[(0,h.jsx)(a.$,{label:"\u67e5\u8a62",icon:"pi pi-search",onClick:()=>{O(C+1),T({name:n,starttime:x,endtime:N,refreshKey:C})}}),(0,h.jsx)(a.$,{label:"\u65b0\u589e",icon:"pi pi-plus",onClick:()=>{e("/patientsdetail")}})]})})]})}),(0,h.jsx)(b.Z,{children:(0,h.jsxs)(s.b,{value:A,paginator:!0,rows:10,rowsPerPageOptions:[10,20,30,40],tableStyle:{minWidth:"50rem"},emptyMessage:"\u6c92\u6709\u627e\u5230\u75c5\u60a3\u8cc7\u6599",paginatorLeft:V,paginatorRight:F,children:[(0,h.jsx)(l.V,{field:"id",header:"ID",style:{width:"5%"}}),(0,h.jsx)(l.V,{field:"fullName",header:"\u59d3\u540d",style:{width:"10%"}}),(0,h.jsx)(l.V,{field:"gender",header:"\u6027\u5225",style:{width:"5%"},body:e=>{var t=String(e.gender);const n=I[t];return(0,h.jsx)("div",{children:n})}}),(0,h.jsx)(l.V,{field:"birthDate",header:"\u5e74\u9f61",style:{width:"5%"},body:e=>(e=>{if(!e)return"";const t=new Date(e),n=new Date;let r=n.getFullYear()-t.getFullYear();return(n.getMonth()<t.getMonth()||n.getMonth()===t.getMonth()&&n.getDate()<t.getDate())&&r--,r})(e.birthDate)}),(0,h.jsx)(l.V,{field:"createdAt",header:"\u65b0\u589e\u65e5\u671f",style:{width:"10%"},body:e=>B(e.createdAt)}),(0,h.jsx)(l.V,{field:"updatedAt",header:"\u66f4\u65b0\u65e5\u671f",style:{width:"10%"},body:e=>B(e.updatedAt)}),(0,h.jsx)(l.V,{field:"operatorUserName",header:"\u64cd\u4f5c\u4eba",style:{width:"5%"}}),(0,h.jsx)(l.V,{field:"option",header:"\u529f\u80fd",style:{width:"12%"},body:n=>(0,h.jsxs)("div",{className:"flex gap-1",children:[(0,h.jsx)(a.$,{label:"\u7de8\u8f2f",type:"button",icon:"pi pi-file-edit",onClick:()=>(async n=>{try{const t=(await y.A.get("/api/patients/",{params:{id:n}})).data;t&&e(m.bw.PATIENT_DETAIL,{state:{patient:t}})}catch(o){var r,a,i,l=403===o.status?"\u60a8\u7121\u6b0a\u9650\uff0c\u8acb\u901a\u77e5\u7ba1\u7406\u54e1":(null===(r=o.response)||void 0===r||null===(a=r.data)||void 0===a?void 0:a.message)||"\u7de8\u8f2f\u5931\u6557";null===(i=t.current)||void 0===i||i.show({severity:"error",summary:"\u932f\u8aa4",detail:l})}})(n.id),size:"small",severity:"info",style:{fontSize:"1rem",margin:"3px"}}),(0,h.jsx)(a.$,{label:"\u958b\u6848",type:"button",icon:"pi pi-clipboard",onClick:()=>(async t=>{e(m.bw.TREATMENT_DETAIL,{state:{patient:{id:t}}})})(n.id),size:"small",severity:"success",style:{fontSize:"1rem",margin:"3px"}}),(0,h.jsx)(a.$,{label:"\u522a\u9664",type:"button",icon:"pi pi-file-excel",onClick:()=>L(n.id),size:"small",severity:"danger",style:{fontSize:"1rem",margin:"3px"}})]})})]})})]})}},9604:(e,t,n)=>{n.d(t,{K:()=>i});var r=n(5043),a=n(1512);function i(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};const{initialData:n,dependencies:i=[],enabled:l=!0,onSuccess:o,onError:s}=t,[c,u]=(0,r.useState)(n),[d,p]=(0,r.useState)(!1),[f,m]=(0,r.useState)(null),{handleError:g}=(0,a.u)(),v=(0,r.useRef)(!0),y=(0,r.useCallback)((async()=>{if(l)try{p(!0),m(null);const t=await e();v.current&&(u(t),null===o||void 0===o||o(t))}catch(t){if(v.current){const e=g(t);m(e),null===s||void 0===s||s(e)}}finally{v.current&&p(!1)}}),[e,l,g,o,s]);return(0,r.useEffect)((()=>{y()}),[y,...i]),(0,r.useEffect)((()=>()=>{v.current=!1}),[]),{data:c,loading:d,error:f,refetch:y,setData:u}}}}]);
//# sourceMappingURL=513.8238f9fb.chunk.js.map
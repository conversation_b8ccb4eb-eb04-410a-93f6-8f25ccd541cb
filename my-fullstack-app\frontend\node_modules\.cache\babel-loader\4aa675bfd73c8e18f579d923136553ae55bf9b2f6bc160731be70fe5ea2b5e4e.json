{"ast": null, "code": "import { tzTokenizeDate } from '../tzTokenizeDate/index.js';\nimport { newDateUTC } from '../newDateUTC/index.js';\nconst MILLISECONDS_IN_HOUR = 3600000;\nconst MILLISECONDS_IN_MINUTE = 60000;\nconst patterns = {\n  timezone: /([Z+-].*)$/,\n  timezoneZ: /^(Z)$/,\n  timezoneHH: /^([+-]\\d{2})$/,\n  timezoneHHMM: /^([+-])(\\d{2}):?(\\d{2})$/\n};\n// Parse constious time zone offset formats to an offset in milliseconds\nexport function tzParseTimezone(timezoneString, date, isUtcDate) {\n  // Empty string\n  if (!timezoneString) {\n    return 0;\n  }\n  // Z\n  let token = patterns.timezoneZ.exec(timezoneString);\n  if (token) {\n    return 0;\n  }\n  let hours;\n  let absoluteOffset;\n  // ±hh\n  token = patterns.timezoneHH.exec(timezoneString);\n  if (token) {\n    hours = parseInt(token[1], 10);\n    if (!validateTimezone(hours)) {\n      return NaN;\n    }\n    return -(hours * MILLISECONDS_IN_HOUR);\n  }\n  // ±hh:mm or ±hhmm\n  token = patterns.timezoneHHMM.exec(timezoneString);\n  if (token) {\n    hours = parseInt(token[2], 10);\n    const minutes = parseInt(token[3], 10);\n    if (!validateTimezone(hours, minutes)) {\n      return NaN;\n    }\n    absoluteOffset = Math.abs(hours) * MILLISECONDS_IN_HOUR + minutes * MILLISECONDS_IN_MINUTE;\n    return token[1] === '+' ? -absoluteOffset : absoluteOffset;\n  }\n  // IANA time zone\n  if (isValidTimezoneIANAString(timezoneString)) {\n    date = new Date(date || Date.now());\n    const utcDate = isUtcDate ? date : toUtcDate(date);\n    const offset = calcOffset(utcDate, timezoneString);\n    const fixedOffset = isUtcDate ? offset : fixOffset(date, offset, timezoneString);\n    return -fixedOffset;\n  }\n  return NaN;\n}\nfunction toUtcDate(date) {\n  return newDateUTC(date.getFullYear(), date.getMonth(), date.getDate(), date.getHours(), date.getMinutes(), date.getSeconds(), date.getMilliseconds());\n}\nfunction calcOffset(date, timezoneString) {\n  const tokens = tzTokenizeDate(date, timezoneString);\n  // ms dropped because it's not provided by tzTokenizeDate\n  const asUTC = newDateUTC(tokens[0], tokens[1] - 1, tokens[2], tokens[3] % 24, tokens[4], tokens[5], 0).getTime();\n  let asTS = date.getTime();\n  const over = asTS % 1000;\n  asTS -= over >= 0 ? over : 1000 + over;\n  return asUTC - asTS;\n}\nfunction fixOffset(date, offset, timezoneString) {\n  const localTS = date.getTime();\n  // Our UTC time is just a guess because our offset is just a guess\n  let utcGuess = localTS - offset;\n  // Test whether the zone matches the offset for this ts\n  const o2 = calcOffset(new Date(utcGuess), timezoneString);\n  // If so, offset didn't change, and we're done\n  if (offset === o2) {\n    return offset;\n  }\n  // If not, change the ts by the difference in the offset\n  utcGuess -= o2 - offset;\n  // If that gives us the local time we want, we're done\n  const o3 = calcOffset(new Date(utcGuess), timezoneString);\n  if (o2 === o3) {\n    return o2;\n  }\n  // If it's different, we're in a hole time. The offset has changed, but we don't adjust the time\n  return Math.max(o2, o3);\n}\nfunction validateTimezone(hours, minutes) {\n  return -23 <= hours && hours <= 23 && (minutes == null || 0 <= minutes && minutes <= 59);\n}\nconst validIANATimezoneCache = {};\nfunction isValidTimezoneIANAString(timeZoneString) {\n  if (validIANATimezoneCache[timeZoneString]) return true;\n  try {\n    new Intl.DateTimeFormat(undefined, {\n      timeZone: timeZoneString\n    });\n    validIANATimezoneCache[timeZoneString] = true;\n    return true;\n  } catch (error) {\n    return false;\n  }\n}", "map": {"version": 3, "names": ["tzTokenizeDate", "newDateUTC", "MILLISECONDS_IN_HOUR", "MILLISECONDS_IN_MINUTE", "patterns", "timezone", "timezoneZ", "timezoneHH", "timezoneHHMM", "tzParseTimezone", "timezoneString", "date", "isUtcDate", "token", "exec", "hours", "absoluteOffset", "parseInt", "validateTimezone", "NaN", "minutes", "Math", "abs", "isValidTimezoneIANAString", "Date", "now", "utcDate", "toUtcDate", "offset", "calcOffset", "fixedOffset", "fixOffset", "getFullYear", "getMonth", "getDate", "getHours", "getMinutes", "getSeconds", "getMilliseconds", "tokens", "asUTC", "getTime", "asTS", "over", "localTS", "ut<PERSON><PERSON><PERSON><PERSON>", "o2", "o3", "max", "validIANATimezoneCache", "timeZoneString", "Intl", "DateTimeFormat", "undefined", "timeZone", "error"], "sources": ["C:/Users/<USER>/Desktop/demo-git/demo-react/my-fullstack-app/frontend/node_modules/date-fns-tz/dist/esm/_lib/tzParseTimezone/index.js"], "sourcesContent": ["import { tzTokenizeDate } from '../tzTokenizeDate/index.js';\nimport { newDateUTC } from '../newDateUTC/index.js';\nconst MILLISECONDS_IN_HOUR = 3600000;\nconst MILLISECONDS_IN_MINUTE = 60000;\nconst patterns = {\n    timezone: /([Z+-].*)$/,\n    timezoneZ: /^(Z)$/,\n    timezoneHH: /^([+-]\\d{2})$/,\n    timezoneHHMM: /^([+-])(\\d{2}):?(\\d{2})$/,\n};\n// Parse constious time zone offset formats to an offset in milliseconds\nexport function tzParseTimezone(timezoneString, date, isUtcDate) {\n    // Empty string\n    if (!timezoneString) {\n        return 0;\n    }\n    // Z\n    let token = patterns.timezoneZ.exec(timezoneString);\n    if (token) {\n        return 0;\n    }\n    let hours;\n    let absoluteOffset;\n    // ±hh\n    token = patterns.timezoneHH.exec(timezoneString);\n    if (token) {\n        hours = parseInt(token[1], 10);\n        if (!validateTimezone(hours)) {\n            return NaN;\n        }\n        return -(hours * MILLISECONDS_IN_HOUR);\n    }\n    // ±hh:mm or ±hhmm\n    token = patterns.timezoneHHMM.exec(timezoneString);\n    if (token) {\n        hours = parseInt(token[2], 10);\n        const minutes = parseInt(token[3], 10);\n        if (!validateTimezone(hours, minutes)) {\n            return NaN;\n        }\n        absoluteOffset = Math.abs(hours) * MILLISECONDS_IN_HOUR + minutes * MILLISECONDS_IN_MINUTE;\n        return token[1] === '+' ? -absoluteOffset : absoluteOffset;\n    }\n    // IANA time zone\n    if (isValidTimezoneIANAString(timezoneString)) {\n        date = new Date(date || Date.now());\n        const utcDate = isUtcDate ? date : toUtcDate(date);\n        const offset = calcOffset(utcDate, timezoneString);\n        const fixedOffset = isUtcDate ? offset : fixOffset(date, offset, timezoneString);\n        return -fixedOffset;\n    }\n    return NaN;\n}\nfunction toUtcDate(date) {\n    return newDateUTC(date.getFullYear(), date.getMonth(), date.getDate(), date.getHours(), date.getMinutes(), date.getSeconds(), date.getMilliseconds());\n}\nfunction calcOffset(date, timezoneString) {\n    const tokens = tzTokenizeDate(date, timezoneString);\n    // ms dropped because it's not provided by tzTokenizeDate\n    const asUTC = newDateUTC(tokens[0], tokens[1] - 1, tokens[2], tokens[3] % 24, tokens[4], tokens[5], 0).getTime();\n    let asTS = date.getTime();\n    const over = asTS % 1000;\n    asTS -= over >= 0 ? over : 1000 + over;\n    return asUTC - asTS;\n}\nfunction fixOffset(date, offset, timezoneString) {\n    const localTS = date.getTime();\n    // Our UTC time is just a guess because our offset is just a guess\n    let utcGuess = localTS - offset;\n    // Test whether the zone matches the offset for this ts\n    const o2 = calcOffset(new Date(utcGuess), timezoneString);\n    // If so, offset didn't change, and we're done\n    if (offset === o2) {\n        return offset;\n    }\n    // If not, change the ts by the difference in the offset\n    utcGuess -= o2 - offset;\n    // If that gives us the local time we want, we're done\n    const o3 = calcOffset(new Date(utcGuess), timezoneString);\n    if (o2 === o3) {\n        return o2;\n    }\n    // If it's different, we're in a hole time. The offset has changed, but we don't adjust the time\n    return Math.max(o2, o3);\n}\nfunction validateTimezone(hours, minutes) {\n    return -23 <= hours && hours <= 23 && (minutes == null || (0 <= minutes && minutes <= 59));\n}\nconst validIANATimezoneCache = {};\nfunction isValidTimezoneIANAString(timeZoneString) {\n    if (validIANATimezoneCache[timeZoneString])\n        return true;\n    try {\n        new Intl.DateTimeFormat(undefined, { timeZone: timeZoneString });\n        validIANATimezoneCache[timeZoneString] = true;\n        return true;\n    }\n    catch (error) {\n        return false;\n    }\n}\n"], "mappings": "AAAA,SAASA,cAAc,QAAQ,4BAA4B;AAC3D,SAASC,UAAU,QAAQ,wBAAwB;AACnD,MAAMC,oBAAoB,GAAG,OAAO;AACpC,MAAMC,sBAAsB,GAAG,KAAK;AACpC,MAAMC,QAAQ,GAAG;EACbC,QAAQ,EAAE,YAAY;EACtBC,SAAS,EAAE,OAAO;EAClBC,UAAU,EAAE,eAAe;EAC3BC,YAAY,EAAE;AAClB,CAAC;AACD;AACA,OAAO,SAASC,eAAeA,CAACC,cAAc,EAAEC,IAAI,EAAEC,SAAS,EAAE;EAC7D;EACA,IAAI,CAACF,cAAc,EAAE;IACjB,OAAO,CAAC;EACZ;EACA;EACA,IAAIG,KAAK,GAAGT,QAAQ,CAACE,SAAS,CAACQ,IAAI,CAACJ,cAAc,CAAC;EACnD,IAAIG,KAAK,EAAE;IACP,OAAO,CAAC;EACZ;EACA,IAAIE,KAAK;EACT,IAAIC,cAAc;EAClB;EACAH,KAAK,GAAGT,QAAQ,CAACG,UAAU,CAACO,IAAI,CAACJ,cAAc,CAAC;EAChD,IAAIG,KAAK,EAAE;IACPE,KAAK,GAAGE,QAAQ,CAACJ,KAAK,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;IAC9B,IAAI,CAACK,gBAAgB,CAACH,KAAK,CAAC,EAAE;MAC1B,OAAOI,GAAG;IACd;IACA,OAAO,EAAEJ,KAAK,GAAGb,oBAAoB,CAAC;EAC1C;EACA;EACAW,KAAK,GAAGT,QAAQ,CAACI,YAAY,CAACM,IAAI,CAACJ,cAAc,CAAC;EAClD,IAAIG,KAAK,EAAE;IACPE,KAAK,GAAGE,QAAQ,CAACJ,KAAK,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;IAC9B,MAAMO,OAAO,GAAGH,QAAQ,CAACJ,KAAK,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;IACtC,IAAI,CAACK,gBAAgB,CAACH,KAAK,EAAEK,OAAO,CAAC,EAAE;MACnC,OAAOD,GAAG;IACd;IACAH,cAAc,GAAGK,IAAI,CAACC,GAAG,CAACP,KAAK,CAAC,GAAGb,oBAAoB,GAAGkB,OAAO,GAAGjB,sBAAsB;IAC1F,OAAOU,KAAK,CAAC,CAAC,CAAC,KAAK,GAAG,GAAG,CAACG,cAAc,GAAGA,cAAc;EAC9D;EACA;EACA,IAAIO,yBAAyB,CAACb,cAAc,CAAC,EAAE;IAC3CC,IAAI,GAAG,IAAIa,IAAI,CAACb,IAAI,IAAIa,IAAI,CAACC,GAAG,CAAC,CAAC,CAAC;IACnC,MAAMC,OAAO,GAAGd,SAAS,GAAGD,IAAI,GAAGgB,SAAS,CAAChB,IAAI,CAAC;IAClD,MAAMiB,MAAM,GAAGC,UAAU,CAACH,OAAO,EAAEhB,cAAc,CAAC;IAClD,MAAMoB,WAAW,GAAGlB,SAAS,GAAGgB,MAAM,GAAGG,SAAS,CAACpB,IAAI,EAAEiB,MAAM,EAAElB,cAAc,CAAC;IAChF,OAAO,CAACoB,WAAW;EACvB;EACA,OAAOX,GAAG;AACd;AACA,SAASQ,SAASA,CAAChB,IAAI,EAAE;EACrB,OAAOV,UAAU,CAACU,IAAI,CAACqB,WAAW,CAAC,CAAC,EAAErB,IAAI,CAACsB,QAAQ,CAAC,CAAC,EAAEtB,IAAI,CAACuB,OAAO,CAAC,CAAC,EAAEvB,IAAI,CAACwB,QAAQ,CAAC,CAAC,EAAExB,IAAI,CAACyB,UAAU,CAAC,CAAC,EAAEzB,IAAI,CAAC0B,UAAU,CAAC,CAAC,EAAE1B,IAAI,CAAC2B,eAAe,CAAC,CAAC,CAAC;AACzJ;AACA,SAAST,UAAUA,CAAClB,IAAI,EAAED,cAAc,EAAE;EACtC,MAAM6B,MAAM,GAAGvC,cAAc,CAACW,IAAI,EAAED,cAAc,CAAC;EACnD;EACA,MAAM8B,KAAK,GAAGvC,UAAU,CAACsC,MAAM,CAAC,CAAC,CAAC,EAAEA,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,EAAEA,MAAM,CAAC,CAAC,CAAC,EAAEA,MAAM,CAAC,CAAC,CAAC,GAAG,EAAE,EAAEA,MAAM,CAAC,CAAC,CAAC,EAAEA,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAACE,OAAO,CAAC,CAAC;EAChH,IAAIC,IAAI,GAAG/B,IAAI,CAAC8B,OAAO,CAAC,CAAC;EACzB,MAAME,IAAI,GAAGD,IAAI,GAAG,IAAI;EACxBA,IAAI,IAAIC,IAAI,IAAI,CAAC,GAAGA,IAAI,GAAG,IAAI,GAAGA,IAAI;EACtC,OAAOH,KAAK,GAAGE,IAAI;AACvB;AACA,SAASX,SAASA,CAACpB,IAAI,EAAEiB,MAAM,EAAElB,cAAc,EAAE;EAC7C,MAAMkC,OAAO,GAAGjC,IAAI,CAAC8B,OAAO,CAAC,CAAC;EAC9B;EACA,IAAII,QAAQ,GAAGD,OAAO,GAAGhB,MAAM;EAC/B;EACA,MAAMkB,EAAE,GAAGjB,UAAU,CAAC,IAAIL,IAAI,CAACqB,QAAQ,CAAC,EAAEnC,cAAc,CAAC;EACzD;EACA,IAAIkB,MAAM,KAAKkB,EAAE,EAAE;IACf,OAAOlB,MAAM;EACjB;EACA;EACAiB,QAAQ,IAAIC,EAAE,GAAGlB,MAAM;EACvB;EACA,MAAMmB,EAAE,GAAGlB,UAAU,CAAC,IAAIL,IAAI,CAACqB,QAAQ,CAAC,EAAEnC,cAAc,CAAC;EACzD,IAAIoC,EAAE,KAAKC,EAAE,EAAE;IACX,OAAOD,EAAE;EACb;EACA;EACA,OAAOzB,IAAI,CAAC2B,GAAG,CAACF,EAAE,EAAEC,EAAE,CAAC;AAC3B;AACA,SAAS7B,gBAAgBA,CAACH,KAAK,EAAEK,OAAO,EAAE;EACtC,OAAO,CAAC,EAAE,IAAIL,KAAK,IAAIA,KAAK,IAAI,EAAE,KAAKK,OAAO,IAAI,IAAI,IAAK,CAAC,IAAIA,OAAO,IAAIA,OAAO,IAAI,EAAG,CAAC;AAC9F;AACA,MAAM6B,sBAAsB,GAAG,CAAC,CAAC;AACjC,SAAS1B,yBAAyBA,CAAC2B,cAAc,EAAE;EAC/C,IAAID,sBAAsB,CAACC,cAAc,CAAC,EACtC,OAAO,IAAI;EACf,IAAI;IACA,IAAIC,IAAI,CAACC,cAAc,CAACC,SAAS,EAAE;MAAEC,QAAQ,EAAEJ;IAAe,CAAC,CAAC;IAChED,sBAAsB,CAACC,cAAc,CAAC,GAAG,IAAI;IAC7C,OAAO,IAAI;EACf,CAAC,CACD,OAAOK,KAAK,EAAE;IACV,OAAO,KAAK;EAChB;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
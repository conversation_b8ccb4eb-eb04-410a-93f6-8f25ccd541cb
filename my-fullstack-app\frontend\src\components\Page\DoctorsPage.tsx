import { formatUtcToTaipei } from "../../utils/dateUtils";
import { Button } from 'primereact/button';
import { Calendar } from 'primereact/calendar';
import { Column } from 'primereact/column';
import { ConfirmDialog, confirmDialog } from 'primereact/confirmdialog';
import { DataTable } from 'primereact/datatable';
import { InputText } from 'primereact/inputtext';
import { Toast } from 'primereact/toast';
import React, { useRef, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import api from '../../services/api';
import useUser from '../../hooks/useUser';
import LoadingSpinner from '../Common/LoadingSpinner';
import { Card } from 'primereact/card';

interface Doctor {
  userId: number;
  userName: string;
  userAccount: string;
  userEmail: string;
  userPhone: string;
  isEnabled: boolean;
  roleId: number;
  roleName: string;
  createdAt: string;
  updatedAt: string;
  operatorUserName: string;
}

const DoctorsPage: React.FC = () => {
  const [searchParams, setSearchParams] = useState({
    name: '',
    startTime: null as Date | null,
    endTime: null as Date | null,
  });

  const navigate = useNavigate();
  const toast = useRef<Toast>(null);
  const { userRole: doctors, Roleloading: loading, refetch } = useUser(3); // 假設 2 是治療師角色ID

  const handleSearch = () => {
    // 實現搜索邏輯
    console.log('搜索參數:', searchParams);
  };

  // 刪除用戶功能
  const handleDeleteUser = (doctor: Doctor) => {
    confirmDialog({
      message: `確認是否刪除治療師 ${doctor.userName}？此操作無法復原`,
      header: '刪除確認',
      icon: 'pi pi-exclamation-triangle',
      accept: async () => {
        try {
          const response = await api.delete(`/api/Users/<USER>/${doctor.userId}`);

          toast.current?.show({
            severity: 'success',
            summary: '刪除成功',
            detail: response.data.message || '治療師已成功刪除'
          });

          // 重新載入數據
          refetch();
        } catch (error: any) {
          var detail =  error.status === 403 ? "您無權限，請通知管理員" : error.response?.data?.message || '刪除失敗';
          toast.current?.show({
            severity: 'error',
            summary: '刪除失敗',
            detail: detail
          });
          
        }
      }
    });
  };



  // 密碼重置功能
  const handleResetPassword = (doctor: Doctor) => {
    confirmDialog({
      message: `確認是否重置 ${doctor.userName} 的密碼？重置後密碼將變為 123456`,
      header: '密碼重置確認',
      icon: 'pi pi-exclamation-triangle',
      accept: async () => {
        try {
          const response = await api.post('/api/Users/<USER>', {
            userId: doctor.userId
          });

          toast.current?.show({
            severity: 'success',
            summary: '重置成功',
            detail: response.data.message || '密碼已重置為 123456'
          });
        } catch (error: any) {
          const message = error.status === 403 ? "無操作權限，請洽管理員" : '密碼重置失敗，請稍後再試';
          toast.current?.show({
            severity: 'error',
            summary: '重置失敗',
            detail: message
          });
        }
      }
    });
  };

  const formatDate = (value: string) => {
    if (!value) return '';
    return formatUtcToTaipei(value, "yyyy/MM/dd HH:mm:ss");
  };

  // 編輯治療師
  const handleEditDoctor = (doctor: Doctor) => {
    navigate('/doctordetail', { state: { doctor, isEdit: true } });
  };

  // 新增治療師
  const handleAddDoctor = () => {
    navigate('/doctordetail', { state: { isEdit: true } });
  };

  const paginatorLeft = (
          <Button
              type="button"
              icon="pi pi-refresh"
              text
              onClick={() => refetch()}
          />
      );
  const paginatorRight = <div></div>;

  if (loading) {
    return <LoadingSpinner message="載入治療師資料中..." />;
  }

  return (
    <div className="doctors-page">
      <Toast ref={toast} />
      <ConfirmDialog />

      <Card title="治療師管理" className="mb-4">
        <p className="text-600 line-height-3 m-0">
          管理治療師的個人資料和權限，包括新增、編輯和刪除治療師。您可以設定治療師的姓名、帳號、Email、電話和狀態。
        </p>
      </Card>

      {/* 搜尋條件 */}
      <Card className="mb-4">
        <div className="grid">
          <div className="col-12 md:col-4">
            
              <InputText
                id="doctorName"
                value={searchParams.name}
                onChange={(e) => setSearchParams(prev => ({ ...prev, name: e.target.value }))}
                placeholder="治療師姓名"
                className="w-full"
              />
            
          </div>
          
          <div className="col-6 md:col-3">
            
              <Calendar
                id="startTime"
                value={searchParams.startTime}
                onChange={(e) => setSearchParams(prev => ({ ...prev, startTime: e.value as Date }))}
                dateFormat="yy-mm-dd"
                showIcon
                className="w-full"
                placeholder="開始時間"
              />
            
          </div>

          <div className="col-6 md:col-3">
              <Calendar
                id="endTime"
                value={searchParams.endTime}
                onChange={(e) => setSearchParams(prev => ({ ...prev, endTime: e.value as Date }))}
                dateFormat="yy-mm-dd"
                showIcon
                className="w-full"
                placeholder="結束時間"
              />
            
          </div>

          <div className="col-12 md:col-4">
            <div className="flex gap-2">
              <Button
                label="查詢"
                icon="pi pi-search"
                onClick={handleSearch}
              />
              <Button
                label="新增"
                icon="pi pi-plus"
                onClick={handleAddDoctor}
              />
            </div>
          </div>
        </div>
      </Card>

      {/* 治療師列表 */}
      <Card>
        <DataTable
          value={doctors}
          paginator
          rows={10}
          rowsPerPageOptions={[10, 20, 30, 40]}
          emptyMessage="沒有找到治療師資料"
          tableStyle={{ minWidth: '50rem' }}
          paginatorLeft={paginatorLeft}
          paginatorRight={paginatorRight}
          
        >
          <Column field="userId" header="ID"  />
          <Column field="userName" header="治療師姓名"  />
          <Column field="userAccount" header="帳號"  />
          <Column field="userEmail" header="Email"  />
          <Column field="userPhone" header="電話"  />
          <Column
            field="isEnabled"
            header="狀態"
            
            body={(rowData: Doctor) => (
              <span className={`p-tag ${rowData.isEnabled ? 'p-tag-success' : 'p-tag-danger'}`}>
                {rowData.isEnabled ? '啟用' : '停用'}
              </span>
            )}
          />
          <Column field="roleName" header="角色"  />
          <Column field="createdAt" header="新增日期" style={{ width: '12%' }} body={(rowData) => formatDate(rowData.createdAt)} />
          <Column field="updatedAt" header="更新日期" style={{ width: '12%' }} body={(rowData) => formatDate(rowData.updatedAt)}/>
          <Column field="operatorUserName" header="操作人" style={{ width: '8%' }} />
          <Column
            header="操作"
            style={{ width: '20%' }}
            body={(rowData: Doctor) => (
              <div className="flex gap-2">
                <Button
                  icon="pi pi-pencil"
                  label="編輯"
                  size="small" 
                  className="p-button-success"
                  onClick={() => handleEditDoctor(rowData)}
                />
                <Button
                  icon="pi pi-key"
                  label="重置密碼"
                  size="small" 
                  className="p-button-warning"
                  onClick={() => handleResetPassword(rowData)}
                />
                <Button
                  icon="pi pi-trash"
                  label="刪除"
                  size="small" 
                  className="p-button-danger"
                  onClick={() => handleDeleteUser(rowData)}
                />
              </div>
            )}
          />
        </DataTable>
      </Card>
    </div>
  );
};

export default DoctorsPage;
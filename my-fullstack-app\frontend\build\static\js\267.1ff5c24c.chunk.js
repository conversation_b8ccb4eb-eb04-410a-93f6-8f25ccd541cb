"use strict";(self.webpackChunkfrontend=self.webpackChunkfrontend||[]).push([[267],{6104:(e,t,n)=>{n.d(t,{v:()=>f});var r=n(5043),s=n(4052),a=n(1828),i=n(2028),l=n(4504);function c(e){return c="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},c(e)}function o(e){var t=function(e,t){if("object"!=c(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!=c(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==c(t)?t:t+""}function u(e,t,n){return(t=o(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var d={value:"p-tag-value",icon:"p-tag-icon",root:function(e){var t=e.props;return(0,l.xW)("p-tag p-component",u(u({},"p-tag-".concat(t.severity),null!==t.severity),"p-tag-rounded",t.rounded))}},m=a.x.extend({defaultProps:{__TYPE:"Tag",value:null,severity:null,rounded:!1,icon:null,style:null,className:null,children:void 0},css:{classes:d,styles:"\n@layer primereact {\n    .p-tag {\n        display: inline-flex;\n        align-items: center;\n        justify-content: center;\n    }\n    \n    .p-tag-icon,\n    .p-tag-value,\n    .p-tag-icon.pi {\n        line-height: 1.5;\n    }\n    \n    .p-tag.p-tag-rounded {\n        border-radius: 10rem;\n    }\n}\n"}});function p(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}var f=r.forwardRef((function(e,t){var n=(0,i.qV)(),c=r.useContext(s.UM),o=m.getProps(e,c),d=m.setMetaData({props:o}),f=d.ptm,y=d.cx,b=d.isUnstyled;(0,a.j)(m.css.styles,b,{name:"tag"});var v=r.useRef(null),x=n({className:y("icon")},f("icon")),g=l.Hj.getJSXIcon(o.icon,function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?p(Object(n),!0).forEach((function(t){u(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):p(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}({},x),{props:o});r.useImperativeHandle(t,(function(){return{props:o,getElement:function(){return v.current}}}));var j=n({ref:v,className:(0,l.xW)(o.className,y("root")),style:o.style},m.getOtherProps(o),f("root")),h=n({className:y("value")},f("value"));return r.createElement("span",j,g,r.createElement("span",h,o.value),r.createElement("span",null,o.children))}));f.displayName="Tag"},8267:(e,t,n)=>{n.r(t),n.d(t,{default:()=>M});var r=n(5855),s=n(5043),a=n(1063),i=n(9642),l=n(8150),c=n(2018),o=n(828),u=n(6104),d=n(4052),m=n(1828),p=n(2028),f=n(2897),y=n(3104),b=n(6961),v=n(7555),x=n(4504);function g(){return g=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)({}).hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},g.apply(null,arguments)}function j(e){return j="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},j(e)}function h(e){var t=function(e,t){if("object"!=j(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!=j(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==j(t)?t:t+""}function N(e,t,n){return(t=h(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var w=m.x.extend({defaultProps:{__TYPE:"Message",id:null,className:null,style:null,text:null,icon:null,severity:"info",content:null,children:void 0},css:{classes:{root:function(e){var t=e.props.severity;return(0,x.xW)("p-inline-message p-component",N({},"p-inline-message-".concat(t),t))},icon:"p-inline-message-icon",text:"p-inline-message-text"},styles:"\n        @layer primereact {\n            .p-inline-message {\n                display: inline-flex;\n                align-items: center;\n                justify-content: center;\n                vertical-align: top;\n            }\n\n            .p-inline-message-icon {\n                flex-shrink: 0;\n            }\n            \n            .p-inline-message-icon-only .p-inline-message-text {\n                visibility: hidden;\n                width: 0;\n            }\n            \n            .p-fluid .p-inline-message {\n                display: flex;\n            }        \n        }\n        "}});function O(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}var P=s.memo(s.forwardRef((function(e,t){var n=(0,p.qV)(),r=s.useContext(d.UM),a=w.getProps(e,r),i=s.useRef(null),l=w.setMetaData({props:a}),c=l.ptm,o=l.cx,u=l.isUnstyled;(0,m.j)(w.css.styles,u,{name:"message"});s.useImperativeHandle(t,(function(){return{props:a,getElement:function(){return i.current}}}));var j=function(){if(a.content)return x.BF.getJSXElement(a.content,a);var e=x.BF.getJSXElement(a.text,a),t=n({className:o("icon")},c("icon")),r=a.icon;if(!r)switch(a.severity){case"info":r=s.createElement(b.e,t);break;case"warn":r=s.createElement(y.P,t);break;case"error":r=s.createElement(v.I,t);break;case"success":r=s.createElement(f.S,t)}var i=x.Hj.getJSXIcon(r,function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?O(Object(n),!0).forEach((function(t){N(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):O(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}({},t),{props:a}),l=n({className:o("text")},c("text"));return s.createElement(s.Fragment,null,i,s.createElement("span",l,e))}(),h=n({className:(0,x.xW)(a.className,o("root")),style:a.style,role:"alert","aria-live":"polite","aria-atomic":"true"},w.getOtherProps(a),c("root"));return s.createElement("div",g({id:a.id,ref:i},h),j)})));P.displayName="Message";var k=n(3740),S=n(402),E=n(8018),B=n(579);const M=()=>{const[e,t]=(0,s.useState)([]),[n,d]=(0,s.useState)(null),[m,p]=(0,s.useState)(!0),[f,y]=(0,s.useState)(!1),b=(0,s.useRef)(null),v=async()=>{try{y(!0),E.Rm.api("\u8f09\u5165\u5099\u4efd\u8cc7\u6599");const[e,n]=await Promise.all([S.A.get("/api/backup/list"),S.A.get("/api/backup/status")]);t(e.data),d(n.data),E.Rm.api("\u5099\u4efd\u8cc7\u6599\u8f09\u5165\u6210\u529f",{filesCount:e.data.length,hasTodayBackup:n.data.hasTodayBackup})}catch(a){var e,n,r;E.Rm.error("\u8f09\u5165\u5099\u4efd\u8cc7\u6599\u5931\u6557",a);var s=403===a.status?"\u60a8\u6c92\u6709\u6b0a\u9650\u67e5\u770b\u5099\u4efd\u8cc7\u6599":(null===(e=a.response)||void 0===e||null===(n=e.data)||void 0===n?void 0:n.message)||"\u8f09\u5165\u5931\u6557";null===(r=b.current)||void 0===r||r.show({severity:"error",summary:"\u8f09\u5165\u5931\u6557",detail:s,life:5e3})}finally{p(!1),y(!1)}},x=e=>e?(0,r.$)(e,"yyyy/MM/dd HH:mm:ss"):"",g=()=>n?(0,B.jsx)("div",{className:"mb-4",children:n.hasTodayBackup?(0,B.jsx)(P,{severity:"success",text:"\u4eca\u65e5\u5099\u4efd\u5b8c\u6210 - ".concat(n.todayBackupFile),className:"w-full"}):(0,B.jsx)(P,{severity:"error",text:"\u4eca\u65e5\u5c1a\u672a\u5099\u4efd",className:"w-full"})}):null,j=()=>n?(0,B.jsxs)("div",{className:"grid mb-4",children:[(0,B.jsx)("div",{className:"col-12 md:col-4",children:(0,B.jsx)("div",{className:"surface-card p-3 border-round",children:(0,B.jsxs)("div",{className:"flex justify-content-between align-items-start",children:[(0,B.jsxs)("div",{children:[(0,B.jsx)("span",{className:"block text-500 font-medium mb-1",children:"\u7e3d\u5099\u4efd\u6a94\u6848"}),(0,B.jsx)("div",{className:"text-900 font-medium text-xl",children:n.totalBackupFiles})]}),(0,B.jsx)("div",{className:"flex align-items-center justify-content-center bg-blue-100 border-round",style:{width:"2.5rem",height:"2.5rem"},children:(0,B.jsx)("i",{className:"pi pi-file text-blue-500 text-xl"})})]})})}),(0,B.jsx)("div",{className:"col-12 md:col-4",children:(0,B.jsx)("div",{className:"surface-card p-3 border-round",children:(0,B.jsxs)("div",{className:"flex justify-content-between align-items-start",children:[(0,B.jsxs)("div",{children:[(0,B.jsx)("span",{className:"block text-500 font-medium mb-1",children:"\u4eca\u65e5\u5099\u4efd\u72c0\u614b"}),(0,B.jsx)("div",{className:"text-900 font-medium text-xl",children:(0,B.jsx)(u.v,{value:n.hasTodayBackup?"\u5df2\u5b8c\u6210":"\u672a\u5b8c\u6210",severity:n.hasTodayBackup?"success":"danger"})})]}),(0,B.jsx)("div",{className:"flex align-items-center justify-content-center bg-green-100 border-round",style:{width:"2.5rem",height:"2.5rem"},children:(0,B.jsx)("i",{className:"pi pi-check-circle text-green-500 text-xl"})})]})})}),(0,B.jsx)("div",{className:"col-12 md:col-4",children:(0,B.jsx)("div",{className:"surface-card p-3 border-round",children:(0,B.jsxs)("div",{className:"flex justify-content-between align-items-start",children:[(0,B.jsxs)("div",{children:[(0,B.jsx)("span",{className:"block text-500 font-medium mb-1",children:"\u6700\u65b0\u5099\u4efd\u6642\u9593"}),(0,B.jsx)("div",{className:"text-900 font-medium text-sm",children:n.latestBackupTime?x(n.latestBackupTime):"\u7121"})]}),(0,B.jsx)("div",{className:"flex align-items-center justify-content-center bg-orange-100 border-round",style:{width:"2.5rem",height:"2.5rem"},children:(0,B.jsx)("i",{className:"pi pi-clock text-orange-500 text-xl"})})]})})})]}):null;return(0,s.useEffect)((()=>{v()}),[]),m?(0,B.jsx)("div",{className:"flex align-items-center justify-content-center min-h-screen",children:(0,B.jsxs)("div",{className:"text-center",children:[(0,B.jsx)(k.p,{}),(0,B.jsx)("p",{className:"mt-3",children:"\u8f09\u5165\u5099\u4efd\u8cc7\u6599\u4e2d..."})]})}):(0,B.jsxs)("div",{className:"backup-page",children:[(0,B.jsx)(o.y,{ref:b}),(0,B.jsx)("div",{className:"grid",children:(0,B.jsx)("div",{className:"col-12",children:(0,B.jsxs)(l.Z,{title:"\u8cc7\u6599\u5eab\u5099\u4efd\u7ba1\u7406",className:"mb-4",children:[(0,B.jsxs)("div",{className:"flex justify-content-between align-items-center mb-4",children:[(0,B.jsx)("h5",{className:"m-0",children:"\u5099\u4efd\u6a94\u6848\u6e05\u55ae"}),(0,B.jsx)(c.$,{label:"\u91cd\u65b0\u6574\u7406",icon:f?"pi pi-spin pi-spinner":"pi pi-refresh",onClick:v,disabled:f,className:"p-button-outlined"})]}),(0,B.jsx)(g,{}),(0,B.jsx)(j,{}),(0,B.jsxs)(a.b,{value:e,paginator:!0,rows:10,rowsPerPageOptions:[5,10,25,50],sortMode:"multiple",removableSort:!0,filterDisplay:"menu",globalFilterFields:["fileName"],emptyMessage:"\u6c92\u6709\u627e\u5230\u5099\u4efd\u6a94\u6848",className:"p-datatable-gridlines",children:[(0,B.jsx)(i.V,{field:"fileName",header:"\u6a94\u6848\u540d\u7a31",sortable:!0,filter:!0,filterPlaceholder:"\u641c\u5c0b\u6a94\u6848\u540d\u7a31",body:e=>(0,B.jsx)(c.$,{label:e.fileName,className:"p-button-link p-0",onClick:()=>(async e=>{try{var t;E.Rm.api("\u958b\u59cb\u4e0b\u8f09\u5099\u4efd\u6a94\u6848",{fileName:e});const n=await S.A.get("/api/backup/download?file=".concat(encodeURIComponent(e)),{responseType:"blob"}),r=window.URL.createObjectURL(new Blob([n.data])),s=document.createElement("a");s.href=r,s.setAttribute("download",e),document.body.appendChild(s),s.click(),s.remove(),window.URL.revokeObjectURL(r),null===(t=b.current)||void 0===t||t.show({severity:"success",summary:"\u4e0b\u8f09\u6210\u529f",detail:"\u6a94\u6848 ".concat(e," \u4e0b\u8f09\u5b8c\u6210"),life:3e3}),E.Rm.api("\u5099\u4efd\u6a94\u6848\u4e0b\u8f09\u6210\u529f",{fileName:e})}catch(r){var n;E.Rm.error("\u4e0b\u8f09\u5099\u4efd\u6a94\u6848\u5931\u6557",r),null===(n=b.current)||void 0===n||n.show({severity:"error",summary:"\u4e0b\u8f09\u5931\u6557",detail:"\u7121\u6cd5\u4e0b\u8f09\u6a94\u6848 ".concat(e),life:5e3})}})(e.fileName),tooltip:"\u9ede\u64ca\u4e0b\u8f09\u6a94\u6848"}),style:{minWidth:"300px"}}),(0,B.jsx)(i.V,{field:"sizeMB",header:"\u6a94\u6848\u5927\u5c0f",sortable:!0,body:e=>{return(t=e.sizeMB)<1?"".concat(Math.round(1024*t)," KB"):"".concat(t.toFixed(2)," MB");var t},style:{minWidth:"120px"}}),(0,B.jsx)(i.V,{field:"lastModified",header:"\u6700\u5f8c\u4fee\u6539\u6642\u9593",sortable:!0,body:e=>x(e.lastModified),style:{minWidth:"200px"}})]})]})})})]})}}}]);
//# sourceMappingURL=267.1ff5c24c.chunk.js.map
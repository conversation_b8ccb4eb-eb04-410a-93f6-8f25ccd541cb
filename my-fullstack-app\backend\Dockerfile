FROM mcr.microsoft.com/dotnet/aspnet:8.0 AS base
WORKDIR /app
EXPOSE 5000

# 安裝必要套件：SkiaSharp、HarfBuzz 與中文字體
RUN apt-get update && apt-get install -y \
	fontconfig \
    libfontconfig1 \
    libfreetype6 \
    libharfbuzz0b \
    libpng16-16 \
    libglib2.0-0 \
    libx11-6 \
    fonts-noto-cjk \
    fonts-noto-core \
    fonts-droid-fallback \
    ghostscript \
    --no-install-recommends && \
    rm -rf /var/lib/apt/lists/*

FROM mcr.microsoft.com/dotnet/sdk:8.0 AS build
WORKDIR /src
COPY . .
RUN dotnet restore "./MyApi.csproj"
RUN dotnet build "./MyApi.csproj" -c Release -o /app/build

FROM build AS publish
RUN dotnet publish "./MyApi.csproj" -c Release -o /app/publish

FROM base AS final
WORKDIR /app
COPY --from=publish /app/publish .

COPY images/ ./images/ 

ENV ASPNETCORE_URLS=http://+:5000

ENTRYPOINT ["dotnet", "MyApi.dll"]
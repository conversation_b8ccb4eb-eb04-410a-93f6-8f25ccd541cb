{"version": 3, "file": "static/js/538.a7da00d4.chunk.js", "mappings": "0KAOA,SAASA,IACP,OAAOA,EAAWC,OAAOC,OAASD,OAAOC,OAAOC,OAAS,SAAUC,GACjE,IAAK,IAAIC,EAAI,EAAGA,EAAIC,UAAUC,OAAQF,IAAK,CACzC,IAAIG,EAAIF,UAAUD,GAClB,IAAK,IAAII,KAAKD,GAAG,CAAG,GAAEE,eAAeC,KAAKH,EAAGC,KAAOL,EAAEK,GAAKD,EAAEC,GAC/D,CACA,OAAOL,CACT,EAAGJ,EAASY,MAAM,KAAMN,UAC1B,CAEA,SAASO,EAAQC,GAGf,OAAOD,EAAU,mBAAqBE,QAAU,iBAAmBA,OAAOC,SAAW,SAAUF,GAC7F,cAAcA,CAChB,EAAI,SAAUA,GACZ,OAAOA,GAAK,mBAAqBC,QAAUD,EAAEG,cAAgBF,QAAUD,IAAMC,OAAOG,UAAY,gBAAkBJ,CACpH,EAAGD,EAAQC,EACb,CAaA,SAASK,EAAcX,GACrB,IAAIY,EAZN,SAAqBZ,EAAGC,GACtB,GAAI,UAAYI,EAAQL,KAAOA,EAAG,OAAOA,EACzC,IAAIH,EAAIG,EAAEO,OAAOM,aACjB,QAAI,IAAWhB,EAAG,CAChB,IAAIe,EAAIf,EAAEM,KAAKH,EAAGC,GAAK,WACvB,GAAI,UAAYI,EAAQO,GAAI,OAAOA,EACnC,MAAM,IAAIE,UAAU,+CACtB,CACA,OAAQ,WAAab,EAAIc,OAASC,QAAQhB,EAC5C,CAGUa,CAAYb,EAAG,UACvB,MAAO,UAAYK,EAAQO,GAAKA,EAAIA,EAAI,EAC1C,CAEA,SAASK,EAAgBpB,EAAGI,EAAGD,GAC7B,OAAQC,EAAIU,EAAcV,MAAOJ,EAAIJ,OAAOyB,eAAerB,EAAGI,EAAG,CAC/DkB,MAAOnB,EACPoB,YAAY,EACZC,cAAc,EACdC,UAAU,IACPzB,EAAEI,GAAKD,EAAGH,CACjB,CAEA,IAAI0B,EAAU,CACZC,KAAM,SAAcC,GAElB,MAAsB,kBADVA,EAAKC,MACJC,MAA2BC,EAAAA,EAAAA,IAAW,0DAA2DA,EAAAA,EAAAA,IAAW,sDAC3H,EACAT,MAAO,kDACPU,MAAO,sBACPC,UAAW,yCAGTC,EAAe,CACjBZ,MAAO,SAAea,GACpB,IAAIN,EAAQM,EAAMN,MACdO,EAAaC,KAAKC,IAAIT,EAAMP,MAAO,GACnCiB,EAAaV,EAAMP,MAAQO,EAAMW,MAAQ,cAC7C,MAAsB,kBAAfX,EAAMC,KAA2B,CACtCW,gBAAiBZ,EAAMW,OACrB,CACFE,MAAON,EAAa,IACpBO,QAAS,OACTF,gBAAiBF,EAErB,GAEEK,EAAkBC,EAAAA,EAAcC,OAAO,CACzCC,aAAc,CACZC,OAAQ,cACRC,iBAAkB,KAClBC,GAAI,KACJ5B,MAAO,KACP6B,WAAW,EACXC,KAAM,IACNC,MAAO,KACPC,UAAW,KACXxB,KAAM,cACNyB,qBAAsB,KACtBf,MAAO,KACPgB,cAAUC,GAEZC,IAAK,CACHhC,QAASA,EACTiC,OAhCS,y1EAiCTzB,aAAcA,KAIlB,SAAS0B,EAAQ5D,EAAGI,GAAK,IAAID,EAAIP,OAAOiE,KAAK7D,GAAI,GAAIJ,OAAOkE,sBAAuB,CAAE,IAAIrD,EAAIb,OAAOkE,sBAAsB9D,GAAII,IAAMK,EAAIA,EAAEsD,QAAO,SAAU3D,GAAK,OAAOR,OAAOoE,yBAAyBhE,EAAGI,GAAGmB,UAAY,KAAKpB,EAAE8D,KAAK1D,MAAMJ,EAAGM,EAAI,CAAE,OAAON,CAAG,CAE9P,IAAI+D,EAA2BC,EAAAA,KAAwBA,EAAAA,YAAiB,SAAUC,EAASC,GACzF,IAAIC,GAAaC,EAAAA,EAAAA,MACbC,EAAUL,EAAAA,WAAiBM,EAAAA,IAC3B5C,EAAQe,EAAgB8B,SAASN,EAASI,GAC1CG,EAAwB/B,EAAgBgC,YAL9C,SAAuB5E,GAAK,IAAK,IAAII,EAAI,EAAGA,EAAIH,UAAUC,OAAQE,IAAK,CAAE,IAAID,EAAI,MAAQF,UAAUG,GAAKH,UAAUG,GAAK,CAAC,EAAGA,EAAI,EAAIwD,EAAQhE,OAAOO,IAAI,GAAI0E,SAAQ,SAAUzE,GAAKgB,EAAgBpB,EAAGI,EAAGD,EAAEC,GAAK,IAAKR,OAAOkF,0BAA4BlF,OAAOmF,iBAAiB/E,EAAGJ,OAAOkF,0BAA0B3E,IAAMyD,EAAQhE,OAAOO,IAAI0E,SAAQ,SAAUzE,GAAKR,OAAOyB,eAAerB,EAAGI,EAAGR,OAAOoE,yBAAyB7D,EAAGC,GAAK,GAAI,CAAE,OAAOJ,CAAG,CAK5XgF,CAAc,CAClEnD,MAAOA,GACNA,EAAMoB,mBACTgC,EAAMN,EAAsBM,IAC5BC,EAAKP,EAAsBO,GAC3BC,EAAaR,EAAsBQ,YACrCC,EAAAA,EAAAA,GAAexC,EAAgBc,IAAIC,OAAQwB,EAAY,CACrDE,KAAM,gBAER,IAAIC,EAAanB,EAAAA,OAAa,MAiE9B,GARAA,EAAAA,oBAA0BE,GAAK,WAC7B,MAAO,CACLxC,MAAOA,EACP0D,WAAY,WACV,OAAOD,EAAWE,OACpB,EAEJ,IACmB,gBAAf3D,EAAMC,KACR,OA1DsB,WACtB,IAAIE,EAPAH,EAAMsB,WAA4B,MAAftB,EAAMP,MACfO,EAAM0B,qBAAuB1B,EAAM0B,qBAAqB1B,EAAMP,OAASO,EAAMP,MAAQO,EAAMuB,KAGlG,KAIHqC,EAAYnB,EAAW,CACzBhB,WAAWvB,EAAAA,EAAAA,IAAWF,EAAMyB,UAAW4B,EAAG,SAC1C7B,MAAOxB,EAAMwB,MACbqC,KAAM,cACN,gBAAiB,IACjB,gBAAiB7D,EAAMP,MACvB,gBAAiB,OAChBsB,EAAgB+C,cAAc9D,GAAQoD,EAAI,SACzCW,EAAatB,EAAW,CAC1BhB,UAAW4B,EAAG,SACd7B,MAAO,CACLX,MAAOb,EAAMP,MAAQ,IACrBqB,QAAS,OACTF,gBAAiBZ,EAAMW,QAExByC,EAAI,UACHY,EAAavB,EAAW,CAC1BhB,UAAW4B,EAAG,UACbD,EAAI,UACP,OAAoBd,EAAAA,cAAoB,MAAOxE,EAAS,CACtDuD,GAAIrB,EAAMqB,GACVmB,IAAKiB,GACJG,GAAyBtB,EAAAA,cAAoB,MAAOyB,EAAqB,MAAT5D,GAA8BmC,EAAAA,cAAoB,MAAO0B,EAAY7D,IAC1I,CAiCS8D,GACF,GAAmB,kBAAfjE,EAAMC,KACf,OAlCwB,WACxB,IAAI2D,EAAYnB,EAAW,CACzBhB,WAAWvB,EAAAA,EAAAA,IAAWF,EAAMyB,UAAW4B,EAAG,SAC1C7B,MAAOxB,EAAMwB,MACbqC,KAAM,cACN,gBAAiB,IACjB,gBAAiB7D,EAAMP,MACvB,gBAAiB,OAChBsB,EAAgB+C,cAAc9D,GAAQoD,EAAI,SACzCc,EAAiBzB,EAAW,CAC9BhB,UAAW4B,EAAG,cACbD,EAAI,cACHW,EAAatB,EAAW,CAC1BhB,UAAW4B,EAAG,SACd7B,MAAO,CACLZ,gBAAiBZ,EAAMW,QAExByC,EAAI,UACP,OAAoBd,EAAAA,cAAoB,MAAOxE,EAAS,CACtDuD,GAAIrB,EAAMqB,GACVmB,IAAKiB,GACJG,GAAyBtB,EAAAA,cAAoB,MAAO4B,EAA6B5B,EAAAA,cAAoB,MAAOyB,IACjH,CAYSI,GAET,MAAM,IAAIC,MAAMpE,EAAMC,KAAO,+FAC/B,KACAoC,EAAYgC,YAAc,a,kCCvL1B,MAKA,GALmB,I,QAAIC,IACpBC,QAAQC,wCACRC,yBACAC,O,iPCeH,MAqeA,EAre+BC,KAC3B,MAAMC,GAAWC,EAAAA,EAAAA,MACXC,GAAQC,EAAAA,EAAAA,QAAc,OACrBvB,EAAMwB,IAAWC,EAAAA,EAAAA,UAAS,KAC1BC,EAAYC,IAAiBF,EAAAA,EAAAA,UAAS,KACtCG,EAAWC,IAAgBJ,EAAAA,EAAAA,eAAkCrD,IAC7D0D,EAASC,IAAcN,EAAAA,EAAAA,eAAkCrD,IACzD4D,EAAYC,IAAiBR,EAAAA,EAAAA,UAAS,IAEtCS,EAAcC,IAAmBV,EAAAA,EAAAA,UAAS,CAC7CzB,KAAM,GACN0B,WAAY,GACZE,UAAW,KACXE,QAAS,KACTE,WAAY,KAITI,EAAkBC,IAAuBZ,EAAAA,EAAAA,UAAgB,KACzDa,EAAkBC,IAAuBd,EAAAA,EAAAA,WAAS,IAClDe,EAAWC,IAAgBhB,EAAAA,EAAAA,UAAS,IACpCiB,EAAgBC,IAAqBlB,EAAAA,EAAAA,UAAS,IAC9CmB,EAAaC,IAAkBpB,EAAAA,EAAAA,WAAS,IAGxCqB,EAAiBC,IAAsBtB,EAAAA,EAAAA,WAAS,IAChDuB,EAAyBC,IAA8BxB,EAAAA,EAAAA,UAAc,OACrEyB,EAAgBC,IAAqB1B,EAAAA,EAAAA,UAAS,KAC9C2B,EAAgBC,IAAqB5B,EAAAA,EAAAA,WAAS,IAE/C,SAAE6B,EAAQ,QAAEC,GCnCP,SACbC,EACA9B,EACA+B,EACAC,EACA1B,GAEA,MAAOsB,EAAUK,IAAelC,EAAAA,EAAAA,UAAwB,KACjD8B,EAASK,IAAcnC,EAAAA,EAAAA,WAAS,GAqBvC,OAnBAoC,EAAAA,EAAAA,YAAU,KACcC,WACpBF,GAAW,GACXG,EAAAA,EAAIC,IAAmB,uBAAwB,CAC7CC,OAAQ,CACNT,YAAaA,EACb9B,WAAYA,EACZE,UAAoB,OAAT6B,QAAS,IAATA,OAAS,EAATA,EAAWS,cACtBpC,QAAgB,OAAP4B,QAAO,IAAPA,OAAO,EAAPA,EAASQ,iBAGnBC,MAAMC,GAAQT,EAAYS,EAAIC,QAC9BC,OAAOC,GAAQC,QAAQC,MAAM,aAAcF,KAC3CG,SAAQ,IAAMd,GAAW,MAG9Be,KACC,CAACnB,EAAa9B,EAAY+B,EAAWC,EAAS1B,IAE1C,CAAEsB,WAAUC,UACrB,CDKkCqB,CAC1B1C,EAAalC,KACbkC,EAAaR,WACbQ,EAAaN,UACbM,EAAaJ,QACbE,GAWE6C,EAAwC,CAC1C,EAAK,eACL,EAAK,eACL,EAAK,iBAIThB,EAAAA,EAAAA,YAAU,KAENiB,EAAAA,EACCC,QACAZ,MAAK,KACNK,QAAQQ,IAAI,uCAGXV,OAAMC,GAAOC,QAAQC,MAAM,oCAAiBF,KAE7CO,EAAAA,EAAWG,GAAG,kBAAmBhJ,IACjC0G,EAAkB1G,MAGlB6I,EAAAA,EAAWG,GAAG,kBAAmBC,IACjCvC,EAAkBuC,MAGX,KACPJ,EAAAA,EAAWK,UAEZ,IAEH,MAwIMC,IACFC,EAAAA,EAAAA,KAACC,EAAAA,EAAM,CACHC,KAAK,SACLC,KAAK,gBACLC,MAAI,EACJC,QAASA,KAtIbzD,GAAc0D,GAAQA,EAAO,OAyI3BC,IAAiBP,EAAAA,EAAAA,KAAA,UAqCjBQ,GAAc5J,GACfA,GACE6J,EAAAA,EAAAA,GAAkB7J,EAAO,uBADb,GAsBnB,OAAIsH,GACO8B,EAAAA,EAAAA,KAACU,EAAAA,EAAc,CAACC,QAAQ,mDAI/BC,EAAAA,EAAAA,MAAA,OAAA9H,SAAA,EACIkH,EAAAA,EAAAA,KAACa,EAAAA,EAAK,CAAClH,IAAKsC,KACZ+D,EAAAA,EAAAA,KAACc,EAAAA,EAAa,KACdd,EAAAA,EAAAA,KAACe,EAAAA,EAAI,CAACC,MAAM,2BAAOpI,UAAU,OAAME,UAC/BkH,EAAAA,EAAAA,KAAA,KAAGpH,UAAU,6BAA4BE,SAAC,sKAM9CkH,EAAAA,EAAAA,KAACe,EAAAA,EAAI,CAACnI,UAAU,OAAME,UAClB8H,EAAAA,EAAAA,MAAA,OAAKhI,UAAU,OAAME,SAAA,EACjBkH,EAAAA,EAAAA,KAAA,OAAKpH,UAAU,iBAAgBE,UAC3BkH,EAAAA,EAAAA,KAACiB,EAAAA,EAAS,CACNzI,GAAG,OACH0H,KAAK,OACLtJ,MAAO+D,EACPuG,SAAW5L,GAAM6G,EAAQ7G,EAAE6L,OAAOvK,OAClCgC,UAAU,SACVwI,YAAY,gCAEpBpB,EAAAA,EAAAA,KAAA,OAAKpH,UAAU,iBAAgBE,UAC3BkH,EAAAA,EAAAA,KAACiB,EAAAA,EAAS,CACNzI,GAAG,aACH0H,KAAK,OACLtJ,MAAOyF,EACP6E,SAAW5L,GAAMgH,EAAchH,EAAE6L,OAAOvK,OACxCgC,UAAU,SACVwI,YAAY,sCAGpBpB,EAAAA,EAAAA,KAAA,OAAKpH,UAAU,iBAAgBE,UAC3BkH,EAAAA,EAAAA,KAACqB,EAAAA,EAAQ,CACL7I,GAAG,YACH5B,MAAO2F,EACP2E,SAAW5L,GAAMkH,EAAalH,EAAEsB,OAChCwK,YAAY,2BACZxI,UAAU,SACV0I,WAAW,WACXC,UAAQ,OAEhBvB,EAAAA,EAAAA,KAAA,OAAKpH,UAAU,iBAAgBE,UAC3BkH,EAAAA,EAAAA,KAACqB,EAAAA,EAAQ,CACL7I,GAAG,UACH5B,MAAO6F,EACPyE,SAAW5L,GAAMoH,EAAWpH,EAAEsB,OAC9BwK,YAAY,2BACZxI,UAAU,SACV0I,WAAW,WACXC,UAAQ,OAEhBvB,EAAAA,EAAAA,KAAA,OAAKpH,UAAU,kBAAiBE,UAC5B8H,EAAAA,EAAAA,MAAA,OAAKhI,UAAU,aAAYE,SAAA,EACvBkH,EAAAA,EAAAA,KAACC,EAAAA,EAAM,CAAC3I,MAAM,eAAK6I,KAAK,eAAeE,QAtQrCmB,KACtB5E,EAAcD,EAAa,GAC3BG,EAAgB,CAAEnC,OAAM0B,aAAYE,YAAWE,UAASE,mBAqQpCqD,EAAAA,EAAAA,KAACC,EAAAA,EAAM,CACH3I,MAAM,2BACN6I,KAAK,iBACLE,QA/PFoB,KACc,IAADC,EAAH,IAA5B3E,EAAiBvH,OAQrB0H,GAAoB,GAPH,QAAbwE,EAAAzF,EAAMnB,eAAO,IAAA4G,GAAbA,EAAeC,KAAK,CAChBC,SAAU,QACVC,QAAS,eACTC,OAAQ,4DA2PQlJ,UAAU,mBACVmJ,SAAsC,IAA5BhF,EAAiBvH,oBAO/CwK,EAAAA,EAAAA,KAACe,EAAAA,EAAI,CAAAjI,UACD8H,EAAAA,EAAAA,MAACoB,EAAAA,EAAS,CACNpL,MAAOqH,EACPgE,UAAWlF,EACXmF,kBAAoB5M,GAAM0H,EAAoB1H,EAAEsB,OAChDuL,WAAS,EACTC,KAAM,GACNC,mBAAoB,CAAC,GAAI,GAAI,GAAI,IACjCC,aAAa,mDACbC,WAAY,CAAEC,SAAU,SACxBzC,cAAeA,GACfQ,eAAgBA,GAAezH,SAAA,EAE/BkH,EAAAA,EAAAA,KAACyC,EAAAA,EAAM,CAACC,cAAc,WAAWC,YAAa,CAAE3K,MAAO,SACvDgI,EAAAA,EAAAA,KAACyC,EAAAA,EAAM,CAACG,MAAM,UAAUC,OAAO,eAAKlK,MAAO,CAAEX,MAAO,SACpDgI,EAAAA,EAAAA,KAACyC,EAAAA,EAAM,CAACG,MAAM,iBAAiBC,OAAO,2BAAOlK,MAAO,CAAEX,MAAO,SAC7DgI,EAAAA,EAAAA,KAACyC,EAAAA,EAAM,CAACG,MAAM,cAAcC,OAAO,2BAAOlK,MAAO,CAAEX,MAAO,SAC1DgI,EAAAA,EAAAA,KAACyC,EAAAA,EAAM,CAACG,MAAM,gBAAgBC,OAAO,eAAKlK,MAAO,CAAEX,MAAO,MAAQ8K,KAzHtDC,IACxB,IAAI/D,EAAOxI,OAAOuM,EAAQC,eAC1B,MAAMC,EAASzD,EAAWR,GACtB,OACIgB,EAAAA,EAAAA,KAAA,OAAAlH,SACKmK,QAqHDjD,EAAAA,EAAAA,KAACyC,EAAAA,EAAM,CAACG,MAAM,mBAAmBC,OAAO,eAAKlK,MAAO,CAAEX,MAAO,MAAS8K,KAAOC,GA3G1EnM,KACf,IAAKA,EAAO,MAAO,GACnB,MAAMsM,EAAO,IAAIC,KAAKvM,GAChBwM,EAAQ,IAAID,KAClB,IAAIE,EAAMD,EAAME,cAAgBJ,EAAKI,cAUrC,OAPIF,EAAMG,WAAaL,EAAKK,YACvBH,EAAMG,aAAeL,EAAKK,YAAcH,EAAMI,UAAYN,EAAKM,YAGhEH,IAGGA,GA6F8FI,CAAUV,EAAQW,qBAC3G1D,EAAAA,EAAAA,KAACyC,EAAAA,EAAM,CAACG,MAAM,mBAAmBC,OAAO,2BAAOlK,MAAO,CAAEX,MAAO,MAAQ8K,KAAOC,GAAYvC,GAAWuC,EAAQY,qBAC7G3D,EAAAA,EAAAA,KAACyC,EAAAA,EAAM,CAACG,MAAM,mBAAmBC,OAAO,2BAAOlK,MAAO,CAAEX,MAAO,MAAQ8K,KAAOC,GAAYvC,GAAWuC,EAAQa,qBAC7G5D,EAAAA,EAAAA,KAACyC,EAAAA,EAAM,CAACG,MAAM,0BAA0BC,OAAO,qBAAMlK,MAAO,CAAEX,MAAO,SACrEgI,EAAAA,EAAAA,KAACyC,EAAAA,EAAM,CAACG,MAAM,SAASC,OAAO,eAAKlK,MAAO,CAAEX,MAAO,OAAS8K,KAxJhDC,IAEpBnC,EAAAA,EAAAA,MAAA,OAAKhI,UAAU,aAAYE,SAAA,EACnBkH,EAAAA,EAAAA,KAACC,EAAAA,EAAM,CACH3I,MAAM,eACN4I,KAAK,SACLC,KAAK,kBACLE,QAASA,IAAMtE,EAAS8H,EAAAA,GAAOC,eAAgB,CAAEC,MAAO,CAAEC,UAAWjB,KACrEkB,KAAK,QACLrC,SAAS,OACTjJ,MAAO,CAAEuL,SAAU,OAAQC,OAAQ,UAEvCnE,EAAAA,EAAAA,KAACC,EAAAA,EAAM,CACH3I,MAAM,eACN4I,KAAK,SACLC,KAAK,aACLE,QAASA,KAAM+D,OAjF/BxG,EADqByG,EAkF0BtB,GAhF/CjF,EAAkBuG,EAAQC,mBAC1B5G,GAAmB,GAHE2G,OAmFLJ,KAAK,QACLrC,SAAS,UACTjJ,MAAO,CAAEuL,SAAU,OAAQC,OAAQ,OACnCpC,UAAWgB,EAAQuB,0BAyI/B1D,EAAAA,EAAAA,MAAC2D,EAAAA,EAAM,CACH1B,OAAO,uCACP2B,QAASvH,EACTtE,MAAO,CAAEX,MAAO,QAChByM,OAAQA,IAAMvH,GAAoB,GAClCwH,OAAK,EAAA5L,SAAA,EAEL8H,EAAAA,EAAAA,MAAA,OAAKhI,UAAU,OAAME,SAAA,EACjB8H,EAAAA,EAAAA,MAAA,MAAA9H,SAAA,CAAI,yCAASiE,EAAiBvH,OAAO,eACrCoL,EAAAA,EAAAA,MAACoB,EAAAA,EAAS,CACNpL,MAAOmG,EACP4H,YAAU,EACVC,aAAa,QACbtC,aAAa,6CAASxJ,SAAA,EAEtBkH,EAAAA,EAAAA,KAACyC,EAAAA,EAAM,CAACG,MAAM,UAAUC,OAAO,eAAKlK,MAAO,CAAEX,MAAO,UACpDgI,EAAAA,EAAAA,KAACyC,EAAAA,EAAM,CAACG,MAAM,iBAAiBC,OAAO,2BAAOlK,MAAO,CAAEX,MAAO,UAC7DgI,EAAAA,EAAAA,KAACyC,EAAAA,EAAM,CAACG,MAAM,cAAcC,OAAO,2BAAOlK,MAAO,CAAEX,MAAO,UAC1DgI,EAAAA,EAAAA,KAACyC,EAAAA,EAAM,CAACG,MAAM,mBAAmBC,OAAO,2BAAOlK,MAAO,CAAEX,MAAO,OAAS8K,KAAOC,GAAYvC,GAAWuC,EAAQY,2BAItH/C,EAAAA,EAAAA,MAAA,OAAKhI,UAAU,uBAAsBE,SAAA,EACjC8H,EAAAA,EAAAA,MAAA,OAAKhI,UAAU,kBAAiBE,SAAA,EAC5BkH,EAAAA,EAAAA,KAAA,SAAO6E,QAAQ,YAAYjM,UAAU,uBAAsBE,SAAC,8BAC5DkH,EAAAA,EAAAA,KAAC8E,EAAAA,EAAQ,CACLtM,GAAG,YACH5B,MAAOuG,EACP4H,QA/WH,CACjB,CAAEzN,MAAO,eAAMV,MAAO,GACtB,CAAEU,MAAO,uCAAUV,MAAO,GAC1B,CAAEU,MAAO,iCAASV,MAAO,GACzB,CAAEU,MAAO,iCAASV,MAAO,IA4WLsK,SAAW5L,GAAM8H,EAAa9H,EAAEsB,OAChCwK,YAAY,uCACZxI,UAAU,eAGlBoH,EAAAA,EAAAA,KAAA,OAAKpH,UAAU,kBAAiBE,UAC5BkH,EAAAA,EAAAA,KAACC,EAAAA,EAAM,CACH3I,MAAM,eACN6I,KAAK,iBACLE,QA5TG5B,UACL,IAADuG,EAAjB,GAAKvF,EAAAA,EASL,IACIjC,GAAe,GACfF,EAAkB,GAElB,MAAM2H,EAAelI,EAAiBmI,KAAIb,GAAWA,EAAQ7L,KAGvD2M,EAAc,CAChBC,YAAaH,EACbI,QAJatI,EAAiBmI,KAAIb,GAAWA,EAAQiB,iBAKrDC,UAAWpI,GAGTqI,QAAiB9G,EAAAA,EAAI+G,KAAK,qCAAsCN,EAAa,CAC/EvG,OAAQ,CACJ8G,aAAcjG,EAAAA,EAAWiG,cAE7BC,aAAc,OACdC,QAAS,CACL,eAAgB,sBAKlBC,EAAO,IAAIC,KAAK,CAACN,EAASxG,MAAO,CAAEkB,KAAM,oBACzC6F,EAAUC,IAAIC,gBAAgBJ,GAGpCK,OAAOC,KAAKJ,GAEZ7I,GAAoB,GACpBF,EAAoB,GAExB,CAAE,MAAOoC,GAAa,IAADgH,EAAAC,EACjB7I,GAAe,GACfF,EAAkB,GACL,QAAb8I,EAAAnK,EAAMnB,eAAO,IAAAsL,GAAbA,EAAezE,KAAK,CAChBC,SAAU,QACVC,QAAS,eACTC,QAAsB,QAAduE,EAAAjH,EAAMoG,gBAAQ,IAAAa,OAAA,EAAdA,EAAgBrH,OAAQ,4BAExC,MAjDiB,QAAbgG,EAAA/I,EAAMnB,eAAO,IAAAkK,GAAbA,EAAerD,KAAK,CAChBC,SAAU,QACVC,QAAS,eACTC,OAAQ,4CAwTIC,SAAUxE,EACV3E,UAAU,+BAGlBgI,EAAAA,EAAAA,MAAA,OAAKhI,UAAU,kBAAiBE,SAAA,EAC5BkH,EAAAA,EAAAA,KAAA,SAAOpH,UAAU,uBAAsBE,SAAC,8BACxCkH,EAAAA,EAAAA,KAACxG,EAAAA,EAAW,CACR5C,MAAOyG,EACP5E,WAAW,EACXE,MAAO,CAAE2N,OAAQ,aAErB1F,EAAAA,EAAAA,MAAA,SAAOhI,UAAU,yBAAwBE,SAAA,CAAEuE,EAAe,iBAMtEuD,EAAAA,EAAAA,MAAC2D,EAAAA,EAAM,CACH1B,OAAO,uCACP2B,QAAS/G,EACT9E,MAAO,CAAEX,MAAO,SAChByM,OAAQA,IAAM/G,GAAmB,GACjCgH,OAAK,EAAA5L,SAAA,EAEL8H,EAAAA,EAAAA,MAAA,OAAKhI,UAAU,OAAME,SAAA,CAChB6E,IACGiD,EAAAA,EAAAA,MAAA,OAAKhI,UAAU,mCAAkCE,SAAA,EAC7CkH,EAAAA,EAAAA,KAAA,MAAIpH,UAAU,YAAWE,SAAC,8BAC1B8H,EAAAA,EAAAA,MAAA,KAAGhI,UAAU,OAAME,SAAA,EAACkH,EAAAA,EAAAA,KAAA,UAAAlH,SAAQ,8BAAc,IAAE6E,EAAwB2H,mBACpE1E,EAAAA,EAAAA,MAAA,KAAGhI,UAAU,OAAME,SAAA,EAACkH,EAAAA,EAAAA,KAAA,UAAAlH,SAAQ,8BAAc,IAAE6E,EAAwB4I,gBACpE3F,EAAAA,EAAAA,MAAA,KAAGhI,UAAU,OAAME,SAAA,EAACkH,EAAAA,EAAAA,KAAA,UAAAlH,SAAQ,8BAAc,IAAE0H,GAAW7C,EAAwBgG,yBAIvF/C,EAAAA,EAAAA,MAAA,OAAKhI,UAAU,QAAOE,SAAA,EAClBkH,EAAAA,EAAAA,KAAA,SAAO6E,QAAQ,iBAAiBjM,UAAU,uBAAsBE,SAAC,sCACjEkH,EAAAA,EAAAA,KAACiB,EAAAA,EAAS,CACNzI,GAAG,iBACH5B,MAAOiH,EACPqD,SAAW5L,GAAMwI,EAAkBxI,EAAE6L,OAAOvK,OAC5CwK,YAAY,+DACZxI,UAAU,SACVmJ,SAAUhE,WAKtB6C,EAAAA,EAAAA,MAAA,OAAKhI,UAAU,iCAAgCE,SAAA,EAC3CkH,EAAAA,EAAAA,KAACC,EAAAA,EAAM,CACH3I,MAAM,eACN6I,KAAK,cACLE,QAASA,IAAM3C,GAAmB,GAClC9E,UAAU,qBACVmJ,SAAUhE,KAEdiC,EAAAA,EAAAA,KAACC,EAAAA,EAAM,CACH3I,MAAOyG,EAAiB,wBAAW,eACnCoC,KAAMpC,EAAiB,wBAA0B,aACjDsC,QAzTK5B,UACQ,IAAD+H,EAA5B,IAAK3I,EAAe4I,OAMhB,YALa,QAAbD,EAAAvK,EAAMnB,eAAO,IAAA0L,GAAbA,EAAe7E,KAAK,CAChBC,SAAU,QACVC,QAAS,eACTC,OAAQ,sDAOuB,IAAD4E,EAAtC,GADmB,6BACHC,KAAK9I,GASrB,IAAK,IAAD+I,EACA5I,GAAkB,SAEZU,EAAAA,EAAI+G,KAAK,gCAAiC,CAC5CoB,MAAOhJ,EACPwH,QAAS1H,EAAwB2H,iBAGxB,QAAbsB,EAAA3K,EAAMnB,eAAO,IAAA8L,GAAbA,EAAejF,KAAK,CAChBC,SAAU,UACVC,QAAS,eACTC,OAAQ,qDAGZpE,GAAmB,GACnBI,EAAkB,IAClBF,EAA2B,KAE/B,CAAE,MAAOwB,GAAa,IAAD0H,EAAAC,EAAAC,EACJ,QAAbF,EAAA7K,EAAMnB,eAAO,IAAAgM,GAAbA,EAAenF,KAAK,CAChBC,SAAU,QACVC,QAAS,eACTC,QAAsB,QAAdiF,EAAA3H,EAAMoG,gBAAQ,IAAAuB,GAAM,QAANC,EAAdD,EAAgB/H,YAAI,IAAAgI,OAAN,EAAdA,EAAsB5H,QAAS,wCAE/C,CAAC,QACGpB,GAAkB,EACtB,MAlCiB,QAAb0I,EAAAzK,EAAMnB,eAAO,IAAA4L,GAAbA,EAAe/E,KAAK,CAChBC,SAAU,QACVC,QAAS,eACTC,OAAQ,kEA0SAC,SAAUhE,IAAmBF,EAAe4I,OAC5C7N,UAAU,8B", "sources": ["../node_modules/primereact/progressbar/progressbar.esm.js", "services/signalr.ts", "components/Page/ReceiptsPage.tsx", "hooks/useReceipt.ts"], "sourcesContent": ["'use client';\nimport * as React from 'react';\nimport { PrimeReactContext } from 'primereact/api';\nimport { ComponentBase, useHandleStyle } from 'primereact/componentbase';\nimport { useMergeProps } from 'primereact/hooks';\nimport { classNames } from 'primereact/utils';\n\nfunction _extends() {\n  return _extends = Object.assign ? Object.assign.bind() : function (n) {\n    for (var e = 1; e < arguments.length; e++) {\n      var t = arguments[e];\n      for (var r in t) ({}).hasOwnProperty.call(t, r) && (n[r] = t[r]);\n    }\n    return n;\n  }, _extends.apply(null, arguments);\n}\n\nfunction _typeof(o) {\n  \"@babel/helpers - typeof\";\n\n  return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (o) {\n    return typeof o;\n  } : function (o) {\n    return o && \"function\" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? \"symbol\" : typeof o;\n  }, _typeof(o);\n}\n\nfunction toPrimitive(t, r) {\n  if (\"object\" != _typeof(t) || !t) return t;\n  var e = t[Symbol.toPrimitive];\n  if (void 0 !== e) {\n    var i = e.call(t, r || \"default\");\n    if (\"object\" != _typeof(i)) return i;\n    throw new TypeError(\"@@toPrimitive must return a primitive value.\");\n  }\n  return (\"string\" === r ? String : Number)(t);\n}\n\nfunction toPropertyKey(t) {\n  var i = toPrimitive(t, \"string\");\n  return \"symbol\" == _typeof(i) ? i : i + \"\";\n}\n\nfunction _defineProperty(e, r, t) {\n  return (r = toPropertyKey(r)) in e ? Object.defineProperty(e, r, {\n    value: t,\n    enumerable: !0,\n    configurable: !0,\n    writable: !0\n  }) : e[r] = t, e;\n}\n\nvar classes = {\n  root: function root(_ref) {\n    var props = _ref.props;\n    return props.mode === 'indeterminate' ? classNames('p-progressbar p-component p-progressbar-indeterminate') : classNames('p-progressbar p-component p-progressbar-determinate');\n  },\n  value: 'p-progressbar-value p-progressbar-value-animate',\n  label: 'p-progressbar-label',\n  container: 'p-progressbar-indeterminate-container'\n};\nvar styles = \"\\n@layer primereact {\\n  .p-progressbar {\\n      position: relative;\\n      overflow: hidden;\\n  }\\n  \\n  .p-progressbar-determinate .p-progressbar-value {\\n      height: 100%;\\n      width: 0%;\\n      position: absolute;\\n      display: none;\\n      border: 0 none;\\n      display: flex;\\n      align-items: center;\\n      justify-content: center;\\n      overflow: hidden;\\n  }\\n  \\n  .p-progressbar-determinate .p-progressbar-label {\\n      display: inline-flex;\\n  }\\n  \\n  .p-progressbar-determinate .p-progressbar-value-animate {\\n      transition: width 1s ease-in-out;\\n  }\\n  \\n  .p-progressbar-indeterminate .p-progressbar-value::before {\\n        content: '';\\n        position: absolute;\\n        background-color: inherit;\\n        top: 0;\\n        left: 0;\\n        bottom: 0;\\n        will-change: left, right;\\n        -webkit-animation: p-progressbar-indeterminate-anim 2.1s cubic-bezier(0.65, 0.815, 0.735, 0.395) infinite;\\n                animation: p-progressbar-indeterminate-anim 2.1s cubic-bezier(0.65, 0.815, 0.735, 0.395) infinite;\\n  }\\n  \\n  .p-progressbar-indeterminate .p-progressbar-value::after {\\n      content: '';\\n      position: absolute;\\n      background-color: inherit;\\n      top: 0;\\n      left: 0;\\n      bottom: 0;\\n      will-change: left, right;\\n      -webkit-animation: p-progressbar-indeterminate-anim-short 2.1s cubic-bezier(0.165, 0.84, 0.44, 1) infinite;\\n              animation: p-progressbar-indeterminate-anim-short 2.1s cubic-bezier(0.165, 0.84, 0.44, 1) infinite;\\n      -webkit-animation-delay: 1.15s;\\n              animation-delay: 1.15s;\\n  }\\n}\\n\\n@-webkit-keyframes p-progressbar-indeterminate-anim {\\n  0% {\\n    left: -35%;\\n    right: 100%; }\\n  60% {\\n    left: 100%;\\n    right: -90%; }\\n  100% {\\n    left: 100%;\\n    right: -90%; }\\n}\\n@keyframes p-progressbar-indeterminate-anim {\\n  0% {\\n    left: -35%;\\n    right: 100%; }\\n  60% {\\n    left: 100%;\\n    right: -90%; }\\n  100% {\\n    left: 100%;\\n    right: -90%; }\\n}\\n\\n@-webkit-keyframes p-progressbar-indeterminate-anim-short {\\n  0% {\\n    left: -200%;\\n    right: 100%; }\\n  60% {\\n    left: 107%;\\n    right: -8%; }\\n  100% {\\n    left: 107%;\\n    right: -8%; }\\n}\\n@keyframes p-progressbar-indeterminate-anim-short {\\n  0% {\\n    left: -200%;\\n    right: 100%; }\\n  60% {\\n    left: 107%;\\n    right: -8%; }\\n  100% {\\n    left: 107%;\\n    right: -8%; }\\n}\\n\";\nvar inlineStyles = {\n  value: function value(_ref2) {\n    var props = _ref2.props;\n    var valueWidth = Math.max(props.value, 2); // min 2 to display full label of 0% and 1%\n    var valueColor = props.value ? props.color : 'transparent';\n    return props.mode === 'indeterminate' ? {\n      backgroundColor: props.color\n    } : {\n      width: valueWidth + '%',\n      display: 'flex',\n      backgroundColor: valueColor\n    };\n  }\n};\nvar ProgressBarBase = ComponentBase.extend({\n  defaultProps: {\n    __TYPE: 'ProgressBar',\n    __parentMetadata: null,\n    id: null,\n    value: null,\n    showValue: true,\n    unit: '%',\n    style: null,\n    className: null,\n    mode: 'determinate',\n    displayValueTemplate: null,\n    color: null,\n    children: undefined\n  },\n  css: {\n    classes: classes,\n    styles: styles,\n    inlineStyles: inlineStyles\n  }\n});\n\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nvar ProgressBar = /*#__PURE__*/React.memo(/*#__PURE__*/React.forwardRef(function (inProps, ref) {\n  var mergeProps = useMergeProps();\n  var context = React.useContext(PrimeReactContext);\n  var props = ProgressBarBase.getProps(inProps, context);\n  var _ProgressBarBase$setM = ProgressBarBase.setMetaData(_objectSpread({\n      props: props\n    }, props.__parentMetadata)),\n    ptm = _ProgressBarBase$setM.ptm,\n    cx = _ProgressBarBase$setM.cx,\n    isUnstyled = _ProgressBarBase$setM.isUnstyled;\n  useHandleStyle(ProgressBarBase.css.styles, isUnstyled, {\n    name: 'progressbar'\n  });\n  var elementRef = React.useRef(null);\n  var createLabel = function createLabel() {\n    if (props.showValue && props.value != null) {\n      var label = props.displayValueTemplate ? props.displayValueTemplate(props.value) : props.value + props.unit;\n      return label;\n    }\n    return null;\n  };\n  var createDeterminate = function createDeterminate() {\n    var label = createLabel();\n    var rootProps = mergeProps({\n      className: classNames(props.className, cx('root')),\n      style: props.style,\n      role: 'progressbar',\n      'aria-valuemin': '0',\n      'aria-valuenow': props.value,\n      'aria-valuemax': '100'\n    }, ProgressBarBase.getOtherProps(props), ptm('root'));\n    var valueProps = mergeProps({\n      className: cx('value'),\n      style: {\n        width: props.value + '%',\n        display: 'flex',\n        backgroundColor: props.color\n      }\n    }, ptm('value'));\n    var labelProps = mergeProps({\n      className: cx('label')\n    }, ptm('label'));\n    return /*#__PURE__*/React.createElement(\"div\", _extends({\n      id: props.id,\n      ref: elementRef\n    }, rootProps), /*#__PURE__*/React.createElement(\"div\", valueProps, label != null && /*#__PURE__*/React.createElement(\"div\", labelProps, label)));\n  };\n  var createIndeterminate = function createIndeterminate() {\n    var rootProps = mergeProps({\n      className: classNames(props.className, cx('root')),\n      style: props.style,\n      role: 'progressbar',\n      'aria-valuemin': '0',\n      'aria-valuenow': props.value,\n      'aria-valuemax': '100'\n    }, ProgressBarBase.getOtherProps(props), ptm('root'));\n    var containerProps = mergeProps({\n      className: cx('container')\n    }, ptm('container'));\n    var valueProps = mergeProps({\n      className: cx('value'),\n      style: {\n        backgroundColor: props.color\n      }\n    }, ptm('value'));\n    return /*#__PURE__*/React.createElement(\"div\", _extends({\n      id: props.id,\n      ref: elementRef\n    }, rootProps), /*#__PURE__*/React.createElement(\"div\", containerProps, /*#__PURE__*/React.createElement(\"div\", valueProps)));\n  };\n  React.useImperativeHandle(ref, function () {\n    return {\n      props: props,\n      getElement: function getElement() {\n        return elementRef.current;\n      }\n    };\n  });\n  if (props.mode === 'determinate') {\n    return createDeterminate();\n  } else if (props.mode === 'indeterminate') {\n    return createIndeterminate();\n  }\n  throw new Error(props.mode + \" is not a valid mode for the ProgressBar. Valid values are 'determinate' and 'indeterminate'\");\n}));\nProgressBar.displayName = 'ProgressBar';\n\nexport { ProgressBar };\n", "import * as signalR from \"@microsoft/signalr\";\r\n\r\nconst connection = new signalR.HubConnectionBuilder()\r\n  .withUrl(process.env.REACT_APP_API_URL + \"/reportHub\")\r\n  .withAutomaticReconnect()\r\n  .build();\r\n\r\nexport default connection;", "import { formatUtcToTaipei } from \"../../utils/dateUtils\";\r\nimport { Button } from 'primereact/button';\r\nimport { Calendar } from 'primereact/calendar';\r\nimport { Column } from 'primereact/column';\r\nimport { ConfirmDialog } from 'primereact/confirmdialog';\r\nimport { DataTable } from 'primereact/datatable';\r\nimport { Dialog } from 'primereact/dialog';\r\nimport { Dropdown } from 'primereact/dropdown';\r\nimport { InputText } from \"primereact/inputtext\";\r\nimport { ProgressBar } from 'primereact/progressbar';\r\nimport LoadingSpinner from '../Common/LoadingSpinner';\r\nimport { Toast } from \"primereact/toast\";\r\nimport React, { useRef, useState, useEffect } from 'react';\r\nimport { useNavigate } from \"react-router-dom\";\r\nimport { ROUTES } from \"../../constants/routes\";\r\nimport useReceipt from '../../hooks/useReceipt';\r\nimport api from '../../services/api';\r\nimport connection from \"../../services/signalr\";\r\nimport { Card } from 'primereact/card';\r\n\r\nconst ReceiptsPage: React.FC = () => {\r\n    const navigate = useNavigate();\r\n    const toast = useRef<Toast>(null);\r\n    const [name, setName] = useState('');\r\n    const [nationalId, setNationalId] = useState('');\r\n    const [starttime, setStarttime] = useState<Date | null | undefined>(undefined);\r\n    const [endtime, setEndtime] = useState<Date | null | undefined>(undefined);\r\n    const [refreshKey, setRefreshKey] = useState(0);\r\n\r\n    const [searchParams, setSearchParams] = useState({\r\n        name: '',\r\n        nationalId: '',\r\n        starttime: null as Date | null | undefined,\r\n        endtime: null as Date | null | undefined,\r\n        refreshKey: 0,\r\n    });\r\n\r\n    // 批量匯出相關狀態\r\n    const [selectedReceipts, setSelectedReceipts] = useState<any[]>([]);\r\n    const [showExportDialog, setShowExportDialog] = useState(false);\r\n    const [titleCode, setTitleCode] = useState(0);\r\n    const [exportProgress, setExportProgress] = useState(0);\r\n    const [isExporting, setIsExporting] = useState(false);\r\n\r\n    // 送信相關狀態\r\n    const [showEmailDialog, setShowEmailDialog] = useState(false);\r\n    const [selectedReceiptForEmail, setSelectedReceiptForEmail] = useState<any>(null);\r\n    const [recipientEmail, setRecipientEmail] = useState('');\r\n    const [isSendingEmail, setIsSendingEmail] = useState(false);\r\n\r\n    const { receipts, loading } = useReceipt(\r\n        searchParams.name, \r\n        searchParams.nationalId, \r\n        searchParams.starttime, \r\n        searchParams.endtime, \r\n        refreshKey\r\n    );\r\n\r\n    // 下拉選單選項\r\n    const titleOptions = [\r\n        { label: '全部', value: 0 },\r\n        { label: '繳款人收執聯', value: 1 },\r\n        { label: '單位存根聯', value: 2 },\r\n        { label: '單位扣底聯', value: 3 }\r\n    ];\r\n\r\n    const genderdict: { [key: string]: string } = {\r\n        \"1\": \"男性\",\r\n        \"2\": \"女性\",\r\n        \"3\": \"其他\"\r\n    };\r\n\r\n    // 初始化 SignalR 連接\r\n    useEffect(() => {\r\n\r\n        connection\r\n        .start()\r\n        .then(() => {\r\n        console.log(\"已連線至 SignalR\");\r\n        //console.log(\"連線 ID\", connection.connectionId);\r\n        })\r\n        .catch(err => console.error(\"SignalR 連線失敗:\", err));\r\n\r\n        connection.on(\"ReportProgress\", (value) => {\r\n        setExportProgress(value);\r\n        });\r\n\r\n        connection.on(\"ReportFinished\", (msg) => {\r\n        setExportProgress(msg);\r\n        });\r\n\r\n        return () => {\r\n        connection.stop();\r\n        };\r\n    }, []);\r\n\r\n    const handleSearchClick = () => {\r\n        setRefreshKey(refreshKey + 1)\r\n        setSearchParams({ name, nationalId, starttime, endtime, refreshKey});\r\n    };\r\n\r\n    const Reload = () => {\r\n        // 重新觸發 usePatient，等於重新查詢\r\n        setRefreshKey(prev => prev + 1);\r\n    };\r\n\r\n    // 批量匯出按鈕點擊\r\n    const handleBatchExport = () => {\r\n        if (selectedReceipts.length === 0) {\r\n            toast.current?.show({\r\n                severity: 'error',\r\n                summary: '錯誤',\r\n                detail: '請勾選欲匯出的收據'\r\n            });\r\n            return;\r\n        }\r\n        setShowExportDialog(true);\r\n    };\r\n\r\n    // 執行批量匯出\r\n    const executeBatchExport = async () => {\r\n        if (!connection) {\r\n            toast.current?.show({\r\n                severity: 'error',\r\n                summary: '錯誤',\r\n                detail: 'SignalR 連接未建立'\r\n            });\r\n            return;\r\n        }\r\n\r\n        try {\r\n            setIsExporting(true);\r\n            setExportProgress(0);\r\n\r\n            const treatmentIds = selectedReceipts.map(receipt => receipt.id);\r\n            const orderNos = selectedReceipts.map(receipt => receipt.receiptOrderNo);\r\n            \r\n            const requestBody = {\r\n                TreatmentId: treatmentIds,\r\n                orderNo: orderNos,\r\n                Titlecode: titleCode\r\n            };\r\n\r\n            const response = await api.post('/api/receipt/ExportReceiptsLisrPdf', requestBody, {\r\n                params: {\r\n                    connectionId: connection.connectionId\r\n                },\r\n                responseType: 'blob',\r\n                headers: {\r\n                    'Content-Type': 'application/json' // Axios 通常會自動設定，但明確指定更佳\r\n                }\r\n            });\r\n\r\n            // 產生 blob url\r\n            const file = new Blob([response.data], { type: 'application/pdf' });\r\n            const fileURL = URL.createObjectURL(file);\r\n\r\n            // 在新分頁開啟 PDF\r\n            window.open(fileURL);\r\n\r\n            setShowExportDialog(false);\r\n            setSelectedReceipts([]);\r\n\r\n        } catch (error: any) {\r\n            setIsExporting(false);\r\n            setExportProgress(0);\r\n            toast.current?.show({\r\n                severity: 'error',\r\n                summary: '錯誤',\r\n                detail: error.response?.data || '匯出失敗'\r\n            });\r\n        }\r\n    };\r\n\r\n    // 開啟送信對話框\r\n    const handleSendEmail = (receipt: any) => {\r\n        setSelectedReceiptForEmail(receipt);\r\n        setRecipientEmail(receipt.patientEmail);\r\n        setShowEmailDialog(true);\r\n    };\r\n\r\n    // 執行送信\r\n    const executeSendEmail = async () => {\r\n        if (!recipientEmail.trim()) {\r\n            toast.current?.show({\r\n                severity: 'error',\r\n                summary: '錯誤',\r\n                detail: '請輸入收件人郵箱'\r\n            });\r\n            return;\r\n        }\r\n\r\n        // 簡單的郵箱格式驗證\r\n        const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\r\n        if (!emailRegex.test(recipientEmail)) {\r\n            toast.current?.show({\r\n                severity: 'error',\r\n                summary: '錯誤',\r\n                detail: '請輸入有效的郵箱地址'\r\n            });\r\n            return;\r\n        }\r\n\r\n        try {\r\n            setIsSendingEmail(true);\r\n\r\n            await api.post('/api/receipt/SendReceiptEmail', {\r\n                email: recipientEmail,\r\n                orderNo: selectedReceiptForEmail.receiptOrderNo\r\n            });\r\n\r\n            toast.current?.show({\r\n                severity: 'success',\r\n                summary: '成功',\r\n                detail: '收據郵件發送成功'\r\n            });\r\n\r\n            setShowEmailDialog(false);\r\n            setRecipientEmail('');\r\n            setSelectedReceiptForEmail(null);\r\n\r\n        } catch (error: any) {\r\n            toast.current?.show({\r\n                severity: 'error',\r\n                summary: '錯誤',\r\n                detail: error.response?.data?.error || '郵件發送失敗'\r\n            });\r\n        } finally {\r\n            setIsSendingEmail(false);\r\n        }\r\n    };\r\n\r\n    const paginatorLeft = (\r\n        <Button\r\n            type=\"button\"\r\n            icon=\"pi pi-refresh\"\r\n            text\r\n            onClick={() => Reload()}\r\n        />\r\n    );\r\n    const paginatorRight = <div></div>;\r\n    const optionBodyTemplate = (rowData: any) => {\r\n        return (\r\n            <div className=\"flex gap-2\">\r\n                    <Button \r\n                        label=\"檢視\" \r\n                        type=\"button\" \r\n                        icon=\"pi pi-file-edit\" \r\n                        onClick={() => navigate(ROUTES.RECEIPT_DETAIL, { state: { treatment: rowData } })}\r\n                        size=\"small\" \r\n                        severity=\"info\" \r\n                        style={{ fontSize: '1rem', margin: '3px' }} \r\n                    />\r\n                    <Button\r\n                        label=\"送信\"\r\n                        type=\"button\"\r\n                        icon=\"pi pi-send\"\r\n                        onClick={() => handleSendEmail(rowData)}\r\n                        size=\"small\"\r\n                        severity=\"success\"\r\n                        style={{ fontSize: '1rem', margin: '3px' }}\r\n                        disabled={ rowData.patientEmail ? false : true}\r\n                    />\r\n            </div>\r\n        );\r\n    };\r\n\r\n    const genderBodyTemplate = (rowData: any) => {\r\n        var data = String(rowData.patientGender)\r\n        const gendar = genderdict[data]\r\n            return (\r\n                <div>\r\n                    {gendar}\r\n                </div>\r\n            );\r\n        };\r\n\r\n    const formatDate = (value: string) => {\r\n    if (!value) return '';\r\n    return formatUtcToTaipei(value, \"yyyy/MM/dd HH:mm:ss\");\r\n  };\r\n\r\n    const formatAge = (value: string) => {\r\n        if (!value) return \"\";\r\n        const date = new Date(value);\r\n        const today = new Date();\r\n        let age = today.getFullYear() - date.getFullYear();\r\n\r\n        const hasNotHadBirthdayThisYear =\r\n            today.getMonth() < date.getMonth() ||\r\n            (today.getMonth() === date.getMonth() && today.getDate() < date.getDate());\r\n\r\n        if (hasNotHadBirthdayThisYear) {\r\n            age--;\r\n        }\r\n\r\n        return age;\r\n        \r\n    };\r\n\r\n    if (loading) {\r\n        return <LoadingSpinner message=\"載入收據資料中...\" />;\r\n    }\r\n\r\n    return (\r\n        <div>\r\n            <Toast ref={toast} />\r\n            <ConfirmDialog />\r\n            <Card title=\"收據管理\" className=\"mb-4\">\r\n                <p className=\"text-600 line-height-3 m-0\">\r\n                    收據管理頁面，可以查詢、檢視、製作PDF、發送Email收據資料。\r\n                </p>\r\n            </Card>\r\n\r\n            {/* 搜尋條件 */}\r\n            <Card className=\"mb-4\">\r\n                <div className=\"grid\">\r\n                    <div className=\"col-6 md:col-3\">\r\n                        <InputText\r\n                            id=\"name\"\r\n                            type=\"text\"\r\n                            value={name}\r\n                            onChange={(e) => setName(e.target.value)}\r\n                            className=\"w-full\"\r\n                            placeholder=\"病患姓名\" />\r\n                    </div>\r\n                    <div className=\"col-6 md:col-3\">\r\n                        <InputText\r\n                            id=\"nationalId\"\r\n                            type=\"text\"\r\n                            value={nationalId}\r\n                            onChange={(e) => setNationalId(e.target.value)}\r\n                            className=\"w-full\"\r\n                            placeholder=\"病患身分證\" />\r\n                    </div>\r\n\r\n                    <div className=\"col-6 md:col-3\">\r\n                        <Calendar\r\n                            id=\"starttime\"\r\n                            value={starttime}\r\n                            onChange={(e) => setStarttime(e.value)}\r\n                            placeholder=\"開始時間\"\r\n                            className=\"w-full\"\r\n                            dateFormat=\"yy/mm/dd\"\r\n                            showIcon/>\r\n                    </div>\r\n                    <div className=\"col-6 md:col-3\">\r\n                        <Calendar\r\n                            id=\"endtime\"\r\n                            value={endtime}\r\n                            onChange={(e) => setEndtime(e.value)}\r\n                            placeholder=\"結束時間\"\r\n                            className=\"w-full\"\r\n                            dateFormat=\"yy/mm/dd\"\r\n                            showIcon/>\r\n                    </div>\r\n                    <div className=\"col-12 md:col-4\">\r\n                        <div className=\"flex gap-2\">\r\n                            <Button label=\"查詢\" icon=\"pi pi-search\" onClick={handleSearchClick}/>\r\n                            <Button\r\n                                label=\"批量匯出\"\r\n                                icon=\"pi pi-download\"\r\n                                onClick={handleBatchExport}\r\n                                className=\"p-button-success\"\r\n                                disabled={selectedReceipts.length === 0}\r\n                            />\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n            </Card>\r\n\r\n            <Card>\r\n                <DataTable\r\n                    value={receipts}\r\n                    selection={selectedReceipts}\r\n                    onSelectionChange={(e) => setSelectedReceipts(e.value)}\r\n                    paginator\r\n                    rows={10}\r\n                    rowsPerPageOptions={[10, 20, 30, 40]}\r\n                    emptyMessage=\"沒有找到收據資料\"\r\n                    tableStyle={{ minWidth: '50rem' }}\r\n                    paginatorLeft={paginatorLeft}\r\n                    paginatorRight={paginatorRight}\r\n                >\r\n                    <Column selectionMode=\"multiple\" headerStyle={{ width: '2%' }} />\r\n                    <Column field=\"orderNo\" header=\"案號\" style={{ width: '5%' }} />\r\n                    <Column field=\"receiptOrderNo\" header=\"收據編號\" style={{ width: '5%' }} />\r\n                    <Column field=\"patientName\" header=\"病患姓名\" style={{ width: '5%' }} />\r\n                    <Column field=\"patientGender\" header=\"性別\" style={{ width: '3%' }} body={genderBodyTemplate}/>\r\n                    <Column field=\"patientBirthDate\" header=\"年齡\" style={{ width: '3%' }}  body={(rowData) => formatAge(rowData.patientBirthDate)}/>\r\n                    <Column field=\"receiptCreatedAt\" header=\"新增日期\" style={{ width: '8%' }} body={(rowData) => formatDate(rowData.receiptCreatedAt)} />\r\n                    <Column field=\"receiptUpdatedAt\" header=\"更新日期\" style={{ width: '8%' }} body={(rowData) => formatDate(rowData.receiptUpdatedAt)}/>\r\n                    <Column field=\"receiptOperatorUserName\" header=\"操作人\" style={{ width: '5%' }} />\r\n                    <Column field=\"Option\" header=\"功能\" style={{ width: '12%' }} body={optionBodyTemplate} />\r\n                </DataTable>\r\n            </Card>\r\n\r\n            {/* 批量匯出彈跳視窗 */}\r\n            <Dialog\r\n                header=\"批量匯出收據\"\r\n                visible={showExportDialog}\r\n                style={{ width: '50vw' }}\r\n                onHide={() => setShowExportDialog(false)}\r\n                modal\r\n            >\r\n                <div className=\"mb-4\">\r\n                    <h5>已選擇的收據 ({selectedReceipts.length} 筆)</h5>\r\n                    <DataTable\r\n                        value={selectedReceipts}\r\n                        scrollable\r\n                        scrollHeight=\"300px\"\r\n                        emptyMessage=\"沒有選擇的收據\"\r\n                    >\r\n                        <Column field=\"orderNo\" header=\"案號\" style={{ width: '15%' }} />\r\n                        <Column field=\"receiptorderNo\" header=\"收據編號\" style={{ width: '15%' }} />\r\n                        <Column field=\"patientName\" header=\"病患姓名\" style={{ width: '15%' }} />\r\n                        <Column field=\"receiptCreatedAt\" header=\"新增日期\" style={{ width: '20%' }} body={(rowData) => formatDate(rowData.receiptCreatedAt)} />\r\n                    </DataTable>\r\n                </div>\r\n\r\n                <div className=\"grid align-items-end\">\r\n                    <div className=\"col-12 md:col-4\">\r\n                        <label htmlFor=\"titleCode\" className=\"font-bold block mb-2\">收據類型</label>\r\n                        <Dropdown\r\n                            id=\"titleCode\"\r\n                            value={titleCode}\r\n                            options={titleOptions}\r\n                            onChange={(e) => setTitleCode(e.value)}\r\n                            placeholder=\"選擇收據類型\"\r\n                            className=\"w-full\"\r\n                        />\r\n                    </div>\r\n                    <div className=\"col-12 md:col-4\">\r\n                        <Button\r\n                            label=\"匯出\"\r\n                            icon=\"pi pi-download\"\r\n                            onClick={executeBatchExport}\r\n                            disabled={isExporting}\r\n                            className=\"p-button-success w-full\"\r\n                        />\r\n                    </div>\r\n                    <div className=\"col-12 md:col-4\">\r\n                        <label className=\"font-bold block mb-2\">匯出進度</label>\r\n                        <ProgressBar\r\n                            value={exportProgress}\r\n                            showValue={false}\r\n                            style={{ height: '1.5rem' }}\r\n                        />\r\n                        <small className=\"text-center block mt-1\">{exportProgress}%</small>\r\n                    </div>\r\n                </div>\r\n            </Dialog>\r\n\r\n            {/* 送信對話框 */}\r\n            <Dialog\r\n                header=\"發送收據郵件\"\r\n                visible={showEmailDialog}\r\n                style={{ width: '500px' }}\r\n                onHide={() => setShowEmailDialog(false)}\r\n                modal\r\n            >\r\n                <div className=\"mb-4\">\r\n                    {selectedReceiptForEmail && (\r\n                        <div className=\"p-3 bg-gray-50 border-round mb-3\">\r\n                            <h3 className=\"mt-0 mb-2\">收據資訊</h3>\r\n                            <p className=\"mb-1\"><strong>收據編號:</strong> {selectedReceiptForEmail.receiptOrderNo}</p>\r\n                            <p className=\"mb-1\"><strong>病患姓名:</strong> {selectedReceiptForEmail.patientName}</p>\r\n                            <p className=\"mb-0\"><strong>建立日期:</strong> {formatDate(selectedReceiptForEmail.receiptCreatedAt)}</p>\r\n                        </div>\r\n                    )}\r\n\r\n                    <div className=\"field\">\r\n                        <label htmlFor=\"recipientEmail\" className=\"font-bold block mb-2\">收件人郵箱 *</label>\r\n                        <InputText\r\n                            id=\"recipientEmail\"\r\n                            value={recipientEmail}\r\n                            onChange={(e) => setRecipientEmail(e.target.value)}\r\n                            placeholder=\"請輸入收件人郵箱地址\"\r\n                            className=\"w-full\"\r\n                            disabled={isSendingEmail}\r\n                        />\r\n                    </div>\r\n                </div>\r\n\r\n                <div className=\"flex justify-content-end gap-2\">\r\n                    <Button\r\n                        label=\"取消\"\r\n                        icon=\"pi pi-times\"\r\n                        onClick={() => setShowEmailDialog(false)}\r\n                        className=\"p-button-secondary\"\r\n                        disabled={isSendingEmail}\r\n                    />\r\n                    <Button\r\n                        label={isSendingEmail ? \"發送中...\" : \"發送\"}\r\n                        icon={isSendingEmail ? \"pi pi-spin pi-spinner\" : \"pi pi-send\"}\r\n                        onClick={executeSendEmail}\r\n                        disabled={isSendingEmail || !recipientEmail.trim()}\r\n                        className=\"p-button-primary\"\r\n                    />\r\n                </div>\r\n            </Dialog>\r\n        </div>\r\n    );\r\n};\r\n\r\nexport default ReceiptsPage;", "import { useEffect, useState } from \"react\";\r\nimport api from '../services/api';\r\n\r\n// 對應 Treatment 的型別定義\r\nexport interface ReceiptItem {\r\n  Id: number;\r\n  orderNo: string;\r\n  ReceiptUrl: string;\r\n  ReceiptorderNo: string;\r\n  ReceiptCreatedAt: string;\r\n  ReceiptUpdatedAt: string;\r\n  ReceiptOperatorUserName: string;\r\n  PatientId: number;\r\n}\r\n\r\nexport default function useReceipt(\r\n  patientname: string,\r\n  nationalId: string,\r\n  startTime: Date | null | undefined,\r\n  endTime: Date | null | undefined,\r\n  refreshKey: number\r\n) {\r\n  const [receipts, setReceipts] = useState<ReceiptItem[]>([]);\r\n  const [loading, setLoading] = useState(true);\r\n\r\n  useEffect(() => {\r\n    const fetchReceipts = async () => {\r\n      setLoading(true);\r\n      api.get<ReceiptItem[]>(\"/api/receipt/GetList\", {\r\n        params: {\r\n          patientname: patientname,\r\n          nationalId: nationalId,\r\n          starttime: startTime?.toISOString(),\r\n          endtime: endTime?.toISOString(),\r\n        },\r\n      })\r\n        .then((res) => setReceipts(res.data))\r\n        .catch((err) => console.error(\"API Error:\", err))\r\n        .finally(() => setLoading(false));\r\n    };\r\n\r\n    fetchReceipts();\r\n  }, [patientname, nationalId, startTime, endTime, refreshKey]);\r\n\r\n  return { receipts, loading };\r\n}"], "names": ["_extends", "Object", "assign", "bind", "n", "e", "arguments", "length", "t", "r", "hasOwnProperty", "call", "apply", "_typeof", "o", "Symbol", "iterator", "constructor", "prototype", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "i", "toPrimitive", "TypeError", "String", "Number", "_defineProperty", "defineProperty", "value", "enumerable", "configurable", "writable", "classes", "root", "_ref", "props", "mode", "classNames", "label", "container", "inlineStyles", "_ref2", "valueWidth", "Math", "max", "valueColor", "color", "backgroundColor", "width", "display", "ProgressBarBase", "ComponentBase", "extend", "defaultProps", "__TYPE", "__parentMetadata", "id", "showValue", "unit", "style", "className", "displayValueTemplate", "children", "undefined", "css", "styles", "ownKeys", "keys", "getOwnPropertySymbols", "filter", "getOwnPropertyDescriptor", "push", "ProgressBar", "React", "inProps", "ref", "mergeProps", "useMergeProps", "context", "PrimeReactContext", "getProps", "_ProgressBarBase$setM", "setMetaData", "for<PERSON>ach", "getOwnPropertyDescriptors", "defineProperties", "_objectSpread", "ptm", "cx", "isUnstyled", "useHandleStyle", "name", "elementRef", "getElement", "current", "rootProps", "role", "getOtherProps", "valueProps", "labelProps", "createDeterminate", "containerProps", "createIndeterminate", "Error", "displayName", "signalR", "withUrl", "process", "withAutomaticReconnect", "build", "ReceiptsPage", "navigate", "useNavigate", "toast", "useRef", "setName", "useState", "nationalId", "setNationalId", "starttime", "set<PERSON><PERSON><PERSON><PERSON>", "endtime", "setEndtime", "refresh<PERSON><PERSON>", "setRefresh<PERSON>ey", "searchParams", "setSearchParams", "selectedReceipts", "setSelectedReceipts", "showExportDialog", "setShowExportDialog", "titleCode", "setTitleCode", "exportProgress", "setExportProgress", "isExporting", "setIsExporting", "showEmailDialog", "setShowEmailDialog", "selectedReceiptForEmail", "setSelectedReceiptForEmail", "recipientEmail", "setRecipientEmail", "isSendingEmail", "setIsSendingEmail", "receipts", "loading", "patientname", "startTime", "endTime", "setReceipts", "setLoading", "useEffect", "async", "api", "get", "params", "toISOString", "then", "res", "data", "catch", "err", "console", "error", "finally", "fetchReceipts", "useReceipt", "genderdict", "connection", "start", "log", "on", "msg", "stop", "paginatorLeft", "_jsx", "<PERSON><PERSON>", "type", "icon", "text", "onClick", "prev", "paginatorRight", "formatDate", "formatUtcToTaipei", "LoadingSpinner", "message", "_jsxs", "Toast", "ConfirmDialog", "Card", "title", "InputText", "onChange", "target", "placeholder", "Calendar", "dateFormat", "showIcon", "handleSearchClick", "handleBatchExport", "_toast$current", "show", "severity", "summary", "detail", "disabled", "DataTable", "selection", "onSelectionChange", "paginator", "rows", "rowsPerPageOptions", "emptyMessage", "tableStyle", "min<PERSON><PERSON><PERSON>", "Column", "selectionMode", "headerStyle", "field", "header", "body", "rowData", "patientGender", "gendar", "date", "Date", "today", "age", "getFullYear", "getMonth", "getDate", "formatAge", "patientBirthDate", "receiptCreatedAt", "receiptUpdatedAt", "ROUTES", "RECEIPT_DETAIL", "state", "treatment", "size", "fontSize", "margin", "handleSendEmail", "receipt", "patientEmail", "Dialog", "visible", "onHide", "modal", "scrollable", "scrollHeight", "htmlFor", "Dropdown", "options", "_toast$current2", "treatmentIds", "map", "requestBody", "TreatmentId", "orderNo", "receiptOrderNo", "Titlecode", "response", "post", "connectionId", "responseType", "headers", "file", "Blob", "fileURL", "URL", "createObjectURL", "window", "open", "_toast$current3", "_error$response", "height", "patientName", "_toast$current4", "trim", "_toast$current5", "test", "_toast$current6", "email", "_toast$current7", "_error$response2", "_error$response2$data"], "sourceRoot": ""}
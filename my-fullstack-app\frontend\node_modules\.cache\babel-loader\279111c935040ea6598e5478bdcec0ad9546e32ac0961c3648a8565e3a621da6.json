{"ast": null, "code": "const formatRelativeLocale = {\n  lastWeek: \"eeee 'pase nan lè' p\",\n  yesterday: \"'yè nan lè' p\",\n  today: \"'jodi a' p\",\n  tomorrow: \"'demen nan lè' p'\",\n  nextWeek: \"eeee 'pwochen nan lè' p\",\n  other: \"P\"\n};\nexport const formatRelative = (token, _date, _baseDate, _options) => formatRelativeLocale[token];", "map": {"version": 3, "names": ["formatRelativeLocale", "lastWeek", "yesterday", "today", "tomorrow", "nextWeek", "other", "formatRelative", "token", "_date", "_baseDate", "_options"], "sources": ["C:/Users/<USER>/Desktop/demo-git/demo-react/my-fullstack-app/frontend/node_modules/date-fns/locale/ht/_lib/formatRelative.js"], "sourcesContent": ["const formatRelativeLocale = {\n  lastWeek: \"eeee 'pase nan lè' p\",\n  yesterday: \"'yè nan lè' p\",\n  today: \"'jodi a' p\",\n  tomorrow: \"'demen nan lè' p'\",\n  nextWeek: \"eeee 'pwochen nan lè' p\",\n  other: \"P\",\n};\n\nexport const formatRelative = (token, _date, _baseDate, _options) =>\n  formatRelativeLocale[token];\n"], "mappings": "AAAA,MAAMA,oBAAoB,GAAG;EAC3BC,QAAQ,EAAE,sBAAsB;EAChCC,SAAS,EAAE,eAAe;EAC1BC,KAAK,EAAE,YAAY;EACnBC,QAAQ,EAAE,mBAAmB;EAC7BC,QAAQ,EAAE,yBAAyB;EACnCC,KAAK,EAAE;AACT,CAAC;AAED,OAAO,MAAMC,cAAc,GAAGA,CAACC,KAAK,EAAEC,KAAK,EAAEC,SAAS,EAAEC,QAAQ,KAC9DX,oBAAoB,CAACQ,KAAK,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
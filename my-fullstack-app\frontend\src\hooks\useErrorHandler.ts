import { useCallback } from 'react';
import { useNavigate } from 'react-router-dom';
import { ApiError } from '../services/api';
import { log } from '../utils/logger';

export interface ErrorInfo {
  message: string;
  stack?: string;
  code?: string | number;
  statusCode?: number;
}

interface UseErrorHandlerReturn {
  handleError: (error: unknown) => string;
  handleErrorWithNavigation: (error: Error | ErrorInfo | any, context?: string) => void;
  handleApiError: (error: any, context?: string) => void;
}

/**
 * Custom hook for consistent error handling across the application
 */
export function useErrorHandler(): UseErrorHandlerReturn {
  const navigate = useNavigate();

  const handleError = useCallback((error: unknown): string => {
    // Handle API errors
    if (isApiError(error)) {
      return error.message;
    }

    // Handle standard Error objects
    if (error instanceof Error) {
      return error.message;
    }

    // Handle string errors
    if (typeof error === 'string') {
      return error;
    }

    // Fallback for unknown error types
    return 'An unexpected error occurred';
  }, []);

  const handleErrorWithNavigation = useCallback((error: Error | ErrorInfo | any, context?: string) => {
    // 標準化錯誤對象
    const errorInfo: ErrorInfo = {
      message: error?.message || error?.toString() || '未知錯誤',
      stack: error?.stack,
      code: error?.code,
      statusCode: error?.response?.status || error?.statusCode
    };

    // 記錄錯誤
    log.error(`錯誤處理 ${context ? `[${context}]` : ''}`, errorInfo);

    // 根據錯誤類型決定處理方式
    if (errorInfo.statusCode === 401) {
      log.warn('未授權訪問，重定向到登入頁面');
      navigate('/login', { replace: true });
      return;
    }

    if (errorInfo.statusCode === 403) {
      log.warn('訪問被禁止');
      navigate('/error', {
        state: {
          error: errorInfo,
          message: '您沒有權限訪問此資源'
        },
        replace: true
      });
      return;
    }

    // 其他錯誤 - 導航到通用錯誤頁面
    navigate('/error', {
      state: {
        error: errorInfo,
        message: errorInfo.message
      },
      replace: true
    });
  }, [navigate]);

  const handleApiError = useCallback((error: any, context?: string) => {
    // 專門處理 API 錯誤
    const apiError: ErrorInfo = {
      message: error?.response?.data?.message || error?.message || 'API 請求失敗',
      statusCode: error?.response?.status,
      code: error?.response?.data?.code || error?.code,
      stack: error?.stack
    };

    handleErrorWithNavigation(apiError, context || 'API');
  }, [handleErrorWithNavigation]);

  return {
    handleError,
    handleErrorWithNavigation,
    handleApiError
  };
}

/**
 * Type guard to check if error is an ApiError
 */
function isApiError(error: unknown): error is ApiError {
  return (
    typeof error === 'object' &&
    error !== null &&
    'message' in error &&
    typeof (error as any).message === 'string'
  );
}

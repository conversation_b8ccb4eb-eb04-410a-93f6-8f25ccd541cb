{"ast": null, "code": "function declension(scheme, count) {\n  // scheme for count=1 exists\n  if (scheme.one !== undefined && count === 1) {\n    return scheme.one;\n  }\n  const rem10 = count % 10;\n  const rem100 = count % 100;\n\n  // 1, 21, 31, ...\n  if (rem10 === 1 && rem100 !== 11) {\n    return scheme.singularNominative.replace(\"{{count}}\", String(count));\n\n    // 2, 3, 4, 22, 23, 24, 32 ...\n  } else if (rem10 >= 2 && rem10 <= 4 && (rem100 < 10 || rem100 > 20)) {\n    return scheme.singularGenitive.replace(\"{{count}}\", String(count));\n\n    // 5, 6, 7, 8, 9, 10, 11, ...\n  } else {\n    return scheme.pluralGenitive.replace(\"{{count}}\", String(count));\n  }\n}\nfunction buildLocalizeTokenFn(scheme) {\n  return (count, options) => {\n    if (options && options.addSuffix) {\n      if (options.comparison && options.comparison > 0) {\n        if (scheme.future) {\n          return declension(scheme.future, count);\n        } else {\n          return \"за \" + declension(scheme.regular, count);\n        }\n      } else {\n        if (scheme.past) {\n          return declension(scheme.past, count);\n        } else {\n          return declension(scheme.regular, count) + \" тому\";\n        }\n      }\n    } else {\n      return declension(scheme.regular, count);\n    }\n  };\n}\nconst halfAtMinute = (_, options) => {\n  if (options && options.addSuffix) {\n    if (options.comparison && options.comparison > 0) {\n      return \"за півхвилини\";\n    } else {\n      return \"півхвилини тому\";\n    }\n  }\n  return \"півхвилини\";\n};\nconst formatDistanceLocale = {\n  lessThanXSeconds: buildLocalizeTokenFn({\n    regular: {\n      one: \"менше секунди\",\n      singularNominative: \"менше {{count}} секунди\",\n      singularGenitive: \"менше {{count}} секунд\",\n      pluralGenitive: \"менше {{count}} секунд\"\n    },\n    future: {\n      one: \"менше, ніж за секунду\",\n      singularNominative: \"менше, ніж за {{count}} секунду\",\n      singularGenitive: \"менше, ніж за {{count}} секунди\",\n      pluralGenitive: \"менше, ніж за {{count}} секунд\"\n    }\n  }),\n  xSeconds: buildLocalizeTokenFn({\n    regular: {\n      singularNominative: \"{{count}} секунда\",\n      singularGenitive: \"{{count}} секунди\",\n      pluralGenitive: \"{{count}} секунд\"\n    },\n    past: {\n      singularNominative: \"{{count}} секунду тому\",\n      singularGenitive: \"{{count}} секунди тому\",\n      pluralGenitive: \"{{count}} секунд тому\"\n    },\n    future: {\n      singularNominative: \"за {{count}} секунду\",\n      singularGenitive: \"за {{count}} секунди\",\n      pluralGenitive: \"за {{count}} секунд\"\n    }\n  }),\n  halfAMinute: halfAtMinute,\n  lessThanXMinutes: buildLocalizeTokenFn({\n    regular: {\n      one: \"менше хвилини\",\n      singularNominative: \"менше {{count}} хвилини\",\n      singularGenitive: \"менше {{count}} хвилин\",\n      pluralGenitive: \"менше {{count}} хвилин\"\n    },\n    future: {\n      one: \"менше, ніж за хвилину\",\n      singularNominative: \"менше, ніж за {{count}} хвилину\",\n      singularGenitive: \"менше, ніж за {{count}} хвилини\",\n      pluralGenitive: \"менше, ніж за {{count}} хвилин\"\n    }\n  }),\n  xMinutes: buildLocalizeTokenFn({\n    regular: {\n      singularNominative: \"{{count}} хвилина\",\n      singularGenitive: \"{{count}} хвилини\",\n      pluralGenitive: \"{{count}} хвилин\"\n    },\n    past: {\n      singularNominative: \"{{count}} хвилину тому\",\n      singularGenitive: \"{{count}} хвилини тому\",\n      pluralGenitive: \"{{count}} хвилин тому\"\n    },\n    future: {\n      singularNominative: \"за {{count}} хвилину\",\n      singularGenitive: \"за {{count}} хвилини\",\n      pluralGenitive: \"за {{count}} хвилин\"\n    }\n  }),\n  aboutXHours: buildLocalizeTokenFn({\n    regular: {\n      singularNominative: \"близько {{count}} години\",\n      singularGenitive: \"близько {{count}} годин\",\n      pluralGenitive: \"близько {{count}} годин\"\n    },\n    future: {\n      singularNominative: \"приблизно за {{count}} годину\",\n      singularGenitive: \"приблизно за {{count}} години\",\n      pluralGenitive: \"приблизно за {{count}} годин\"\n    }\n  }),\n  xHours: buildLocalizeTokenFn({\n    regular: {\n      singularNominative: \"{{count}} годину\",\n      singularGenitive: \"{{count}} години\",\n      pluralGenitive: \"{{count}} годин\"\n    }\n  }),\n  xDays: buildLocalizeTokenFn({\n    regular: {\n      singularNominative: \"{{count}} день\",\n      singularGenitive: \"{{count}} днi\",\n      pluralGenitive: \"{{count}} днів\"\n    }\n  }),\n  aboutXWeeks: buildLocalizeTokenFn({\n    regular: {\n      singularNominative: \"близько {{count}} тижня\",\n      singularGenitive: \"близько {{count}} тижнів\",\n      pluralGenitive: \"близько {{count}} тижнів\"\n    },\n    future: {\n      singularNominative: \"приблизно за {{count}} тиждень\",\n      singularGenitive: \"приблизно за {{count}} тижні\",\n      pluralGenitive: \"приблизно за {{count}} тижнів\"\n    }\n  }),\n  xWeeks: buildLocalizeTokenFn({\n    regular: {\n      singularNominative: \"{{count}} тиждень\",\n      singularGenitive: \"{{count}} тижні\",\n      pluralGenitive: \"{{count}} тижнів\"\n    }\n  }),\n  aboutXMonths: buildLocalizeTokenFn({\n    regular: {\n      singularNominative: \"близько {{count}} місяця\",\n      singularGenitive: \"близько {{count}} місяців\",\n      pluralGenitive: \"близько {{count}} місяців\"\n    },\n    future: {\n      singularNominative: \"приблизно за {{count}} місяць\",\n      singularGenitive: \"приблизно за {{count}} місяці\",\n      pluralGenitive: \"приблизно за {{count}} місяців\"\n    }\n  }),\n  xMonths: buildLocalizeTokenFn({\n    regular: {\n      singularNominative: \"{{count}} місяць\",\n      singularGenitive: \"{{count}} місяці\",\n      pluralGenitive: \"{{count}} місяців\"\n    }\n  }),\n  aboutXYears: buildLocalizeTokenFn({\n    regular: {\n      singularNominative: \"близько {{count}} року\",\n      singularGenitive: \"близько {{count}} років\",\n      pluralGenitive: \"близько {{count}} років\"\n    },\n    future: {\n      singularNominative: \"приблизно за {{count}} рік\",\n      singularGenitive: \"приблизно за {{count}} роки\",\n      pluralGenitive: \"приблизно за {{count}} років\"\n    }\n  }),\n  xYears: buildLocalizeTokenFn({\n    regular: {\n      singularNominative: \"{{count}} рік\",\n      singularGenitive: \"{{count}} роки\",\n      pluralGenitive: \"{{count}} років\"\n    }\n  }),\n  overXYears: buildLocalizeTokenFn({\n    regular: {\n      singularNominative: \"більше {{count}} року\",\n      singularGenitive: \"більше {{count}} років\",\n      pluralGenitive: \"більше {{count}} років\"\n    },\n    future: {\n      singularNominative: \"більше, ніж за {{count}} рік\",\n      singularGenitive: \"більше, ніж за {{count}} роки\",\n      pluralGenitive: \"більше, ніж за {{count}} років\"\n    }\n  }),\n  almostXYears: buildLocalizeTokenFn({\n    regular: {\n      singularNominative: \"майже {{count}} рік\",\n      singularGenitive: \"майже {{count}} роки\",\n      pluralGenitive: \"майже {{count}} років\"\n    },\n    future: {\n      singularNominative: \"майже за {{count}} рік\",\n      singularGenitive: \"майже за {{count}} роки\",\n      pluralGenitive: \"майже за {{count}} років\"\n    }\n  })\n};\nexport const formatDistance = (token, count, options) => {\n  options = options || {};\n  return formatDistanceLocale[token](count, options);\n};", "map": {"version": 3, "names": ["declension", "scheme", "count", "one", "undefined", "rem10", "rem100", "singularNominative", "replace", "String", "singularGenitive", "pluralGenitive", "buildLocalizeTokenFn", "options", "addSuffix", "comparison", "future", "regular", "past", "halfAtMinute", "_", "formatDistanceLocale", "lessThanXSeconds", "xSeconds", "halfAMinute", "lessThanXMinutes", "xMinutes", "aboutXHours", "xHours", "xDays", "aboutXWeeks", "xWeeks", "aboutXMonths", "xMonths", "aboutXYears", "xYears", "overXYears", "almostXYears", "formatDistance", "token"], "sources": ["C:/Users/<USER>/Desktop/demo-git/demo-react/my-fullstack-app/frontend/node_modules/date-fns/locale/uk/_lib/formatDistance.js"], "sourcesContent": ["function declension(scheme, count) {\n  // scheme for count=1 exists\n  if (scheme.one !== undefined && count === 1) {\n    return scheme.one;\n  }\n\n  const rem10 = count % 10;\n  const rem100 = count % 100;\n\n  // 1, 21, 31, ...\n  if (rem10 === 1 && rem100 !== 11) {\n    return scheme.singularNominative.replace(\"{{count}}\", String(count));\n\n    // 2, 3, 4, 22, 23, 24, 32 ...\n  } else if (rem10 >= 2 && rem10 <= 4 && (rem100 < 10 || rem100 > 20)) {\n    return scheme.singularGenitive.replace(\"{{count}}\", String(count));\n\n    // 5, 6, 7, 8, 9, 10, 11, ...\n  } else {\n    return scheme.pluralGenitive.replace(\"{{count}}\", String(count));\n  }\n}\n\nfunction buildLocalizeTokenFn(scheme) {\n  return (count, options) => {\n    if (options && options.addSuffix) {\n      if (options.comparison && options.comparison > 0) {\n        if (scheme.future) {\n          return declension(scheme.future, count);\n        } else {\n          return \"за \" + declension(scheme.regular, count);\n        }\n      } else {\n        if (scheme.past) {\n          return declension(scheme.past, count);\n        } else {\n          return declension(scheme.regular, count) + \" тому\";\n        }\n      }\n    } else {\n      return declension(scheme.regular, count);\n    }\n  };\n}\n\nconst halfAtMinute = (_, options) => {\n  if (options && options.addSuffix) {\n    if (options.comparison && options.comparison > 0) {\n      return \"за півхвилини\";\n    } else {\n      return \"півхвилини тому\";\n    }\n  }\n\n  return \"півхвилини\";\n};\n\nconst formatDistanceLocale = {\n  lessThanXSeconds: buildLocalizeTokenFn({\n    regular: {\n      one: \"менше секунди\",\n      singularNominative: \"менше {{count}} секунди\",\n      singularGenitive: \"менше {{count}} секунд\",\n      pluralGenitive: \"менше {{count}} секунд\",\n    },\n    future: {\n      one: \"менше, ніж за секунду\",\n      singularNominative: \"менше, ніж за {{count}} секунду\",\n      singularGenitive: \"менше, ніж за {{count}} секунди\",\n      pluralGenitive: \"менше, ніж за {{count}} секунд\",\n    },\n  }),\n\n  xSeconds: buildLocalizeTokenFn({\n    regular: {\n      singularNominative: \"{{count}} секунда\",\n      singularGenitive: \"{{count}} секунди\",\n      pluralGenitive: \"{{count}} секунд\",\n    },\n    past: {\n      singularNominative: \"{{count}} секунду тому\",\n      singularGenitive: \"{{count}} секунди тому\",\n      pluralGenitive: \"{{count}} секунд тому\",\n    },\n    future: {\n      singularNominative: \"за {{count}} секунду\",\n      singularGenitive: \"за {{count}} секунди\",\n      pluralGenitive: \"за {{count}} секунд\",\n    },\n  }),\n\n  halfAMinute: halfAtMinute,\n\n  lessThanXMinutes: buildLocalizeTokenFn({\n    regular: {\n      one: \"менше хвилини\",\n      singularNominative: \"менше {{count}} хвилини\",\n      singularGenitive: \"менше {{count}} хвилин\",\n      pluralGenitive: \"менше {{count}} хвилин\",\n    },\n    future: {\n      one: \"менше, ніж за хвилину\",\n      singularNominative: \"менше, ніж за {{count}} хвилину\",\n      singularGenitive: \"менше, ніж за {{count}} хвилини\",\n      pluralGenitive: \"менше, ніж за {{count}} хвилин\",\n    },\n  }),\n\n  xMinutes: buildLocalizeTokenFn({\n    regular: {\n      singularNominative: \"{{count}} хвилина\",\n      singularGenitive: \"{{count}} хвилини\",\n      pluralGenitive: \"{{count}} хвилин\",\n    },\n    past: {\n      singularNominative: \"{{count}} хвилину тому\",\n      singularGenitive: \"{{count}} хвилини тому\",\n      pluralGenitive: \"{{count}} хвилин тому\",\n    },\n    future: {\n      singularNominative: \"за {{count}} хвилину\",\n      singularGenitive: \"за {{count}} хвилини\",\n      pluralGenitive: \"за {{count}} хвилин\",\n    },\n  }),\n\n  aboutXHours: buildLocalizeTokenFn({\n    regular: {\n      singularNominative: \"близько {{count}} години\",\n      singularGenitive: \"близько {{count}} годин\",\n      pluralGenitive: \"близько {{count}} годин\",\n    },\n    future: {\n      singularNominative: \"приблизно за {{count}} годину\",\n      singularGenitive: \"приблизно за {{count}} години\",\n      pluralGenitive: \"приблизно за {{count}} годин\",\n    },\n  }),\n\n  xHours: buildLocalizeTokenFn({\n    regular: {\n      singularNominative: \"{{count}} годину\",\n      singularGenitive: \"{{count}} години\",\n      pluralGenitive: \"{{count}} годин\",\n    },\n  }),\n\n  xDays: buildLocalizeTokenFn({\n    regular: {\n      singularNominative: \"{{count}} день\",\n      singularGenitive: \"{{count}} днi\",\n      pluralGenitive: \"{{count}} днів\",\n    },\n  }),\n\n  aboutXWeeks: buildLocalizeTokenFn({\n    regular: {\n      singularNominative: \"близько {{count}} тижня\",\n      singularGenitive: \"близько {{count}} тижнів\",\n      pluralGenitive: \"близько {{count}} тижнів\",\n    },\n    future: {\n      singularNominative: \"приблизно за {{count}} тиждень\",\n      singularGenitive: \"приблизно за {{count}} тижні\",\n      pluralGenitive: \"приблизно за {{count}} тижнів\",\n    },\n  }),\n\n  xWeeks: buildLocalizeTokenFn({\n    regular: {\n      singularNominative: \"{{count}} тиждень\",\n      singularGenitive: \"{{count}} тижні\",\n      pluralGenitive: \"{{count}} тижнів\",\n    },\n  }),\n\n  aboutXMonths: buildLocalizeTokenFn({\n    regular: {\n      singularNominative: \"близько {{count}} місяця\",\n      singularGenitive: \"близько {{count}} місяців\",\n      pluralGenitive: \"близько {{count}} місяців\",\n    },\n    future: {\n      singularNominative: \"приблизно за {{count}} місяць\",\n      singularGenitive: \"приблизно за {{count}} місяці\",\n      pluralGenitive: \"приблизно за {{count}} місяців\",\n    },\n  }),\n\n  xMonths: buildLocalizeTokenFn({\n    regular: {\n      singularNominative: \"{{count}} місяць\",\n      singularGenitive: \"{{count}} місяці\",\n      pluralGenitive: \"{{count}} місяців\",\n    },\n  }),\n\n  aboutXYears: buildLocalizeTokenFn({\n    regular: {\n      singularNominative: \"близько {{count}} року\",\n      singularGenitive: \"близько {{count}} років\",\n      pluralGenitive: \"близько {{count}} років\",\n    },\n    future: {\n      singularNominative: \"приблизно за {{count}} рік\",\n      singularGenitive: \"приблизно за {{count}} роки\",\n      pluralGenitive: \"приблизно за {{count}} років\",\n    },\n  }),\n\n  xYears: buildLocalizeTokenFn({\n    regular: {\n      singularNominative: \"{{count}} рік\",\n      singularGenitive: \"{{count}} роки\",\n      pluralGenitive: \"{{count}} років\",\n    },\n  }),\n\n  overXYears: buildLocalizeTokenFn({\n    regular: {\n      singularNominative: \"більше {{count}} року\",\n      singularGenitive: \"більше {{count}} років\",\n      pluralGenitive: \"більше {{count}} років\",\n    },\n    future: {\n      singularNominative: \"більше, ніж за {{count}} рік\",\n      singularGenitive: \"більше, ніж за {{count}} роки\",\n      pluralGenitive: \"більше, ніж за {{count}} років\",\n    },\n  }),\n\n  almostXYears: buildLocalizeTokenFn({\n    regular: {\n      singularNominative: \"майже {{count}} рік\",\n      singularGenitive: \"майже {{count}} роки\",\n      pluralGenitive: \"майже {{count}} років\",\n    },\n    future: {\n      singularNominative: \"майже за {{count}} рік\",\n      singularGenitive: \"майже за {{count}} роки\",\n      pluralGenitive: \"майже за {{count}} років\",\n    },\n  }),\n};\n\nexport const formatDistance = (token, count, options) => {\n  options = options || {};\n\n  return formatDistanceLocale[token](count, options);\n};\n"], "mappings": "AAAA,SAASA,UAAUA,CAACC,MAAM,EAAEC,KAAK,EAAE;EACjC;EACA,IAAID,MAAM,CAACE,GAAG,KAAKC,SAAS,IAAIF,KAAK,KAAK,CAAC,EAAE;IAC3C,OAAOD,MAAM,CAACE,GAAG;EACnB;EAEA,MAAME,KAAK,GAAGH,KAAK,GAAG,EAAE;EACxB,MAAMI,MAAM,GAAGJ,KAAK,GAAG,GAAG;;EAE1B;EACA,IAAIG,KAAK,KAAK,CAAC,IAAIC,MAAM,KAAK,EAAE,EAAE;IAChC,OAAOL,MAAM,CAACM,kBAAkB,CAACC,OAAO,CAAC,WAAW,EAAEC,MAAM,CAACP,KAAK,CAAC,CAAC;;IAEpE;EACF,CAAC,MAAM,IAAIG,KAAK,IAAI,CAAC,IAAIA,KAAK,IAAI,CAAC,KAAKC,MAAM,GAAG,EAAE,IAAIA,MAAM,GAAG,EAAE,CAAC,EAAE;IACnE,OAAOL,MAAM,CAACS,gBAAgB,CAACF,OAAO,CAAC,WAAW,EAAEC,MAAM,CAACP,KAAK,CAAC,CAAC;;IAElE;EACF,CAAC,MAAM;IACL,OAAOD,MAAM,CAACU,cAAc,CAACH,OAAO,CAAC,WAAW,EAAEC,MAAM,CAACP,KAAK,CAAC,CAAC;EAClE;AACF;AAEA,SAASU,oBAAoBA,CAACX,MAAM,EAAE;EACpC,OAAO,CAACC,KAAK,EAAEW,OAAO,KAAK;IACzB,IAAIA,OAAO,IAAIA,OAAO,CAACC,SAAS,EAAE;MAChC,IAAID,OAAO,CAACE,UAAU,IAAIF,OAAO,CAACE,UAAU,GAAG,CAAC,EAAE;QAChD,IAAId,MAAM,CAACe,MAAM,EAAE;UACjB,OAAOhB,UAAU,CAACC,MAAM,CAACe,MAAM,EAAEd,KAAK,CAAC;QACzC,CAAC,MAAM;UACL,OAAO,KAAK,GAAGF,UAAU,CAACC,MAAM,CAACgB,OAAO,EAAEf,KAAK,CAAC;QAClD;MACF,CAAC,MAAM;QACL,IAAID,MAAM,CAACiB,IAAI,EAAE;UACf,OAAOlB,UAAU,CAACC,MAAM,CAACiB,IAAI,EAAEhB,KAAK,CAAC;QACvC,CAAC,MAAM;UACL,OAAOF,UAAU,CAACC,MAAM,CAACgB,OAAO,EAAEf,KAAK,CAAC,GAAG,OAAO;QACpD;MACF;IACF,CAAC,MAAM;MACL,OAAOF,UAAU,CAACC,MAAM,CAACgB,OAAO,EAAEf,KAAK,CAAC;IAC1C;EACF,CAAC;AACH;AAEA,MAAMiB,YAAY,GAAGA,CAACC,CAAC,EAAEP,OAAO,KAAK;EACnC,IAAIA,OAAO,IAAIA,OAAO,CAACC,SAAS,EAAE;IAChC,IAAID,OAAO,CAACE,UAAU,IAAIF,OAAO,CAACE,UAAU,GAAG,CAAC,EAAE;MAChD,OAAO,eAAe;IACxB,CAAC,MAAM;MACL,OAAO,iBAAiB;IAC1B;EACF;EAEA,OAAO,YAAY;AACrB,CAAC;AAED,MAAMM,oBAAoB,GAAG;EAC3BC,gBAAgB,EAAEV,oBAAoB,CAAC;IACrCK,OAAO,EAAE;MACPd,GAAG,EAAE,eAAe;MACpBI,kBAAkB,EAAE,yBAAyB;MAC7CG,gBAAgB,EAAE,wBAAwB;MAC1CC,cAAc,EAAE;IAClB,CAAC;IACDK,MAAM,EAAE;MACNb,GAAG,EAAE,uBAAuB;MAC5BI,kBAAkB,EAAE,iCAAiC;MACrDG,gBAAgB,EAAE,iCAAiC;MACnDC,cAAc,EAAE;IAClB;EACF,CAAC,CAAC;EAEFY,QAAQ,EAAEX,oBAAoB,CAAC;IAC7BK,OAAO,EAAE;MACPV,kBAAkB,EAAE,mBAAmB;MACvCG,gBAAgB,EAAE,mBAAmB;MACrCC,cAAc,EAAE;IAClB,CAAC;IACDO,IAAI,EAAE;MACJX,kBAAkB,EAAE,wBAAwB;MAC5CG,gBAAgB,EAAE,wBAAwB;MAC1CC,cAAc,EAAE;IAClB,CAAC;IACDK,MAAM,EAAE;MACNT,kBAAkB,EAAE,sBAAsB;MAC1CG,gBAAgB,EAAE,sBAAsB;MACxCC,cAAc,EAAE;IAClB;EACF,CAAC,CAAC;EAEFa,WAAW,EAAEL,YAAY;EAEzBM,gBAAgB,EAAEb,oBAAoB,CAAC;IACrCK,OAAO,EAAE;MACPd,GAAG,EAAE,eAAe;MACpBI,kBAAkB,EAAE,yBAAyB;MAC7CG,gBAAgB,EAAE,wBAAwB;MAC1CC,cAAc,EAAE;IAClB,CAAC;IACDK,MAAM,EAAE;MACNb,GAAG,EAAE,uBAAuB;MAC5BI,kBAAkB,EAAE,iCAAiC;MACrDG,gBAAgB,EAAE,iCAAiC;MACnDC,cAAc,EAAE;IAClB;EACF,CAAC,CAAC;EAEFe,QAAQ,EAAEd,oBAAoB,CAAC;IAC7BK,OAAO,EAAE;MACPV,kBAAkB,EAAE,mBAAmB;MACvCG,gBAAgB,EAAE,mBAAmB;MACrCC,cAAc,EAAE;IAClB,CAAC;IACDO,IAAI,EAAE;MACJX,kBAAkB,EAAE,wBAAwB;MAC5CG,gBAAgB,EAAE,wBAAwB;MAC1CC,cAAc,EAAE;IAClB,CAAC;IACDK,MAAM,EAAE;MACNT,kBAAkB,EAAE,sBAAsB;MAC1CG,gBAAgB,EAAE,sBAAsB;MACxCC,cAAc,EAAE;IAClB;EACF,CAAC,CAAC;EAEFgB,WAAW,EAAEf,oBAAoB,CAAC;IAChCK,OAAO,EAAE;MACPV,kBAAkB,EAAE,0BAA0B;MAC9CG,gBAAgB,EAAE,yBAAyB;MAC3CC,cAAc,EAAE;IAClB,CAAC;IACDK,MAAM,EAAE;MACNT,kBAAkB,EAAE,+BAA+B;MACnDG,gBAAgB,EAAE,+BAA+B;MACjDC,cAAc,EAAE;IAClB;EACF,CAAC,CAAC;EAEFiB,MAAM,EAAEhB,oBAAoB,CAAC;IAC3BK,OAAO,EAAE;MACPV,kBAAkB,EAAE,kBAAkB;MACtCG,gBAAgB,EAAE,kBAAkB;MACpCC,cAAc,EAAE;IAClB;EACF,CAAC,CAAC;EAEFkB,KAAK,EAAEjB,oBAAoB,CAAC;IAC1BK,OAAO,EAAE;MACPV,kBAAkB,EAAE,gBAAgB;MACpCG,gBAAgB,EAAE,eAAe;MACjCC,cAAc,EAAE;IAClB;EACF,CAAC,CAAC;EAEFmB,WAAW,EAAElB,oBAAoB,CAAC;IAChCK,OAAO,EAAE;MACPV,kBAAkB,EAAE,yBAAyB;MAC7CG,gBAAgB,EAAE,0BAA0B;MAC5CC,cAAc,EAAE;IAClB,CAAC;IACDK,MAAM,EAAE;MACNT,kBAAkB,EAAE,gCAAgC;MACpDG,gBAAgB,EAAE,8BAA8B;MAChDC,cAAc,EAAE;IAClB;EACF,CAAC,CAAC;EAEFoB,MAAM,EAAEnB,oBAAoB,CAAC;IAC3BK,OAAO,EAAE;MACPV,kBAAkB,EAAE,mBAAmB;MACvCG,gBAAgB,EAAE,iBAAiB;MACnCC,cAAc,EAAE;IAClB;EACF,CAAC,CAAC;EAEFqB,YAAY,EAAEpB,oBAAoB,CAAC;IACjCK,OAAO,EAAE;MACPV,kBAAkB,EAAE,0BAA0B;MAC9CG,gBAAgB,EAAE,2BAA2B;MAC7CC,cAAc,EAAE;IAClB,CAAC;IACDK,MAAM,EAAE;MACNT,kBAAkB,EAAE,+BAA+B;MACnDG,gBAAgB,EAAE,+BAA+B;MACjDC,cAAc,EAAE;IAClB;EACF,CAAC,CAAC;EAEFsB,OAAO,EAAErB,oBAAoB,CAAC;IAC5BK,OAAO,EAAE;MACPV,kBAAkB,EAAE,kBAAkB;MACtCG,gBAAgB,EAAE,kBAAkB;MACpCC,cAAc,EAAE;IAClB;EACF,CAAC,CAAC;EAEFuB,WAAW,EAAEtB,oBAAoB,CAAC;IAChCK,OAAO,EAAE;MACPV,kBAAkB,EAAE,wBAAwB;MAC5CG,gBAAgB,EAAE,yBAAyB;MAC3CC,cAAc,EAAE;IAClB,CAAC;IACDK,MAAM,EAAE;MACNT,kBAAkB,EAAE,4BAA4B;MAChDG,gBAAgB,EAAE,6BAA6B;MAC/CC,cAAc,EAAE;IAClB;EACF,CAAC,CAAC;EAEFwB,MAAM,EAAEvB,oBAAoB,CAAC;IAC3BK,OAAO,EAAE;MACPV,kBAAkB,EAAE,eAAe;MACnCG,gBAAgB,EAAE,gBAAgB;MAClCC,cAAc,EAAE;IAClB;EACF,CAAC,CAAC;EAEFyB,UAAU,EAAExB,oBAAoB,CAAC;IAC/BK,OAAO,EAAE;MACPV,kBAAkB,EAAE,uBAAuB;MAC3CG,gBAAgB,EAAE,wBAAwB;MAC1CC,cAAc,EAAE;IAClB,CAAC;IACDK,MAAM,EAAE;MACNT,kBAAkB,EAAE,8BAA8B;MAClDG,gBAAgB,EAAE,+BAA+B;MACjDC,cAAc,EAAE;IAClB;EACF,CAAC,CAAC;EAEF0B,YAAY,EAAEzB,oBAAoB,CAAC;IACjCK,OAAO,EAAE;MACPV,kBAAkB,EAAE,qBAAqB;MACzCG,gBAAgB,EAAE,sBAAsB;MACxCC,cAAc,EAAE;IAClB,CAAC;IACDK,MAAM,EAAE;MACNT,kBAAkB,EAAE,wBAAwB;MAC5CG,gBAAgB,EAAE,yBAAyB;MAC3CC,cAAc,EAAE;IAClB;EACF,CAAC;AACH,CAAC;AAED,OAAO,MAAM2B,cAAc,GAAGA,CAACC,KAAK,EAAErC,KAAK,EAAEW,OAAO,KAAK;EACvDA,OAAO,GAAGA,OAAO,IAAI,CAAC,CAAC;EAEvB,OAAOQ,oBAAoB,CAACkB,KAAK,CAAC,CAACrC,KAAK,EAAEW,OAAO,CAAC;AACpD,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
-- 測試 IP 封鎖狀態判斷功能
-- 創建不同狀態的測試資料

-- 清理舊的測試資料
DELETE FROM IpBlock WHERE IPAddress LIKE '192.168.100.%';

-- 插入測試資料
INSERT INTO IpBlock (IPAddress, CreatedAt, UpdatedAt, ExpiredAt) VALUES
-- 1. 已封鎖（未來到期）
('*************', NOW(), NOW(), DATE_ADD(NOW(), INTERVAL 1 DAY)),
('*************', NOW(), NOW(), DATE_ADD(NOW(), INTERVAL 3 HOUR)),
('*************', NOW(), NOW(), DATE_ADD(NOW(), INTERVAL 30 MINUTE)),

-- 2. 已解鎖（過去到期）
('*************', DATE_SUB(NOW(), INTERVAL 2 DAY), DATE_SUB(NOW(), INTERVAL 2 DAY), DATE_SUB(NOW(), INTERVAL 1 DAY)),
('*************', DATE_SUB(NOW(), INTERVAL 1 DAY), DATE_SUB(NOW(), INTERVAL 1 DAY), DATE_SUB(NOW(), INTERVAL 1 HOUR)),

-- 3. 同一 IP 的多筆記錄（測試新的約束）
('*************', DATE_SUB(NOW(), INTERVAL 3 DAY), DATE_SUB(NOW(), INTERVAL 3 DAY), DATE_SUB(NOW(), INTERVAL 2 DAY)), -- 已過期
('*************', NOW(), NOW(), DATE_ADD(NOW(), INTERVAL 2 DAY)), -- 目前活躍

-- 4. 即將到期（1分鐘後）
('*************', NOW(), NOW(), DATE_ADD(NOW(), INTERVAL 1 MINUTE));

-- 查看測試資料
SELECT 
    Id,
    IPAddress,
    CreatedAt,
    UpdatedAt,
    ExpiredAt,
    CASE 
        WHEN ExpiredAt > NOW() THEN '已封鎖'
        ELSE '已解鎖'
    END as CurrentStatus,
    CASE 
        WHEN ExpiredAt > NOW() THEN TIMESTAMPDIFF(MINUTE, NOW(), ExpiredAt)
        ELSE TIMESTAMPDIFF(MINUTE, ExpiredAt, NOW())
    END as MinutesFromNow,
    CASE 
        WHEN ExpiredAt > NOW() THEN CONCAT('還有 ', TIMESTAMPDIFF(MINUTE, NOW(), ExpiredAt), ' 分鐘到期')
        ELSE CONCAT('已過期 ', TIMESTAMPDIFF(MINUTE, ExpiredAt, NOW()), ' 分鐘')
    END as TimeDescription
FROM IpBlock 
WHERE IPAddress LIKE '192.168.100.%'
ORDER BY IPAddress, CreatedAt DESC;

-- 統計不同狀態的數量
SELECT 
    CASE 
        WHEN ExpiredAt > NOW() THEN '已封鎖'
        ELSE '已解鎖'
    END as Status,
    COUNT(*) as Count
FROM IpBlock 
WHERE IPAddress LIKE '192.168.100.%'
GROUP BY (ExpiredAt > NOW())
ORDER BY Status;

SELECT '=== 測試資料已創建 ===' as message;
SELECT '前端頁面現在會根據當前時間動態判斷封鎖狀態' as note;
SELECT '只有已封鎖的 IP 才會顯示解鎖按鈕' as feature;

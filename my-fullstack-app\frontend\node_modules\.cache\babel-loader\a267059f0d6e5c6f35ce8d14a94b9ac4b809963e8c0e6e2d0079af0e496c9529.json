{"ast": null, "code": "import React,{useRef}from'react';import{useNavigate}from\"react-router-dom\";import{<PERSON><PERSON>}from\"primereact/button\";import{<PERSON><PERSON><PERSON>}from\"primereact/menubar\";import{Toast}from\"primereact/toast\";import{log}from'../../utils/logger';import{useAuth}from'../../contexts/AuthContext';import useMenu from'../../hooks/useMenu';import{usePermissions}from'../../hooks/usePermissions';// 導入 usePermissions\nimport BreadcrumbNav from'../Common/BreadcrumbNav';import LoadingSpinner from'../Common/LoadingSpinner';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";// 輔助函數：將菜單路徑映射到權限代碼\nconst getPermissionCodeForPath=path=>{switch(path){case'/doctors':return'doctors.read';case'/doctor-detail':return'doctors.detail.read';case'/treatments':return'treatments.read';case'/treatment-detail':return'treatments.read';case'/patients':return'patients.read';case'/patient-detail':return'patients.read';case'/schedules':return'schedules.read';case'/receipts':return'receipts.read';case'/receipt-detail':return'receipts.read';case'/users':return'userroles.read';case'/backup-management':return'backup.read';case'/report-management':return'reports.read';case'/image-management':return'images.read';case'/login-logs':return'loginlogs.read';case'/ip-blocks':return'ipblocks.read';case'/debug':return'debug.read';case'/update-password':return'users.updatepassword';case'/permission-management':return'permissions.read';default:return null;}};const Layout=_ref=>{let{children}=_ref;const navigate=useNavigate();const{isAuthenticated,logout:authLogout,user}=useAuth();const{menu,loading:menuLoading}=useMenu(isAuthenticated);const{hasPermission,loading:permissionsLoading}=usePermissions();// 獲取 hasPermission 和 loading 狀態\nconst toast=useRef(null);const handleLogout=()=>{var _toast$current;log.auth('Layout: 執行登出');authLogout();(_toast$current=toast.current)===null||_toast$current===void 0?void 0:_toast$current.show({severity:'success',summary:'登出成功',detail:'您已成功登出系統',life:2000});setTimeout(()=>{log.route('Layout: 導航到登入頁面');navigate(\"/login\",{replace:true});},1000);};if(menuLoading||permissionsLoading){return/*#__PURE__*/_jsx(LoadingSpinner,{message:\"\\u8F09\\u5165\\u9078\\u55AE\\u4E2D...\"});}const HeaderItems=[{label:\"首頁\",icon:\"pi pi-home\",command:()=>{navigate(\"/\");}}];menu.forEach(group=>{const templist=group.menus.filter(data=>data.isEnabled)// 確保菜單啟用\n.filter(data=>!data.path.includes('detail'))// 過濾掉詳情頁面\n.filter(data=>{const permissionCode=getPermissionCodeForPath(data.path);return permissionCode?hasPermission(permissionCode):true;// 如果有對應權限代碼，則檢查權限\n}).map(data=>({key:data.itemId,label:data.name,command:()=>{navigate(data.path);}}));if(templist.length>0){HeaderItems.push({label:group.groupName,icon:group.groupIcon,items:templist});}});const getUserDisplayName=()=>{if(user!==null&&user!==void 0&&user.username){return user.username+\" 治療師\";}const storedUsername=localStorage.getItem('username');return storedUsername||'用戶';};const HeaderEndItems=/*#__PURE__*/_jsxs(\"div\",{className:\"flex align-items-center gap-2\",children:[/*#__PURE__*/_jsxs(\"label\",{className:\"font-bold block\",children:[getUserDisplayName(),\"\\u60A8\\u597D\"]}),/*#__PURE__*/_jsx(Button,{icon:\"pi pi-sign-out\",label:\"\\u767B\\u51FA\",className:\"p-button-text\",onClick:handleLogout})]});return/*#__PURE__*/_jsxs(\"div\",{className:\"layout-wrapper min-h-screen flex flex-column\",children:[/*#__PURE__*/_jsx(Toast,{ref:toast}),/*#__PURE__*/_jsx(Menubar,{model:HeaderItems,end:HeaderEndItems,className:\"border-none\"}),/*#__PURE__*/_jsx(\"div\",{className:\"breadcrumb-container p-3 bg-gray-50 surface-border\",children:/*#__PURE__*/_jsx(BreadcrumbNav,{})}),/*#__PURE__*/_jsx(\"div\",{className:\"main-content flex-1 p-3\",children:children})]});};export default Layout;", "map": {"version": 3, "names": ["React", "useRef", "useNavigate", "<PERSON><PERSON>", "Men<PERSON><PERSON>", "Toast", "log", "useAuth", "useMenu", "usePermissions", "BreadcrumbNav", "LoadingSpinner", "jsx", "_jsx", "jsxs", "_jsxs", "getPermissionCodeForPath", "path", "Layout", "_ref", "children", "navigate", "isAuthenticated", "logout", "authLogout", "user", "menu", "loading", "menuLoading", "hasPermission", "permissionsLoading", "toast", "handleLogout", "_toast$current", "auth", "current", "show", "severity", "summary", "detail", "life", "setTimeout", "route", "replace", "message", "HeaderItems", "label", "icon", "command", "for<PERSON>ach", "group", "templist", "menus", "filter", "data", "isEnabled", "includes", "permissionCode", "map", "key", "itemId", "name", "length", "push", "groupName", "groupIcon", "items", "getUserDisplayName", "username", "storedUsername", "localStorage", "getItem", "HeaderEndItems", "className", "onClick", "ref", "model", "end"], "sources": ["C:/Users/<USER>/Desktop/demo-git/demo-react/my-fullstack-app/frontend/src/components/Layout/Layout.tsx"], "sourcesContent": ["import React, { ReactNode, useRef } from 'react';\nimport { useNavigate } from \"react-router-dom\";\n\nimport { But<PERSON> } from \"primereact/button\";\nimport { <PERSON><PERSON><PERSON> } from \"primereact/menubar\";\nimport { MenuItem } from \"primereact/menuitem\";\nimport { Toast } from \"primereact/toast\";\nimport { log } from '../../utils/logger';\n\nimport { useAuth } from '../../contexts/AuthContext';\nimport useMenu from '../../hooks/useMenu';\nimport { usePermissions } from '../../hooks/usePermissions'; // 導入 usePermissions\nimport BreadcrumbNav from '../Common/BreadcrumbNav';\nimport LoadingSpinner from '../Common/LoadingSpinner';\n\ninterface LayoutProps {\n  children: ReactNode;\n}\n\n// 輔助函數：將菜單路徑映射到權限代碼\nconst getPermissionCodeForPath = (path: string): string | null => {\n  switch (path) {\n    case '/doctors':\n      return 'doctors.read';\n    case '/doctor-detail':\n      return 'doctors.detail.read';\n    case '/treatments':\n      return 'treatments.read';\n    case '/treatment-detail':\n      return 'treatments.read';\n    case '/patients':\n      return 'patients.read';\n    case '/patient-detail':\n      return 'patients.read';\n    case '/schedules':\n      return 'schedules.read';\n    case '/receipts':\n      return 'receipts.read';\n    case '/receipt-detail':\n      return 'receipts.read';\n    case '/users':\n      return 'userroles.read';\n    case '/backup-management':\n      return 'backup.read';\n    case '/report-management':\n      return 'reports.read';\n    case '/image-management':\n      return 'images.read';\n    case '/login-logs':\n      return 'loginlogs.read';\n    case '/ip-blocks':\n      return 'ipblocks.read';\n    case '/debug':\n      return 'debug.read';\n    case '/update-password':\n      return 'users.updatepassword';\n    case '/permission-management':\n      return 'permissions.read';\n    default:\n      return null;\n  }\n};\n\nconst Layout: React.FC<LayoutProps> = ({ children }) => {\n  const navigate = useNavigate();\n  const { isAuthenticated, logout: authLogout, user } = useAuth();\n  const { menu, loading: menuLoading } = useMenu(isAuthenticated);\n  const { hasPermission, loading: permissionsLoading } = usePermissions(); // 獲取 hasPermission 和 loading 狀態\n  const toast = useRef<Toast>(null);\n\n  const handleLogout = () => {\n    log.auth('Layout: 執行登出');\n    authLogout();\n\n    toast.current?.show({\n      severity: 'success',\n      summary: '登出成功',\n      detail: '您已成功登出系統',\n      life: 2000\n    });\n\n    setTimeout(() => {\n      log.route('Layout: 導航到登入頁面');\n      navigate(\"/login\", { replace: true });\n    }, 1000);\n  };\n\n  if (menuLoading || permissionsLoading) {\n    return <LoadingSpinner message=\"載入選單中...\" />;\n  }\n\n  const HeaderItems: MenuItem[] = [\n    {\n      label: \"首頁\",\n      icon: \"pi pi-home\",\n      command: () => {\n        navigate(\"/\");\n      }\n    }\n  ];\n\n  menu.forEach((group) => {\n    const templist = group.menus\n      .filter((data) => data.isEnabled) // 確保菜單啟用\n      .filter((data) => !data.path.includes('detail')) // 過濾掉詳情頁面\n      .filter((data) => {\n        const permissionCode = getPermissionCodeForPath(data.path);\n        return permissionCode ? hasPermission(permissionCode) : true; // 如果有對應權限代碼，則檢查權限\n      })\n      .map((data) => ({\n        key: data.itemId,\n        label: data.name,\n        command: () => {\n          navigate(data.path);\n        }\n      }));\n\n    if (templist.length > 0) {\n      HeaderItems.push({\n        label: group.groupName,\n        icon: group.groupIcon,\n        items: templist,\n      });\n    }\n  });\n\n  const getUserDisplayName = () => {\n    if (user?.username) {\n      return user.username + \" 治療師\";\n    }\n    const storedUsername = localStorage.getItem('username');\n    return storedUsername || '用戶';\n  };\n\n  const HeaderEndItems = (\n    <div className=\"flex align-items-center gap-2\">\n      <label className=\"font-bold block\">{getUserDisplayName()}您好</label>\n      <Button\n        icon=\"pi pi-sign-out\"\n        label=\"登出\"\n        className=\"p-button-text\"\n        onClick={handleLogout}\n      />\n    </div>\n  );\n\n  return (\n    <div className=\"layout-wrapper min-h-screen flex flex-column\">\n      <Toast ref={toast} />\n      <Menubar model={HeaderItems} end={HeaderEndItems} className=\"border-none\" />\n      <div className=\"breadcrumb-container p-3 bg-gray-50 surface-border\">\n        <BreadcrumbNav />\n      </div>\n      <div className=\"main-content flex-1 p-3\">\n        {children}\n      </div>\n    </div>\n  );\n};\n\nexport default Layout;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAeC,MAAM,KAAQ,OAAO,CAChD,OAASC,WAAW,KAAQ,kBAAkB,CAE9C,OAASC,MAAM,KAAQ,mBAAmB,CAC1C,OAASC,OAAO,KAAQ,oBAAoB,CAE5C,OAASC,KAAK,KAAQ,kBAAkB,CACxC,OAASC,GAAG,KAAQ,oBAAoB,CAExC,OAASC,OAAO,KAAQ,4BAA4B,CACpD,MAAO,CAAAC,OAAO,KAAM,qBAAqB,CACzC,OAASC,cAAc,KAAQ,4BAA4B,CAAE;AAC7D,MAAO,CAAAC,aAAa,KAAM,yBAAyB,CACnD,MAAO,CAAAC,cAAc,KAAM,0BAA0B,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAMtD;AACA,KAAM,CAAAC,wBAAwB,CAAIC,IAAY,EAAoB,CAChE,OAAQA,IAAI,EACV,IAAK,UAAU,CACb,MAAO,cAAc,CACvB,IAAK,gBAAgB,CACnB,MAAO,qBAAqB,CAC9B,IAAK,aAAa,CAChB,MAAO,iBAAiB,CAC1B,IAAK,mBAAmB,CACtB,MAAO,iBAAiB,CAC1B,IAAK,WAAW,CACd,MAAO,eAAe,CACxB,IAAK,iBAAiB,CACpB,MAAO,eAAe,CACxB,IAAK,YAAY,CACf,MAAO,gBAAgB,CACzB,IAAK,WAAW,CACd,MAAO,eAAe,CACxB,IAAK,iBAAiB,CACpB,MAAO,eAAe,CACxB,IAAK,QAAQ,CACX,MAAO,gBAAgB,CACzB,IAAK,oBAAoB,CACvB,MAAO,aAAa,CACtB,IAAK,oBAAoB,CACvB,MAAO,cAAc,CACvB,IAAK,mBAAmB,CACtB,MAAO,aAAa,CACtB,IAAK,aAAa,CAChB,MAAO,gBAAgB,CACzB,IAAK,YAAY,CACf,MAAO,eAAe,CACxB,IAAK,QAAQ,CACX,MAAO,YAAY,CACrB,IAAK,kBAAkB,CACrB,MAAO,sBAAsB,CAC/B,IAAK,wBAAwB,CAC3B,MAAO,kBAAkB,CAC3B,QACE,MAAO,KAAI,CACf,CACF,CAAC,CAED,KAAM,CAAAC,MAA6B,CAAGC,IAAA,EAAkB,IAAjB,CAAEC,QAAS,CAAC,CAAAD,IAAA,CACjD,KAAM,CAAAE,QAAQ,CAAGnB,WAAW,CAAC,CAAC,CAC9B,KAAM,CAAEoB,eAAe,CAAEC,MAAM,CAAEC,UAAU,CAAEC,IAAK,CAAC,CAAGlB,OAAO,CAAC,CAAC,CAC/D,KAAM,CAAEmB,IAAI,CAAEC,OAAO,CAAEC,WAAY,CAAC,CAAGpB,OAAO,CAACc,eAAe,CAAC,CAC/D,KAAM,CAAEO,aAAa,CAAEF,OAAO,CAAEG,kBAAmB,CAAC,CAAGrB,cAAc,CAAC,CAAC,CAAE;AACzE,KAAM,CAAAsB,KAAK,CAAG9B,MAAM,CAAQ,IAAI,CAAC,CAEjC,KAAM,CAAA+B,YAAY,CAAGA,CAAA,GAAM,KAAAC,cAAA,CACzB3B,GAAG,CAAC4B,IAAI,CAAC,cAAc,CAAC,CACxBV,UAAU,CAAC,CAAC,CAEZ,CAAAS,cAAA,CAAAF,KAAK,CAACI,OAAO,UAAAF,cAAA,iBAAbA,cAAA,CAAeG,IAAI,CAAC,CAClBC,QAAQ,CAAE,SAAS,CACnBC,OAAO,CAAE,MAAM,CACfC,MAAM,CAAE,UAAU,CAClBC,IAAI,CAAE,IACR,CAAC,CAAC,CAEFC,UAAU,CAAC,IAAM,CACfnC,GAAG,CAACoC,KAAK,CAAC,iBAAiB,CAAC,CAC5BrB,QAAQ,CAAC,QAAQ,CAAE,CAAEsB,OAAO,CAAE,IAAK,CAAC,CAAC,CACvC,CAAC,CAAE,IAAI,CAAC,CACV,CAAC,CAED,GAAIf,WAAW,EAAIE,kBAAkB,CAAE,CACrC,mBAAOjB,IAAA,CAACF,cAAc,EAACiC,OAAO,CAAC,mCAAU,CAAE,CAAC,CAC9C,CAEA,KAAM,CAAAC,WAAuB,CAAG,CAC9B,CACEC,KAAK,CAAE,IAAI,CACXC,IAAI,CAAE,YAAY,CAClBC,OAAO,CAAEA,CAAA,GAAM,CACb3B,QAAQ,CAAC,GAAG,CAAC,CACf,CACF,CAAC,CACF,CAEDK,IAAI,CAACuB,OAAO,CAAEC,KAAK,EAAK,CACtB,KAAM,CAAAC,QAAQ,CAAGD,KAAK,CAACE,KAAK,CACzBC,MAAM,CAAEC,IAAI,EAAKA,IAAI,CAACC,SAAS,CAAE;AAAA,CACjCF,MAAM,CAAEC,IAAI,EAAK,CAACA,IAAI,CAACrC,IAAI,CAACuC,QAAQ,CAAC,QAAQ,CAAC,CAAE;AAAA,CAChDH,MAAM,CAAEC,IAAI,EAAK,CAChB,KAAM,CAAAG,cAAc,CAAGzC,wBAAwB,CAACsC,IAAI,CAACrC,IAAI,CAAC,CAC1D,MAAO,CAAAwC,cAAc,CAAG5B,aAAa,CAAC4B,cAAc,CAAC,CAAG,IAAI,CAAE;AAChE,CAAC,CAAC,CACDC,GAAG,CAAEJ,IAAI,GAAM,CACdK,GAAG,CAAEL,IAAI,CAACM,MAAM,CAChBd,KAAK,CAAEQ,IAAI,CAACO,IAAI,CAChBb,OAAO,CAAEA,CAAA,GAAM,CACb3B,QAAQ,CAACiC,IAAI,CAACrC,IAAI,CAAC,CACrB,CACF,CAAC,CAAC,CAAC,CAEL,GAAIkC,QAAQ,CAACW,MAAM,CAAG,CAAC,CAAE,CACvBjB,WAAW,CAACkB,IAAI,CAAC,CACfjB,KAAK,CAAEI,KAAK,CAACc,SAAS,CACtBjB,IAAI,CAAEG,KAAK,CAACe,SAAS,CACrBC,KAAK,CAAEf,QACT,CAAC,CAAC,CACJ,CACF,CAAC,CAAC,CAEF,KAAM,CAAAgB,kBAAkB,CAAGA,CAAA,GAAM,CAC/B,GAAI1C,IAAI,SAAJA,IAAI,WAAJA,IAAI,CAAE2C,QAAQ,CAAE,CAClB,MAAO,CAAA3C,IAAI,CAAC2C,QAAQ,CAAG,MAAM,CAC/B,CACA,KAAM,CAAAC,cAAc,CAAGC,YAAY,CAACC,OAAO,CAAC,UAAU,CAAC,CACvD,MAAO,CAAAF,cAAc,EAAI,IAAI,CAC/B,CAAC,CAED,KAAM,CAAAG,cAAc,cAClBzD,KAAA,QAAK0D,SAAS,CAAC,+BAA+B,CAAArD,QAAA,eAC5CL,KAAA,UAAO0D,SAAS,CAAC,iBAAiB,CAAArD,QAAA,EAAE+C,kBAAkB,CAAC,CAAC,CAAC,cAAE,EAAO,CAAC,cACnEtD,IAAA,CAACV,MAAM,EACL4C,IAAI,CAAC,gBAAgB,CACrBD,KAAK,CAAC,cAAI,CACV2B,SAAS,CAAC,eAAe,CACzBC,OAAO,CAAE1C,YAAa,CACvB,CAAC,EACC,CACN,CAED,mBACEjB,KAAA,QAAK0D,SAAS,CAAC,8CAA8C,CAAArD,QAAA,eAC3DP,IAAA,CAACR,KAAK,EAACsE,GAAG,CAAE5C,KAAM,CAAE,CAAC,cACrBlB,IAAA,CAACT,OAAO,EAACwE,KAAK,CAAE/B,WAAY,CAACgC,GAAG,CAAEL,cAAe,CAACC,SAAS,CAAC,aAAa,CAAE,CAAC,cAC5E5D,IAAA,QAAK4D,SAAS,CAAC,oDAAoD,CAAArD,QAAA,cACjEP,IAAA,CAACH,aAAa,GAAE,CAAC,CACd,CAAC,cACNG,IAAA,QAAK4D,SAAS,CAAC,yBAAyB,CAAArD,QAAA,CACrCA,QAAQ,CACN,CAAC,EACH,CAAC,CAEV,CAAC,CAED,cAAe,CAAAF,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
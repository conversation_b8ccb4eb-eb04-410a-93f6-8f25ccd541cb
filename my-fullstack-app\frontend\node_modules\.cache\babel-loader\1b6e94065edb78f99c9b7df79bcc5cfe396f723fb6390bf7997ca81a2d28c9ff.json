{"ast": null, "code": "const formatRelativeLocale = {\n  lastWeek: date => {\n    const weekday = date.getDay();\n    const last = weekday === 0 || weekday === 6 ? \"último\" : \"última\";\n    return \"'\" + last + \"' eeee 'às' p\";\n  },\n  yesterday: \"'ontem às' p\",\n  today: \"'hoje às' p\",\n  tomorrow: \"'amanhã às' p\",\n  nextWeek: \"eeee 'às' p\",\n  other: \"P\"\n};\nexport const formatRelative = (token, date, _baseDate, _options) => {\n  const format = formatRelativeLocale[token];\n  if (typeof format === \"function\") {\n    return format(date);\n  }\n  return format;\n};", "map": {"version": 3, "names": ["formatRelativeLocale", "lastWeek", "date", "weekday", "getDay", "last", "yesterday", "today", "tomorrow", "nextWeek", "other", "formatRelative", "token", "_baseDate", "_options", "format"], "sources": ["C:/Users/<USER>/Desktop/demo-git/demo-react/my-fullstack-app/frontend/node_modules/date-fns/locale/pt/_lib/formatRelative.js"], "sourcesContent": ["const formatRelativeLocale = {\n  lastWeek: (date) => {\n    const weekday = date.getDay();\n    const last = weekday === 0 || weekday === 6 ? \"último\" : \"última\";\n    return \"'\" + last + \"' eeee 'às' p\";\n  },\n  yesterday: \"'ontem às' p\",\n  today: \"'hoje às' p\",\n  tomorrow: \"'amanhã às' p\",\n  nextWeek: \"eeee 'às' p\",\n  other: \"P\",\n};\n\nexport const formatRelative = (token, date, _baseDate, _options) => {\n  const format = formatRelativeLocale[token];\n\n  if (typeof format === \"function\") {\n    return format(date);\n  }\n\n  return format;\n};\n"], "mappings": "AAAA,MAAMA,oBAAoB,GAAG;EAC3BC,QAAQ,EAAGC,IAAI,IAAK;IAClB,MAAMC,OAAO,GAAGD,IAAI,CAACE,MAAM,CAAC,CAAC;IAC7B,MAAMC,IAAI,GAAGF,OAAO,KAAK,CAAC,IAAIA,OAAO,KAAK,CAAC,GAAG,QAAQ,GAAG,QAAQ;IACjE,OAAO,GAAG,GAAGE,IAAI,GAAG,eAAe;EACrC,CAAC;EACDC,SAAS,EAAE,cAAc;EACzBC,KAAK,EAAE,aAAa;EACpBC,QAAQ,EAAE,eAAe;EACzBC,QAAQ,EAAE,aAAa;EACvBC,KAAK,EAAE;AACT,CAAC;AAED,OAAO,MAAMC,cAAc,GAAGA,CAACC,KAAK,EAAEV,IAAI,EAAEW,SAAS,EAAEC,QAAQ,KAAK;EAClE,MAAMC,MAAM,GAAGf,oBAAoB,CAACY,KAAK,CAAC;EAE1C,IAAI,OAAOG,MAAM,KAAK,UAAU,EAAE;IAChC,OAAOA,MAAM,CAACb,IAAI,CAAC;EACrB;EAEA,OAAOa,MAAM;AACf,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
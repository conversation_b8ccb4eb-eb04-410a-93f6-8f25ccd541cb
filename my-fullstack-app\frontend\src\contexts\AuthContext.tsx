import React, { createContext, ReactNode, useContext, useEffect, useState } from 'react';
import { AuthApi } from '../services/apiService';
import { log } from '../utils/logger';

interface User {
  id: number;
  username: string;
  email?: string;
  role?: string;
}

interface LoginResult {
  isDefaultPassword: boolean;
  userId: number;
  username: string;
}

interface AuthContextType {
  user: User | null;
  token: string | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  login: (credentials: { username: string; password: string }) => Promise<LoginResult>;
  logout: () => void;
  refreshToken: () => Promise<void>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

interface AuthProviderProps {
  children: ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [token, setToken] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    // Check for existing token on mount
    const storedToken = localStorage.getItem('token');
    const storedUserId = localStorage.getItem('userId');
    const storedUsername = localStorage.getItem('username');

    log.auth('初始化檢查', {
      hasToken: !!storedToken,
      hasUserId: !!storedUserId,
      hasUsername: !!storedUsername
    });

    if (storedToken && storedUserId && storedUsername) {
      setToken(storedToken);
      setUser({
        id: parseInt(storedUserId),
        username: storedUsername
      });
      log.auth('從 localStorage 恢復用戶狀態');
    } else {
      // 清除不完整的登入狀態
      localStorage.removeItem('token');
      localStorage.removeItem('userId');
      localStorage.removeItem('username');
      localStorage.removeItem('isDefaultPassword');
      log.auth('清除不完整的登入狀態');
    }
    setIsLoading(false);
  }, []);

  const login = async (credentials: { username: string; password: string }): Promise<LoginResult> => {
    try {
      log.auth('開始登入流程', { username: credentials.username });
      setIsLoading(true);

      // 調用標準化的 API 服務
      const res = await AuthApi.login(credentials);

      log.auth('API 回應', res);

      // 從 API 回應中獲取數據
      const { token, username, userId, isDefaultPassword } = res;
      if (token) {
        // 創建用戶對象
        const user = {
          id: userId,
          username: username,
        };

        log.auth('設置 token 和 user', { token: !!token, user, isDefaultPassword });
        setToken(token);
        setUser(user);
        localStorage.setItem('token', token);
        localStorage.setItem('userId', userId.toString());
        localStorage.setItem('username', username);
        localStorage.setItem('isDefaultPassword', isDefaultPassword.toString());
        log.auth('登入成功');

        return {
          isDefaultPassword: isDefaultPassword || false,
          userId,
          username
        };
      } else {
        throw new Error('API 回應格式錯誤：缺少 token');
      }
    } catch (error: any) {
      log.error('AuthContext: 登入失敗', error);
      // 錯誤已經在 api.ts 中被標準化，直接拋出即可
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const logout = () => {
    setUser(null);
    setToken(null);
    localStorage.removeItem('token');
    localStorage.removeItem('userId');
    localStorage.removeItem('username');
    localStorage.removeItem('isDefaultPassword');

    AuthApi.logout(user?.id as number).catch((error) => log.error('登出API調用失敗', error));
  };

  const refreshToken = async () => {
    try {
      const response = await AuthApi.refreshToken();
      setToken(response.token);
      localStorage.setItem('token', response.token);
    } catch (error) {
      log.error('Token refresh failed:', error);
      logout();
      throw error;
    }
  };

  const value: AuthContextType = {
    user,
    token,
    isAuthenticated: !!token && !!user,
    isLoading,
    login,
    logout,
    refreshToken,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = (): AuthContextType => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

-- 修改 IpBlock 表的 IPAddress 欄位約束
-- 將 UNIQUE 約束改為普通索引（MUL），允許同一 IP 有多筆記錄

-- 1. 檢查當前表結構
DESCRIBE IpBlock;

-- 2. 顯示當前索引
SHOW INDEX FROM IpBlock;

-- 3. 刪除 UNIQUE 約束
-- 首先找到約束名稱
SELECT 
    CONSTRAINT_NAME,
    CONSTRAINT_TYPE,
    COLUMN_NAME
FROM 
    INFORMATION_SCHEMA.KEY_COLUMN_USAGE 
WHERE 
    TABLE_SCHEMA = DATABASE() 
    AND TABLE_NAME = 'IpBlock' 
    AND COLUMN_NAME = 'IPAddress';

-- 刪除 UNIQUE 約束（如果存在）
SET @sql = (
    SELECT CONCAT('ALTER TABLE IpBlock DROP INDEX ', CONSTRAINT_NAME)
    FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE 
    WHERE TABLE_SCHEMA = DATABASE() 
        AND TABLE_NAME = 'IpBlock' 
        AND COLUMN_NAME = 'IPAddress'
        AND CONSTRAINT_NAME != 'PRIMARY'
    LIMIT 1
);

-- 執行刪除約束的 SQL（如果存在）
SET @sql = IFNULL(@sql, 'SELECT "No unique constraint found on IPAddress" as message');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 4. 添加普通索引（如果不存在）
-- 檢查是否已有 idx_ipaddress 索引
SET @index_exists = (
    SELECT COUNT(*)
    FROM INFORMATION_SCHEMA.STATISTICS 
    WHERE TABLE_SCHEMA = DATABASE() 
        AND TABLE_NAME = 'IpBlock' 
        AND INDEX_NAME = 'idx_ipaddress'
);

-- 如果索引不存在，則創建
SET @create_index_sql = IF(
    @index_exists = 0,
    'CREATE INDEX idx_ipaddress ON IpBlock (IPAddress)',
    'SELECT "Index idx_ipaddress already exists" as message'
);

PREPARE stmt FROM @create_index_sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 5. 驗證修改結果
SELECT '=== 修改後的表結構 ===' as info;
DESCRIBE IpBlock;

SELECT '=== 修改後的索引 ===' as info;
SHOW INDEX FROM IpBlock;

-- 6. 測試插入重複 IP（驗證約束已移除）
SELECT '=== 測試插入重複 IP ===' as info;

-- 插入測試資料
INSERT INTO IpBlock (IPAddress, CreatedAt, UpdatedAt, ExpiredAt) 
VALUES 
    ('*************', NOW(), NOW(), DATE_ADD(NOW(), INTERVAL 1 DAY)),
    ('*************', NOW(), NOW(), DATE_ADD(NOW(), INTERVAL 2 DAY))
ON DUPLICATE KEY UPDATE UpdatedAt = NOW();

-- 查看結果
SELECT 
    Id,
    IPAddress,
    CreatedAt,
    ExpiredAt,
    CASE 
        WHEN ExpiredAt > NOW() THEN 'Active'
        ELSE 'Expired'
    END as Status
FROM IpBlock 
WHERE IPAddress = '*************'
ORDER BY CreatedAt DESC;

-- 7. 清理測試資料
DELETE FROM IpBlock WHERE IPAddress = '*************';

SELECT '=== 修改完成 ===' as result;
SELECT 'IPAddress 欄位現在允許重複值' as message;

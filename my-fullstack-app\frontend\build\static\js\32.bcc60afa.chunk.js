"use strict";(self.webpackChunkfrontend=self.webpackChunkfrontend||[]).push([[32],{32:(e,a,s)=>{s.r(a),s.d(a,{default:()=>f});var l=s(9379),t=s(2018),r=s(9642),i=s(1104),c=s(1063),o=s(8060),n=s(5831),d=s(828),m=s(5043),u=s(5666),p=s(402),h=s(8685),v=s(1182),x=s(579);const f=()=>{var e;const a=null===(e=(0,u.zy)().state)||void 0===e?void 0:e.treatment,[s,f]=(0,m.useState)(""),[b,y]=(0,m.useState)(0),[j,N]=(0,m.useState)(""),[g,w]=(0,m.useState)(!1),[A,k]=(0,m.useState)(!1),I=(0,m.useRef)(null),[S,C]=(0,m.useState)([]),{dataType:R,loading:T}=(0,v.A)(),[$,L]=(0,m.useState)(0),[E,M]=(0,m.useState)("");console.log("Progress:",$,"Message:",E);const U=S.map((e=>e.treatmentItem));(0,m.useEffect)((()=>(w(!!a.receiptUrl),P(),h.A.start().then((()=>{console.log("\u5df2\u9023\u7dda\u81f3 SignalR")})).catch((e=>console.error("SignalR \u9023\u7dda\u5931\u6557:",e))),h.A.on("ReportProgress",(e=>{L(e)})),h.A.on("ReportFinished",(e=>{M(e)})),()=>{h.A.stop()})),[]);const P=async()=>{try{const e=await p.A.get("/api/receipt/",{params:{Id:a.id}});C(e.data),e.data.length>0&&(N(e.data[0].orderNo),k(!0))}catch(s){var e;null===(e=I.current)||void 0===e||e.show({severity:"error",summary:"\u932f\u8aa4",detail:"\u8b80\u53d6\u6536\u64da\u8cc7\u6599\u5931\u6557"})}},V=()=>Math.floor(9e4*Math.random())+1e4,F=async()=>{var e;if(0!=S.length)try{var s;const e=await p.A.get("/api/receipt/ExportReceiptsPdf",{params:{TreatmentId:a.id,orderNo:j,connectionId:h.A.connectionId},responseType:"blob"});null===(s=I.current)||void 0===s||s.show({severity:"success",summary:"\u6210\u529f",detail:"\u6536\u64da\u88fd\u4f5c\u6210\u529f"});const l=new Blob([e.data],{type:"application/pdf"}),t=URL.createObjectURL(l);w(!0),window.open(t)}catch(t){var l;null===(l=I.current)||void 0===l||l.show({severity:"error",summary:"\u932f\u8aa4",detail:t.message})}else null===(e=I.current)||void 0===e||e.show({severity:"error",summary:"\u932f\u8aa4",detail:"\u8cc7\u6599\u8acb\u52ff\u5c11\u65bc\u4e00\u7b46"})};if(T)return(0,x.jsx)("p",{children:"Loading..."});const G=(0,x.jsxs)("div",{className:"card flex flex-wrap p-fluid",children:[(0,x.jsx)("div",{className:"col-6 md:col-2",children:(0,x.jsxs)("div",{className:"flex-auto",children:[(0,x.jsx)("label",{className:"font-bold block mb-2",children:"\u6cbb\u7642\u9805\u76ee"}),(0,x.jsx)(o.m,{value:s,options:(e=>{var a;return(null===(a=R.find((a=>a.groupId===e)))||void 0===a?void 0:a.dataTypes.map((e=>({label:e.name,value:e.name}))))||[]})(9).filter((e=>!U.includes(e.value))),onChange:e=>f(e.value),placeholder:"\u8acb\u9078\u64c7"})]})}),(0,x.jsx)("div",{className:"col-5 md:col-2",children:(0,x.jsxs)("div",{className:"flex-auto",children:[(0,x.jsx)("label",{htmlFor:"mile",className:"font-bold block mb-2",children:"\u91d1\u984d"}),(0,x.jsx)(n.Y,{value:b,onValueChange:e=>y(Number(e.target.value))})]})}),(0,x.jsxs)("div",{className:" flex flex-wrap col-11 md:col-5",children:[(0,x.jsx)("div",{className:"flex col-6 md:col-2",children:(0,x.jsxs)("div",{className:"flex-auto",children:[(0,x.jsx)("label",{className:"font-bold block mb-2",children:" "}),(0,x.jsx)(t.$,{label:"\u65b0\u589e",icon:"pi pi-plus",onClick:()=>{if(!s||!b)return alert("\u8acb\u586b\u5beb\u5b8c\u6574\u6b04\u4f4d");if(S.length>=4)return alert("\u6700\u591a\u53ea\u80fd\u65b0\u589e 4 \u7b46\u8cc7\u6599");if(U.includes(s))return alert("\u9805\u76ee\u540d\u7a31\u4e0d\u53ef\u91cd\u8907");const e={id:V(),treatmentItem:s,treatmentMoney:b,treatmentId:a.id,patientId:a.patientId};C([...S,e]),f(""),y(0)},disabled:S.length>=5})]})}),(0,x.jsx)("div",{className:"flex col-5 md:col-2",children:(0,x.jsxs)("div",{className:"flex-auto",children:[(0,x.jsx)("label",{className:"font-bold block mb-2",children:" "}),(0,x.jsx)(t.$,{label:"\u8907\u88fd",icon:"pi pi-copy",severity:"info",onClick:()=>{(0,i.Z)({message:"\u662f\u5426\u8907\u88fd\u4e0a\u4e00\u7b46\u6536\u64da\u7d00\u9304",header:"\u8907\u88fd\u78ba\u8a8d",icon:"pi pi-exclamation-triangle",accept:async()=>{try{const s=await p.A.get("/api/Receipt/GetLatestRecord/".concat(a.patientId),{method:"GET",headers:{"Content-Type":"application/json"}});if(s){var e;const t=await s.data;C([]);const r=t.map((e=>(0,l.A)((0,l.A)({},e),{},{id:V(),treatmentId:a.id,orderNo:void 0})));C(r),null===(e=I.current)||void 0===e||e.show({severity:"success",summary:"\u8907\u88fd\u6210\u529f",detail:"\u5df2\u8907\u88fd\u4e0a\u4e00\u7b46\u6536\u64da\u7d00\u9304"})}}catch(t){var s;null===(s=I.current)||void 0===s||s.show({severity:"error",summary:"\u8907\u88fd\u5931\u6557",detail:t.details})}}})},disabled:A})]})}),(0,x.jsx)("div",{className:"flex col-6 md:col-2",children:(0,x.jsxs)("div",{className:"flex-auto",children:[(0,x.jsx)("label",{className:"font-bold block mb-2",children:" "}),(0,x.jsx)(t.$,{label:"\u5132\u5b58",icon:"pi pi-save",severity:"success",onClick:async()=>{var e;if(0!=S.length)try{if(j){var a;const e=S.map((e=>(0,l.A)((0,l.A)({},e),{},{orderNo:j})));await p.A.put("/api/receipt/Update",e),null===(a=I.current)||void 0===a||a.show({severity:"success",summary:"\u6210\u529f",detail:"\u66f4\u65b0\u5b8c\u6210"})}else{var s;const e=await p.A.post("/api/receipt/Insert",S);null===(s=I.current)||void 0===s||s.show({severity:"success",summary:"\u6210\u529f",detail:e.data.msg}),N(e.data.orderNo),k(!0)}}catch(i){var t,r;const e=(null===i||void 0===i||null===(t=i.response)||void 0===t?void 0:t.data)||"\u5132\u5b58\u5931\u6557";null===(r=I.current)||void 0===r||r.show({severity:"error",summary:"\u932f\u8aa4",detail:e})}else null===(e=I.current)||void 0===e||e.show({severity:"error",summary:"\u932f\u8aa4",detail:"\u8cc7\u6599\u8acb\u52ff\u5c11\u65bc\u4e00\u7b46"})}})]})}),g&&(0,x.jsx)("div",{className:"flex col-6 md:col-3",children:(0,x.jsxs)("div",{className:"flex-auto",children:[(0,x.jsx)("label",{className:"font-bold block mb-2",children:" "}),(0,x.jsx)(t.$,{label:"\u6aa2\u8996\u6536\u64da",icon:"pi pi-file-pdf",severity:"secondary",onClick:F})]})}),!g&&(0,x.jsx)("div",{className:"flex col-5 md:col-3",children:(0,x.jsxs)("div",{className:"flex-auto",children:[(0,x.jsx)("label",{className:"font-bold block mb-2",children:" "}),(0,x.jsx)(t.$,{label:"\u958b\u7acb\u6536\u64da",icon:"pi pi-file-pdf",severity:"secondary",onClick:()=>{(0,i.Z)({message:"\u6536\u64da\u958b\u7acb\u5f8c\u5c31\u7121\u6cd5\u4fee\u6539\u5167\u5bb9\uff0c\u78ba\u5b9a\u8981\u958b\u7acb\u6536\u64da\u55ce\uff1f",header:"\u6536\u64da\u958b\u7acb\u78ba\u8a8d",icon:"pi pi-exclamation-triangle",defaultFocus:"accept",acceptLabel:"\u78ba\u5b9a",rejectLabel:"\u53d6\u6d88",accept:()=>F()})}})]})}),(0,x.jsx)("div",{className:"flex col-5 md:col-2",children:(0,x.jsxs)("div",{className:"flex-auto",children:[(0,x.jsx)("label",{className:"font-bold block mb-2",children:"\u5831\u8868\u7522\u751f\u9032\u5ea6"}),(0,x.jsx)("progress",{value:$,max:"100",style:{width:"100%"}})]})})]})]});return(0,x.jsxs)("div",{className:"card",children:[(0,x.jsx)(d.y,{ref:I}),(0,x.jsx)(i.T,{}),G,(0,x.jsxs)(c.b,{value:S,dataKey:"id",children:[(0,x.jsx)(r.V,{field:"treatmentItem",header:"\u9805\u76ee"}),(0,x.jsx)(r.V,{dataType:"numeric",field:"treatmentMoney",header:"\u91d1\u984d"}),(0,x.jsx)(r.V,{body:e=>(0,x.jsx)(t.$,{icon:"pi pi-trash",className:"p-button-danger",onClick:()=>(e=>{if(1==S.length){var a;null===(a=I.current)||void 0===a||a.show({severity:"error",summary:"\u932f\u8aa4",detail:"\u8cc7\u6599\u8acb\u52ff\u5c11\u65bc\u4e00\u7b46"})}else{const a=S.filter((a=>a.id!==e.id));C(a)}})(e)}),header:"\u522a\u9664"})]})]})}},1182:(e,a,s)=>{s.d(a,{A:()=>r});var l=s(5043),t=s(402);function r(){const[e,a]=(0,l.useState)([]),[s,r]=(0,l.useState)(!0);return(0,l.useEffect)((()=>{t.A.get("/api/system/GetDataType").then((e=>a(e.data))).catch((e=>console.error("API Error:",e))).finally((()=>r(!1)))}),[]),{dataType:e,loading:s}}},8685:(e,a,s)=>{s.d(a,{A:()=>l});const l=(new(s(7127).$)).withUrl("https://nbhphysical.idv.tw/reportHub").withAutomaticReconnect().build()}}]);
//# sourceMappingURL=32.bcc60afa.chunk.js.map
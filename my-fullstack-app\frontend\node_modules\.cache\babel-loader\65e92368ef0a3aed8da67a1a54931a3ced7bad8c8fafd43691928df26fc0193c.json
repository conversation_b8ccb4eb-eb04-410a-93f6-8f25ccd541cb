{"ast": null, "code": "import axios from\"axios\";import{config}from\"../config/env\";/**\r\n * API Error types\r\n *//**\r\n * API Response wrapper\r\n *//**\r\n * Create API client with enhanced error handling and type safety\r\n */class ApiClient{constructor(){this.instance=void 0;this.instance=axios.create({baseURL:config.apiUrl,timeout:10000,headers:{'Content-Type':'application/json'}});this.setupInterceptors();}setupInterceptors(){// Request interceptor\nthis.instance.interceptors.request.use(config=>{const token=localStorage.getItem(\"token\");if(token&&config.headers){config.headers.Authorization=\"Bearer \".concat(token);}return config;},error=>Promise.reject(this.handleError(error)));// Response interceptor\nthis.instance.interceptors.response.use(response=>response,error=>Promise.reject(this.handleError(error)));}handleError(error){var _error$response;const apiError={message:'An unexpected error occurred',status:(_error$response=error.response)===null||_error$response===void 0?void 0:_error$response.status,code:error.code};if(error.response){// Server responded with error status\nconst{status,data}=error.response;apiError.status=status;apiError.message=(data===null||data===void 0?void 0:data.message)||\"HTTP Error \".concat(status);apiError.details=data;// Handle specific status codes\nswitch(status){case 400:apiError.message='Bad request. Please check your input.';break;case 401:apiError.message='Unauthorized. Please login again.';localStorage.removeItem('token');// window.location.href = '/login';\nbreak;case 403:apiError.message='Access forbidden.';break;case 404:apiError.message='Resource not found.';break;case 500:apiError.message='Internal server error.';break;}}else if(error.request){// Network error\napiError.message='Network error. Please check your connection.';}return apiError;}/**\r\n   * Generic GET request\r\n   */async get(url,config){const response=await this.instance.get(url,config);return response.data;}/**\r\n   * Generic POST request\r\n   */async post(url,data,config){const response=await this.instance.post(url,data,config);return response.data;}/**\r\n   * Generic PUT request\r\n   */async put(url,data,config){const response=await this.instance.put(url,data,config);return response.data;}/**\r\n   * Generic DELETE request\r\n   */async delete(url,config){const response=await this.instance.delete(url,config);return response.data;}/**\r\n   * Get the underlying axios instance\r\n   */getInstance(){return this.instance;}}// Export singleton instance\nexport const apiClient=new ApiClient();// Export default for backward compatibility\nexport default apiClient.getInstance();", "map": {"version": 3, "names": ["axios", "config", "ApiClient", "constructor", "instance", "create", "baseURL", "apiUrl", "timeout", "headers", "setupInterceptors", "interceptors", "request", "use", "token", "localStorage", "getItem", "Authorization", "concat", "error", "Promise", "reject", "handleError", "response", "_error$response", "apiError", "message", "status", "code", "data", "details", "removeItem", "get", "url", "post", "put", "delete", "getInstance", "apiClient"], "sources": ["C:/Users/<USER>/Desktop/demo-git/demo-react/my-fullstack-app/frontend/src/services/api.ts"], "sourcesContent": ["import axios, { AxiosError, <PERSON><PERSON>os<PERSON><PERSON><PERSON>, AxiosRequestConfig, AxiosResponse } from \"axios\";\r\nimport { config } from \"../config/env\";\r\n\r\n/**\r\n * API Error types\r\n */\r\nexport interface ApiError {\r\n  message: string ;\r\n  status?: number | undefined;\r\n  code?: string | undefined;\r\n  details?: any | undefined;\r\n}\r\n\r\n/**\r\n * API Response wrapper\r\n */\r\nexport interface ApiResponse<T = any> {\r\n  data: T;\r\n  success: boolean;\r\n  message?: string;\r\n  errors?: string[];\r\n}\r\n\r\n/**\r\n * Create API client with enhanced error handling and type safety\r\n */\r\nclass ApiClient {\r\n  private instance: AxiosInstance;\r\n\r\n  constructor() {\r\n    this.instance = axios.create({\r\n      baseURL: config.apiUrl,\r\n      timeout: 10000,\r\n      headers: {\r\n        'Content-Type': 'application/json',\r\n      },\r\n    });\r\n\r\n    this.setupInterceptors();\r\n  }\r\n\r\n  private setupInterceptors(): void {\r\n    // Request interceptor\r\n    this.instance.interceptors.request.use(\r\n      (config) => {\r\n        const token = localStorage.getItem(\"token\");\r\n        if (token && config.headers) {\r\n          config.headers.Authorization = `Bearer ${token}`;\r\n        }\r\n        return config;\r\n      },\r\n      (error) => Promise.reject(this.handleError(error))\r\n    );\r\n\r\n    // Response interceptor\r\n    this.instance.interceptors.response.use(\r\n      (response: AxiosResponse) => response,\r\n      (error: AxiosError) => Promise.reject(this.handleError(error))\r\n    );\r\n  }\r\n\r\n  private handleError(error: AxiosError): ApiError {\r\n    const apiError: ApiError = {\r\n      message: 'An unexpected error occurred',\r\n      status: error.response?.status,\r\n      code: error.code,\r\n    };\r\n\r\n    if (error.response) {\r\n      // Server responded with error status\r\n      const { status, data } = error.response;\r\n      apiError.status = status;\r\n      apiError.message = (data as any)?.message || `HTTP Error ${status}`;\r\n      apiError.details = data;\r\n\r\n      // Handle specific status codes\r\n      switch (status) {\r\n        case 400:\r\n          apiError.message = 'Bad request. Please check your input.';\r\n          break;\r\n        case 401:\r\n          apiError.message = 'Unauthorized. Please login again.';\r\n          localStorage.removeItem('token');\r\n          // window.location.href = '/login';\r\n          break;\r\n        case 403:\r\n          apiError.message = 'Access forbidden.';\r\n          break;\r\n        case 404:\r\n          apiError.message = 'Resource not found.';\r\n          break;\r\n        case 500:\r\n          apiError.message = 'Internal server error.';\r\n          break;\r\n      }\r\n    } else if (error.request) {\r\n      // Network error\r\n      apiError.message = 'Network error. Please check your connection.';\r\n    }\r\n\r\n    return apiError;\r\n  }\r\n\r\n  /**\r\n   * Generic GET request\r\n   */\r\n  async get<T = any>(url: string, config?: AxiosRequestConfig): Promise<T> {\r\n    const response = await this.instance.get<T>(url, config);\r\n    return response.data;\r\n  }\r\n\r\n  /**\r\n   * Generic POST request\r\n   */\r\n  async post<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> {\r\n    const response = await this.instance.post<T>(url, data, config);\r\n    return response.data;\r\n  }\r\n\r\n  /**\r\n   * Generic PUT request\r\n   */\r\n  async put<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> {\r\n    const response = await this.instance.put<T>(url, data, config);\r\n    return response.data;\r\n  }\r\n\r\n  /**\r\n   * Generic DELETE request\r\n   */\r\n  async delete<T = any>(url: string, config?: AxiosRequestConfig): Promise<T> {\r\n    const response = await this.instance.delete<T>(url, config);\r\n    return response.data;\r\n  }\r\n\r\n  /**\r\n   * Get the underlying axios instance\r\n   */\r\n  getInstance(): AxiosInstance {\r\n    return this.instance;\r\n  }\r\n}\r\n\r\n// Export singleton instance\r\nexport const apiClient = new ApiClient();\r\n\r\n// Export default for backward compatibility\r\nexport default apiClient.getInstance();"], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAwE,OAAO,CAC3F,OAASC,MAAM,KAAQ,eAAe,CAEtC;AACA;AACA,GAQA;AACA;AACA,GAQA;AACA;AACA,GACA,KAAM,CAAAC,SAAU,CAGdC,WAAWA,CAAA,CAAG,MAFNC,QAAQ,QAGd,IAAI,CAACA,QAAQ,CAAGJ,KAAK,CAACK,MAAM,CAAC,CAC3BC,OAAO,CAAEL,MAAM,CAACM,MAAM,CACtBC,OAAO,CAAE,KAAK,CACdC,OAAO,CAAE,CACP,cAAc,CAAE,kBAClB,CACF,CAAC,CAAC,CAEF,IAAI,CAACC,iBAAiB,CAAC,CAAC,CAC1B,CAEQA,iBAAiBA,CAAA,CAAS,CAChC;AACA,IAAI,CAACN,QAAQ,CAACO,YAAY,CAACC,OAAO,CAACC,GAAG,CACnCZ,MAAM,EAAK,CACV,KAAM,CAAAa,KAAK,CAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC,CAC3C,GAAIF,KAAK,EAAIb,MAAM,CAACQ,OAAO,CAAE,CAC3BR,MAAM,CAACQ,OAAO,CAACQ,aAAa,WAAAC,MAAA,CAAaJ,KAAK,CAAE,CAClD,CACA,MAAO,CAAAb,MAAM,CACf,CAAC,CACAkB,KAAK,EAAKC,OAAO,CAACC,MAAM,CAAC,IAAI,CAACC,WAAW,CAACH,KAAK,CAAC,CACnD,CAAC,CAED;AACA,IAAI,CAACf,QAAQ,CAACO,YAAY,CAACY,QAAQ,CAACV,GAAG,CACpCU,QAAuB,EAAKA,QAAQ,CACpCJ,KAAiB,EAAKC,OAAO,CAACC,MAAM,CAAC,IAAI,CAACC,WAAW,CAACH,KAAK,CAAC,CAC/D,CAAC,CACH,CAEQG,WAAWA,CAACH,KAAiB,CAAY,KAAAK,eAAA,CAC/C,KAAM,CAAAC,QAAkB,CAAG,CACzBC,OAAO,CAAE,8BAA8B,CACvCC,MAAM,EAAAH,eAAA,CAAEL,KAAK,CAACI,QAAQ,UAAAC,eAAA,iBAAdA,eAAA,CAAgBG,MAAM,CAC9BC,IAAI,CAAET,KAAK,CAACS,IACd,CAAC,CAED,GAAIT,KAAK,CAACI,QAAQ,CAAE,CAClB;AACA,KAAM,CAAEI,MAAM,CAAEE,IAAK,CAAC,CAAGV,KAAK,CAACI,QAAQ,CACvCE,QAAQ,CAACE,MAAM,CAAGA,MAAM,CACxBF,QAAQ,CAACC,OAAO,CAAG,CAACG,IAAI,SAAJA,IAAI,iBAAJA,IAAI,CAAUH,OAAO,iBAAAR,MAAA,CAAkBS,MAAM,CAAE,CACnEF,QAAQ,CAACK,OAAO,CAAGD,IAAI,CAEvB;AACA,OAAQF,MAAM,EACZ,IAAK,IAAG,CACNF,QAAQ,CAACC,OAAO,CAAG,uCAAuC,CAC1D,MACF,IAAK,IAAG,CACND,QAAQ,CAACC,OAAO,CAAG,mCAAmC,CACtDX,YAAY,CAACgB,UAAU,CAAC,OAAO,CAAC,CAChC;AACA,MACF,IAAK,IAAG,CACNN,QAAQ,CAACC,OAAO,CAAG,mBAAmB,CACtC,MACF,IAAK,IAAG,CACND,QAAQ,CAACC,OAAO,CAAG,qBAAqB,CACxC,MACF,IAAK,IAAG,CACND,QAAQ,CAACC,OAAO,CAAG,wBAAwB,CAC3C,MACJ,CACF,CAAC,IAAM,IAAIP,KAAK,CAACP,OAAO,CAAE,CACxB;AACAa,QAAQ,CAACC,OAAO,CAAG,8CAA8C,CACnE,CAEA,MAAO,CAAAD,QAAQ,CACjB,CAEA;AACF;AACA,KACE,KAAM,CAAAO,GAAGA,CAAUC,GAAW,CAAEhC,MAA2B,CAAc,CACvE,KAAM,CAAAsB,QAAQ,CAAG,KAAM,KAAI,CAACnB,QAAQ,CAAC4B,GAAG,CAAIC,GAAG,CAAEhC,MAAM,CAAC,CACxD,MAAO,CAAAsB,QAAQ,CAACM,IAAI,CACtB,CAEA;AACF;AACA,KACE,KAAM,CAAAK,IAAIA,CAAUD,GAAW,CAAEJ,IAAU,CAAE5B,MAA2B,CAAc,CACpF,KAAM,CAAAsB,QAAQ,CAAG,KAAM,KAAI,CAACnB,QAAQ,CAAC8B,IAAI,CAAID,GAAG,CAAEJ,IAAI,CAAE5B,MAAM,CAAC,CAC/D,MAAO,CAAAsB,QAAQ,CAACM,IAAI,CACtB,CAEA;AACF;AACA,KACE,KAAM,CAAAM,GAAGA,CAAUF,GAAW,CAAEJ,IAAU,CAAE5B,MAA2B,CAAc,CACnF,KAAM,CAAAsB,QAAQ,CAAG,KAAM,KAAI,CAACnB,QAAQ,CAAC+B,GAAG,CAAIF,GAAG,CAAEJ,IAAI,CAAE5B,MAAM,CAAC,CAC9D,MAAO,CAAAsB,QAAQ,CAACM,IAAI,CACtB,CAEA;AACF;AACA,KACE,KAAM,CAAAO,MAAMA,CAAUH,GAAW,CAAEhC,MAA2B,CAAc,CAC1E,KAAM,CAAAsB,QAAQ,CAAG,KAAM,KAAI,CAACnB,QAAQ,CAACgC,MAAM,CAAIH,GAAG,CAAEhC,MAAM,CAAC,CAC3D,MAAO,CAAAsB,QAAQ,CAACM,IAAI,CACtB,CAEA;AACF;AACA,KACEQ,WAAWA,CAAA,CAAkB,CAC3B,MAAO,KAAI,CAACjC,QAAQ,CACtB,CACF,CAEA;AACA,MAAO,MAAM,CAAAkC,SAAS,CAAG,GAAI,CAAApC,SAAS,CAAC,CAAC,CAExC;AACA,cAAe,CAAAoC,SAAS,CAACD,WAAW,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
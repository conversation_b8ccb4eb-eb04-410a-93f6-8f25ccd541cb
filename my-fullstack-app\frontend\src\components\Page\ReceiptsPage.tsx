import { formatUtcToTaipei } from "../../utils/dateUtils";
import { Button } from 'primereact/button';
import { Calendar } from 'primereact/calendar';
import { Column } from 'primereact/column';
import { ConfirmDialog } from 'primereact/confirmdialog';
import { DataTable } from 'primereact/datatable';
import { Dialog } from 'primereact/dialog';
import { Dropdown } from 'primereact/dropdown';
import { InputText } from "primereact/inputtext";
import { ProgressBar } from 'primereact/progressbar';
import LoadingSpinner from '../Common/LoadingSpinner';
import { Toast } from "primereact/toast";
import React, { useRef, useState, useEffect } from 'react';
import { useNavigate } from "react-router-dom";
import { ROUTES } from "../../constants/routes";
import useReceipt from '../../hooks/useReceipt';
import api from '../../services/api';
import connection from "../../services/signalr";
import { Card } from 'primereact/card';

const ReceiptsPage: React.FC = () => {
    const navigate = useNavigate();
    const toast = useRef<Toast>(null);
    const [name, setName] = useState('');
    const [nationalId, setNationalId] = useState('');
    const [starttime, setStarttime] = useState<Date | null | undefined>(undefined);
    const [endtime, setEndtime] = useState<Date | null | undefined>(undefined);
    const [refreshKey, setRefreshKey] = useState(0);

    const [searchParams, setSearchParams] = useState({
        name: '',
        nationalId: '',
        starttime: null as Date | null | undefined,
        endtime: null as Date | null | undefined,
        refreshKey: 0,
    });

    // 批量匯出相關狀態
    const [selectedReceipts, setSelectedReceipts] = useState<any[]>([]);
    const [showExportDialog, setShowExportDialog] = useState(false);
    const [titleCode, setTitleCode] = useState(0);
    const [exportProgress, setExportProgress] = useState(0);
    const [isExporting, setIsExporting] = useState(false);

    // 送信相關狀態
    const [showEmailDialog, setShowEmailDialog] = useState(false);
    const [selectedReceiptForEmail, setSelectedReceiptForEmail] = useState<any>(null);
    const [recipientEmail, setRecipientEmail] = useState('');
    const [isSendingEmail, setIsSendingEmail] = useState(false);

    const { receipts, loading } = useReceipt(
        searchParams.name, 
        searchParams.nationalId, 
        searchParams.starttime, 
        searchParams.endtime, 
        refreshKey
    );

    // 下拉選單選項
    const titleOptions = [
        { label: '全部', value: 0 },
        { label: '繳款人收執聯', value: 1 },
        { label: '單位存根聯', value: 2 },
        { label: '單位扣底聯', value: 3 }
    ];

    const genderdict: { [key: string]: string } = {
        "1": "男性",
        "2": "女性",
        "3": "其他"
    };

    // 初始化 SignalR 連接
    useEffect(() => {

        connection
        .start()
        .then(() => {
        console.log("已連線至 SignalR");
        //console.log("連線 ID", connection.connectionId);
        })
        .catch(err => console.error("SignalR 連線失敗:", err));

        connection.on("ReportProgress", (value) => {
        setExportProgress(value);
        });

        connection.on("ReportFinished", (msg) => {
        setExportProgress(msg);
        });

        return () => {
        connection.stop();
        };
    }, []);

    const handleSearchClick = () => {
        setRefreshKey(refreshKey + 1)
        setSearchParams({ name, nationalId, starttime, endtime, refreshKey});
    };

    const Reload = () => {
        // 重新觸發 usePatient，等於重新查詢
        setRefreshKey(prev => prev + 1);
    };

    // 批量匯出按鈕點擊
    const handleBatchExport = () => {
        if (selectedReceipts.length === 0) {
            toast.current?.show({
                severity: 'error',
                summary: '錯誤',
                detail: '請勾選欲匯出的收據'
            });
            return;
        }
        setShowExportDialog(true);
    };

    // 執行批量匯出
    const executeBatchExport = async () => {
        if (!connection) {
            toast.current?.show({
                severity: 'error',
                summary: '錯誤',
                detail: 'SignalR 連接未建立'
            });
            return;
        }

        try {
            setIsExporting(true);
            setExportProgress(0);

            const treatmentIds = selectedReceipts.map(receipt => receipt.id);
            const orderNos = selectedReceipts.map(receipt => receipt.receiptOrderNo);
            
            const requestBody = {
                TreatmentId: treatmentIds,
                orderNo: orderNos,
                Titlecode: titleCode
            };

            const response = await api.post('/api/receipt/ExportReceiptsLisrPdf', requestBody, {
                params: {
                    connectionId: connection.connectionId
                },
                responseType: 'blob',
                headers: {
                    'Content-Type': 'application/json' // Axios 通常會自動設定，但明確指定更佳
                }
            });

            // 產生 blob url
            const file = new Blob([response.data], { type: 'application/pdf' });
            const fileURL = URL.createObjectURL(file);

            // 在新分頁開啟 PDF
            window.open(fileURL);

            setShowExportDialog(false);
            setSelectedReceipts([]);

        } catch (error: any) {
            setIsExporting(false);
            setExportProgress(0);
            toast.current?.show({
                severity: 'error',
                summary: '錯誤',
                detail: error.response?.data || '匯出失敗'
            });
        }
    };

    // 開啟送信對話框
    const handleSendEmail = (receipt: any) => {
        setSelectedReceiptForEmail(receipt);
        setRecipientEmail(receipt.patientEmail);
        setShowEmailDialog(true);
    };

    // 執行送信
    const executeSendEmail = async () => {
        if (!recipientEmail.trim()) {
            toast.current?.show({
                severity: 'error',
                summary: '錯誤',
                detail: '請輸入收件人郵箱'
            });
            return;
        }

        // 簡單的郵箱格式驗證
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(recipientEmail)) {
            toast.current?.show({
                severity: 'error',
                summary: '錯誤',
                detail: '請輸入有效的郵箱地址'
            });
            return;
        }

        try {
            setIsSendingEmail(true);

            await api.post('/api/receipt/SendReceiptEmail', {
                email: recipientEmail,
                orderNo: selectedReceiptForEmail.receiptOrderNo
            });

            toast.current?.show({
                severity: 'success',
                summary: '成功',
                detail: '收據郵件發送成功'
            });

            setShowEmailDialog(false);
            setRecipientEmail('');
            setSelectedReceiptForEmail(null);

        } catch (error: any) {
            toast.current?.show({
                severity: 'error',
                summary: '錯誤',
                detail: error.response?.data?.error || '郵件發送失敗'
            });
        } finally {
            setIsSendingEmail(false);
        }
    };

    const paginatorLeft = (
        <Button
            type="button"
            icon="pi pi-refresh"
            text
            onClick={() => Reload()}
        />
    );
    const paginatorRight = <div></div>;
    const optionBodyTemplate = (rowData: any) => {
        return (
            <div className="flex gap-2">
                    <Button 
                        label="檢視" 
                        type="button" 
                        icon="pi pi-file-edit" 
                        onClick={() => navigate(ROUTES.RECEIPT_DETAIL, { state: { treatment: rowData } })}
                        size="small" 
                        severity="info" 
                        style={{ fontSize: '1rem', margin: '3px' }} 
                    />
                    <Button
                        label="送信"
                        type="button"
                        icon="pi pi-send"
                        onClick={() => handleSendEmail(rowData)}
                        size="small"
                        severity="success"
                        style={{ fontSize: '1rem', margin: '3px' }}
                        disabled={ rowData.patientEmail ? false : true}
                    />
            </div>
        );
    };

    const genderBodyTemplate = (rowData: any) => {
        var data = String(rowData.patientGender)
        const gendar = genderdict[data]
            return (
                <div>
                    {gendar}
                </div>
            );
        };

    const formatDate = (value: string) => {
    if (!value) return '';
    return formatUtcToTaipei(value, "yyyy/MM/dd HH:mm:ss");
  };

    const formatAge = (value: string) => {
        if (!value) return "";
        const date = new Date(value);
        const today = new Date();
        let age = today.getFullYear() - date.getFullYear();

        const hasNotHadBirthdayThisYear =
            today.getMonth() < date.getMonth() ||
            (today.getMonth() === date.getMonth() && today.getDate() < date.getDate());

        if (hasNotHadBirthdayThisYear) {
            age--;
        }

        return age;
        
    };

    if (loading) {
        return <LoadingSpinner message="載入收據資料中..." />;
    }

    return (
        <div>
            <Toast ref={toast} />
            <ConfirmDialog />
            <Card title="收據管理" className="mb-4">
                <p className="text-600 line-height-3 m-0">
                    收據管理頁面，可以查詢、檢視、製作PDF、發送Email收據資料。
                </p>
            </Card>

            {/* 搜尋條件 */}
            <Card className="mb-4">
                <div className="grid">
                    <div className="col-6 md:col-3">
                        <InputText
                            id="name"
                            type="text"
                            value={name}
                            onChange={(e) => setName(e.target.value)}
                            className="w-full"
                            placeholder="病患姓名" />
                    </div>
                    <div className="col-6 md:col-3">
                        <InputText
                            id="nationalId"
                            type="text"
                            value={nationalId}
                            onChange={(e) => setNationalId(e.target.value)}
                            className="w-full"
                            placeholder="病患身分證" />
                    </div>

                    <div className="col-6 md:col-3">
                        <Calendar
                            id="starttime"
                            value={starttime}
                            onChange={(e) => setStarttime(e.value)}
                            placeholder="開始時間"
                            className="w-full"
                            dateFormat="yy/mm/dd"
                            showIcon/>
                    </div>
                    <div className="col-6 md:col-3">
                        <Calendar
                            id="endtime"
                            value={endtime}
                            onChange={(e) => setEndtime(e.value)}
                            placeholder="結束時間"
                            className="w-full"
                            dateFormat="yy/mm/dd"
                            showIcon/>
                    </div>
                    <div className="col-12 md:col-4">
                        <div className="flex gap-2">
                            <Button label="查詢" icon="pi pi-search" onClick={handleSearchClick}/>
                            <Button
                                label="批量匯出"
                                icon="pi pi-download"
                                onClick={handleBatchExport}
                                className="p-button-success"
                                disabled={selectedReceipts.length === 0}
                            />
                        </div>
                    </div>
                </div>
            </Card>

            <Card>
                <DataTable
                    value={receipts}
                    selection={selectedReceipts}
                    onSelectionChange={(e) => setSelectedReceipts(e.value)}
                    paginator
                    rows={10}
                    rowsPerPageOptions={[10, 20, 30, 40]}
                    emptyMessage="沒有找到收據資料"
                    tableStyle={{ minWidth: '50rem' }}
                    paginatorLeft={paginatorLeft}
                    paginatorRight={paginatorRight}
                >
                    <Column selectionMode="multiple" headerStyle={{ width: '2%' }} />
                    <Column field="orderNo" header="案號" style={{ width: '5%' }} />
                    <Column field="receiptOrderNo" header="收據編號" style={{ width: '5%' }} />
                    <Column field="patientName" header="病患姓名" style={{ width: '5%' }} />
                    <Column field="patientGender" header="性別" style={{ width: '3%' }} body={genderBodyTemplate}/>
                    <Column field="patientBirthDate" header="年齡" style={{ width: '3%' }}  body={(rowData) => formatAge(rowData.patientBirthDate)}/>
                    <Column field="receiptCreatedAt" header="新增日期" style={{ width: '8%' }} body={(rowData) => formatDate(rowData.receiptCreatedAt)} />
                    <Column field="receiptUpdatedAt" header="更新日期" style={{ width: '8%' }} body={(rowData) => formatDate(rowData.receiptUpdatedAt)}/>
                    <Column field="receiptOperatorUserName" header="操作人" style={{ width: '5%' }} />
                    <Column field="Option" header="功能" style={{ width: '12%' }} body={optionBodyTemplate} />
                </DataTable>
            </Card>

            {/* 批量匯出彈跳視窗 */}
            <Dialog
                header="批量匯出收據"
                visible={showExportDialog}
                style={{ width: '50vw' }}
                onHide={() => setShowExportDialog(false)}
                modal
            >
                <div className="mb-4">
                    <h5>已選擇的收據 ({selectedReceipts.length} 筆)</h5>
                    <DataTable
                        value={selectedReceipts}
                        scrollable
                        scrollHeight="300px"
                        emptyMessage="沒有選擇的收據"
                    >
                        <Column field="orderNo" header="案號" style={{ width: '15%' }} />
                        <Column field="receiptorderNo" header="收據編號" style={{ width: '15%' }} />
                        <Column field="patientName" header="病患姓名" style={{ width: '15%' }} />
                        <Column field="receiptCreatedAt" header="新增日期" style={{ width: '20%' }} body={(rowData) => formatDate(rowData.receiptCreatedAt)} />
                    </DataTable>
                </div>

                <div className="grid align-items-end">
                    <div className="col-12 md:col-4">
                        <label htmlFor="titleCode" className="font-bold block mb-2">收據類型</label>
                        <Dropdown
                            id="titleCode"
                            value={titleCode}
                            options={titleOptions}
                            onChange={(e) => setTitleCode(e.value)}
                            placeholder="選擇收據類型"
                            className="w-full"
                        />
                    </div>
                    <div className="col-12 md:col-4">
                        <Button
                            label="匯出"
                            icon="pi pi-download"
                            onClick={executeBatchExport}
                            disabled={isExporting}
                            className="p-button-success w-full"
                        />
                    </div>
                    <div className="col-12 md:col-4">
                        <label className="font-bold block mb-2">匯出進度</label>
                        <ProgressBar
                            value={exportProgress}
                            showValue={false}
                            style={{ height: '1.5rem' }}
                        />
                        <small className="text-center block mt-1">{exportProgress}%</small>
                    </div>
                </div>
            </Dialog>

            {/* 送信對話框 */}
            <Dialog
                header="發送收據郵件"
                visible={showEmailDialog}
                style={{ width: '500px' }}
                onHide={() => setShowEmailDialog(false)}
                modal
            >
                <div className="mb-4">
                    {selectedReceiptForEmail && (
                        <div className="p-3 bg-gray-50 border-round mb-3">
                            <h3 className="mt-0 mb-2">收據資訊</h3>
                            <p className="mb-1"><strong>收據編號:</strong> {selectedReceiptForEmail.receiptOrderNo}</p>
                            <p className="mb-1"><strong>病患姓名:</strong> {selectedReceiptForEmail.patientName}</p>
                            <p className="mb-0"><strong>建立日期:</strong> {formatDate(selectedReceiptForEmail.receiptCreatedAt)}</p>
                        </div>
                    )}

                    <div className="field">
                        <label htmlFor="recipientEmail" className="font-bold block mb-2">收件人郵箱 *</label>
                        <InputText
                            id="recipientEmail"
                            value={recipientEmail}
                            onChange={(e) => setRecipientEmail(e.target.value)}
                            placeholder="請輸入收件人郵箱地址"
                            className="w-full"
                            disabled={isSendingEmail}
                        />
                    </div>
                </div>

                <div className="flex justify-content-end gap-2">
                    <Button
                        label="取消"
                        icon="pi pi-times"
                        onClick={() => setShowEmailDialog(false)}
                        className="p-button-secondary"
                        disabled={isSendingEmail}
                    />
                    <Button
                        label={isSendingEmail ? "發送中..." : "發送"}
                        icon={isSendingEmail ? "pi pi-spin pi-spinner" : "pi pi-send"}
                        onClick={executeSendEmail}
                        disabled={isSendingEmail || !recipientEmail.trim()}
                        className="p-button-primary"
                    />
                </div>
            </Dialog>
        </div>
    );
};

export default ReceiptsPage;
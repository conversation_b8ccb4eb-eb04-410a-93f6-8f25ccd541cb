-- 添加備份管理菜單項到系統管理群組
-- 首先檢查系統管理群組是否存在，如果不存在則創建

-- 插入或更新系統管理群組
INSERT INTO MenuGroups (Name, SortOrder, Icon, IsEnabled, OperatorUserId, CreatedAt, UpdatedAt)
SELECT '系統管理', 3, 'pi pi-cog', 1, 1, NOW(), NOW()
WHERE NOT EXISTS (
    SELECT 1 FROM MenuGroups WHERE Name = '系統管理'
);

-- 獲取系統管理群組的ID
SET @systemGroupId = (SELECT Id FROM MenuGroups WHERE Name = '系統管理' LIMIT 1);

-- 添加備份管理菜單項
INSERT INTO Menus (Path, Name, SortOrder, IsEnabled, OperatorUserId, GroupId, CreatedAt, UpdatedAt)
VALUES ('/backup-management', '備份管理', 10, 1, 1, @systemGroupId, NOW(), NOW())
ON DUPLICATE KEY UPDATE
    Name = '備份管理',
    SortOrder = 10,
    IsEnabled = 1,
    UpdatedAt = NOW();

-- 驗證插入結果
SELECT 
    mg.Name as GroupName,
    m.Name as MenuName,
    m.Path as MenuPath,
    m.SortOrder,
    m.IsEnabled
FROM Menus m
JOIN MenuGroups mg ON m.GroupId = mg.Id
WHERE m.Path = '/backup-management';

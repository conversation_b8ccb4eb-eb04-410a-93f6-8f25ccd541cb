{"ast": null, "code": "const accusativeWeekdays = [\"vas<PERSON>rna<PERSON>\", \"hétfőn\", \"kedden\", \"szerdán\", \"csütörtök<PERSON>n\", \"pénte<PERSON>\", \"szombaton\"];\nfunction week(isFuture) {\n  return date => {\n    const weekday = accusativeWeekdays[date.getDay()];\n    const prefix = isFuture ? \"\" : \"'múlt' \";\n    return \"\".concat(prefix, \"'\").concat(weekday, \"' p'-kor'\");\n  };\n}\nconst formatRelativeLocale = {\n  lastWeek: week(false),\n  yesterday: \"'tegnap' p'-kor'\",\n  today: \"'ma' p'-kor'\",\n  tomorrow: \"'holnap' p'-kor'\",\n  nextWeek: week(true),\n  other: \"P\"\n};\nexport const formatRelative = (token, date) => {\n  const format = formatRelativeLocale[token];\n  if (typeof format === \"function\") {\n    return format(date);\n  }\n  return format;\n};", "map": {"version": 3, "names": ["accusativeWeekdays", "week", "isFuture", "date", "weekday", "getDay", "prefix", "concat", "formatRelativeLocale", "lastWeek", "yesterday", "today", "tomorrow", "nextWeek", "other", "formatRelative", "token", "format"], "sources": ["C:/Users/<USER>/Desktop/demo-git/demo-react/my-fullstack-app/frontend/node_modules/date-fns/locale/hu/_lib/formatRelative.js"], "sourcesContent": ["const accusativeWeekdays = [\n  \"vas<PERSON>rna<PERSON>\",\n  \"hétfőn\",\n  \"kedden\",\n  \"szerdán\",\n  \"csü<PERSON><PERSON>rtökön\",\n  \"pénteken\",\n  \"szombaton\",\n];\n\nfunction week(isFuture) {\n  return (date) => {\n    const weekday = accusativeWeekdays[date.getDay()];\n    const prefix = isFuture ? \"\" : \"'múlt' \";\n    return `${prefix}'${weekday}' p'-kor'`;\n  };\n}\nconst formatRelativeLocale = {\n  lastWeek: week(false),\n  yesterday: \"'tegnap' p'-kor'\",\n  today: \"'ma' p'-kor'\",\n  tomorrow: \"'holnap' p'-kor'\",\n  nextWeek: week(true),\n  other: \"P\",\n};\n\nexport const formatRelative = (token, date) => {\n  const format = formatRelativeLocale[token];\n\n  if (typeof format === \"function\") {\n    return format(date);\n  }\n\n  return format;\n};\n"], "mappings": "AAAA,MAAMA,kBAAkB,GAAG,CACzB,UAAU,EACV,QAAQ,EACR,QAAQ,EACR,SAAS,EACT,aAAa,EACb,UAAU,EACV,WAAW,CACZ;AAED,SAASC,IAAIA,CAACC,QAAQ,EAAE;EACtB,OAAQC,IAAI,IAAK;IACf,MAAMC,OAAO,GAAGJ,kBAAkB,CAACG,IAAI,CAACE,MAAM,CAAC,CAAC,CAAC;IACjD,MAAMC,MAAM,GAAGJ,QAAQ,GAAG,EAAE,GAAG,SAAS;IACxC,UAAAK,MAAA,CAAUD,MAAM,OAAAC,MAAA,CAAIH,OAAO;EAC7B,CAAC;AACH;AACA,MAAMI,oBAAoB,GAAG;EAC3BC,QAAQ,EAAER,IAAI,CAAC,KAAK,CAAC;EACrBS,SAAS,EAAE,kBAAkB;EAC7BC,KAAK,EAAE,cAAc;EACrBC,QAAQ,EAAE,kBAAkB;EAC5BC,QAAQ,EAAEZ,IAAI,CAAC,IAAI,CAAC;EACpBa,KAAK,EAAE;AACT,CAAC;AAED,OAAO,MAAMC,cAAc,GAAGA,CAACC,KAAK,EAAEb,IAAI,KAAK;EAC7C,MAAMc,MAAM,GAAGT,oBAAoB,CAACQ,KAAK,CAAC;EAE1C,IAAI,OAAOC,MAAM,KAAK,UAAU,EAAE;IAChC,OAAOA,MAAM,CAACd,IAAI,CAAC;EACrB;EAEA,OAAOc,MAAM;AACf,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
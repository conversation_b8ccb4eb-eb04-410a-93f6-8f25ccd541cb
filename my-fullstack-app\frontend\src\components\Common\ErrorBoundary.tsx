import React from 'react';
import { Button } from 'primereact/button';
import { Card } from 'primereact/card';
import { log } from '../../utils/logger';

interface ErrorBoundaryState {
  hasError: boolean;
  error?: Error;
}

interface ErrorBoundaryProps {
  children: React.ReactNode;
  fallback?: React.ComponentType<{ error?: Error; resetError: () => void }>;
  onError?: (error: Error, errorInfo: React.ErrorInfo) => void;
}

class ErrorBoundary extends React.Component<ErrorBoundaryProps, ErrorBoundaryState> {
  constructor(props: ErrorBoundaryProps) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error): ErrorBoundaryState {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    // 記錄錯誤到日誌系統
    log.error('ErrorBoundary 捕獲到錯誤', {
      error: error.message,
      stack: error.stack,
      componentStack: errorInfo.componentStack
    });

    // 調用自定義錯誤處理函數
    if (this.props.onError) {
      this.props.onError(error, errorInfo);
    }

    // 可以在這裡發送錯誤到監控服務
    // 例如: Sentry.captureException(error, { contexts: { react: errorInfo } });
  }

  resetError = () => {
    this.setState({ hasError: false });
  };

  render() {
    if (this.state.hasError) {
      if (this.props.fallback) {
        const FallbackComponent = this.props.fallback;
        return <FallbackComponent {...(this.state.error && { error: this.state.error })} resetError={this.resetError} />;
      }

      return (
        <div className="flex align-items-center justify-content-center min-h-screen">
          <Card className="max-w-md">
            <div className="text-center">
              <i className="pi pi-exclamation-triangle text-6xl text-red-500 mb-3"></i>
              <h2 className="text-2xl font-bold text-gray-800 mb-3">發生錯誤</h2>
              <p className="text-gray-600 mb-4">
                抱歉，應用程式遇到了一個問題。
              </p>
              
              {process.env.NODE_ENV === 'development' && this.state.error && (
                <div className="mb-4 p-3 bg-red-50 border-round text-left">
                  <p className="text-red-700 font-medium mb-2">錯誤詳情：</p>
                  <p className="text-red-600 text-sm mb-2">{this.state.error.message}</p>
                  {this.state.error.stack && (
                    <pre className="text-xs text-red-500 overflow-auto max-h-10rem">
                      {this.state.error.stack}
                    </pre>
                  )}
                </div>
              )}

              <div className="flex flex-column gap-2">
                <Button
                  label="重試"
                  icon="pi pi-refresh"
                  onClick={this.resetError}
                  className="p-button-primary"
                />
                
                <Button
                  label="重新載入頁面"
                  icon="pi pi-sync"
                  onClick={() => window.location.reload()}
                  className="p-button-secondary"
                  outlined
                />
              </div>
            </div>
          </Card>
        </div>
      );
    }

    return this.props.children;
  }
}

export default ErrorBoundary;

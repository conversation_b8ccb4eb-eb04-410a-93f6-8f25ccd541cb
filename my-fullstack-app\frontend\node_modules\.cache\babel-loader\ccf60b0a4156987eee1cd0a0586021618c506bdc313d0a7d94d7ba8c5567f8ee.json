{"ast": null, "code": "/**\n * Typed API service layer\n */import{apiClient}from'./api';/**\n * System API endpoints\n */export class SystemApi{/**\n   * Get menu items for authenticated user\n   */static async getMenus(){return apiClient.get('/api/system/GetMenus');}/**\n   * Get data types\n   */static async getDataTypes(){return apiClient.get('/api/system/GetDataType');}}/**\n * Patient API endpoints\n */export class PatientApi{/**\n   * Get patients list with optional filters\n   */static async getList(){let params=arguments.length>0&&arguments[0]!==undefined?arguments[0]:{};return apiClient.get('/api/patients/GetList',{params});}/**\n   * Get patient by ID\n   */static async getById(id){return apiClient.get(\"/api/patients/\".concat(id));}/**\n   * Create new patient\n   */static async create(patient){return apiClient.post('/api/patients/Insert',patient);}/**\n   * Update patient\n   */static async update(patient){return apiClient.put('/api/patients/Update',patient);}/**\n   * Delete patient\n   */static async delete(id){return apiClient.get(\"/api/patients/Delete?id=\".concat(id));}}/**\n * Treatment API endpoints\n */export class TreatmentApi{/**\n   * Get treatments list with optional filters\n   */static async getList(){let params=arguments.length>0&&arguments[0]!==undefined?arguments[0]:{};return apiClient.get('/api/treatment/GetList',{params});}/**\n   * Get treatment by ID\n   */static async getById(id){return apiClient.get(\"/api/treatment/\".concat(id));}/**\n   * Create new treatment\n   */static async create(treatment){return apiClient.post('/api/treatment/Insert',treatment);}/**\n   * Update treatment\n   */static async update(treatment){return apiClient.put('/api/treatment/Update',treatment);}/**\n   * Delete treatment\n   */static async delete(orderNo){return apiClient.get(\"/api/treatment/Delete?OrderNo=\".concat(orderNo));}}/**\n * Receipt API endpoints\n */export class ReceiptApi{/**\n   * Get receipts list with optional filters\n   */static async getList(){let params=arguments.length>0&&arguments[0]!==undefined?arguments[0]:{};return apiClient.get('/api/receipt/GetList',{params});}/**\n   * Get receipt by ID\n   */static async getById(id){return apiClient.get(\"/api/receipt/\".concat(id));}/**\n   * Create new receipt\n   */static async create(receipt){return apiClient.post('/api/receipt/Insert',receipt);}/**\n   * Update receipt\n   */static async update(receipt){return apiClient.put('/api/receipt/Update',receipt);}/**\n   * Delete receipt\n   */static async delete(id){return apiClient.delete(\"/api/receipt/\".concat(id));}}/**\n * Doctor/User API endpoints\n */export class DoctorApi{/**\n   * Get doctors list\n   */static async getList(){let params=arguments.length>0&&arguments[0]!==undefined?arguments[0]:{};return apiClient.get('/api/users/GetList',{params});}/**\n   * Get doctor by ID\n   */static async getById(id){return apiClient.get(\"/api/users/\".concat(id));}}/**\n * Authentication API endpoints\n */export class AuthApi{/**\n   * Login user\n   */static async login(credentials){return apiClient.post('/api/auth/login',credentials);}/**\n   * Logout user\n   */static async logout(userid){return apiClient.post('/api/auth/logout',userid);}/**\n   * Refresh token\n   */static async refreshToken(){return apiClient.post('/api/auth/refresh');}}/**\n * Permission API endpoints\n */export class PermissionApi{/**\n   * Get all defined permissions\n   */static async getAllPermissions(){return apiClient.get('/api/permissions/all');}/**\n   * Get permissions for a specific role\n   */static async getRolePermissions(roleId){return apiClient.get(\"/api/permissions/role/\".concat(roleId));}/**\n   * Update permissions for a specific role\n   */static async updateRolePermissions(roleId,permissionCodes){return apiClient.post(\"/api/permissions/role/\".concat(roleId),permissionCodes);}/**\n   * Get current user's permissions\n   */static async getCurrentUserPermissions(){return apiClient.get('/api/permissions/user/current');}}", "map": {"version": 3, "names": ["apiClient", "SystemApi", "getMenus", "get", "getDataTypes", "Patient<PERSON><PERSON>", "getList", "params", "arguments", "length", "undefined", "getById", "id", "concat", "create", "patient", "post", "update", "put", "delete", "TreatmentApi", "treatment", "orderNo", "ReceiptApi", "receipt", "<PERSON><PERSON><PERSON>", "AuthA<PERSON>", "login", "credentials", "logout", "userid", "refreshToken", "PermissionApi", "getAllPermissions", "getRolePermissions", "roleId", "updateRolePermissions", "permissionCodes", "getCurrentUserPermissions"], "sources": ["C:/Users/<USER>/Desktop/demo-git/demo-react/my-fullstack-app/frontend/src/services/apiService.ts"], "sourcesContent": ["/**\n * Typed API service layer\n */\nimport { apiClient } from './api';\nimport {\n  MenuGroupItem,\n  DataTypeGroupItem,\n  Patient,\n  Treatment,\n  Receipt,\n  UserRole,\n  PatientSearchParams,\n  TreatmentSearchParams,\n  ReceiptSearchParams,\n  LoginResponse,\n  Permission, // 新增 Permission 類型\n} from '../types/api';\n\n/**\n * System API endpoints\n */\nexport class SystemApi {\n  /**\n   * Get menu items for authenticated user\n   */\n  static async getMenus(): Promise<MenuGroupItem[]> {\n    return apiClient.get<MenuGroupItem[]>('/api/system/GetMenus');\n  }\n\n  /**\n   * Get data types\n   */\n  static async getDataTypes(): Promise<DataTypeGroupItem[]> {\n    return apiClient.get<DataTypeGroupItem[]>('/api/system/GetDataType');\n  }\n}\n\n/**\n * Patient API endpoints\n */\nexport class PatientApi {\n  /**\n   * Get patients list with optional filters\n   */\n  static async getList(params: PatientSearchParams = {}): Promise<Patient[]> {\n    return apiClient.get<Patient[]>('/api/patients/GetList', { params });\n  }\n\n  /**\n   * Get patient by ID\n   */\n  static async getById(id: number): Promise<Patient> {\n    return apiClient.get<Patient>(`/api/patients/${id}`);\n  }\n\n  /**\n   * Create new patient\n   */\n  static async create(patient: Omit<Patient, 'Id' | 'CreatedAt' | 'UpdatedAt'>): Promise<Patient> {\n    return apiClient.post<Patient>('/api/patients/Insert', patient);\n  }\n\n  /**\n   * Update patient\n   */\n  static async update(patient: Patient): Promise<void> {\n    return apiClient.put<void>('/api/patients/Update', patient);\n  }\n\n  /**\n   * Delete patient\n   */\n  static async delete(id: number): Promise<void> {\n    return apiClient.get<void>(`/api/patients/Delete?id=${id}`);\n  }\n}\n\n/**\n * Treatment API endpoints\n */\nexport class TreatmentApi {\n  /**\n   * Get treatments list with optional filters\n   */\n  static async getList(params: TreatmentSearchParams = {}): Promise<Treatment[]> {\n    return apiClient.get<Treatment[]>('/api/treatment/GetList', { params });\n  }\n\n  /**\n   * Get treatment by ID\n   */\n  static async getById(id: number): Promise<Treatment> {\n    return apiClient.get<Treatment>(`/api/treatment/${id}`);\n  }\n\n  /**\n   * Create new treatment\n   */\n  static async create(treatment: Omit<Treatment, 'Id' | 'CreatedAt' | 'UpdatedAt'>): Promise<void> {\n    return apiClient.post<void>('/api/treatment/Insert', treatment);\n  }\n\n  /**\n   * Update treatment\n   */\n  static async update(treatment: Treatment): Promise<void> {\n    return apiClient.put<void>('/api/treatment/Update', treatment);\n  }\n\n  /**\n   * Delete treatment\n   */\n  static async delete(orderNo: string): Promise<void> {\n    return apiClient.get<void>(`/api/treatment/Delete?OrderNo=${orderNo}`);\n  }\n}\n\n/**\n * Receipt API endpoints\n */\nexport class ReceiptApi {\n  /**\n   * Get receipts list with optional filters\n   */\n  static async getList(params: ReceiptSearchParams = {}): Promise<Receipt[]> {\n    return apiClient.get<Receipt[]>('/api/receipt/GetList', { params });\n  }\n\n  /**\n   * Get receipt by ID\n   */\n  static async getById(id: number): Promise<Receipt> {\n    return apiClient.get<Receipt>(`/api/receipt/${id}`);\n  }\n\n  /**\n   * Create new receipt\n   */\n  static async create(receipt: Omit<Receipt, 'Id' | 'CreatedAt' | 'UpdatedAt'>): Promise<void> {\n    return apiClient.post<void>('/api/receipt/Insert', receipt);\n  }\n\n  /**\n   * Update receipt\n   */\n  static async update(receipt: Receipt): Promise<void> {\n    return apiClient.put<void>('/api/receipt/Update', receipt);\n  }\n\n  /**\n   * Delete receipt\n   */\n  static async delete(id: number): Promise<void> {\n    return apiClient.delete(`/api/receipt/${id}`);\n  }\n}\n\n/**\n * Doctor/User API endpoints\n */\nexport class DoctorApi {\n  /**\n   * Get doctors list\n   */\n  static async getList(params: { UserName?: string; RoleId?: number } = {}): Promise<UserRole[]> {\n    return apiClient.get<UserRole[]>('/api/users/GetList', { params });\n  }\n\n  /**\n   * Get doctor by ID\n   */\n  static async getById(id: number): Promise<UserRole> {\n    return apiClient.get<UserRole>(`/api/users/${id}`);\n  }\n}\n\n/**\n * Authentication API endpoints\n */\nexport class AuthApi {\n  /**\n   * Login user\n   */\n  static async login(credentials: { username: string; password: string }): Promise<LoginResponse> {\n    return apiClient.post<LoginResponse>('/api/auth/login', credentials);\n  }\n\n  /**\n   * Logout user\n   */\n  static async logout(userid : number): Promise<void> {\n    return apiClient.post('/api/auth/logout', userid);\n  }\n\n  /**\n   * Refresh token\n   */\n  static async refreshToken(): Promise<{ token: string }> {\n    return apiClient.post<{ token: string }>('/api/auth/refresh');\n  }\n}\n\n/**\n * Permission API endpoints\n */\nexport class PermissionApi {\n  /**\n   * Get all defined permissions\n   */\n  static async getAllPermissions(): Promise<Permission[]> {\n    return apiClient.get<Permission[]>('/api/permissions/all');\n  }\n\n  /**\n   * Get permissions for a specific role\n   */\n  static async getRolePermissions(roleId: number): Promise<string[]> {\n    return apiClient.get<string[]>(`/api/permissions/role/${roleId}`);\n  }\n\n  /**\n   * Update permissions for a specific role\n   */\n  static async updateRolePermissions(roleId: number, permissionCodes: string[]): Promise<void> {\n    return apiClient.post<void>(`/api/permissions/role/${roleId}`, permissionCodes);\n  }\n\n  /**\n   * Get current user's permissions\n   */\n  static async getCurrentUserPermissions(): Promise<string[]> {\n    return apiClient.get<string[]>('/api/permissions/user/current');\n  }\n}"], "mappings": "AAAA;AACA;AACA,GACA,OAASA,SAAS,KAAQ,OAAO,CAejC;AACA;AACA,GACA,MAAO,MAAM,CAAAC,SAAU,CACrB;AACF;AACA,KACE,YAAa,CAAAC,QAAQA,CAAA,CAA6B,CAChD,MAAO,CAAAF,SAAS,CAACG,GAAG,CAAkB,sBAAsB,CAAC,CAC/D,CAEA;AACF;AACA,KACE,YAAa,CAAAC,YAAYA,CAAA,CAAiC,CACxD,MAAO,CAAAJ,SAAS,CAACG,GAAG,CAAsB,yBAAyB,CAAC,CACtE,CACF,CAEA;AACA;AACA,GACA,MAAO,MAAM,CAAAE,UAAW,CACtB;AACF;AACA,KACE,YAAa,CAAAC,OAAOA,CAAA,CAAuD,IAAtD,CAAAC,MAA2B,CAAAC,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAE,SAAA,CAAAF,SAAA,IAAG,CAAC,CAAC,CACnD,MAAO,CAAAR,SAAS,CAACG,GAAG,CAAY,uBAAuB,CAAE,CAAEI,MAAO,CAAC,CAAC,CACtE,CAEA;AACF;AACA,KACE,YAAa,CAAAI,OAAOA,CAACC,EAAU,CAAoB,CACjD,MAAO,CAAAZ,SAAS,CAACG,GAAG,kBAAAU,MAAA,CAA2BD,EAAE,CAAE,CAAC,CACtD,CAEA;AACF;AACA,KACE,YAAa,CAAAE,MAAMA,CAACC,OAAwD,CAAoB,CAC9F,MAAO,CAAAf,SAAS,CAACgB,IAAI,CAAU,sBAAsB,CAAED,OAAO,CAAC,CACjE,CAEA;AACF;AACA,KACE,YAAa,CAAAE,MAAMA,CAACF,OAAgB,CAAiB,CACnD,MAAO,CAAAf,SAAS,CAACkB,GAAG,CAAO,sBAAsB,CAAEH,OAAO,CAAC,CAC7D,CAEA;AACF;AACA,KACE,YAAa,CAAAI,MAAMA,CAACP,EAAU,CAAiB,CAC7C,MAAO,CAAAZ,SAAS,CAACG,GAAG,4BAAAU,MAAA,CAAkCD,EAAE,CAAE,CAAC,CAC7D,CACF,CAEA;AACA;AACA,GACA,MAAO,MAAM,CAAAQ,YAAa,CACxB;AACF;AACA,KACE,YAAa,CAAAd,OAAOA,CAAA,CAA2D,IAA1D,CAAAC,MAA6B,CAAAC,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAE,SAAA,CAAAF,SAAA,IAAG,CAAC,CAAC,CACrD,MAAO,CAAAR,SAAS,CAACG,GAAG,CAAc,wBAAwB,CAAE,CAAEI,MAAO,CAAC,CAAC,CACzE,CAEA;AACF;AACA,KACE,YAAa,CAAAI,OAAOA,CAACC,EAAU,CAAsB,CACnD,MAAO,CAAAZ,SAAS,CAACG,GAAG,mBAAAU,MAAA,CAA8BD,EAAE,CAAE,CAAC,CACzD,CAEA;AACF;AACA,KACE,YAAa,CAAAE,MAAMA,CAACO,SAA4D,CAAiB,CAC/F,MAAO,CAAArB,SAAS,CAACgB,IAAI,CAAO,uBAAuB,CAAEK,SAAS,CAAC,CACjE,CAEA;AACF;AACA,KACE,YAAa,CAAAJ,MAAMA,CAACI,SAAoB,CAAiB,CACvD,MAAO,CAAArB,SAAS,CAACkB,GAAG,CAAO,uBAAuB,CAAEG,SAAS,CAAC,CAChE,CAEA;AACF;AACA,KACE,YAAa,CAAAF,MAAMA,CAACG,OAAe,CAAiB,CAClD,MAAO,CAAAtB,SAAS,CAACG,GAAG,kCAAAU,MAAA,CAAwCS,OAAO,CAAE,CAAC,CACxE,CACF,CAEA;AACA;AACA,GACA,MAAO,MAAM,CAAAC,UAAW,CACtB;AACF;AACA,KACE,YAAa,CAAAjB,OAAOA,CAAA,CAAuD,IAAtD,CAAAC,MAA2B,CAAAC,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAE,SAAA,CAAAF,SAAA,IAAG,CAAC,CAAC,CACnD,MAAO,CAAAR,SAAS,CAACG,GAAG,CAAY,sBAAsB,CAAE,CAAEI,MAAO,CAAC,CAAC,CACrE,CAEA;AACF;AACA,KACE,YAAa,CAAAI,OAAOA,CAACC,EAAU,CAAoB,CACjD,MAAO,CAAAZ,SAAS,CAACG,GAAG,iBAAAU,MAAA,CAA0BD,EAAE,CAAE,CAAC,CACrD,CAEA;AACF;AACA,KACE,YAAa,CAAAE,MAAMA,CAACU,OAAwD,CAAiB,CAC3F,MAAO,CAAAxB,SAAS,CAACgB,IAAI,CAAO,qBAAqB,CAAEQ,OAAO,CAAC,CAC7D,CAEA;AACF;AACA,KACE,YAAa,CAAAP,MAAMA,CAACO,OAAgB,CAAiB,CACnD,MAAO,CAAAxB,SAAS,CAACkB,GAAG,CAAO,qBAAqB,CAAEM,OAAO,CAAC,CAC5D,CAEA;AACF;AACA,KACE,YAAa,CAAAL,MAAMA,CAACP,EAAU,CAAiB,CAC7C,MAAO,CAAAZ,SAAS,CAACmB,MAAM,iBAAAN,MAAA,CAAiBD,EAAE,CAAE,CAAC,CAC/C,CACF,CAEA;AACA;AACA,GACA,MAAO,MAAM,CAAAa,SAAU,CACrB;AACF;AACA,KACE,YAAa,CAAAnB,OAAOA,CAAA,CAA2E,IAA1E,CAAAC,MAA8C,CAAAC,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAE,SAAA,CAAAF,SAAA,IAAG,CAAC,CAAC,CACtE,MAAO,CAAAR,SAAS,CAACG,GAAG,CAAa,oBAAoB,CAAE,CAAEI,MAAO,CAAC,CAAC,CACpE,CAEA;AACF;AACA,KACE,YAAa,CAAAI,OAAOA,CAACC,EAAU,CAAqB,CAClD,MAAO,CAAAZ,SAAS,CAACG,GAAG,eAAAU,MAAA,CAAyBD,EAAE,CAAE,CAAC,CACpD,CACF,CAEA;AACA;AACA,GACA,MAAO,MAAM,CAAAc,OAAQ,CACnB;AACF;AACA,KACE,YAAa,CAAAC,KAAKA,CAACC,WAAmD,CAA0B,CAC9F,MAAO,CAAA5B,SAAS,CAACgB,IAAI,CAAgB,iBAAiB,CAAEY,WAAW,CAAC,CACtE,CAEA;AACF;AACA,KACE,YAAa,CAAAC,MAAMA,CAACC,MAAe,CAAiB,CAClD,MAAO,CAAA9B,SAAS,CAACgB,IAAI,CAAC,kBAAkB,CAAEc,MAAM,CAAC,CACnD,CAEA;AACF;AACA,KACE,YAAa,CAAAC,YAAYA,CAAA,CAA+B,CACtD,MAAO,CAAA/B,SAAS,CAACgB,IAAI,CAAoB,mBAAmB,CAAC,CAC/D,CACF,CAEA;AACA;AACA,GACA,MAAO,MAAM,CAAAgB,aAAc,CACzB;AACF;AACA,KACE,YAAa,CAAAC,iBAAiBA,CAAA,CAA0B,CACtD,MAAO,CAAAjC,SAAS,CAACG,GAAG,CAAe,sBAAsB,CAAC,CAC5D,CAEA;AACF;AACA,KACE,YAAa,CAAA+B,kBAAkBA,CAACC,MAAc,CAAqB,CACjE,MAAO,CAAAnC,SAAS,CAACG,GAAG,0BAAAU,MAAA,CAAoCsB,MAAM,CAAE,CAAC,CACnE,CAEA;AACF;AACA,KACE,YAAa,CAAAC,qBAAqBA,CAACD,MAAc,CAAEE,eAAyB,CAAiB,CAC3F,MAAO,CAAArC,SAAS,CAACgB,IAAI,0BAAAH,MAAA,CAAgCsB,MAAM,EAAIE,eAAe,CAAC,CACjF,CAEA;AACF;AACA,KACE,YAAa,CAAAC,yBAAyBA,CAAA,CAAsB,CAC1D,MAAO,CAAAtC,SAAS,CAACG,GAAG,CAAW,+BAA+B,CAAC,CACjE,CACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "import _objectSpread from\"C:/Users/<USER>/Desktop/demo-git/demo-react/my-fullstack-app/frontend/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";import React from'react';import{Button}from'primereact/button';import{Card}from'primereact/card';import{log}from'../../utils/logger';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";class ErrorBoundary extends React.Component{constructor(props){super(props);this.resetError=()=>{this.setState({hasError:false});};this.state={hasError:false};}static getDerivedStateFromError(error){return{hasError:true,error};}componentDidCatch(error,errorInfo){// 記錄錯誤到日誌系統\nlog.error('ErrorBoundary 捕獲到錯誤',{error:error.message,stack:error.stack,componentStack:errorInfo.componentStack});// 調用自定義錯誤處理函數\nif(this.props.onError){this.props.onError(error,errorInfo);}// 可以在這裡發送錯誤到監控服務\n// 例如: Sentry.captureException(error, { contexts: { react: errorInfo } });\n}render(){if(this.state.hasError){if(this.props.fallback){const FallbackComponent=this.props.fallback;return/*#__PURE__*/_jsx(FallbackComponent,_objectSpread(_objectSpread({},this.state.error&&{error:this.state.error}),{},{resetError:this.resetError}));}return/*#__PURE__*/_jsx(\"div\",{className:\"flex align-items-center justify-content-center min-h-screen\",children:/*#__PURE__*/_jsx(Card,{className:\"max-w-md\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"text-center\",children:[/*#__PURE__*/_jsx(\"i\",{className:\"pi pi-exclamation-triangle text-6xl text-red-500 mb-3\"}),/*#__PURE__*/_jsx(\"h2\",{className:\"text-2xl font-bold text-gray-800 mb-3\",children:\"\\u767C\\u751F\\u932F\\u8AA4\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-gray-600 mb-4\",children:\"\\u62B1\\u6B49\\uFF0C\\u61C9\\u7528\\u7A0B\\u5F0F\\u9047\\u5230\\u4E86\\u4E00\\u500B\\u554F\\u984C\\u3002\"}),process.env.NODE_ENV==='development'&&this.state.error&&/*#__PURE__*/_jsxs(\"div\",{className:\"mb-4 p-3 bg-red-50 border-round text-left\",children:[/*#__PURE__*/_jsx(\"p\",{className:\"text-red-700 font-medium mb-2\",children:\"\\u932F\\u8AA4\\u8A73\\u60C5\\uFF1A\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-red-600 text-sm mb-2\",children:this.state.error.message}),this.state.error.stack&&/*#__PURE__*/_jsx(\"pre\",{className:\"text-xs text-red-500 overflow-auto max-h-10rem\",children:this.state.error.stack})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-column gap-2\",children:[/*#__PURE__*/_jsx(Button,{label:\"\\u91CD\\u8A66\",icon:\"pi pi-refresh\",onClick:this.resetError,className:\"p-button-primary\"}),/*#__PURE__*/_jsx(Button,{label:\"\\u91CD\\u65B0\\u8F09\\u5165\\u9801\\u9762\",icon:\"pi pi-sync\",onClick:()=>window.location.reload(),className:\"p-button-secondary\",outlined:true})]})]})})});}return this.props.children;}}export default ErrorBoundary;", "map": {"version": 3, "names": ["React", "<PERSON><PERSON>", "Card", "log", "jsx", "_jsx", "jsxs", "_jsxs", "Error<PERSON>ou<PERSON><PERSON>", "Component", "constructor", "props", "resetError", "setState", "<PERSON><PERSON><PERSON><PERSON>", "state", "getDerivedStateFromError", "error", "componentDidCatch", "errorInfo", "message", "stack", "componentStack", "onError", "render", "fallback", "FallbackComponent", "_objectSpread", "className", "children", "process", "env", "NODE_ENV", "label", "icon", "onClick", "window", "location", "reload", "outlined"], "sources": ["C:/Users/<USER>/Desktop/demo-git/demo-react/my-fullstack-app/frontend/src/components/Common/ErrorBoundary.tsx"], "sourcesContent": ["import React from 'react';\r\nimport { Button } from 'primereact/button';\r\nimport { Card } from 'primereact/card';\r\nimport { log } from '../../utils/logger';\r\n\r\ninterface ErrorBoundaryState {\r\n  hasError: boolean;\r\n  error?: Error;\r\n}\r\n\r\ninterface ErrorBoundaryProps {\r\n  children: React.ReactNode;\r\n  fallback?: React.ComponentType<{ error?: Error; resetError: () => void }>;\r\n  onError?: (error: Error, errorInfo: React.ErrorInfo) => void;\r\n}\r\n\r\nclass ErrorBoundary extends React.Component<ErrorBoundaryProps, ErrorBoundaryState> {\r\n  constructor(props: ErrorBoundaryProps) {\r\n    super(props);\r\n    this.state = { hasError: false };\r\n  }\r\n\r\n  static getDerivedStateFromError(error: Error): ErrorBoundaryState {\r\n    return { hasError: true, error };\r\n  }\r\n\r\n  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {\r\n    // 記錄錯誤到日誌系統\r\n    log.error('ErrorBoundary 捕獲到錯誤', {\r\n      error: error.message,\r\n      stack: error.stack,\r\n      componentStack: errorInfo.componentStack\r\n    });\r\n\r\n    // 調用自定義錯誤處理函數\r\n    if (this.props.onError) {\r\n      this.props.onError(error, errorInfo);\r\n    }\r\n\r\n    // 可以在這裡發送錯誤到監控服務\r\n    // 例如: Sentry.captureException(error, { contexts: { react: errorInfo } });\r\n  }\r\n\r\n  resetError = () => {\r\n    this.setState({ hasError: false });\r\n  };\r\n\r\n  render() {\r\n    if (this.state.hasError) {\r\n      if (this.props.fallback) {\r\n        const FallbackComponent = this.props.fallback;\r\n        return <FallbackComponent {...(this.state.error && { error: this.state.error })} resetError={this.resetError} />;\r\n      }\r\n\r\n      return (\r\n        <div className=\"flex align-items-center justify-content-center min-h-screen\">\r\n          <Card className=\"max-w-md\">\r\n            <div className=\"text-center\">\r\n              <i className=\"pi pi-exclamation-triangle text-6xl text-red-500 mb-3\"></i>\r\n              <h2 className=\"text-2xl font-bold text-gray-800 mb-3\">發生錯誤</h2>\r\n              <p className=\"text-gray-600 mb-4\">\r\n                抱歉，應用程式遇到了一個問題。\r\n              </p>\r\n              \r\n              {process.env.NODE_ENV === 'development' && this.state.error && (\r\n                <div className=\"mb-4 p-3 bg-red-50 border-round text-left\">\r\n                  <p className=\"text-red-700 font-medium mb-2\">錯誤詳情：</p>\r\n                  <p className=\"text-red-600 text-sm mb-2\">{this.state.error.message}</p>\r\n                  {this.state.error.stack && (\r\n                    <pre className=\"text-xs text-red-500 overflow-auto max-h-10rem\">\r\n                      {this.state.error.stack}\r\n                    </pre>\r\n                  )}\r\n                </div>\r\n              )}\r\n\r\n              <div className=\"flex flex-column gap-2\">\r\n                <Button\r\n                  label=\"重試\"\r\n                  icon=\"pi pi-refresh\"\r\n                  onClick={this.resetError}\r\n                  className=\"p-button-primary\"\r\n                />\r\n                \r\n                <Button\r\n                  label=\"重新載入頁面\"\r\n                  icon=\"pi pi-sync\"\r\n                  onClick={() => window.location.reload()}\r\n                  className=\"p-button-secondary\"\r\n                  outlined\r\n                />\r\n              </div>\r\n            </div>\r\n          </Card>\r\n        </div>\r\n      );\r\n    }\r\n\r\n    return this.props.children;\r\n  }\r\n}\r\n\r\nexport default ErrorBoundary;\r\n"], "mappings": "wJAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CACzB,OAASC,MAAM,KAAQ,mBAAmB,CAC1C,OAASC,IAAI,KAAQ,iBAAiB,CACtC,OAASC,GAAG,KAAQ,oBAAoB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAazC,KAAM,CAAAC,aAAa,QAAS,CAAAR,KAAK,CAACS,SAAkD,CAClFC,WAAWA,CAACC,KAAyB,CAAE,CACrC,KAAK,CAACA,KAAK,CAAC,CAAC,KAyBfC,UAAU,CAAG,IAAM,CACjB,IAAI,CAACC,QAAQ,CAAC,CAAEC,QAAQ,CAAE,KAAM,CAAC,CAAC,CACpC,CAAC,CA1BC,IAAI,CAACC,KAAK,CAAG,CAAED,QAAQ,CAAE,KAAM,CAAC,CAClC,CAEA,MAAO,CAAAE,wBAAwBA,CAACC,KAAY,CAAsB,CAChE,MAAO,CAAEH,QAAQ,CAAE,IAAI,CAAEG,KAAM,CAAC,CAClC,CAEAC,iBAAiBA,CAACD,KAAY,CAAEE,SAA0B,CAAE,CAC1D;AACAhB,GAAG,CAACc,KAAK,CAAC,qBAAqB,CAAE,CAC/BA,KAAK,CAAEA,KAAK,CAACG,OAAO,CACpBC,KAAK,CAAEJ,KAAK,CAACI,KAAK,CAClBC,cAAc,CAAEH,SAAS,CAACG,cAC5B,CAAC,CAAC,CAEF;AACA,GAAI,IAAI,CAACX,KAAK,CAACY,OAAO,CAAE,CACtB,IAAI,CAACZ,KAAK,CAACY,OAAO,CAACN,KAAK,CAAEE,SAAS,CAAC,CACtC,CAEA;AACA;AACF,CAMAK,MAAMA,CAAA,CAAG,CACP,GAAI,IAAI,CAACT,KAAK,CAACD,QAAQ,CAAE,CACvB,GAAI,IAAI,CAACH,KAAK,CAACc,QAAQ,CAAE,CACvB,KAAM,CAAAC,iBAAiB,CAAG,IAAI,CAACf,KAAK,CAACc,QAAQ,CAC7C,mBAAOpB,IAAA,CAACqB,iBAAiB,CAAAC,aAAA,CAAAA,aAAA,IAAM,IAAI,CAACZ,KAAK,CAACE,KAAK,EAAI,CAAEA,KAAK,CAAE,IAAI,CAACF,KAAK,CAACE,KAAM,CAAC,MAAGL,UAAU,CAAE,IAAI,CAACA,UAAW,EAAE,CAAC,CAClH,CAEA,mBACEP,IAAA,QAAKuB,SAAS,CAAC,6DAA6D,CAAAC,QAAA,cAC1ExB,IAAA,CAACH,IAAI,EAAC0B,SAAS,CAAC,UAAU,CAAAC,QAAA,cACxBtB,KAAA,QAAKqB,SAAS,CAAC,aAAa,CAAAC,QAAA,eAC1BxB,IAAA,MAAGuB,SAAS,CAAC,uDAAuD,CAAI,CAAC,cACzEvB,IAAA,OAAIuB,SAAS,CAAC,uCAAuC,CAAAC,QAAA,CAAC,0BAAI,CAAI,CAAC,cAC/DxB,IAAA,MAAGuB,SAAS,CAAC,oBAAoB,CAAAC,QAAA,CAAC,4FAElC,CAAG,CAAC,CAEHC,OAAO,CAACC,GAAG,CAACC,QAAQ,GAAK,aAAa,EAAI,IAAI,CAACjB,KAAK,CAACE,KAAK,eACzDV,KAAA,QAAKqB,SAAS,CAAC,2CAA2C,CAAAC,QAAA,eACxDxB,IAAA,MAAGuB,SAAS,CAAC,+BAA+B,CAAAC,QAAA,CAAC,gCAAK,CAAG,CAAC,cACtDxB,IAAA,MAAGuB,SAAS,CAAC,2BAA2B,CAAAC,QAAA,CAAE,IAAI,CAACd,KAAK,CAACE,KAAK,CAACG,OAAO,CAAI,CAAC,CACtE,IAAI,CAACL,KAAK,CAACE,KAAK,CAACI,KAAK,eACrBhB,IAAA,QAAKuB,SAAS,CAAC,gDAAgD,CAAAC,QAAA,CAC5D,IAAI,CAACd,KAAK,CAACE,KAAK,CAACI,KAAK,CACpB,CACN,EACE,CACN,cAEDd,KAAA,QAAKqB,SAAS,CAAC,wBAAwB,CAAAC,QAAA,eACrCxB,IAAA,CAACJ,MAAM,EACLgC,KAAK,CAAC,cAAI,CACVC,IAAI,CAAC,eAAe,CACpBC,OAAO,CAAE,IAAI,CAACvB,UAAW,CACzBgB,SAAS,CAAC,kBAAkB,CAC7B,CAAC,cAEFvB,IAAA,CAACJ,MAAM,EACLgC,KAAK,CAAC,sCAAQ,CACdC,IAAI,CAAC,YAAY,CACjBC,OAAO,CAAEA,CAAA,GAAMC,MAAM,CAACC,QAAQ,CAACC,MAAM,CAAC,CAAE,CACxCV,SAAS,CAAC,oBAAoB,CAC9BW,QAAQ,MACT,CAAC,EACC,CAAC,EACH,CAAC,CACF,CAAC,CACJ,CAAC,CAEV,CAEA,MAAO,KAAI,CAAC5B,KAAK,CAACkB,QAAQ,CAC5B,CACF,CAEA,cAAe,CAAArB,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
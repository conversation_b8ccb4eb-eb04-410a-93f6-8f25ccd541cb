{"ast": null, "code": "import _objectSpread from\"C:/Users/<USER>/Desktop/demo-git/demo-react/my-fullstack-app/frontend/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";import _objectWithoutProperties from\"C:/Users/<USER>/Desktop/demo-git/demo-react/my-fullstack-app/frontend/node_modules/@babel/runtime/helpers/esm/objectWithoutProperties.js\";const _excluded=[\"pageSize\"];import{useState,useEffect,useCallback,useRef}from'react';import{useErrorHandler}from'./useErrorHandler';/**\r\n * Generic hook for API data fetching with loading states and error handling\r\n */export function useApiData(apiCall){let options=arguments.length>1&&arguments[1]!==undefined?arguments[1]:{};const{initialData,dependencies=[],enabled=true,onSuccess,onError}=options;const[data,setData]=useState(initialData);const[loading,setLoading]=useState(false);const[error,setError]=useState(null);const{handleError}=useErrorHandler();const isMountedRef=useRef(true);const fetchData=useCallback(async()=>{if(!enabled)return;try{setLoading(true);setError(null);const result=await apiCall();if(isMountedRef.current){setData(result);onSuccess===null||onSuccess===void 0?void 0:onSuccess(result);}}catch(err){if(isMountedRef.current){const errorMessage=handleError(err);setError(errorMessage);onError===null||onError===void 0?void 0:onError(errorMessage);}}finally{if(isMountedRef.current){setLoading(false);}}},[apiCall,enabled,handleError,onSuccess,onError]);useEffect(()=>{fetchData();},[fetchData,...dependencies]);useEffect(()=>{return()=>{isMountedRef.current=false;};},[]);return{data,loading,error,refetch:fetchData,setData};}/**\r\n * Hook for paginated API data\r\n */export function usePaginatedData(apiCall){let options=arguments.length>1&&arguments[1]!==undefined?arguments[1]:{};const{pageSize=10}=options,restOptions=_objectWithoutProperties(options,_excluded);const[page,setPage]=useState(1);const[totalPages,setTotalPages]=useState(0);const paginatedApiCall=useCallback(async()=>{const result=await apiCall(page,pageSize);setTotalPages(Math.ceil(result.total/pageSize));return result.data;},[apiCall,page,pageSize]);const apiDataResult=useApiData(paginatedApiCall,_objectSpread(_objectSpread({},restOptions),{},{dependencies:[page,pageSize,...(restOptions.dependencies||[])]}));const nextPage=useCallback(()=>{setPage(prev=>Math.min(prev+1,totalPages));},[totalPages]);const previousPage=useCallback(()=>{setPage(prev=>Math.max(prev-1,1));},[]);const goToPage=useCallback(newPage=>{setPage(Math.max(1,Math.min(newPage,totalPages)));},[totalPages]);return _objectSpread(_objectSpread({},apiDataResult),{},{page,totalPages,hasNextPage:page<totalPages,hasPreviousPage:page>1,nextPage,previousPage,goToPage});}", "map": {"version": 3, "names": ["useState", "useEffect", "useCallback", "useRef", "useErrorHandler", "useApiData", "apiCall", "options", "arguments", "length", "undefined", "initialData", "dependencies", "enabled", "onSuccess", "onError", "data", "setData", "loading", "setLoading", "error", "setError", "handleError", "isMountedRef", "fetchData", "result", "current", "err", "errorMessage", "refetch", "usePaginatedData", "pageSize", "restOptions", "_objectWithoutProperties", "_excluded", "page", "setPage", "totalPages", "setTotalPages", "paginatedApiCall", "Math", "ceil", "total", "apiDataResult", "_objectSpread", "nextPage", "prev", "min", "previousPage", "max", "goToPage", "newPage", "hasNextPage", "hasPreviousPage"], "sources": ["C:/Users/<USER>/Desktop/demo-git/demo-react/my-fullstack-app/frontend/src/hooks/useApiData.ts"], "sourcesContent": ["import { useState, useEffect, useCallback, useRef } from 'react';\r\nimport { useError<PERSON>andler } from './useErrorHandler';\r\n\r\ninterface UseApiDataOptions<T> {\r\n  initialData?: T;\r\n  dependencies?: any[];\r\n  enabled?: boolean;\r\n  onSuccess?: (data: T) => void;\r\n  onError?: (error: string) => void;\r\n}\r\n\r\ninterface UseApiDataReturn<T> {\r\n  data: T | undefined;\r\n  loading: boolean;\r\n  error: string | null;\r\n  refetch: () => Promise<void>;\r\n  setData: (data: T | undefined) => void;\r\n}\r\n\r\n/**\r\n * Generic hook for API data fetching with loading states and error handling\r\n */\r\nexport function useApiData<T>(\r\n  apiCall: () => Promise<T>,\r\n  options: UseApiDataOptions<T> = {}\r\n): UseApiDataReturn<T> {\r\n  const {\r\n    initialData,\r\n    dependencies = [],\r\n    enabled = true,\r\n    onSuccess,\r\n    onError,\r\n  } = options;\r\n\r\n  const [data, setData] = useState<T | undefined>(initialData);\r\n  const [loading, setLoading] = useState(false);\r\n  const [error, setError] = useState<string | null>(null);\r\n  const { handleError } = useErrorHandler();\r\n  const isMountedRef = useRef(true);\r\n\r\n  const fetchData = useCallback(async () => {\r\n    if (!enabled) return;\r\n\r\n    try {\r\n      setLoading(true);\r\n      setError(null);\r\n      \r\n      const result = await apiCall();\r\n      \r\n      if (isMountedRef.current) {\r\n        setData(result);\r\n        onSuccess?.(result);\r\n      }\r\n    } catch (err) {\r\n      if (isMountedRef.current) {\r\n        const errorMessage = handleError(err);\r\n        setError(errorMessage);\r\n        onError?.(errorMessage);\r\n      }\r\n    } finally {\r\n      if (isMountedRef.current) {\r\n        setLoading(false);\r\n      }\r\n    }\r\n  }, [apiCall, enabled, handleError, onSuccess, onError]);\r\n\r\n  useEffect(() => {\r\n    fetchData();\r\n  }, [fetchData, ...dependencies]);\r\n\r\n  useEffect(() => {\r\n    return () => {\r\n      isMountedRef.current = false;\r\n    };\r\n  }, []);\r\n\r\n  return {\r\n    data,\r\n    loading,\r\n    error,\r\n    refetch: fetchData,\r\n    setData,\r\n  };\r\n}\r\n\r\n/**\r\n * Hook for paginated API data\r\n */\r\ninterface UsePaginatedDataOptions<T> extends UseApiDataOptions<T[]> {\r\n  pageSize?: number;\r\n}\r\n\r\ninterface UsePaginatedDataReturn<T> extends UseApiDataReturn<T[]> {\r\n  page: number;\r\n  totalPages: number;\r\n  hasNextPage: boolean;\r\n  hasPreviousPage: boolean;\r\n  nextPage: () => void;\r\n  previousPage: () => void;\r\n  goToPage: (page: number) => void;\r\n}\r\n\r\nexport function usePaginatedData<T>(\r\n  apiCall: (page: number, pageSize: number) => Promise<{ data: T[]; total: number }>,\r\n  options: UsePaginatedDataOptions<T> = {}\r\n): UsePaginatedDataReturn<T> {\r\n  const { pageSize = 10, ...restOptions } = options;\r\n  const [page, setPage] = useState(1);\r\n  const [totalPages, setTotalPages] = useState(0);\r\n\r\n  const paginatedApiCall = useCallback(async () => {\r\n    const result = await apiCall(page, pageSize);\r\n    setTotalPages(Math.ceil(result.total / pageSize));\r\n    return result.data;\r\n  }, [apiCall, page, pageSize]);\r\n\r\n  const apiDataResult = useApiData(paginatedApiCall, {\r\n    ...restOptions,\r\n    dependencies: [page, pageSize, ...(restOptions.dependencies || [])],\r\n  });\r\n\r\n  const nextPage = useCallback(() => {\r\n    setPage(prev => Math.min(prev + 1, totalPages));\r\n  }, [totalPages]);\r\n\r\n  const previousPage = useCallback(() => {\r\n    setPage(prev => Math.max(prev - 1, 1));\r\n  }, []);\r\n\r\n  const goToPage = useCallback((newPage: number) => {\r\n    setPage(Math.max(1, Math.min(newPage, totalPages)));\r\n  }, [totalPages]);\r\n\r\n  return {\r\n    ...apiDataResult,\r\n    page,\r\n    totalPages,\r\n    hasNextPage: page < totalPages,\r\n    hasPreviousPage: page > 1,\r\n    nextPage,\r\n    previousPage,\r\n    goToPage,\r\n  };\r\n}\r\n"], "mappings": "kWAAA,OAASA,QAAQ,CAAEC,SAAS,CAAEC,WAAW,CAAEC,MAAM,KAAQ,OAAO,CAChE,OAASC,eAAe,KAAQ,mBAAmB,CAkBnD;AACA;AACA,GACA,MAAO,SAAS,CAAAC,UAAUA,CACxBC,OAAyB,CAEJ,IADrB,CAAAC,OAA6B,CAAAC,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAE,SAAA,CAAAF,SAAA,IAAG,CAAC,CAAC,CAElC,KAAM,CACJG,WAAW,CACXC,YAAY,CAAG,EAAE,CACjBC,OAAO,CAAG,IAAI,CACdC,SAAS,CACTC,OACF,CAAC,CAAGR,OAAO,CAEX,KAAM,CAACS,IAAI,CAAEC,OAAO,CAAC,CAAGjB,QAAQ,CAAgBW,WAAW,CAAC,CAC5D,KAAM,CAACO,OAAO,CAAEC,UAAU,CAAC,CAAGnB,QAAQ,CAAC,KAAK,CAAC,CAC7C,KAAM,CAACoB,KAAK,CAAEC,QAAQ,CAAC,CAAGrB,QAAQ,CAAgB,IAAI,CAAC,CACvD,KAAM,CAAEsB,WAAY,CAAC,CAAGlB,eAAe,CAAC,CAAC,CACzC,KAAM,CAAAmB,YAAY,CAAGpB,MAAM,CAAC,IAAI,CAAC,CAEjC,KAAM,CAAAqB,SAAS,CAAGtB,WAAW,CAAC,SAAY,CACxC,GAAI,CAACW,OAAO,CAAE,OAEd,GAAI,CACFM,UAAU,CAAC,IAAI,CAAC,CAChBE,QAAQ,CAAC,IAAI,CAAC,CAEd,KAAM,CAAAI,MAAM,CAAG,KAAM,CAAAnB,OAAO,CAAC,CAAC,CAE9B,GAAIiB,YAAY,CAACG,OAAO,CAAE,CACxBT,OAAO,CAACQ,MAAM,CAAC,CACfX,SAAS,SAATA,SAAS,iBAATA,SAAS,CAAGW,MAAM,CAAC,CACrB,CACF,CAAE,MAAOE,GAAG,CAAE,CACZ,GAAIJ,YAAY,CAACG,OAAO,CAAE,CACxB,KAAM,CAAAE,YAAY,CAAGN,WAAW,CAACK,GAAG,CAAC,CACrCN,QAAQ,CAACO,YAAY,CAAC,CACtBb,OAAO,SAAPA,OAAO,iBAAPA,OAAO,CAAGa,YAAY,CAAC,CACzB,CACF,CAAC,OAAS,CACR,GAAIL,YAAY,CAACG,OAAO,CAAE,CACxBP,UAAU,CAAC,KAAK,CAAC,CACnB,CACF,CACF,CAAC,CAAE,CAACb,OAAO,CAAEO,OAAO,CAAES,WAAW,CAAER,SAAS,CAAEC,OAAO,CAAC,CAAC,CAEvDd,SAAS,CAAC,IAAM,CACduB,SAAS,CAAC,CAAC,CACb,CAAC,CAAE,CAACA,SAAS,CAAE,GAAGZ,YAAY,CAAC,CAAC,CAEhCX,SAAS,CAAC,IAAM,CACd,MAAO,IAAM,CACXsB,YAAY,CAACG,OAAO,CAAG,KAAK,CAC9B,CAAC,CACH,CAAC,CAAE,EAAE,CAAC,CAEN,MAAO,CACLV,IAAI,CACJE,OAAO,CACPE,KAAK,CACLS,OAAO,CAAEL,SAAS,CAClBP,OACF,CAAC,CACH,CAEA;AACA;AACA,GAeA,MAAO,SAAS,CAAAa,gBAAgBA,CAC9BxB,OAAkF,CAEvD,IAD3B,CAAAC,OAAmC,CAAAC,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAE,SAAA,CAAAF,SAAA,IAAG,CAAC,CAAC,CAExC,KAAM,CAAEuB,QAAQ,CAAG,EAAmB,CAAC,CAAGxB,OAAO,CAAvByB,WAAW,CAAAC,wBAAA,CAAK1B,OAAO,CAAA2B,SAAA,EACjD,KAAM,CAACC,IAAI,CAAEC,OAAO,CAAC,CAAGpC,QAAQ,CAAC,CAAC,CAAC,CACnC,KAAM,CAACqC,UAAU,CAAEC,aAAa,CAAC,CAAGtC,QAAQ,CAAC,CAAC,CAAC,CAE/C,KAAM,CAAAuC,gBAAgB,CAAGrC,WAAW,CAAC,SAAY,CAC/C,KAAM,CAAAuB,MAAM,CAAG,KAAM,CAAAnB,OAAO,CAAC6B,IAAI,CAAEJ,QAAQ,CAAC,CAC5CO,aAAa,CAACE,IAAI,CAACC,IAAI,CAAChB,MAAM,CAACiB,KAAK,CAAGX,QAAQ,CAAC,CAAC,CACjD,MAAO,CAAAN,MAAM,CAACT,IAAI,CACpB,CAAC,CAAE,CAACV,OAAO,CAAE6B,IAAI,CAAEJ,QAAQ,CAAC,CAAC,CAE7B,KAAM,CAAAY,aAAa,CAAGtC,UAAU,CAACkC,gBAAgB,CAAAK,aAAA,CAAAA,aAAA,IAC5CZ,WAAW,MACdpB,YAAY,CAAE,CAACuB,IAAI,CAAEJ,QAAQ,CAAE,IAAIC,WAAW,CAACpB,YAAY,EAAI,EAAE,CAAC,CAAC,EACpE,CAAC,CAEF,KAAM,CAAAiC,QAAQ,CAAG3C,WAAW,CAAC,IAAM,CACjCkC,OAAO,CAACU,IAAI,EAAIN,IAAI,CAACO,GAAG,CAACD,IAAI,CAAG,CAAC,CAAET,UAAU,CAAC,CAAC,CACjD,CAAC,CAAE,CAACA,UAAU,CAAC,CAAC,CAEhB,KAAM,CAAAW,YAAY,CAAG9C,WAAW,CAAC,IAAM,CACrCkC,OAAO,CAACU,IAAI,EAAIN,IAAI,CAACS,GAAG,CAACH,IAAI,CAAG,CAAC,CAAE,CAAC,CAAC,CAAC,CACxC,CAAC,CAAE,EAAE,CAAC,CAEN,KAAM,CAAAI,QAAQ,CAAGhD,WAAW,CAAEiD,OAAe,EAAK,CAChDf,OAAO,CAACI,IAAI,CAACS,GAAG,CAAC,CAAC,CAAET,IAAI,CAACO,GAAG,CAACI,OAAO,CAAEd,UAAU,CAAC,CAAC,CAAC,CACrD,CAAC,CAAE,CAACA,UAAU,CAAC,CAAC,CAEhB,OAAAO,aAAA,CAAAA,aAAA,IACKD,aAAa,MAChBR,IAAI,CACJE,UAAU,CACVe,WAAW,CAAEjB,IAAI,CAAGE,UAAU,CAC9BgB,eAAe,CAAElB,IAAI,CAAG,CAAC,CACzBU,QAAQ,CACRG,YAAY,CACZE,QAAQ,GAEZ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
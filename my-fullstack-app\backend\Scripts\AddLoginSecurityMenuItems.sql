-- 添加登入安全管理菜單項到系統管理群組

-- 獲取系統管理群組的ID
SET @systemGroupId = (SELECT Id FROM MenuGroups WHERE Name = '系統管理' LIMIT 1);

-- 如果系統管理群組不存在，則創建
INSERT INTO MenuGroups (Name, SortOrder, Icon, IsEnabled, OperatorUserId, CreatedAt, UpdatedAt)
SELECT '系統管理', 3, 'pi pi-cog', 1, 1, NOW(), NOW()
WHERE NOT EXISTS (
    SELECT 1 FROM MenuGroups WHERE Name = '系統管理'
);

-- 重新獲取系統管理群組的ID
SET @systemGroupId = (SELECT Id FROM MenuGroups WHERE Name = '系統管理' LIMIT 1);

-- 添加登入紀錄菜單項
INSERT INTO Menus (Path, Name, SortOrder, IsEnabled, OperatorUserId, GroupId, CreatedAt, UpdatedAt)
VALUES ('/login-logs', '登入紀錄', 40, 1, 1, @systemGroupId, NOW(), NOW())
ON DUPLICATE KEY UPDATE
    Name = '登入紀錄',
    SortOrder = 40,
    IsEnabled = 1,
    UpdatedAt = NOW();

-- 添加IP封鎖菜單項
INSERT INTO Menus (Path, Name, SortOrder, IsEnabled, OperatorUserId, GroupId, CreatedAt, UpdatedAt)
VALUES ('/ip-blocks', 'IP封鎖', 50, 1, 1, @systemGroupId, NOW(), NOW())
ON DUPLICATE KEY UPDATE
    Name = 'IP封鎖',
    SortOrder = 50,
    IsEnabled = 1,
    UpdatedAt = NOW();

-- 驗證插入結果
SELECT 
    mg.Name as GroupName,
    m.Name as MenuName,
    m.Path as MenuPath,
    m.SortOrder,
    m.IsEnabled
FROM Menus m
JOIN MenuGroups mg ON m.GroupId = mg.Id
WHERE m.Path IN ('/login-logs', '/ip-blocks')
ORDER BY m.SortOrder;

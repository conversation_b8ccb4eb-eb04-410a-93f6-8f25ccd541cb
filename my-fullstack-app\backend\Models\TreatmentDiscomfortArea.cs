using System;
using System.ComponentModel.DataAnnotations;

namespace MyApi.Models
{
    public class TreatmentDiscomfortArea
    {
        public int Id { get; set; }
        
        [Required]
        public int TreatmentId { get; set; }
        
        [Required]
        [MaxLength(50)]
        public string FrontAndBack { get; set; } = string.Empty;
        
        [Required]
        [MaxLength(100)]
        public string DiscomfortArea { get; set; } = string.Empty;
        
        [Required]
        [MaxLength(200)]
        public string DiscomfortSituation { get; set; } = string.Empty;
        
        [Range(0, 10)]
        public int DiscomfortDegree { get; set; }
        
        public DateTime CreatedAt { get; set; } = DateTime.Now;
        public DateTime UpdatedAt { get; set; } = DateTime.Now;
        public bool IsDelete { get; set; } = false;
        
        // Navigation property
        public Treatment Treatment { get; set; } = null!;
    }
}

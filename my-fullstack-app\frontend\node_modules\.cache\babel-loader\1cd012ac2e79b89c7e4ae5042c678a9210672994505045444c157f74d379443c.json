{"ast": null, "code": "import{formatInTimeZone}from'date-fns-tz';// Timezone for UTC+8\nconst TIME_ZONE='Asia/Taipei';/**\r\n * Formats a UTC date string or Date object into a specified format in UTC+8 timezone.\r\n * \r\n * @param date - The date to format (can be a string or Date object).\r\n * @param formatStr - The desired output format string (e.g., 'yyyy-MM-dd HH:mm:ss').\r\n * @param options - Optional configuration, including locale for i18n.\r\n * @returns The formatted date string in UTC+8.\r\n */export const formatUtcToTaipei=(date,formatStr,options)=>{try{return formatInTimeZone(date,TIME_ZONE,formatStr,options);}catch(error){console.error('Invalid date value for formatting:',date,error);return'Invalid Date';}};", "map": {"version": 3, "names": ["formatInTimeZone", "TIME_ZONE", "formatUtcToTaipei", "date", "formatStr", "options", "error", "console"], "sources": ["C:/Users/<USER>/Desktop/demo-git/demo-react/my-fullstack-app/frontend/src/utils/dateUtils.ts"], "sourcesContent": ["import { formatInTimeZone } from 'date-fns-tz';\r\nimport { Locale } from 'date-fns';\r\n\r\n// Timezone for UTC+8\r\nconst TIME_ZONE = 'Asia/Taipei';\r\n\r\ninterface FormatOptions {\r\n  locale?: Locale;\r\n}\r\n\r\n/**\r\n * Formats a UTC date string or Date object into a specified format in UTC+8 timezone.\r\n * \r\n * @param date - The date to format (can be a string or Date object).\r\n * @param formatStr - The desired output format string (e.g., 'yyyy-MM-dd HH:mm:ss').\r\n * @param options - Optional configuration, including locale for i18n.\r\n * @returns The formatted date string in UTC+8.\r\n */\r\nexport const formatUtcToTaipei = (\r\n  date: string | Date, \r\n  formatStr: string, \r\n  options?: FormatOptions\r\n): string => {\r\n  try {\r\n    return formatInTimeZone(date, TIME_ZONE, formatStr, options);\r\n  } catch (error) {\r\n    console.error('Invalid date value for formatting:', date, error);\r\n    return 'Invalid Date';\r\n  }\r\n};"], "mappings": "AAAA,OAASA,gBAAgB,KAAQ,aAAa,CAG9C;AACA,KAAM,CAAAC,SAAS,CAAG,aAAa,CAM/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GACA,MAAO,MAAM,CAAAC,iBAAiB,CAAGA,CAC/BC,IAAmB,CACnBC,SAAiB,CACjBC,OAAuB,GACZ,CACX,GAAI,CACF,MAAO,CAAAL,gBAAgB,CAACG,IAAI,CAAEF,SAAS,CAAEG,SAAS,CAAEC,OAAO,CAAC,CAC9D,CAAE,MAAOC,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,oCAAoC,CAAEH,IAAI,CAAEG,KAAK,CAAC,CAChE,MAAO,cAAc,CACvB,CACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
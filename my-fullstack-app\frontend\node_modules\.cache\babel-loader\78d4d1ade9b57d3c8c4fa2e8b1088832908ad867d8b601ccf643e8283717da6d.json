{"ast": null, "code": "/**\n * Returns the [year, month, day, hour, minute, seconds] tokens of the provided\n * `date` as it will be rendered in the `timeZone`.\n */\nexport function tzTokenizeDate(date, timeZone) {\n  const dtf = getDateTimeFormat(timeZone);\n  return 'formatToParts' in dtf ? partsOffset(dtf, date) : hackyOffset(dtf, date);\n}\nconst typeToPos = {\n  year: 0,\n  month: 1,\n  day: 2,\n  hour: 3,\n  minute: 4,\n  second: 5\n};\nfunction partsOffset(dtf, date) {\n  try {\n    const formatted = dtf.formatToParts(date);\n    const filled = [];\n    for (let i = 0; i < formatted.length; i++) {\n      const pos = typeToPos[formatted[i].type];\n      if (pos !== undefined) {\n        filled[pos] = parseInt(formatted[i].value, 10);\n      }\n    }\n    return filled;\n  } catch (error) {\n    if (error instanceof RangeError) {\n      return [NaN];\n    }\n    throw error;\n  }\n}\nfunction hackyOffset(dtf, date) {\n  const formatted = dtf.format(date);\n  // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n  const parsed = /(\\d+)\\/(\\d+)\\/(\\d+),? (\\d+):(\\d+):(\\d+)/.exec(formatted);\n  // const [, fMonth, fDay, fYear, fHour, fMinute, fSecond] = parsed\n  // return [fYear, fMonth, fDay, fHour, fMinute, fSecond]\n  return [parseInt(parsed[3], 10), parseInt(parsed[1], 10), parseInt(parsed[2], 10), parseInt(parsed[4], 10), parseInt(parsed[5], 10), parseInt(parsed[6], 10)];\n}\n// Get a cached Intl.DateTimeFormat instance for the IANA `timeZone`. This can be used\n// to get deterministic local date/time output according to the `en-US` locale which\n// can be used to extract local time parts as necessary.\nconst dtfCache = {};\n// New browsers use `hourCycle`, IE and Chrome <73 does not support it and uses `hour12`\nconst testDateFormatted = new Intl.DateTimeFormat('en-US', {\n  hourCycle: 'h23',\n  timeZone: 'America/New_York',\n  year: 'numeric',\n  month: '2-digit',\n  day: '2-digit',\n  hour: '2-digit',\n  minute: '2-digit',\n  second: '2-digit'\n}).format(new Date('2014-06-25T04:00:00.123Z'));\nconst hourCycleSupported = testDateFormatted === '06/25/2014, 00:00:00' || testDateFormatted === '‎06‎/‎25‎/‎2014‎ ‎00‎:‎00‎:‎00';\nfunction getDateTimeFormat(timeZone) {\n  if (!dtfCache[timeZone]) {\n    dtfCache[timeZone] = hourCycleSupported ? new Intl.DateTimeFormat('en-US', {\n      hourCycle: 'h23',\n      timeZone: timeZone,\n      year: 'numeric',\n      month: 'numeric',\n      day: '2-digit',\n      hour: '2-digit',\n      minute: '2-digit',\n      second: '2-digit'\n    }) : new Intl.DateTimeFormat('en-US', {\n      hour12: false,\n      timeZone: timeZone,\n      year: 'numeric',\n      month: 'numeric',\n      day: '2-digit',\n      hour: '2-digit',\n      minute: '2-digit',\n      second: '2-digit'\n    });\n  }\n  return dtfCache[timeZone];\n}", "map": {"version": 3, "names": ["tzTokenizeDate", "date", "timeZone", "dtf", "getDateTimeFormat", "partsOffset", "hackyOffset", "typeToPos", "year", "month", "day", "hour", "minute", "second", "formatted", "formatToParts", "filled", "i", "length", "pos", "type", "undefined", "parseInt", "value", "error", "RangeError", "NaN", "format", "parsed", "exec", "dtfCache", "testDateFormatted", "Intl", "DateTimeFormat", "hourCycle", "Date", "hourCycleSupported", "hour12"], "sources": ["C:/Users/<USER>/Desktop/demo-git/demo-react/my-fullstack-app/frontend/node_modules/date-fns-tz/dist/esm/_lib/tzTokenizeDate/index.js"], "sourcesContent": ["/**\n * Returns the [year, month, day, hour, minute, seconds] tokens of the provided\n * `date` as it will be rendered in the `timeZone`.\n */\nexport function tzTokenizeDate(date, timeZone) {\n    const dtf = getDateTimeFormat(timeZone);\n    return 'formatToParts' in dtf ? partsOffset(dtf, date) : hackyOffset(dtf, date);\n}\nconst typeToPos = {\n    year: 0,\n    month: 1,\n    day: 2,\n    hour: 3,\n    minute: 4,\n    second: 5,\n};\nfunction partsOffset(dtf, date) {\n    try {\n        const formatted = dtf.formatToParts(date);\n        const filled = [];\n        for (let i = 0; i < formatted.length; i++) {\n            const pos = typeToPos[formatted[i].type];\n            if (pos !== undefined) {\n                filled[pos] = parseInt(formatted[i].value, 10);\n            }\n        }\n        return filled;\n    }\n    catch (error) {\n        if (error instanceof RangeError) {\n            return [NaN];\n        }\n        throw error;\n    }\n}\nfunction hackyOffset(dtf, date) {\n    const formatted = dtf.format(date);\n    // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n    const parsed = /(\\d+)\\/(\\d+)\\/(\\d+),? (\\d+):(\\d+):(\\d+)/.exec(formatted);\n    // const [, fMonth, fDay, fYear, fHour, fMinute, fSecond] = parsed\n    // return [fYear, fMonth, fDay, fHour, fMinute, fSecond]\n    return [\n        parseInt(parsed[3], 10),\n        parseInt(parsed[1], 10),\n        parseInt(parsed[2], 10),\n        parseInt(parsed[4], 10),\n        parseInt(parsed[5], 10),\n        parseInt(parsed[6], 10),\n    ];\n}\n// Get a cached Intl.DateTimeFormat instance for the IANA `timeZone`. This can be used\n// to get deterministic local date/time output according to the `en-US` locale which\n// can be used to extract local time parts as necessary.\nconst dtfCache = {};\n// New browsers use `hourCycle`, IE and Chrome <73 does not support it and uses `hour12`\nconst testDateFormatted = new Intl.DateTimeFormat('en-US', {\n    hourCycle: 'h23',\n    timeZone: 'America/New_York',\n    year: 'numeric',\n    month: '2-digit',\n    day: '2-digit',\n    hour: '2-digit',\n    minute: '2-digit',\n    second: '2-digit',\n}).format(new Date('2014-06-25T04:00:00.123Z'));\nconst hourCycleSupported = testDateFormatted === '06/25/2014, 00:00:00' ||\n    testDateFormatted === '‎06‎/‎25‎/‎2014‎ ‎00‎:‎00‎:‎00';\nfunction getDateTimeFormat(timeZone) {\n    if (!dtfCache[timeZone]) {\n        dtfCache[timeZone] = hourCycleSupported\n            ? new Intl.DateTimeFormat('en-US', {\n                hourCycle: 'h23',\n                timeZone: timeZone,\n                year: 'numeric',\n                month: 'numeric',\n                day: '2-digit',\n                hour: '2-digit',\n                minute: '2-digit',\n                second: '2-digit',\n            })\n            : new Intl.DateTimeFormat('en-US', {\n                hour12: false,\n                timeZone: timeZone,\n                year: 'numeric',\n                month: 'numeric',\n                day: '2-digit',\n                hour: '2-digit',\n                minute: '2-digit',\n                second: '2-digit',\n            });\n    }\n    return dtfCache[timeZone];\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA,OAAO,SAASA,cAAcA,CAACC,IAAI,EAAEC,QAAQ,EAAE;EAC3C,MAAMC,GAAG,GAAGC,iBAAiB,CAACF,QAAQ,CAAC;EACvC,OAAO,eAAe,IAAIC,GAAG,GAAGE,WAAW,CAACF,GAAG,EAAEF,IAAI,CAAC,GAAGK,WAAW,CAACH,GAAG,EAAEF,IAAI,CAAC;AACnF;AACA,MAAMM,SAAS,GAAG;EACdC,IAAI,EAAE,CAAC;EACPC,KAAK,EAAE,CAAC;EACRC,GAAG,EAAE,CAAC;EACNC,IAAI,EAAE,CAAC;EACPC,MAAM,EAAE,CAAC;EACTC,MAAM,EAAE;AACZ,CAAC;AACD,SAASR,WAAWA,CAACF,GAAG,EAAEF,IAAI,EAAE;EAC5B,IAAI;IACA,MAAMa,SAAS,GAAGX,GAAG,CAACY,aAAa,CAACd,IAAI,CAAC;IACzC,MAAMe,MAAM,GAAG,EAAE;IACjB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,SAAS,CAACI,MAAM,EAAED,CAAC,EAAE,EAAE;MACvC,MAAME,GAAG,GAAGZ,SAAS,CAACO,SAAS,CAACG,CAAC,CAAC,CAACG,IAAI,CAAC;MACxC,IAAID,GAAG,KAAKE,SAAS,EAAE;QACnBL,MAAM,CAACG,GAAG,CAAC,GAAGG,QAAQ,CAACR,SAAS,CAACG,CAAC,CAAC,CAACM,KAAK,EAAE,EAAE,CAAC;MAClD;IACJ;IACA,OAAOP,MAAM;EACjB,CAAC,CACD,OAAOQ,KAAK,EAAE;IACV,IAAIA,KAAK,YAAYC,UAAU,EAAE;MAC7B,OAAO,CAACC,GAAG,CAAC;IAChB;IACA,MAAMF,KAAK;EACf;AACJ;AACA,SAASlB,WAAWA,CAACH,GAAG,EAAEF,IAAI,EAAE;EAC5B,MAAMa,SAAS,GAAGX,GAAG,CAACwB,MAAM,CAAC1B,IAAI,CAAC;EAClC;EACA,MAAM2B,MAAM,GAAG,yCAAyC,CAACC,IAAI,CAACf,SAAS,CAAC;EACxE;EACA;EACA,OAAO,CACHQ,QAAQ,CAACM,MAAM,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,EACvBN,QAAQ,CAACM,MAAM,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,EACvBN,QAAQ,CAACM,MAAM,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,EACvBN,QAAQ,CAACM,MAAM,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,EACvBN,QAAQ,CAACM,MAAM,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,EACvBN,QAAQ,CAACM,MAAM,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAC1B;AACL;AACA;AACA;AACA;AACA,MAAME,QAAQ,GAAG,CAAC,CAAC;AACnB;AACA,MAAMC,iBAAiB,GAAG,IAAIC,IAAI,CAACC,cAAc,CAAC,OAAO,EAAE;EACvDC,SAAS,EAAE,KAAK;EAChBhC,QAAQ,EAAE,kBAAkB;EAC5BM,IAAI,EAAE,SAAS;EACfC,KAAK,EAAE,SAAS;EAChBC,GAAG,EAAE,SAAS;EACdC,IAAI,EAAE,SAAS;EACfC,MAAM,EAAE,SAAS;EACjBC,MAAM,EAAE;AACZ,CAAC,CAAC,CAACc,MAAM,CAAC,IAAIQ,IAAI,CAAC,0BAA0B,CAAC,CAAC;AAC/C,MAAMC,kBAAkB,GAAGL,iBAAiB,KAAK,sBAAsB,IACnEA,iBAAiB,KAAK,gCAAgC;AAC1D,SAAS3B,iBAAiBA,CAACF,QAAQ,EAAE;EACjC,IAAI,CAAC4B,QAAQ,CAAC5B,QAAQ,CAAC,EAAE;IACrB4B,QAAQ,CAAC5B,QAAQ,CAAC,GAAGkC,kBAAkB,GACjC,IAAIJ,IAAI,CAACC,cAAc,CAAC,OAAO,EAAE;MAC/BC,SAAS,EAAE,KAAK;MAChBhC,QAAQ,EAAEA,QAAQ;MAClBM,IAAI,EAAE,SAAS;MACfC,KAAK,EAAE,SAAS;MAChBC,GAAG,EAAE,SAAS;MACdC,IAAI,EAAE,SAAS;MACfC,MAAM,EAAE,SAAS;MACjBC,MAAM,EAAE;IACZ,CAAC,CAAC,GACA,IAAImB,IAAI,CAACC,cAAc,CAAC,OAAO,EAAE;MAC/BI,MAAM,EAAE,KAAK;MACbnC,QAAQ,EAAEA,QAAQ;MAClBM,IAAI,EAAE,SAAS;MACfC,KAAK,EAAE,SAAS;MAChBC,GAAG,EAAE,SAAS;MACdC,IAAI,EAAE,SAAS;MACfC,MAAM,EAAE,SAAS;MACjBC,MAAM,EAAE;IACZ,CAAC,CAAC;EACV;EACA,OAAOiB,QAAQ,CAAC5B,QAAQ,CAAC;AAC7B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
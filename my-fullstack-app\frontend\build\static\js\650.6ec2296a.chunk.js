"use strict";(self.webpackChunkfrontend=self.webpackChunkfrontend||[]).push([[650],{3650:(e,t,r)=>{r.r(t),r.d(t,{default:()=>b});var a=r(9379),s=r(5043),n=r(2018),l=r(9642),i=r(1063),o=r(828),c=r(8150),d=r(3740),u=r(6104),p=r(2052),m=r(8060),f=r(5371),v=r(5855),y=r(402),h=r(8018),g=r(579);const b=()=>{const e=(0,s.useRef)(null),[t,r]=(0,s.useState)([]),[b,j]=(0,s.useState)(!0),[x,A]=(0,s.useState)(0),[S,w]=(0,s.useState)(0),[N,O]=(0,s.useState)(20),[P,E]=(0,s.useState)({ipAddress:"",device:"",status:"",createdAtStart:null,createdAtEnd:null}),C=async function(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:1,a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:20;try{j(!0),h.Rm.api("\u8f09\u5165\u767b\u5165\u7d00\u9304");const e={page:t,pageSize:a};P.ipAddress&&(e.ipAddress=P.ipAddress),P.device&&(e.device=P.device),P.status&&(e.status=P.status),P.createdAtStart&&(e.createdAtStart=P.createdAtStart.toISOString()),P.createdAtEnd&&(e.createdAtEnd=P.createdAtEnd.toISOString());const s=await y.A.get("/api/auth/GetUserLoginLog",{params:e});r(s.data.data),A(s.data.totalCount),h.Rm.api("\u767b\u5165\u7d00\u9304\u8f09\u5165\u6210\u529f",{count:s.data.data.length})}catch(n){var s;h.Rm.error("\u8f09\u5165\u767b\u5165\u7d00\u9304\u5931\u6557",n),null===(s=e.current)||void 0===s||s.show({severity:"error",summary:"\u8f09\u5165\u5931\u6557",detail:"\u7121\u6cd5\u8f09\u5165\u767b\u5165\u7d00\u9304",life:5e3})}finally{j(!1)}},I=(0,g.jsx)(n.$,{type:"button",icon:"pi pi-refresh",text:!0,onClick:()=>C(Math.floor(S/N)+1,N),disabled:b}),V=(0,g.jsx)("div",{});return(0,s.useEffect)((()=>{C()}),[]),b&&0===t.length?(0,g.jsx)("div",{className:"flex justify-content-center align-items-center",style:{height:"400px"},children:(0,g.jsx)(d.p,{})}):(0,g.jsxs)("div",{children:[(0,g.jsx)(o.y,{ref:e}),(0,g.jsx)(c.Z,{title:"\u767b\u5165\u7d00\u9304",className:"mb-4",children:(0,g.jsx)("p",{className:"text-600 line-height-3 m-0",children:"\u67e5\u770b\u7cfb\u7d71\u7528\u6236\u7684\u767b\u5165\u7d00\u9304\uff0c\u5305\u62ec\u6210\u529f\u548c\u5931\u6557\u7684\u767b\u5165\u5617\u8a66\u3002\u53ef\u4ee5\u6839\u64da IP \u4f4d\u5740\u3001\u88dd\u7f6e\u3001\u72c0\u614b\u548c\u6642\u9593\u7bc4\u570d\u9032\u884c\u7be9\u9078\u3002"})}),(0,g.jsx)(c.Z,{className:"mb-4",children:(0,g.jsxs)("div",{className:"grid",children:[(0,g.jsx)("div",{className:"col-6 md:col-3",children:(0,g.jsx)(p.S,{id:"ipAddress",value:P.ipAddress,onChange:e=>E((0,a.A)((0,a.A)({},P),{},{ipAddress:e.target.value})),placeholder:"\u8f38\u5165 IP \u4f4d\u5740",className:"w-full"})}),(0,g.jsx)("div",{className:"col-6 md:col-3",children:(0,g.jsx)(p.S,{id:"device",value:P.device,onChange:e=>E((0,a.A)((0,a.A)({},P),{},{device:e.target.value})),placeholder:"\u8f38\u5165\u88dd\u7f6e\u540d\u7a31",className:"w-full"})}),(0,g.jsx)("div",{className:"col-6 md:col-3",children:(0,g.jsx)(f.V,{value:P.createdAtStart,onChange:e=>E((0,a.A)((0,a.A)({},P),{},{createdAtStart:e.value})),placeholder:"\u9078\u64c7\u958b\u59cb\u65e5\u671f",className:"w-full",showIcon:!0,dateFormat:"yy/mm/dd"})}),(0,g.jsx)("div",{className:"col-6 md:col-3",children:(0,g.jsx)(f.V,{value:P.createdAtEnd,onChange:e=>E((0,a.A)((0,a.A)({},P),{},{createdAtEnd:e.value})),placeholder:"\u9078\u64c7\u7d50\u675f\u65e5\u671f",className:"w-full",showIcon:!0,dateFormat:"yy/mm/dd"})}),(0,g.jsx)("div",{className:"col-12 md:col-3",children:(0,g.jsx)(m.m,{id:"status",value:P.status,options:[{label:"\u5168\u90e8",value:""},{label:"\u767b\u5165\u6210\u529f",value:"Login Success"},{label:"\u767b\u51fa\u6210\u529f",value:"Logout Success"},{label:"\u767b\u5165\u5931\u6557",value:"Login Failed"},{label:"\u767b\u51fa\u5931\u6557",value:"Logout Failed"}],onChange:e=>E((0,a.A)((0,a.A)({},P),{},{status:e.value})),placeholder:"\u9078\u64c7\u72c0\u614b",className:"w-full"})}),(0,g.jsx)("div",{className:"col-12 md:col-4",children:(0,g.jsxs)("div",{className:"flex gap-2",children:[(0,g.jsx)(n.$,{label:"\u641c\u5c0b",icon:"pi pi-search",onClick:()=>{w(0),C(1,N)}}),(0,g.jsx)(n.$,{label:"\u91cd\u7f6e",icon:"pi pi-refresh",onClick:()=>{E({ipAddress:"",device:"",status:"",createdAtStart:null,createdAtEnd:null}),w(0),C(1,N)},className:"p-button-secondary"})]})})]})}),(0,g.jsx)(c.Z,{children:(0,g.jsxs)(i.b,{value:t,paginator:!0,lazy:!0,first:S,rows:N,totalRecords:x,onPage:e=>{w(e.first),O(e.rows);const t=Math.floor(e.first/e.rows)+1;C(t,e.rows)},rowsPerPageOptions:[10,20,50],emptyMessage:"\u6c92\u6709\u627e\u5230\u767b\u5165\u7d00\u9304",tableStyle:{minWidth:"50rem"},paginatorLeft:I,paginatorRight:V,loading:b,children:[(0,g.jsx)(l.V,{field:"username",header:"\u7528\u6236\u540d\u7a31",sortable:!0,style:{width:"15%"}}),(0,g.jsx)(l.V,{field:"ipAddress",header:"IP \u4f4d\u5740",sortable:!0,style:{width:"15%"}}),(0,g.jsx)(l.V,{field:"device",header:"\u88dd\u7f6e",style:{width:"15%"}}),(0,g.jsx)(l.V,{field:"browser",header:"\u700f\u89bd\u5668",style:{width:"15%"}}),(0,g.jsx)(l.V,{field:"status",header:"\u72c0\u614b",body:e=>{const t=e.status.includes("Success")?"success":"danger",r=e.status.includes("Login")?"\u767b\u5165":"\u767b\u51fa",a="success"===t?"\u6210\u529f":"\u5931\u6557";return(0,g.jsx)(u.v,{value:r+a,severity:t})},style:{width:"10%"}}),(0,g.jsx)(l.V,{field:"createdAt",header:"\u767b\u5165\u6642\u9593",body:e=>(e=>{if(!e)return"";try{return(0,v.$)(e,"yyyy/MM/dd HH:mm:ss")}catch(t){return console.error("Error formatting date:",t),e}})(e.createdAt),sortable:!0,style:{width:"20%"}})]})})]})}},6104:(e,t,r)=>{r.d(t,{v:()=>f});var a=r(5043),s=r(4052),n=r(1828),l=r(2028),i=r(4504);function o(e){return o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},o(e)}function c(e){var t=function(e,t){if("object"!=o(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var a=r.call(e,t||"default");if("object"!=o(a))return a;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==o(t)?t:t+""}function d(e,t,r){return(t=c(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}var u={value:"p-tag-value",icon:"p-tag-icon",root:function(e){var t=e.props;return(0,i.xW)("p-tag p-component",d(d({},"p-tag-".concat(t.severity),null!==t.severity),"p-tag-rounded",t.rounded))}},p=n.x.extend({defaultProps:{__TYPE:"Tag",value:null,severity:null,rounded:!1,icon:null,style:null,className:null,children:void 0},css:{classes:u,styles:"\n@layer primereact {\n    .p-tag {\n        display: inline-flex;\n        align-items: center;\n        justify-content: center;\n    }\n    \n    .p-tag-icon,\n    .p-tag-value,\n    .p-tag-icon.pi {\n        line-height: 1.5;\n    }\n    \n    .p-tag.p-tag-rounded {\n        border-radius: 10rem;\n    }\n}\n"}});function m(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);t&&(a=a.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,a)}return r}var f=a.forwardRef((function(e,t){var r=(0,l.qV)(),o=a.useContext(s.UM),c=p.getProps(e,o),u=p.setMetaData({props:c}),f=u.ptm,v=u.cx,y=u.isUnstyled;(0,n.j)(p.css.styles,y,{name:"tag"});var h=a.useRef(null),g=r({className:v("icon")},f("icon")),b=i.Hj.getJSXIcon(c.icon,function(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?m(Object(r),!0).forEach((function(t){d(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):m(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}({},g),{props:c});a.useImperativeHandle(t,(function(){return{props:c,getElement:function(){return h.current}}}));var j=r({ref:h,className:(0,i.xW)(c.className,v("root")),style:c.style},p.getOtherProps(c),f("root")),x=r({className:v("value")},f("value"));return a.createElement("span",j,b,a.createElement("span",x,c.value),a.createElement("span",null,c.children))}));f.displayName="Tag"}}]);
//# sourceMappingURL=650.6ec2296a.chunk.js.map
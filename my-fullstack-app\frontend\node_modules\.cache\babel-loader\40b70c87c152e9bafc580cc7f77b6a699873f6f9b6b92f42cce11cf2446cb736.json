{"ast": null, "code": "const formatDistanceLocale = {\n  lessThanXSeconds: {\n    one: \"minder as 'n sekonde\",\n    other: \"minder as {{count}} sekondes\"\n  },\n  xSeconds: {\n    one: \"1 sekonde\",\n    other: \"{{count}} sekondes\"\n  },\n  halfAMinute: \"'n halwe minuut\",\n  lessThanXMinutes: {\n    one: \"minder as 'n minuut\",\n    other: \"minder as {{count}} minute\"\n  },\n  xMinutes: {\n    one: \"'n minuut\",\n    other: \"{{count}} minute\"\n  },\n  aboutXHours: {\n    one: \"ongeveer 1 uur\",\n    other: \"ongeveer {{count}} ure\"\n  },\n  xHours: {\n    one: \"1 uur\",\n    other: \"{{count}} ure\"\n  },\n  xDays: {\n    one: \"1 dag\",\n    other: \"{{count}} dae\"\n  },\n  aboutXWeeks: {\n    one: \"ongeveer 1 week\",\n    other: \"ongeveer {{count}} weke\"\n  },\n  xWeeks: {\n    one: \"1 week\",\n    other: \"{{count}} weke\"\n  },\n  aboutXMonths: {\n    one: \"ongeveer 1 maand\",\n    other: \"ongeveer {{count}} maande\"\n  },\n  xMonths: {\n    one: \"1 maand\",\n    other: \"{{count}} maande\"\n  },\n  aboutXYears: {\n    one: \"ongeveer 1 jaar\",\n    other: \"ongeveer {{count}} jaar\"\n  },\n  xYears: {\n    one: \"1 jaar\",\n    other: \"{{count}} jaar\"\n  },\n  overXYears: {\n    one: \"meer as 1 jaar\",\n    other: \"meer as {{count}} jaar\"\n  },\n  almostXYears: {\n    one: \"byna 1 jaar\",\n    other: \"byna {{count}} jaar\"\n  }\n};\nexport const formatDistance = (token, count, options) => {\n  let result;\n  const tokenValue = formatDistanceLocale[token];\n  if (typeof tokenValue === \"string\") {\n    result = tokenValue;\n  } else if (count === 1) {\n    result = tokenValue.one;\n  } else {\n    result = tokenValue.other.replace(\"{{count}}\", String(count));\n  }\n  if (options !== null && options !== void 0 && options.addSuffix) {\n    if (options.comparison && options.comparison > 0) {\n      return \"oor \" + result;\n    } else {\n      return result + \" gelede\";\n    }\n  }\n  return result;\n};", "map": {"version": 3, "names": ["formatDistanceLocale", "lessThanXSeconds", "one", "other", "xSeconds", "halfAMinute", "lessThanXMinutes", "xMinutes", "aboutXHours", "xHours", "xDays", "aboutXWeeks", "xWeeks", "aboutXMonths", "xMonths", "aboutXYears", "xYears", "overXYears", "almostXYears", "formatDistance", "token", "count", "options", "result", "tokenValue", "replace", "String", "addSuffix", "comparison"], "sources": ["C:/Users/<USER>/Desktop/demo-git/demo-react/my-fullstack-app/frontend/node_modules/date-fns/locale/af/_lib/formatDistance.js"], "sourcesContent": ["const formatDistanceLocale = {\n  lessThanXSeconds: {\n    one: \"minder as 'n sekonde\",\n    other: \"minder as {{count}} sekondes\",\n  },\n\n  xSeconds: {\n    one: \"1 sekonde\",\n    other: \"{{count}} sekondes\",\n  },\n\n  halfAMinute: \"'n halwe minuut\",\n\n  lessThanXMinutes: {\n    one: \"minder as 'n minuut\",\n    other: \"minder as {{count}} minute\",\n  },\n\n  xMinutes: {\n    one: \"'n minuut\",\n    other: \"{{count}} minute\",\n  },\n\n  aboutXHours: {\n    one: \"ongeveer 1 uur\",\n    other: \"ongeveer {{count}} ure\",\n  },\n\n  xHours: {\n    one: \"1 uur\",\n    other: \"{{count}} ure\",\n  },\n\n  xDays: {\n    one: \"1 dag\",\n    other: \"{{count}} dae\",\n  },\n\n  aboutXWeeks: {\n    one: \"ongeveer 1 week\",\n    other: \"ongeveer {{count}} weke\",\n  },\n\n  xWeeks: {\n    one: \"1 week\",\n    other: \"{{count}} weke\",\n  },\n\n  aboutXMonths: {\n    one: \"ongeveer 1 maand\",\n    other: \"ongeveer {{count}} maande\",\n  },\n\n  xMonths: {\n    one: \"1 maand\",\n    other: \"{{count}} maande\",\n  },\n\n  aboutXYears: {\n    one: \"ongeveer 1 jaar\",\n    other: \"ongeveer {{count}} jaar\",\n  },\n\n  xYears: {\n    one: \"1 jaar\",\n    other: \"{{count}} jaar\",\n  },\n\n  overXYears: {\n    one: \"meer as 1 jaar\",\n    other: \"meer as {{count}} jaar\",\n  },\n\n  almostXYears: {\n    one: \"byna 1 jaar\",\n    other: \"byna {{count}} jaar\",\n  },\n};\n\nexport const formatDistance = (token, count, options) => {\n  let result;\n\n  const tokenValue = formatDistanceLocale[token];\n  if (typeof tokenValue === \"string\") {\n    result = tokenValue;\n  } else if (count === 1) {\n    result = tokenValue.one;\n  } else {\n    result = tokenValue.other.replace(\"{{count}}\", String(count));\n  }\n\n  if (options?.addSuffix) {\n    if (options.comparison && options.comparison > 0) {\n      return \"oor \" + result;\n    } else {\n      return result + \" gelede\";\n    }\n  }\n\n  return result;\n};\n"], "mappings": "AAAA,MAAMA,oBAAoB,GAAG;EAC3BC,gBAAgB,EAAE;IAChBC,GAAG,EAAE,sBAAsB;IAC3BC,KAAK,EAAE;EACT,CAAC;EAEDC,QAAQ,EAAE;IACRF,GAAG,EAAE,WAAW;IAChBC,KAAK,EAAE;EACT,CAAC;EAEDE,WAAW,EAAE,iBAAiB;EAE9BC,gBAAgB,EAAE;IAChBJ,GAAG,EAAE,qBAAqB;IAC1BC,KAAK,EAAE;EACT,CAAC;EAEDI,QAAQ,EAAE;IACRL,GAAG,EAAE,WAAW;IAChBC,KAAK,EAAE;EACT,CAAC;EAEDK,WAAW,EAAE;IACXN,GAAG,EAAE,gBAAgB;IACrBC,KAAK,EAAE;EACT,CAAC;EAEDM,MAAM,EAAE;IACNP,GAAG,EAAE,OAAO;IACZC,KAAK,EAAE;EACT,CAAC;EAEDO,KAAK,EAAE;IACLR,GAAG,EAAE,OAAO;IACZC,KAAK,EAAE;EACT,CAAC;EAEDQ,WAAW,EAAE;IACXT,GAAG,EAAE,iBAAiB;IACtBC,KAAK,EAAE;EACT,CAAC;EAEDS,MAAM,EAAE;IACNV,GAAG,EAAE,QAAQ;IACbC,KAAK,EAAE;EACT,CAAC;EAEDU,YAAY,EAAE;IACZX,GAAG,EAAE,kBAAkB;IACvBC,KAAK,EAAE;EACT,CAAC;EAEDW,OAAO,EAAE;IACPZ,GAAG,EAAE,SAAS;IACdC,KAAK,EAAE;EACT,CAAC;EAEDY,WAAW,EAAE;IACXb,GAAG,EAAE,iBAAiB;IACtBC,KAAK,EAAE;EACT,CAAC;EAEDa,MAAM,EAAE;IACNd,GAAG,EAAE,QAAQ;IACbC,KAAK,EAAE;EACT,CAAC;EAEDc,UAAU,EAAE;IACVf,GAAG,EAAE,gBAAgB;IACrBC,KAAK,EAAE;EACT,CAAC;EAEDe,YAAY,EAAE;IACZhB,GAAG,EAAE,aAAa;IAClBC,KAAK,EAAE;EACT;AACF,CAAC;AAED,OAAO,MAAMgB,cAAc,GAAGA,CAACC,KAAK,EAAEC,KAAK,EAAEC,OAAO,KAAK;EACvD,IAAIC,MAAM;EAEV,MAAMC,UAAU,GAAGxB,oBAAoB,CAACoB,KAAK,CAAC;EAC9C,IAAI,OAAOI,UAAU,KAAK,QAAQ,EAAE;IAClCD,MAAM,GAAGC,UAAU;EACrB,CAAC,MAAM,IAAIH,KAAK,KAAK,CAAC,EAAE;IACtBE,MAAM,GAAGC,UAAU,CAACtB,GAAG;EACzB,CAAC,MAAM;IACLqB,MAAM,GAAGC,UAAU,CAACrB,KAAK,CAACsB,OAAO,CAAC,WAAW,EAAEC,MAAM,CAACL,KAAK,CAAC,CAAC;EAC/D;EAEA,IAAIC,OAAO,aAAPA,OAAO,eAAPA,OAAO,CAAEK,SAAS,EAAE;IACtB,IAAIL,OAAO,CAACM,UAAU,IAAIN,OAAO,CAACM,UAAU,GAAG,CAAC,EAAE;MAChD,OAAO,MAAM,GAAGL,MAAM;IACxB,CAAC,MAAM;MACL,OAAOA,MAAM,GAAG,SAAS;IAC3B;EACF;EAEA,OAAOA,MAAM;AACf,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
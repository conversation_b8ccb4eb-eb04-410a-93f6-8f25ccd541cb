{"ast": null, "code": "import{Button}from'primereact/button';import{Card}from'primereact/card';import{InputText}from\"primereact/inputtext\";import{Toast}from\"primereact/toast\";import React,{useEffect,useRef,useState}from\"react\";import{useNavigate}from'react-router-dom';import{useAuth}from'../../contexts/AuthContext';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const LoginPage=()=>{const toast=useRef(null);const navigate=useNavigate();const{login,isAuthenticated}=useAuth();const[username,setUsername]=useState('');const[password,setPassword]=useState('');const[isLoggingIn,setIsLoggingIn]=useState(false);useEffect(()=>{// 檢查是否已經登入\nconst localToken=localStorage.getItem('token');console.log('LoginPage: 檢查登入狀態',{isAuthenticated,hasLocalToken:!!localToken,currentPath:window.location.pathname});if(isAuthenticated||localToken){console.log('LoginPage: 已登入，導航到首頁');navigate('/');}},[isAuthenticated,navigate]);const handleLogin=async e=>{if(e){e.preventDefault();// 防止表單提交導致頁面重新整理\n}if(isLoggingIn)return;// 防止重複點擊\ntry{setIsLoggingIn(true);console.log('開始登入...',{username,password});const result=await login({username,password});console.log('登入成功，檢查密碼狀態...',result);// 檢查是否使用預設密碼\nif(result!==null&&result!==void 0&&result.isDefaultPassword){console.log('使用預設密碼，導航到更新密碼頁面');// 延遲導航，確保狀態更新完成\nsetTimeout(()=>{navigate('/update-password');},100);}else{console.log('密碼已更新，導航到首頁');// 延遲導航，確保狀態更新完成\nsetTimeout(()=>{navigate('/');},100);}}catch(err){console.error('登入失敗:',err);// 錯誤訊息已經由 API 層標準化\nconst errorMessage=err.details||'登入失敗，請稍後再試';// 確保 Toast 組件已經渲染完成再顯示訊息\nsetTimeout(()=>{var _toast$current;(_toast$current=toast.current)===null||_toast$current===void 0?void 0:_toast$current.show({severity:\"error\",summary:\"登入失敗\",detail:errorMessage});},100);// 縮短延遲，因為我們不需要等待 DOM 渲染\n}finally{setIsLoggingIn(false);}};const handleKeyDown=e=>{if(e.key==='Enter'){e.preventDefault();handleLogin();}};return/*#__PURE__*/_jsxs(\"div\",{className:\"flex align-items-center justify-content-center min-h-screen bg-gray-100\",children:[/*#__PURE__*/_jsx(Toast,{ref:toast}),/*#__PURE__*/_jsx(Card,{title:\"\\u539D\\u908A\\u982D\\u5BB6\\u7269\\u7406\\u6CBB\\u7642\\u6240\",subTitle:\"\\u5F8C\\u53F0\\u7CFB\\u7D71\",className:\"w-25rem\",children:/*#__PURE__*/_jsxs(\"form\",{onSubmit:handleLogin,className:\"p-fluid\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"field mb-3\",children:[/*#__PURE__*/_jsx(\"label\",{htmlFor:\"username\",className:\"block mb-2\",children:\"\\u5E33\\u865F\"}),/*#__PURE__*/_jsx(InputText,{id:\"username\",type:\"text\",value:username,onChange:e=>setUsername(e.target.value),onKeyDown:handleKeyDown,placeholder:\"\\u8F38\\u5165\\u5E33\\u865F\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"field mb-4\",children:[/*#__PURE__*/_jsx(\"label\",{htmlFor:\"password\",className:\"block mb-2\",children:\"\\u5BC6\\u78BC\"}),/*#__PURE__*/_jsx(InputText,{id:\"password\",type:\"password\",value:password,onChange:e=>setPassword(e.target.value),onKeyDown:handleKeyDown,placeholder:\"\\u8F38\\u5165\\u5BC6\\u78BC\"})]}),/*#__PURE__*/_jsx(Button,{label:\"\\u767B\\u5165\",type:\"button\",onClick:()=>handleLogin(),className:\"w-full\",loading:isLoggingIn,disabled:isLoggingIn||!username||!password})]})})]});};export default LoginPage;", "map": {"version": 3, "names": ["<PERSON><PERSON>", "Card", "InputText", "Toast", "React", "useEffect", "useRef", "useState", "useNavigate", "useAuth", "jsx", "_jsx", "jsxs", "_jsxs", "LoginPage", "toast", "navigate", "login", "isAuthenticated", "username", "setUsername", "password", "setPassword", "isLoggingIn", "setIsLoggingIn", "localToken", "localStorage", "getItem", "console", "log", "hasLocalToken", "currentPath", "window", "location", "pathname", "handleLogin", "e", "preventDefault", "result", "isDefaultPassword", "setTimeout", "err", "error", "errorMessage", "details", "_toast$current", "current", "show", "severity", "summary", "detail", "handleKeyDown", "key", "className", "children", "ref", "title", "subTitle", "onSubmit", "htmlFor", "id", "type", "value", "onChange", "target", "onKeyDown", "placeholder", "label", "onClick", "loading", "disabled"], "sources": ["C:/Users/<USER>/Desktop/demo-git/demo-react/my-fullstack-app/frontend/src/components/Page/LoginPage.tsx"], "sourcesContent": ["import { But<PERSON> } from 'primereact/button';\r\nimport { Card } from 'primereact/card';\r\nimport { InputText } from \"primereact/inputtext\";\r\nimport { Toast } from \"primereact/toast\";\r\nimport React, { useEffect, useRef, useState } from \"react\";\r\nimport { useNavigate } from 'react-router-dom';\r\nimport { useAuth } from '../../contexts/AuthContext';\r\n\r\nconst LoginPage: React.FC = () => {\r\n  const toast = useRef<Toast>(null);\r\n  const navigate = useNavigate();\r\n  const { login, isAuthenticated } = useAuth();\r\n  const [username, setUsername] = useState('');\r\n  const [password, setPassword] = useState('');\r\n  const [isLoggingIn, setIsLoggingIn] = useState(false);\r\n\r\n  useEffect(() => {\r\n    // 檢查是否已經登入\r\n    const localToken = localStorage.getItem('token');\r\n    console.log('LoginPage: 檢查登入狀態', {\r\n      isAuthenticated,\r\n      hasLocalToken: !!localToken,\r\n      currentPath: window.location.pathname\r\n    });\r\n\r\n    if (isAuthenticated || localToken) {\r\n      console.log('LoginPage: 已登入，導航到首頁');\r\n      navigate('/');\r\n    }\r\n  }, [isAuthenticated, navigate]);\r\n\r\n  const handleLogin = async (e?: React.FormEvent) => {\r\n    if (e) {\r\n      e.preventDefault(); // 防止表單提交導致頁面重新整理\r\n    }\r\n    if (isLoggingIn) return; // 防止重複點擊\r\n\r\n    try {\r\n      setIsLoggingIn(true);\r\n      console.log('開始登入...', { username, password });\r\n\r\n      const result = await login({ username, password });\r\n      console.log('登入成功，檢查密碼狀態...', result);\r\n\r\n      // 檢查是否使用預設密碼\r\n      if (result?.isDefaultPassword) {\r\n        console.log('使用預設密碼，導航到更新密碼頁面');\r\n        // 延遲導航，確保狀態更新完成\r\n        setTimeout(() => {\r\n          navigate('/update-password');\r\n        }, 100);\r\n      } else {\r\n        console.log('密碼已更新，導航到首頁');\r\n        // 延遲導航，確保狀態更新完成\r\n        setTimeout(() => {\r\n          navigate('/');\r\n        }, 100);\r\n      }\r\n    } catch (err: any) {\r\n      console.error('登入失敗:', err);\r\n\r\n      // 錯誤訊息已經由 API 層標準化\r\n      const errorMessage = err.details || '登入失敗，請稍後再試';\r\n\r\n      // 確保 Toast 組件已經渲染完成再顯示訊息\r\n      setTimeout(() => {\r\n        toast.current?.show({\r\n            severity: \"error\",\r\n            summary: \"登入失敗\",\r\n            detail: errorMessage,\r\n        });\r\n      }, 100); // 縮短延遲，因為我們不需要等待 DOM 渲染\r\n    } finally {\r\n      setIsLoggingIn(false);\r\n    }\r\n  };\r\n\r\n  const handleKeyDown = (e: React.KeyboardEvent) => {\r\n    if (e.key === 'Enter') {\r\n      e.preventDefault();\r\n      handleLogin();\r\n    }\r\n  };\r\n\r\n\r\n  return (\r\n    <div className=\"flex align-items-center justify-content-center min-h-screen bg-gray-100\">\r\n      <Toast ref={toast} />\r\n      <Card title=\"厝邊頭家物理治療所\" subTitle=\"後台系統\" className=\"w-25rem\">\r\n        <form onSubmit={handleLogin} className=\"p-fluid\">\r\n          <div className=\"field mb-3\">\r\n            <label htmlFor=\"username\" className=\"block mb-2\">帳號</label>\r\n            <InputText\r\n              id=\"username\"\r\n              type=\"text\"\r\n              value={username}\r\n              onChange={(e) => setUsername(e.target.value)}\r\n              onKeyDown={handleKeyDown}\r\n              placeholder=\"輸入帳號\"\r\n            />\r\n          </div>\r\n          <div className=\"field mb-4\">\r\n            <label htmlFor=\"password\" className=\"block mb-2\">密碼</label>\r\n            <InputText\r\n              id=\"password\"\r\n              type=\"password\"\r\n              value={password}\r\n              onChange={(e) => setPassword(e.target.value)}\r\n              onKeyDown={handleKeyDown}\r\n              placeholder=\"輸入密碼\"\r\n            />\r\n          </div>\r\n          <Button\r\n            label=\"登入\"\r\n            type=\"button\"\r\n            onClick={() => handleLogin()}\r\n            className=\"w-full\"\r\n            loading={isLoggingIn}\r\n            disabled={isLoggingIn || !username || !password}\r\n          />\r\n        </form>\r\n      </Card>\r\n    </div>\r\n  );\r\n}\r\n\r\nexport default LoginPage;"], "mappings": "AAAA,OAASA,MAAM,KAAQ,mBAAmB,CAC1C,OAASC,IAAI,KAAQ,iBAAiB,CACtC,OAASC,SAAS,KAAQ,sBAAsB,CAChD,OAASC,KAAK,KAAQ,kBAAkB,CACxC,MAAO,CAAAC,KAAK,EAAIC,SAAS,CAAEC,MAAM,CAAEC,QAAQ,KAAQ,OAAO,CAC1D,OAASC,WAAW,KAAQ,kBAAkB,CAC9C,OAASC,OAAO,KAAQ,4BAA4B,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAErD,KAAM,CAAAC,SAAmB,CAAGA,CAAA,GAAM,CAChC,KAAM,CAAAC,KAAK,CAAGT,MAAM,CAAQ,IAAI,CAAC,CACjC,KAAM,CAAAU,QAAQ,CAAGR,WAAW,CAAC,CAAC,CAC9B,KAAM,CAAES,KAAK,CAAEC,eAAgB,CAAC,CAAGT,OAAO,CAAC,CAAC,CAC5C,KAAM,CAACU,QAAQ,CAAEC,WAAW,CAAC,CAAGb,QAAQ,CAAC,EAAE,CAAC,CAC5C,KAAM,CAACc,QAAQ,CAAEC,WAAW,CAAC,CAAGf,QAAQ,CAAC,EAAE,CAAC,CAC5C,KAAM,CAACgB,WAAW,CAAEC,cAAc,CAAC,CAAGjB,QAAQ,CAAC,KAAK,CAAC,CAErDF,SAAS,CAAC,IAAM,CACd;AACA,KAAM,CAAAoB,UAAU,CAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC,CAChDC,OAAO,CAACC,GAAG,CAAC,mBAAmB,CAAE,CAC/BX,eAAe,CACfY,aAAa,CAAE,CAAC,CAACL,UAAU,CAC3BM,WAAW,CAAEC,MAAM,CAACC,QAAQ,CAACC,QAC/B,CAAC,CAAC,CAEF,GAAIhB,eAAe,EAAIO,UAAU,CAAE,CACjCG,OAAO,CAACC,GAAG,CAAC,sBAAsB,CAAC,CACnCb,QAAQ,CAAC,GAAG,CAAC,CACf,CACF,CAAC,CAAE,CAACE,eAAe,CAAEF,QAAQ,CAAC,CAAC,CAE/B,KAAM,CAAAmB,WAAW,CAAG,KAAO,CAAAC,CAAmB,EAAK,CACjD,GAAIA,CAAC,CAAE,CACLA,CAAC,CAACC,cAAc,CAAC,CAAC,CAAE;AACtB,CACA,GAAId,WAAW,CAAE,OAAQ;AAEzB,GAAI,CACFC,cAAc,CAAC,IAAI,CAAC,CACpBI,OAAO,CAACC,GAAG,CAAC,SAAS,CAAE,CAAEV,QAAQ,CAAEE,QAAS,CAAC,CAAC,CAE9C,KAAM,CAAAiB,MAAM,CAAG,KAAM,CAAArB,KAAK,CAAC,CAAEE,QAAQ,CAAEE,QAAS,CAAC,CAAC,CAClDO,OAAO,CAACC,GAAG,CAAC,gBAAgB,CAAES,MAAM,CAAC,CAErC;AACA,GAAIA,MAAM,SAANA,MAAM,WAANA,MAAM,CAAEC,iBAAiB,CAAE,CAC7BX,OAAO,CAACC,GAAG,CAAC,kBAAkB,CAAC,CAC/B;AACAW,UAAU,CAAC,IAAM,CACfxB,QAAQ,CAAC,kBAAkB,CAAC,CAC9B,CAAC,CAAE,GAAG,CAAC,CACT,CAAC,IAAM,CACLY,OAAO,CAACC,GAAG,CAAC,aAAa,CAAC,CAC1B;AACAW,UAAU,CAAC,IAAM,CACfxB,QAAQ,CAAC,GAAG,CAAC,CACf,CAAC,CAAE,GAAG,CAAC,CACT,CACF,CAAE,MAAOyB,GAAQ,CAAE,CACjBb,OAAO,CAACc,KAAK,CAAC,OAAO,CAAED,GAAG,CAAC,CAE3B;AACA,KAAM,CAAAE,YAAY,CAAGF,GAAG,CAACG,OAAO,EAAI,YAAY,CAEhD;AACAJ,UAAU,CAAC,IAAM,KAAAK,cAAA,CACf,CAAAA,cAAA,CAAA9B,KAAK,CAAC+B,OAAO,UAAAD,cAAA,iBAAbA,cAAA,CAAeE,IAAI,CAAC,CAChBC,QAAQ,CAAE,OAAO,CACjBC,OAAO,CAAE,MAAM,CACfC,MAAM,CAAEP,YACZ,CAAC,CAAC,CACJ,CAAC,CAAE,GAAG,CAAC,CAAE;AACX,CAAC,OAAS,CACRnB,cAAc,CAAC,KAAK,CAAC,CACvB,CACF,CAAC,CAED,KAAM,CAAA2B,aAAa,CAAIf,CAAsB,EAAK,CAChD,GAAIA,CAAC,CAACgB,GAAG,GAAK,OAAO,CAAE,CACrBhB,CAAC,CAACC,cAAc,CAAC,CAAC,CAClBF,WAAW,CAAC,CAAC,CACf,CACF,CAAC,CAGD,mBACEtB,KAAA,QAAKwC,SAAS,CAAC,yEAAyE,CAAAC,QAAA,eACtF3C,IAAA,CAACR,KAAK,EAACoD,GAAG,CAAExC,KAAM,CAAE,CAAC,cACrBJ,IAAA,CAACV,IAAI,EAACuD,KAAK,CAAC,wDAAW,CAACC,QAAQ,CAAC,0BAAM,CAACJ,SAAS,CAAC,SAAS,CAAAC,QAAA,cACzDzC,KAAA,SAAM6C,QAAQ,CAAEvB,WAAY,CAACkB,SAAS,CAAC,SAAS,CAAAC,QAAA,eAC9CzC,KAAA,QAAKwC,SAAS,CAAC,YAAY,CAAAC,QAAA,eACzB3C,IAAA,UAAOgD,OAAO,CAAC,UAAU,CAACN,SAAS,CAAC,YAAY,CAAAC,QAAA,CAAC,cAAE,CAAO,CAAC,cAC3D3C,IAAA,CAACT,SAAS,EACR0D,EAAE,CAAC,UAAU,CACbC,IAAI,CAAC,MAAM,CACXC,KAAK,CAAE3C,QAAS,CAChB4C,QAAQ,CAAG3B,CAAC,EAAKhB,WAAW,CAACgB,CAAC,CAAC4B,MAAM,CAACF,KAAK,CAAE,CAC7CG,SAAS,CAAEd,aAAc,CACzBe,WAAW,CAAC,0BAAM,CACnB,CAAC,EACC,CAAC,cACNrD,KAAA,QAAKwC,SAAS,CAAC,YAAY,CAAAC,QAAA,eACzB3C,IAAA,UAAOgD,OAAO,CAAC,UAAU,CAACN,SAAS,CAAC,YAAY,CAAAC,QAAA,CAAC,cAAE,CAAO,CAAC,cAC3D3C,IAAA,CAACT,SAAS,EACR0D,EAAE,CAAC,UAAU,CACbC,IAAI,CAAC,UAAU,CACfC,KAAK,CAAEzC,QAAS,CAChB0C,QAAQ,CAAG3B,CAAC,EAAKd,WAAW,CAACc,CAAC,CAAC4B,MAAM,CAACF,KAAK,CAAE,CAC7CG,SAAS,CAAEd,aAAc,CACzBe,WAAW,CAAC,0BAAM,CACnB,CAAC,EACC,CAAC,cACNvD,IAAA,CAACX,MAAM,EACLmE,KAAK,CAAC,cAAI,CACVN,IAAI,CAAC,QAAQ,CACbO,OAAO,CAAEA,CAAA,GAAMjC,WAAW,CAAC,CAAE,CAC7BkB,SAAS,CAAC,QAAQ,CAClBgB,OAAO,CAAE9C,WAAY,CACrB+C,QAAQ,CAAE/C,WAAW,EAAI,CAACJ,QAAQ,EAAI,CAACE,QAAS,CACjD,CAAC,EACE,CAAC,CACH,CAAC,EACJ,CAAC,CAEV,CAAC,CAED,cAAe,CAAAP,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
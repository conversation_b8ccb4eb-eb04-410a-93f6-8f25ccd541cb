import { Calendar } from 'primereact/calendar';
import React from 'react';

interface CalendarItemProps {
  Label: string;
  value?: Date | null;
  onChange?: (value: Date | null) => void;
  showTime?: boolean;
  dateFormat?: string;
  placeholder?: string;
  disabled?: boolean;
  className?: string;
}

const CalendarItem: React.FC<CalendarItemProps> = ({
  Label,
  value = null,
  onChange,
  showTime = true,
  dateFormat = 'yy-mm-dd',
  placeholder,
  disabled = false,
  className = '',
}) => {
  return (
    <div className={`field ${className}`}>
      <label className="font-bold block mb-2">{Label}</label>
      <Calendar
        value={value}
        onChange={(e) => onChange?.(e.value as Date | null)}
        placeholder={placeholder || Label}
        showTime={showTime}
        hourFormat="24"
        dateFormat={dateFormat}
        showIcon
        disabled={disabled}
        className="w-full"
      />
    </div>
  );
};

export default CalendarItem;
import React from 'react';
import { Button } from 'primereact/button';
import { Card } from 'primereact/card';

interface ErrorFallbackProps {
  error?: Error;
  resetError?: () => void;
}

const ErrorFallback: React.FC<ErrorFallbackProps> = ({ error, resetError }) => {
  const errorMessage = error?.message || '發生未知錯誤';
  const errorStack = error?.stack;

  const handleGoHome = () => {
    window.location.href = '/';
  };

  const handleGoBack = () => {
    window.history.back();
  };

  const handleReload = () => {
    if (resetError) {
      resetError();
    } else {
      window.location.reload();
    }
  };

  const handleReportError = () => {
    // 這裡可以實現錯誤報告功能
    const errorReport = {
      message: errorMessage,
      stack: errorStack,
      url: window.location.href,
      userAgent: navigator.userAgent,
      timestamp: new Date().toISOString()
    };
    
    console.error('錯誤報告:', errorReport);
    
    // 可以發送到錯誤追蹤服務
    // 例如: Sentry, LogRocket 等
    alert('錯誤報告已記錄，感謝您的回饋！');
  };

  return (
    <div className="error-page min-h-screen flex align-items-center justify-content-center bg-gray-50">
      <div className="max-w-md w-full">
        <Card className="text-center">
          <div className="mb-4">
            <i className="pi pi-exclamation-triangle text-6xl text-red-500 mb-3"></i>
            <h1 className="text-3xl font-bold text-gray-800 mb-2">糟糕！</h1>
            <h2 className="text-xl text-gray-600 mb-4">應用程式發生錯誤</h2>
          </div>

          <div className="mb-4 p-3 bg-red-50 border-round">
            <p className="text-red-700 font-medium mb-2">錯誤詳情：</p>
            <p className="text-red-600 text-sm">{errorMessage}</p>
          </div>

          {process.env.NODE_ENV === 'development' && errorStack && (
            <div className="mb-4 p-3 bg-gray-100 border-round">
              <p className="text-gray-700 font-medium mb-2">技術詳情：</p>
              <pre className="text-xs text-gray-600 text-left overflow-auto max-h-20rem">
                {errorStack}
              </pre>
            </div>
          )}

          <div className="flex flex-column gap-2">
            <Button
              label="重新載入頁面"
              icon="pi pi-refresh"
              onClick={handleReload}
              className="p-button-primary"
            />
            
            <div className="flex gap-2">
              <Button
                label="返回上頁"
                icon="pi pi-arrow-left"
                onClick={handleGoBack}
                className="p-button-secondary flex-1"
                outlined
              />
              
              <Button
                label="回到首頁"
                icon="pi pi-home"
                onClick={handleGoHome}
                className="p-button-secondary flex-1"
                outlined
              />
            </div>

            <Button
              label="回報問題"
              icon="pi pi-send"
              onClick={handleReportError}
              className="p-button-help"
              outlined
              size="small"
            />
          </div>

          <div className="mt-4 pt-3 border-top-1 surface-border">
            <p className="text-sm text-gray-500">
              如果問題持續發生，請聯繫系統管理員
            </p>
          </div>
        </Card>
      </div>
    </div>
  );
};

export default ErrorFallback;

{"version": 3, "file": "static/js/650.6ec2296a.chunk.js", "mappings": "kSAyBA,MA4PA,EA5PgCA,KAC9B,MAAMC,GAAQC,EAAAA,EAAAA,QAAc,OACrBC,EAAMC,IAAWC,EAAAA,EAAAA,UAAqB,KACtCC,EAASC,IAAcF,EAAAA,EAAAA,WAAS,IAChCG,EAAcC,IAAmBJ,EAAAA,EAAAA,UAAS,IAC1CK,EAAOC,IAAYN,EAAAA,EAAAA,UAAS,IAC5BO,EAAMC,IAAWR,EAAAA,EAAAA,UAAS,KAG1BS,EAASC,IAAcV,EAAAA,EAAAA,UAAS,CACrCW,UAAW,GACXC,OAAQ,GACRC,OAAQ,GACRC,eAAgB,KAChBC,aAAc,OAaVC,EAAWC,iBAAoC,IAA7BC,EAAIC,UAAAC,OAAA,QAAAC,IAAAF,UAAA,GAAAA,UAAA,GAAG,EAAGG,EAAQH,UAAAC,OAAA,QAAAC,IAAAF,UAAA,GAAAA,UAAA,GAAG,GAC3C,IACEjB,GAAW,GACXqB,EAAAA,GAAIC,IAAI,wCAER,MAAMC,EAAc,CAClBP,OACAI,YAGEb,EAAQE,YAAWc,EAAOd,UAAYF,EAAQE,WAC9CF,EAAQG,SAAQa,EAAOb,OAASH,EAAQG,QACxCH,EAAQI,SAAQY,EAAOZ,OAASJ,EAAQI,QACxCJ,EAAQK,iBAAgBW,EAAOX,eAAiBL,EAAQK,eAAeY,eACvEjB,EAAQM,eAAcU,EAAOV,aAAeN,EAAQM,aAAaW,eAErE,MAAMC,QAAiBH,EAAAA,EAAII,IAAI,4BAA6B,CAAEH,WAE9D1B,EAAQ4B,EAASE,KAAKA,MACtBzB,EAAgBuB,EAASE,KAAKC,YAE9BP,EAAAA,GAAIC,IAAI,mDAAY,CAAEO,MAAOJ,EAASE,KAAKA,KAAKT,QAElD,CAAE,MAAOY,GAAa,IAADC,EACnBV,EAAAA,GAAIS,MAAM,mDAAYA,GACT,QAAbC,EAAArC,EAAMsC,eAAO,IAAAD,GAAbA,EAAeE,KAAK,CAClBC,SAAU,QACVC,QAAS,2BACTC,OAAQ,mDACRC,KAAM,KAEV,CAAC,QACCrC,GAAW,EACb,CACF,EAsDMsC,GACJC,EAAAA,EAAAA,KAACC,EAAAA,EAAM,CACLC,KAAK,SACLC,KAAK,gBACLC,MAAI,EACJC,QAASA,IAAM9B,EAAS+B,KAAKC,MAAM3C,EAAQE,GAAQ,EAAGA,GACtD0C,SAAUhD,IAIRiD,GAAiBT,EAAAA,EAAAA,KAAA,UAMvB,OAJAU,EAAAA,EAAAA,YAAU,KACRnC,MACC,IAECf,GAA2B,IAAhBH,EAAKsB,QAEhBqB,EAAAA,EAAAA,KAAA,OAAKW,UAAU,iDAAiDC,MAAO,CAAEC,OAAQ,SAAUC,UACzFd,EAAAA,EAAAA,KAACe,EAAAA,EAAe,OAMpBC,EAAAA,EAAAA,MAAA,OAAAF,SAAA,EACEd,EAAAA,EAAAA,KAACiB,EAAAA,EAAK,CAACC,IAAK/D,KAEZ6C,EAAAA,EAAAA,KAACmB,EAAAA,EAAI,CAACC,MAAM,2BAAOT,UAAU,OAAMG,UACjCd,EAAAA,EAAAA,KAAA,KAAGW,UAAU,6BAA4BG,SAAC,sSAM5Cd,EAAAA,EAAAA,KAACmB,EAAAA,EAAI,CAACR,UAAU,OAAMG,UACpBE,EAAAA,EAAAA,MAAA,OAAKL,UAAU,OAAMG,SAAA,EACnBd,EAAAA,EAAAA,KAAA,OAAKW,UAAU,iBAAgBG,UAC7Bd,EAAAA,EAAAA,KAACqB,EAAAA,EAAS,CACRC,GAAG,YACHC,MAAOvD,EAAQE,UACfsD,SAAWC,GAAMxD,GAAUyD,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAC,CAAC,EAAI1D,GAAO,IAAEE,UAAWuD,EAAEE,OAAOJ,SAC9DK,YAAY,+BACZjB,UAAU,cAIdX,EAAAA,EAAAA,KAAA,OAAKW,UAAU,iBAAgBG,UAC7Bd,EAAAA,EAAAA,KAACqB,EAAAA,EAAS,CACRC,GAAG,SACHC,MAAOvD,EAAQG,OACfqD,SAAWC,GAAMxD,GAAUyD,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAC,CAAC,EAAI1D,GAAO,IAAEG,OAAQsD,EAAEE,OAAOJ,SAC3DK,YAAY,uCACZjB,UAAU,cAIdX,EAAAA,EAAAA,KAAA,OAAKW,UAAU,iBAAgBG,UAC7Bd,EAAAA,EAAAA,KAAC6B,EAAAA,EAAQ,CACPN,MAAOvD,EAAQK,eACfmD,SAAWC,GAAMxD,GAAUyD,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAC,CAAC,EAAI1D,GAAO,IAAEK,eAAgBoD,EAAEF,SAC5DK,YAAY,uCACZjB,UAAU,SACVmB,UAAQ,EACRC,WAAW,gBAIf/B,EAAAA,EAAAA,KAAA,OAAKW,UAAU,iBAAgBG,UAC7Bd,EAAAA,EAAAA,KAAC6B,EAAAA,EAAQ,CACPN,MAAOvD,EAAQM,aACfkD,SAAWC,GAAMxD,GAAUyD,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAC,CAAC,EAAI1D,GAAO,IAAEM,aAAcmD,EAAEF,SAC1DK,YAAY,uCACZjB,UAAU,SACVmB,UAAQ,EACRC,WAAW,gBAIf/B,EAAAA,EAAAA,KAAA,OAAKW,UAAU,kBAAiBG,UAC9Bd,EAAAA,EAAAA,KAACgC,EAAAA,EAAQ,CACPV,GAAG,SACHC,MAAOvD,EAAQI,OACf6D,QArLU,CACpB,CAAEC,MAAO,eAAMX,MAAO,IACtB,CAAEW,MAAO,2BAAQX,MAAO,iBACxB,CAAEW,MAAO,2BAAQX,MAAO,kBACxB,CAAEW,MAAO,2BAAQX,MAAO,gBACxB,CAAEW,MAAO,2BAAQX,MAAO,kBAiLdC,SAAWC,GAAMxD,GAAUyD,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAC,CAAC,EAAI1D,GAAO,IAAEI,OAAQqD,EAAEF,SACpDK,YAAY,2BACZjB,UAAU,cAIdX,EAAAA,EAAAA,KAAA,OAAKW,UAAU,kBAAiBG,UAC9BE,EAAAA,EAAAA,MAAA,OAAKL,UAAU,aAAYG,SAAA,EACzBd,EAAAA,EAAAA,KAACC,EAAAA,EAAM,CACLiC,MAAM,eACN/B,KAAK,eACLE,QAlJO8B,KACnBtE,EAAS,GACTU,EAAS,EAAGT,OAkJFkC,EAAAA,EAAAA,KAACC,EAAAA,EAAM,CACLiC,MAAM,eACN/B,KAAK,gBACLE,QAjJM+B,KAClBnE,EAAW,CACTC,UAAW,GACXC,OAAQ,GACRC,OAAQ,GACRC,eAAgB,KAChBC,aAAc,OAEhBT,EAAS,GACTU,EAAS,EAAGT,IAyIA6C,UAAU,kCAQpBX,EAAAA,EAAAA,KAACmB,EAAAA,EAAI,CAAAL,UACHE,EAAAA,EAAAA,MAACqB,EAAAA,EAAS,CACRd,MAAOlE,EACPiF,WAAS,EACTC,MAAI,EACJ3E,MAAOA,EACPE,KAAMA,EACNJ,aAAcA,EACd8E,OArJcC,IACpB5E,EAAS4E,EAAM7E,OACfG,EAAQ0E,EAAM3E,MACd,MAAMW,EAAO6B,KAAKC,MAAMkC,EAAM7E,MAAQ6E,EAAM3E,MAAQ,EACpDS,EAASE,EAAMgE,EAAM3E,OAkJf4E,mBAAoB,CAAC,GAAI,GAAI,IAC7BC,aAAa,mDACbC,WAAY,CAAEC,SAAU,SACxB9C,cAAeA,EACfU,eAAgBA,EAChBjD,QAASA,EAAQsD,SAAA,EAEjBd,EAAAA,EAAAA,KAAC8C,EAAAA,EAAM,CAACC,MAAM,WAAWC,OAAO,2BAAOC,UAAQ,EAACrC,MAAO,CAAEsC,MAAO,UAChElD,EAAAA,EAAAA,KAAC8C,EAAAA,EAAM,CAACC,MAAM,YAAYC,OAAO,kBAAQC,UAAQ,EAACrC,MAAO,CAAEsC,MAAO,UAClElD,EAAAA,EAAAA,KAAC8C,EAAAA,EAAM,CAACC,MAAM,SAASC,OAAO,eAAKpC,MAAO,CAAEsC,MAAO,UACnDlD,EAAAA,EAAAA,KAAC8C,EAAAA,EAAM,CAACC,MAAM,UAAUC,OAAO,qBAAMpC,MAAO,CAAEsC,MAAO,UACrDlD,EAAAA,EAAAA,KAAC8C,EAAAA,EAAM,CAACC,MAAM,SAASC,OAAO,eAAKG,KA9IfC,IAC1B,MAAMzD,EAAWyD,EAAQhF,OAAOiF,SAAS,WAAa,UAAY,SAC5DC,EAAaF,EAAQhF,OAAOiF,SAAS,SAAW,eAAO,eACvDE,EAA2B,YAAb5D,EAAyB,eAAO,eACpD,OAAOK,EAAAA,EAAAA,KAACwD,EAAAA,EAAG,CAACjC,MAAO+B,EAAaC,EAAa5D,SAAUA,KA0IYiB,MAAO,CAAEsC,MAAO,UAC7ElD,EAAAA,EAAAA,KAAC8C,EAAAA,EAAM,CAACC,MAAM,YAAYC,OAAO,2BAAOG,KAvItBC,GAnBNK,KAClB,IAAKA,EAAY,MAAO,GACxB,IACE,OAAOC,EAAAA,EAAAA,GAAkBD,EAAY,sBACvC,CAAE,MAAOlE,GAEP,OADAoE,QAAQpE,MAAM,yBAA0BA,GACjCkE,CACT,GAaOG,CAAWR,EAAQS,WAsI4CZ,UAAQ,EAACrC,MAAO,CAAEsC,MAAO,iB,wFCvQnG,SAASY,EAAQC,GAGf,OAAOD,EAAU,mBAAqBE,QAAU,iBAAmBA,OAAOC,SAAW,SAAUF,GAC7F,cAAcA,CAChB,EAAI,SAAUA,GACZ,OAAOA,GAAK,mBAAqBC,QAAUD,EAAEG,cAAgBF,QAAUD,IAAMC,OAAOG,UAAY,gBAAkBJ,CACpH,EAAGD,EAAQC,EACb,CAaA,SAASK,EAAcC,GACrB,IAAIC,EAZN,SAAqBD,EAAGE,GACtB,GAAI,UAAYT,EAAQO,KAAOA,EAAG,OAAOA,EACzC,IAAI5C,EAAI4C,EAAEL,OAAOQ,aACjB,QAAI,IAAW/C,EAAG,CAChB,IAAI6C,EAAI7C,EAAEgD,KAAKJ,EAAGE,GAAK,WACvB,GAAI,UAAYT,EAAQQ,GAAI,OAAOA,EACnC,MAAM,IAAII,UAAU,+CACtB,CACA,OAAQ,WAAaH,EAAII,OAASC,QAAQP,EAC5C,CAGUG,CAAYH,EAAG,UACvB,MAAO,UAAYP,EAAQQ,GAAKA,EAAIA,EAAI,EAC1C,CAEA,SAASO,EAAgBpD,EAAG8C,EAAGF,GAC7B,OAAQE,EAAIH,EAAcG,MAAO9C,EAAIqD,OAAOC,eAAetD,EAAG8C,EAAG,CAC/DhD,MAAO8C,EACPW,YAAY,EACZC,cAAc,EACdC,UAAU,IACPzD,EAAE8C,GAAKF,EAAG5C,CACjB,CAEA,IAAI0D,EAAU,CACZ5D,MAAO,cACPpB,KAAM,aACNiF,KAAM,SAAcC,GAClB,IAAIC,EAAQD,EAAKC,MACjB,OAAOC,EAAAA,EAAAA,IAAW,oBAAqBV,EAAgBA,EAAgB,CAAC,EAAG,SAASW,OAAOF,EAAM3F,UAA8B,OAAnB2F,EAAM3F,UAAoB,gBAAiB2F,EAAMG,SAC/J,GAGEC,EAAUC,EAAAA,EAAcC,OAAO,CACjCC,aAAc,CACZC,OAAQ,MACRvE,MAAO,KACP5B,SAAU,KACV8F,SAAS,EACTtF,KAAM,KACNS,MAAO,KACPD,UAAW,KACXG,cAAUlC,GAEZmH,IAAK,CACHZ,QAASA,EACTa,OAdS,+TAkBb,SAASC,EAAQxE,EAAG8C,GAAK,IAAIF,EAAIS,OAAOoB,KAAKzE,GAAI,GAAIqD,OAAOqB,sBAAuB,CAAE,IAAIpC,EAAIe,OAAOqB,sBAAsB1E,GAAI8C,IAAMR,EAAIA,EAAEqC,QAAO,SAAU7B,GAAK,OAAOO,OAAOuB,yBAAyB5E,EAAG8C,GAAGS,UAAY,KAAKX,EAAEiC,KAAKC,MAAMlC,EAAGN,EAAI,CAAE,OAAOM,CAAG,CAE9P,IAAIb,EAAmBgD,EAAAA,YAAiB,SAAUC,EAASvF,GACzD,IAAIwF,GAAaC,EAAAA,EAAAA,MACbC,EAAUJ,EAAAA,WAAiBK,EAAAA,IAC3BvB,EAAQI,EAAQoB,SAASL,EAASG,GAClCG,EAAuBrB,EAAQsB,YAAY,CAC3C1B,MAAOA,IAET2B,EAAMF,EAAqBE,IAC3BC,EAAKH,EAAqBG,GAC1BC,EAAaJ,EAAqBI,YACpCC,EAAAA,EAAAA,GAAe1B,EAAQK,IAAIC,OAAQmB,EAAY,CAC7CE,KAAM,QAER,IAAIC,EAAad,EAAAA,OAAa,MAC1Be,EAAYb,EAAW,CACzB/F,UAAWuG,EAAG,SACbD,EAAI,SACH9G,EAAOqH,EAAAA,GAAUC,WAAWnC,EAAMnF,KAlBxC,SAAuBsB,GAAK,IAAK,IAAI8C,EAAI,EAAGA,EAAI7F,UAAUC,OAAQ4F,IAAK,CAAE,IAAIF,EAAI,MAAQ3F,UAAU6F,GAAK7F,UAAU6F,GAAK,CAAC,EAAGA,EAAI,EAAI0B,EAAQnB,OAAOT,IAAI,GAAIqD,SAAQ,SAAUnD,GAAKM,EAAgBpD,EAAG8C,EAAGF,EAAEE,GAAK,IAAKO,OAAO6C,0BAA4B7C,OAAO8C,iBAAiBnG,EAAGqD,OAAO6C,0BAA0BtD,IAAM4B,EAAQnB,OAAOT,IAAIqD,SAAQ,SAAUnD,GAAKO,OAAOC,eAAetD,EAAG8C,EAAGO,OAAOuB,yBAAyBhC,EAAGE,GAAK,GAAI,CAAE,OAAO9C,CAAG,CAkBxYC,CAAc,CAAC,EAAG6F,GAAY,CACxEjC,MAAOA,IAETkB,EAAAA,oBAA0BtF,GAAK,WAC7B,MAAO,CACLoE,MAAOA,EACPuC,WAAY,WACV,OAAOP,EAAW7H,OACpB,EAEJ,IACA,IAAIqI,EAAYpB,EAAW,CACzBxF,IAAKoG,EACL3G,WAAW4E,EAAAA,EAAAA,IAAWD,EAAM3E,UAAWuG,EAAG,SAC1CtG,MAAO0E,EAAM1E,OACZ8E,EAAQqC,cAAczC,GAAQ2B,EAAI,SACjCe,EAAatB,EAAW,CAC1B/F,UAAWuG,EAAG,UACbD,EAAI,UACP,OAAoBT,EAAAA,cAAoB,OAAQsB,EAAW3H,EAAmBqG,EAAAA,cAAoB,OAAQwB,EAAY1C,EAAM/D,OAAqBiF,EAAAA,cAAoB,OAAQ,KAAMlB,EAAMxE,UAC3L,IACA0C,EAAIyE,YAAc,K", "sources": ["components/Page/LoginLogsPage.tsx", "../node_modules/primereact/tag/tag.esm.js"], "sourcesContent": ["import React, { useState, useRef, useEffect } from 'react';\r\nimport { Button } from 'primereact/button';\r\nimport { Column } from 'primereact/column';\r\nimport { DataTable } from 'primereact/datatable';\r\nimport { Toast } from 'primereact/toast';\r\nimport { Card } from 'primereact/card';\r\nimport { ProgressSpinner } from 'primereact/progressspinner';\r\nimport { Tag } from 'primereact/tag';\r\nimport { InputText } from 'primereact/inputtext';\r\nimport { Dropdown } from 'primereact/dropdown';\r\nimport { Calendar } from 'primereact/calendar';\r\nimport { formatUtcToTaipei } from \"../../utils/dateUtils\";\r\nimport api from '../../services/api';\r\nimport { log } from '../../utils/logger';\r\n\r\ninterface LoginLog {\r\n  id: number;\r\n  username: string;\r\n  createdAt: string;\r\n  ipAddress: string;\r\n  device: string;\r\n  browser: string;\r\n  status: string;\r\n}\r\n\r\nconst LoginLogsPage: React.FC = () => {\r\n  const toast = useRef<Toast>(null);\r\n  const [logs, setLogs] = useState<LoginLog[]>([]);\r\n  const [loading, setLoading] = useState(true);\r\n  const [totalRecords, setTotalRecords] = useState(0);\r\n  const [first, setFirst] = useState(0);\r\n  const [rows, setRows] = useState(20);\r\n\r\n  // 搜尋條件\r\n  const [filters, setFilters] = useState({\r\n    ipAddress: '',\r\n    device: '',\r\n    status: '',\r\n    createdAtStart: null as Date | null,\r\n    createdAtEnd: null as Date | null\r\n  });\r\n\r\n  const statusOptions = [\r\n    { label: '全部', value: '' },\r\n    { label: '登入成功', value: 'Login Success' },\r\n    { label: '登出成功', value: 'Logout Success' },\r\n    { label: '登入失敗', value: 'Login Failed' },\r\n    { label: '登出失敗', value: 'Logout Failed' }\r\n    \r\n  ];\r\n\r\n  // 載入登入紀錄\r\n  const loadLogs = async (page = 1, pageSize = 20) => {\r\n    try {\r\n      setLoading(true);\r\n      log.api('載入登入紀錄');\r\n\r\n      const params: any = {\r\n        page,\r\n        pageSize\r\n      };\r\n\r\n      if (filters.ipAddress) params.ipAddress = filters.ipAddress;\r\n      if (filters.device) params.device = filters.device;\r\n      if (filters.status) params.status = filters.status;\r\n      if (filters.createdAtStart) params.createdAtStart = filters.createdAtStart.toISOString();\r\n      if (filters.createdAtEnd) params.createdAtEnd = filters.createdAtEnd.toISOString();\r\n\r\n      const response = await api.get('/api/auth/GetUserLoginLog', { params });\r\n      \r\n      setLogs(response.data.data);\r\n      setTotalRecords(response.data.totalCount);\r\n      \r\n      log.api('登入紀錄載入成功', { count: response.data.data.length });\r\n      \r\n    } catch (error: any) {\r\n      log.error('載入登入紀錄失敗', error);\r\n      toast.current?.show({\r\n        severity: 'error',\r\n        summary: '載入失敗',\r\n        detail: '無法載入登入紀錄',\r\n        life: 5000\r\n      });\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  // 搜尋\r\n  const handleSearch = () => {\r\n    setFirst(0);\r\n    loadLogs(1, rows);\r\n  };\r\n\r\n  // 重置搜尋\r\n  const handleReset = () => {\r\n    setFilters({\r\n      ipAddress: '',\r\n      device: '',\r\n      status: '',\r\n      createdAtStart: null,\r\n      createdAtEnd: null\r\n    });\r\n    setFirst(0);\r\n    loadLogs(1, rows);\r\n  };\r\n\r\n  // 分頁變更\r\n  const onPageChange = (event: any) => {\r\n    setFirst(event.first);\r\n    setRows(event.rows);\r\n    const page = Math.floor(event.first / event.rows) + 1;\r\n    loadLogs(page, event.rows);\r\n  };\r\n\r\n  // 格式化日期\r\n  const formatDate = (dateString: string): string => {\r\n    if (!dateString) return '';\r\n    try {\r\n      return formatUtcToTaipei(dateString, 'yyyy/MM/dd HH:mm:ss');\r\n    } catch (error) {\r\n      console.error('Error formatting date:', error);\r\n      return dateString;\r\n    }\r\n  };\r\n\r\n  // 狀態標籤模板\r\n  const statusBodyTemplate = (rowData: LoginLog) => {\r\n    const severity = rowData.status.includes('Success') ? 'success' : 'danger';\r\n    const labelfirst = rowData.status.includes('Login') ? '登入' : '登出';\r\n    const labelsecond = severity === \"success\" ? '成功' : '失敗';\r\n    return <Tag value={labelfirst + labelsecond} severity={severity} />;\r\n  };\r\n\r\n  // 日期模板\r\n  const dateBodyTemplate = (rowData: LoginLog) => {\r\n    return formatDate(rowData.createdAt);\r\n  };\r\n\r\n  // 分頁器左側\r\n  const paginatorLeft = (\r\n    <Button\r\n      type=\"button\"\r\n      icon=\"pi pi-refresh\"\r\n      text\r\n      onClick={() => loadLogs(Math.floor(first / rows) + 1, rows)}\r\n      disabled={loading}\r\n    />\r\n  );\r\n\r\n  const paginatorRight = <div></div>;\r\n\r\n  useEffect(() => {\r\n    loadLogs();\r\n  }, []);\r\n\r\n  if (loading && logs.length === 0) {\r\n    return (\r\n      <div className=\"flex justify-content-center align-items-center\" style={{ height: '400px' }}>\r\n        <ProgressSpinner />\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <div>\r\n      <Toast ref={toast} />\r\n      \r\n      <Card title=\"登入紀錄\" className=\"mb-4\">\r\n        <p className=\"text-600 line-height-3 m-0\">\r\n          查看系統用戶的登入紀錄，包括成功和失敗的登入嘗試。可以根據 IP 位址、裝置、狀態和時間範圍進行篩選。\r\n        </p>\r\n      </Card>\r\n\r\n      {/* 搜尋條件 */}\r\n      <Card className=\"mb-4\">\r\n        <div className=\"grid\">\r\n          <div className=\"col-6 md:col-3\">\r\n            <InputText\r\n              id=\"ipAddress\"\r\n              value={filters.ipAddress}\r\n              onChange={(e) => setFilters({ ...filters, ipAddress: e.target.value })}\r\n              placeholder=\"輸入 IP 位址\"\r\n              className=\"w-full\"\r\n            />\r\n          </div>\r\n          \r\n          <div className=\"col-6 md:col-3\">\r\n            <InputText\r\n              id=\"device\"\r\n              value={filters.device}\r\n              onChange={(e) => setFilters({ ...filters, device: e.target.value })}\r\n              placeholder=\"輸入裝置名稱\"\r\n              className=\"w-full\"\r\n            />\r\n          </div>\r\n\r\n          <div className=\"col-6 md:col-3\">\r\n            <Calendar\r\n              value={filters.createdAtStart}\r\n              onChange={(e) => setFilters({ ...filters, createdAtStart: e.value as Date })}\r\n              placeholder=\"選擇開始日期\"\r\n              className=\"w-full\"\r\n              showIcon\r\n              dateFormat=\"yy/mm/dd\"\r\n            />\r\n          </div>\r\n          \r\n          <div className=\"col-6 md:col-3\">\r\n            <Calendar\r\n              value={filters.createdAtEnd}\r\n              onChange={(e) => setFilters({ ...filters, createdAtEnd: e.value as Date })}\r\n              placeholder=\"選擇結束日期\"\r\n              className=\"w-full\"\r\n              showIcon\r\n              dateFormat=\"yy/mm/dd\"\r\n            />\r\n          </div>\r\n\r\n          <div className=\"col-12 md:col-3\">\r\n            <Dropdown\r\n              id=\"status\"\r\n              value={filters.status}\r\n              options={statusOptions}\r\n              onChange={(e) => setFilters({ ...filters, status: e.value })}\r\n              placeholder=\"選擇狀態\"\r\n              className=\"w-full\"\r\n            />\r\n          </div>\r\n\r\n          <div className=\"col-12 md:col-4\">\r\n            <div className=\"flex gap-2\">\r\n              <Button\r\n                label=\"搜尋\"\r\n                icon=\"pi pi-search\"\r\n                onClick={handleSearch}\r\n              />\r\n              <Button\r\n                label=\"重置\"\r\n                icon=\"pi pi-refresh\"\r\n                onClick={handleReset}\r\n                className=\"p-button-secondary\"\r\n              />\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </Card>\r\n\r\n      {/* 資料表 */}\r\n      <Card>\r\n        <DataTable\r\n          value={logs}\r\n          paginator\r\n          lazy\r\n          first={first}\r\n          rows={rows}\r\n          totalRecords={totalRecords}\r\n          onPage={onPageChange}\r\n          rowsPerPageOptions={[10, 20, 50]}\r\n          emptyMessage=\"沒有找到登入紀錄\"\r\n          tableStyle={{ minWidth: '50rem' }}\r\n          paginatorLeft={paginatorLeft}\r\n          paginatorRight={paginatorRight}\r\n          loading={loading}\r\n        >\r\n          <Column field=\"username\" header=\"用戶名稱\" sortable style={{ width: '15%' }} />\r\n          <Column field=\"ipAddress\" header=\"IP 位址\" sortable style={{ width: '15%' }} />\r\n          <Column field=\"device\" header=\"裝置\" style={{ width: '15%' }} />\r\n          <Column field=\"browser\" header=\"瀏覽器\" style={{ width: '15%' }} />\r\n          <Column field=\"status\" header=\"狀態\" body={statusBodyTemplate} style={{ width: '10%' }} />\r\n          <Column field=\"createdAt\" header=\"登入時間\" body={dateBodyTemplate} sortable style={{ width: '20%' }} />\r\n        </DataTable>\r\n      </Card>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default LoginLogsPage;\r\n", "'use client';\nimport * as React from 'react';\nimport { PrimeReactContext } from 'primereact/api';\nimport { ComponentBase, useHandleStyle } from 'primereact/componentbase';\nimport { useMergeProps } from 'primereact/hooks';\nimport { classNames, IconUtils } from 'primereact/utils';\n\nfunction _typeof(o) {\n  \"@babel/helpers - typeof\";\n\n  return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (o) {\n    return typeof o;\n  } : function (o) {\n    return o && \"function\" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? \"symbol\" : typeof o;\n  }, _typeof(o);\n}\n\nfunction toPrimitive(t, r) {\n  if (\"object\" != _typeof(t) || !t) return t;\n  var e = t[Symbol.toPrimitive];\n  if (void 0 !== e) {\n    var i = e.call(t, r || \"default\");\n    if (\"object\" != _typeof(i)) return i;\n    throw new TypeError(\"@@toPrimitive must return a primitive value.\");\n  }\n  return (\"string\" === r ? String : Number)(t);\n}\n\nfunction toPropertyKey(t) {\n  var i = toPrimitive(t, \"string\");\n  return \"symbol\" == _typeof(i) ? i : i + \"\";\n}\n\nfunction _defineProperty(e, r, t) {\n  return (r = toPropertyKey(r)) in e ? Object.defineProperty(e, r, {\n    value: t,\n    enumerable: !0,\n    configurable: !0,\n    writable: !0\n  }) : e[r] = t, e;\n}\n\nvar classes = {\n  value: 'p-tag-value',\n  icon: 'p-tag-icon',\n  root: function root(_ref) {\n    var props = _ref.props;\n    return classNames('p-tag p-component', _defineProperty(_defineProperty({}, \"p-tag-\".concat(props.severity), props.severity !== null), 'p-tag-rounded', props.rounded));\n  }\n};\nvar styles = \"\\n@layer primereact {\\n    .p-tag {\\n        display: inline-flex;\\n        align-items: center;\\n        justify-content: center;\\n    }\\n    \\n    .p-tag-icon,\\n    .p-tag-value,\\n    .p-tag-icon.pi {\\n        line-height: 1.5;\\n    }\\n    \\n    .p-tag.p-tag-rounded {\\n        border-radius: 10rem;\\n    }\\n}\\n\";\nvar TagBase = ComponentBase.extend({\n  defaultProps: {\n    __TYPE: 'Tag',\n    value: null,\n    severity: null,\n    rounded: false,\n    icon: null,\n    style: null,\n    className: null,\n    children: undefined\n  },\n  css: {\n    classes: classes,\n    styles: styles\n  }\n});\n\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nvar Tag = /*#__PURE__*/React.forwardRef(function (inProps, ref) {\n  var mergeProps = useMergeProps();\n  var context = React.useContext(PrimeReactContext);\n  var props = TagBase.getProps(inProps, context);\n  var _TagBase$setMetaData = TagBase.setMetaData({\n      props: props\n    }),\n    ptm = _TagBase$setMetaData.ptm,\n    cx = _TagBase$setMetaData.cx,\n    isUnstyled = _TagBase$setMetaData.isUnstyled;\n  useHandleStyle(TagBase.css.styles, isUnstyled, {\n    name: 'tag'\n  });\n  var elementRef = React.useRef(null);\n  var iconProps = mergeProps({\n    className: cx('icon')\n  }, ptm('icon'));\n  var icon = IconUtils.getJSXIcon(props.icon, _objectSpread({}, iconProps), {\n    props: props\n  });\n  React.useImperativeHandle(ref, function () {\n    return {\n      props: props,\n      getElement: function getElement() {\n        return elementRef.current;\n      }\n    };\n  });\n  var rootProps = mergeProps({\n    ref: elementRef,\n    className: classNames(props.className, cx('root')),\n    style: props.style\n  }, TagBase.getOtherProps(props), ptm('root'));\n  var valueProps = mergeProps({\n    className: cx('value')\n  }, ptm('value'));\n  return /*#__PURE__*/React.createElement(\"span\", rootProps, icon, /*#__PURE__*/React.createElement(\"span\", valueProps, props.value), /*#__PURE__*/React.createElement(\"span\", null, props.children));\n});\nTag.displayName = 'Tag';\n\nexport { Tag };\n"], "names": ["LoginLogsPage", "toast", "useRef", "logs", "setLogs", "useState", "loading", "setLoading", "totalRecords", "setTotalRecords", "first", "<PERSON><PERSON><PERSON><PERSON>", "rows", "setRows", "filters", "setFilters", "ip<PERSON><PERSON><PERSON>", "device", "status", "createdAtStart", "createdAtEnd", "loadLogs", "async", "page", "arguments", "length", "undefined", "pageSize", "log", "api", "params", "toISOString", "response", "get", "data", "totalCount", "count", "error", "_toast$current", "current", "show", "severity", "summary", "detail", "life", "paginatorLeft", "_jsx", "<PERSON><PERSON>", "type", "icon", "text", "onClick", "Math", "floor", "disabled", "paginatorRight", "useEffect", "className", "style", "height", "children", "ProgressSpinner", "_jsxs", "Toast", "ref", "Card", "title", "InputText", "id", "value", "onChange", "e", "_objectSpread", "target", "placeholder", "Calendar", "showIcon", "dateFormat", "Dropdown", "options", "label", "handleSearch", "handleReset", "DataTable", "paginator", "lazy", "onPage", "event", "rowsPerPageOptions", "emptyMessage", "tableStyle", "min<PERSON><PERSON><PERSON>", "Column", "field", "header", "sortable", "width", "body", "rowData", "includes", "labelfirst", "labelsecond", "Tag", "dateString", "formatUtcToTaipei", "console", "formatDate", "createdAt", "_typeof", "o", "Symbol", "iterator", "constructor", "prototype", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "t", "i", "r", "toPrimitive", "call", "TypeError", "String", "Number", "_defineProperty", "Object", "defineProperty", "enumerable", "configurable", "writable", "classes", "root", "_ref", "props", "classNames", "concat", "rounded", "TagBase", "ComponentBase", "extend", "defaultProps", "__TYPE", "css", "styles", "ownKeys", "keys", "getOwnPropertySymbols", "filter", "getOwnPropertyDescriptor", "push", "apply", "React", "inProps", "mergeProps", "useMergeProps", "context", "PrimeReactContext", "getProps", "_TagBase$setMetaData", "setMetaData", "ptm", "cx", "isUnstyled", "useHandleStyle", "name", "elementRef", "iconProps", "IconUtils", "getJSXIcon", "for<PERSON>ach", "getOwnPropertyDescriptors", "defineProperties", "getElement", "rootProps", "getOtherProps", "valueProps", "displayName"], "sourceRoot": ""}
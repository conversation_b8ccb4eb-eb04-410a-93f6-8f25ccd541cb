{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\demo-git\\\\demo-react\\\\my-fullstack-app\\\\frontend\\\\src\\\\components\\\\Page\\\\UsersPage.tsx\",\n  _s = $RefreshSig$();\nimport { Button } from 'primereact/button';\nimport { Column } from 'primereact/column';\nimport { DataTable } from 'primereact/datatable';\nimport { Dialog } from 'primereact/dialog';\nimport { Dropdown } from 'primereact/dropdown';\nimport { InputText } from 'primereact/inputtext';\nimport { Toast } from 'primereact/toast';\nimport { Tag } from 'primereact/tag';\nimport { MultiSelect } from 'primereact/multiselect';\nimport { Card } from 'primereact/card';\nimport { ProgressSpinner } from 'primereact/progressspinner';\nimport React, { useRef, useState, useEffect } from 'react';\nimport api from '../../services/api';\nimport { log } from '../../utils/logger';\nimport { usePermissions } from '../../hooks/usePermissions';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst UsersPage = () => {\n  _s();\n  const toast = useRef(null);\n  const [users, setUsers] = useState([]);\n  const [roles, setRoles] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [searchName, setSearchName] = useState('');\n  const [selectedRoleFilter, setSelectedRoleFilter] = useState(null);\n  const [showEditDialog, setShowEditDialog] = useState(false);\n  const [editingUser, setEditingUser] = useState(null);\n  const [selectedRoles, setSelectedRoles] = useState([]);\n  const [refreshing, setRefreshing] = useState(false);\n  const {\n    hasPermission\n  } = usePermissions();\n\n  // 載入用戶列表\n  const loadUsers = async () => {\n    try {\n      setRefreshing(true);\n      log.api('載入用戶列表', {\n        searchName,\n        selectedRoleFilter\n      });\n      const response = await api.get('/api/users/GetUserRolesList', {\n        params: {\n          name: searchName\n        }\n      });\n      let filteredUsers = response.data;\n\n      // 如果選擇了角色篩選，則進行前端篩選\n      if (selectedRoleFilter) {\n        filteredUsers = response.data.filter(user => user.roles && user.roles.some(role => role.roleId === selectedRoleFilter));\n      }\n      setUsers(filteredUsers);\n      log.api('用戶列表載入成功', {\n        total: response.data.length,\n        filtered: filteredUsers.length,\n        roleFilter: selectedRoleFilter\n      });\n    } catch (error) {\n      var _toast$current;\n      log.error('載入用戶列表失敗', error);\n      (_toast$current = toast.current) === null || _toast$current === void 0 ? void 0 : _toast$current.show({\n        severity: 'error',\n        summary: '載入失敗',\n        detail: '無法載入用戶列表',\n        life: 5000\n      });\n    } finally {\n      setLoading(false);\n      setRefreshing(false);\n    }\n  };\n\n  // 載入角色列表\n  const loadRoles = async () => {\n    try {\n      log.api('載入角色列表');\n      const response = await api.get('/api/users/GetRoles');\n      setRoles(response.data);\n      log.api('角色列表載入成功', {\n        count: response.data.length\n      });\n    } catch (error) {\n      var _toast$current2;\n      log.error('載入角色列表失敗', error);\n      (_toast$current2 = toast.current) === null || _toast$current2 === void 0 ? void 0 : _toast$current2.show({\n        severity: 'error',\n        summary: '載入失敗',\n        detail: '無法載入角色列表',\n        life: 5000\n      });\n    }\n  };\n\n  // 打開編輯對話框\n  const handleEditUserRoles = async user => {\n    try {\n      log.api('載入用戶角色', {\n        userId: user.userId\n      });\n      const response = await api.get(`/api/users/GetUserRoles/${user.userId}`);\n      const userData = response.data;\n      setEditingUser({\n        userId: userData.userId,\n        userName: userData.userName,\n        userAccount: userData.userAccount,\n        roles: userData.roles || []\n      });\n\n      // 安全檢查：確保 Roles 存在且為陣列\n      const roles = userData.roles || [];\n      setSelectedRoles(roles.map(role => role.roleId));\n      setShowEditDialog(true);\n      log.api('用戶角色載入成功', userData);\n    } catch (error) {\n      var _toast$current3;\n      log.error('載入用戶角色失敗', error);\n      (_toast$current3 = toast.current) === null || _toast$current3 === void 0 ? void 0 : _toast$current3.show({\n        severity: 'error',\n        summary: '載入失敗',\n        detail: '無法載入用戶角色資訊',\n        life: 5000\n      });\n    }\n  };\n\n  // 保存用戶角色\n  const handleSaveUserRoles = async () => {\n    if (!editingUser) return;\n    try {\n      var _toast$current4;\n      log.api('更新用戶角色', {\n        userId: editingUser.userId,\n        roleIds: selectedRoles\n      });\n      await api.put('/api/users/UpdateUserRoles', {\n        UserId: editingUser.userId,\n        RoleIds: selectedRoles\n      });\n      (_toast$current4 = toast.current) === null || _toast$current4 === void 0 ? void 0 : _toast$current4.show({\n        severity: 'success',\n        summary: '更新成功',\n        detail: '用戶角色權限已更新',\n        life: 3000\n      });\n      setShowEditDialog(false);\n      loadUsers(); // 重新載入用戶列表\n\n      log.api('用戶角色更新成功');\n    } catch (error) {\n      var _toast$current5;\n      log.error('更新用戶角色失敗', error);\n      (_toast$current5 = toast.current) === null || _toast$current5 === void 0 ? void 0 : _toast$current5.show({\n        severity: 'error',\n        summary: '更新失敗',\n        detail: '無法更新用戶角色權限',\n        life: 5000\n      });\n    }\n  };\n\n  // 初始化載入\n  useEffect(() => {\n    loadUsers();\n    loadRoles();\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, []);\n\n  // 監聽角色篩選變化\n  useEffect(() => {\n    if (roles.length > 0) {\n      loadUsers();\n    }\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [selectedRoleFilter]);\n\n  // 搜索處理\n  const handleSearch = () => {\n    loadUsers();\n  };\n\n  // 重置緩存\n  const resetMemCache = async () => {\n    try {\n      var _toast$current6;\n      await api.get('/api/system/ResetMemCache');\n      (_toast$current6 = toast.current) === null || _toast$current6 === void 0 ? void 0 : _toast$current6.show({\n        severity: 'success',\n        summary: '重置成功',\n        detail: '緩存已重置',\n        life: 3000\n      });\n    } catch (error) {\n      var _toast$current7;\n      log.error('重置緩存失敗', error);\n      (_toast$current7 = toast.current) === null || _toast$current7 === void 0 ? void 0 : _toast$current7.show({\n        severity: 'error',\n        summary: '重置失敗',\n        detail: '無法重置緩存',\n        life: 5000\n      });\n    }\n  };\n\n  // 角色標籤模板\n  const rolesBodyTemplate = rowData => {\n    // 安全檢查：確保 Roles 存在且為陣列\n    if (!rowData.roles || !Array.isArray(rowData.roles) || rowData.roles.length === 0) {\n      return /*#__PURE__*/_jsxDEV(Tag, {\n        value: \"\\u7121\\u89D2\\u8272\",\n        severity: \"warning\",\n        className: \"text-sm\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 241,\n        columnNumber: 9\n      }, this);\n    }\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex flex-wrap gap-1\",\n      children: rowData.roles.map(role => /*#__PURE__*/_jsxDEV(Tag, {\n        value: role.roleName,\n        severity: \"info\",\n        className: \"text-sm\"\n      }, role.roleId, false, {\n        fileName: _jsxFileName,\n        lineNumber: 252,\n        columnNumber: 11\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 250,\n      columnNumber: 7\n    }, this);\n  };\n\n  // 操作按鈕模板\n  const actionBodyTemplate = rowData => {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex gap-2\",\n      children: hasPermission('/users', 'write') && /*#__PURE__*/_jsxDEV(Button, {\n        icon: \"pi pi-pencil\",\n        className: \"p-button-success\",\n        size: \"small\",\n        onClick: () => handleEditUserRoles(rowData),\n        label: \"\\u7DE8\\u8F2F\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 268,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 266,\n      columnNumber: 7\n    }, this);\n  };\n\n  // 狀態模板\n  const statusBodyTemplate = rowData => {\n    return /*#__PURE__*/_jsxDEV(Tag, {\n      value: rowData.isEnabled ? '啟用' : '停用',\n      severity: rowData.isEnabled ? 'success' : 'danger'\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 283,\n      columnNumber: 7\n    }, this);\n  };\n\n  // 可選角色選項\n  const availableRoleOptions = roles.map(role => ({\n    label: role.Name,\n    value: role.Id\n  }));\n  const paginatorLeft = /*#__PURE__*/_jsxDEV(Button, {\n    type: \"button\",\n    icon: \"pi pi-refresh\",\n    text: true,\n    onClick: () => loadUsers()\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 297,\n    columnNumber: 7\n  }, this);\n  const paginatorRight = /*#__PURE__*/_jsxDEV(\"div\", {}, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 304,\n    columnNumber: 26\n  }, this);\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex align-items-center justify-content-center min-h-screen\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center\",\n        children: [/*#__PURE__*/_jsxDEV(ProgressSpinner, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 310,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"mt-3\",\n          children: \"\\u8F09\\u5165\\u7528\\u6236\\u8CC7\\u6599\\u4E2D...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 311,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 309,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 308,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"users-page\",\n    children: [/*#__PURE__*/_jsxDEV(Toast, {\n      ref: toast\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 319,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      title: \"\\u7528\\u6236\\u6B0A\\u9650\\u7BA1\\u7406\",\n      className: \"mb-4\",\n      children: /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"text-600 line-height-3 m-0\",\n        children: \"\\u7BA1\\u7406\\u7CFB\\u7D71\\u7684\\u7528\\u6236\\u6B0A\\u9650\\uFF0C\\u5305\\u62EC\\u5206\\u914D\\u89D2\\u8272\\u548C\\u67E5\\u770B\\u7528\\u6236\\u8A73\\u7D30\\u4FE1\\u606F\\u3002\\u60A8\\u53EF\\u4EE5\\u7DE8\\u8F2F\\u7528\\u6236\\u7684\\u89D2\\u8272\\u6B0A\\u9650\\u4EE5\\u63A7\\u5236\\u5176\\u5728\\u7CFB\\u7D71\\u4E2D\\u7684\\u8A2A\\u554F\\u548C\\u64CD\\u4F5C\\u3002\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 322,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 321,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      className: \"mb-4\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"col-6 md:col-4\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"p-inputgroup\",\n            children: [/*#__PURE__*/_jsxDEV(InputText, {\n              placeholder: \"\\u641C\\u5C0B\\u7528\\u6236\\u540D\\u7A31\",\n              value: searchName,\n              onChange: e => setSearchName(e.target.value),\n              onKeyDown: e => e.key === 'Enter' && handleSearch()\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 332,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              icon: \"pi pi-search\",\n              onClick: handleSearch,\n              disabled: refreshing\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 338,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 331,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 330,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"col-6 md:col-4\",\n          children: /*#__PURE__*/_jsxDEV(Dropdown, {\n            value: selectedRoleFilter,\n            options: [{\n              label: '全部角色',\n              value: null\n            }, ...roles.map(role => ({\n              label: role.Name,\n              value: role.Id\n            }))],\n            onChange: e => setSelectedRoleFilter(e.value),\n            placeholder: \"\\u7BE9\\u9078\\u89D2\\u8272\",\n            className: \"w-full\",\n            showClear: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 346,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 345,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"col-6 md:col-4 \",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex gap-2\",\n            children: /*#__PURE__*/_jsxDEV(Button, {\n              label: \"\\u7DE9\\u5B58\\u91CD\\u7F6E\",\n              className: \"p-button-danger\",\n              icon: \"pi pi-trash\",\n              onClick: resetMemCache\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 360,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 359,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 358,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 329,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 328,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      children: /*#__PURE__*/_jsxDEV(DataTable, {\n        value: users,\n        paginator: true,\n        rows: 10,\n        rowsPerPageOptions: [5, 10, 25, 50],\n        sortMode: \"multiple\",\n        removableSort: true,\n        filterDisplay: \"menu\",\n        globalFilterFields: ['userName', 'userAccount', 'userEmail'],\n        emptyMessage: \"\\u6C92\\u6709\\u627E\\u5230\\u7528\\u6236\\u8CC7\\u6599\",\n        className: \"p-datatable-gridlines\",\n        paginatorLeft: paginatorLeft,\n        paginatorRight: paginatorRight,\n        children: [/*#__PURE__*/_jsxDEV(Column, {\n          field: \"userName\",\n          header: \"\\u7528\\u6236\\u540D\\u7A31\",\n          sortable: true,\n          filter: true,\n          filterPlaceholder: \"\\u641C\\u5C0B\\u540D\\u7A31\",\n          style: {\n            minWidth: '150px'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 387,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Column, {\n          field: \"userAccount\",\n          header: \"\\u5E33\\u865F\",\n          sortable: true,\n          filter: true,\n          filterPlaceholder: \"\\u641C\\u5C0B\\u5E33\\u865F\",\n          style: {\n            minWidth: '120px'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 395,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Column, {\n          field: \"userEmail\",\n          header: \"Email\",\n          sortable: true,\n          filter: true,\n          filterPlaceholder: \"\\u641C\\u5C0BEmail\",\n          style: {\n            minWidth: '200px'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 403,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Column, {\n          header: \"\\u89D2\\u8272\\u6B0A\\u9650\",\n          body: rolesBodyTemplate,\n          style: {\n            minWidth: '200px'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 411,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Column, {\n          field: \"isEnabled\",\n          header: \"\\u72C0\\u614B\",\n          body: statusBodyTemplate,\n          sortable: true,\n          style: {\n            minWidth: '100px'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 416,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Column, {\n          header: \"\\u64CD\\u4F5C\",\n          body: actionBodyTemplate,\n          style: {\n            minWidth: '100px'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 423,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 373,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 372,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n      header: `編輯用戶角色權限 - ${editingUser === null || editingUser === void 0 ? void 0 : editingUser.userName}`,\n      visible: showEditDialog,\n      style: {\n        width: '500px'\n      },\n      onHide: () => setShowEditDialog(false),\n      footer: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex justify-content-end gap-2\",\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          label: \"\\u53D6\\u6D88\",\n          icon: \"pi pi-times\",\n          onClick: () => setShowEditDialog(false),\n          className: \"p-button-text\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 439,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          label: \"\\u4FDD\\u5B58\",\n          icon: \"pi pi-check\",\n          onClick: handleSaveUserRoles,\n          className: \"p-button-primary\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 445,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 438,\n        columnNumber: 11\n      }, this),\n      children: editingUser && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"col-12\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"field\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"font-bold\",\n              children: \"\\u7528\\u6236\\u5E33\\u865F:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 458,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"m-0\",\n              children: editingUser.userAccount\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 459,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 457,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 456,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"col-12\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"field\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"roles\",\n              className: \"font-bold\",\n              children: \"\\u89D2\\u8272\\u6B0A\\u9650:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 464,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(MultiSelect, {\n              id: \"roles\",\n              value: selectedRoles,\n              options: availableRoleOptions,\n              onChange: e => setSelectedRoles(e.value),\n              placeholder: \"\\u9078\\u64C7\\u89D2\\u8272\\u6B0A\\u9650\",\n              className: \"w-full\",\n              display: \"chip\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 465,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 463,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 462,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 455,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 432,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 318,\n    columnNumber: 5\n  }, this);\n};\n_s(UsersPage, \"OhR6bv7fHvRJP3PVP0W5doL4djk=\", false, function () {\n  return [usePermissions];\n});\n_c = UsersPage;\nexport default UsersPage;\nvar _c;\n$RefreshReg$(_c, \"UsersPage\");", "map": {"version": 3, "names": ["<PERSON><PERSON>", "Column", "DataTable", "Dialog", "Dropdown", "InputText", "Toast", "Tag", "MultiSelect", "Card", "ProgressSpinner", "React", "useRef", "useState", "useEffect", "api", "log", "usePermissions", "jsxDEV", "_jsxDEV", "UsersPage", "_s", "toast", "users", "setUsers", "roles", "setRoles", "loading", "setLoading", "searchName", "setSearchName", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "setSelectedRoleFilter", "showEditDialog", "setShowEditDialog", "editingUser", "setEditingUser", "selectedRoles", "setSelectedRoles", "refreshing", "setRefreshing", "hasPermission", "loadUsers", "response", "get", "params", "name", "filteredUsers", "data", "filter", "user", "some", "role", "roleId", "total", "length", "filtered", "<PERSON><PERSON><PERSON>er", "error", "_toast$current", "current", "show", "severity", "summary", "detail", "life", "loadRoles", "count", "_toast$current2", "handleEditUserRoles", "userId", "userData", "userName", "userAccount", "map", "_toast$current3", "handleSaveUserRoles", "_toast$current4", "roleIds", "put", "UserId", "RoleIds", "_toast$current5", "handleSearch", "resetMemCache", "_toast$current6", "_toast$current7", "rolesBodyTemplate", "rowData", "Array", "isArray", "value", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "children", "<PERSON><PERSON><PERSON>", "actionBodyTemplate", "icon", "size", "onClick", "label", "statusBodyTemplate", "isEnabled", "availableRoleOptions", "Name", "Id", "paginatorLeft", "type", "text", "paginatorRight", "ref", "title", "placeholder", "onChange", "e", "target", "onKeyDown", "key", "disabled", "options", "showClear", "paginator", "rows", "rowsPerPageOptions", "sortMode", "removableSort", "filterDisplay", "globalFilterFields", "emptyMessage", "field", "header", "sortable", "filterPlaceholder", "style", "min<PERSON><PERSON><PERSON>", "body", "visible", "width", "onHide", "footer", "htmlFor", "id", "display", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/demo-git/demo-react/my-fullstack-app/frontend/src/components/Page/UsersPage.tsx"], "sourcesContent": ["import { But<PERSON> } from 'primereact/button';\nimport { Column } from 'primereact/column';\nimport { DataTable } from 'primereact/datatable';\nimport { Dialog } from 'primereact/dialog';\nimport { Dropdown } from 'primereact/dropdown';\nimport { InputText } from 'primereact/inputtext';\nimport { Toast } from 'primereact/toast';\nimport { Tag } from 'primereact/tag';\nimport { MultiSelect } from 'primereact/multiselect';\nimport { Card } from 'primereact/card';\nimport { ProgressSpinner } from 'primereact/progressspinner';\nimport React, { useRef, useState, useEffect } from 'react';\nimport api from '../../services/api';\nimport { log } from '../../utils/logger';\nimport { usePermissions } from '../../hooks/usePermissions';\n\ninterface Role {\n  Id: number;\n  Name: string;\n}\n\ninterface UserRole {\n  roleId: number;\n  roleName: string;\n}\n\ninterface User {\n  userId: number;\n  userName: string;\n  userAccount: string;\n  userEmail?: string;\n  userPhone?: string;\n  address?: string;\n  gender?: string;\n  birthDate?: string;\n  isEnabled: boolean;\n  createdAt: string;\n  updatedAt: string;\n  roles?: UserRole[];\n}\n\ninterface EditUserRolesData {\n  userId: number;\n  userName: string;\n  userAccount: string;\n  roles: UserRole[];\n}\n\nconst UsersPage: React.FC = () => {\n  const toast = useRef<Toast>(null);\n  const [users, setUsers] = useState<User[]>([]);\n  const [roles, setRoles] = useState<Role[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [searchName, setSearchName] = useState('');\n  const [selectedRoleFilter, setSelectedRoleFilter] = useState<number | null>(null);\n  const [showEditDialog, setShowEditDialog] = useState(false);\n  const [editingUser, setEditingUser] = useState<EditUserRolesData | null>(null);\n  const [selectedRoles, setSelectedRoles] = useState<number[]>([]);\n  const [refreshing, setRefreshing] = useState(false);\n  const { hasPermission } = usePermissions();\n\n  // 載入用戶列表\n  const loadUsers = async () => {\n    try {\n      setRefreshing(true);\n      log.api('載入用戶列表', { searchName, selectedRoleFilter });\n\n      const response = await api.get('/api/users/GetUserRolesList', {\n        params: { name: searchName }\n      });\n\n      let filteredUsers = response.data;\n\n      // 如果選擇了角色篩選，則進行前端篩選\n      if (selectedRoleFilter) {\n        filteredUsers = response.data.filter((user: User) =>\n          user.roles && user.roles.some((role: UserRole) => role.roleId === selectedRoleFilter)\n        );\n      }\n\n      setUsers(filteredUsers);\n      log.api('用戶列表載入成功', {\n        total: response.data.length,\n        filtered: filteredUsers.length,\n        roleFilter: selectedRoleFilter\n      });\n\n    } catch (error: any) {\n      log.error('載入用戶列表失敗', error);\n      toast.current?.show({\n        severity: 'error',\n        summary: '載入失敗',\n        detail: '無法載入用戶列表',\n        life: 5000\n      });\n    } finally {\n      setLoading(false);\n      setRefreshing(false);\n    }\n  };\n\n  // 載入角色列表\n  const loadRoles = async () => {\n    try {\n      log.api('載入角色列表');\n      \n      const response = await api.get('/api/users/GetRoles');\n      setRoles(response.data);\n      \n      log.api('角色列表載入成功', { count: response.data.length });\n      \n    } catch (error: any) {\n      log.error('載入角色列表失敗', error);\n      toast.current?.show({\n        severity: 'error',\n        summary: '載入失敗',\n        detail: '無法載入角色列表',\n        life: 5000\n      });\n    }\n  };\n\n  // 打開編輯對話框\n  const handleEditUserRoles = async (user: User) => {\n    try {\n      \n      log.api('載入用戶角色', { userId: user.userId });\n\n      const response = await api.get(`/api/users/GetUserRoles/${user.userId}`);\n      const userData = response.data;\n      \n      setEditingUser({\n        userId: userData.userId,\n        userName: userData.userName,\n        userAccount: userData.userAccount,\n        roles: userData.roles || []\n      });\n\n      // 安全檢查：確保 Roles 存在且為陣列\n      const roles = userData.roles || [];\n      setSelectedRoles(roles.map((role: UserRole) => role.roleId));\n      setShowEditDialog(true);\n      \n      log.api('用戶角色載入成功', userData);\n      \n    } catch (error: any) {\n      log.error('載入用戶角色失敗', error);\n      toast.current?.show({\n        severity: 'error',\n        summary: '載入失敗',\n        detail: '無法載入用戶角色資訊',\n        life: 5000\n      });\n    }\n  };\n\n  // 保存用戶角色\n  const handleSaveUserRoles = async () => {\n    if (!editingUser) return;\n    \n    try {\n      log.api('更新用戶角色', { \n        userId: editingUser.userId, \n        roleIds: selectedRoles \n      });\n      \n      await api.put('/api/users/UpdateUserRoles', {\n        UserId: editingUser.userId,\n        RoleIds: selectedRoles\n      });\n      \n      toast.current?.show({\n        severity: 'success',\n        summary: '更新成功',\n        detail: '用戶角色權限已更新',\n        life: 3000\n      });\n      \n      setShowEditDialog(false);\n      loadUsers(); // 重新載入用戶列表\n      \n      log.api('用戶角色更新成功');\n      \n    } catch (error: any) {\n      log.error('更新用戶角色失敗', error);\n      toast.current?.show({\n        severity: 'error',\n        summary: '更新失敗',\n        detail: '無法更新用戶角色權限',\n        life: 5000\n      });\n    }\n  };\n\n  // 初始化載入\n  useEffect(() => {\n    loadUsers();\n    loadRoles();\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, []);\n\n  // 監聽角色篩選變化\n  useEffect(() => {\n    if (roles.length > 0) {\n      loadUsers();\n    }\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [selectedRoleFilter]);\n\n  // 搜索處理\n  const handleSearch = () => {\n    loadUsers();\n  };\n\n    // 重置緩存\n  const resetMemCache = async () => {\n    try {\n      await api.get('/api/system/ResetMemCache');\n      toast.current?.show({\n        severity: 'success',\n        summary: '重置成功',\n        detail: '緩存已重置',\n        life: 3000\n      });\n    } catch (error: any) {\n      log.error('重置緩存失敗', error);\n      toast.current?.show({\n        severity: 'error',\n        summary: '重置失敗',\n        detail: '無法重置緩存',\n        life: 5000\n      });\n    } \n  };\n\n  // 角色標籤模板\n  const rolesBodyTemplate = (rowData: User) => {\n    // 安全檢查：確保 Roles 存在且為陣列\n    if (!rowData.roles || !Array.isArray(rowData.roles) || rowData.roles.length === 0) {\n      return (\n        <Tag\n          value=\"無角色\"\n          severity=\"warning\"\n          className=\"text-sm\"\n        />\n      );\n    }\n\n    return (\n      <div className=\"flex flex-wrap gap-1\">\n        {rowData.roles.map((role: UserRole) => (\n          <Tag\n            key={role.roleId}\n            value={role.roleName}\n            severity=\"info\"\n            className=\"text-sm\"\n          />\n        ))}\n      </div>\n    );\n  };\n\n  // 操作按鈕模板\n  const actionBodyTemplate = (rowData: User) => {\n    return (\n      <div className=\"flex gap-2\">\n        {hasPermission('/users', 'write') && (\n          <Button\n            icon=\"pi pi-pencil\"\n            className=\"p-button-success\"\n            size=\"small\" \n            onClick={() => handleEditUserRoles(rowData)}\n            label=\"編輯\"\n          />\n        )}\n      </div>\n    );\n  };\n\n  // 狀態模板\n  const statusBodyTemplate = (rowData: User) => {\n    return (\n      <Tag \n        value={rowData.isEnabled ? '啟用' : '停用'} \n        severity={rowData.isEnabled ? 'success' : 'danger'}\n      />\n    );\n  };\n\n  // 可選角色選項\n  const availableRoleOptions = roles.map(role => ({\n    label: role.Name,\n    value: role.Id\n  }));\n\n  const paginatorLeft = (\n      <Button\n          type=\"button\"\n          icon=\"pi pi-refresh\"\n          text\n          onClick={() => loadUsers()}\n      />\n  );\n  const paginatorRight = <div></div>;\n\n  if (loading) {\n    return (\n      <div className=\"flex align-items-center justify-content-center min-h-screen\">\n        <div className=\"text-center\">\n          <ProgressSpinner />\n          <p className=\"mt-3\">載入用戶資料中...</p>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"users-page\">\n      <Toast ref={toast} />\n      \n      <Card title=\"用戶權限管理\" className=\"mb-4\">\n        <p className=\"text-600 line-height-3 m-0\">\n          管理系統的用戶權限，包括分配角色和查看用戶詳細信息。您可以編輯用戶的角色權限以控制其在系統中的訪問和操作。\n        </p>\n      </Card>\n      \n      {/* 搜尋條件 */}\n      <Card className=\"mb-4\">\n        <div className=\"grid\">\n          <div className=\"col-6 md:col-4\">\n            <div className=\"p-inputgroup\">\n              <InputText\n                placeholder=\"搜尋用戶名稱\"\n                value={searchName}\n                onChange={(e) => setSearchName(e.target.value)}\n                onKeyDown={(e) => e.key === 'Enter' && handleSearch()}\n              />\n              <Button\n                icon=\"pi pi-search\"\n                onClick={handleSearch}\n                disabled={refreshing}\n              />\n            </div>\n          </div>\n          <div className=\"col-6 md:col-4\">\n            <Dropdown\n              value={selectedRoleFilter}\n              options={[\n                { label: '全部角色', value: null },\n                ...roles.map(role => ({ label: role.Name, value: role.Id }))\n              ]}\n              onChange={(e) => setSelectedRoleFilter(e.value)}\n              placeholder=\"篩選角色\"\n              className=\"w-full\"\n              showClear\n            />\n          </div>\n          <div className=\"col-6 md:col-4 \">\n            <div className=\"flex gap-2\">\n              <Button\n                  label=\"緩存重置\"\n                  className=\"p-button-danger\"\n                  icon=\"pi pi-trash\"\n                  onClick={resetMemCache}\n                />\n            </div>\n          </div>\n        </div>\n      </Card>\n\n      {/* 用戶列表 */}\n      <Card>\n        <DataTable\n          value={users}\n          paginator\n          rows={10}\n          rowsPerPageOptions={[5, 10, 25, 50]}\n          sortMode=\"multiple\"\n          removableSort\n          filterDisplay=\"menu\"\n          globalFilterFields={['userName', 'userAccount', 'userEmail']}\n          emptyMessage=\"沒有找到用戶資料\"\n          className=\"p-datatable-gridlines\"\n          paginatorLeft={paginatorLeft}\n          paginatorRight={paginatorRight}\n        >\n          <Column\n            field=\"userName\"\n            header=\"用戶名稱\"\n            sortable\n            filter\n            filterPlaceholder=\"搜尋名稱\"\n            style={{ minWidth: '150px' }}\n          />\n          <Column\n            field=\"userAccount\"\n            header=\"帳號\"\n            sortable\n            filter\n            filterPlaceholder=\"搜尋帳號\"\n            style={{ minWidth: '120px' }}\n          />\n          <Column\n            field=\"userEmail\"\n            header=\"Email\"\n            sortable\n            filter\n            filterPlaceholder=\"搜尋Email\"\n            style={{ minWidth: '200px' }}\n          />\n          <Column\n            header=\"角色權限\"\n            body={rolesBodyTemplate}\n            style={{ minWidth: '200px' }}\n          />\n          <Column\n            field=\"isEnabled\"\n            header=\"狀態\"\n            body={statusBodyTemplate}\n            sortable\n            style={{ minWidth: '100px' }}\n          />\n          <Column\n            header=\"操作\"\n            body={actionBodyTemplate}\n            style={{ minWidth: '100px' }}\n          />\n        </DataTable>\n      </Card>\n\n      {/* 編輯角色對話框 */}\n      <Dialog\n        header={`編輯用戶角色權限 - ${editingUser?.userName}`}\n        visible={showEditDialog}\n        style={{ width: '500px' }}\n        onHide={() => setShowEditDialog(false)}\n        footer={\n          <div className=\"flex justify-content-end gap-2\">\n            <Button\n              label=\"取消\"\n              icon=\"pi pi-times\"\n              onClick={() => setShowEditDialog(false)}\n              className=\"p-button-text\"\n            />\n            <Button\n              label=\"保存\"\n              icon=\"pi pi-check\"\n              onClick={handleSaveUserRoles}\n              className=\"p-button-primary\"\n            />\n          </div>\n        }\n      >\n        {editingUser && (\n          <div className=\"grid\">\n            <div className=\"col-12\">\n              <div className=\"field\">\n                <label className=\"font-bold\">用戶帳號:</label>\n                <p className=\"m-0\">{editingUser.userAccount}</p>\n              </div>\n            </div>\n            <div className=\"col-12\">\n              <div className=\"field\">\n                <label htmlFor=\"roles\" className=\"font-bold\">角色權限:</label>\n                <MultiSelect\n                  id=\"roles\"\n                  value={selectedRoles}\n                  options={availableRoleOptions}\n                  onChange={(e) => setSelectedRoles(e.value)}\n                  placeholder=\"選擇角色權限\"\n                  className=\"w-full\"\n                  display=\"chip\"\n                />\n              </div>\n            </div>\n          </div>\n        )}\n      </Dialog>\n    </div>\n  );\n};\n\nexport default UsersPage;\n"], "mappings": ";;AAAA,SAASA,MAAM,QAAQ,mBAAmB;AAC1C,SAASC,MAAM,QAAQ,mBAAmB;AAC1C,SAASC,SAAS,QAAQ,sBAAsB;AAChD,SAASC,MAAM,QAAQ,mBAAmB;AAC1C,SAASC,QAAQ,QAAQ,qBAAqB;AAC9C,SAASC,SAAS,QAAQ,sBAAsB;AAChD,SAASC,KAAK,QAAQ,kBAAkB;AACxC,SAASC,GAAG,QAAQ,gBAAgB;AACpC,SAASC,WAAW,QAAQ,wBAAwB;AACpD,SAASC,IAAI,QAAQ,iBAAiB;AACtC,SAASC,eAAe,QAAQ,4BAA4B;AAC5D,OAAOC,KAAK,IAAIC,MAAM,EAAEC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAC1D,OAAOC,GAAG,MAAM,oBAAoB;AACpC,SAASC,GAAG,QAAQ,oBAAoB;AACxC,SAASC,cAAc,QAAQ,4BAA4B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAkC5D,MAAMC,SAAmB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAChC,MAAMC,KAAK,GAAGV,MAAM,CAAQ,IAAI,CAAC;EACjC,MAAM,CAACW,KAAK,EAAEC,QAAQ,CAAC,GAAGX,QAAQ,CAAS,EAAE,CAAC;EAC9C,MAAM,CAACY,KAAK,EAAEC,QAAQ,CAAC,GAAGb,QAAQ,CAAS,EAAE,CAAC;EAC9C,MAAM,CAACc,OAAO,EAAEC,UAAU,CAAC,GAAGf,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACgB,UAAU,EAAEC,aAAa,CAAC,GAAGjB,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACkB,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGnB,QAAQ,CAAgB,IAAI,CAAC;EACjF,MAAM,CAACoB,cAAc,EAAEC,iBAAiB,CAAC,GAAGrB,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAM,CAACsB,WAAW,EAAEC,cAAc,CAAC,GAAGvB,QAAQ,CAA2B,IAAI,CAAC;EAC9E,MAAM,CAACwB,aAAa,EAAEC,gBAAgB,CAAC,GAAGzB,QAAQ,CAAW,EAAE,CAAC;EAChE,MAAM,CAAC0B,UAAU,EAAEC,aAAa,CAAC,GAAG3B,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM;IAAE4B;EAAc,CAAC,GAAGxB,cAAc,CAAC,CAAC;;EAE1C;EACA,MAAMyB,SAAS,GAAG,MAAAA,CAAA,KAAY;IAC5B,IAAI;MACFF,aAAa,CAAC,IAAI,CAAC;MACnBxB,GAAG,CAACD,GAAG,CAAC,QAAQ,EAAE;QAAEc,UAAU;QAAEE;MAAmB,CAAC,CAAC;MAErD,MAAMY,QAAQ,GAAG,MAAM5B,GAAG,CAAC6B,GAAG,CAAC,6BAA6B,EAAE;QAC5DC,MAAM,EAAE;UAAEC,IAAI,EAAEjB;QAAW;MAC7B,CAAC,CAAC;MAEF,IAAIkB,aAAa,GAAGJ,QAAQ,CAACK,IAAI;;MAEjC;MACA,IAAIjB,kBAAkB,EAAE;QACtBgB,aAAa,GAAGJ,QAAQ,CAACK,IAAI,CAACC,MAAM,CAAEC,IAAU,IAC9CA,IAAI,CAACzB,KAAK,IAAIyB,IAAI,CAACzB,KAAK,CAAC0B,IAAI,CAAEC,IAAc,IAAKA,IAAI,CAACC,MAAM,KAAKtB,kBAAkB,CACtF,CAAC;MACH;MAEAP,QAAQ,CAACuB,aAAa,CAAC;MACvB/B,GAAG,CAACD,GAAG,CAAC,UAAU,EAAE;QAClBuC,KAAK,EAAEX,QAAQ,CAACK,IAAI,CAACO,MAAM;QAC3BC,QAAQ,EAAET,aAAa,CAACQ,MAAM;QAC9BE,UAAU,EAAE1B;MACd,CAAC,CAAC;IAEJ,CAAC,CAAC,OAAO2B,KAAU,EAAE;MAAA,IAAAC,cAAA;MACnB3C,GAAG,CAAC0C,KAAK,CAAC,UAAU,EAAEA,KAAK,CAAC;MAC5B,CAAAC,cAAA,GAAArC,KAAK,CAACsC,OAAO,cAAAD,cAAA,uBAAbA,cAAA,CAAeE,IAAI,CAAC;QAClBC,QAAQ,EAAE,OAAO;QACjBC,OAAO,EAAE,MAAM;QACfC,MAAM,EAAE,UAAU;QAClBC,IAAI,EAAE;MACR,CAAC,CAAC;IACJ,CAAC,SAAS;MACRrC,UAAU,CAAC,KAAK,CAAC;MACjBY,aAAa,CAAC,KAAK,CAAC;IACtB;EACF,CAAC;;EAED;EACA,MAAM0B,SAAS,GAAG,MAAAA,CAAA,KAAY;IAC5B,IAAI;MACFlD,GAAG,CAACD,GAAG,CAAC,QAAQ,CAAC;MAEjB,MAAM4B,QAAQ,GAAG,MAAM5B,GAAG,CAAC6B,GAAG,CAAC,qBAAqB,CAAC;MACrDlB,QAAQ,CAACiB,QAAQ,CAACK,IAAI,CAAC;MAEvBhC,GAAG,CAACD,GAAG,CAAC,UAAU,EAAE;QAAEoD,KAAK,EAAExB,QAAQ,CAACK,IAAI,CAACO;MAAO,CAAC,CAAC;IAEtD,CAAC,CAAC,OAAOG,KAAU,EAAE;MAAA,IAAAU,eAAA;MACnBpD,GAAG,CAAC0C,KAAK,CAAC,UAAU,EAAEA,KAAK,CAAC;MAC5B,CAAAU,eAAA,GAAA9C,KAAK,CAACsC,OAAO,cAAAQ,eAAA,uBAAbA,eAAA,CAAeP,IAAI,CAAC;QAClBC,QAAQ,EAAE,OAAO;QACjBC,OAAO,EAAE,MAAM;QACfC,MAAM,EAAE,UAAU;QAClBC,IAAI,EAAE;MACR,CAAC,CAAC;IACJ;EACF,CAAC;;EAED;EACA,MAAMI,mBAAmB,GAAG,MAAOnB,IAAU,IAAK;IAChD,IAAI;MAEFlC,GAAG,CAACD,GAAG,CAAC,QAAQ,EAAE;QAAEuD,MAAM,EAAEpB,IAAI,CAACoB;MAAO,CAAC,CAAC;MAE1C,MAAM3B,QAAQ,GAAG,MAAM5B,GAAG,CAAC6B,GAAG,CAAC,2BAA2BM,IAAI,CAACoB,MAAM,EAAE,CAAC;MACxE,MAAMC,QAAQ,GAAG5B,QAAQ,CAACK,IAAI;MAE9BZ,cAAc,CAAC;QACbkC,MAAM,EAAEC,QAAQ,CAACD,MAAM;QACvBE,QAAQ,EAAED,QAAQ,CAACC,QAAQ;QAC3BC,WAAW,EAAEF,QAAQ,CAACE,WAAW;QACjChD,KAAK,EAAE8C,QAAQ,CAAC9C,KAAK,IAAI;MAC3B,CAAC,CAAC;;MAEF;MACA,MAAMA,KAAK,GAAG8C,QAAQ,CAAC9C,KAAK,IAAI,EAAE;MAClCa,gBAAgB,CAACb,KAAK,CAACiD,GAAG,CAAEtB,IAAc,IAAKA,IAAI,CAACC,MAAM,CAAC,CAAC;MAC5DnB,iBAAiB,CAAC,IAAI,CAAC;MAEvBlB,GAAG,CAACD,GAAG,CAAC,UAAU,EAAEwD,QAAQ,CAAC;IAE/B,CAAC,CAAC,OAAOb,KAAU,EAAE;MAAA,IAAAiB,eAAA;MACnB3D,GAAG,CAAC0C,KAAK,CAAC,UAAU,EAAEA,KAAK,CAAC;MAC5B,CAAAiB,eAAA,GAAArD,KAAK,CAACsC,OAAO,cAAAe,eAAA,uBAAbA,eAAA,CAAed,IAAI,CAAC;QAClBC,QAAQ,EAAE,OAAO;QACjBC,OAAO,EAAE,MAAM;QACfC,MAAM,EAAE,YAAY;QACpBC,IAAI,EAAE;MACR,CAAC,CAAC;IACJ;EACF,CAAC;;EAED;EACA,MAAMW,mBAAmB,GAAG,MAAAA,CAAA,KAAY;IACtC,IAAI,CAACzC,WAAW,EAAE;IAElB,IAAI;MAAA,IAAA0C,eAAA;MACF7D,GAAG,CAACD,GAAG,CAAC,QAAQ,EAAE;QAChBuD,MAAM,EAAEnC,WAAW,CAACmC,MAAM;QAC1BQ,OAAO,EAAEzC;MACX,CAAC,CAAC;MAEF,MAAMtB,GAAG,CAACgE,GAAG,CAAC,4BAA4B,EAAE;QAC1CC,MAAM,EAAE7C,WAAW,CAACmC,MAAM;QAC1BW,OAAO,EAAE5C;MACX,CAAC,CAAC;MAEF,CAAAwC,eAAA,GAAAvD,KAAK,CAACsC,OAAO,cAAAiB,eAAA,uBAAbA,eAAA,CAAehB,IAAI,CAAC;QAClBC,QAAQ,EAAE,SAAS;QACnBC,OAAO,EAAE,MAAM;QACfC,MAAM,EAAE,WAAW;QACnBC,IAAI,EAAE;MACR,CAAC,CAAC;MAEF/B,iBAAiB,CAAC,KAAK,CAAC;MACxBQ,SAAS,CAAC,CAAC,CAAC,CAAC;;MAEb1B,GAAG,CAACD,GAAG,CAAC,UAAU,CAAC;IAErB,CAAC,CAAC,OAAO2C,KAAU,EAAE;MAAA,IAAAwB,eAAA;MACnBlE,GAAG,CAAC0C,KAAK,CAAC,UAAU,EAAEA,KAAK,CAAC;MAC5B,CAAAwB,eAAA,GAAA5D,KAAK,CAACsC,OAAO,cAAAsB,eAAA,uBAAbA,eAAA,CAAerB,IAAI,CAAC;QAClBC,QAAQ,EAAE,OAAO;QACjBC,OAAO,EAAE,MAAM;QACfC,MAAM,EAAE,YAAY;QACpBC,IAAI,EAAE;MACR,CAAC,CAAC;IACJ;EACF,CAAC;;EAED;EACAnD,SAAS,CAAC,MAAM;IACd4B,SAAS,CAAC,CAAC;IACXwB,SAAS,CAAC,CAAC;IACX;EACF,CAAC,EAAE,EAAE,CAAC;;EAEN;EACApD,SAAS,CAAC,MAAM;IACd,IAAIW,KAAK,CAAC8B,MAAM,GAAG,CAAC,EAAE;MACpBb,SAAS,CAAC,CAAC;IACb;IACA;EACF,CAAC,EAAE,CAACX,kBAAkB,CAAC,CAAC;;EAExB;EACA,MAAMoD,YAAY,GAAGA,CAAA,KAAM;IACzBzC,SAAS,CAAC,CAAC;EACb,CAAC;;EAEC;EACF,MAAM0C,aAAa,GAAG,MAAAA,CAAA,KAAY;IAChC,IAAI;MAAA,IAAAC,eAAA;MACF,MAAMtE,GAAG,CAAC6B,GAAG,CAAC,2BAA2B,CAAC;MAC1C,CAAAyC,eAAA,GAAA/D,KAAK,CAACsC,OAAO,cAAAyB,eAAA,uBAAbA,eAAA,CAAexB,IAAI,CAAC;QAClBC,QAAQ,EAAE,SAAS;QACnBC,OAAO,EAAE,MAAM;QACfC,MAAM,EAAE,OAAO;QACfC,IAAI,EAAE;MACR,CAAC,CAAC;IACJ,CAAC,CAAC,OAAOP,KAAU,EAAE;MAAA,IAAA4B,eAAA;MACnBtE,GAAG,CAAC0C,KAAK,CAAC,QAAQ,EAAEA,KAAK,CAAC;MAC1B,CAAA4B,eAAA,GAAAhE,KAAK,CAACsC,OAAO,cAAA0B,eAAA,uBAAbA,eAAA,CAAezB,IAAI,CAAC;QAClBC,QAAQ,EAAE,OAAO;QACjBC,OAAO,EAAE,MAAM;QACfC,MAAM,EAAE,QAAQ;QAChBC,IAAI,EAAE;MACR,CAAC,CAAC;IACJ;EACF,CAAC;;EAED;EACA,MAAMsB,iBAAiB,GAAIC,OAAa,IAAK;IAC3C;IACA,IAAI,CAACA,OAAO,CAAC/D,KAAK,IAAI,CAACgE,KAAK,CAACC,OAAO,CAACF,OAAO,CAAC/D,KAAK,CAAC,IAAI+D,OAAO,CAAC/D,KAAK,CAAC8B,MAAM,KAAK,CAAC,EAAE;MACjF,oBACEpC,OAAA,CAACZ,GAAG;QACFoF,KAAK,EAAC,oBAAK;QACX7B,QAAQ,EAAC,SAAS;QAClB8B,SAAS,EAAC;MAAS;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpB,CAAC;IAEN;IAEA,oBACE7E,OAAA;MAAKyE,SAAS,EAAC,sBAAsB;MAAAK,QAAA,EAClCT,OAAO,CAAC/D,KAAK,CAACiD,GAAG,CAAEtB,IAAc,iBAChCjC,OAAA,CAACZ,GAAG;QAEFoF,KAAK,EAAEvC,IAAI,CAAC8C,QAAS;QACrBpC,QAAQ,EAAC,MAAM;QACf8B,SAAS,EAAC;MAAS,GAHdxC,IAAI,CAACC,MAAM;QAAAwC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAIjB,CACF;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC;EAEV,CAAC;;EAED;EACA,MAAMG,kBAAkB,GAAIX,OAAa,IAAK;IAC5C,oBACErE,OAAA;MAAKyE,SAAS,EAAC,YAAY;MAAAK,QAAA,EACxBxD,aAAa,CAAC,QAAQ,EAAE,OAAO,CAAC,iBAC/BtB,OAAA,CAACnB,MAAM;QACLoG,IAAI,EAAC,cAAc;QACnBR,SAAS,EAAC,kBAAkB;QAC5BS,IAAI,EAAC,OAAO;QACZC,OAAO,EAAEA,CAAA,KAAMjC,mBAAmB,CAACmB,OAAO,CAAE;QAC5Ce,KAAK,EAAC;MAAI;QAAAV,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACX;IACF;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAEV,CAAC;;EAED;EACA,MAAMQ,kBAAkB,GAAIhB,OAAa,IAAK;IAC5C,oBACErE,OAAA,CAACZ,GAAG;MACFoF,KAAK,EAAEH,OAAO,CAACiB,SAAS,GAAG,IAAI,GAAG,IAAK;MACvC3C,QAAQ,EAAE0B,OAAO,CAACiB,SAAS,GAAG,SAAS,GAAG;IAAS;MAAAZ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACpD,CAAC;EAEN,CAAC;;EAED;EACA,MAAMU,oBAAoB,GAAGjF,KAAK,CAACiD,GAAG,CAACtB,IAAI,KAAK;IAC9CmD,KAAK,EAAEnD,IAAI,CAACuD,IAAI;IAChBhB,KAAK,EAAEvC,IAAI,CAACwD;EACd,CAAC,CAAC,CAAC;EAEH,MAAMC,aAAa,gBACf1F,OAAA,CAACnB,MAAM;IACH8G,IAAI,EAAC,QAAQ;IACbV,IAAI,EAAC,eAAe;IACpBW,IAAI;IACJT,OAAO,EAAEA,CAAA,KAAM5D,SAAS,CAAC;EAAE;IAAAmD,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAC9B,CACJ;EACD,MAAMgB,cAAc,gBAAG7F,OAAA;IAAA0E,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAU,CAAC;EAElC,IAAIrE,OAAO,EAAE;IACX,oBACER,OAAA;MAAKyE,SAAS,EAAC,6DAA6D;MAAAK,QAAA,eAC1E9E,OAAA;QAAKyE,SAAS,EAAC,aAAa;QAAAK,QAAA,gBAC1B9E,OAAA,CAACT,eAAe;UAAAmF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACnB7E,OAAA;UAAGyE,SAAS,EAAC,MAAM;UAAAK,QAAA,EAAC;QAAU;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC/B;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,oBACE7E,OAAA;IAAKyE,SAAS,EAAC,YAAY;IAAAK,QAAA,gBACzB9E,OAAA,CAACb,KAAK;MAAC2G,GAAG,EAAE3F;IAAM;MAAAuE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAErB7E,OAAA,CAACV,IAAI;MAACyG,KAAK,EAAC,sCAAQ;MAACtB,SAAS,EAAC,MAAM;MAAAK,QAAA,eACnC9E,OAAA;QAAGyE,SAAS,EAAC,4BAA4B;QAAAK,QAAA,EAAC;MAE1C;QAAAJ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC,eAGP7E,OAAA,CAACV,IAAI;MAACmF,SAAS,EAAC,MAAM;MAAAK,QAAA,eACpB9E,OAAA;QAAKyE,SAAS,EAAC,MAAM;QAAAK,QAAA,gBACnB9E,OAAA;UAAKyE,SAAS,EAAC,gBAAgB;UAAAK,QAAA,eAC7B9E,OAAA;YAAKyE,SAAS,EAAC,cAAc;YAAAK,QAAA,gBAC3B9E,OAAA,CAACd,SAAS;cACR8G,WAAW,EAAC,sCAAQ;cACpBxB,KAAK,EAAE9D,UAAW;cAClBuF,QAAQ,EAAGC,CAAC,IAAKvF,aAAa,CAACuF,CAAC,CAACC,MAAM,CAAC3B,KAAK,CAAE;cAC/C4B,SAAS,EAAGF,CAAC,IAAKA,CAAC,CAACG,GAAG,KAAK,OAAO,IAAIrC,YAAY,CAAC;YAAE;cAAAU,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvD,CAAC,eACF7E,OAAA,CAACnB,MAAM;cACLoG,IAAI,EAAC,cAAc;cACnBE,OAAO,EAAEnB,YAAa;cACtBsC,QAAQ,EAAElF;YAAW;cAAAsD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACN7E,OAAA;UAAKyE,SAAS,EAAC,gBAAgB;UAAAK,QAAA,eAC7B9E,OAAA,CAACf,QAAQ;YACPuF,KAAK,EAAE5D,kBAAmB;YAC1B2F,OAAO,EAAE,CACP;cAAEnB,KAAK,EAAE,MAAM;cAAEZ,KAAK,EAAE;YAAK,CAAC,EAC9B,GAAGlE,KAAK,CAACiD,GAAG,CAACtB,IAAI,KAAK;cAAEmD,KAAK,EAAEnD,IAAI,CAACuD,IAAI;cAAEhB,KAAK,EAAEvC,IAAI,CAACwD;YAAG,CAAC,CAAC,CAAC,CAC5D;YACFQ,QAAQ,EAAGC,CAAC,IAAKrF,qBAAqB,CAACqF,CAAC,CAAC1B,KAAK,CAAE;YAChDwB,WAAW,EAAC,0BAAM;YAClBvB,SAAS,EAAC,QAAQ;YAClB+B,SAAS;UAAA;YAAA9B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eACN7E,OAAA;UAAKyE,SAAS,EAAC,iBAAiB;UAAAK,QAAA,eAC9B9E,OAAA;YAAKyE,SAAS,EAAC,YAAY;YAAAK,QAAA,eACzB9E,OAAA,CAACnB,MAAM;cACHuG,KAAK,EAAC,0BAAM;cACZX,SAAS,EAAC,iBAAiB;cAC3BQ,IAAI,EAAC,aAAa;cAClBE,OAAO,EAAElB;YAAc;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,eAGP7E,OAAA,CAACV,IAAI;MAAAwF,QAAA,eACH9E,OAAA,CAACjB,SAAS;QACRyF,KAAK,EAAEpE,KAAM;QACbqG,SAAS;QACTC,IAAI,EAAE,EAAG;QACTC,kBAAkB,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAE;QACpCC,QAAQ,EAAC,UAAU;QACnBC,aAAa;QACbC,aAAa,EAAC,MAAM;QACpBC,kBAAkB,EAAE,CAAC,UAAU,EAAE,aAAa,EAAE,WAAW,CAAE;QAC7DC,YAAY,EAAC,kDAAU;QACvBvC,SAAS,EAAC,uBAAuB;QACjCiB,aAAa,EAAEA,aAAc;QAC7BG,cAAc,EAAEA,cAAe;QAAAf,QAAA,gBAE/B9E,OAAA,CAAClB,MAAM;UACLmI,KAAK,EAAC,UAAU;UAChBC,MAAM,EAAC,0BAAM;UACbC,QAAQ;UACRrF,MAAM;UACNsF,iBAAiB,EAAC,0BAAM;UACxBC,KAAK,EAAE;YAAEC,QAAQ,EAAE;UAAQ;QAAE;UAAA5C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9B,CAAC,eACF7E,OAAA,CAAClB,MAAM;UACLmI,KAAK,EAAC,aAAa;UACnBC,MAAM,EAAC,cAAI;UACXC,QAAQ;UACRrF,MAAM;UACNsF,iBAAiB,EAAC,0BAAM;UACxBC,KAAK,EAAE;YAAEC,QAAQ,EAAE;UAAQ;QAAE;UAAA5C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9B,CAAC,eACF7E,OAAA,CAAClB,MAAM;UACLmI,KAAK,EAAC,WAAW;UACjBC,MAAM,EAAC,OAAO;UACdC,QAAQ;UACRrF,MAAM;UACNsF,iBAAiB,EAAC,mBAAS;UAC3BC,KAAK,EAAE;YAAEC,QAAQ,EAAE;UAAQ;QAAE;UAAA5C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9B,CAAC,eACF7E,OAAA,CAAClB,MAAM;UACLoI,MAAM,EAAC,0BAAM;UACbK,IAAI,EAAEnD,iBAAkB;UACxBiD,KAAK,EAAE;YAAEC,QAAQ,EAAE;UAAQ;QAAE;UAAA5C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9B,CAAC,eACF7E,OAAA,CAAClB,MAAM;UACLmI,KAAK,EAAC,WAAW;UACjBC,MAAM,EAAC,cAAI;UACXK,IAAI,EAAElC,kBAAmB;UACzB8B,QAAQ;UACRE,KAAK,EAAE;YAAEC,QAAQ,EAAE;UAAQ;QAAE;UAAA5C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9B,CAAC,eACF7E,OAAA,CAAClB,MAAM;UACLoI,MAAM,EAAC,cAAI;UACXK,IAAI,EAAEvC,kBAAmB;UACzBqC,KAAK,EAAE;YAAEC,QAAQ,EAAE;UAAQ;QAAE;UAAA5C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9B,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACO;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR,CAAC,eAGP7E,OAAA,CAAChB,MAAM;MACLkI,MAAM,EAAE,cAAclG,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEqC,QAAQ,EAAG;MAC9CmE,OAAO,EAAE1G,cAAe;MACxBuG,KAAK,EAAE;QAAEI,KAAK,EAAE;MAAQ,CAAE;MAC1BC,MAAM,EAAEA,CAAA,KAAM3G,iBAAiB,CAAC,KAAK,CAAE;MACvC4G,MAAM,eACJ3H,OAAA;QAAKyE,SAAS,EAAC,gCAAgC;QAAAK,QAAA,gBAC7C9E,OAAA,CAACnB,MAAM;UACLuG,KAAK,EAAC,cAAI;UACVH,IAAI,EAAC,aAAa;UAClBE,OAAO,EAAEA,CAAA,KAAMpE,iBAAiB,CAAC,KAAK,CAAE;UACxC0D,SAAS,EAAC;QAAe;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1B,CAAC,eACF7E,OAAA,CAACnB,MAAM;UACLuG,KAAK,EAAC,cAAI;UACVH,IAAI,EAAC,aAAa;UAClBE,OAAO,EAAE1B,mBAAoB;UAC7BgB,SAAS,EAAC;QAAkB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7B,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CACN;MAAAC,QAAA,EAEA9D,WAAW,iBACVhB,OAAA;QAAKyE,SAAS,EAAC,MAAM;QAAAK,QAAA,gBACnB9E,OAAA;UAAKyE,SAAS,EAAC,QAAQ;UAAAK,QAAA,eACrB9E,OAAA;YAAKyE,SAAS,EAAC,OAAO;YAAAK,QAAA,gBACpB9E,OAAA;cAAOyE,SAAS,EAAC,WAAW;cAAAK,QAAA,EAAC;YAAK;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAC1C7E,OAAA;cAAGyE,SAAS,EAAC,KAAK;cAAAK,QAAA,EAAE9D,WAAW,CAACsC;YAAW;cAAAoB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7C;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACN7E,OAAA;UAAKyE,SAAS,EAAC,QAAQ;UAAAK,QAAA,eACrB9E,OAAA;YAAKyE,SAAS,EAAC,OAAO;YAAAK,QAAA,gBACpB9E,OAAA;cAAO4H,OAAO,EAAC,OAAO;cAACnD,SAAS,EAAC,WAAW;cAAAK,QAAA,EAAC;YAAK;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAC1D7E,OAAA,CAACX,WAAW;cACVwI,EAAE,EAAC,OAAO;cACVrD,KAAK,EAAEtD,aAAc;cACrBqF,OAAO,EAAEhB,oBAAqB;cAC9BU,QAAQ,EAAGC,CAAC,IAAK/E,gBAAgB,CAAC+E,CAAC,CAAC1B,KAAK,CAAE;cAC3CwB,WAAW,EAAC,sCAAQ;cACpBvB,SAAS,EAAC,QAAQ;cAClBqD,OAAO,EAAC;YAAM;cAAApD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACf,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IACN;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACK,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACN,CAAC;AAEV,CAAC;AAAC3E,EAAA,CAhbID,SAAmB;EAAA,QAWGH,cAAc;AAAA;AAAAiI,EAAA,GAXpC9H,SAAmB;AAkbzB,eAAeA,SAAS;AAAC,IAAA8H,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
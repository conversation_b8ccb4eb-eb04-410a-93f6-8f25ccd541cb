{"ast": null, "code": "import{BreadCrumb}from'primereact/breadcrumb';import React from'react';import{useLocation,useNavigate}from'react-router-dom';import{ROUTES}from'../../constants/routes';import{jsx as _jsx}from\"react/jsx-runtime\";const BreadcrumbNav=_ref=>{let{customItems,className=''}=_ref;const location=useLocation();const navigate=useNavigate();// 路由到中文名稱的映射\nconst routeNameMap={[ROUTES.HOME]:'首頁',[ROUTES.DOCTORS]:'治療師列表',[ROUTES.DOCTOR_DETAIL]:'治療師詳細資料',[ROUTES.TREATMENTS]:'診療紀錄',[ROUTES.TREATMENT_DETAIL]:'診療詳情',[ROUTES.PATIENTS]:'病患列表',[ROUTES.PATIENT_DETAIL]:'病患詳情',[ROUTES.SCHEDULES]:'排班表',[ROUTES.RECEIPTS]:'收據列表',[ROUTES.RECEIPT_DETAIL]:'收據詳情',[ROUTES.USERS]:'權限管理',[ROUTES.DEBUG]:'除錯頁面',[ROUTES.BACKUP_MANAGEMENT]:'備份監控',[ROUTES.REPORT_MANAGEMENT]:'報表管理',[ROUTES.IMAGE_MANAGEMENT]:'圖片管理',[ROUTES.LOGIN_LOGS]:'登入紀錄',[ROUTES.IP_BLOCKS]:'IP封鎖'};// 生成麵包屑項目\nconst generateBreadcrumbItems=()=>{if(customItems){return customItems;}const items=[];// 首頁項目\nitems.push({label:'首頁',icon:'pi pi-home',command:()=>navigate(ROUTES.HOME)});// 如果不是首頁，添加當前頁面\nif(location.pathname!==ROUTES.HOME){const currentPath=location.pathname;let currentRouteName=routeNameMap[currentPath];// 檢查是否是詳細頁面\nif(currentPath.includes('detail')){let parentRoute='';let parentRouteName='';// 根據詳情頁面確定父級路由\nif(currentPath===ROUTES.TREATMENT_DETAIL){parentRoute=ROUTES.TREATMENTS;parentRouteName=routeNameMap[ROUTES.TREATMENTS]||'診療紀錄';}else if(currentPath===ROUTES.PATIENT_DETAIL){parentRoute=ROUTES.PATIENTS;parentRouteName=routeNameMap[ROUTES.PATIENTS]||'病患列表';}else if(currentPath===ROUTES.RECEIPT_DETAIL){parentRoute=ROUTES.RECEIPTS;parentRouteName=routeNameMap[ROUTES.RECEIPTS]||'收據列表';}else if(currentPath===ROUTES.DOCTOR_DETAIL){parentRoute=ROUTES.DOCTORS;parentRouteName=routeNameMap[ROUTES.DOCTORS]||'治療師管理';}// 添加父級路由\nif(parentRoute&&parentRouteName){items.push({label:parentRouteName,command:()=>navigate(parentRoute)});}// 如果沒有找到當前路由名稱，使用預設名稱\nif(!currentRouteName){if(currentPath===ROUTES.TREATMENT_DETAIL){currentRouteName='診療詳情';}else if(currentPath===ROUTES.PATIENT_DETAIL){currentRouteName='病患詳情';}else if(currentPath===ROUTES.RECEIPT_DETAIL){currentRouteName='收據詳情';}else if(currentPath===ROUTES.DOCTOR_DETAIL){currentRouteName='治療師詳細資料';}else{currentRouteName='詳情頁面';}}}// 如果還是沒有找到路由名稱，使用未知頁面\nif(!currentRouteName){currentRouteName='未知頁面';}items.push({label:currentRouteName});}return items;};const breadcrumbItems=generateBreadcrumbItems();// 首頁項目\nconst home={icon:'pi pi-home',command:()=>navigate(ROUTES.HOME)};return/*#__PURE__*/_jsx(\"div\",{className:\"breadcrumb-nav \".concat(className),children:/*#__PURE__*/_jsx(BreadCrumb,{model:breadcrumbItems.slice(1)// 移除首頁，因為 home prop 會處理\n,home:home,className:\"border-none bg-transparent p-0\"})});};export default BreadcrumbNav;", "map": {"version": 3, "names": ["BreadCrumb", "React", "useLocation", "useNavigate", "ROUTES", "jsx", "_jsx", "BreadcrumbNav", "_ref", "customItems", "className", "location", "navigate", "routeNameMap", "HOME", "DOCTORS", "DOCTOR_DETAIL", "TREATMENTS", "TREATMENT_DETAIL", "PATIENTS", "PATIENT_DETAIL", "SCHEDULES", "RECEIPTS", "RECEIPT_DETAIL", "USERS", "DEBUG", "BACKUP_MANAGEMENT", "REPORT_MANAGEMENT", "IMAGE_MANAGEMENT", "LOGIN_LOGS", "IP_BLOCKS", "generateBreadcrumbItems", "items", "push", "label", "icon", "command", "pathname", "currentPath", "currentRouteName", "includes", "parentRoute", "parentRouteName", "breadcrumbItems", "home", "concat", "children", "model", "slice"], "sources": ["C:/Users/<USER>/Desktop/demo-git/demo-react/my-fullstack-app/frontend/src/components/Common/BreadcrumbNav.tsx"], "sourcesContent": ["import { BreadCrumb } from 'primereact/breadcrumb';\r\nimport { MenuItem } from 'primereact/menuitem';\r\nimport React from 'react';\r\nimport { useLocation, useNavigate } from 'react-router-dom';\r\nimport { ROUTES } from '../../constants/routes';\r\n\r\ninterface BreadcrumbNavProps {\r\n  customItems?: MenuItem[];\r\n  className?: string;\r\n}\r\n\r\nconst BreadcrumbNav: React.FC<BreadcrumbNavProps> = ({ \r\n  customItems, \r\n  className = '' \r\n}) => {\r\n  const location = useLocation();\r\n  const navigate = useNavigate();\r\n\r\n  // 路由到中文名稱的映射\r\n  const routeNameMap: Record<string, string> = {\r\n    [ROUTES.HOME]: '首頁',\r\n    [ROUTES.DOCTORS]: '治療師列表',\r\n    [ROUTES.DOCTOR_DETAIL]: '治療師詳細資料',\r\n    [ROUTES.TREATMENTS]: '診療紀錄',\r\n    [ROUTES.TREATMENT_DETAIL]: '診療詳情',\r\n    [ROUTES.PATIENTS]: '病患列表',\r\n    [ROUTES.PATIENT_DETAIL]: '病患詳情',\r\n    [ROUTES.SCHEDULES]: '排班表',\r\n    [ROUTES.RECEIPTS]: '收據列表',\r\n    [ROUTES.RECEIPT_DETAIL]: '收據詳情',\r\n    [ROUTES.USERS]: '權限管理',\r\n    [ROUTES.DEBUG]: '除錯頁面',\r\n    [ROUTES.BACKUP_MANAGEMENT]: '備份監控',\r\n    [ROUTES.REPORT_MANAGEMENT]: '報表管理',\r\n    [ROUTES.IMAGE_MANAGEMENT]: '圖片管理',\r\n    [ROUTES.LOGIN_LOGS]: '登入紀錄',\r\n    [ROUTES.IP_BLOCKS]: 'IP封鎖',\r\n  };\r\n\r\n  // 生成麵包屑項目\r\n  const generateBreadcrumbItems = (): MenuItem[] => {\r\n    if (customItems) {\r\n      return customItems;\r\n    }\r\n\r\n    const items: MenuItem[] = [];\r\n\r\n    // 首頁項目\r\n    items.push({\r\n      label: '首頁',\r\n      icon: 'pi pi-home',\r\n      command: () => navigate(ROUTES.HOME),\r\n    });\r\n\r\n    // 如果不是首頁，添加當前頁面\r\n    if (location.pathname !== ROUTES.HOME) {\r\n      const currentPath = location.pathname;\r\n      let currentRouteName = routeNameMap[currentPath];\r\n\r\n      // 檢查是否是詳細頁面\r\n      if (currentPath.includes('detail')) {\r\n        let parentRoute = '';\r\n        let parentRouteName = '';\r\n\r\n        // 根據詳情頁面確定父級路由\r\n        if (currentPath === ROUTES.TREATMENT_DETAIL) {\r\n          parentRoute = ROUTES.TREATMENTS;\r\n          parentRouteName = routeNameMap[ROUTES.TREATMENTS] || '診療紀錄';\r\n        } else if (currentPath === ROUTES.PATIENT_DETAIL) {\r\n          parentRoute = ROUTES.PATIENTS;\r\n          parentRouteName = routeNameMap[ROUTES.PATIENTS] || '病患列表';\r\n        } else if (currentPath === ROUTES.RECEIPT_DETAIL) {\r\n          parentRoute = ROUTES.RECEIPTS;\r\n          parentRouteName = routeNameMap[ROUTES.RECEIPTS] || '收據列表';\r\n        } else if (currentPath === ROUTES.DOCTOR_DETAIL) {\r\n          parentRoute = ROUTES.DOCTORS;\r\n          parentRouteName = routeNameMap[ROUTES.DOCTORS] || '治療師管理';\r\n        }\r\n\r\n        // 添加父級路由\r\n        if (parentRoute && parentRouteName) {\r\n          items.push({\r\n            label: parentRouteName,\r\n            command: () => navigate(parentRoute),\r\n          });\r\n        }\r\n\r\n        // 如果沒有找到當前路由名稱，使用預設名稱\r\n        if (!currentRouteName) {\r\n          if (currentPath === ROUTES.TREATMENT_DETAIL) {\r\n            currentRouteName = '診療詳情';\r\n          } else if (currentPath === ROUTES.PATIENT_DETAIL) {\r\n            currentRouteName = '病患詳情';\r\n          } else if (currentPath === ROUTES.RECEIPT_DETAIL) {\r\n            currentRouteName = '收據詳情';\r\n          } else if (currentPath === ROUTES.DOCTOR_DETAIL) {\r\n            currentRouteName = '治療師詳細資料';\r\n          } else {\r\n            currentRouteName = '詳情頁面';\r\n          }\r\n        }\r\n      }\r\n\r\n      // 如果還是沒有找到路由名稱，使用未知頁面\r\n      if (!currentRouteName) {\r\n        currentRouteName = '未知頁面';\r\n      }\r\n\r\n      items.push({\r\n        label: currentRouteName,\r\n      });\r\n    }\r\n\r\n    return items;\r\n  };\r\n\r\n  const breadcrumbItems = generateBreadcrumbItems();\r\n\r\n  // 首頁項目\r\n  const home: MenuItem = {\r\n    icon: 'pi pi-home',\r\n    command: () => navigate(ROUTES.HOME),\r\n  };\r\n\r\n  return (\r\n    <div className={`breadcrumb-nav ${className}`}>\r\n      <BreadCrumb \r\n        model={breadcrumbItems.slice(1)} // 移除首頁，因為 home prop 會處理\r\n        home={home}\r\n        className=\"border-none bg-transparent p-0\"\r\n      />\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default BreadcrumbNav;\r\n"], "mappings": "AAAA,OAASA,UAAU,KAAQ,uBAAuB,CAElD,MAAO,CAAAC,KAAK,KAAM,OAAO,CACzB,OAASC,WAAW,CAAEC,WAAW,KAAQ,kBAAkB,CAC3D,OAASC,MAAM,KAAQ,wBAAwB,CAAC,OAAAC,GAAA,IAAAC,IAAA,yBAOhD,KAAM,CAAAC,aAA2C,CAAGC,IAAA,EAG9C,IAH+C,CACnDC,WAAW,CACXC,SAAS,CAAG,EACd,CAAC,CAAAF,IAAA,CACC,KAAM,CAAAG,QAAQ,CAAGT,WAAW,CAAC,CAAC,CAC9B,KAAM,CAAAU,QAAQ,CAAGT,WAAW,CAAC,CAAC,CAE9B;AACA,KAAM,CAAAU,YAAoC,CAAG,CAC3C,CAACT,MAAM,CAACU,IAAI,EAAG,IAAI,CACnB,CAACV,MAAM,CAACW,OAAO,EAAG,OAAO,CACzB,CAACX,MAAM,CAACY,aAAa,EAAG,SAAS,CACjC,CAACZ,MAAM,CAACa,UAAU,EAAG,MAAM,CAC3B,CAACb,MAAM,CAACc,gBAAgB,EAAG,MAAM,CACjC,CAACd,MAAM,CAACe,QAAQ,EAAG,MAAM,CACzB,CAACf,MAAM,CAACgB,cAAc,EAAG,MAAM,CAC/B,CAAChB,MAAM,CAACiB,SAAS,EAAG,KAAK,CACzB,CAACjB,MAAM,CAACkB,QAAQ,EAAG,MAAM,CACzB,CAAClB,MAAM,CAACmB,cAAc,EAAG,MAAM,CAC/B,CAACnB,MAAM,CAACoB,KAAK,EAAG,MAAM,CACtB,CAACpB,MAAM,CAACqB,KAAK,EAAG,MAAM,CACtB,CAACrB,MAAM,CAACsB,iBAAiB,EAAG,MAAM,CAClC,CAACtB,MAAM,CAACuB,iBAAiB,EAAG,MAAM,CAClC,CAACvB,MAAM,CAACwB,gBAAgB,EAAG,MAAM,CACjC,CAACxB,MAAM,CAACyB,UAAU,EAAG,MAAM,CAC3B,CAACzB,MAAM,CAAC0B,SAAS,EAAG,MACtB,CAAC,CAED;AACA,KAAM,CAAAC,uBAAuB,CAAGA,CAAA,GAAkB,CAChD,GAAItB,WAAW,CAAE,CACf,MAAO,CAAAA,WAAW,CACpB,CAEA,KAAM,CAAAuB,KAAiB,CAAG,EAAE,CAE5B;AACAA,KAAK,CAACC,IAAI,CAAC,CACTC,KAAK,CAAE,IAAI,CACXC,IAAI,CAAE,YAAY,CAClBC,OAAO,CAAEA,CAAA,GAAMxB,QAAQ,CAACR,MAAM,CAACU,IAAI,CACrC,CAAC,CAAC,CAEF;AACA,GAAIH,QAAQ,CAAC0B,QAAQ,GAAKjC,MAAM,CAACU,IAAI,CAAE,CACrC,KAAM,CAAAwB,WAAW,CAAG3B,QAAQ,CAAC0B,QAAQ,CACrC,GAAI,CAAAE,gBAAgB,CAAG1B,YAAY,CAACyB,WAAW,CAAC,CAEhD;AACA,GAAIA,WAAW,CAACE,QAAQ,CAAC,QAAQ,CAAC,CAAE,CAClC,GAAI,CAAAC,WAAW,CAAG,EAAE,CACpB,GAAI,CAAAC,eAAe,CAAG,EAAE,CAExB;AACA,GAAIJ,WAAW,GAAKlC,MAAM,CAACc,gBAAgB,CAAE,CAC3CuB,WAAW,CAAGrC,MAAM,CAACa,UAAU,CAC/ByB,eAAe,CAAG7B,YAAY,CAACT,MAAM,CAACa,UAAU,CAAC,EAAI,MAAM,CAC7D,CAAC,IAAM,IAAIqB,WAAW,GAAKlC,MAAM,CAACgB,cAAc,CAAE,CAChDqB,WAAW,CAAGrC,MAAM,CAACe,QAAQ,CAC7BuB,eAAe,CAAG7B,YAAY,CAACT,MAAM,CAACe,QAAQ,CAAC,EAAI,MAAM,CAC3D,CAAC,IAAM,IAAImB,WAAW,GAAKlC,MAAM,CAACmB,cAAc,CAAE,CAChDkB,WAAW,CAAGrC,MAAM,CAACkB,QAAQ,CAC7BoB,eAAe,CAAG7B,YAAY,CAACT,MAAM,CAACkB,QAAQ,CAAC,EAAI,MAAM,CAC3D,CAAC,IAAM,IAAIgB,WAAW,GAAKlC,MAAM,CAACY,aAAa,CAAE,CAC/CyB,WAAW,CAAGrC,MAAM,CAACW,OAAO,CAC5B2B,eAAe,CAAG7B,YAAY,CAACT,MAAM,CAACW,OAAO,CAAC,EAAI,OAAO,CAC3D,CAEA;AACA,GAAI0B,WAAW,EAAIC,eAAe,CAAE,CAClCV,KAAK,CAACC,IAAI,CAAC,CACTC,KAAK,CAAEQ,eAAe,CACtBN,OAAO,CAAEA,CAAA,GAAMxB,QAAQ,CAAC6B,WAAW,CACrC,CAAC,CAAC,CACJ,CAEA;AACA,GAAI,CAACF,gBAAgB,CAAE,CACrB,GAAID,WAAW,GAAKlC,MAAM,CAACc,gBAAgB,CAAE,CAC3CqB,gBAAgB,CAAG,MAAM,CAC3B,CAAC,IAAM,IAAID,WAAW,GAAKlC,MAAM,CAACgB,cAAc,CAAE,CAChDmB,gBAAgB,CAAG,MAAM,CAC3B,CAAC,IAAM,IAAID,WAAW,GAAKlC,MAAM,CAACmB,cAAc,CAAE,CAChDgB,gBAAgB,CAAG,MAAM,CAC3B,CAAC,IAAM,IAAID,WAAW,GAAKlC,MAAM,CAACY,aAAa,CAAE,CAC/CuB,gBAAgB,CAAG,SAAS,CAC9B,CAAC,IAAM,CACLA,gBAAgB,CAAG,MAAM,CAC3B,CACF,CACF,CAEA;AACA,GAAI,CAACA,gBAAgB,CAAE,CACrBA,gBAAgB,CAAG,MAAM,CAC3B,CAEAP,KAAK,CAACC,IAAI,CAAC,CACTC,KAAK,CAAEK,gBACT,CAAC,CAAC,CACJ,CAEA,MAAO,CAAAP,KAAK,CACd,CAAC,CAED,KAAM,CAAAW,eAAe,CAAGZ,uBAAuB,CAAC,CAAC,CAEjD;AACA,KAAM,CAAAa,IAAc,CAAG,CACrBT,IAAI,CAAE,YAAY,CAClBC,OAAO,CAAEA,CAAA,GAAMxB,QAAQ,CAACR,MAAM,CAACU,IAAI,CACrC,CAAC,CAED,mBACER,IAAA,QAAKI,SAAS,mBAAAmC,MAAA,CAAoBnC,SAAS,CAAG,CAAAoC,QAAA,cAC5CxC,IAAA,CAACN,UAAU,EACT+C,KAAK,CAAEJ,eAAe,CAACK,KAAK,CAAC,CAAC,CAAG;AAAA,CACjCJ,IAAI,CAAEA,IAAK,CACXlC,SAAS,CAAC,gCAAgC,CAC3C,CAAC,CACC,CAAC,CAEV,CAAC,CAED,cAAe,CAAAH,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
import { formatUtcToTaipei } from "../../utils/dateUtils";
import { Button } from 'primereact/button';
import { Calendar } from 'primereact/calendar';
import { Column } from 'primereact/column';
import { ConfirmDialog, confirmDialog } from 'primereact/confirmdialog';
import { DataTable } from 'primereact/datatable';
import { Dropdown, DropdownChangeEvent } from 'primereact/dropdown';
import { InputText } from "primereact/inputtext";
import LoadingSpinner from '../Common/LoadingSpinner';
import { Tag } from 'primereact/tag';
import { Toast } from "primereact/toast";
import React, { useEffect, useRef, useState } from 'react';
import { useNavigate } from "react-router-dom";
import { ROUTES } from "../../constants/routes";
import useTreatment from '../../hooks/useTreatment';
import api from "../../services/api";
import { Card } from 'primereact/card';

interface Doctor {
  Id: number;
  Name: string;
}

const TreatmentsPage: React.FC = () => {
    const navigate = useNavigate();
    const toast = useRef<Toast>(null);
    const [name, setName] = useState('');
    const [nationalId, setNationalId] = useState('');
    const [doctortId, setDoctortId] = useState<number | null | undefined>(undefined);
    const [starttime, setStarttime] = useState<Date | null | undefined>(undefined);
    const [endtime, setEndtime] = useState<Date | null | undefined>(undefined);
    const [refreshKey, setRefreshKey] = useState(0);
    const [deletedFlag, setDeletedFlag] = useState(false);

    const [doctors, setDoctors] = useState<Doctor[]>([]);
    const [doctorsLoading, setDoctorsLoading] = useState(true);

    const [searchParams, setSearchParams] = useState({
        name: '',
        nationalId: '',
        doctortId: null as number | null | undefined,
        starttime: null as Date | null | undefined,
        endtime: null as Date | null | undefined,
        refreshKey: 0,
    });

    const { treatments, loading } = useTreatment({
        patientname: searchParams.name,
        nationalId: searchParams.nationalId,
        doctortid: searchParams.doctortId || null,
        startTime: searchParams.starttime || null,
        endTime: searchParams.endtime || null,
        refreshKey
    });

    const getStep = (status: string) => {
        switch (status) {
            case '10':
                return 'info';

            case '20':
                return 'warning';

            case '30':
                return 'danger';

            case '40':
                return 'success';
            default: 
                return null; 
        }
    };

    const genderdict: { [key: string]: string } = {
        "1": "男性",
        "2": "女性",
        "3": "其他"
    };

    const stepdict: { [key: string]: string } = {
        "10": "新案",
        "20": "治療",
        "30": "上傳",
        "40": "結案",
        "50": "收據"
    };
    
    // 載入治療師數據
    useEffect(() => {
        const loadDoctors = async () => {
            try {
                setDoctorsLoading(true);
                console.log('開始載入治療師數據...');

                const response = await api.get('/api/users/DoctorList');
                console.log('治療師數據:', response.data);

                // 解析 JSON 字符串（因為後端返回的是序列化的 JSON）
                const doctorsData = typeof response.data === 'string'
                    ? JSON.parse(response.data)
                    : response.data;

                setDoctors(doctorsData);

            } catch (error: any) {
                console.error('載入治療師數據失敗:', error);
                toast.current?.show({
                    severity: "error",
                    summary: "錯誤",
                    detail: error.details
                });
            } finally {
                setDoctorsLoading(false);
            }
        };

        loadDoctors();
    }, []);

    useEffect(() => {
        if (deletedFlag && !loading) {
            toast.current?.show({ severity: "success", summary: "成功", detail: "病患診療紀錄已刪除" });
            setDeletedFlag(false); // 重置
        }
    }, [deletedFlag, loading]);

    const handleSearchClick = () => {
        setRefreshKey(refreshKey + 1)
        setSearchParams({ name, nationalId, doctortId, starttime, endtime, refreshKey});
    };

    const handleDelete = async (orderNo:string) => {
        try {
            await api.get("/api/treatment/Delete",  {
                    params: { 
                        orderNo: orderNo
                    }
                }
            );
            setDeletedFlag(true);
            Reload();
        } catch (error:any) {
            var detail =  error.status === 403 ? "您無權限，請通知管理員" : error.response?.data?.message || '刪除失敗';
            toast.current?.show({ severity: "error", summary: "錯誤", detail: detail });
        }
    };

    const Reload = () => {
        // 重新觸發 usePatient，等於重新查詢
        setRefreshKey(prev => prev + 1);
    }

    const Receipt_detail = async (rowdata: any) => {
        navigate(ROUTES.RECEIPT_DETAIL, { 
            state: { 
                treatment: { 
                    receiptUrl: rowdata.receiptUrl, 
                    id: rowdata.id, 
                    patientId: rowdata.patientId
                } 
            } 
        }) 
    }

    const Edit = async (id: string, patientId: string) => {
        try {
            const Response = await api.get('/api/treatment/', {
                params: {
                    Id: id
                }
            });
    
            const Data = Response.data;
            
            if (Data) {
                navigate(ROUTES.TREATMENT_DETAIL, { state: { treatment: Data, patient: { id: patientId} } })
            }
        } catch (error:any) {
            var detail =  error.status === 403 ? "您無權限，請通知管理員" : error.response?.data?.message || '編輯失敗';
            toast.current?.show({ severity: "error", summary: "錯誤", detail: detail });
        }
    }

    const paginatorLeft = (
        <Button
            type="button"
            icon="pi pi-refresh"
            text
            onClick={() => Reload()}
        />
    );
    const paginatorRight = <div></div>;
    const optionBodyTemplate = (rowData: any) => {
        return (
            <div className="flex gap-1">
                <Button 
                    label="編輯" 
                    type="button" 
                    icon="pi pi-file-edit" 
                    onClick={() => Edit(rowData.id, rowData.patientId)}
                    size="small" 
                    severity="info" 
                    style={{ fontSize: '1rem', margin: '3px' }} 
                />
                <Button 
                    label="收據" 
                    type="button" 
                    icon="pi pi-clipboard" 
                    onClick={() => Receipt_detail(rowData)}
                    size="small" 
                    severity="success" 
                    style={{ fontSize: '1rem', margin: '3px' }}
                    disabled={ rowData.step === 40 || rowData.step === 50 ? false : true}
                />
                <Button 
                    label="刪除" 
                    type="button" 
                    icon="pi pi-file-excel" 
                    onClick={()=> confirm(rowData.orderNo)} 
                    size="small" 
                    severity="danger" 
                    style={{  fontSize: '1rem', margin: '3px' }} 
                    disabled={ rowData.step === 40 || rowData.step === 50 ? true : false}
                />
            </div>
        );
    };

    const confirm = (Id:string) => {
        confirmDialog({
            message: '確定要刪除這筆資料嗎？',
            header: '刪除確認',
            icon: 'pi pi-exclamation-triangle',
            defaultFocus: 'reject',
            acceptClassName: 'p-button-danger',
            acceptLabel: '確定',
            rejectLabel: '取消',
            accept: () => handleDelete(Id),
        });
    };

    const stepBodyTemplate = (rowData: any) => {
        return (
            <div>
                <Tag value={stepdict[String(rowData.step)]} severity={getStep(String(rowData.step))} />
            </div>
        );
    };

    const genderBodyTemplate = (rowData: any) => {
        var data = String(rowData.patientGender)
        const gendar = genderdict[data]
            return (
                <div>
                    {gendar}
                </div>
            );
        };

    const formatDate = (value: string) => {
    if (!value) return '';
    return formatUtcToTaipei(value, "yyyy/MM/dd HH:mm:ss");
  };

    const formatAge = (value: string) => {
        if (!value) return "";
        const date = new Date(value);
        const today = new Date();
        let age = today.getFullYear() - date.getFullYear();

        const hasNotHadBirthdayThisYear =
            today.getMonth() < date.getMonth() ||
            (today.getMonth() === date.getMonth() && today.getDate() < date.getDate());

        if (hasNotHadBirthdayThisYear) {
            age--;
        }

        return age;
        
    };

    if (loading) {
        return <LoadingSpinner message="載入診療資料中..." />;
    }

    return (
        <div>
            <Toast ref={toast} />
            <ConfirmDialog />
            <Card title="診療紀錄" className="mb-4">
                <p className="text-600 line-height-3 m-0">
                    病患的診療紀錄，可以查詢、編輯、刪除診療資料，結案的診療資料可以編輯收據並製作報表。
                </p>
            </Card>

            {/* 搜尋條件 */}
            <Card className="mb-4">
                <div className="grid">
                    <div className="col-12 md:col-4">
                        <InputText
                            id="name"
                            type="text"
                            value={name}
                            onChange={(e) => setName(e.target.value)}
                            placeholder="病患姓名" 
                            className="w-full"
                        />
                    </div>
                    <div className="col-6 md:col-4">
                        <Dropdown
                            value={doctortId}
                            onChange={(e: DropdownChangeEvent) =>  setDoctortId(e.value)}
                            options={doctors}
                            optionLabel="Name"
                            optionValue="Id"
                            placeholder="請選擇治療師"
                            disabled={doctorsLoading}
                            className="w-full"
                            showClear />
                    </div>
                    <div className="col-6 md:col-4">
                        <InputText
                            id="nationalId"
                            type="text"
                            value={nationalId}
                            onChange={(e) => setNationalId(e.target.value)}
                            placeholder="病患身分證"
                            className="w-full"
                        />
                    </div>
                    
                    <div className="col-6 md:col-3">
                        <Calendar 
                            id="starttime" 
                            value={starttime} 
                            onChange={(e) => setStarttime(e.value)} 
                            placeholder="開始時間"
                            className="w-full"
                            dateFormat="yy/mm/dd"
                            showIcon/>
                    </div>
                    <div className="col-6 md:col-3">
                        <Calendar 
                            id="endtime" 
                            value={endtime} 
                            onChange={(e) => setEndtime(e.value)} 
                            placeholder="結束時間"
                            className="w-full"
                            dateFormat="yy/mm/dd"
                            showIcon/>
                    </div>
                    <div className="col-12 md:col-4">
                        <div className="flex gap-2">
                            <Button label="查詢" icon="pi pi-search" onClick={handleSearchClick}/>
                        </div>
                    </div>
                </div>
            </Card>

            <Card>
                <DataTable
                    value={treatments}
                    paginator
                    rows={10}
                    rowsPerPageOptions={[10, 20, 30, 40]}
                    tableStyle={{ minWidth: '50rem' }}
                    emptyMessage="沒有找到診療資料"
                    paginatorLeft={paginatorLeft}
                    paginatorRight={paginatorRight}
                >
                    <Column field="orderNo" header="案號" style={{ width: '5%' }} />
                    <Column field="patientName" header="病患姓名" style={{ width: '5%' }} />
                    <Column field="doctorName" header="治療治療師" style={{ width: '5%' }} />
                    <Column field="patientGender" header="性別" style={{ width: '3%' }} body={genderBodyTemplate}/>
                    <Column field="patientBirthDate" header="年齡" style={{ width: '3%' }}  body={(rowData) => formatAge(rowData.patientBirthDate)}/>
                    <Column field="step" header="階段" style={{ width: '3%' }} body={stepBodyTemplate}/>
                    <Column field="createdAt" header="新增日期" style={{ width: '8%' }} body={(rowData) => formatDate(rowData.createdAt)} />
                    <Column field="updatedAt" header="更新日期" style={{ width: '8%' }} body={(rowData) => formatDate(rowData.updatedAt)}/>
                    <Column field="operatorUserName" header="操作人" style={{ width: '5%' }} />
                    <Column field="option" header="功能" style={{ width: '12%' }} body={optionBodyTemplate} />
                </DataTable>
            </Card>
        </div>
    );
};

export default TreatmentsPage;
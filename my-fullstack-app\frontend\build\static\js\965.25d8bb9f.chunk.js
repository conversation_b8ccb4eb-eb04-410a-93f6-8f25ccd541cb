"use strict";(self.webpackChunkfrontend=self.webpackChunkfrontend||[]).push([[965],{965:(e,t,n)=>{n.r(t),n.d(t,{default:()=>p});var r=n(5043),l=n(8060),o=n(4972),a=n(2018),i=n(828),u=n(8150),c=n(9304),s=n(402),d=n(579);const p=()=>{const e=(0,r.useRef)(null),[t,n]=(0,r.useState)([]),[p,f]=(0,r.useState)(null),[m,v]=(0,r.useState)([]),[h,b]=(0,r.useState)([]),[y,g]=(0,r.useState)(!1),x=(0,r.useCallback)((async()=>{try{const e=await s.u.get("/api/users/GetRoles");n(e.map((e=>({id:e.id,name:e.name}))))}catch(o){var t,r,l;null===(t=e.current)||void 0===t||t.show({severity:"error",summary:"\u7372\u53d6\u89d2\u8272\u5217\u8868\u5931\u6557",detail:(null===(r=o.response)||void 0===r||null===(l=r.data)||void 0===l?void 0:l.message)||o.message})}}),[]),j=(0,r.useCallback)((async()=>{try{const e=await c.A8.getAllPermissions();v(e)}catch(l){var t,n,r;null===(t=e.current)||void 0===t||t.show({severity:"error",summary:"\u7372\u53d6\u6240\u6709\u6b0a\u9650\u5931\u6557",detail:(null===(n=l.response)||void 0===n||null===(r=n.data)||void 0===r?void 0:r.message)||l.message})}}),[]),k=(0,r.useCallback)((async t=>{g(!0);try{const e=await c.A8.getRolePermissions(t);b(e)}catch(o){var n,r,l;null===(n=e.current)||void 0===n||n.show({severity:"error",summary:"\u7372\u53d6\u89d2\u8272\u6b0a\u9650\u5931\u6557",detail:(null===(r=o.response)||void 0===r||null===(l=r.data)||void 0===l?void 0:l.message)||o.message}),b([])}finally{g(!1)}}),[]);(0,r.useEffect)((()=>{x(),j()}),[x,j]),(0,r.useEffect)((()=>{p?k(p):b([])}),[p,k]);const O=m.reduce(((e,t)=>{const n=t.Category||"\u672a\u5206\u985e";return e[n]||(e[n]=[]),e[n].push(t),e}),{});return(0,d.jsxs)("div",{className:"p-grid p-fluid",children:[(0,d.jsx)(i.y,{ref:e}),(0,d.jsx)("div",{className:"p-col-12",children:(0,d.jsxs)(u.Z,{title:"\u6b0a\u9650\u7ba1\u7406",children:[(0,d.jsxs)("div",{className:"p-field p-grid mb-4",children:[(0,d.jsx)("label",{htmlFor:"role",className:"p-col-12 p-md-2",children:"\u9078\u64c7\u89d2\u8272"}),(0,d.jsx)("div",{className:"p-col-12 p-md-10",children:(0,d.jsx)(l.m,{id:"role",value:p,options:t,onChange:e=>f(e.value),optionLabel:"name",optionValue:"id",placeholder:"\u8acb\u9078\u64c7\u4e00\u500b\u89d2\u8272",style:{width:"100%"}})})]}),p&&Object.keys(O).map((e=>(0,d.jsxs)("div",{className:"p-field mb-4",children:[(0,d.jsx)("h5",{className:"text-lg font-bold mb-2",children:e}),(0,d.jsx)("div",{className:"p-grid",children:(O[e]||[]).map((e=>(0,d.jsxs)("div",{className:"p-col-12 p-md-4",children:[(0,d.jsx)(o.S,{inputId:e.Code,checked:h.includes(e.Code),onChange:t=>{return n=e.Code,r=!!t.checked,void b((e=>r?[...e,n]:e.filter((e=>e!==n))));var n,r}}),(0,d.jsxs)("label",{htmlFor:e.Code,className:"p-ml-2",children:[e.Name," (",e.Code,")"]})]},e.Code)))})]},e))),(0,d.jsx)("div",{className:"p-d-flex p-jc-end mt-4",children:(0,d.jsx)(a.$,{label:"\u5132\u5b58\u8b8a\u66f4",icon:"pi pi-check",onClick:async()=>{if(p){g(!0);try{var t;await c.A8.updateRolePermissions(p,h),null===(t=e.current)||void 0===t||t.show({severity:"success",summary:"\u6b0a\u9650\u66f4\u65b0\u6210\u529f",detail:""}),k(p)}catch(o){var n,r,l;null===(n=e.current)||void 0===n||n.show({severity:"error",summary:"\u6b0a\u9650\u66f4\u65b0\u5931\u6557",detail:(null===(r=o.response)||void 0===r||null===(l=r.data)||void 0===l?void 0:l.message)||o.message})}finally{g(!1)}}},disabled:!p||y,loading:y})})]})})]})}},4972:(e,t,n)=>{n.d(t,{S:()=>x});var r=n(5043),l=n(4052),o=n(1828),a=n(2028),i=n(2897),u=n(1356),c=n(4504);function s(){return s=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)({}).hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},s.apply(null,arguments)}function d(e){return d="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},d(e)}function p(e){var t=function(e,t){if("object"!=d(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!=d(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==d(t)?t:t+""}function f(e,t,n){return(t=p(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function m(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=Array(t);n<t;n++)r[n]=e[n];return r}function v(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,l,o,a,i=[],u=!0,c=!1;try{if(o=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;u=!1}else for(;!(u=(r=o.call(n)).done)&&(i.push(r.value),i.length!==t);u=!0);}catch(e){c=!0,l=e}finally{try{if(!u&&null!=n.return&&(a=n.return(),Object(a)!==a))return}finally{if(c)throw l}}return i}}(e,t)||function(e,t){if(e){if("string"==typeof e)return m(e,t);var n={}.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?m(e,t):void 0}}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}var h={box:"p-checkbox-box",input:"p-checkbox-input",icon:"p-checkbox-icon",root:function(e){var t=e.props,n=e.checked,r=e.context;return(0,c.xW)("p-checkbox p-component",{"p-highlight":n,"p-disabled":t.disabled,"p-invalid":t.invalid,"p-variant-filled":t.variant?"filled"===t.variant:r&&"filled"===r.inputStyle})}},b=o.x.extend({defaultProps:{__TYPE:"Checkbox",autoFocus:!1,checked:!1,className:null,disabled:!1,falseValue:!1,icon:null,id:null,inputId:null,inputRef:null,invalid:!1,variant:null,name:null,onChange:null,onContextMenu:null,onMouseDown:null,readOnly:!1,required:!1,style:null,tabIndex:null,tooltip:null,tooltipOptions:null,trueValue:!0,value:null,children:void 0},css:{classes:h}});function y(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function g(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?y(Object(n),!0).forEach((function(t){f(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):y(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var x=r.memo(r.forwardRef((function(e,t){var n=(0,a.qV)(),d=r.useContext(l.UM),p=b.getProps(e,d),f=v(r.useState(!1),2),m=f[0],h=f[1],y=b.setMetaData({props:p,state:{focused:m},context:{checked:p.checked===p.trueValue,disabled:p.disabled}}),x=y.ptm,j=y.cx,k=y.isUnstyled;(0,o.j)(b.css.styles,k,{name:"checkbox"});var O=r.useRef(null),w=r.useRef(p.inputRef),C=function(){return p.checked===p.trueValue};r.useImperativeHandle(t,(function(){return{props:p,focus:function(){return c.DV.focus(w.current)},getElement:function(){return O.current},getInput:function(){return w.current}}})),r.useEffect((function(){c.BF.combinedRefs(w,p.inputRef)}),[w,p.inputRef]),(0,a.w5)((function(){w.current.checked=C()}),[p.checked,p.trueValue]),(0,a.uU)((function(){p.autoFocus&&c.DV.focus(w.current,p.autoFocus)}));var S=C(),P=c.BF.isNotEmpty(p.tooltip),N=b.getOtherProps(p),E=n({id:p.id,className:(0,c.xW)(p.className,j("root",{checked:S,context:d})),style:p.style,"data-p-highlight":S,"data-p-disabled":p.disabled,onContextMenu:p.onContextMenu,onMouseDown:p.onMouseDown},N,x("root"));return r.createElement(r.Fragment,null,r.createElement("div",s({ref:O},E),function(){var e=c.BF.reduceKeys(N,c.DV.ARIA_PROPS),t=n(g({id:p.inputId,type:"checkbox",className:j("input"),name:p.name,tabIndex:p.tabIndex,onFocus:function(e){return function(e){var t;h(!0),null===p||void 0===p||null===(t=p.onFocus)||void 0===t||t.call(p,e)}(e)},onBlur:function(e){return function(e){var t;h(!1),null===p||void 0===p||null===(t=p.onBlur)||void 0===t||t.call(p,e)}(e)},onChange:function(e){return function(e){if(!p.disabled&&!p.readOnly&&p.onChange){var t,n=C()?p.falseValue:p.trueValue,r={originalEvent:e,value:p.value,checked:n,stopPropagation:function(){null===e||void 0===e||e.stopPropagation()},preventDefault:function(){null===e||void 0===e||e.preventDefault()},target:{type:"checkbox",name:p.name,id:p.id,value:p.value,checked:n}};if(null===p||void 0===p||null===(t=p.onChange)||void 0===t||t.call(p,r),e.defaultPrevented)return;c.DV.focus(w.current)}}(e)},disabled:p.disabled,readOnly:p.readOnly,required:p.required,"aria-invalid":p.invalid,checked:S},e),x("input"));return r.createElement("input",s({ref:w},t))}(),function(){var e=n({className:j("icon")},x("icon")),t=n({className:j("box",{checked:S}),"data-p-highlight":S,"data-p-disabled":p.disabled},x("box")),l=S?p.icon||r.createElement(i.S,e):null,o=c.Hj.getJSXIcon(l,g({},e),{props:p,checked:S});return r.createElement("div",t,o)}()),P&&r.createElement(u.m,s({target:O,content:p.tooltip,pt:x("tooltip")},p.tooltipOptions)))})));x.displayName="Checkbox"}}]);
//# sourceMappingURL=965.25d8bb9f.chunk.js.map
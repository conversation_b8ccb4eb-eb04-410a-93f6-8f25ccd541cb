{"ast": null, "code": "import { formatRelative } from \"./en-US/_lib/formatRelative.js\";\nimport { localize } from \"./en-US/_lib/localize.js\";\nimport { match } from \"./en-US/_lib/match.js\";\nimport { formatDistance } from \"./en-CA/_lib/formatDistance.js\";\nimport { formatLong } from \"./en-CA/_lib/formatLong.js\";\n\n/**\n * @category Locales\n * @summary English locale (Canada).\n * @language English\n * @iso-639-2 eng\n * <AUTHOR> [@markowsiak](https://github.com/markowsiak)\n * <AUTHOR> [@mimperatore](https://github.com/mimperatore)\n */\nexport const enCA = {\n  code: \"en-CA\",\n  formatDistance: formatDistance,\n  formatLong: formatLong,\n  formatRelative: formatRelative,\n  localize: localize,\n  match: match,\n  options: {\n    weekStartsOn: 0 /* Sunday */,\n    firstWeekContainsDate: 1\n  }\n};\n\n// Fallback for modularized imports:\nexport default enCA;", "map": {"version": 3, "names": ["formatRelative", "localize", "match", "formatDistance", "formatLong", "enCA", "code", "options", "weekStartsOn", "firstWeekContainsDate"], "sources": ["C:/Users/<USER>/Desktop/demo-git/demo-react/my-fullstack-app/frontend/node_modules/date-fns/locale/en-CA.js"], "sourcesContent": ["import { formatRelative } from \"./en-US/_lib/formatRelative.js\";\nimport { localize } from \"./en-US/_lib/localize.js\";\nimport { match } from \"./en-US/_lib/match.js\";\n\nimport { formatDistance } from \"./en-CA/_lib/formatDistance.js\";\nimport { formatLong } from \"./en-CA/_lib/formatLong.js\";\n\n/**\n * @category Locales\n * @summary English locale (Canada).\n * @language English\n * @iso-639-2 eng\n * <AUTHOR> [@markowsiak](https://github.com/markowsiak)\n * <AUTHOR> [@mimperatore](https://github.com/mimperatore)\n */\nexport const enCA = {\n  code: \"en-CA\",\n  formatDistance: formatDistance,\n  formatLong: formatLong,\n  formatRelative: formatRelative,\n  localize: localize,\n  match: match,\n  options: {\n    weekStartsOn: 0 /* Sunday */,\n    firstWeekContainsDate: 1,\n  },\n};\n\n// Fallback for modularized imports:\nexport default enCA;\n"], "mappings": "AAAA,SAASA,cAAc,QAAQ,gCAAgC;AAC/D,SAASC,QAAQ,QAAQ,0BAA0B;AACnD,SAASC,KAAK,QAAQ,uBAAuB;AAE7C,SAASC,cAAc,QAAQ,gCAAgC;AAC/D,SAASC,UAAU,QAAQ,4BAA4B;;AAEvD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMC,IAAI,GAAG;EAClBC,IAAI,EAAE,OAAO;EACbH,cAAc,EAAEA,cAAc;EAC9BC,UAAU,EAAEA,UAAU;EACtBJ,cAAc,EAAEA,cAAc;EAC9BC,QAAQ,EAAEA,QAAQ;EAClBC,KAAK,EAAEA,KAAK;EACZK,OAAO,EAAE;IACPC,YAAY,EAAE,CAAC,CAAC;IAChBC,qBAAqB,EAAE;EACzB;AACF,CAAC;;AAED;AACA,eAAeJ,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
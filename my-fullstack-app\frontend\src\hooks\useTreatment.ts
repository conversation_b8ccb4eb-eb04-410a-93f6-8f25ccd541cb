import { useMemo } from "react";
import { TreatmentApi } from '../services/apiService';
import { Treatment, TreatmentSearchParams } from '../types/api';
import { useApiData } from './useApiData';

interface UseTreatmentParams {
  patientname?: string;
  nationalId?: string;
  doctortid?: number | null;
  startTime?: Date | null;
  endTime?: Date | null;
  refreshKey?: number;
}

interface UseTreatmentReturn {
  treatments: Treatment[];
  loading: boolean;
  error: string | null;
  refetch: () => Promise<void>;
}

export default function useTreatment({
  patientname = '',
  nationalId = '',
  doctortid,
  startTime,
  endTime,
  refreshKey = 0,
}: UseTreatmentParams = {}): UseTreatmentReturn {

  const searchParams: TreatmentSearchParams = useMemo(() => ({
    patientname: patientname || undefined,
    nationalId: nationalId || undefined,
    doctortid: doctortid || undefined,
    starttime: startTime?.toISOString(),
    endtime: endTime?.toISOString(),
  }), [patientname, nationalId, doctortid, startTime, endTime]);

  const apiCall = useMemo(() =>
    () => TreatmentApi.getList(searchParams),
    [searchParams]
  );

  const { data: treatments = [], loading, error, refetch } = useApiData(
    apiCall,
    {
      dependencies: [refreshKey],
      initialData: [],
    }
  );

  return {
    treatments,
    loading,
    error,
    refetch,
  };
}

// Export the Treatment type for backward compatibility
export type { Treatment as TreatmentItem };
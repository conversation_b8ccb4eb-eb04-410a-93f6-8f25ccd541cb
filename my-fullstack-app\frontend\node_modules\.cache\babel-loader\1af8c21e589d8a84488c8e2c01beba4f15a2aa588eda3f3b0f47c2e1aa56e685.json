{"ast": null, "code": "import { isSameWeek } from \"../../../isSameWeek.js\";\nconst weekdays = [\"domenica\", \"luned<PERSON>\", \"marted<PERSON>\", \"mercoledì\", \"gio<PERSON><PERSON>\", \"venerd<PERSON>\", \"sabato\"];\nfunction lastWeek(day) {\n  switch (day) {\n    case 0:\n      return \"'domenica scorsa alle' p\";\n    default:\n      return \"'\" + weekdays[day] + \" scorso alle' p\";\n  }\n}\nfunction thisWeek(day) {\n  return \"'\" + weekdays[day] + \" alle' p\";\n}\nfunction nextWeek(day) {\n  switch (day) {\n    case 0:\n      return \"'domenica prossima alle' p\";\n    default:\n      return \"'\" + weekdays[day] + \" prossimo alle' p\";\n  }\n}\nconst formatRelativeLocale = {\n  lastWeek: (date, baseDate, options) => {\n    const day = date.getDay();\n    if (isSameWeek(date, baseDate, options)) {\n      return thisWeek(day);\n    } else {\n      return lastWeek(day);\n    }\n  },\n  yesterday: \"'ieri alle' p\",\n  today: \"'oggi alle' p\",\n  tomorrow: \"'domani alle' p\",\n  nextWeek: (date, baseDate, options) => {\n    const day = date.getDay();\n    if (isSameWeek(date, baseDate, options)) {\n      return thisWeek(day);\n    } else {\n      return nextWeek(day);\n    }\n  },\n  other: \"P\"\n};\nexport const formatRelative = (token, date, baseDate, options) => {\n  const format = formatRelativeLocale[token];\n  if (typeof format === \"function\") {\n    return format(date, baseDate, options);\n  }\n  return format;\n};", "map": {"version": 3, "names": ["isSameWeek", "weekdays", "lastWeek", "day", "thisWeek", "nextWeek", "formatRelativeLocale", "date", "baseDate", "options", "getDay", "yesterday", "today", "tomorrow", "other", "formatRelative", "token", "format"], "sources": ["C:/Users/<USER>/Desktop/demo-git/demo-react/my-fullstack-app/frontend/node_modules/date-fns/locale/it/_lib/formatRelative.js"], "sourcesContent": ["import { isSameWeek } from \"../../../isSameWeek.js\";\n\nconst weekdays = [\n  \"domenica\",\n  \"luned<PERSON>\",\n  \"marted<PERSON>\",\n  \"mercoledì\",\n  \"gio<PERSON><PERSON>\",\n  \"venerd<PERSON>\",\n  \"sabato\",\n];\n\nfunction lastWeek(day) {\n  switch (day) {\n    case 0:\n      return \"'domenica scorsa alle' p\";\n    default:\n      return \"'\" + weekdays[day] + \" scorso alle' p\";\n  }\n}\n\nfunction thisWeek(day) {\n  return \"'\" + weekdays[day] + \" alle' p\";\n}\n\nfunction nextWeek(day) {\n  switch (day) {\n    case 0:\n      return \"'domenica prossima alle' p\";\n    default:\n      return \"'\" + weekdays[day] + \" prossimo alle' p\";\n  }\n}\n\nconst formatRelativeLocale = {\n  lastWeek: (date, baseDate, options) => {\n    const day = date.getDay();\n    if (isSameWeek(date, baseDate, options)) {\n      return thisWeek(day);\n    } else {\n      return lastWeek(day);\n    }\n  },\n  yesterday: \"'ieri alle' p\",\n  today: \"'oggi alle' p\",\n  tomorrow: \"'domani alle' p\",\n  nextWeek: (date, baseDate, options) => {\n    const day = date.getDay();\n    if (isSameWeek(date, baseDate, options)) {\n      return thisWeek(day);\n    } else {\n      return nextWeek(day);\n    }\n  },\n  other: \"P\",\n};\n\nexport const formatRelative = (token, date, baseDate, options) => {\n  const format = formatRelativeLocale[token];\n\n  if (typeof format === \"function\") {\n    return format(date, baseDate, options);\n  }\n\n  return format;\n};\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,wBAAwB;AAEnD,MAAMC,QAAQ,GAAG,CACf,UAAU,EACV,QAAQ,EACR,SAAS,EACT,WAAW,EACX,SAAS,EACT,SAAS,EACT,QAAQ,CACT;AAED,SAASC,QAAQA,CAACC,GAAG,EAAE;EACrB,QAAQA,GAAG;IACT,KAAK,CAAC;MACJ,OAAO,0BAA0B;IACnC;MACE,OAAO,GAAG,GAAGF,QAAQ,CAACE,GAAG,CAAC,GAAG,iBAAiB;EAClD;AACF;AAEA,SAASC,QAAQA,CAACD,GAAG,EAAE;EACrB,OAAO,GAAG,GAAGF,QAAQ,CAACE,GAAG,CAAC,GAAG,UAAU;AACzC;AAEA,SAASE,QAAQA,CAACF,GAAG,EAAE;EACrB,QAAQA,GAAG;IACT,KAAK,CAAC;MACJ,OAAO,4BAA4B;IACrC;MACE,OAAO,GAAG,GAAGF,QAAQ,CAACE,GAAG,CAAC,GAAG,mBAAmB;EACpD;AACF;AAEA,MAAMG,oBAAoB,GAAG;EAC3BJ,QAAQ,EAAEA,CAACK,IAAI,EAAEC,QAAQ,EAAEC,OAAO,KAAK;IACrC,MAAMN,GAAG,GAAGI,IAAI,CAACG,MAAM,CAAC,CAAC;IACzB,IAAIV,UAAU,CAACO,IAAI,EAAEC,QAAQ,EAAEC,OAAO,CAAC,EAAE;MACvC,OAAOL,QAAQ,CAACD,GAAG,CAAC;IACtB,CAAC,MAAM;MACL,OAAOD,QAAQ,CAACC,GAAG,CAAC;IACtB;EACF,CAAC;EACDQ,SAAS,EAAE,eAAe;EAC1BC,KAAK,EAAE,eAAe;EACtBC,QAAQ,EAAE,iBAAiB;EAC3BR,QAAQ,EAAEA,CAACE,IAAI,EAAEC,QAAQ,EAAEC,OAAO,KAAK;IACrC,MAAMN,GAAG,GAAGI,IAAI,CAACG,MAAM,CAAC,CAAC;IACzB,IAAIV,UAAU,CAACO,IAAI,EAAEC,QAAQ,EAAEC,OAAO,CAAC,EAAE;MACvC,OAAOL,QAAQ,CAACD,GAAG,CAAC;IACtB,CAAC,MAAM;MACL,OAAOE,QAAQ,CAACF,GAAG,CAAC;IACtB;EACF,CAAC;EACDW,KAAK,EAAE;AACT,CAAC;AAED,OAAO,MAAMC,cAAc,GAAGA,CAACC,KAAK,EAAET,IAAI,EAAEC,QAAQ,EAAEC,OAAO,KAAK;EAChE,MAAMQ,MAAM,GAAGX,oBAAoB,CAACU,KAAK,CAAC;EAE1C,IAAI,OAAOC,MAAM,KAAK,UAAU,EAAE;IAChC,OAAOA,MAAM,CAACV,IAAI,EAAEC,QAAQ,EAAEC,OAAO,CAAC;EACxC;EAEA,OAAOQ,MAAM;AACf,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
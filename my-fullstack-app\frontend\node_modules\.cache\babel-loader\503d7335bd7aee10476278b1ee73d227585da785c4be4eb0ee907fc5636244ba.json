{"ast": null, "code": "const formatRelativeLocale = {\n  lastWeek: \"'joan den' eeee, LT\",\n  yesterday: \"'atzo,' p\",\n  today: \"'gaur,' p\",\n  tomorrow: \"'bihar,' p\",\n  nextWeek: \"eeee, p\",\n  other: \"P\"\n};\nconst formatRelativeLocalePlural = {\n  lastWeek: \"'joan den' eeee, p\",\n  yesterday: \"'atzo,' p\",\n  today: \"'gaur,' p\",\n  tomorrow: \"'bihar,' p\",\n  nextWeek: \"eeee, p\",\n  other: \"P\"\n};\nexport const formatRelative = (token, date) => {\n  if (date.getHours() !== 1) {\n    return formatRelativeLocalePlural[token];\n  }\n  return formatRelativeLocale[token];\n};", "map": {"version": 3, "names": ["formatRelativeLocale", "lastWeek", "yesterday", "today", "tomorrow", "nextWeek", "other", "formatRelativeLocalePlural", "formatRelative", "token", "date", "getHours"], "sources": ["C:/Users/<USER>/Desktop/demo-git/demo-react/my-fullstack-app/frontend/node_modules/date-fns/locale/eu/_lib/formatRelative.js"], "sourcesContent": ["const formatRelativeLocale = {\n  lastWeek: \"'joan den' eeee, LT\",\n  yesterday: \"'atzo,' p\",\n  today: \"'gaur,' p\",\n  tomorrow: \"'bihar,' p\",\n  nextWeek: \"eeee, p\",\n  other: \"P\",\n};\n\nconst formatRelativeLocalePlural = {\n  lastWeek: \"'joan den' eeee, p\",\n  yesterday: \"'atzo,' p\",\n  today: \"'gaur,' p\",\n  tomorrow: \"'bihar,' p\",\n  nextWeek: \"eeee, p\",\n  other: \"P\",\n};\n\nexport const formatRelative = (token, date) => {\n  if (date.getHours() !== 1) {\n    return formatRelativeLocalePlural[token];\n  }\n  return formatRelativeLocale[token];\n};\n"], "mappings": "AAAA,MAAMA,oBAAoB,GAAG;EAC3BC,QAAQ,EAAE,qBAAqB;EAC/BC,SAAS,EAAE,WAAW;EACtBC,KAAK,EAAE,WAAW;EAClBC,QAAQ,EAAE,YAAY;EACtBC,QAAQ,EAAE,SAAS;EACnBC,KAAK,EAAE;AACT,CAAC;AAED,MAAMC,0BAA0B,GAAG;EACjCN,QAAQ,EAAE,oBAAoB;EAC9BC,SAAS,EAAE,WAAW;EACtBC,KAAK,EAAE,WAAW;EAClBC,QAAQ,EAAE,YAAY;EACtBC,QAAQ,EAAE,SAAS;EACnBC,KAAK,EAAE;AACT,CAAC;AAED,OAAO,MAAME,cAAc,GAAGA,CAACC,KAAK,EAAEC,IAAI,KAAK;EAC7C,IAAIA,IAAI,CAACC,QAAQ,CAAC,CAAC,KAAK,CAAC,EAAE;IACzB,OAAOJ,0BAA0B,CAACE,KAAK,CAAC;EAC1C;EACA,OAAOT,oBAAoB,CAACS,KAAK,CAAC;AACpC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
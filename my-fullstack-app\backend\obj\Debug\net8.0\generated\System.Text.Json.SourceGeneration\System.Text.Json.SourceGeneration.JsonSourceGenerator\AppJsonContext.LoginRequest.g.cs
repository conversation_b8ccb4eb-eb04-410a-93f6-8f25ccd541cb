﻿// <auto-generated/>

#nullable enable annotations
#nullable disable warnings

// Suppress warnings about [Obsolete] member usage in generated code.
#pragma warning disable CS0612, CS0618

namespace MyApi.Helpers
{
    public partial class AppJsonContext
    {
        private global::System.Text.Json.Serialization.Metadata.JsonTypeInfo<global::LoginRequest>? _LoginRequest;
        
        /// <summary>
        /// Defines the source generated JSON serialization contract metadata for a given type.
        /// </summary>
        public global::System.Text.Json.Serialization.Metadata.JsonTypeInfo<global::LoginRequest> LoginRequest
        {
            get => _LoginRequest ??= (global::System.Text.Json.Serialization.Metadata.JsonTypeInfo<global::LoginRequest>)Options.GetTypeInfo(typeof(global::LoginRequest));
        }
        
        private global::System.Text.Json.Serialization.Metadata.JsonTypeInfo<global::LoginRequest> Create_LoginRequest(global::System.Text.Json.JsonSerializerOptions options)
        {
            if (!TryGetTypeInfoForRuntimeCustomConverter<global::LoginRequest>(options, out global::System.Text.Json.Serialization.Metadata.JsonTypeInfo<global::LoginRequest> jsonTypeInfo))
            {
                var objectInfo = new global::System.Text.Json.Serialization.Metadata.JsonObjectInfoValues<global::LoginRequest>
                {
                    ObjectCreator = () => new global::LoginRequest(),
                    ObjectWithParameterizedConstructorCreator = null,
                    PropertyMetadataInitializer = _ => LoginRequestPropInit(options),
                    ConstructorParameterMetadataInitializer = null,
                    SerializeHandler = LoginRequestSerializeHandler
                };
                
                jsonTypeInfo = global::System.Text.Json.Serialization.Metadata.JsonMetadataServices.CreateObjectInfo<global::LoginRequest>(options, objectInfo);
                jsonTypeInfo.NumberHandling = null;
            }
        
            jsonTypeInfo.OriginatingResolver = this;
            return jsonTypeInfo;
        }

        private static global::System.Text.Json.Serialization.Metadata.JsonPropertyInfo[] LoginRequestPropInit(global::System.Text.Json.JsonSerializerOptions options)
        {
            var properties = new global::System.Text.Json.Serialization.Metadata.JsonPropertyInfo[2];

            var info0 = new global::System.Text.Json.Serialization.Metadata.JsonPropertyInfoValues<string>
            {
                IsProperty = true,
                IsPublic = true,
                IsVirtual = false,
                DeclaringType = typeof(global::LoginRequest),
                Converter = null,
                Getter = static obj => ((global::LoginRequest)obj).Username,
                Setter = static (obj, value) => ((global::LoginRequest)obj).Username = value!,
                IgnoreCondition = null,
                HasJsonInclude = false,
                IsExtensionData = false,
                NumberHandling = null,
                PropertyName = "Username",
                JsonPropertyName = null
            };
            
            properties[0] = global::System.Text.Json.Serialization.Metadata.JsonMetadataServices.CreatePropertyInfo<string>(options, info0);

            var info1 = new global::System.Text.Json.Serialization.Metadata.JsonPropertyInfoValues<string>
            {
                IsProperty = true,
                IsPublic = true,
                IsVirtual = false,
                DeclaringType = typeof(global::LoginRequest),
                Converter = null,
                Getter = static obj => ((global::LoginRequest)obj).Password,
                Setter = static (obj, value) => ((global::LoginRequest)obj).Password = value!,
                IgnoreCondition = null,
                HasJsonInclude = false,
                IsExtensionData = false,
                NumberHandling = null,
                PropertyName = "Password",
                JsonPropertyName = null
            };
            
            properties[1] = global::System.Text.Json.Serialization.Metadata.JsonMetadataServices.CreatePropertyInfo<string>(options, info1);

            return properties;
        }

        // Intentionally not a static method because we create a delegate to it. Invoking delegates to instance
        // methods is almost as fast as virtual calls. Static methods need to go through a shuffle thunk.
        private void LoginRequestSerializeHandler(global::System.Text.Json.Utf8JsonWriter writer, global::LoginRequest? value)
        {
            if (value == null)
            {
                writer.WriteNullValue();
                return;
            }
            
            writer.WriteStartObject();

            writer.WriteString(PropName_Username, ((global::LoginRequest)value).Username);
            writer.WriteString(PropName_Password, ((global::LoginRequest)value).Password);

            writer.WriteEndObject();
        }
    }
}

{
  "ConnectionStrings": {
    "DefaultConnection": "Server=db;port=3306;Database=mydb;User=AdminUser;Password=******************;",
    "Redis": "redis:6379",
    "DevDefaultConnection": "Server=localhost;port=3306;Database=mydb;User=AdminUser;Password=******************;", // 本地開發用
    "DevRedis": "localhost:6379" // 本地開發用
  },
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft.AspNetCore": "Warning",
      "Microsoft.EntityFrameworkCore": "Warning"
    }
  },
  "Jwt": {
    "Key": "t0AIPBSJMGgSCJRbaIoFAZRGijaHw59L",
    "Issuer": "MyApi",
    "Audience": "react-admin"
  },
  "Features": {
    "EnableRedis": false,
    "EnableSignalR": false,
    "EnablePdfGeneration": true
  },
  "WebSitSettings": {
    "WebSitTitle": "厝邊頭家物理治療所"
  },
  "AllowedHosts": "*",
  "GoogleApiService": {
    "ServiceAccountKeyPath": "projectphysicalsite-11895cc283aa.json",
    "CalendarId": "<EMAIL>",
    "ApplicationName": "ProjectPhysicalSiteCalendar",
    "OAuthClientKeyPath": "client_secret_448638201562-ntutsm4jsi9o2sbs5dsq0af54hlv5n3g.apps.googleusercontent.com.json",
    "SenderEmail": "<EMAIL>",
    "UseRealGmail":  true
  }
}